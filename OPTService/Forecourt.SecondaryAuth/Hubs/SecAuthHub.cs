using Forecourt.SecondaryAuth.Hubs.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Microsoft.AspNet.SignalR.Hubs;

namespace Forecourt.SecondaryAuth.Hubs
{
    /// <summary>
    /// Publishes the requests from <see cref="ISecAuthIntegratorOut{IMessageTracking}"/> to the outside world
    /// </summary>
    [HubName(HubName)]
    public class SecAuthHub: Hub<SecAuthMessage, IMessageTracking>, ISecAuthHub, ISecAuthHubIn
    {
        /// <summary>
        /// Name of the SecAuth SignalR Hub
        /// </summary>
        public const string HubName = "Htec-Hubs:Forecourt-Service:SecAuth";

        /// <inheritdoc/>
        public SecAuthHub(IHubbableIn<SecAuthMessage, IMessageTracking> hubbable) : base(hubbable) { }
    }
}
