USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

if not exists (select * from sys.objects where object_id = OBJECT_ID('TransactionBookingState') and type = 'u')
 begin
  create table dbo.TransactionBookingState
   (Id bigint identity(1, 1),
    TransactionId bigint not null,
    TxnNumber nvarchar(20) not null,
    TransactionDate DateTime2 not null,
    ExternalTransactionId nvarchar(30),
    ShiftId int,
    PeriodId int,
    BusinessDate DateTime2,
    HttpStatusCode int,
    HttpResponse nvarchar(2048),
    BookedDate DateTime2,
    RetryCount int default 0,
    NextRetryDate DateTime2,
	CreatedDate DateTime2 NOT NULL,
	ModifiedDate DateTime2 NOT NULL,
    SendTransactionItem nvarchar(2048),
    constraint pk_TransactionBookingState primary key clustered (Id),
    constraint ix_TransactionIds unique nonclustered (TransactionId,TxnNumber,ExternalTransactionId),
    index ix_BookedDate nonclustered (BookedDate)
    )
 end

go
