using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;

namespace Forecourt.SecondaryAuth.Workers.Interfaces
{
    /// <summary>
    /// Provides a named interface for <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, for Unity purposed and for PreAuth requests
    /// </summary>
    public interface ISignalRPreSecAuthOutWorker: ISecAuthIntegratorOut<IMessageTracking>
    {
    }

    /// <summary>
    /// Provides a named interface for <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, for Unity purposed and for PostAuth requests
    /// </summary>
    public interface ISignalRPostSecAuthOutWorker : ISecAuthIntegratorOut<IMessageTracking>
    {
    }
}
