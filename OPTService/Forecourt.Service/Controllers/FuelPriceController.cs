using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class FuelPriceController : LoggableApiController
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        public FuelPriceController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger)
            : base(logger)
        {
            _web = web ?? throw new ArgumentNullException();
        }

        #endregion Constructor

        #region Get Data

        /// <summary>
        /// Get pump(s)
        /// This method can be used to retrieve only one pump data (pumpNumber > 0) or all pumps data (pumpNumber zero or not supplied)
        /// </summary>
        /// <param name="grade">The optional grade to be gotten</param>
        /// <param name="reference">The logging reference</param>
        /// <returns>A collection with the pump(s) details</returns>
        [HttpGet]
        [ResponseType(typeof(IEnumerable<GradePrices>))]
        public IHttpActionResult GetFuelPrices(byte grade = 0, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            //Call the Web object to retrieve the data
            var resultData = _web.GetPrices(grade);

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
            return Ok(resultData);
        }

        #endregion Get Data

        #region Update Data
        /// <summary>
        /// Set price for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and price, in pence per litre, to set .</param>
        /// <param name="reference">The logging reference</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrice(GradePrices details, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            byte grade = details.Grade;
            float price = details.PriceToSet;
            if (price < 0)
            {
                GetLogger().Debug(FormatMessage($"Validation error: Invalid value for details.PriceToSet: {price}. Returning BadRequest"));
                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("Invalid Price")
                };
            }

            _web.SendChatMessage(WebName, $"Set Price, Grade {grade}, Price {price} p / L");
            string result = _web.SetPrice(grade, Convert.ToInt32(price * 10.0));

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
            return new HttpResponseMessage
            {
                StatusCode = result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest,
                Content = result == null ? null : new StringContent(result)
            };
        }

        /// <summary>
        /// Set name for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and name to set.</param>
        /// <param name="reference">The logging reference</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetGradeName(GradePrices details, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            byte grade = details.Grade;
            string name = details.GradeName?.Trim();

            if (string.IsNullOrEmpty(name))
            {
                GetLogger().Debug(FormatMessage($"Validation error: details.GradeName is null or empty. Returning BadRequest"));
                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("Grade Name Empty")
                };
            }
            else if (name.All(x => char.IsLetterOrDigit(x) || char.IsWhiteSpace(x)))
            {
                _web.SendChatMessage(WebName, $"Set Grade Name, Grade {grade}, Name {name}");
                string result = _web.SetGradeName(grade, name);

                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return new HttpResponseMessage
                {
                    StatusCode = result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest,
                    Content = result == null ? null : new StringContent(result)
                };
            }
            else
            {
                GetLogger().Debug(FormatMessage($"Validation error: Invalid Characters in details.GradeName. Returning BadRequest"));
                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("Invalid Characters in Grade Name")
                };
            }
        }

        /// <summary>
        /// Set VAT Rate for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and VAT Rate to set.</param>
        /// <param name="reference">The logging reference</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetGradeVatRate(GradePrices details, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            byte grade = details.Grade;
            float vatRate = details.VatRate;

            if (float.IsNaN(vatRate) || vatRate < 0 || vatRate > 100)
            {
                GetLogger().Debug(FormatMessage($"Validation error: Invalid details.VatRate. Returning BadRequest"));
                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("Invalid VAT Rate")
                };
            }

            _web.SendChatMessage(WebName, $"Set Grade VAT Rate, Grade {grade}, VAT Rate {vatRate}");
            string result = _web.SetGradeVatRate(grade, vatRate);

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
            return new HttpResponseMessage
            {
                StatusCode = result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest,
                Content = result == null ? null : new StringContent(result)
            };
        }

        #endregion Update Data
    }
}
