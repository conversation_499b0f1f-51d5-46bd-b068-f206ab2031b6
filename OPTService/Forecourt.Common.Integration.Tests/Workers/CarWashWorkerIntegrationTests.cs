using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading;
using Xunit;

namespace OPT.Common.Integration.Tests.Workers
{
    public class CarWashWorkerIntegrationTests
    {
        private readonly ICarWashWorker _carWashWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IFromOptWorker _optWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IHtecLogger _logger;
        private readonly IOpt _optOne;
        private readonly IOpt _optTwo;
        private readonly IOpt _optThree;
        private readonly IClientConnectionThread<string> _connectionThread;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITimerFactory _timerFactory;

        public CarWashWorkerIntegrationTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _logger = Substitute.For<IHtecLogger>();
            _optOne = Substitute.For<IOpt>();
            _optTwo = Substitute.For<IOpt>();
            _optThree = Substitute.For<IOpt>();
            _connectionThread = Substitute.For<IClientConnectionThread<string>>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _timerFactory = Substitute.For<ITimerFactory>();

            _carWashWorker = new CarWashWorker(_telemetryWorker, _hydraDb, _logger, _connectionThread, _configurationManager, _timerFactory);
            _carWashWorker.RegisterWorker(_optWorker);
            _carWashWorker.Start();
            _carWashWorker.OnConnected();
        }

        [Fact]
        public void test_send_request_no_connection_sends_message_to_opt_worker()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            _carWashWorker.OnDisconnected();

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);

            // Assert
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optOne));
            _connectionThread.DidNotReceive().Send(Arg.Any<MessageTracking<string>>(), Arg.Any<AsyncSocketState<Htec.Foundation.Connections.Sockets.Interfaces.ISocketWithBuffer>>(), Arg.Any<Action>());
        }

        [Fact]
        public void test_send_requests_and_disconnect_sends_opt_messages_to_opt_worker()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var bytesSent = "";

            InitialiseConnectionThread(x => bytesSent = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);
            _carWashWorker.SendRequestToCarWash(_optTwo, machine, prog);
            _carWashWorker.OnDisconnected();

            // Assert
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optOne));
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optTwo));
            bytesSent.Should().Be(expectedText);
        }

        [Fact]
        public void test_send_request_with_noticket_response_sends_message_to_opt_worker()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            const string message = "NOTICKET";
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var bytesSent = "";
            InitialiseConnectionThread(x => bytesSent = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optOne));
            bytesSent.Should().Be(expectedText);
        }

        [Fact]
        public void test_send_request_with_ticket_response()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            const string ticket = "123456";
            const string version = "654321";
            string message = $"TICKET{ticket}/{version}";
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var bytesSent = "";

            InitialiseConnectionThread(x => bytesSent = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            bytesSent.Should().Be(expectedText);
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optOne), Arg.Is(ticket), Arg.Is(version));
        }

        [Fact]
        public void test_send_request_with_ticket_and_pin_response()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            const string ticket = "123456";
            const string version = "654321";
            const string pin = "789";
            string message = $"TICKET{ticket}/{version}/{pin}";
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var bytesSent = "";

            InitialiseConnectionThread(x => bytesSent = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            bytesSent.Should().Be(expectedText);
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optOne), Arg.Is(ticket), Arg.Is(version), Arg.Is(pin));
        }

        [Fact]
        public void test_send_two_requests_with_timeout_one_with_ticket_response()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog1 = 1;
            const byte prog2 = 2;
            const byte prog3 = 3;
            const string ticket3 = "901234";
            const string version3 = "432109";
            string message3 = $"TICKET{ticket3}/{version3}";
            string expectedText1 = $"Valet={machine}/Normal/{prog1}/";
            string expectedText3 = $"Valet={machine}/Normal/{prog3}/";

            var messageReceived = new List<string>();
            InitialiseConnectionThread(x => messageReceived.Add(x));

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog1);
            _carWashWorker.SendRequestToCarWash(_optTwo, machine, prog2);
            Thread.Sleep(3000);
            _carWashWorker.SendRequestToCarWash(_optThree, machine, prog3);
            Thread.Sleep(2000);
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message3));

            // Assert
            messageReceived[0].Should().Be(expectedText1);
            messageReceived[1].Should().Be(expectedText3);
            _optWorker.ReceivedWithAnyArgs(2).CarWashNoTicket(null);
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optOne));
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optTwo));
            _optWorker.ReceivedWithAnyArgs(1).CarWashTicket(null, "", "");
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optThree), Arg.Is(ticket3), Arg.Is(version3));
        }

        [Fact]
        public void test_send_three_requests_with_ticket_response()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog1 = 1;
            const byte prog2 = 2;
            const byte prog3 = 3;
            const string ticket1 = "123456";
            const string version1 = "654321";
            const string ticket2 = "012345";
            const string version2 = "543210";
            const string ticket3 = "901234";
            const string version3 = "432109";
            string message1 = $"TICKET{ticket1}/{version1}";
            string message2 = $"TICKET{ticket2}/{version2}";
            string message3 = $"TICKET{ticket3}/{version3}";
            string expectedText1 = $"Valet={machine}/Normal/{prog1}/";
            string expectedText2 = $"Valet={machine}/Normal/{prog2}/";
            string expectedText3 = $"Valet={machine}/Normal/{prog3}/";

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog1);
            _carWashWorker.SendRequestToCarWash(_optTwo, machine, prog2);
            _carWashWorker.SendRequestToCarWash(_optThree, machine, prog3);
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message1));
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message2));
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message3));

            // Assert
            //_workerProxy.ReceivedWithAnyArgs(3).SendTextToServer("");
            //_workerProxy.Received(1).SendTextToServer(Arg.Is(expectedText1));
            //_workerProxy.Received(1).SendTextToServer(Arg.Is(expectedText2));
            //_workerProxy.Received(1).SendTextToServer(Arg.Is(expectedText3));
            _optWorker.ReceivedWithAnyArgs(3).CarWashTicket(null, "", "");
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optOne), Arg.Is(ticket1), Arg.Is(version1));
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optTwo), Arg.Is(ticket2), Arg.Is(version2));
            _optWorker.Received(1).CarWashTicket(Arg.Is(_optThree), Arg.Is(ticket3), Arg.Is(version3));
        }

        [Fact]
        public void test_send_request_with_timeout()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var messageReceived = string.Empty;

            InitialiseConnectionThread(x => messageReceived = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);
            Thread.Sleep(5000);

            // Assert
            messageReceived.Should().Be(expectedText);
            _optWorker.Received(1).CarWashNoTicket(Arg.Is(_optOne));
            _optWorker.DidNotReceiveWithAnyArgs().CarWashTicket(null, "", "");
        }

        #region Helpers

        private void InitialiseConnectionThread(Action<string> doFunc)
        {
            _connectionThread.When(x => x.Send(Arg.Any<MessageTracking<string>>(), Arg.Any<AsyncSocketState<Htec.Foundation.Connections.Sockets.Interfaces.ISocketWithBuffer>>(), Arg.Any<Action>()))
                .Do(x => doFunc(x.ArgAt<MessageTracking<string>>(0).Request));

            _connectionThread.IsConnected()
                .Returns(true);
        }

        #endregion
    }
}
