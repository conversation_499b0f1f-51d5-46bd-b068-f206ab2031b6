import { FileVersionInfo } from './fileVersioInfo.model';

export class VersionInfo {
    Current: Array<FileVersionInfo>;
    Upgrade: Array<FileVersionInfo>;
    Rollback: Array<FileVersionInfo>;
    PumpIntegrator: Array<FileVersionInfo>;
    OfflineFileService: Array<FileVersionInfo>;
    UploadedFileNames: Array<string> = [];
    WhitelistFiles: Array<string> = [];
    LayoutFiles: Array<string> = [];
    UpgradeFiles: Array<string> = [];
    SoftwareFiles: Array<string> = [];
    MediaFiles: Array<string> = [];
    PlaylistFiles: Array<string> = [];
    DatabaseBackupFiles: Array<string> = [];
    OptLogFiles: Array<string> = [];
    DisplayOptLogFiles: Array<string> = [];
}
