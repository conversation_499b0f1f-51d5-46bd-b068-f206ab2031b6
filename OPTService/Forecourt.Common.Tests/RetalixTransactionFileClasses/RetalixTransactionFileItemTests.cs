using System;
using FluentAssertions;
using Htec.Hydra.Core.Bos.Messages.Retalix;
using Xunit;

namespace OPT.Common.Tests.RetalixTransactionFileClasses
{
    public class RetalixTransactionFileItemTests
    {
        [Fact]
        public void constructor_transaction_number_under_max_is_not_amended()
        {
            // Arrange
            const int transactionNumber = 1234;

            // Act

            var retalixTransactionFileItem = RetalixTransactionFileItem.ConstructSalesItem(1, 1, 1, "Test", "1234", 146, 1, transactionNumber, "00000000000001", DateTime.Now, 546, 4567, 0, string.Empty, 0, 10000, "Test", string.Empty, string.Empty, 0, string.Empty, false);

            // Assert
            retalixTransactionFileItem.TransactionNumber.Should().Be("1234");
        }

        [Fact(Skip = "Needs to be moved as cycling now in db")]
        public void constructor_transaction_number_over_max_is_cycled()
        {
            // Arrange
            const int transactionNumber = 101234;

            // Act

            var retalixTransactionFileItem = RetalixTransactionFileItem.ConstructSalesItem(1, 1, 1, "Test", "1234", 146, 1, transactionNumber, "00000000000001", DateTime.Now, 546, 4567, 0, string.Empty, 0, 10000, "Test", string.Empty, string.Empty, 0, string.Empty, false);

            // Assert
            retalixTransactionFileItem.TransactionNumber.Should().Be("1234");
        }
    }
}
