namespace OPT.Common.HydraDbClasses
{
    public class MeterReading
    {
        public byte Pump { get; }
        public uint Volume1 { get; }
        public uint Cash1 { get; }
        public uint Volume2 { get; }
        public uint Cash2 { get; }
        public uint Volume3 { get; }
        public uint Cash3 { get; }
        public uint Volume4 { get; }
        public uint Cash4 { get; }

        public MeterReading
            (int pump, long volume1, long cash1, long volume2, long cash2, long volume3, long cash3, long volume4, long cash4)
        {
            Pump = (byte) pump;
            Volume1 = (uint) volume1;
            Cash1 = (uint) cash1;
            Volume2 = (uint) volume2;
            Cash2 = (uint) cash2;
            Volume3 = (uint) volume3;
            Cash3 = (uint) cash3;
            Volume4 = (uint) volume4;
            Cash4 = (uint) cash4;
        }
    }
}