using ConstantsIntegrator = Forecourt.Core.Configuration.Constants.Integrator;

namespace OPT.Common.Repositories
{
    public static class DetailConsts
    {
        public const string Enabled = "Enabled";
        public const string CardPreAuth = "CardPreAuth";
        public const string DevicePreAuth = "DevicePreAuth";
        public const string Ttq = "TTQ";
        public const string SingleButton = "SingleButton";
        public const string SiteType = ConstantsIntegrator.ConfigKeyIntegratorSite;
        public const string PosType = ConstantsIntegrator.ConfigKeyIntegratorPos;
        public const string LocalAccountsEnabled = "LocalAccountsEnabled";
    }
}