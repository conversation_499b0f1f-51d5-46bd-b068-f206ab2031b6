<div class="d-flex justify-content-between header p-3">
  <div class="d-flex">
    <div class="logo-wrapper">
      <img src="assets/img/logo-100.png" alt="HTEC logo"/>
    </div>
    <div class="pt-1">
      <span class="text-secondary h4 ml-3 align-self-end">Hydra Forecourt/OPT Controller</span>
      <span class="vertical-divider d-inline ml-3 border border-blue"></span>
      <span class="h4 ml-3 align-self-end text-blue">{{activeScreenName}}</span>
    </div>
  </div>
  <div class="d-flex">
    <div class="header-button-disabled d-flex flex-column align-items-center ml-3 h3 pt-2" *ngIf="!isPumpControllerEnabled">
      <span class="badge badge-danger badge-blink">PUMP CONTROLLER INHIBITED</span>
    </div>
    <div class="d-flex flex-column align-items-center h3 pt-2 text-blue">
      <span class="badge text-uppercase">{{siteName}}</span>
    </div>
    <span class="vertical-divider d-inline ml-3 border border-blue"></span>
    <ng-container *ngIf="unmannedPseudoPos === true">
      <div class="header-button-disabled d-flex flex-column align-items-center ml-3 text-blue" ngbTooltip="Unmanned Pseudo POS">
        <fa-icon [icon]="faUserSlash" size="2x"></fa-icon>
        <span>Pseudo POS</span>
      </div>
    </ng-container>
    <div class="header-button-disabled d-flex flex-column align-items-center ml-3 text-blue" ngbTooltip="Site Type">
      <ng-container *ngIf="siteType === retailSiteType">
        <fa-icon [icon]="faStoreAlt" size="2x"></fa-icon>
        <span>Retail</span>
      </ng-container>
      <ng-container *ngIf="siteType === rdcSiteType">
        <fa-icon [icon]="faTruckMoving" size="2x"></fa-icon>
        <span>RDC</span>
      </ng-container>
      <ng-container *ngIf="siteType === airfieldSiteType">
        <fa-icon [icon]="faPlane" size="2x"></fa-icon>
        <span>Airfield</span>
      </ng-container>
      <ng-container *ngIf="siteType === marinaSiteType">
        <fa-icon [icon]="faShip" size="2x"></fa-icon>
        <span>Marina</span>
      </ng-container>
      <ng-container *ngIf="siteType !== retailSiteType && siteType !== rdcSiteType && siteType !== airfieldSiteType && siteType !== marinaSiteType">
        <fa-icon [icon]="faQuestionCircle" size="2x"></fa-icon>
        <span>Unknown</span>
      </ng-container>
    </div>
    <div class="header-button-disabled d-flex flex-column align-items-center ml-3" ngbTooltip="Connection Status"
         [ngClass]="{'text-blue':isSignalRConnected(),'text-secondary':!isSignalRConnected()}">
      <fa-icon [icon]="faWifi" size="2x"></fa-icon>
      <span>{{getSignalRConnectionStateText()}}</span>
    </div>
    <div class="header-button d-flex flex-column align-items-center text-blue ml-3" (click)="refreshCurrentComponent()">
      <fa-icon [icon]="faSyncAlt" size="2x"></fa-icon>
      <span>Refresh</span>
    </div>
  </div>
</div>
<div class="content-wrapper px-3 pb-3">
  <div class="card">
    <div class="card-header border-bottom-0 pb-0 px-0">
      <ul ngbNav #nav="ngbNav" [(activeId)]="activeScreenId" class="nav-tabs px-2">
        <li ngbNavItem="opts">
          <a ngbNavLink routerLink="" routerLink="" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }">OPTs</a>
        </li>
        <li ngbNavItem="pumps">
          <a ngbNavLink routerLink="pumps" routerLinkActive="active">Pumps</a>
        </li>
        <li ngbNavItem="fuel-prices">
          <a ngbNavLink routerLink="fuelPrices" routerLinkActive="active">Fuel Prices</a>
        </li>
        <li ngbNavItem="connections">
          <a ngbNavLink routerLink="connections" routerLinkActive="active">Connections</a>
        </li>
        <li ngbNavItem="genericOptConfig">
          <a ngbNavLink routerLink="genericOptConfig" routerLinkActive="active">Generic OPT Config</a>
        </li>
        <li ngbNavItem="advancedConfig">
          <a ngbNavLink routerLink="advancedConfig" routerLinkActive="active">Advanced Config</a>
        </li>
        <li ngbNavItem="fileLocations">
          <a ngbNavLink routerLink="fileLocations" routerLinkActive="active">File Locations</a>
        </li>
        <li ngbNavItem="shiftEnd">
          <a ngbNavLink routerLink="shiftEnd" routerLinkActive="active">Shift End</a>
        </li>
        <li ngbNavItem="transactions">
          <a ngbNavLink routerLink="transactions" routerLinkActive="active">Transactions</a>
        </li>
        <li ngbNavItem="localAccounts">
          <a ngbNavLink routerLink="/localAccounts" routerLinkActive="active">Local Accounts</a>
        </li>
        <!--
        <li ngbNavItem="doms">
          <a ngbNavLink routerLink="/doms" routerLinkActive="active">DOMS</a>
          <ng-template ngbNavContent>
            <app-doms></app-doms>
          </ng-template>
        </li> -->
        <li ngbNavItem="infoMessages">
          <a ngbNavLink routerLink="infoMessages" routerLinkActive="active">Info Messages</a>
        </li>
        <li ngbNavItem="about">
          <a ngbNavLink routerLink="about" routerLinkActive="active">About</a>
        </li>
      </ul>
    </div>
    <div class="card-body">
      <!-- <div class="spinner-wrapper" *ngIf="shouldShowLoading()" [@inOutAnimation]>
        <div class="spinner">
          <div class="cube1"></div>
          <div class="cube2"></div>
        </div>
      </div> -->
      <div *ngIf="shouldShowLoading()">
        <div class="spinner-border spinner-border-sm mr-2" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        <span>Loading <span class="text-lowercase">{{activeScreenName}}</span> data. Please wait...</span>
      </div>
      <div *ngIf="shouldShowError()" class="alert alert-danger" role="alert">
        <span>
          <i class="bi bi-exclamation-triangle"></i> 
          An error occurred whilst loading <span class="text-lowercase">{{activeScreenName}}</span> data. 
          <a (click)="refreshCurrentComponent()" class="alert-link">Please try refreshing the page</a>.
        </span>
      </div>
      <router-outlet></router-outlet>
    </div>
    <div class="card-footer text-muted text-size-075 text-center">
      <span *ngIf="getLastRefreshTime()">Data was last refreshed on {{getLastRefreshTime()}}</span>
    </div>  
  </div>
</div>
