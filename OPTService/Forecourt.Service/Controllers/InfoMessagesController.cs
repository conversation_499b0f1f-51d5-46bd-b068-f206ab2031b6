using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPTService.OPTServiceClasses;
using System;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    /// <summary>
    /// Controller for services for info message details
    /// </summary>
    public class InfoMessagesController : LoggableApiController
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        public InfoMessagesController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)]IHtecLogger logger) : base(logger)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        #endregion Constructor

        #region Get Data

        /// <summary>
        /// Retrieves all the info messages
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <response code="200">Success</response>
        /// <response code="404">Info messages not found</response>
        /// <returns>The info messages</returns>
        [HttpGet]
        [ResponseType(typeof(InfoMessageDetails))]
        public IHttpActionResult GetInfoMessageDetails(string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            var infoMessageDetails = _web.GetInfoDetails();

            if (infoMessageDetails is null)
            {
                GetLogger().Debug(FormatMessage($"No info message details found. Returning Not Found. Reference: {reference}"));
                return NotFound();
            }

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
            return Ok(infoMessageDetails);
        }

        #endregion Get Data
    }
}