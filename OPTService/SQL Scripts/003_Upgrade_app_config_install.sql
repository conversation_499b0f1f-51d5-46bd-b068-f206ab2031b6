USE Hydra

PRINT N'HOPT-1062 - Standard Configuration...Begin';
PRINT N'Creating Procedure [dbo].[GetCategoryConfiguration]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetCategoryConfiguration') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetCategoryConfiguration
END
GO

CREATE PROCEDURE [dbo].[GetCategoryConfiguration]
  @headerId INT,
  @categoryId INT AS
BEGIN
 
    SELECT [Key],[Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId

END
GO

PRINT N'HOPT-1062 - Standard Configuration...End';
GO
