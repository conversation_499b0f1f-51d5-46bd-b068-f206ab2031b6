using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    [Migration(6, "Upgrade the HydraDb database to, v2.2.0"), Tags(Constants.MigrationTypeSchema)]
    public class UpdateDb_v2_2_0: Migrationable
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.003_Upgrade_app_config_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.004_Upgrade_site_type_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.005_Upgrade_pos_type_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.006_Upgrade_local_accounts_enabled_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.007_Upgrade_log_folders_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.008_Upgrade_receipt_layout_mode_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.009_Upgrade_get_loyalty_terminal_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.010_Upgrade_receipt_cycling_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.011_Upgrade_ConfigCategory_IsEditable_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.012_Upgrade_app_config_timer_interval_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_install.sql");
        }

        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.012_Upgrade_app_config_timer_interval_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.011_Upgrade_ConfigCategory_IsEditable_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.010_Upgrade_receipt_cycling_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.009_Upgrade_get_loyalty_terminal_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.008_Upgrade_receipt_layout_mode_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.007_Upgrade_log_folders_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.006_Upgrade_local_accounts_enabled_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.005_Upgrade_pos_type_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.004_Upgrade_site_type_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.003_Upgrade_app_config_rollback.sql");
        }
    }
}
