using optModels = Htec.Hydra.Messages.Opt.Models;
using posModels = Htec.Hydra.Core.Pos.Common;

namespace Forecourt.Core.Extensions
{
    /// <summary>
    /// Type conversion helpers for enum, as no explicit operators
    /// </summary>
    public static class PumpStateTypeExtensions
    {
        /// <summary>
        /// Converts an enum value from OPT to POS equivalent enum
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static posModels.PumpOptType ToPumpOptType(this optModels.PumpStateType value) =>
            value == optModels.PumpStateType.Closed ? posModels.PumpOptType.Closed :
            value == optModels.PumpStateType.Open ? posModels.PumpOptType.OptOnly :
            value == optModels.PumpStateType.KioskOnly ? posModels.PumpOptType.KioskOnly :
            posModels.PumpOptType.Mixed;

        /// <summary>
        /// Converts an enum value from OPT to POS equivalent enum
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static optModels.PumpStateType ToPumpStateType(this posModels.PumpOptType value) =>
            value == posModels.PumpOptType.Closed ? optModels.PumpStateType.Closed :
            value == posModels.PumpOptType.OptOnly ? optModels.PumpStateType.Open :
            value == posModels.PumpOptType.KioskOnly ? optModels.PumpStateType.KioskOnly :
            optModels.PumpStateType.Mixed;
    }
}
