using Forecourt.Common.HydraDbClasses;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using OPT.Common.Helpers;
using OPT.Common.Workers;
using System.IO.Abstractions;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    public class ConfigUpdateWorkerTests
    {
        private readonly IHydraDb _hydraDb;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IHtecLogger _logger;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITimerFactory _timerFactory;
        private readonly IFileSystem _fileSystem;
        private readonly ILoggingHelper _loggingHelper;

        public ConfigUpdateWorkerTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _fileSystem = Substitute.For<IFileSystem>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
        }

        #region Constructor

        [Fact]
        public void constructor_null_hydra_db_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(null, _paymentConfig, _logger, _configurationManager, _timerFactory, _fileSystem);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "hydraDb");
        }

        [Fact]
        public void constructor_null_esocket_db_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(_hydraDb, null, _logger, _configurationManager, _timerFactory, _fileSystem);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "eSocketDb");
        }

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(_hydraDb, _paymentConfig, null, _configurationManager, _timerFactory, _fileSystem);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        [Fact]
        public void constructor_null_configuration_manager_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(_hydraDb, _paymentConfig, _logger, null, _timerFactory, _fileSystem);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "configurationManager");
        }

        [Fact]
        public void constructor_null_timer_factory_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(_hydraDb, _paymentConfig, _logger, _configurationManager, null, _fileSystem);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "timerFactory");
        }

        [Fact]
        public void constructor_null_file_system_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigUpdateWorker(_hydraDb, _paymentConfig, _logger, _configurationManager, _timerFactory, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "fileSystem");
        }

        #endregion
    }
}
