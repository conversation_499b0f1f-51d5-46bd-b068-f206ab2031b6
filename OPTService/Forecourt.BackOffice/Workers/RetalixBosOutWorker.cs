using CSharpFunctionalExtensions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Bos.Workers.Interfaces;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using retalix = Htec.Hydra.Core.Bos.Messages.Retalix;

namespace Forecourt.Bos.Workers
{
    /// <inheritdoc />
    [HasConfiguration()]
    public class RetalixBosOutWorker : HydraBosOutWorker, IRetalixBosOutWorker
    {
        /// <summary>
        /// Config key for the inhibit file movements to the Retalix Offline Transaction files
        /// </summary>
        public const string ConfigKeyInhibitRetalixOfflineTransactionFileMovement = ConfigurationConstants.CategoryNamePOS + ConfigurationConstants.CategorySeparator + "Retalix:OfflineTransactionFile:InhibitMovement";

        /// <summary>
        /// Default value for the inhibit file movements to the Retalix Offline Transaction files
        /// </summary>
        public const bool DefaultValueInhibitRetalixOfflineTransactionFileMovement = true;

        private IRetalixTransactionFile _retalixTransactionFile;

        /// <summary>
        /// ConfigValue, for the inhibit file movements to the Retalix Offline Transaction files
        /// </summary>
        public ConfigurableBool ConfigValueinhibitRetalixOfflineTransactionFileMovement { get; private set; }


        /// <inheritdoc />
        public RetalixBosOutWorker(IHtecLogger logger, IConfigurationManager configurationManager, IHydraTransactionFile transactionFile, IRetalixTransactionFile retalixTransactionFile, HydraDb.Interfaces.IHydraDb hydraDb) : 
            base(logger, configurationManager, transactionFile, hydraDb)
        {
            DoCtor(retalixTransactionFile);
        }

        private void DoCtor(IRetalixTransactionFile transactionFile)
        {
            _retalixTransactionFile = transactionFile ?? throw new ArgumentNullException(nameof(transactionFile));

            ConfigValueinhibitRetalixOfflineTransactionFileMovement = new ConfigurableBool(this, ConfigKeyInhibitRetalixOfflineTransactionFileMovement, DefaultValueInhibitRetalixOfflineTransactionFileMovement);
        }

        /// <inheritdoc />
        public RetalixBosOutWorker(IHtecLogManager logManager, string name, IConfigurationManager configurationManager, IHydraTransactionFile transactionFile, 
            IRetalixTransactionFile retalixTransactionFile, HydraDb.Interfaces.IHydraDb hydraDb) : base(logManager, $"{(string.IsNullOrEmpty(name) ? nameof(RetalixBosOutWorker) : name).ConvertToPrefixedDottedName(LoggerNamePrefix)}", 
                configurationManager, transactionFile, hydraDb)
        {
            DoCtor(retalixTransactionFile);
        }

        /// <inheritdoc />
        public override Result MoveOfflineTransactionFiles(IPAddress ipAddress, string loggingReference = null)
        {
            if (!ConfigValueinhibitRetalixOfflineTransactionFileMovement.GetValue())
            {
                return _retalixTransactionFile.MoveOfflineTransactionFiles(ipAddress, loggingReference);
            }

            return Result.Success();
        }

        /// <inheritdoc />
        public override Result SendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = base.SendTransaction(sendTransaction, message);
            if (!result.IsSuccess)
            {
                return result;
            }

            var x = sendTransaction;
            var transaction = retalix.RetalixTransactionFileItem.ConstructSalesItem(x.Pump,
                x.Hose, x.Grade, x.GradeName, x.ProductCode, x.Price, (int)x.Id, Convert.ToInt32(x.Number), x.CardNumber,
                DateTime.Now, x.AuthAmount, x.Volume, x.WashProgram, x.WashTicket, x.WashPrice, x.PreAuthAmount, // TODO: Carwash when we get to it
                x.CardProductName, x.Discount?.Name ?? string.Empty, x.Discount?.Type ?? string.Empty, x.Discount?.Value ?? 0,
                x.Discount?.CardNumber ?? string.Empty, x.LocalAccountPayment != null);
            var addresses = Enumerable.Empty<IPAddress>();

            if (_retalixTransactionFile?.Available ?? false)
            {
                _retalixTransactionFile.SendTransaction(transaction, out addresses, message);
                _retalixTransactionFile.WriteTransactionFile(new[] { transaction }, DateTime.Now, addresses ?? new List<IPAddress>(), message);
            }
            else
            {
                var siteInfo = HydraDb.AdvancedConfig;
                if (siteInfo.PosType == PosType.Retalix)
                {
                    DoDeferredLogging(LogLevel.Warn, "Didn't write Retalix transaction file as not _retalixTransactionFile.Available", reference: message.FullId);
                }
            }

            return Result.Success();
        }

        /// <inheritdoc />
        public override Result WriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem,
            IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = default)
        {
            message ??= new MessageTracking();
            var result = base.WriteShiftEndFiles(isDayEnd, startTime, endTime, endItem, itemSales, categorySales, cardSales, cardAmountSales, cardVolumeSales, message);
            if (!result.IsSuccess)
            {
                return result;
            }

            var retalixItemSales = itemSales.Select(x => (retalix.RetalixItemSalesItem)x);
            var retalixCardAmountSales = cardAmountSales.Select(x => (retalix.RetalixCardAmountItem)x); 
            var retalixCardVolumeSales = cardVolumeSales.Select(x => (retalix.RetalixCardVolumeItem)x);

            _retalixTransactionFile.WriteShiftEndFiles((retalix.RetalixShiftEndItem)endItem, isDayEnd, retalixItemSales, retalixCardAmountSales,
                // TODO: cardSales,
                Enumerable.Empty<retalix.RetalixCardSalesItem>(),
                // TODO: categorySales, ADO #517039 CR58550: OPTService Missing SC EOD file for Back Office
                Enumerable.Empty<retalix.RetalixCategorySalesItem>(), 
                retalixCardVolumeSales, message);

            return Result.Success();
        }
    }
}
