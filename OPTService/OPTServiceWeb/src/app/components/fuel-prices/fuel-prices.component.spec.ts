import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { FuelPriceService } from 'src/app/services/fuel-price.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { DecimalPipe } from '@angular/common';
import { RemoveCommaPipe } from 'src/app/core/pipes/removeCommaPipe';

import { FuelPricesComponent } from './fuel-prices.component';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';

describe('FuelPricesComponent', () => {
  let component: FuelPricesComponent;
  let serviceSpy: jasmine.SpyObj<FuelPriceService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let fbSpy: jasmine.SpyObj<FormBuilder>;

  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('OptService', ['getFuelPrices', 'restartOpt']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getFuelPriceSignalRMessage']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        FuelPricesComponent,
        FormBuilder,
        DecimalPipe,
        RemoveCommaPipe,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: FuelPriceService, useValue: serviceObjSpy },
        { provide: SignalRService, useValue: signalRSpy },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    serviceSpy = TestBed.inject(FuelPriceService) as jasmine.SpyObj<FuelPriceService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    fbSpy = TestBed.inject(FormBuilder) as jasmine.SpyObj<FormBuilder>;
    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    signalRServiceSpy.getFuelPriceSignalRMessage.and.returnValue(of());
    component = TestBed.inject(FuelPricesComponent);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    jasmine.clock().install();
    //Arrange
    spyOn(component, 'refreshData').and.stub();

    //Act
    component.ngOnInit();
    jasmine.clock().tick(50);

    //Assert
    expect(component.refreshData).toHaveBeenCalledTimes(1);
    jasmine.clock().uninstall();
  });

  it('.ngOnDestroy() success', () => {
    //Arrange
    spyOn(component.signalRData, 'unsubscribe').and.stub();

    //Act
    component.ngOnDestroy();

    //Assert
    expect(component.signalRData.unsubscribe).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle success response from service', () => {
    //Arrange
    let fakeConfig = [
      {
        Grade: 1,
        GradeName: "Unleaded",
        VatRate: 20,
        PriceToSet: 136.9,
        Prices: [
          {
            Pump: 1,
            Price: 136.9
          },
          {
            Pump: 2,
            Price: 136.9
          }
        ]
      },
      {
        Grade: 2,
        GradeName: "Diesel",
        VatRate: 20,
        PriceToSet: 145.9,
        Prices: [
          {
            Pump: 1,
            Price: 145.9
          },
          {
            Pump: 2,
            Price: 145.9
          }
        ]
      }
    ];
    serviceSpy.getFuelPrices.and.returnValue(of(fakeConfig));

    //Act
    component.refreshData();

    //Assert
    expect(component.fuelPrices).toEqual(fakeConfig);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', () => {
    //Arrange
    let fakeConfig = [];
    serviceSpy.getFuelPrices.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshData();

    //Assert
    expect(component.fuelPrices).toEqual(fakeConfig);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });
});
