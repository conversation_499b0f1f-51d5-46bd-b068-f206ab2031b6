using System.Net;
using Htec.Logger.Interfaces.Tracing;

namespace Forecourt.Core.HydraDb.Models
{
    ///
    ///  Name: OptEndPoints
    ///  Description: The OPT End Points read from the database.
    /// 
    public class OptEndPoints
    {
        public const string DefaultHydraId = "Hydra 1";
        public string HydraId { get; set; }
        private string IpAddress { get; }
        private int FromOptPort { get; }
        private int ToOptPort { get; }
        private int HeartbeatPort { get; }
        private int HydraPosPort { get; }
        private int RetalixPosPort { get; }
        private int ThirdPartyPosPort { get; }
        private int MediaChannelPort { get; }

        private IPAddress Ip
        {
            get
            {
                if (IPAddress.TryParse(IpAddress, out IPAddress value))
                {
                    return value;
                }
                else
                {
                    return IPAddress.Loopback;
                }
            }
        }

        private static IPAddress BindIp => IPAddress.Any;

        public IPEndPoint FromOptEndPoint => new IPEndPoint(Ip, FromOptPort);
        public IPEndPoint ToOptEndPoint => new IPEndPoint(Ip, ToOptPort);
        public IPEndPoint HeartbeatEndPoint => new IPEndPoint(Ip, HeartbeatPort);
        public IPEndPoint HydraPosEndPoint => new IPEndPoint(Ip, HydraPosPort);
        public IPEndPoint RetalixPosEndPoint => new IPEndPoint(Ip, RetalixPosPort);
        public IPEndPoint ThirdPartyPosEndPoint => new IPEndPoint(Ip, ThirdPartyPosPort);
        public IPEndPoint MediaChannelEndPoint => new IPEndPoint(Ip, MediaChannelPort);
        public IPEndPoint FromOptBindEndPoint => new IPEndPoint(BindIp, FromOptPort);
        public IPEndPoint ToOptBindEndPoint => new IPEndPoint(BindIp, ToOptPort);
        public IPEndPoint HeartbeatBindEndPoint => new IPEndPoint(BindIp, HeartbeatPort);
        public IPEndPoint HydraPosBindEndPoint => new IPEndPoint(BindIp, HydraPosPort);
        public IPEndPoint RetalixPosBindEndPoint => new IPEndPoint(BindIp, RetalixPosPort);
        public IPEndPoint ThirdPartyPosBindEndPoint => new IPEndPoint(BindIp, ThirdPartyPosPort);
        public IPEndPoint MediaChannelBindEndPoint => new IPEndPoint(BindIp, MediaChannelPort);
        public bool AutoAuth { get; }
        public bool MediaChannel { get; }
        public bool UnmannedPseudoPos { get; }
        public bool AsdaDayEndReport { get; }

        public bool ValidPorts =>
            FromOptPort >= IPEndPoint.MinPort && FromOptPort <= IPEndPoint.MaxPort && ToOptPort >= IPEndPoint.MinPort &&
            ToOptPort <= IPEndPoint.MaxPort && HeartbeatPort >= IPEndPoint.MinPort && HeartbeatPort <= IPEndPoint.MaxPort &&
            HydraPosPort >= IPEndPoint.MinPort && HydraPosPort <= IPEndPoint.MaxPort && RetalixPosPort >= IPEndPoint.MinPort &&
            RetalixPosPort <= IPEndPoint.MaxPort && ThirdPartyPosPort >= IPEndPoint.MinPort && ThirdPartyPosPort <= IPEndPoint.MaxPort &&
            MediaChannelPort >= IPEndPoint.MinPort && MediaChannelPort <= IPEndPoint.MaxPort && FromOptPort != ToOptPort &&
            FromOptPort != HeartbeatPort && FromOptPort != HydraPosPort && FromOptPort != RetalixPosPort &&
            FromOptPort != ThirdPartyPosPort && FromOptPort != MediaChannelPort && ToOptPort != HeartbeatPort &&
            ToOptPort != HydraPosPort && ToOptPort != RetalixPosPort && ToOptPort != ThirdPartyPosPort && ToOptPort != MediaChannelPort &&
            HeartbeatPort != HydraPosPort && HeartbeatPort != RetalixPosPort && HeartbeatPort != ThirdPartyPosPort &&
            HeartbeatPort != MediaChannelPort && HydraPosPort != RetalixPosPort && HydraPosPort != ThirdPartyPosPort &&
            HydraPosPort != MediaChannelPort && RetalixPosPort != ThirdPartyPosPort && RetalixPosPort != MediaChannelPort &&
            ThirdPartyPosPort != MediaChannelPort;

        public OptEndPoints
        (string hydraId = DefaultHydraId, string ipAddress = "127.0.0.1", int fromOptPort = 1262, int toOptPort = 1263,
            int heartbeatPort = 1264, int hydraPosPort = 1261, int retalixPosPort = 10029, int thirdPartyPosPort = 10030,
            int mediaChannelPort = 1266, bool autoAuth = true, bool mediaChannel = false, bool unmannedPseudoPos = false,
            bool asdaDayEndReport = false)
        {
            HydraId = hydraId;
            IpAddress = ipAddress;
            FromOptPort = fromOptPort;
            ToOptPort = toOptPort;
            HeartbeatPort = heartbeatPort;
            HydraPosPort = hydraPosPort;
            RetalixPosPort = retalixPosPort;
            ThirdPartyPosPort = thirdPartyPosPort;
            MediaChannelPort = mediaChannelPort;
            AutoAuth = autoAuth;
            MediaChannel = mediaChannel;
            UnmannedPseudoPos = unmannedPseudoPos;
            AsdaDayEndReport = asdaDayEndReport;
        }

        public bool ValidChange(OptEndPoints prev, bool portsOnly, IHtecLogger logger)
        {
            if (!ValidPorts)
            {
                logger.Warn("New service ports are not valid");
                return false;
            }

            if (FromOptPort == prev.FromOptPort && ToOptPort == prev.ToOptPort && HeartbeatPort == prev.HeartbeatPort &&
                HydraPosPort == prev.HydraPosPort && RetalixPosPort == prev.RetalixPosPort && ThirdPartyPosPort == prev.ThirdPartyPosPort &&
                MediaChannelPort == prev.MediaChannelPort)
            {
                logger.Debug("New Service Ports match old Service Ports");
                if (portsOnly)
                {
                    return false;
                }

                return !prev.IpAddress.Equals(IpAddress);
            }

            if (FromOptPort == prev.ToOptPort || FromOptPort == prev.HeartbeatPort || FromOptPort == prev.HydraPosPort ||
                FromOptPort == prev.RetalixPosPort || FromOptPort == prev.ThirdPartyPosPort || FromOptPort == prev.MediaChannelPort ||
                ToOptPort == prev.FromOptPort || ToOptPort == prev.HeartbeatPort || ToOptPort == prev.HydraPosPort ||
                ToOptPort == prev.RetalixPosPort || ToOptPort == prev.ThirdPartyPosPort || ToOptPort == prev.MediaChannelPort ||
                HeartbeatPort == prev.FromOptPort || HeartbeatPort == prev.ToOptPort || HeartbeatPort == prev.HydraPosPort ||
                HeartbeatPort == prev.RetalixPosPort || HeartbeatPort == prev.ThirdPartyPosPort || HeartbeatPort == prev.MediaChannelPort ||
                HydraPosPort == prev.FromOptPort || HydraPosPort == prev.ToOptPort || HydraPosPort == prev.HeartbeatPort ||
                HydraPosPort == prev.RetalixPosPort || HydraPosPort == prev.ThirdPartyPosPort || HydraPosPort == prev.MediaChannelPort ||
                RetalixPosPort == prev.FromOptPort || RetalixPosPort == prev.ToOptPort || RetalixPosPort == prev.HeartbeatPort ||
                RetalixPosPort == prev.HydraPosPort || RetalixPosPort == prev.ThirdPartyPosPort ||
                RetalixPosPort == prev.MediaChannelPort || ThirdPartyPosPort == prev.FromOptPort || ThirdPartyPosPort == prev.ToOptPort ||
                ThirdPartyPosPort == prev.HeartbeatPort || ThirdPartyPosPort == prev.HydraPosPort ||
                ThirdPartyPosPort == prev.RetalixPosPort || ThirdPartyPosPort == prev.MediaChannelPort ||
                MediaChannelPort == prev.FromOptPort || MediaChannelPort == prev.ToOptPort || MediaChannelPort == prev.HeartbeatPort ||
                MediaChannelPort == prev.HydraPosPort || MediaChannelPort == prev.RetalixPosPort ||
                MediaChannelPort == prev.ThirdPartyPosPort)
            {
                logger.Warn("New Service Ports overlap old Service Ports");
                return false;
            }

            return true;
        }
    }
}