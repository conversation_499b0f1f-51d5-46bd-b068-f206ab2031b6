USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddReceipt') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE AddReceipt
END
GO

CREATE procedure [dbo].[AddReceipt]
 @cardNumber varchar(20),
 @receiptContent varchar(max),
 @expiry DateTime2,
 @opt varchar(1024),
 @transactionNumber bigint,
 @amount bigint,
 @transactionTime DateTime2,
 @printedCount int as
 begin
  begin TRY

   begin transaction

   DECLARE @receiptDataHash VARBINARY(64) = dbo.getReceiptHash(@cardNumber,  @ReceiptContent, @OPT, @TransactionNumber, @amount, @transactionTime, NULL);

   delete from Receipts where CardNumber = @cardNumber and TransactionNumber = @transactionNumber
   if (@receiptContent is not null and @expiry is not null)
    begin
     insert into Receipts (<PERSON>N<PERSON><PERSON>, ReceiptContent, Expiry, OPT, TransactionNumber, ReceiptHash, Amount, TransactionTime, PrintedCount)
      values (@cardNumber, @receiptContent, @expiry, @opt, @transactionNumber, @receiptDataHash, @amount, @transactionTime, @printedCount)
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end
GO

GRANT EXECUTE ON AddReceipt TO UserRole
GO

