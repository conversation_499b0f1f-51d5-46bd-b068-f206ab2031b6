using Forecourt.Core.HydraDb.Models;
using System.Collections.Generic;

namespace Forecourt.Core.Helpers.Interfaces
{
    public interface IGradeHelper
    {
        /// <summary>
        /// Get full grade information for a grade
        /// </summary>
        /// <param name="id">Grade id</param>
        /// <returns><see cref="GradeName"/> instance</returns>
        GradeName GetGradeInfo(byte id);

        /// <summary>
        /// Get grade name
        /// </summary>
        /// <param name="id">Grade id</param>
        /// <returns>Grade name</returns>
        string GetGradeName(byte id);

        /// <summary>
        /// Get a list of <see cref="GradeName"/> values
        /// </summary>
        /// <inheritdoc/>
        IEnumerable<GradeName> Grades {get; }

        /// <summary>
        /// Set full grade information
        /// </summary>
        /// <param name="number">Grade number</param>
        /// <param name="name">Grade name</param>
        /// <param name="vatRate">Grade VAT rate</param>
        /// <param name="reference">Logging reference</param>
        void SetGrade(byte number, string name, float vatRate, string reference = null);

        /// <summary>
        /// Override grade information, using previous VAT rate or default
        /// </summary>
        /// <param name="number">Grade number</param>
        /// <param name="name">Grade name</param>
        /// <param name="reference">Logging reference</param>
        void UpdateGrade(byte number, string name, string reference = null);

        /// <summary>
        /// Override grade information, using previous Name or default
        /// </summary>
        /// <param name="number">Grade number</param>
        /// <param name="vatRate">Grade VAT rate</param>
        /// <param name="reference">Logging reference</param>
        void UpdateGrade(byte number, float vatRate, string reference = null);

        /// <summary>
        /// Remove (or null out) grade information
        /// </summary>
        /// <param name="number">Grade number</param>
        /// <param name="reference">Logging reference</param>
        void RemoveGrade(byte number, string reference = null);

        /// <summary>
        /// Initialise the grades (from HydraDb)
        /// </summary>
        /// <param name="reference"></param>
        void Initialise(string reference = null);
    }
}
