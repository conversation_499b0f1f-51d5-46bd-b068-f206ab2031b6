using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add SECAUTH Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20050002, "ADO#608163 - add SECAUTH Configuraion Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO608163_AddSecAuthConfigurationCategory : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO608163_upgrade_config_categories_install.sql");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO608163_upgrade_config_categories_rollback.sql");
        }
    }
}
