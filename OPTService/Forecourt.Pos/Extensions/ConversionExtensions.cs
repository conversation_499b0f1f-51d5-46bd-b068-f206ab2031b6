using Htec.Hydra.Core.Pos.Messages.HydraMobile;
using System;
using System.Linq;
using bos = Htec.Hydra.Core.Bos.Messages;
using bosMobile = Htec.Hydra.Core.Bos.Messages.Hydra.Mobile;
using posMobile = Htec.Hydra.Core.Pos.Messages.HydraMobile;

namespace Forecourt.Pos.Extensions
{
    public static class ConversionExtensions
    {
        public static bosMobile.LocalTotals CastTo(this posMobile.LocalTotals from)
        {
            return from == null ?
                null :
                new bosMobile.LocalTotals()
                {
                    Amount = from.Amount,
                    FuelTotals = from.FuelTotals.Select(x=> x.CastTo()).ToList()
                };
        }

        public static bosMobile.HostTotals CastTo(this posMobile.HostTotals from)
        {
            return from == null ?
                null :
                new bosMobile.HostTotals()
                {
                    Amount = from.Amount,
                    CardCircuits = from.CardCircuits.Select(x => x.CastTo()).ToList()
                };
        }

        public static bosMobile.FuelTotals CastTo(this posMobile.FuelTotals from)
        {
            return from == null ?
                null :
                new bosMobile.FuelTotals()
                {
                    Name = from.Name,
                    Amount = Convert.ToDecimal(from.Amount),
                    Quantity = Convert.ToDecimal(from.Quantity)
                };
        }

        public static bosMobile.CardCircuit CastTo(this posMobile.CardCircuit from)
        {
            return from == null ?
                null :
                new bosMobile.CardCircuit()
                { 
                    Name = from.Name,
                    Amount = Convert.ToDecimal(from.Amount),
                    Number = from.Number
                };
        }

        public static bosMobile.DayEnd CastTo(this posMobile.DayEnd from)
        {
            if (from == null)
            {
                return null;
            }

            var local = from.LocalTotals;
            var host = from.HostTotals;

            return new bosMobile.DayEnd()
            {
                ShiftNumber = from.ShiftNumber,
                StartTime = from.StartTime,
                EndTime = from.EndTime,
                LocalTotals = local.CastTo(),
                HostTotals = host.CastTo()
            };
        }

        // TODO: What are the defaults here
        public static bos.ShiftEndItem ConvertTo(this bosMobile.DayEnd from, short tillNUmber = 99, string opId = "2", string OpName = "HMOB", bool isDayEnd = false)
        {
            return from == null ?
                null :
                new bos.ShiftEndItem(
                    tillNUmber, from.StartTime, from.EndTime, Convert.ToInt16(from.ShiftNumber), opId, OpName,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, isDayEnd);
        }

        public static Htec.Hydra.Messages.Opt.Models.ProductItem ConvertTo(this posMobile.SaleItem from, string uom = "76")
        {
            return from == null ?
                null :
                new Htec.Hydra.Messages.Opt.Models.ProductItem(
                    from.ItemId,
                    Convert.ToUInt32(from.Quantity * HydraMobileMessage.VolumeFactor),
                    Convert.ToUInt32(from.Amount * HydraMobileMessage.CurrencyFactor),
                    uom
                    );
        }

        public static Htec.Hydra.Messages.Opt.Models.PaymentCleared ConvertTo(this posMobile.Transaction from, byte pump, string txnNumber, string cardNumber)
        {
            return from == null ?
                null :
                new Htec.Hydra.Messages.Opt.Models.PaymentCleared(
                    pump,
                    txnNumber,
                    Convert.ToUInt32(from.TotalAmount * HydraMobileMessage.CurrencyFactor),
                    cardNumber,
                    cardProductName: from.CardCircuit,
                    productItems: from.SaleItems.Select(x => x.ConvertTo()).ToList()
                    );
        }
    }
}
