using System.Net;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace Forecourt.SecondaryAuth.HydraDb.Interfaces
{
    /// <summary>
    /// Any and all defintions of IHydraDb needed by SecAuth, split down by area
    /// </summary>
    public interface IHydraDb: 
        Core.HydraDb.Interfaces.IHydraDb,
        IHydraDbSecAuth        
    {
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by SecAuth, mobile functionality
    /// </summary>
    public interface IHydraDbSecAuth
    {
        /// <summary>
        /// Fetch the ANPR end point from the database.
        /// </summary>
        /// <returns>The ANPR end point.</returns>
        connGenericEndPoint FetchAnprEndPoint();

        /// <summary>
        /// Store End Point for ANPR in the database.
        /// </summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="port">Port number to set.</param>
        void SetAnprEndPoint(IPAddress ip, int port);
    }
}
