using Forecourt.Core.HydraDb.Models;
using Forecourt.Service.OPTServiceClasses;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using Unity;
using Constants = Forecourt.Core.Configuration.Constants;

namespace OPTService.Controllers
{
    /// <summary>
    /// Transaction Controller implementation
    /// </summary>
    [HasConfiguration()]
    [Authorize(Roles = "WebUi,Swagger")]

    public class TransactionController : LoggableApiController
    {
        #region Private properties

        /// <summary>
        /// The web name
        /// </summary>
        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        /// <summary>
        /// Transaction limit for GetFuelTransactions
        /// </summary>
        private uint ConfigValueTransactionLimit => ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyTransactionLimit, DefaultValueTransactionLimit, GetLogger());

        #endregion

        /// <summary>
        /// Config key to set the transaction limit to return fuel transactions
        /// </summary>
        public const string ConfigKeyTransactionLimit = Constants.CategoryNameWeb + Constants.CategorySeparator + "TransactionLimit";

        /// <summary>
        /// Default value for ConfigKeyTransactionLimit
        /// </summary>
        public const uint DefaultValueTransactionLimit = 10;

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        public TransactionController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger, IConfigurationManager configurationManager) : base(logger, configurationManager)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }
        }

        #endregion

        #region Data Retrieval

        /// <summary>
        /// Get the list of fuel transactions between the given dates.
        /// </summary>
        /// <param name="startTime">From date.</param>
        /// <param name="endTime">To date.</param>
        /// <param name="reference">optional logging reference</param>
        /// <returns>List of transactions.</returns>
        [HttpGet]
        public HttpResponseMessage GetFuelTransactions(DateTime startTime, DateTime endTime, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));

            var fuelTransactions = _web.GetFuelTransactions(startTime, endTime).ToList();
            
            if (fuelTransactions.Count > ConfigValueTransactionLimit)
            {
                GetLogger().Debug(FormatMessage($"Returning InternalServerError - Transaction limit exceeded ({fuelTransactions.Count})."));
                GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                return Request.CreateResponse(HttpStatusCode.InternalServerError, new HttpError($"Transactions limit exceeded ({ConfigValueTransactionLimit}). Reduce the date range to return fewer results"));
            }

            var results = fuelTransactions.OrderByDescending(x => x.TransactionTime).Select(MapFuelTransactionDetails).ToArray();

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));

            return Request.CreateResponse(HttpStatusCode.OK, results);
        }

        private FuelTransactionDetails MapFuelTransactionDetails(FuelTransaction fuelTransaction)
        {
            return new FuelTransactionDetails
            {
                TransactionId = fuelTransaction.TransactionId,
                TransactionTime = fuelTransaction.TransactionTime,
                GradeCode = fuelTransaction.GradeCode,
                WashCode = fuelTransaction.WashCode,
                GradeName = fuelTransaction.GradeName,
                WashName = fuelTransaction.WashName,
                PumpDetailsString = fuelTransaction.PumpDetails,
                CardNumber = fuelTransaction.CardNumber,
                FuelQuantity = fuelTransaction.FuelQuantity / (float) 1000.0,
                WashQuantity = fuelTransaction.WashQuantity,
                Amount = fuelTransaction.Amount / (float) 100.0,
                FuelCategory = fuelTransaction.FuelCategory,
                WashCategory = fuelTransaction.WashCategory,
                FuelSubcategory = fuelTransaction.FuelSubcategory,
                WashSubcategory = fuelTransaction.WashSubcategory,
                DiscountName = fuelTransaction.DiscountName,
                DiscountCode = fuelTransaction.DiscountCode,
                DiscountValue = fuelTransaction.DiscountValue / (float) 100.0,
                DiscountCardNumber = fuelTransaction.DiscountCardNumber,
                LocalAccountMileage = fuelTransaction.LocalAccountMileage,
                LocalAccountRegistration = fuelTransaction.LocalAccountRegistration,
                TxnNumber = fuelTransaction.TxnNumber,
                HasReceipt = _web.ReceiptTrans.Contains(fuelTransaction.TransactionId),
                PrinterEnabled = _web.GetPrinterEnabled()
            };
        }

        /// <summary>
        /// Fetch the receipt stroed for the given card number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number for receipt.</param>
        /// <returns>The receipt.</returns>
        [HttpGet]
        public HttpResponseMessage GetReceipt(long transactionNumber)
        {
            var receipt = _web.FetchReceipt(transactionNumber);
            var response = new HttpResponseMessage
            {
                Content = new StringContent(
                JsonConvert.SerializeObject(new ReceiptDetails { TransId = transactionNumber, ReceiptContent = receipt }))
            };
            return response;
        }

        /// <summary>
        /// Get the list of other events between the given dates.
        /// </summary>
        /// <param name="startTime">From date.</param>
        /// <param name="endTime">To date.</param>
        /// <returns>List of transactions.</returns>
        [HttpGet]
        public HttpResponseMessage GetOtherEvents(DateTime startTime, DateTime endTime)
        {
            IList<OtherEventDetails> results = _web.GetOtherEvents(startTime, endTime).Select(x => new OtherEventDetails
                {TransactionId = x.TransactionId, TransactionTime = x.TransactionTime}).ToArray();

            return Request.CreateResponse(HttpStatusCode.OK, results);
        }

        /// <summary>
        /// Print Receipt.
        /// </summary>
        /// <param name="details">Include transaction number.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage PrintReceipt(FuelTransactionDetails details)
        {
            long transactionNumber = details.TransactionId;
            _web.SendChatMessage(WebName, $"Print Receipt, transaction number {transactionNumber}");
            string result = _web.PrintReceipt(transactionNumber);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Save Receipt.
        /// </summary>
        /// <param name="details">Include transaction number.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SaveReceipt(FuelTransactionDetails details)
        {
            long transactionNumber = details.TransactionId;
            _web.SendChatMessage(WebName, $"Save Receipt, transaction number {transactionNumber}");
            string result = _web.SaveReceipt(transactionNumber);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }


        #endregion
    }
}