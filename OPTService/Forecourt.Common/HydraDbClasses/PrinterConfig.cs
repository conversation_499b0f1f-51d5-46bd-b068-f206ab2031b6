using System;
using System.IO.Ports;

namespace OPT.Common.HydraDbClasses
{
    public class PrinterConfig
    {
        public bool Enabled { get; }
        public string PortName { get; }
        public int BaudRate { get; }
        public Handshake Handshake { get; }
        public StopBits StopBits { get; }
        public int DataBits { get; }

        // ReSharper disable once UnusedMember.Global
        public PrinterConfig(bool enabled, string portName, int baudRate, string handshake, string stopBits, int dataBits)
        {
            Enabled = enabled;
            PortName = portName;
            BaudRate = baudRate;
            if (Enum.TryParse(handshake, out Handshake theHandshake))
            {
                Handshake = theHandshake;
            }
            else
            {
                Handshake = Handshake.None;
            }
            if (Enum.TryParse(stopBits, out StopBits theStopBits))
            {
                StopBits = theStopBits;
            }
            else
            {
                StopBits = StopBits.One;
            }

            if (dataBits < 5)
            {
                DataBits = 5;
            }
            else if (dataBits > 8)
            {
                DataBits = 8;
            }
            else
            {
                DataBits = dataBits;
            }
        }
    }
}
