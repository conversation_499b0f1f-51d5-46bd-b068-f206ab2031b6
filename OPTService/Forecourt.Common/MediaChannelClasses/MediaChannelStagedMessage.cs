using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Abstractions;
using System.Linq;
using System.Text;

namespace OPT.Common.MediaChannelClasses
{
    [Obsolete("Use Htec.Foundation.Connection.Models.MediaMessage versions now!")]
    public class MediaChannelStagedMessage : Disposable
    {
        /// <summary>
        /// Config key, for the delay (in ms) between sending chunks of a file
        /// </summary>
        public const string ConfigKeyChunkDelayInMs = "CONNECTIVITY::CHUNK:DELAY:MS:" + nameof(MediaChannelStagedMessage);

        /// <summary>
        /// Default value, for the delay (in ms) between sending chunks of a file
        /// </summary>
        public const int DefaultValueChunkDelayInMs = 100;

        /// <summary>
        /// Config key, for the delay (in ms) between sending chunks of a file
        /// </summary>
        public const string ConfigKeyChunkSizeInMb = "CONNECTIVITY::CHUNK:SIZE:MB:" + nameof(MediaChannelStagedMessage);

        /// <summary>
        /// Default value, for the delay (in ms) between sending chunks of a file
        /// </summary>
        public const int DefaultValueChunkSizeInMb = 1024;

        private int ChunkSize { get; }
        private const byte TagByte = 0xFF;

        private readonly FileStream _stream;
        private readonly byte[] _nextBytes;
        private byte[] _currentBytes = new byte[0];

        /// <summary>
        /// Delay (in ms) between sending chunks of a file
        /// </summary>
        public int ChunkDelay { get; }

        public int Remaining { get; private set; } = 0;
        public string FileName { get; }
        public int FileLength { get; }

        public MediaChannelStagedMessage(IHtecLogger logger, IConfigurationManager configurationManager, string fileName, IFileInfoFactory fileInfoFactory): base(logger, configurationManager)
        {
            if (fileInfoFactory == null)
            {
                throw new ArgumentNullException(nameof(fileInfoFactory));
            }

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            var appSettings = ConfigurationManager.AppSettings;
            ChunkDelay = appSettings.GetAppSettingOrDefault(ConfigKeyChunkDelayInMs, DefaultValueChunkDelayInMs, LoggerIConfigurationManager);
            ChunkSize = appSettings.GetAppSettingOrDefault(ConfigKeyChunkSizeInMb, DefaultValueChunkSizeInMb, LoggerIConfigurationManager) * 1024;
            _nextBytes = new byte[ChunkSize];

            var fileInfo = fileInfoFactory.FromFileName(fileName);
            if (fileInfo.Exists)
            {
                FileName = fileInfo.Name;
                FileLength = (int) fileInfo.Length;
                _stream = File.OpenRead(fileName);
                _currentBytes = GetHeaderBytes(fileInfo.Name, (int) fileInfo.Length, out var remaining);
                Remaining = remaining;
            }
            else
            {
                FileName = "Not Found";
                FileLength = 0;
            }
        }

        public byte[] GetNextBytes()
        {
            if (_currentBytes.Length < ChunkSize && _currentBytes.Length < Remaining)
            {
                var bytesRead = _stream.Read(_nextBytes, 0, (ChunkSize < Remaining ? ChunkSize : Remaining) - _currentBytes.Length);
                _currentBytes = _currentBytes.Concat(_nextBytes.Take(bytesRead)).ToArray();
                if (_currentBytes.Length < ChunkSize && _currentBytes.Length < Remaining)
                {
                    _currentBytes = _currentBytes.Concat(new byte[Remaining - _currentBytes.Length]).ToArray();
                }
            }

            var bytes = _currentBytes.Take(ChunkSize < Remaining ? ChunkSize : Remaining).ToArray();
            _currentBytes = _currentBytes.Skip(bytes.Length).ToArray();
            Remaining -= bytes.Length;

            return bytes;
        }

        private static byte[] GetHeaderBytes(string fileName, int fileSize, out int fullLength)
        {
            var length = fileName.Length + 6 + fileSize + 6;
            fullLength = length + 6;

            var result = new List<byte>() {TagByte, (byte) MediaChannelMessageType.GetFileResponse};
            var bytes = BitConverter.GetBytes(length);
            result.AddRange(BitConverter.IsLittleEndian ? bytes : bytes.Reverse());

            result.Add(TagByte);
            result.Add((byte)MediaChannelMessageComponentType.FileName);
            bytes = BitConverter.GetBytes(fileName.Length);
            result.AddRange(BitConverter.IsLittleEndian ? bytes : bytes.Reverse());
            result.AddRange(Encoding.ASCII.GetBytes(fileName));

            result.Add(TagByte);
            result.Add((byte)MediaChannelMessageComponentType.FileContent);

            bytes = BitConverter.GetBytes(fileSize);
            result.AddRange(BitConverter.IsLittleEndian ? bytes : bytes.Reverse());

            return result.ToArray();
        }

        protected override void DoDisposeDisposing()
        {
            _stream?.Dispose();

            base.DoDisposeDisposing();
        }
    }
}