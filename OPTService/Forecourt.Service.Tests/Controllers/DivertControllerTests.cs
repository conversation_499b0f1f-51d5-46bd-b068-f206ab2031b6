using FluentAssertions;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class DivertControllerTests
    {
        private readonly IWeb _web;
        private readonly IHtecLogger _logger;
        private readonly ILoggingHelper _loggingHelper;
        
        public DivertControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _web = Substitute.For<IWeb>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
        }

        [Fact]
        public void test_GetDivertDetails_returns_Ok()
        {
            // Arrange
            var divertDetails = new DivertDetails()
            {
                FromOptPort = 1,
                ToOptPort = 2,
                HeartbeatPort = 3,
                MediaChannelPort = 4,
                IpAddress = "127.0.0.1",
                IsDiverted = true,
                MediaChannel = true            
            };
            _web.GetDivertDetails().Returns(divertDetails);

            var controller = new DivertController(_web, _logger);

            // Act
            var actual = controller.GetDivertDetails();

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<DivertDetails>>().Which.Content.Should().BeEquivalentTo(divertDetails);
        }

        [Fact]
        public void test_GetDivertDetails_returns_NotFound_result()
        {
            // Arrange
            DivertDetails divertDetails = null;
            _web.GetDivertDetails().Returns(divertDetails);

            var controller = new DivertController(_web, _logger);

            // Act
            var actual = controller.GetDivertDetails();

            // Assert
            actual.Should().BeOfType<NotFoundResult>();
        }
    }
}

