using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Messages.Opt.Models;
using OPT.Common.Models;
using System;
using System.Collections.Generic;
using System.Net;
using System.Xml.Linq;
using CardAmountSalesItem = Htec.Hydra.Core.Bos.Messages.CardAmountSalesItem;

namespace OPT.Common.Workers.Interfaces
{
    public interface IFromOptWorker : IListenerWorker<XElement>, IPumpIntegratorOutTransient<IMessageTracking>, IPosIntegratorOutMode<IMessageTracking>, IBosIntegratorOutJournal<IMessageTracking>
    {
        bool AutoAuth { get; }
        bool MediaChannel { get; }
        bool UnmannedPseudoPos { get; }
        bool AsdaDayEndReport { get; }
        IList<TermId> FetchedTids { get; }

        ICollection<GradeName> GradeNames { get; }

        IList<string> AvailableSoftware { get; }
        IList<string> AvailableSecureAssets { get; }
        IList<string> AvailableCpatAssets { get; }
        GenericOptConfig GenericOptConfig { get; }
        string WhitelistDirectory { get; }
        string LayoutDirectory { get; }
        string SoftwareDirectory { get; }
        string MediaDirectory { get; }
        string PlaylistDirectory { get; }

        string OptLogFileDirectory { get; }

        void WaitForOpts();

        /// <summary>
        /// Inform the OPT Worker of a change of pump state.
        /// </summary>
        /// <param name="state">New pump state.</param>
        /// <param name="pump">Number of changed pump.</param>
        /// <param name="hose">Current hose number of changed pump.</param>
        /// <param name="grade">Current grade number of changed pump.</param>
        /// <param name="volume">Current delivered volume of changed pump in ml.</param>
        /// <param name="amount">Current delivered value of changed pump in pence.</param>
        /// <param name="ppu">Current price of current grade of changed pump in tenths of a penny.</param>
        /// <param name="paid">Indicates whether all transactions are paid.</param>
        /// <param name="allGrades">List of all grades on pump.</param>
        /// <param name="isTimer">Was this event called from an OnTimer event</param>
        /// <param name="wasBlocked">Was the pump blocked on the last OnPumpState cycle</param>
        /// <param name="isBlocked">Is the pump currently blocked, by a call to ANPR_Stop</param>
        /// <param name="isOptAvailable">Is the pump linked to an OPT</param>
        /// <param name="isOptInControl">Is the pump being controlled by OPT</param>
        /// <param name="isPendingTxnOnReboot">Is there a pending unpaid transaction, following a re-start</param>
        /// <param name="message">Logging reference</param>
        /// <param name="isError"></param>
        /// <param name="pos">POS number of changed pump</param>
        /// <param name="transSeqNum">Transaction Sequence Number</param>
        void OnPumpState(IMessageTracking message, PumpState state, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, IList<byte> allGrades, bool isTimer = false,
            bool wasBlocked = false, bool isBlocked = false, bool isOptAvailable = false, bool isOptInControl = false, bool isPendingTxnOnReboot = false, bool isError = false, byte pos = 0, int transSeqNum = 0);

        /// <summary>Inform the OPT Worker of a change of payment timeout.</summary>
        /// <param name="mode">The mode for which the payment timeout is being changed.</param>
        /// <param name="timeout">New timeout in seconds.</param>
        void SetPaymentTimeout(PaymentTimeoutType mode, int timeout);

        /// <summary>
        /// Get the payment timeout for a given mode.
        /// </summary>
        /// <param name="mode">The mode to fetch.</param>
        /// <returns>The timeout in seconds.</returns>
        int GetPaymentTimeout(PaymentTimeoutType mode);

        /// <summary>Tell OPT Worker to set the payment timeout of an OPT that has chnged state.</summary>
        /// <param name="opt">The OPT for which the state has changed.</param>
        /// <param name="modeOverride">Mode override</param>
        void SetPaymentTimeout(IOpt opt, OptModeType? modeOverride = null);

        /// <summary>Check whether the OPT Config has changed since last sent to the OPT.</summary>
        /// <param name="opt">The OPT to check.</param>
        void CheckOptConfig(IOpt opt, bool suppressModeChange = false, string caller = "");

        /// <summary>
        /// Sends the ConfigPending to the selected opt
        /// </summary>
        /// <param name="opt">The OPT to send the notification to</param>
        void SendConfigPendingNotification(IOpt opt);

        /// <summary>
        /// Sends the RequestLog to the selected opt
        /// </summary>
        /// <param name="opt">The OPT to send the notification to</param>
        void SendRequestLogNotification(IOpt opt);

        /// <summary>
        /// Sends the EngineerReset notification to the selected opt
        /// </summary>
        /// <param name="opt">The OPT to send the notification to</param>
        void EngineerResetOptNotification(IOpt opt);

        /// <summary>Check whether the state of a pump has changed.</summary>
        /// <param name="pump">The pump to check.</param>
        /// <param name="prevState">Previous value of state.</param>
        /// <param name="message">Current message</param>
        /// <param name="sendPushChangeNotifications">Send PushChange notifications or not, default true</param>
        void CheckPumpState(IPump pump, PumpStateType prevState, IMessageTracking message = null, bool sendPushChangeNotifications = true);

        /// <summary>Send heartbeat message to all unknown To Opt sockets.</summary>
        [Obsolete("Use IToOptWorker!")]
        void SendToOptHeartbeat();

        /// <summary>Check if eSocket.POS end point list has changed.</summary>
        void CheckEsocketChanges();

        /// <summary>Set AutoAuth flag.</summary>
        /// <param name="isOn">New value of flag.</param>
        void SetAutoAuth(bool isOn);

        /// <summary>Set MediaChannel flag.</summary>
        /// <param name="isOn">New value of flag.</param>
        void SetMediaChannel(bool isOn);

        /// <summary>Set UnmannedPseudoPos flag.</summary>
        /// <param name="isOn">New value of flag.</param>
        void SetUnmannedPseudoPos(bool isOn);

        /// <summary>Set AsdaDayEndReport flag.</summary>
        /// <param name="isAsda">New value of flag.</param>
        void SetAsdaDayEndReport(bool isAsda);

        /// <summary>Set Grade Name and VAT Rate.</summary>
        /// <param name="number">Grade number of grade to change.</param>
        /// <param name="name">New name of grade.</param>
        /// <param name="vatRate">New VAT rate of grade.</param>
        void SetGradeName(byte number, string name, float vatRate);

        /// <summary>Set Grade Name.</summary>
        /// <param name="number">Grade number of grade to change.</param>
        /// <param name="name">New name of grade.</param>
        void SetGradeName(byte number, string name);

        /// <summary>Set Grade VAT Rate.</summary>
        /// <param name="number">Grade number of grade to change.</param>
        /// <param name="vatRate">New VAT rate of grade.</param>
        void SetGradeName(byte number, float vatRate);

        /// <summary>Clear Grade Name and VAT Rate.</summary>
        /// <param name="number">Grade number of grade to clear.</param>
        void SetGradeName(byte number);

        /// <summary>Change to sending the config of different Hydra ID.</summary>
        /// <param name="hydraId">Id of config to send.</param>
        void Redirect(string hydraId);

        /// <summary>Send restart message to all OPTs.</summary>
        void RestartAll();

        /// <summary>
        /// Set divert values for the OPT Config.
        /// </summary>
        /// <param name="address">IP address for diverted service.</param>
        /// <param name="fromOptPort">From OPT port for diverted service.</param>
        /// <param name="toOptPort">To OPT port for diverted service.</param>
        /// <param name="heartbeatPort">Heartbeat port for diverted service.</param>
        /// <param name="mediaChannelPort">Heartbeat port for diverted service.</param>
        void DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort);

        /// <summary>
        /// Cancel divert values for the OPT Config.
        /// </summary>
        void CancelDivertOptService();

        /// <summary>
        /// Check whether divert values are set for the OPT Config.
        /// The IP address and ports are set whether the diversion is currently on or not.
        /// </summary>
        /// <param name="address">IP address for diverted service.</param>
        /// <param name="fromOptPort">From OPT port for diverted service.</param>
        /// <param name="toOptPort">To OPT port for diverted service.</param>
        /// <param name="heartbeatPort">Heartbeat port for diverted service.</param>
        /// <param name="mediaChannelPort">Media Channel port for diverted service.</param>
        /// <returns>True is currently diverted, false otherwise.</returns>
        bool IsOptServiceDiverted
            (out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort, out int mediaChannelPort);

        /// <summary>
        /// Inform the OPT that no ticket has been returned by the Car Wash server.
        /// <param name="opt">OPT for which request was sent.</param>
        /// </summary>
        void CarWashNoTicket(IOpt opt);

        /// <summary>
        /// Inform the OPT that a ticket has been returned by the Car Wash server.
        /// </summary>
        /// <param name="opt">OPT for which request was sent.</param>
        /// <param name="ticketNum">Ticket Number for ticket.</param>
        /// <param name="version">Version for ticket.</param>
        /// <param name="pin">PIN for ticket.</param>
        void CarWashTicket(IOpt opt, string ticketNum, string version, string pin = null);

        /// <summary>
        /// Fetch the generic OPT config from the database.
        /// </summary>
        void FetchGenericOptConfig(bool checkEsocket = false, bool forceRefresh = false);

        /// <summary>
        /// Check the Whitelist Files.
        /// </summary>
        void CheckWhitelistFiles();

        /// <summary>
        /// Check the Layout Files.
        /// </summary>
        void CheckLayoutFiles();

        /// <summary>
        /// Check the Contactless Properties File.
        /// </summary>
        void CheckContactlessProperties();

        /// <summary>
        /// Check the Media Files.
        /// </summary>
        void CheckMediaFiles();

        /// <summary>
        /// Check the Software Files.
        /// </summary>
        void CheckSoftwareFiles();

        /// <summary>
        /// Check the Rollback Files.
        /// </summary>
        void CheckRollbackFiles();

        /// <summary>
        /// Send Day End Notification to OPTs.
        /// </summary>
        /// <param name="cardAmountItems">List of card amounts.</param>
        /// <param name="shiftNumber">Shift Number.</param>
        [Obsolete("Use IBosIntegratorOutJournal<IMessageTracking>.DayEndResponse, but left in for unit tests")]
        void SendDayEndNotification(IEnumerable<CardAmountSalesItem> cardAmountItems, int shiftNumber);

        /// <summary>
        /// Send restart to OPT.
        /// </summary>
        /// <param name="optId">Serial number of OPT to send to.</param>
        /// <returns>True if request sent, false otherwise.</returns>
        bool RestartOpt(string optId);

        /// <summary>
        /// Add an OPT to the list of OPTs needing a config check.
        /// </summary>
        /// <param name="opt">OPT to add, if null all OPTs are added.</param>
        void CheckConfigNeeded(IOpt opt = null);

        IPAddress GetOptIpAddress(IOpt opt);
        void SetWhitelistDirectory(string directory);
        void SetLayoutDirectory(string directory);
        void SetSoftwareDirectory(string directory);
        void SetMediaDirectory(string directory);
        void SetPlaylistDirectory(string directory);
        void SetOptLogFileDirectory(string directory);
        void FetchAvailableSoftware();
        void FetchMediaFilesList();
        void FetchPlaylistFilesList();
        string GetGradeName(byte grade);
    }
}