using System.Collections.Generic;

namespace OPT.Common.HydraDbClasses
{
    public class DiscountCard
    {
        public string Iin { get; }
        public string Name { get; }
        public string Type { get; }
        public float Value { get; }
        public byte Grade { get; }
        public IList<string> Whitelist { get; } = new List<string>();

        public DiscountCard(string iin, string name, string type, double value, int grade)
        {
            Iin = iin;
            Name = name;
            Type = type;
            Value = (float) value;
            Grade = (byte) grade;
        }

        public void SetWhitelist(IEnumerable<string> whitelist)
        {
            Whitelist.Clear();
            ((List<string>)Whitelist).AddRange(whitelist);
        }
    }
}