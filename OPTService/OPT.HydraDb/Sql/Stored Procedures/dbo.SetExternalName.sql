USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[SetExternalName]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE SetExternalName
END
GO

CREATE PROCEDURE SetExternalName
 @cardName VARCHAR(MAX),
 @externalCardName VARCHAR(MAX) AS
 BEGIN
  IF EXISTS (SELECT * FROM CardReference WHERE CardProductName = @cardName)
   BEGIN
    UPDATE CardReference SET CardExternalName = @externalCardName WHERE CardProductName = @cardName
   END
  ELSE
   BEGIN
    DECLARE @cardRef INT
    IF EXISTS (SELECT * FROM CardReference)
     BEGIN
      SET @cardRef = (SELECT MAX(CardRef) FROM CardReference) + 1
     END
    ELSE
     BEGIN
      SET @cardRef = 1
     END
    INSERT INTO CardReference (<PERSON>R<PERSON>, CardProductName, CardExternalName) VALUES (@cardRef, @cardName, @externalCardName)
   END
 END
GO

GRANT EXECUTE ON SetExternalName TO UserRole
GO