namespace Forecourt.Core.Payment.Enums
{
    /// <summary>
    /// Result of the payment.
    /// </summary>
    public enum PaymentResult
    {
        /// <summary>
        /// Payment card approved for pre-auth.
        /// </summary>
        Approved,

        /// <summary>
        /// Payment cancelled.
        /// </summary>
        Cancelled,

        /// <summary>
        ///  Payment cleared and funds taken.
        /// </summary>
        Cleared,

        /// <summary>
        /// Card approved and payment authorised.
        /// </summary>
        ApprovedAndAuthorised
    }
}
