using CSharpFunctionalExtensions;
using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Extensions;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations
{
    /// <summary>
    /// Base class of all Migrations, can't inherit Loggable!!
    /// </summary>
    public abstract class Migrationable: Migration
    {
        /// <summary>
        /// Shortcut property to the Schema embedded resource path
        /// </summary>
        protected string EmbeddedResourcePathSchema => $"{Constants.EmbeddedResourcePrefix}.{Constants.MigrationTypeSchema}";

        /// <summary>
        /// Shortcut property to the Programmatic/Stored Procedures embedded resource path
        /// </summary>
        protected string EmbeddedResourcePathStoredProcedures => $"{Constants.EmbeddedResourcePrefix}.{Constants.EmbededResourceSqlTypeStoredProcedures}";

        /// <summary>
        /// Shortcut property to the Programmatic/Functions embedded resource path
        /// </summary>
        protected string EmbeddedResourcePathFunctions => $"{Constants.EmbeddedResourcePrefix}.{Constants.EmbededResourceSqlTypeFunctions}";


        /// <summary>
        /// Shortcut property to the Data embedded resource path
        /// </summary>
        protected string EmbeddedResourcePathData => $"{Constants.EmbeddedResourcePrefix}.{Constants.MigrationTypeData}";

        /// <summary>
        /// IDbExecutorFactory instance
        /// </summary>
        protected IDbExecutorFactory DbExecutorFactory { get; }

        /// <summary>
        /// IFileSystem instance
        /// </summary>
        protected IFileSystem FileSystem { get; }

        protected readonly object SyncObject = new object();

        // TODO: use IHtecLogger/Loggable instead
        /// <summary>
        /// ILogger instance
        /// </summary>
        protected ILogger<Migrationable> Logger { get; }

        /// <summary>
        /// General list of DbObjects
        /// </summary>
        protected IList<string> DbObjects { get; set; }

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">ILogger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        protected Migrationable(ILogger<Migrationable> logger = null, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null)
        {
            Logger = logger;
            DbExecutorFactory = dbExecutorFactory;
            FileSystem = fileSystem;
        }

        /// <summary>
        /// Simple class to hold the results of a custom Db object definition query
        /// </summary>
        public class DbObjectDefinition
        {
            /// <summary>
            /// Id of the Db object
            /// </summary>
            public int Id { get; set; }

            /// <summary>
            /// Name of the Db object
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// Type of the DbObject (P=STORED PROCEDURE, FN=FUNCION, V=VIEW)
            /// </summary>
            public string Type { get; set; }

            /// <summary>
            /// Sql definition of the Db object
            /// </summary>
            public string Sql { get; set; }
        }

        /// <summary>
        /// Attempts to backup the current version of the given Db objects to a prefixed Backup folder
        /// </summary>
        /// <param name="prefix">Folder prefix</param>
        /// <param name="dbObjects">List of Db objects to obtain source and save</param>
        /// <returns>Result</returns>
        protected Result<bool, Exception> BackupDbObjects(string prefix, params string[] dbObjects)
        {
            if (FileSystem == null)
            {
                throw new InvalidOperationException(nameof(FileSystem));
            }

            if (DbExecutorFactory == null)
            {
                throw new InvalidOperationException(nameof(DbExecutorFactory));
            }

            try
            {
                var sql = "SELECT a.object_id AS Id, a.name, a.Type AS Type, definition AS Sql " +
                          "FROM sys.all_objects a JOIN sys.all_sql_modules asm ON a.object_id = asm.object_id AND a.type IN ('P', 'FN', 'V') " +
                          "WHERE (a.name LIKE @dbObject)";

                var folder = FileSystem.Path.Combine(Constants.RootHydraDbFolder, Constants.BackupHydraDbFolder, prefix);
                FileSystem.Directory.CreateDirectory(folder);

                foreach (var dbObject in dbObjects)
                {
                    var dbObject1 = dbObject.Replace(Constants.SchemaNameDefault, string.Empty);

                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        var results = db.Query<DbObjectDefinition>(sql, new { dbObject = $"%{dbObject1}%" }, commandType: CommandType.Text).ToList();

                        if (!results.Any())
                        {
                            var dropCommand = GenerateDropCommand(dbObject, DbObjectType.StoredProcedure);

                            WriteFile($"{FileSystem.Path.Combine(folder, EnsureSchemaPrefixeDbObjectName(dbObject))}{Constants.FileExtensionSql}", dropCommand, "Default rollback sql saved", FileMode.Create);
                        }
                        else
                        {
                            foreach (var result in results)
                            {
                                var dropCommand = GenerateDropCommand(result.Name, result.Type.Trim().ToEnumFromDescription<DbObjectType>());

                                WriteFile($"{FileSystem.Path.Combine(folder, EnsureSchemaPrefixeDbObjectName(result.Name))}{Constants.FileExtensionSql}", $"{dropCommand}{result.Sql}{Environment.NewLine}",
                                    "Rollback sql saved", FileMode.Create);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"{nameof(BackupDbObjects)} failed!");
                return Result.Failure<bool, Exception>(ex);
            }

            return Result.Success<bool, Exception>(true);
        }

        private string GenerateDropCommand(string dbObject, DbObjectType dbObjectType)
        {
            var fullType = dbObjectType switch
            {
                DbObjectType.StoredProcedure => "PROCEDURE",
                DbObjectType.Function => "FUNCTION",
                DbObjectType.View => "VIEW",
                _ => "PROCEDURE",
            };

            return $"IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('{dbObject}') AND TYPE IN ('{dbObjectType.ToDescriptionValue()}')){Environment.NewLine}" +
                $"BEGIN{Environment.NewLine}DROP {fullType} {dbObject}{Environment.NewLine}END{Environment.NewLine}GO{Environment.NewLine}{Environment.NewLine}";
        }

        /// <summary>
        /// Attempts to rollback the given Db objects from a prefixed Backup folder
        /// </summary>
        /// <param name="prefix">Folder prefix</param>
        /// <param name="dbObjects">List of Db objects to obtain source and save</param>
        /// <returns>Result</returns>
        protected Result<bool, Exception> RestoreDbObjects(string prefix, params string[] dbObjects)
        {
            if (FileSystem == null)
            {
                throw new InvalidOperationException(nameof(FileSystem));
            }

            if (DbExecutorFactory == null)
            {
                throw new InvalidOperationException(nameof(DbExecutorFactory));
            }

            try
            {
                var rollBackFolder = FileSystem.Path.Combine(Constants.RootHydraDbFolder, Constants.BackupHydraDbFolder, prefix);
                if (!FileSystem.Directory.Exists(rollBackFolder))
                {
                    return Result.Failure<bool, Exception>(new DirectoryNotFoundException($"Rollback folder not found: {rollBackFolder}"));
                }

                foreach (var dbObject in dbObjects)
                {
                    Execute.Script($"{FileSystem.Path.Combine(rollBackFolder, $"{EnsureSchemaPrefixeDbObjectName(dbObject)}{Constants.FileExtensionSql}")}");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"{nameof(RestoreDbObjects)} failed!");
                return Result.Failure<bool, Exception>(ex);
            }

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Writes the content to a file
        /// </summary>
        /// <param name="fileName">Full filename</param>
        /// <param name="contents">String contents</param>
        /// <param name="logMessage">Log message to output</param>
        /// <param name="fileMode">FileMode value, default FileMode.Append</param>
        /// <param name="fileAccess">FileAccess value, default FileAccess.Write</param>
        /// <param name="fileShare">FileShare value, default FileShare.None</param>
        protected void WriteFile(string fileName, string contents, string logMessage, FileMode fileMode = FileMode.Append, FileAccess fileAccess = FileAccess.Write,
            FileShare fileShare = FileShare.None)
        {
            if (FileSystem == null)
            {
                throw new InvalidOperationException(nameof(FileSystem));
            }

            var folder = FileSystem.Path.GetDirectoryName(fileName);
            FileSystem.Directory.CreateDirectory(folder);

            lock (SyncObject)
            {
                using (var file = FileSystem.FileStream.Create(fileName, fileMode, fileAccess, fileShare))
                {
                    using (var writer = new StreamWriter(file))
                    {
                        writer.Write(contents);
                    }
                }

                Logger?.LogInformation($"{logMessage}: {fileName}");
            }

            VerifyFile(fileName);
        }

        /// <summary>
        /// Verifies that the file exists
        /// </summary>
        /// <param name="fileName">Full filename</param>
        protected void VerifyFile(string fileName)
        {
            try
            {
                var fileInfo = FileSystem.FileInfo.FromFileName(fileName);
                Logger?.LogInformation($"BytesWritten: {fileInfo.Length}; File: {fileName}");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"Exception.File: {fileName}");
            }
        }

        /// <summary>
        /// Ensures that the Db Object name has a schema prefix, with dbo. being the default
        /// </summary>
        /// <param name="name">Db Object name</param>
        /// <returns>Correctly formatted name</returns>
        public static string EnsureSchemaPrefixeDbObjectName(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                return name;
            }

            if (name.Contains("."))
            {
                return name;
            }

            return $"{Constants.SchemaNameDefault}{name}";
        }
    }
}
