using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using System.Net;

namespace Forecourt.Pos.Workers.Interfaces
{
    /// <summary>
    /// Placeholder interface for all Pos based listener workers
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public interface IPosListenerWorker<T> : IListenerWorker<T>, IPosIntegratorOut<IMessageTracking>, IPosIntegratorInTransient<IMessageTracking, IPump, Result>
    {
        /// <summary>
        /// IpAddress of the Primary Pos
        /// </summary>
        IPAddress PrimaryIpAddress { get; }        
    }
}
