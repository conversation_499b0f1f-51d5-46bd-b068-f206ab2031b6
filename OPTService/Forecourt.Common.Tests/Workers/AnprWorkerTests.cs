using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System.Net;
using Xunit;
using foundation = Htec.Foundation.Connections;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Tests.Workers
{
    public class AnprWorkerTests
    {
        public const string DefaultLoggingReference = "LOGGING-REFERENCE";

        private readonly IAnprWorker _anprWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IPumpWorker _hscWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPumpCollection _allPumps;
        private readonly IHtecLogger _logger;
        private readonly IPump _pumpOne;
        private readonly IPump _pumpTwo;
        private readonly IClientConnectionThread<string> _connectionThread;
        private readonly IMessageTracking _messageTracking;
        private readonly ISecAuthIntegratorInTransient<IMessageTracking> _secAuthInWorker;

        public AnprWorkerTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _allPumps = Substitute.For<IPumpCollection>();
            _logger = Substitute.For<IHtecLogger>();
            _pumpOne = Substitute.For<IPump>();
            _pumpOne.Number.Returns((byte) 1);
            _pumpTwo = Substitute.For<IPump>();
            _allPumps.TryGetPump(Arg.Any<byte>(), out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (byte) x[0] == 1 ? _pumpOne : (byte) x[0] == 2 ? _pumpTwo : null;
                return (byte) x[0] == 1 || (byte) x[0] == 2;
            });
            _connectionThread = Substitute.For<IClientConnectionThread<string>>();

            _messageTracking = Substitute.For<IMessageTracking>();
            _secAuthInWorker = Substitute.For<ISecAuthIntegratorInTransient<IMessageTracking>>();

            _anprWorker = new AnprWorker(_telemetryWorker, _hydraDb, _secAuthInWorker, _allPumps, _logger, _connectionThread);
            _anprWorker.OnConnected(IPAddress.Loopback);
        }

        [Fact]
        public void test_unknown_command()
        {
            // Arrange
            const string message = ConfigConstants.Unknown;

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
        }
        [Fact]
        public void test_invalid_command()
        {
            // Arrange
            const byte pump = 1;
            const string reg = "GEN11";
            string message = $"REG=:   REG={pump},{reg},OK:REG=";

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
            _hscWorker.Received(1).EmergencyStopUpdate(pump, reg, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_send_request_no_connection()
        {
            // Arrange
            _anprWorker.OnDisconnected();

            // Act
            _anprWorker.SendSecondaryAuthRequest(_pumpOne, _messageTracking);

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
        }

        [Fact]
        public void test_send_request()
        {
            // Arrange
            const byte pump = 1;
            var expectedText = $"GetReg={pump}/";
            _connectionThread.Send(expectedText, null);

            // Act
            _anprWorker.SendSecondaryAuthRequest(_pumpOne, _messageTracking);

            // Assert
            _connectionThread.Received(1).Send(Arg.Is<string>(x => x == expectedText), Arg.Any<string>(), Arg.Any<AsyncSocketState<foundation.Sockets.Interfaces.ISocketWithBuffer>>());
        }

        [Fact]
        public void test_receive_ok_response()
        {
            // Arrange
            const byte pump = 1;
            const string reg = "GEN11";
            var message = $"REG={pump},{reg},OK:";

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
            _hscWorker.Received(1).EmergencyStopUpdate(pump, reg, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_receive_two_ok_responses()
        {
            // Arrange
            const byte pump1 = 1;
            const string reg1 = "GEN11";
            const byte pump2 = 2;
            const string reg2 = "GEN12";
            string message = $"REG={pump1},{reg1},OK:REG={pump2},{reg2},OK:";

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
            _pumpTwo.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
            _hscWorker.Received(1).EmergencyStopUpdate(pump1, reg1, Arg.Any<IMessageTracking<string>>());
            _hscWorker.Received(1).EmergencyStopUpdate(pump2, reg2, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_receive_reject_response()
        {
            // Arrange
            const byte pump = 1;
            const string reg = "GEN11";
            string message = $"REG={pump},{reg},REJECT:";

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(false, Arg.Any<string>());
            _hscWorker.Received(1).EmergencyStop(pump, reg, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_receive_ok_response_with_authorise()
        {
            // Arrange
            const byte pump = 1;
            const string reg = "GEN11";
            const uint amount = 1000;
            string message = $"REG={pump},{reg},OK:";
            _pumpOne.IsAuthorised(out _, Arg.Any<string>()).Returns(x =>
            {
                x[0] = amount;
                return true;
            });

            // Act
            _anprWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _pumpOne.Received(1).SetSecAuthResponse(true, Arg.Any<string>());
            _hscWorker.Received(1).EmergencyStopUpdate(pump, reg, Arg.Any<IMessageTracking<string>>());
            _hscWorker.Received(1).PaymentApproved(Arg.Is<byte>(x => x == pump), amount, Arg.Any<IMessageTracking>());
        }
    }
}
