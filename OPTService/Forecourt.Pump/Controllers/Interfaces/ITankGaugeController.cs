using CSharpFunctionalExtensions;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models;
using System.Net;

namespace Forecourt.Pump.Controllers.Interfaces
{
    /// <summary>
    /// Any and all actions of a TankGaugeController, split down in sections to mimic Pump counterparts
    /// </summary>
    public interface ITankGaugeController : IConnectable,
        ITankGaugeControllerConfiguration,
        ITankGaugeControllerJournal
    {
    }

    public interface ITankGaugeControllerJournal
    {
        /// <summary>
        /// Request current Dip information
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result RequestDips(string loggingReference = null);
    }

    public interface ITankGaugeControllerConfiguration
    {
        /// <summary>
        /// Log header
        /// </summary>
        string LogHeader { get; }

        /// <summary>
        /// <see cref="GenericEndPoint"/> of the machine controlling the Tank Gauges.
        /// </summary>
        /// 
        GenericEndPoint CurrentEndPoint { get; }

        /// <summary>
        /// Sets the IpAdress 
        /// </summary>
        /// <param name="ipAddress">IpAddress</param>
        /// <param name="port">Port number</param>
        /// <param name="loggingReference">Current logging reference</param>
        Result SetIpAddress(IPAddress ipAddress, int port, string loggingReference = null);
    }
}
