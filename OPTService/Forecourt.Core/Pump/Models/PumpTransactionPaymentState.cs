using System;

namespace Forecourt.Core.Pump.Models
{
    /// <summary>
    /// Record of when each payment result
    /// </summary>
    public class PumpTransactionPaymentState
    {
        /// <summary>
        /// PaymentResult equivalent 
        /// </summary>
        public Payment.Enums.PaymentResult PaymentResult { get; set; }

        /// <summary>
        /// Result of the payment result
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// When the payment result was received
        /// </summary>
        public DateTime WhenProcessed { get; set; }

        /// <summary>
        /// The logging reference, at this stage of the transaction
        /// </summary>
        public string Reference { get; set; }
    }
}
