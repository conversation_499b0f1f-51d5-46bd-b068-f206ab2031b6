using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.HubClient.Interfaces;
using Htec.HubClient.Managers;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Custom SignalRPosInModeWorker, specific to SignalR
    /// </summary>
    public abstract class SignalRPosInModeWorker : PosInModeWorker, ISignalRPosInModeWorker
    {
        /// <summary>
        /// HubClient instance
        /// </summary>
        protected IHubClient HubClient;

        /// <inheritdoc/>
        protected SignalRPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutTransientWorker,
            IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutTransientWorker, configurationManager)
        {
        }

        /// <summary>
        /// Template for getting the working <see cref="IHubClient"/> instance, with event handler pre-plugged in
        /// </summary>
        /// <param name="endPoint">Hub endpoint</param>
        /// <param name="hubName">Hub name</param>
        /// <param name="retryManager"><see cref="IRetryManager"/> instance</param>
        /// <param name="startParams">List of start parameters, if needed</param>
        /// <returns>Result wrapped <see cref="IHubClient"/> instance</returns>
        protected abstract Result<IHubClient> DoGetHubClient(string endPoint, string hubName, IRetryManager retryManager, params object[] startParams);

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            var endPoint = HydraDb.EndPointsConfig.ConfigValueEndPointsSignalREndPoint.GetValue();
            var hubName = HydraDb.EndPointsConfig.ConfigValueEndPointsSignalRHubNameModeChange.GetValue();

            if (hubName.IsNullOrWhiteSpace())
            { 
                hubName = HydraDb.EndPointsConfig.ConfigValueEndPointsSignalRHubName.GetValue(); 
            }

            if (endPoint.IsNullOrWhiteSpace() || hubName.IsNullOrWhiteSpace())
            {
                return Result.Failure($"Invalid hub configuration. Endpoint: {endPoint}; Hub Name: {hubName}");
            }

            var retryManager = new RetryManager(Logger, ConfigurationManager);

            var result = DoGetHubClient(endPoint, hubName, retryManager, startParams);
            if (!result.IsSuccess)
            {
                return Result.Failure(result.Error);
            }

            HubClient = result.Value;
            HubClient.SignalRConnection += HubClient_SignalRConnection;
            HubClient.StartAsync();

            return base.DoStart(startParams);
        }

        /// <inheritdoc/>
        protected override Result DoStop()
        {
            var result = base.DoStop();

            DisposeHubClient();

            return result;
        }

        /// <summary>
        /// Handled the SignalR connection status
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void HubClient_SignalRConnection(object sender, string e)
        {
            DoAction(() =>
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "SignalRConnection", () => new[] { $"Status: {e}" });

                switch (e)
                {
                    case "Connected":
                        this.OnConnected();
                        break;
                    case "Disconnected":
                        this.OnDisconnected();
                        break;
                }
            }, null);
        }

        /// <summary>
        /// Dispose of the HubClient
        /// </summary>
        private void DisposeHubClient()
        {
            if (HubClient != null)
            {
                HubClient.Stop();
                HubClient.SignalRConnection -= HubClient_SignalRConnection;
                HubClient.Dispose();
                HubClient = null;
            }
        }

        /// <inheritdoc/>
        protected override void DoDisposeDisposing()
        {
            DisposeHubClient();

            base.DoDisposeDisposing();
        }

        /// <inheritdoc/>
        protected override void DoDisposeUnHookInstances()
        {
            HubClient = null;

            base.DoDisposeUnHookInstances();
        }

        /// <inheritdoc/>
        Result<StatusCodeResult> Htec.Hydra.Core.Pos.Interfaces.Core.IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>.RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            var result = RequestModeChange(pump, mode, message);

            return Result.Success<StatusCodeResult>(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(System.Net.HttpStatusCode.BadRequest, new System.Exception(result.Error)));
        }
    }
}
