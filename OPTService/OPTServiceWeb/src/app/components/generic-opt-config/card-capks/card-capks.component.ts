import { Component, Input } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { NgbPanelChangeEvent } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-card-capks',
  templateUrl: './card-capks.component.html',
  styleUrls: ['./card-capks.component.css']
})
export class CardCapksComponent {

  @Input() form: FormGroup;
  @Input() formArrayName: string;

  // Active sections in sub-controls
  activeCardCapk: string = '';
  activeCardCapkIndex: string = '';

  /**
   * The getter for the card AIDs
   */
  get coockedCardCapks(): FormArray {
    return this.form?.get(this.formArrayName) as FormArray;
  }

  toggleActiveCardCapk(props: NgbPanelChangeEvent) {
    this.activeCardCapk = props.panelId;
  }

  toggleActiveCardCapkIndex(props: NgbPanelChangeEvent) {
    this.activeCardCapkIndex = props.panelId;
  }
}
