if not exists (select * from sys.databases where name = '<PERSON><PERSON><PERSON>')
begin
  begin try
    create database Hydra on (filename = 'C:\HydraOPTService\DB\hydra.mdf') for attach
  end try 
  begin catch 
    create database Hydra on primary (name = Hydra, filename = 'C:\HydraOPTService\DB\hydra.mdf')
  end catch
end
go

use Hydra

  if not exists (select 1 from TariffMappings) 
  begin
      insert into TariffMappings (Grade, ProductCode) values (1, '1')
      insert into TariffMappings (Grade, ProductCode) values (2, '5')
      insert into TariffMappings (Grade, ProductCode) values (3, '6')
  end

  if not exists (select 1 from PaymentTimeout) 
  begin
      insert into PaymentTimeout (Mode, Timeout) values (0, 15)
      insert into PaymentTimeout (Mode, Timeout) values (1, 300)
      insert into PaymentTimeout (Mode, Timeout) values (2, 15)
      insert into PaymentTimeout (Mode, Timeout) values (3, 15)
  end

  if not exists (select 1 from SiteInfo) 
  begin
      insert into SiteInfo(Mode, SiteName, VATNumber, NozzleUpForKioskUse, C<PERSON>rencyCode,  TillNumber, FuelCategory)
      values (1, 'XXXX <SiteName>', '343 4753 55', 0, 826, 99, 99)
  end
  
  if not exists (select 1 from PosClaim) 
  begin
    insert into PosClaim(PosNumber) values (99)
  end

  if not exists (select 1 from NextDayEnd) 
  begin
    insert into NextDayEnd(DayEnd) values (null)
  end

  if not exists (select 1 from ReceiptExpiry) 
  begin
    insert into ReceiptExpiry default values
  end
  