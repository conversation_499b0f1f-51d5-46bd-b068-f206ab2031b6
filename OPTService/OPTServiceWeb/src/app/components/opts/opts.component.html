<ng-container *ngIf="showContent">
    <div id="mainOpts" class="row row-cols-1 row-cols-sm-1 row-cols-md-1 row-cols-lg-1 row-cols-xl-2">
        <div *ngFor="let item of optsData; let i = index;" [id]="'item'+i" class="col mb-4">
            <div class="card card-opt">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-2 col-xs-3 text-left">
                            <span>{{item.StringId}}</span><br/>
                            <label *ngFor="let pump of item.Pumps; let j = index" class="badge badge-pill badge-primary badge-pump mr-1">{{pump.Number}}</label>
                        </div>
                        <div class="col-5 col-xs-5">
                            <span class="badge mr-3 badge-opt-connected" 
                                [ngClass]="item.Connected ? 'badge-success' : 'badge-danger'">
                                {{item.Connected ? 'Connected' : 'Not connected'}}
                            </span>
                            <span *ngIf="item.Connected" class="badge mr-3 badge-opt-signed-in"
                                [ngClass]="item.SignedIn ? 'badge-success' : 'badge-danger'">
                                {{item.SignedIn ? 'Signed in' : 'Not signed in'}}
                            </span>
                            <span class="badge badge-info mr-3 badge-opt-mode" [ngClass]="item.ModeChangePending ? 'badge-warning badge-blink' : 'badge-info'">{{item.Status}}</span>
                            <span *ngIf="item.Connected" class="badge mr-3 badge-opt-printer-status"
                                [ngClass]="item.PrinterError ? 'badge-danger' : item.PaperLow ? 'badge-warning' : 'badge-success'">
                                {{item.PrinterError ? 'Printer error' : item.PaperLow ? 'Printer paper low' : 'Printer
                                OK'}}
                            </span>
                            <br/>
                            <span class="badge mr-3" title="{{item.DeviceStatus}}"
                                [ngClass]="item.Connected ? 'badge-success' : 'badge-danger'">
                                {{item.DeviceStatusCode}}
                            </span>
                            <span *ngIf="item.InUse" class="badge badge-warning mr-3 badge-opt-in-use">In Use</span>
                            <span *ngIf="item.ConfigChangePending" class="badge badge-warning mr-3 badge-opt-config-pending">Config Pending</span>
                            <span *ngIf="item.Connected && item.LogFileRequestSent" class="badge badge-warning mr-3 badge-opt-logs-pending">Logs Pending</span>
                        </div>
                        <div class="col-4 col-xs-3 text-right">
                            <button *ngIf="item.Connected && !item.LogFileRequestSent" enabled type="button" name="optRequestLogFile" class="btn btn-primary" (click)="requestOptLog(item, $event)">Logs</button>
                            <button *ngIf="item.Connected && item.LogFileRequestSent" disabled type="button" name="optRequestLogFile" class="btn btn-primary" (click)="requestOptLog(item, $event)">Logs</button>
                            <span>&nbsp;</span>
                            <button type="button" name="optRestartBtn" class="btn btn-primary" (click)="restartOpt(item, $event)">Restart</button>
                            <span>&nbsp;</span>
                            <button type="button" name="optRefreshBtn" class="btn btn-primary" (click)="refreshOpt(item, $event)">Refresh</button>
                        </div>
                        <div class="col-1 text-right" [id]="'heading-opt-collapsible'+i">
                            <a data-toggle="collapse" [href]="'#collapse-opt-body'+item.StringId" aria-expanded="false"
                                [attr.aria-controls]="'collapse-opt-body'+item.StringId" class="d-block" >
                                <i class="bi-chevron-down"></i>
                                <i class="bi-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div [id]="'collapse-opt-body'+item.StringId" class="p-3" [ngClass]="getOptCardState('collapse-opt-body' + item.StringId)"
                            [attr.aria-labelledby]="'heading-opt-collapsible'+i">
                            <app-opt [opt]="item" [opts]="optsData" [tids]="tids" [maxFillOverride]="maxFillOverride" [pumpCardStates]="getPumpCardStates(item)" (pumpCardStatesChange)="updatePumpCardStates(item,$event)"></app-opt>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-center">
                        <a data-toggle="collapse" [href]="'#collapse-opt-body'+item.StringId" aria-expanded="false"
                            [attr.aria-controls]="'collapse-opt-body'+item.StringId" class="d-block">
                            <i class="bi-chevron-up"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <h2 class="h4">Unmapped Pumps</h2>
    <br>
    <app-pumps [pumpsData]="orphanPumps" [parentOpt]="'none'" [opts]="optsData" [tids]="tids" [maxFillOverride]="maxFillOverride" (pumpCardStatesChange)="updatePumpCardStates(item,$event)"></app-pumps>     
</ng-container>
