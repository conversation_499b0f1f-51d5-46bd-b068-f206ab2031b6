using Forecourt.Core.Opt.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using System.Xml.Linq;

namespace OPT.Common.Workers.Interfaces
{
    public interface IToOptWorker : IListenerWorker<XElement>
    {
        /// <summary>
        /// Send heartbeat message to all unknown To Opt sockets.
        /// </summary>
        /// <param name="opt">IOpt to send notification to, optional</param>
        /// <param name="currentMessage">Current message being processed, if any</param>
        void SendToOptHeartbeat(IOpt opt = null, IMessageTracking currentMessage = null);

        /// <summary>
        /// Send Notification ToOpt
        /// </summary>
        /// <param name="opt">IOpt to send notification to.</param>
        /// <param name="currentMessage">Current message being processed, if any</param>
        void SendCurrentNotificationToOpt(IOpt opt, IMessageTracking currentMessage = null);
    }
}
