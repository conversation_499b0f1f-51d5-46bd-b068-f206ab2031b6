using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Worker for handling messages on the HydraPoS connection.
    /// </summary>
    public class HydraPosWorker : PosListenerWorker<string, IHydraDb, ITelemetryWorker>, IHydraPosWorker
    {
        public const string HeaderCommand = "Command";

        public const string GbpSymbol = "\xa3";
        private const string OptDataString = "OPTDATA";
        private const int OptDataLength = 7;
        private const int NumberOfReceiptLines = 7;
        private const int ReceiptLineLength = 120;

        private bool AutoAuth => CurrentEndPoints?.AutoAuth ?? true;

        private readonly IPumpCollection _allPumps;
        private readonly IPrinterHelper<IMessageTracking> _printerHelper;
        private readonly Queue<IMessageTracking<byte[]>> _queue = new Queue<IMessageTracking<byte[]>>();

        /// <summary>
        /// Creates a new instance of the <see cref="HydraPosWorker"/> class.
        /// </summary>
        /// <param name="bosInJournalWorker">Worker for Bos journal entries.</param>
        /// <param name="telemetryWorker">Worker for telemetry logging.</param>
        /// <param name="connectionThread">PoS connection thread.</param>
        /// <param name="hydraDb">Abstracted database.</param>
        /// <param name="allPumps">Collection of all known pumps.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        /// <param name="printerHelper">Printer helper instance</param>
        /// <param name="posOutWorker">PosOut worker</param>
        /// <param name="posInMode">PosInMode worker</param>
        public HydraPosWorker(IBosIntegratorInJournal<IMessageTracking> bosInJournalWorker, ITelemetryWorker telemetryWorker, IListenerConnectionThread<string> connectionThread,
            IHydraDb hydraDb, IPumpCollection allPumps, IHtecLogger logger, IConfigurationManager configurationManager, IPrinterHelper<IMessageTracking> printerHelper,
            IPosIntegratorOut<IMessageTracking> posOutWorker, IPosIntegratorInMode<IMessageTracking> posInMode) : base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager)
        {
            _allPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));
            _printerHelper = printerHelper ?? throw new ArgumentNullException(nameof(printerHelper));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            RegisterWorker(bosInJournalWorker ?? throw new ArgumentNullException(nameof(bosInJournalWorker)));
            RegisterWorker(posOutWorker ?? throw new ArgumentNullException(nameof(posOutWorker)));
            RegisterWorker(posInMode ?? throw new ArgumentNullException(nameof(posInMode)));
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();

            var endPoint = endPoints.HydraPosBindEndPoint;

            return new GenericEndPoint(endPoint.Address, endPoint.Port);
        }

        /// <inheritdoc />
        protected override Result DoStart(params object[] startParams)
        {
            ConnectionThread.MessageLengthFlow = HydraDb.AdvancedConfig.PosType == PosType.Retalix ? MessageLengthFlowType.Outbound : MessageLengthFlowType.Both;
            return base.DoStart(startParams);
        }

        /// <inheritdoc />
        protected override Result DoStatusResponse(byte pump, IMessageTracking message)
        {
            var result = DoSendUpdate(pump, message);
            return result;
        }

        /// <inheritdoc />
        protected override Result DoStatusResponse(StatusResponse status, IMessageTracking message)
        {
            // TODO: For now until the ConnectionThread is FormattedStringConnectionThread...
            var result = DoSendUpdate(status.Pump, message);
            return result;
        }

        private Result DoSendUpdate(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();

            var bytes = _allPumps.TryGetPump(pump, out var thePump)
                ? ConstructUpdateMessage(thePump, AutoAuth)
                : ConstructBlankUpdateMessage(pump);

            return DoSendOrQueue(pump, bytes, message);

        }
        private Result DoSendOrQueue(byte pump, byte[] bytes, IMessageTracking message)
        {
            if (IsConnected())
            {
                return ConnectionThread.Send(bytes, message.FullId, socket: null); // Full Broadcast
            }

            var messageTracking = new MessageTracking<byte[]>(bytes) { ParentIdAsString = message.ParentIdAsString, IdAsString = message.IdAsString };

            DoDeferredLogging(LogLevel.Warn, $"Queued.{Pump.HeaderPump}", () => new[] { $"{pump}" }, reference: messageTracking.FullId);

            _queue.Enqueue(messageTracking);
            return Result.Success();
        }

        /// <inheritdoc />
        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            while (_queue.Any())
            {
                var queuedMessage = _queue.Dequeue();
                DoDeferredLogging(LogLevel.Warn, $"Sending.Queued.Message.Id", () => new[] { $"{queuedMessage.IdAsString}" });
                ConnectionThread.Send(queuedMessage.Request, queuedMessage.IdAsString, socket: null); // Full Broadcast
            }

            GetWorker<IPosIntegratorInMode<IMessageTracking>>()?.RequestDefaultMode(LoggingReference);

            base.DoOnConnected(ipAddress, port);
        }

        /// <summary>Called when text message is received from Hydra POS socket.</summary>
        /// <param name="message">Message received.</param>
        /// <param name="id">ID of POS.</param>
        /// <returns>Response if there is one, otherwise null.</returns>
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message, int id)
        {
            static bool Is(string thisMessage, string tag, out byte pump)
            {
                pump = (byte)0;
                return thisMessage.StartsWith(tag) && thisMessage.EndsWith(":") && byte.TryParse(thisMessage.Substring(tag.Length, thisMessage.Length - $"{tag}:".Length), out pump);
            }

            var request = message.Request;
            var messages = GetHydraMessages(request);

            if (messages.Any())
            {
                var replyString = new StringBuilder();
                try
                {
                    foreach (var thisMessage in messages)
                    {
                        if (Is(thisMessage, "Update", out var pump))
                        {
                            DoDeferredLogging(LogLevel.Info, HeaderCommand, () => new[] {$"Update; Pump: {pump}"});
                            NotificationWorker?.SendInformation($"Hydra POS Update command received, pump is {pump}");
                            GetWorker<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>().RequestStatus(pump, message);                            
                        }
                        else if (thisMessage.Equals("DayEnd"))
                        {
                            DoDeferredLogging(LogLevel.Info, HeaderCommand, () => new[] {"DayEnd"});
                            NotificationWorker?.SendInformation("Hydra POS Day End command received");
                            GetWorker<IBosIntegratorInJournal<IMessageTracking>>()?.RequestDayEnd(message);
                        }
                        else if (Is(thisMessage, "Kiosk", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.KioskOnly, message);
                        }
                        else if (Is(thisMessage, "OPTuse", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.Mixed, message);
                        }
                        else if (Is(thisMessage, "Close", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.Close, message);                         
                        }
                        else if (Is(thisMessage, "Open", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.Open, message);
                        }
                        else if (Is(thisMessage, "OutSide", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.OutsideOnly, message);
                        }
                        else if (Is(thisMessage, "MixMode", out pump))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestModeChange(pump, ModeChangeType.Mixed, message);
                        }
                        else if (thisMessage.Equals("NightMode:"))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestTimeModeChange(TimeModeChangeType.NightMode, message);
                        }
                        else if (thisMessage.Equals("DayMode:"))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>()?.RequestTimeModeChange(TimeModeChangeType.DayMode, message);
                        }
                        else if (thisMessage.Equals("EveningMode:"))
                        {
                            GetWorker<IPosIntegratorInMode<IMessageTracking>>()?.RequestTimeModeChange(TimeModeChangeType.EveningMode, message);
                        }
                        else if (thisMessage.StartsWith("AddPAN") && thisMessage.EndsWith(":"))
                        {
                            var pan = thisMessage.Substring("AddPAN".Length, thisMessage.Length - "AddPAN:".Length);
                            DoDeferredLogging(LogLevel.Info, HeaderCommand, () => new[] {$"AddPAN; PAN: {pan}"});
                            NotificationWorker?.SendInformation($"Hydra POS Add PAN command received, PAN is {pan}");
                        }
                        else if (Is(thisMessage, "Receipts", out pump))
                        {
                            DoDeferredLogging(LogLevel.Info, HeaderCommand, () => new[] {$"Receipts; Pump: {pump}"});
                            NotificationWorker?.SendInformation($"Hydra POS Receipts command received, pump is {pump}");
                            GetWorker<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>().RequestReceipt(new GetReceiptRequest() { Pump = pump }, message);
                        }
                        else
                        {
                            DoDeferredLogging(LogLevel.Warn, HeaderCommand, () => new[] {$"UnknownMessage ({thisMessage})"});
                        }
                    }

                    return Result.Success(string.Empty);
                }
                catch (Exception ex) // Yes, bad root exception, but we have no clue what might explode
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {ex.Message}, ex);
                }
            }

            return Result.Failure<string>("Unknown HydraPosMessageType");
        }

        private byte[] ConstructUpdateMessage(IPump pump, bool autoAuth)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderParameters, () => new[]
            {
                $"(Pump: {pump.Number}, " +
                $"Any OPT: {pump.Opt != null}, " +
                $"OPT: {(pump.Opt == null ? ConfigConstants.None : $"{pump.Opt.IdString} ({pump.Opt.Id})")}, " +
                $"No Kiosk=Outside only: {pump.OutsideOnly}, " +
                $"AutoAuth: {autoAuth}, " +
                $"State: {pump.State}, " +
                $"Offline: {pump.Opt?.Offline ?? true}, " +
                $"In Use: {pump.InUse}, " +
                $"Kiosk mode: {pump.KioskUse})"
            });

            var messageBytes = new List<byte>();
            messageBytes.AddRange(ConstructDelphiString(OptDataString, OptDataLength));
            messageBytes.Add(pump.Number);
            messageBytes.AddRange(BitConverter.GetBytes(pump.Opt != null));
            messageBytes.AddRange(BitConverter.GetBytes(pump.OutsideOnly));
            messageBytes.AddRange(BitConverter.GetBytes(autoAuth));
            // padding for two byte boundary
            while (messageBytes.Count % sizeof(ushort) > 0)
            {
                messageBytes.Add(0);
            }

            messageBytes.AddRange(BitConverter.GetBytes((ushort) pump.State));
            messageBytes.AddRange(BitConverter.GetBytes(pump.Opt?.Offline ?? true));
            messageBytes.AddRange(BitConverter.GetBytes(pump.InUse));
            messageBytes.AddRange(BitConverter.GetBytes(pump.KioskUse));
            // padding for four byte boundary
            while (messageBytes.Count % sizeof(uint) > 0)
            {
                messageBytes.Add(0);
            }

            messageBytes.AddRange(BitConverter.GetBytes(pump.Opt?.CashLimit ?? 0));
            return messageBytes.ToArray();
        }

        private byte[] ConstructBlankUpdateMessage(byte pump)
        {
            var messageBytes = new List<byte>();
            messageBytes.AddRange(ConstructDelphiString(OptDataString, OptDataLength));
            messageBytes.Add(pump);
            messageBytes.AddRange(BitConverter.GetBytes(false));
            messageBytes.AddRange(BitConverter.GetBytes(false));
            messageBytes.AddRange(BitConverter.GetBytes(false));
            // padding for two byte boundary
            while (messageBytes.Count % sizeof(ushort) > 0)
            {
                messageBytes.Add(0);
            }

            messageBytes.AddRange(BitConverter.GetBytes((ushort) HydraPosPumpState.Closed));
            messageBytes.AddRange(BitConverter.GetBytes(true));
            messageBytes.AddRange(BitConverter.GetBytes(false));
            messageBytes.AddRange(BitConverter.GetBytes(true));
            // padding for four byte boundary
            while (messageBytes.Count % sizeof(uint) > 0)
            {
                messageBytes.Add(0);
            }

            messageBytes.AddRange(BitConverter.GetBytes((uint) 0));

            return messageBytes.ToArray();
        }

        // TODO: Is this used anywhere else?!
        protected IList<ReceiptInfo> GetReceiptsForOpt(string opt) => HydraDb.GetReceiptsForOpt(opt);

        private byte[] ConstructReceiptsMessage(byte pump, string opt)
        {
            var messageBytes = new List<byte>();

            DoAction(() =>
            {
                var receipts = HydraDb.GetReceiptsForOpt(opt);

                messageBytes.AddRange(ConstructDelphiString(
                    $"RECEIPTS PUMP={pump} LINES={receipts.Count * NumberOfReceiptLines}", ReceiptLineLength));

                foreach (var receipt in receipts)
                {
                    var myReceipt = _printerHelper.FormatReceipt(receipt.ReceiptContent, true, false);
                    messageBytes.AddRange(ConstructReceiptBlock(myReceipt, receipt));
                }
            }, LoggingReference);
            return messageBytes.ToArray();
        }

        internal byte[] ConstructReceiptBlock(string receipt, ReceiptInfo receiptInfo)
        {
            // split on new lines and add linefeed (0x0a)
            var receiptLines = receipt.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
            receiptLines = receiptLines.Select(s => string.Concat(s, '\n')).ToArray();

            var receiptStrings = new List<string>
            {
                GetOptOcxReceiptLine1(receiptInfo)
            };

            var currentLine = string.Empty;
            
            // compose receipt lines
            foreach (var line in receiptLines)
            {
                if (currentLine.Length + line.Length <= ReceiptLineLength)
                {
                    currentLine += line;
                }
                else
                {
                    receiptStrings.Add(currentLine);
                    currentLine = line;
                }
            }

            // add last line
            receiptStrings.Add(currentLine);

            // add padding
            if (receiptStrings.Count > 7)
            {
                DoDeferredLogging(LogLevel.Warn, "Receipt clipped because of insufficient space in OPT OCX receipt block!");
                receiptStrings = receiptStrings.Take(7).ToList();
            }

            var paddingLineCount = NumberOfReceiptLines - receiptStrings.Count;
            receiptStrings.AddRange(Enumerable.Repeat(string.Empty, paddingLineCount));

            // convert into delphi strings
            var messageBytes = new List<byte>();
            receiptStrings.ForEach(s => messageBytes.AddRange(ConstructDelphiString(s, ReceiptLineLength)));

            return messageBytes.ToArray();
        }

        internal static string GetOptOcxReceiptLine1(ReceiptInfo receiptInfo)
        {
            return $"{GbpSymbol}{(decimal)receiptInfo.Amount / 100:F} {receiptInfo.TransactionTime:HH:mm dd/MM/yy}";
        }

        /// <summary>Construct a Delphi string.</summary>
        /// <param name="message">String to be converted.</param>
        /// <param name="size">Size of string to be converted.</param>
        /// <returns>Constructed string in a byte array.</returns>
        internal static byte[] ConstructDelphiString(string message, int size)
        {
            // ReSharper disable once UseObjectOrCollectionInitializer
            var messageBytes = new List<byte>();
            messageBytes.Add((byte) message.Length);
            messageBytes.AddRange(message.ToCharArray().Select(c => Convert.ToByte(c)).ToArray());

            // Pad bytes to size
            for (var i = 0; i < size - message.Length; i++)
            {
                messageBytes.Add(0);
            }

            return messageBytes.ToArray();
        }

        // TODO: For now until the ConnectionThread is FormattedStringConnectionThread...
        private IList<string> GetHydraMessages(string message)
        {
            var keys = new List<string>
            {
                "Update",
                "DayEnd",
                "Kiosk",
                "OPTuse",
                "Close",
                "Open",
                "OutSide",
                "MixMode",
                "NightMode",
                "DayMode",
                "EveningMode",
                "AddPAN",
                "Receipts"
            };
            var messages = new List<string>();
            DoAction(() =>
            {
                while (message.Length > 0)
                {
                    var start = message.Length;

                    foreach (var key in keys)
                    {
                        var keyStart = message.IndexOf(key);
                        if (keyStart >= 0 && keyStart < start)
                        {
                            start = keyStart;
                        }
                    }

                    if (start > 0)
                    {
                        DoDeferredLogging(LogLevel.Warn, "Discarding characters from message", () => new[] {message});
                        message = message.Substring(start);
                    }

                    if (message.StartsWith("DayEnd"))
                    {
                        start = "DayEnd".Length;
                        messages.Add(message.Substring(0, start));
                    }
                    else if (message.Contains(":"))
                    {
                        start = message.IndexOf(":") + 1;
                        messages.Add(message.Substring(0, start));
                    }
                    else
                    {
                        start = message.Length;
                        foreach (var key in keys)
                        {
                            var keyStart = message.IndexOf(key);
                            if (keyStart > 0 && keyStart < start)
                            {
                                start = keyStart;
                            }
                        }

                        DoDeferredLogging(LogLevel.Warn, "Discarding characters from message", () => new[] {message});
                    }

                    message = message.Substring(start);
                }
            }, LoggingReference);
            return messages;
        }

        /// <inheritdoc/>
        protected override Result DoModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking messageTracking, bool suppressModeChange = false)
        {
            return StatusResponse(pump, messageTracking);
        }

        /// <inheritdoc/>
        protected override Result DoReceiptResponse(byte pump, IMessageTracking message)
        {
            var bytes = _allPumps.TryGetPump(pump, out var thePump) && thePump.Opt?.IdString != null
                ? ConstructReceiptsMessage(pump, thePump.Opt.IdString)
                : new byte[0];

            return DoSendOrQueue(pump, bytes, message);
        }
    }
}