using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Htec.Foundation.Configuration;
using System.Collections.Generic;

namespace OPT.Common.Repositories.Interfaces
{
    /// <summary>
    /// Interface for handling configuration data in the database.
    /// </summary>
    public interface IConfigurationRepository
    {
        /// <summary>
        /// Sets the state of contactless card handling.
        /// </summary>
        /// <param name="isEnabled">Is contatless enabled on this site?</param>
        void SetContactless(bool isEnabled);

        /// <summary>
        /// Sets the pre-auth limit for contactless via payment cards.
        /// </summary>
        /// <param name="limit">Pre-auth limit in pence.</param>
        void SetContactlessCardPreAuth(int limit);

        /// <summary>
        /// Sets the pre-auth limit for contactless via payment devices e.g. phone.
        /// </summary>
        /// <param name="limit">Pre-auth limit in pence.</param>
        void SetContactlessDevicePreAuth(int limit);
        
        /// <summary>
        /// Sets the TTQ value needed for some contactless card types.
        /// </summary>
        /// <param name="ttq">TTQ value.</param>
        void SetContactlessTtq(string ttq);

        /// <summary>
        /// Show a single button for contactless on the OPT.
        /// </summary>
        /// <param name="showSingleButton">Show single button if enabled.</param>
        void SetContactlessSingleButton(bool showSingleButton);

        /// <summary>
        /// Sets the list of categories
        /// </summary>
        /// <param name="categories">list of categories</param>
        void SetCategories(IEnumerable<CategoryConfiguration> categories);

        /// <summary>
        /// Refreshes static data used for lookups.
        /// </summary>
        void RefreshStaticData();
      
        /// <summary>
        /// Set Integration value
        /// </summary>
        /// ///<param name="integrationType">The <see cref="IntegrationType"/> value</param>
        /// <param name="value">The integration type value</param>
        /// <param name="loggingReference">Logging reference</param>
        Result SetIntegrationType(IntegrationType integrationType, string value, string loggingReference = null);

        /// <summary>
        /// Gets the current site level configuration.
        /// </summary>
        /// <returns>Site level configuration information.</returns>
        Result<SiteInfo> GetSiteInfo();

        /// <summary>
        /// Retrieves all category information from database
        /// </summary>
        /// <returns>List of categories</returns>
        IList<CategoryConfiguration> GetCategoriesConfiguration();

        /// <summary>
        /// Sets local account enabled flag
        /// </summary>
        /// <param name="enabled">Flag value.</param>
        Result SetLocalAccountsEnabled(bool enabled);
    }
}