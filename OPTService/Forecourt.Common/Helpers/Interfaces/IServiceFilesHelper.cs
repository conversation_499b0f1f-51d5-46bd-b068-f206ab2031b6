using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.System.Diagnostics.Interfaces;
using Htec.Foundation.Models;
using System.Collections.Generic;

namespace Forecourt.Common.Helpers.Interfaces
{
    /// <summary>
    /// Anything and everything related to the Service files or file information
    /// </summary>
    public interface IServiceFilesHelper
    {
        /// <summary>
        /// All currently installed file version information, for the Forecourt and OPT Services, and OPT.Common.dll
        /// </summary>
        public IEnumerable<FileVersionInfo> Current { get; }

        /// <summary>
        /// All previously installed file version information, for the Forecourt and OPT Services, and OPT.Common.dll
        /// </summary>
        public IEnumerable<FileVersionInfo> Rollback { get; }

        /// <summary>
        /// All pending upgrade file version information, for the Forecourt and OPT Services, and OPT.Common.dll
        /// </summary>
        public IEnumerable<FileVersionInfo> Upgrade { get; }

        /// <summary>
        /// All currently installed file version information, for related Pump Integrator software
        /// </summary>
        public IEnumerable<FileVersionInfo> PumpIntegrator { get; }

        /// <summary>
        /// All currently installed file version information, for the Offline Transaction File Service
        /// </summary>
        public IEnumerable<FileVersionInfo> OfflineFileService { get; }

        /// <summary>
        /// Any available files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> UploadedFileNames { get; }

        /// <summary>
        /// Any available whitelist files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> WhitelistFiles { get; }

        /// <summary>
        /// Any available layout files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> LayoutFiles { get; }

        /// <summary>
        /// Any available upgrade files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> UpgradeFiles { get; }

        /// <summary>
        /// Any available software files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> SoftwareFiles { get; }

        /// <summary>
        /// Any available media files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> MediaFiles { get; }

        /// <summary>
        /// Any available playlist files, to be uploaded to OPT
        /// </summary>
        IEnumerable<string> PlaylistFiles { get; }

        /// <summary>
        /// Any previously backed up database files
        /// </summary>
        IEnumerable<string> DatabaseBackupFiles { get; }

        /// <summary>
        /// Any available OPT log files, downloaded from OPT
        /// </summary>
        IEnumerable<string> OptLogFiles { get; }

        /// <summary>
        /// Extracts the full version information from the specified assembly.
        /// </summary>
        /// <param name="targetFile">Assembly file name.</param>
        /// <param name="targetFolder">Folder containing the assembly, null defaults to current assembly folder</param>
        /// <param name="targetName">Friendly name for the assembly.</param>
        /// <returns>Result wrapped <see cref="IFileVersionInfoWrapper"/></returns>
        Result<IFileVersionInfoWrapper> ExtractAssemblyVersionInfoWrapper(string targetFile, string targetFolder = null, string targetName = "current");
    }
}
