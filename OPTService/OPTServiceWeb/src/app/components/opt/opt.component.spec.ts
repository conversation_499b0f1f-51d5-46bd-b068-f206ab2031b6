import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { Opt } from 'src/app/core/models/opt.model';
import { OptService } from 'src/app/services/opt.service';
import { AppComponent } from 'src/app/app.component';
import { OptComponent } from './opt.component';

describe('OptComponent', () => {
  let component: OptComponent;
  let fixture: ComponentFixture<OptComponent>;
  let serviceSpy: jasmine.SpyObj<OptService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
 
  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('serviceSpy', ['setReceiptHeader','sendSoftwareVersion','sendSecureAssets', 'sendCpatAssets', 'setPlaylistFileName', 'setContactless']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);   
    const appComponentObjSpy =  jasmine.createSpyObj('AppComponent', ['integrators']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        HttpClientModule],
      providers: [
        OptComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: OptService, useValue: serviceObjSpy },
        { provide: AppComponent, useValue: appComponentObjSpy}
      ]
    });

    serviceSpy = TestBed.inject(OptService) as jasmine.SpyObj<OptService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    component = TestBed.inject(OptComponent);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    //Arrange
    spyOn(component.optForm, 'patchValue').and.stub();

    //Act
    component.ngOnInit();

    //Assert
    expect(component.optForm.patchValue).toHaveBeenCalledTimes(1);
  });

  it('.setPlaylistFileName() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPlaylistFileName.and.returnValue(of({}));
    
    //Act
    component.setPlaylistFileName();

    //Assert
    expect(serviceSpy.setPlaylistFileName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPlaylistFileName() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPlaylistFileName.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setPlaylistFileName();

    //Assert
    expect(serviceSpy.setPlaylistFileName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

});
