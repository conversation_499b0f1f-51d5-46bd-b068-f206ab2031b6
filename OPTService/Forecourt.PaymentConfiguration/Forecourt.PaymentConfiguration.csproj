<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net462;net472;net48</TargetFrameworks>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
	  <LangVersion>9.0</LangVersion>
	  <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Htec.DapperWrapper" Version="[2.1.0,)" />
		<PackageReference Include="Htec.Foundation" Version="[4.3.0,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[5.0.0,)" />
		<PackageReference Include="Htec.Hydra.Core.PaymentConfiguration" Version="[1.0.0,)" />
		<PackageReference Include="Htec.Hydra.Opt.Common" Version="[2.4.0,)" />
	</ItemGroup>
	
	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<Reference Include="hsqldb">
			<HintPath>..\Java\hsqldb.dll</HintPath>
		</Reference>
		<Reference Include="IKVM.OpenJDK.Core, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
			<HintPath>..\Java\IKVM.OpenJDK.Core.dll</HintPath>
		</Reference>
		<Reference Include="IKVM.OpenJDK.Jdbc, Version=8.1.5717.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
			<HintPath>..\Java\IKVM.OpenJDK.Jdbc.dll</HintPath>
		</Reference>
		<Reference Include="jtds">
			<HintPath>..\Java\jtds.dll</HintPath>
		</Reference>
	</ItemGroup>
	
	<ItemGroup>
		<InternalsVisibleTo Include="Forecourt.Common.Tests" />
		<InternalsVisibleTo Include="Forecourt.Common.Integration.Tests" />
	</ItemGroup>
	
</Project>
