using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Core;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using JetBrains.Annotations;
using Microsoft.AspNet.SignalR;
using Newtonsoft.Json;
using OPT.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;

namespace Htec.Foundation.Web.Hubs
{
    /// <summary>
    /// Workerable class that also has access to a SignalR HubContext
    /// </summary>
    /// <typeparam name="TMessage">Base type of messages passing through a SignalR Hub</typeparam>
    /// <typeparam name="TMessageTracking">Current message tracking instance</typeparam>
    [HasConfiguration]
    public abstract class Hubbable<TMessage, TMessageTracking> : Connectable,
        IHubbable<TMessage, TMessageTracking>,
        IHubbableIn<TMessage, TMessageTracking>
        where TMessage : class
        where TMessageTracking : ILogTracking
    {

        public class ConnectionInfo : Entity<int>
        {
            public ConnectionInfo(int id, string ipAddress) : base(id)
            {
                IpAddress = ipAddress;
            }

            public string IpAddress { get; set; }
        }

        /// <summary>
        /// Current state of the message
        /// </summary>
        public class MessageState: Entity<string>
        {
            public MessageState(TMessageTracking messageTracking, TMessage message, Action<dynamic, TMessage> action)
            {
                base.Id = messageTracking.IdAsString;
                MessageTracking = messageTracking;
                Message = message;
                Action = action;
                SentTime = DateTime.UtcNow;    
            }

            /// <summary>
            /// Identifier/message id, known to SignalR/Htec.HubClient
            /// </summary>
            public string Identifier => MessageTracking.IdAsString;

            /// <summary>
            /// Message tracking instance
            /// </summary>
            public TMessageTracking MessageTracking { get; }

            /// <summary>
            /// Message sent
            /// </summary>
            public TMessage Message { get; }

            /// <summary>
            /// Gets or sets the action method.
            /// </summary>
            public Action<dynamic, TMessage> Action { get; }

            /// <summary>
            /// Message sent time
            /// </summary>
            public DateTime SentTime { get; }
        }

        /// <inheritdoc />
        public const string HeaderSignalR = ConfigConstants.HeaderSignalR;

        /// <summary>
        /// SignalR Hub context
        /// </summary>
        protected IHubContext HubContext { get; private set; }

        /// <summary>
        /// A comma separated list of contexts to ignore (ack and logging), default of null/empty to include all, * to exclude all
        /// </summary>
        protected string IgnoreAckContexts { get; private set; }

        /// <summary>
        /// Configuration key for whether the Rx/Tx details is logged.  Suffix with the connection for uniqueness.
        /// </summary>
        public const string ConfigKeyPrefixLogRxTx = Foundation.Connections.Common.Constants.ConfigKeyCategoryConnectivity + nameof(Hubbable<TMessage, TMessageTracking>) + ":Log:RxTx:";

        /// <summary>
        /// Default value, for whether the Rx/Tx details is logged.
        /// </summary>s
        public const bool DefaultValueLogRxTx = true;

        /// <inheritdoc cref="IConnectionThread"/>
        public ConfigurableBool LogRxTx { get; private set; }

        /// <inheritdoc />
        protected Hubbable(IHtecLogManager logManager, string loggerName, IHubContext hubContext, IConfigurationManager configurationManager = null, string ignoreAckContexts = null) : base(logManager, loggerName, configurationManager)
        {
            HubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));

            IgnoreAckContexts = ignoreAckContexts;

            // Move off the OPT Id range
            NextClientId = 1000;

            LogRxTx = new ConfigurableBool(this, ConfigKeyPrefixLogRxTx, DefaultValueLogRxTx);
        }

        /// <summary>
        /// Dictionary to track state of messages flowing through the SignalR Hub
        /// </summary>        
        protected ConcurrentDictionary<string, MessageState> Messages { get; private set; } = new ConcurrentDictionary<string, MessageState>();

        /// <summary>
        /// Dictionary to track current Connections
        /// </summary>
        protected internal ConcurrentDictionary<string, ConnectionInfo> Connections { get; set; } = new ConcurrentDictionary<string, ConnectionInfo>();

        private bool ShouldIgnoreContext(string context) => !IgnoreAckContexts.IsNullOrWhiteSpace() || IgnoreAckContexts == "*" ||
            $"{IgnoreAckContexts}|AcknowledgeSignalrMethod|".Contains(context, StringComparison.InvariantCultureIgnoreCase);

        /// <inheritdoc/>
        public Result Send(string connectionId, string context, TMessage message, Action<dynamic, TMessage> action, [NotNull] TMessageTracking tracking, [CallerMemberName] string methodName = null)
        {
            var client = HubContext.Clients.Client(connectionId);
            if (client == null)
            {
                return Result.Failure($"Unknown ConnectionId: {connectionId}");
            }

            return DoSend(client, context, message, action, tracking, methodName);
        }

        private Result DoSend(dynamic connections, string context, TMessage message, Action<dynamic, TMessage> action, [NotNull] TMessageTracking tracking, string methodName)
        {
            return DoAction(() =>
            {
                tracking.Context = context;

                if (Messages.Count > 0)
                {
                    var acks = Messages.Values.Select(x => $"[{JsonConvert.SerializeObject(new { x.MessageTracking.Context, x.SentTime, x.Message })}]");
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderSignalR, () => new[] { $"AckList: {string.Join(", ", acks)}" });
                }

                bool ignore = ShouldIgnoreContext(context);

                if (!(ignore || Messages.ContainsKey(tracking.IdAsString) || Messages.TryAdd(tracking.IdAsString, new(tracking, message, action))))
                {
                    DoDeferredLogging(LogLevel.Warn, HeaderSignalR, () => new[] { $"Context: {tracking.Context}; Unable to manage ACK" }, methodName: methodName);
                }

                action.Invoke(connections, message);

                if (LogRxTx.GetValue())
                {
                    DoDeferredLogging(LogLevel.Info, $"{HeaderSignalR}.{ByteExtensions.HeaderTxValue}", () => new[] { $"Context: {tracking.Context}; Message: [{JsonConvert.SerializeObject(message)}]" }, methodName: methodName);
                }

                return Result.Success();

            }, tracking.FullId, methodName: methodName);
        }

        /// <inheritdoc/>
        public Result Publish(string context, TMessage message, Action<dynamic, TMessage> action, [NotNull] TMessageTracking tracking, [CallerMemberName] string methodName = null)
        {
            return DoSend(HubContext.Clients.All, context, message, action, tracking, methodName);
        }

        /// <inheritdoc/>
        public Result ProcessAckResponse(string messageId, [CallerMemberName] string methodName = null)
        {
            return DoAction(() =>
            {
                if (!(Messages.TryGetValue(messageId, out var _) && Messages.TryRemove(messageId, out var info)))
                {
                    return Result.Success();
                    //return Result.Failure($"Message not found: {messageId}");
                }

                if (LogRxTx.GetValue())
                {
                    DoDeferredLogging(LogLevel.Info, $"{HeaderSignalR}.{ByteExtensions.HeaderRxValue}", () => new[] { $"Context: {info.MessageTracking.Context}" }, methodName: methodName);
                }

                return Result.Success();
            }, messageId, methodName: methodName);
        }

        /// <inheritdoc/>
        public Result ProcessAckResponse<TTarget>(string messageId, [NotNull]Func<TMessageTracking, TTarget, Result> action, [CallerMemberName] string methodName = null) where TTarget : TMessage
        {
            return DoAction(() =>
            {
                if (!Messages.TryGetValue(messageId, out var info))
                {
                    return Result.Success();
                    //return Result.Failure($"Message not found: {messageId}");
                }

                if (LogRxTx.GetValue())
                {
                    DoDeferredLogging(LogLevel.Info, $"{HeaderSignalR}.{ByteExtensions.HeaderRxValue}", () => new[] { $"Context: {info.MessageTracking.Context};" }, methodName: methodName);
                }

                var result = action?.Invoke(info.MessageTracking, (TTarget)info.Message) ?? Result.Success();

                if (result.IsSuccess)
                {
                    if (!Messages.TryRemove(messageId, out var _))
                    {
                        return Result.Failure($"Unable to remove ACK");
                    }
                }

                return result;
            }, messageId, methodName: methodName);
        }

        /// <inheritdoc/>
        public Result ProcessRequest<TTarget>(string connectionId, string messageId, TTarget request, [NotNull]Func<TMessageTracking, TTarget, IHtecLogger, INotificationWorker<string>, Result> action, [CallerMemberName] string methodName = null) where TTarget : TMessage
        {
            return DoAction(() =>
            {
                if (!ShouldIgnoreContext(methodName))
                {
                    HubContext.Clients.Client(connectionId).AcknowledgeClientMessage(messageId);
                }

                if (LogRxTx.GetValue())
                {
                    DoDeferredLogging(LogLevel.Info, $"{HeaderSignalR}.{ByteExtensions.HeaderRxValue}", () => new[] { $"Request: {JsonConvert.SerializeObject(request)};" }, methodName: methodName);
                }

                var result = action?.Invoke((TMessageTracking)LoggingReference.ToMessageTracking(), request, GetLogger(), GetWorker<INotificationWorker<string>>()) ?? Result.Success();
               
                return result;
            }, messageId, methodName: methodName);
        }

        protected override int DoGetConnectedCount()
        {
            return Connections.Count;
        }

        protected override bool DoIsConnected()
        {
            return Connections.Count > 0;
        }

        /// <inheritdoc cref="IConnectable.GetAllIpAddresses"/>
        protected override IEnumerable<IPAddress> DoGetAllIpAddresses()
        {
            return Connections.Values.Select(x => IPAddress.TryParse(x.IpAddress, out var ip) ? ip : IPAddress.Loopback);
        }

        protected internal Result AddConnection(string id, string ipAddress)
        {
            return DoAction(() =>
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderSignalR, () => new[] { $"Id: {id}; IpAddess: {ipAddress}"});

                Connections[id] = new ConnectionInfo(GetNextClientId().Value, ipAddress);

                if (IPAddress.TryParse(ipAddress, out var ip))
                {
                    OnConnected(ip);
                }

                return Result.Success();
            }, null);
        }

        internal Result RemoveConnection(string id)
        {
            return DoAction(() =>
            {
                if (!Connections.TryRemove(id, out var connection))
                {
                    return Result.Failure($"Could not remove connection: {id}");
                }

                OnDisconnected(connection.Id);

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderSignalR, () => new[] { $"Id: {id}" });

                return Result.Success();
            }, null);
        }
    }
}
