import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { FileLocationService } from 'src/app/services/file-location.service';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { LoyaltyService } from 'src/app/services/loyalty.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { FileLocationsComponent } from './file-locations.component';

describe('FileLocationsComponent', () => {
  let component: FileLocationsComponent;  
  let fileLocationServiceSpy: jasmine.SpyObj<FileLocationService>;
  let loyaltyServiceSpy: jasmine.SpyObj<LoyaltyService>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(() => {
    const fileLocationSpy = jasmine.createSpyObj('FileLocationService', ['getFileLocations', 'setProperty']);
    const loyaltySpy = jasmine.createSpyObj('LoyaltyService', ['addLoyalty','deleteLoyalty']);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getFileLocationSignalRMessage']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        FileLocationsComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: FileLocationService, useValue: fileLocationSpy },
        { provide: LoyaltyService, useValue: loyaltySpy },
        { provide: SignalRService, useValue: signalRSpy },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    fileLocationServiceSpy = TestBed.inject(FileLocationService) as jasmine.SpyObj<FileLocationService>;
    loyaltyServiceSpy = TestBed.inject(LoyaltyService) as jasmine.SpyObj<LoyaltyService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;

    signalRServiceSpy.getFileLocationSignalRMessage.and.returnValue(of());

    component = TestBed.inject(FileLocationsComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(component).toBeTruthy();
  });

  it('.refreshData() should handle success response from service', ()=>{
    //Arrange
    let fakeFileLocations = {
      ContactlessPropertiesFile: "C:\HydraOPTService\PropertiesFile\\",
      DatabaseBackupDirectory: "C:\HydraOPTService\Database Backups\\",
      EsocketConfigFile: "C:\postilion\eSocket.POS\properties.txt",
      EsocketConnectionMade: "true",
      EsocketConnectionString: "Data Source=***************\sqlesp ;Initial Catalog=esocketpos;Integrated Security=False; user id =sa; password=****;",
      EsocketDbUrl: "********************************",
      EsocketKeystoreFile: "C:\postilion\eSocket.POS\keystore\esp.ks",
      EsocketOverrideContactless: "false",
      EsocketOverrideKeystore: "false",
      EsocketOverrideProperties: "false",
      EsocketOverrideUrl: "false",
      EsocketUseConnectionString: "false",
      FuelDataUpdateFile: "C:\HydraOPTService\ToSend\FuelData.upd",
      JournalFileDirectory: "C:\HydraOPTService\JournalFiles\\",
      LayoutDirectory: "C:\HydraOPTService\Layout\\",
      LogFileDirectory: "C:\HydraOPTService\Logs\\",
      MediaChannel: "true",
      MediaDirectory: "C:\HydraOPTService\Media\\",
      OptLogFileDirectory: "C:\HydraOPTService\OPT Logs\\",
      PlaylistDirectory: "C:\HydraOPTService\Playlist\\",
      ReceivedUpdateDirectory: "C:\HydraOPTService\Received\\",
      RetalixTransactionFileDirectory: "C:\HydraOPTService\RetalixTransactionFiles\\",
      RollbackFileDirectory: "C:\HydraOPTService\Archive",
      SoftwareDirectory: "C:\HydraOPTService\Software\\",
      TraceFileDirectory: "C:\HydraOPTService\Traces\\",
      TransactionFileDirectory: "C:\HydraOPTService\TransactionFiles\\",
      UpgradeFileDirectory: "C:\HydraOPTService\Upgrade",
      WhitelistDirectory: "C:\HydraOPTService\Whitelist\\"
    };
    fileLocationServiceSpy.getFileLocations.and.returnValue(of(fakeFileLocations));

    //Act
    component.refreshData("");

    //Assert
    expect(component.fileLocationsData).toEqual(fakeFileLocations);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', ()=>{
    //Arrange
    fileLocationServiceSpy.getFileLocations.and.returnValue(throwError({status:500}));

    //Act
    component.refreshData("");

    //Assert
    expect(component.fileLocationsData).toBeUndefined();
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.setContactlessPropertiesFile() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setContactlessPropertiesFile();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setContactlessPropertiesFile() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setContactlessPropertiesFile();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setRetalixTransactionFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setRetalixTransactionFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setRetalixTransactionFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setRetalixTransactionFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setTransactionFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setTransactionFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setTransactionFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setTransactionFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setWhitelistDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setWhitelistDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setWhitelistDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setWhitelistDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setLayoutDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setLayoutDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setLayoutDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setLayoutDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setSoftwareDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setSoftwareDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setSoftwareDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setSoftwareDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setFuelDataUpdateFile() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setFuelDataUpdateFile();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setFuelDataUpdateFile() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setFuelDataUpdateFile();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setUpgradeFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setUpgradeFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setUpgradeFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setUpgradeFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setRollbackFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setRollbackFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setRollbackFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setRollbackFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setOptLogFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setOptLogFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setOptLogFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setOptLogFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setLogFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setLogFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setLogFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setLogFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setTraceFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setTraceFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setTraceFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setTraceFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setJournalFileDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setJournalFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setJournalFileDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setJournalFileDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setReceivedUpdateDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setReceivedUpdateDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setReceivedUpdateDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setReceivedUpdateDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setDatabaseBackupDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setDatabaseBackupDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setDatabaseBackupDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setDatabaseBackupDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideContactless() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketOverrideContactless();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideContactless() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketOverrideContactless();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setMediaDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setMediaDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setMediaDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setMediaDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPlaylistDirectory() should handle success response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(of({}));
    
    //Act
    component.setPlaylistDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPlaylistDirectory() should handle unsuccess response from service', () => {
    //Arrange
    fileLocationServiceSpy.setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setPlaylistDirectory();

    //Assert
    expect(fileLocationServiceSpy.setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

});
