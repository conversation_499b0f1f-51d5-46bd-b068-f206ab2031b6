using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Simple dummy implementation of all IPosIntegratorXxx that does nothing
    /// </summary>
    public class NonePosXxxWorker: PosXxxWorker, INonePosXxxWorker
    {
        /// <inheritdoc/>
        public NonePosXxxWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IConfigurationManager configurationManager = null) : base(logManager, nameof(NonePosXxxWorker), hydraDb, allPumps, configurationManager)
        {
            if (AllPumps == null)
            {
                throw new ArgumentNullException(nameof(allPumps));
            }
        }

        /// <inheritdoc/>
        public Result ModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking message = null, bool suppressModeChange = false)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result ReceiptResponse(ReceiptInfo receipt, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result ReceiptResponse(byte pump, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestDefaultMode(string loggingReference = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestReceipt(GetReceiptRequest request, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestStatus(byte pump, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestStatus(IPump pump, string loggingReference = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestTimeModeChange(TimeModeChangeType mode, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result StatusResponse(StatusResponse status, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result StatusResponse(byte pump, IMessageTracking message = null)
        {
            return Result.Success();
        }
    }
}
