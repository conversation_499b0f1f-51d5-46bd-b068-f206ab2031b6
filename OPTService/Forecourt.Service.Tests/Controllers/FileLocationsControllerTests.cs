using FluentAssertions;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using NSubstitute;
using OPT.Common.Helpers;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System.Collections.Generic;
using System.Reflection;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class FileLocationControllerTests
    {
        private readonly IWeb _web;
        private readonly IHtecLogger _logger;
        private readonly ILoggingHelper _loggingHelper;
        
        public FileLocationControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _web = Substitute.For<IWeb>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
        }

        [Fact]
        public void test_GetFileLocations_returns_Ok_EmptyData_result()
        {
            // Arrange
            string fileLocationName = null;

            var fileLocations = new FileLocations();
            _web.GetFileLocations().Returns(fileLocations);
            var json = JsonConvert.SerializeObject(fileLocations);
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);

            FileLocationController controller = new FileLocationController(_web, _logger);

            // Act
            var actual = controller.GetFileLocations(fileLocationName);

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<Dictionary<string, string>>>().Which.Content.Should().BeEquivalentTo(dictionary);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public void test_GetFileLocations_returns_Ok_result(string fileLocationName)
        {
            // Arrange
            var fileLocations = new FileLocations
            {
                RetalixTransactionFileDirectory = "default",
                TransactionFileDirectory = "default",
                WhitelistDirectory = "default",
                LayoutDirectory = "default",
                SoftwareDirectory = "default",
                ContactlessPropertiesFile = "default",
                FuelDataUpdateFile = "default",
                UpgradeFileDirectory = "default",
                RollbackFileDirectory = "default",
                MediaDirectory = "default",
                PlaylistDirectory = "default",
                OptLogFileDirectory = "default",
                LogFileDirectory = "default",
                TraceFileDirectory = "default",
                JournalFileDirectory = "default",
                ReceivedUpdateDirectory = "default",
                DatabaseBackupDirectory = "default",
                EsocketOverrideContactless = false,
                MediaChannel = false,
            };
            _web.GetFileLocations().Returns(fileLocations);
            var json = JsonConvert.SerializeObject(fileLocations);
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);

            FileLocationController controller = new FileLocationController(_web, _logger);

            // Act
            var actual = controller.GetFileLocations(fileLocationName);

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<Dictionary<string, string>>>().Which.Content.Should().BeEquivalentTo(dictionary);
        }

        [Theory]
        [InlineData("RetalixTransactionFileDirectory", "c:\test")]
        [InlineData("TransactionFileDirectory", "c:\test")]
        [InlineData("WhitelistDirectory", "c:\test")]
        [InlineData("LayoutDirectory", "c:\test")]
        [InlineData("SoftwareDirectory", "c:\test")]
        [InlineData("ContactlessPropertiesFile", "c:\test")]
        [InlineData("FuelDataUpdateFile", "c:\test")]
        [InlineData("UpgradeFileDirectory", "c:\test")]
        [InlineData("RollbackFileDirectory", "c:\test")]
        [InlineData("MediaDirectory", "c:\test")]
        [InlineData("PlaylistDirectory", "c:\test")]
        [InlineData("OptLogFileDirectory", "c:\test")]
        [InlineData("LogFileDirectory", "c:\test")]
        [InlineData("TraceFileDirectory", "c:\test")]
        [InlineData("JournalFileDirectory", "c:\test")]
        [InlineData("ReceivedUpdateDirectory", "c:\test")]
        [InlineData("DatabaseBackupDirectory", "c:\test")]
        [InlineData("RetalixTransactionFileDirectory", "c:\test1")]
        [InlineData("TransactionFileDirectory", "c:\test1")]
        [InlineData("WhitelistDirectory", "c:\test1")]
        [InlineData("LayoutDirectory", "c:\test1")]
        [InlineData("SoftwareDirectory", "c:\test1")]
        [InlineData("ContactlessPropertiesFile", "c:\test1")]
        [InlineData("FuelDataUpdateFile", "c:\test1")]
        [InlineData("UpgradeFileDirectory", "c:\test1")]
        [InlineData("RollbackFileDirectory", "c:\test1")]
        [InlineData("MediaDirectory", "c:\test1")]
        [InlineData("PlaylistDirectory", "c:\test1")]
        [InlineData("OptLogFileDirectory", "c:\test1")]
        [InlineData("LogFileDirectory", "c:\test1")]
        [InlineData("TraceFileDirectory", "c:\test1")]
        [InlineData("JournalFileDirectory", "c:\test1")]
        [InlineData("ReceivedUpdateDirectory", "c:\test1")]
        [InlineData("DatabaseBackupDirectory", "c:\test1")]
        public void test_GetFileLocations_returns_Ok_Single_result(string fileLocationName, string value)
        {
            // Arrange
            var fileLocations = new FileLocations
            {
                RetalixTransactionFileDirectory = "default",
                TransactionFileDirectory = "default",
                WhitelistDirectory = "default",
                LayoutDirectory = "default",
                SoftwareDirectory = "default",
                ContactlessPropertiesFile = "default",
                FuelDataUpdateFile = "default",
                UpgradeFileDirectory = "default",
                RollbackFileDirectory = "default",
                MediaDirectory = "default",
                PlaylistDirectory = "default",
                OptLogFileDirectory = "default",
                LogFileDirectory = "default",
                TraceFileDirectory = "default",
                JournalFileDirectory = "default",
                ReceivedUpdateDirectory = "default",
                DatabaseBackupDirectory = "default",
                EsocketOverrideContactless = false,
                MediaChannel = false,
            };
            PropertyInfo propertyInfo = fileLocations.GetType().GetProperty(fileLocationName, BindingFlags.Public | BindingFlags.Instance);
            if (propertyInfo != null && propertyInfo.CanWrite == true)
            {
                propertyInfo.SetValue(fileLocations, value, null);
            }
            _web.GetFileLocations().Returns(fileLocations);
            var dictionary = new Dictionary<string, string>();
            dictionary.Add(fileLocationName, value);

            var controller = new FileLocationController(_web, _logger);

            // Act
            var actual = controller.GetFileLocations(fileLocationName);

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<Dictionary<string, string>>>().Which.Content.Should().BeEquivalentTo(dictionary);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void test_GetFileLocations_returns_Ok_SingleBoolean_result(bool value)
        {
            // Arrange
            const string fileLocationName = "MediaChannel";

            var fileLocations = new FileLocations
            {
                RetalixTransactionFileDirectory = "c:\test",
                MediaChannel = value
            };
            _web.GetFileLocations().Returns(fileLocations);
            var dictionary = new Dictionary<string, string>();
            dictionary.Add(fileLocationName, value.ToString().ToLower());

            var controller = new FileLocationController(_web, _logger);

            // Act
            var actual = controller.GetFileLocations(fileLocationName);

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<Dictionary<string, string>>>().Which.Content.Should().BeEquivalentTo(dictionary);
        }

        [Fact]
        public void test_GetFileLocations_returns_NotFound_result()
        {
            // Arrange
            string fileLocationName = "notFound";

            var fileLocations = new FileLocations();
            _web.GetFileLocations().Returns(fileLocations);

            var webResult = new Dictionary<string, string>();

            var controller = new FileLocationController(_web, _logger);

            // Act
            var actual = controller.GetFileLocations(fileLocationName);

            // Assert
            actual.Should().BeOfType<NotFoundResult>();
        }
    }
}

