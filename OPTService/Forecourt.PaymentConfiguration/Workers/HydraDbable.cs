using CSharpFunctionalExtensions;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Forecourt.PaymentConfiguration.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Logger.Interfaces;
using OPT.Common.Workers;
using System;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace Forecourt.PaymentConfiguration.Workers
{
    /// <summary>
    /// Base worker for (most) PaymentConfiguration workers
    /// </summary>
    public abstract class HydraDbable : HydraDbable<IHydraDb>, IPaymentConfigIntegrator
    {
        /// <summary>
        /// <see cref="IEsocketDb"/> instance
        /// </summary>
        protected IEsocketDb ESocketDb { get; private set; }

        /// <summary>
        /// <see cref="IContactlessProperties"/> instance
        /// </summary>
        protected IContactlessProperties ContactlessProperties { get; private set; }

        private readonly IFileSystem _fileSystem;

        /// <inheritdoc/>
        protected HydraDbable(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IFileSystem fileSystem, IEsocketDb esocketDb = null, IContactlessProperties contactlessProperties = null, IConfigurationManager configurationManager = null) :
            base(hydraDb, logManager, loggerName.ConvertToPrefixedDottedName(LoggerNamePrefix), configurationManager)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            ESocketDb = esocketDb;
            ContactlessProperties = contactlessProperties;
        }

        /// <inheritdoc/>
        public IList<TermId> AllPumpTids => ESocketDb?.AllPumpTids ?? new List<TermId>();

        /// <inheritdoc/>
        public TermProcCategory TermProcCategory => ESocketDb?.TermProcCategory;

        /// <inheritdoc/>
        public IList<CardAid> CardAids => ESocketDb?.CardAids ?? new List<CardAid>();

        /// <inheritdoc/>
        public IList<CardCapk> CardCapks => ESocketDb?.CardCapks ?? new List<CardCapk>();

        /// <inheritdoc/>
        public IList<FuelCard> FuelCards => ESocketDb?.FuelCards ?? new List<FuelCard>();

        /// <inheritdoc/>
        public string TermCapabilities => ESocketDb?.TermCapabilities;

        /// <inheritdoc/>
        public string TermAddCapabilities => ESocketDb?.TermAddCapabilities;

        /// <inheritdoc/>
        public string ConnectionString => ESocketDb?.ConnectionString;

        /// <inheritdoc/>
        public bool UseConnectionString => ESocketDb?.UseConnectionString ?? false;

        /// <inheritdoc/>
        public string ConfigFile => ESocketDb?.ConfigFile;

        /// <inheritdoc/>
        public string CurrentConfigFile => ESocketDb?.CurrentConfigFile;

        /// <inheritdoc/>
        public string KeystoreFile => ESocketDb?.KeystoreFile;

        /// <inheritdoc/>
        public string CurrentKeystoreFile => ESocketDb?.CurrentKeystoreFile;

        /// <inheritdoc/>
        public string DbUrl => ESocketDb?.DbUrl;

        /// <inheritdoc/>
        public string ContactlessFile => ESocketDb?.ContactlessFile;

        /// <inheritdoc/>
        public string CurrentContactlessFile => ESocketDb?.CurrentContactlessFile;

        /// <inheritdoc/>
        public bool OverrideProperties => ESocketDb?.OverrideProperties ?? false;

        /// <inheritdoc/>
        public bool OverrideKeystore => ESocketDb?.OverrideKeystore ?? false;

        /// <inheritdoc/>
        public bool OverrideUrl => ESocketDb?.OverrideUrl ?? false;

        /// <inheritdoc/>
        public bool OverrideContactless => ESocketDb?.OverrideContactless ?? false;

        /// <inheritdoc/>
        public bool ConnectionMade => ESocketDb?.ConnectionMade ?? false;

        /// <inheritdoc/>
        public IList<CardClessAid> Cards => ContactlessProperties?.Cards ?? new List<CardClessAid>();

        /// <inheritdoc/>
        public IList<CardClessDrl> Drls => ContactlessProperties?.Drls ?? new List<CardClessDrl>();

        /// <inheritdoc/>
        public virtual bool StrictPumpTidValidation => true;

        /// <inheritdoc/>
        public bool CheckTermProcCategory(string loggingReference) => ESocketDb?.CheckTermProcCategory(loggingReference) ?? false;

        /// <inheritdoc/>
        public void FetchContactlessProperties(IList<CardClessAid> cards, IList<CardClessDrl> drls, string loggingReference) => ESocketDb?.FetchContactlessProperties(cards, drls, loggingReference);

        /// <inheritdoc/>
        public void GetAllPumpTids(string loggingReference) => ESocketDb?.GetAllPumpTids(loggingReference);

        /// <inheritdoc/>
        public void MakeConnection() => ESocketDb?.MakeConnection();

        /// <inheritdoc/>
        public void RefreshCustomDataOverrides(string loggingReference) => ESocketDb?.RefreshCustomDataOverrides(loggingReference);

        /// <inheritdoc/>
        public void SetConfigFile(string fileName, bool connect = true) => ESocketDb?.SetConfigFile(fileName, connect);

        /// <inheritdoc/>
        public void SetConnectionString(string newConnectionString, bool connect = true) => ESocketDb?.SetConnectionString(newConnectionString, connect);

        /// <inheritdoc/>
        public void SetContactlessFile(string fileName) => ESocketDb?.SetContactlessFile(fileName);

        /// <inheritdoc/>
        public void SetDbUrl(string url, bool connect = true) => ESocketDb?.SetDbUrl(url, connect);

        /// <inheritdoc/>
        public void SetKeystoreFile(string fileName, bool connect = true) => ESocketDb?.SetKeystoreFile(fileName, connect);

        /// <inheritdoc/>
        public void SetOverrideContactless(bool flag) => ESocketDb?.SetOverrideContactless(flag);

        /// <inheritdoc/>
        public void SetOverrideKeystore(bool flag, bool connect = true) => ESocketDb?.SetOverrideKeystore(flag, connect);

        /// <inheritdoc/>
        public void SetOverrideProperties(bool flag, bool connect = true) => ESocketDb?.SetOverrideProperties(flag, connect);

        /// <inheritdoc/>
        public void SetOverrideUrl(bool flag, bool connect = true) => ESocketDb?.SetOverrideUrl(flag, connect);

        /// <inheritdoc/>
        public void SetServiceDirectory(string serviceDirectory) => ESocketDb?.SetServiceDirectory(serviceDirectory);

        /// <inheritdoc/>
        public void SetUseConnectionString(bool useConnectionString, bool connect = true) => ESocketDb?.SetUseConnectionString(useConnectionString, connect);

        /// <summary>
        /// Extension as to how the worker is initialised
        /// </summary>
        /// <returns>Result</returns>
        protected virtual Result DoInitialise()
        {
            return Result.Success();
        }

        /// <summary>
        /// Initialise the worker
        /// </summary>
        /// <returns>Result</returns>
        public Result Initialise(Action action = null)
        {
            return DoAction(() =>
            {
                var result = DoInitialise();
                action?.Invoke();

                if (!ESocketDb?.ConnectionMade ?? true)
                {
                    ESocketDb?.CheckTermProcCategory(LoggingReference);
                }
                return result;
            }, null);
        }

        /// <inheritdoc/>
        public IEnumerable<string> CheckGetAllPumpTids(string loggingReference)
        {
            return DoAction(() =>
            {
                ESocketDb?.CheckTermProcCategory(LoggingReference);
                return Result.Success(ESocketDb?.AllPumpTids.Select(x => x.Tid) ?? new List<string>());
            }, loggingReference).Value;
        }

        /// <inheritdoc/>
        public void ReadContactlessProperties()
        {
            DoAction(() =>
            {
                if (ESocketDb == null || ContactlessProperties == null)
                {
                    return;
                }

                var fileLocation = CurrentContactlessFile;
                DoDeferredLogging(LogLevel.Info, "PropertiesFileLocation", () => new[] { fileLocation });

                if (string.IsNullOrWhiteSpace(fileLocation) || !_fileSystem.File.Exists(fileLocation))
                {
                    FetchContactlessProperties(ContactlessProperties.Cards, ContactlessProperties.Drls, LoggingReference);
                }
                else
                {
                    try
                    {
                        string body = _fileSystem.File.ReadAllText(fileLocation);
                        ExtractContactlessProperties(body, LoggingReference);
                    }
                    catch (Exception ex)
                    {
                        DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { "Contactless properties setting not found while reading contactless properties" }, exception: ex);
                    }
                }
            }, null);
        }

        /// <inheritdoc/>
        public void ExtractContactlessProperties(string body, string loggingReference = null)
        {
            ContactlessProperties?.ExtractContactlessProperties(body, loggingReference);
        }

        /// <inheritdoc/>
        public virtual IList<Tuple<string, int>> EndPoints => HydraDb?.FetchESocketEndPoints().Select(x => Tuple.Create(x.IpAddress, x.Port)).ToList();
    }
}
