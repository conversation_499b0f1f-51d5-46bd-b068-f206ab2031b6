using CSharpFunctionalExtensions;
using Forecourt.SecondaryAuth.ApiController.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Web.Http;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Description;
using Unity;
using core = Htec.Hydra.Core.SecondaryAuth.Messages;

namespace OPTService.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all Secondary Authorisation responses
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "WebUi,Swagger,Orbis")]
    [RoutePrefix("api/secAuth")]
    public class SecAuthController : LinkedLoggableApiController<ISecAuthIntegratorInTransient<IMessageTracking>>
    {
        /// <inheritdoc/>
        public SecAuthController([Dependency(UnityConfig.MessageBrokerName)]ISecAuthIntegratorInTransient<IMessageTracking> loggable, IConfigurationManager configurationManager = null) : base(loggable as Loggable, configurationManager)
        {
        }

        private StatusCodeResult ResultMapper(Result r, HttpStatusCode failCode = HttpStatusCode.BadRequest) => 
            r.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(failCode, new System.Exception(r.Error));

        private IMessageTracking GetMessageTracking(Response response) =>
            new MessageTracking() { ParentIdAsString = response.TransactionId, IdAsString = response.MessageId };
        private IMessageTracking GetMessageTracking(SecAuthResponse response) =>
                 new MessageTracking() { ParentIdAsString = response.TransactionId, IdAsString = response.MessageId };

        private core.SecAuthResponse CastResponse(byte pump, Response response, IMessageTracking message) => new()
        {
            Pump = pump,
            MessageId = message.IdAsString,
            TransactionId = message.ParentIdAsString,
            IsAuthorised = response.IsAuthorised,
            VehicleRegistration = response.VehicleRegistration,
            TimeStamp = System.DateTime.UtcNow
        };

        /// <summary>
        /// Send a PreAuth Secondary Auth response into the Forecourt Service
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="response">Response details</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, response was processed</response>
        /// <response code="400">Bad/invalid response</response>
        [HttpPost, Route("pump/{pump:range(1,256)}/response/preAuth}"), ResponseType(typeof(Response))]
        public async Task<IHttpActionResult> PreAuthResponse([FromUri] byte pump, [FromBody] Response response)
        {
            if (response == null)
            {
                return GetActionResult(StatusCodeResult.Specific(HttpStatusCode.BadRequest, new System.Exception("Invalid Response")));
            }

            var message = GetMessageTracking(response);
            var coreResponse = CastResponse(pump, response, message);

            return await DoActionAsync(async () => Loggable.PreAuthReponse(coreResponse, message), (r) => ResultMapper(r)).ConfigureAwait(false);
        }

        /// <summary>
        /// Send a PostAuth Secondary Auth response into the Forecourt Service
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="response">Response details</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, response was processed</response>
        /// <response code="400">Bad/invalid response</response>
        [HttpPost]
        [Route("pump/{pump:range(1,256)}/response/postAuth"), ResponseType(typeof(Response))]
        public async Task<IHttpActionResult> PostAuthResponse([FromUri] byte pump, [FromBody] Response response)
        {
            if (response == null)
            {
                return GetActionResult(StatusCodeResult.Specific(HttpStatusCode.BadRequest, new System.Exception("Invalid Response")));
            }

            var message = GetMessageTracking(response);
            var coreResponse = CastResponse(pump, response, message);

            return await DoActionAsync(async () => Loggable.PostAuthResponse(coreResponse, message), (r) => ResultMapper(r)).ConfigureAwait(false);
        }

        /// <summary>
        /// Send a PostAuth Secondary Auth response into the Forecourt Service
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="loggingReference">Logging reference, optional</param>
        /// <param name="response">Response details</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, response was processed</response>
        /// <response code="400">Bad/invalid response</response>
        [HttpPost]
        [Route("pump/{pump:range(1,256)}/response/postAuth/{loggingReference}")]
        public async Task<IHttpActionResult> PostAuthResponse2([FromUri] byte pump, [FromBody] SecAuthResponse response, [FromUri]string loggingReference = null)
        {
            // Ignore pump/loggingReference as all information is held within response, Orbis code already committed!

            if (response == null)
            {
                return GetActionResult(StatusCodeResult.Specific(HttpStatusCode.BadRequest, new System.Exception("Invalid Response")));
            }

            var message = GetMessageTracking(response);
            
            return await DoActionAsync(async () => Loggable.PostAuthResponse(response, message), (r) => ResultMapper(r)).ConfigureAwait(false);
        }
    }
}
