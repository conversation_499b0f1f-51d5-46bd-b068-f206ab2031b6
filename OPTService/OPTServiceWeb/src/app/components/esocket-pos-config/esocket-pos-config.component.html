<div id="card-esocket-pos-config" class="card form-group" *ngIf="showContent" [formGroup]="configForm">
    <div class="card-header">
        <button type="button" (click)="configurationCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
            <span>eSocket.POS configuration</span>
            <div id="eSocketConnectedBadge">
                <app-esocket-connected-badge [eSocketConnected]="fileLocationsData?.EsocketConnectionMade"></app-esocket-connected-badge>
                <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':configurationIsCollapsed,'bi-chevron-up':configurationIsCollapsed===false }"></i>
            </div>
        </button>
    </div>
    <div #configurationCollapse="ngbCollapse" [(ngbCollapse)]="configurationIsCollapsed" class="card-body">      
        <ng-container *ngIf="fileLocationsData?.EsocketUseConnectionString === false">
            <div class="row form-group">
                <label [ngClass]="labelColClass" class="form-check-label">Properties file location</label>
                <div [ngClass]="radioColClass">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="defaultEsocketOverrideProperties" [value]="false"
                            formControlName="EsocketOverrideProperties" (change)="setEsocketOverrideProperties($event)">
                        <label class="form-check-label" for="defaultEsocketOverrideProperties">Default</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="overrideEsocketOverrideProperties" [value]="true"
                            formControlName="EsocketOverrideProperties" (change)="setEsocketOverrideProperties($event)">
                        <label class="form-check-label" for="overrideEsocketOverrideProperties">Override</label>
                    </div>
                </div>
                <div [ngClass]="inputColClass">
                    <app-label-text-button *ngIf="fileLocationsData?.EsocketOverrideProperties === true"
                        id="esocketConfigFile" labelText="" controlName="EsocketConfigFile"
                        (action)="setEsocketConfigFile($event)" [errorInAction]="errors.EsocketConfigFile"
                        labelColClass="col-1" textColClass="col-9">
                    </app-label-text-button>
                </div>
            </div>
            <div class="row form-group">
                <label [ngClass]="labelColClass" class="form-check-label">Keystore file location</label>
                <div [ngClass]="radioColClass">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="defaultEsocketOverrideKeystore" [value]="false"
                            formControlName="EsocketOverrideKeystore" (change)="setEsocketOverrideKeystore($event)">
                        <label class="form-check-label" for="defaultEsocketOverrideKeystore">Default</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="overrideEsocketOverrideKeystore" [value]="true"
                            formControlName="EsocketOverrideKeystore" (change)="setEsocketOverrideKeystore($event)">
                        <label class="form-check-label" for="overrideEsocketOverrideKeystore">Override</label>
                    </div>
                </div>
                <div [ngClass]="inputColClass">
                    <app-label-text-button *ngIf="fileLocationsData?.EsocketOverrideKeystore === true"
                        id="esocketKeystoreFile" labelText="" controlName="EsocketKeystoreFile"
                        (action)="setEsocketKeystoreFile($event)" [errorInAction]="errors.EsocketKeystoreFile" 
                        labelColClass="col-1" textColClass="col-9">
                    </app-label-text-button>
                </div>
            </div>
            <div class="row form-group">
                <label [ngClass]="labelColClass" class="form-check-label">Database URL</label>
                <div [ngClass]="radioColClass">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="defaultEsocketOverrideUrl" [value]="false"
                            formControlName="EsocketOverrideUrl" (change)="setEsocketOverrideUrl($event)">
                        <label class="form-check-label" for="defaultEsocketOverrideUrl">Default</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" id="overrideEsocketOverrideUrl" [value]="true"
                            formControlName="EsocketOverrideUrl" (change)="setEsocketOverrideUrl($event)">
                        <label class="form-check-label" for="overrideEsocketOverrideUrl">Override</label>
                    </div>
                </div>
                <div [ngClass]="inputColClass">
                    <app-label-text-button *ngIf="fileLocationsData?.EsocketOverrideUrl === true"
                        id="esocketDbUrl" labelText="" controlName="EsocketDbUrl"
                        (action)="setEsocketDbUrl($event)" [errorInAction]="errors.EsocketDbUrl"
                        labelColClass="col-1" textColClass="col-9">
                    </app-label-text-button>
                </div>    
            </div>
        </ng-container>
        <ng-container *ngIf="fileLocationsData?.EsocketUseConnectionString === true">
            <app-label-text-button id="esocketConnectionString" labelText="eSocket.POS connection string"
                controlName="EsocketConnectionString" (action)="setEsocketConnectionString($event)" [errorInAction]="errors.EsocketConnectionString"
                labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-6">
            </app-label-text-button>
        </ng-container>
    </div>
</div>
