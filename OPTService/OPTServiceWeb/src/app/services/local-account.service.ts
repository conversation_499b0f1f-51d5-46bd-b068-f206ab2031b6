import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class LocalAccountService {

  private baseUrl: string;

  /**
   * The LocalAccountService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.localAccountController.base;
  }

  /**
   * Gets the local account customers
   */
  public getLocalAccounts(): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.getLocalAccounts;
    const options = {
      params: {}
    };

    this.logger.debug(`Getting local accounts via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Adds a customer to local accounts
   * @param customer customer to add
   */
  public addLocalAccountCustomer(customer: any): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.addLocalAccountCustomer;
    this.logger.debug(`Adding local account customer via ${url}`);
    return this.http.post(url, customer);
  }

  /**
   * Removes a customer to local accounts
   * @param customer customer to add
   */
   public removeLocalAccountCustomer(customer: any): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.removeLocalAccountCustomer;
    this.logger.debug(`Removing local account customer via ${url}`);
    return this.http.post(url, customer);
  }
 
  /**
   * Sets balanace for a given customer
   * @param customer customer to add
   */
   public setLocalAccountCustomerBalance(customer: any): Observable<any> {
    let url: string;
    let balance = customer.Balance;
    if (balance === 0) {
      url = this.baseUrl + environment.localAccountController.clearLocalAccountCustomerBalance;
    } else {
      url = this.baseUrl + environment.localAccountController.setLocalAccountCustomerBalance;
    }
    
    this.logger.debug(`Setting balance for local account customer via ${url}`);
    return this.http.post(url, customer);
  }

  /**
   * Adds a local accounts card to a customer
   * @param card card to add
   */
   public addLocalAccountCard(card: any): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.addLocalAccountCard;
    this.logger.debug(`Adding local account card via ${url}`);
    return this.http.post(url, card);
  }
  
  /**
   * Sets local accounts enabled flag
   * @param enabled enabled flag value
   */
  public setLocalAccountsEnabled(enabled: boolean): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.setLocalAccountsEnabled + '?enabled=' + enabled;
    this.logger.debug(`Setting local accounts enabled: ${enabled} via ${url}`);
    return this.http.post(url, {});
  }

  /**
   * Removes a local account card
   * @param card card to remove
   */
   public removeLocalAccountCard(card: any): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.removeLocalAccountCard;
    this.logger.debug(`Removing local account card via ${url}`);
    return this.http.post(url, card);
  }

  /**
   * Changes the hot status of a card
   * @param card card to change
   */
   public setLocalAccountCardHot(card: any): Observable<any> {
    const url = this.baseUrl + environment.localAccountController.setLocalAccountCardHot;
    this.logger.debug(`Changeing hot status card via ${url}`);
    return this.http.post(url, card);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
