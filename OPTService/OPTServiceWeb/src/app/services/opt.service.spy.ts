import { TestBed } from "@angular/core/testing";
import { OptService } from "./opt.service";

export function OPT_SERVICE_PROVIDER(): { provide: typeof OptService, useValue: any } {
  return {
    provide: OptService,
    useValue: jasmine.createSpyObj('OptService', [
      'addDiscountCard',
      'addEsocketEndpoint',
      'addPredefinedAmount',
      'addTariffMapping',
      'addWash',
      'clearMaxFillOverride', 
      'getGenericOptConfig', 
      'getOptDataChanged', 
      'getOpts',
      'reloadOptConfiguration', 
      'removeDiscountCard',
      'removeEsocketEndpoint',
      'removePredefinedAmount',
      'removeTariffMapping',
      'removeWash',
      'refreshOpt',
      'restartOpt',
      'requestOptLog',
      'setContactlessAllowed',
      'setContactlessCardPreAuth',
      'setContactlessDevicePreAuth',
      'setContactlessTtq',
      'setCurrencyCode',
      'setGenericServiceAdress', 
      'setMaxFillOverride',
      'setNozzleUpForKioskUse',
      'setPlaylistFileName',
      'setGlobalReceiptFooter',
      'setReceiptFooter',
      'setGlobalReceiptHeader',
      'setReceiptHeader',
      'setReceiptLayoutMode',
      'setReceiptReprintAvailability', 
      'setReceiptMaxCount',
      'setSiteName',
      'setIntegrationType',
      'setTariffMappingFuelCardsOnly',
      'setUseReplaceNozzleScreen',
      'setVatNumber',
      'startConfigBatch', 
      'stopConfigBatch', 
    ]),
  };
}

export function OPT_SERVICE_SPY(): jasmine.SpyObj<OptService> {
  return TestBed.inject(OptService) as jasmine.SpyObj<OptService>;
}
