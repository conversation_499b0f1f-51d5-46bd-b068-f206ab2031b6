import { Component, NgZone, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { PrinterDetails } from 'src/app/core/models/printerDetails';
import { ShiftEndDetails } from 'src/app/core/models/shiftEndDetails';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { LoadingService } from 'src/app/services/loading.service';
import { ShiftEndService } from 'src/app/services/shift-end.service';
import { SignalRService } from 'src/app/services/signal-r.service';

@Component({
  selector: 'app-shift-end',
  templateUrl: './shift-end.component.html',
  styleUrls: ['./shift-end.component.css']
})
export class ShiftEndComponent implements OnInit {

  showContent: boolean = false;
  baudRates: Array<number> = [110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 28800, 38400, 56000, 57600, 115200, 128000, 153600, 230400, 256000, 460800, 921600];
  handshakes: Array<string> = ['None', 'XOnXOff', 'RequestToSend', 'RequestToSendXOnXOff'];
  stopBits: Array<string> = ['None', 'One', 'OnePointFive', 'Two'];
  dataBits: Array<number> = [5, 6, 7, 8];
  printerPortNameError: boolean = false;
  signalRData: Subscription | undefined;
  shiftEndDetails: ShiftEndDetails = new ShiftEndDetails();
  shiftEndForm = this.fb.group({
    ShiftEndTime: ['', Validators.required],
    DayEndTime: ['', Validators.required],
    NextDayEnd: [''],
    Unmanned: [''],
    AsdaDayEndReport: [''],
    LogIntervalControl: [''],
    NextDayEndControl: [''],
    PrinterDetails: this.fb.group({
      BaudRate: [''],
      DataBits: [''],
      NextDayEnd: [''],
      Enabled: [''],
      Handshake: [''],
      IsPrinterBusy: [''],
      PortName: [''],
      StopBits: ['']
    })
  });

  public get printerDetails(): PrinterDetails {
    return this.shiftEndDetails?.PrinterDetails;
  }

  constructor(
    private signalRService: SignalRService,
    private zone: NgZone,
    private fb: FormBuilder,
    private shiftEndService: ShiftEndService,
    private logger: NGXLogger,
    private loadingService: LoadingService
  ) {
    this.signalRData = this.signalRService.getShiftEndSignalRMessage().subscribe(() => {
      this.zone.run(() => {
        this.refreshData(false);
      });
    });
  }

  /**
   * ngOnInit angular hook.
   */
  ngOnInit(): void {
    // Refreshing data
    setTimeout(()=>this.refreshData());

    // Formulary assignments
    this.shiftEndForm.patchValue(this.shiftEndDetails);
    Object.keys(this.shiftEndForm.controls).forEach(key => {
      this.shiftEndForm.controls[key].valueChanges.subscribe(val => {
        this.shiftEndDetails[key] = val;
      });
    });
  }

  /**
   * ngOnDestroy angular hook.
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * The refresh data method called when received a push from signalR
   */
  refreshData(showLoading: boolean = true): void {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();  
    }

    this.shiftEndService.getShiftEndInfo().subscribe(data => {
      this.logger.debug('ShiftEnd data: ', data)
      this.shiftEndDetails = data;
      this.shiftEndForm.patchValue(data);

      var nextDayEndDate = new Date(data.NextDayEnd ? data.NextDayEnd : '1900-01-01T00:00:00');
      this.shiftEndForm.controls.NextDayEndControl.setValue(this.getStringTime(nextDayEndDate.getHours(), nextDayEndDate.getMinutes(), 0));
      this.shiftEndForm.controls.LogIntervalControl.setValue(this.getStringTime(data.LogInterval?.Hours, data.LogInterval?.Minutes, 0));
    }, error => {
      this.logger.error('Error getting ShiftEnd data: ', error);
      this.loadingService.errorDuringLoading();
    },
    () => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

  getStringTime(hours: number, minutes: number, seconds: number) : string {
    return hours.toString().padStart(2, '0') + 
    ':' + minutes.toString().padStart(2, '0') + 
    ':' + seconds.toString().padStart(2, '0');
  }

  /**
   * Performs ShiftEnd operation 
   */
  public performShiftEnd(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.performShiftEnd().subscribe(() => {
      this.logger.debug('ShiftEnd operation done');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error performing ShiftEnd operation: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Performing DayEnd operation 
   */
  public performDayEnd(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.performDayEnd().subscribe(() => {
      this.logger.debug('DayEnd operation done');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error performing DayEnd operation: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Performs ClearAutoDayEnd operation 
   */
  public clearAutoDayEnd(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.clearAutoDayEnd().subscribe(() => {
      this.logger.debug('ClearAutoDayEnd operation done');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error performing ClearAutoDayEnd operation: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets Asda Day End Report On 
   */
  public setAsdaDayEndReportOn(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setAsdaDayEndReportOn().subscribe(() => {
      this.logger.debug('Asda Day End Report enabled');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error settting Asda Day End Report on: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Setting Asda Day End Report Off 
   */
  public setAsdaDayEndReportOff(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setAsdaDayEndReportOff().subscribe(() => {
      this.logger.debug('Asda Day End Report disabled');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error settting Asda Day End Report off: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets Auto Day End time
   */
  public setNextDayEnd(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setNextDayEnd(this.shiftEndForm.controls.NextDayEndControl.value.toString()).subscribe(() => {
      this.logger.debug('Auto Day End time set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error settting Auto Day End time: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets log interval
   */
  public setLogInterval(event?: Event): void {
    ButtonHelper.clicked(event);
    const logIntervalArray = this.shiftEndForm.controls.LogIntervalControl.value.split(':');
    this.shiftEndDetails.LogInterval.Hours = +logIntervalArray[0];
    this.shiftEndDetails.LogInterval.Minutes = +logIntervalArray[1];
    this.shiftEndDetails.LogInterval.Seconds = 0;
    this.shiftEndService.setLogInterval(this.shiftEndDetails.LogInterval).subscribe(() => {
      this.logger.debug('Log interval set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error settting log interval: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer enabled
   */
  public setPrinterEnabled(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndDetails.PrinterDetails.Enabled = !this.shiftEndDetails.PrinterDetails.Enabled;
    this.shiftEndService.setPrinterEnabled(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.logger.debug('Printer enabled');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error enabling printer: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer port name
   */
  public setPrinterPortName(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setPrinterPortName(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.printerPortNameError = false;
      this.logger.debug('Printer port name set');
      ButtonHelper.reset(event);
    }, error => {
      this.printerPortNameError = true;
      this.logger.error('Error setting printer port name: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer baud rate
   */
  public setPrinterBaudRate(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setPrinterBaudRate(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.logger.debug('Printer baud rate set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error setting printer baud rate: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer handshake
   */
  public setPrinterHandshake(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setPrinterHandshake(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.logger.debug('Printer handshake set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error setting printer handshake: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer stop bits
   */
  public setPrinterStopBits(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setPrinterStopBits(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.logger.debug('Printer stop bits set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error setting printer stop bits: ', error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set printer data bits
   */
  public setPrinterDataBits(event?: Event): void {
    ButtonHelper.clicked(event);
    this.shiftEndService.setPrinterDataBits(this.shiftEndDetails.PrinterDetails).subscribe(() => {
      this.logger.debug('Printer data bits set');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Error setting printer data bits: ', error);
      ButtonHelper.reset(event);
    });
  }
}
