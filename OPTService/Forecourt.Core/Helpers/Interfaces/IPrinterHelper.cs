using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models.Interfaces;

namespace Forecourt.Core.Helpers.Interfaces
{
    /// <summary>
    /// Any and all printer helper functionality
    /// </summary>
    /// <typeparam name="TMessageTracking"></typeparam>
    public interface IPrinterHelper<TMessageTracking> where TMessageTracking : ILogTracking
    {
        string InitPrinter { get; }
        string BoldOn { get; }
        string BoldOff { get; }
        string Centred { get; }
        string LeftJustified { get; }
        string DoubleSize { get; }
        string SingleSize { get; }
        string FeedAndCut { get; }
        string PrintLogo { get; }

        /// <summary>
        /// Format the receipt (as string)
        /// </summary>
        /// <param name="receipt">Receipt string</param>
        /// <param name="isPrint">For printable purposes</param>
        /// <param name="useControlChars">Use Control characters</param>
        /// <param name="message">Current message tracking information</param>
        /// <returns>Result wrapperd string</returns>

        string FormatReceipt(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null);

        /// <summary>
        /// Has the logo been uploaded to the printer
        /// </summary>
        bool IsLogoUploaded { get; set; }

        string ReplaceCommandStrings(string input);
    }
}
