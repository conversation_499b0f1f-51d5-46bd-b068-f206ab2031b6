using OPT.Common;
using OPT.Common.Constants;

namespace Forecourt.Core.Configuration
{
    /// <summary>
    /// All constants related to Configuration
    /// </summary>
    public static partial class Constants
    {
        /// <summary>
        /// Constant for configuration name
        /// </summary>
        public const string NameConfiguration = ConfigurationConstants.NameConfiguration;

        /// <summary>
        /// Name of General category, as it will appear in config
        /// </summary>
        public const string CategoryNameGeneral = "GENERAL";

        /// <summary>
        /// Name of Contactless category
        /// </summary>
        public const string CategoryNameContactless = "Contactless";

        /// <summary>
        /// Name of SiteInfo category
        /// </summary>
        public const string CategoryNameSiteInfo = "SiteInfo";

        /// <summary>
        /// Name of Cache category
        /// </summary>
        public const string CategoryNameCache = "CACHE";

        /// <summary>
        /// Name of POS category
        /// </summary>
        public const string CategoryNamePOS = "POS";

        /// <summary>
        /// Name of Web category
        /// </summary>
        public const string CategoryNameWeb = "WEB";

        /// <summary>
        /// Constant that separates the category part from the key part, of a ConfigKey value
        /// </summary>
        public const string CategorySeparator = ConfigurationConstants.CategorySeparator;

        /// <summary>
        /// Cached item name for, HydraDb
        /// </summary>
        public const string ConfigKeySuffixHydraDb = "HydraDb:";

        /// <summary>
        /// Cached item name for, EndPoints
        /// </summary>
        public const string ConfigKeySuffixEndPoints = ConfigKeySuffixHydraDb + "EndPoints:";

        /// <summary>
        /// Cached item Prefix, for all repositories
        /// </summary>
        public const string CachedItemPrefixRepository = ConfigKeySuffixHydraDb + "Repository:";

        /// <summary>
        /// Cached item name for the configuration repository
        /// </summary>
        public const string CachedItemTypeConfiguration = CachedItemPrefixRepository + NameConfiguration + ":";

        /// <summary>
        /// Cached item name for categories
        /// </summary>
        public const string CachedItemCategories = "Categories";

        /// <summary>
        /// Cached item Prefix, for each Category
        /// </summary>
        public const string CachedItemPrefixCategory = "Category:";

        /// <summary>
        /// Cached item name for, ESocket
        /// </summary>
        public const string CachedItemESocket = "ESocket";

        /// <summary>
        /// Cached item name for, ESocket
        /// </summary>
        public const string CachedItemHydraOpt = ConfigConstants.NameOpt + ":";

        /// <summary>
        /// Cached item name for, IsGenericLoyaltyAvailable
        /// </summary>
        public const string CachedItemIsGenericLoyaltyAvailable = "IsGenericLoyaltyAvailable:";

        /// <summary>
        /// Cached item name for, IsGenericLoyaltyPresent
        /// </summary>
        public const string CachedItemIsGenericLoyaltyPresent = "IsGenericLoyaltyPresent:";

        /// <summary>
        /// Cached item name for, AdvancedConfig
        /// </summary>
        public const string CachedItemAdvancedConfig = "AdvancedConfig";

        /// <summary>
        /// Name of Logging category
        /// </summary>
        public const string ConfigCategoryLogging = ConfigurationConstants.ConfigCategoryLogging;

        /// <summary>
        /// Configuration category for all things Marina
        /// </summary>
        public const string CategoryNameMarina = "MARINA";

        public const string ConfigCategoryOpt = ConfigConstants.NameOptUpper + CategorySeparator;

        /// <summary>
        /// Cached item name for, RetalixPosPrimaryIpAddress
        /// </summary>
        public const string CachedItemRetalixPosPrimaryIpAddress = "RetalixPosPrimaryIpAddress";

        /// <summary>
        /// Config key for, Maximum Transaction Number (before cycling)
        /// </summary>
        /// <remarks>Tied to Retalix as this was the POS in operation at the time!</remarks>
        public const string ConfigKeyMaxTransNumber = "Retalix:MaxTransactionNumber";

        /// <summary>
        /// Default value for, Maximum Transaction Number (before cycling)
        /// </summary>
        /// <remarks>Tied to Retalix as this was the POS in operation at the time!</remarks>
        public const uint DefaultMaxTransactionNumber = 100000; // The pos can only deal with 99999

        /// <summary>
        /// Cached item name for, all file locations
        /// </summary>
        public const string CachedItemAllFileLocations = "AllFileLocations";

        /// <summary>
        /// Name of Secondary Authorisation category
        /// </summary>
        public const string CategoryNameSecondaryAuthorisation = "SECONDARYAUTH";

        /// <summary>
        /// Config key for, SecAuth Timed Out Response
        /// </summary>
        public const string ConfigKeyPrefixSecAuthTimedOutResponse = CategoryNameSecondaryAuthorisation + CategorySeparator + "TimedOutResponse:";

        /// <summary>
        /// Default vaue for, SecAuth Timed Out Response
        /// </summary>
        public const bool DefaultValueSecAuthTimedOutResponse = true;

        /// <summary>
        /// Name of Shift and Day End category
        /// </summary>
        public const string CategoryNameShiftAndDayEnd = "SHIFT|DAY-END";

        /// <summary>
        /// Name of Timers category, as it will appear in config
        /// </summary>
        public const string CategoryNameTimers = "TIMERS";

        /// <summary>
        /// Name of Workers category, as it will appear in config
        /// </summary>
        public const string CategoryNameWorkers = "WORKERS";

        /// <summary>
        /// Name of EndPoints API category
        /// </summary>
        public const string CategoryNameEndPointsApi = "ENDPOINTS:API";

        /// <summary>
        /// Name of EndPoints SignalR category
        /// </summary>
        public const string CategoryNameEndPointsSignalR = "ENDPOINTS:SIGNALR";

        /// <summary>
        /// Cached item name for, Endpoints
        /// </summary>
        public const string CachedItemEndPoints = "EndPoints";

        /// <summary>
        /// Name of EndPoints SignalR category
        /// </summary>
        public const string CategoryNameSignalRHubClient = "SIGNALR-HUBCLIENT";

        /// <summary>
        /// Cached item name for, Bos Configuration Data Map
        /// </summary>
        public const string CachedItemHydraToExternalConfigDataMap = "ConfigDataMap";

        /// <summary>
        /// Name of POS category
        /// </summary>
        public const string CategoryNameBOS = "BOS";

        /// <summary>
        /// Cached item name for, Bos config
        /// </summary>
        public const string CachedItemBosConfig = "Config";

        /// <summary>
        /// Name of Pump category
        /// </summary>
        public const string CategoryNamePump = "PUMP";

        /// <summary>
        /// Name of HouseKeeping category, as it will appear in config
        /// </summary>
        public const string CategoryNameHouseKeeping = "HOUSEKEEPING";

        /// <summary>
        /// Name of TimeModeChange category, as it will appear in config
        /// </summary>
        public const string CategoryNameTimeModeChange = "TIMEMODECHANGE";

        /// <summary>
        /// Config key for, internal Grade list
        /// </summary>
        public const string ConfigKeyGrades = "GradeList";
    }
}
