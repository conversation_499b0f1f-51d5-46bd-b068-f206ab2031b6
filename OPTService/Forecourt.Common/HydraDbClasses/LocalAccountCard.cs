namespace OPT.Common.HydraDbClasses
{
    public class LocalAccountCard
    {
        public string Pan { get; }
        public string Description { get; }
        public float Discount { get; }
        public bool NoRestrictions { get; }
        public bool Unleaded { get; }
        public bool Diesel { get; }
        public bool Lpg { get; }
        public bool Lrp { get; }
        public bool GasOil { get; }
        public bool AdBlue { get; }
        public bool Kerosene { get; }
        public bool Oil { get; }
        public bool Avgas { get; }
        public bool Jet { get; }
        public bool Mogas { get; }
        public bool Valeting { get; }
        public bool OtherMotorRelatedGoods { get; }
        public bool ShopGoods { get; }
        public bool Hot { get; private set; }

        public int Restrictions1 => NoRestrictions
            ? 0
            : (Unleaded ? 1 : 0) | (Diesel ? 2 : 0) | (Lpg ? 4 : 0) | (Lrp ? 8 : 0) | (GasOil ? 16 : 0) | (AdBlue ? 32 : 0) |
              (Kerosene ? 64 : 0);

        public int Restrictions2 =>
            NoRestrictions
                ? 0
                : (Oil ? 1 : 0) | (Avgas ? 2 : 0) | (Jet ? 4 : 0) | (Mogas ? 8 : 0) | (Valeting ? 16 : 0) |
                  (OtherMotorRelatedGoods ? 32 : 0) | (ShopGoods ? 64 : 0);

        public LocalAccountCard
        (string pan, string description, double discount, bool noRestrictions, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods, bool hot)
        {
            Pan = pan;
            Description = description;
            Discount = (float) discount;
            NoRestrictions = noRestrictions;
            Unleaded = unleaded;
            Diesel = diesel;
            Lpg = lpg;
            Lrp = lrp;
            GasOil = gasOil;
            AdBlue = adBlue;
            Kerosene = kerosene;
            Oil = oil;
            Avgas = avgas;
            Jet = jet;
            Mogas = mogas;
            Valeting = valeting;
            OtherMotorRelatedGoods = otherMotorRelatedGoods;
            ShopGoods = shopGoods;
            Hot = hot;
        }

        public void SetHot(bool hot)
        {
            Hot = hot;
        }
    }
}