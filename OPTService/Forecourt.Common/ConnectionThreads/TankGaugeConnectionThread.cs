using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;

namespace OPT.Common.ConnectionThreads
{
    [HasConfiguration]
    public class TankGaugeConnectionThread : PlainTextClientConnectionThread
    {
        public TankGaugeConnectionThread(IHtecLogManager logMan, ISocketWrapperFactory socketWrapperFactory, IConfigurationManager configurationManager,
            MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.None, bool logRxTx = true) : base(logMan, "TankGauge", socketWrapperFactory, configurationManager, messageLengthFlow, logRxTx)
        {
        }
    }
}
