using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc cref="Migrationable"/>
    [Migration(20050014, "ADO#623939 - Add stored procedures for adding/updating ConfigurationDetails"), Tags(Constants.MigrationTypeSchema)]
    public class ADO623939_Add_UpsertConfigurationDetail : MigrationableProgrammibility
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">Migration logger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        public ADO623939_Add_UpsertConfigurationDetail(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory, IFileSystem fileSystem) :
            base(null, new List<string> { "dbo.UpsertConfigurationDetail" }, logger, dbExecutorFactory, fileSystem)
        { }
    }
}
