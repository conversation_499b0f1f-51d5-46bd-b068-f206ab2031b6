using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    [Migration(20040006, "ADO#620347 - In dbo.GetReceiptHash, truncate Receipt Content if necessary, and deal with pre and post change content"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO4620347_Update_GetReceiptHash : Migrationable
    {
        public ADO4620347_Update_GetReceiptHash(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetReceiptHash", "dbo.AddReceipt", "dbo.GetReceipt", "dbo.GetReceiptForOpt", "dbo.GetReceipts" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO4620347_Update_GetReceiptHash), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{(dbObject == "dbo.GetReceiptHash" ? EmbeddedResourcePathFunctions : EmbeddedResourcePathStoredProcedures)}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO4620347_Update_GetReceiptHash), DbObjects.Reverse().ToArray());
        }
    }
}
