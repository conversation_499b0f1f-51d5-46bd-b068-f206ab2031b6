import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ControlContainer, FormGroup, FormGroupDirective } from '@angular/forms';
import { Setting } from 'src/app/core/models/setting.model';
import { MiscSettingDetailComponent } from './misc-setting-detail.component';

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('MiscSettingDetailComponent', () => {
  let component: MiscSettingDetailComponent;
  let fixture: ComponentFixture<MiscSettingDetailComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MiscSettingDetailComponent],
      providers: [
        { provide: ControlContainer, useValue: fgd }
      ]
    })
    fixture = TestBed.createComponent(MiscSettingDetailComponent);
    component = fixture.componentInstance;
    component.setting = new Setting();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('fieldFeedbackId', () => {

    it('should set fieldFeedbackId when no ID set', () => {
      expect(component.fieldFeedbackId).toEqual('FieldFeedback');
    });

    it('should set fieldFeedbackId using ID when set', () => {
      component.id = 'input';

      expect(component.fieldFeedbackId).toEqual('inputFieldFeedback');
    });
  });
  
  describe('formGroupId', () => {

    it('should set formGroupId when no ID set', () => {
      expect(component.formGroupId).toEqual('FormGroup');
    });

    it('should set formGroupId using ID when set', () => {
      component.id = 'input';

      expect(component.formGroupId).toEqual('inputFormGroup');
    });
  });

  describe('actionButtonId', () => {

    it('should set actionButtonId when no ID set', () => {
      expect(component.actionButtonId).toEqual('ActionBtn');
    });

    it('should set actionButtonId using ID when set', () => {
      component.id = 'input';

      expect(component.actionButtonId).toEqual('inputActionBtn');
    });
  });

  describe('revertDefaultButtonId', () => {

    it('should set revertDefaultButtonId when no ID set', () => {
      expect(component.revertDefaultButtonId).toEqual('RevertDefaultBtn');
    });

    it('should set revertDefaultButtonId using ID when set', () => {
      component.id = 'input';

      expect(component.revertDefaultButtonId).toEqual('inputRevertDefaultBtn');
    });
  });

  describe('revertGlobalButtonId', () => {

    it('should set revertGlobalButtonId when no ID set', () => {
      expect(component.revertGlobalButtonId).toEqual('RevertGlobalBtn');
    });

    it('should set revertGlobalButtonId using ID when set', () => {
      component.id = 'input';

      expect(component.revertGlobalButtonId).toEqual('inputRevertGlobalBtn');
    });
  });

  describe('doAction', () => {

    it('should emit action', () => {
      spyOn(component.action, 'emit');

      component.doAction();

      expect(component.action.emit).toHaveBeenCalled();
    });
  });

  describe('doRevertAction', () => {

    it('should emit action', () => {
      spyOn(component.revertAction, 'emit');

      component.doRevertAction();

      expect(component.revertAction.emit).toHaveBeenCalled();
    });
  });
});
