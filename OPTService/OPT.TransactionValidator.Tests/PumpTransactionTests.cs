using FluentAssertions;
using Forecourt.Core.Payment.Enums;
using Forecourt.Core.Pump.Models;
using Htec.Common.Abstractions.System;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using OPT.TransactionValidator.Common;
using OPT.TransactionValidator.Models;
using System;
using Xunit;

namespace OPT.TransactionValidator.Tests
{
    public class PumpTransactionTests
    {
        #region Is Continuation State
        
        [Fact]
        public void is_continuation_state_no_states_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act & Assert
            pumpTransaction.IsContinuationState().Should().BeFalse();
        }
        
        [Fact]
        public void is_continuation_has_comms_error_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.HasPumpError = true;

            // Assert
            pumpTransaction.IsContinuationState().Should().BeTrue();
        }

        [Fact]
        public void is_continuation_request_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) {LastEntered = DateTime.UtcNow});

            // Assert
            pumpTransaction.IsContinuationState().Should().BeFalse();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void is_continuation_request_state_and_approved_payment_returns_true(PaymentResult paymentResult)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            pumpTransaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Approved, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsContinuationState().Should().BeTrue();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void is_continuation_idle_state_and_approved_payment_returns_true(PaymentResult paymentResult)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            pumpTransaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Approved, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsContinuationState().Should().BeTrue();
        }

        [Fact]
        public void is_continuation_state_three_pump_no_payment_approved_states_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            // Assert
            pumpTransaction.IsContinuationState().Should().BeFalse();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void is_continuation_state_three_pump_and_payment_approved_states_returns_false(PaymentResult paymentResult)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            pumpTransaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Approved, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsContinuationState().Should().BeFalse();
        }

        #endregion

        #region Is Default State

        [Fact]
        public void is_default_state_no_state_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act & Assert
            pumpTransaction.IsDefaultState().Should().BeTrue();
        }

        [Fact]
        public void is_default_state_cleared_payment_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.Cleared, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Cleared, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsDefaultState().Should().BeTrue();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void is_default_state_approved_payment_returns_false(PaymentResult paymentResult)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState { PaymentResult = paymentResult, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsDefaultState().Should().BeFalse();
        }

        [Fact]
        public void is_default_state_three_pump_states_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            // Assert
            pumpTransaction.IsDefaultState().Should().BeFalse();
        }

        #endregion

        #region HasBeenAuthorisedWithin

        [Fact]
        public void has_been_authorised_within_no_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act & Assert
            var result = pumpTransaction.WasPaymentAuthorisedWithin(10);
            result.Should().BeFalse();
        }

        [Fact]
        public void has_been_authorised_within_with_incorrect_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.Cleared, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Cleared, WhenProcessed = DateTime.UtcNow});

            // Assert
            var result = pumpTransaction.WasPaymentAuthorisedWithin(10);
            result.Should().BeFalse();
        }

        [Theory, InlineData(275, true), InlineData(251, true), InlineData(250, false), InlineData(249, false), InlineData(0, false)]
        public void has_been_authorised_within_with_correct_state_returns_expected(int intervalInMs, bool expectedResult)
        {
            // Arrange
            var authInterval = 250;
            var utcNow = DateTime.UtcNow;
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.ApprovedAndAuthorised,
                new PumpTransactionPaymentState {PaymentResult = PaymentResult.ApprovedAndAuthorised, WhenProcessed = utcNow - TimeSpan.FromMilliseconds(authInterval)});

            // Assert
            var result = pumpTransaction.WasPaymentAuthorisedWithin(intervalInMs, utcNow);
            result.Should().Be(expectedResult);
        }

        [Fact]
        public void has_been_authorised_priorto_no_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act & Assert
            var result = pumpTransaction.WasAuthorisedPriorTo(DateTime.UtcNow);
            result.Should().BeFalse();
        }

        [Fact]
        public void has_been_authorised_priorto_with_incorrect_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.Cleared, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Cleared, WhenProcessed = DateTime.UtcNow });

            // Assert
            var result = pumpTransaction.WasAuthorisedPriorTo(DateTime.UtcNow);
            result.Should().BeFalse();
        }

        [Fact]
        public void has_been_authorised_priorto_with_incorrect_trans_state_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.ApprovedAndAuthorised, new PumpTransactionPaymentState { PaymentResult = PaymentResult.ApprovedAndAuthorised, WhenProcessed = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Closed, new PumpTransactionState(PumpState.Closed, DateTime.UtcNow));

            // Assert
            var result = pumpTransaction.WasAuthorisedPriorTo(DateTime.UtcNow);
            result.Should().BeFalse();
        }

        [Theory, InlineData(275, false), InlineData(251, false), InlineData(250, true), InlineData(249, true), InlineData(0, true)]
        public void has_been_authorised_priorto_with_correct_trans_state_returns_expected(int intervalInMs, bool expectedResult)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);
            var authInterval = 250;
            var utcNow = DateTime.UtcNow;
            var utcAuth = utcNow - TimeSpan.FromMilliseconds(authInterval);
            var utcThen = utcNow - TimeSpan.FromMilliseconds(intervalInMs);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.ApprovedAndAuthorised, new PumpTransactionPaymentState {PaymentResult = PaymentResult.ApprovedAndAuthorised, WhenProcessed = utcAuth});
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, utcAuth) {LastEntered = utcAuth});

            // Assert
            var result = pumpTransaction.WasAuthorisedPriorTo(utcThen);
            result.Should().Be(expectedResult);
        }

        #endregion

        #region HasIntermediateStates

        [Fact]
        public void has_intermediate_states_with_empty_states_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            var result = pumpTransaction.HasIntermediateStates();

            // Assert
            result.Should().BeFalse();
        }

        [Theory, InlineData(PumpState.Request), InlineData(PumpState.Idle), InlineData(PumpState.Authorise)]
        public void has_intermediate_states_with_some_incorrect_states_returns_false(PumpState state)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);
            pumpTransaction.TransactionStates.Add(state, new PumpTransactionState(state, DateTime.UtcNow));

            // Act
            var result = pumpTransaction.HasIntermediateStates();

            // Assert
            result.Should().BeFalse();
        }

        [Theory, InlineData(PumpState.Delivering), InlineData(PumpState.Finished)]
        public void has_intermediate_states_with_some_correct_states_returns_true(PumpState state)
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);
            pumpTransaction.TransactionStates.Add(state, new PumpTransactionState(state, DateTime.UtcNow));

            // Act
            var result = pumpTransaction.HasIntermediateStates();

            // Assert
            result.Should().BeTrue();
        }

        #endregion

        #region Is Default Kiosk State

        [Fact]
        public void is_default_kiosk_state_no_state_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act & Assert
            pumpTransaction.IsDefaultKioskState().Should().BeTrue();
        }

        [Fact]
        public void is_default_kiosk_state_cleared_payment_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.PaymentStates.Add(PaymentResult.Cleared, new PumpTransactionPaymentState { PaymentResult = PaymentResult.Cleared, WhenProcessed = DateTime.Now });

            // Assert
            pumpTransaction.IsDefaultKioskState().Should().BeTrue();
        }

        [Fact]
        public void is_default_kiosk_state_three_pump_states_returns_true()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            // Assert
            pumpTransaction.IsDefaultKioskState().Should().BeTrue();
        }


        [Fact]
        public void is_default_kiosk_state_four_pump_states_returns_false()
        {
            // Arrange
            var pumpTransaction = new PumpTransaction(1);

            // Act
            pumpTransaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });
            pumpTransaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()) { LastEntered = DateTime.UtcNow });

            // Assert
            pumpTransaction.IsDefaultKioskState().Should().BeFalse();
        }

        #endregion
    }
}
