using FluentMigrator;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Extensions;
using OPT.Common;
using OPT.HydraDb.Common;
using System;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to update WORKERS::BackgroundTask:Interval:SignalRBosOutWorker (used in Magical Advanced Config keys)
    /// </summary>
    [Migration(20050011, "ADO#647581 - update WORKERS::BackgroundTask:Interval:SignalRBosOutWorker (used in Magical Advanced Config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO647581_UpdateConfigDetail_BackgroundTimer_SignalRBosOutWorker : Migrationable
    {
        private const string ConfigKey = "BackgroundTask:Interval:SignalRBosOutWorker";

        private string client => ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, ConfigConstants.DefaultValueClientName, null);

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            if (client.Equals($"{PosType.GenericSignalRApi}", StringComparison.InvariantCultureIgnoreCase))
            {
                Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{ConfigKey}', @value = '00:00:05', @category = '{cfgConstants.CategoryNameWorkers}'");
            }
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            if (client.Equals($"{PosType.GenericSignalRApi}", StringComparison.InvariantCultureIgnoreCase))
            {
                // Restore to Default
                Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{ConfigKey}', @value = '', @category = '{cfgConstants.CategoryNameWorkers}'");
            }
        }
    }
}
