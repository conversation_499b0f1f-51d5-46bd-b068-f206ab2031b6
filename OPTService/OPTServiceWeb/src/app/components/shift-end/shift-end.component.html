<ng-container *ngIf="showContent">
    <form [formGroup]="shiftEndForm">
        <div class="card form-group">
            <div class="card-header">Shift end</div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="shiftEndTime">Last shift end:
                            {{shiftEndDetails.ShiftEndTime | date: 'dd/MM/yyyy HH:mm:ss'}}</label>
                    </div>
                    <div class="col-sm-12 col-md-9">
                        <button type="button" id="shiftEndTime" class="btn btn-primary mr-3"
                            (click)="performShiftEnd($event)">Process shift end</button>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label pr-3" for="logInterval">Log interval:</label>
                        <input type="time" class="" id="logInterval" formControlName="LogIntervalControl">
                    </div>
                    <div class="col-sm-12 col-md-9">
                        <button type="button" id="setLogInterval" class="btn btn-primary mr-3"
                            (click)="setLogInterval($event)">Set log interval</button>
                            <label class="col-form-label pr-3" for="logInterval">Log interval currently set to {{shiftEndDetails.LogInterval?.Hours}} hours and {{shiftEndDetails.LogInterval?.Minutes}} minutes</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="card form-group">
            <div class="card-header">Day end</div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="dayEndTime">Last day end:
                            {{shiftEndDetails.DayEndTime | date: 'dd/MM/yyyy HH:mm:ss'}}</label>
                    </div>
                    <div class="col-sm-12 col-md-9">
                        <button type="button" id="dayEndTime" class="btn btn-primary mr-3" (click)="performDayEnd($event)">Process
                            day end</button>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label pr-3" for="AutoDayEnd">Auto day end:</label>
                        <input type="time" class="" id="AutoDayEnd" formControlName="NextDayEndControl">
                    </div>
                    <div class="col-sm-12 col-md-9">
                        <button type="button" id="setNextDayEnd" class="btn btn-primary mr-3" (click)="setNextDayEnd($event)">Set
                            auto day end</button>
                        <button *ngIf="shiftEndDetails.NextDayEnd" type="button" id="clearAutoDayEnd"
                            class="btn btn-primary mr-3" (click)="clearAutoDayEnd($event)">Clear auto day end</button>
                            <label class="col-form-label pr-3" for="AutoDayEnd">{{!shiftEndDetails.NextDayEnd ?
                                'Auto day end currently not set' : 'Next day end is ' + (shiftEndDetails.NextDayEnd | date: 'dd/MM/yyyy HH:mm:ss')}}</label>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="asdaDayEndReport">Asda day end report is
                            {{shiftEndDetails.AsdaDayEndReport ? 'ON' : 'OFF'}}</label>
                    </div>
                    <div class="col-sm-12 col-md-9">
                        <button type="button" id="asdaDayEndReport" class="btn btn-primary mr-3"
                            (click)="shiftEndDetails.AsdaDayEndReport ? setAsdaDayEndReportOff($event) : setAsdaDayEndReportOn($event)">Turn 
                            {{shiftEndDetails.AsdaDayEndReport ? 'OFF' : 'ON'}}</button>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="shiftEndDetails.Unmanned" id="card-printer-details" class="card form-group" formGroupName="PrinterDetails">
            <div class="card-header pt-1 pb-1">
                <div class="row">
                    <div class="col-sm-12 col-md-10">
                        <label class="col-form-label" for="printerBtn">Printer</label>
                        <span class="badge ml-3 badge-printer-status"
                            [ngClass]="!shiftEndDetails.PrinterDetails?.Enabled ? 'badge-danger' : shiftEndDetails.PrinterDetails?.IsPrinterBusy ? 'badge-warning' : 'badge-success'">
                            {{!shiftEndDetails.PrinterDetails?.Enabled ? 'Disabled' : shiftEndDetails.PrinterDetails?.IsPrinterBusy ? 'Busy' : 'Ready'}}
                        </span>
                    </div>
                    <div class="col-sm-12 col-md-2 text-right">
                        <button type="button" id="printerBtn" class="btn btn-primary mr-3" (click)="setPrinterEnabled($event)">Turn
                            {{shiftEndDetails.PrinterDetails?.Enabled ? 'OFF' :
                            'ON'}}</button>
                    </div>
                </div>
            </div>
            <div *ngIf="shiftEndDetails.PrinterDetails?.Enabled" class="card-body">
                <app-label-text-button id="printerPortName" labelColClass="col-sm-12 col-md-3" textColClass="col-sm-12 col-md-4"
                    labelText="Port name" controlName="PrinterDetails.PortName" (action)="setPrinterPortName($event)"
                    [formGroup]="shiftEndForm" maxLength="25" [errorInAction]="printerPortNameError">
                </app-label-text-button>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="printerBaudRate">Baud rate</label>
                    </div>        
                    <div class="col-sm-12 col-md-4">
                        <select id="printerBaudRate" class="form-control" formControlName="BaudRate">
                            <option *ngFor="let baudRate of baudRates" [ngValue]="baudRate" [attr.selected]="baudRate === printerDetails?.BaudRate ? true : null">{{baudRate}}</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-md-5">
                        <button id="printerBaudRateBtn" type="button" class="btn btn-primary mr-3" (click)="setPrinterBaudRate($event)">Set baud rate</button>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="printerHandshake">Handshake</label>
                    </div>        
                    <div class="col-sm-12 col-md-4">
                        <select id="printerHandshake" class="form-control" formControlName="Handshake">
                            <option *ngFor="let handshake of handshakes" [ngValue]="handshake" [attr.selected]="handshake === printerDetails?.Handshake ? true : null">{{handshake}}</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-md-5">
                        <button id="printerHandshakeBtn" type="button" class="btn btn-primary mr-3" (click)="setPrinterHandshake($event)">Set handshake</button>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="printerStopBit">Stop bits</label>
                    </div>        
                    <div class="col-sm-12 col-md-4">
                        <select id="printerStopBit" class="form-control" formControlName="StopBits">
                            <option *ngFor="let stopBit of stopBits" [ngValue]="stopBit" [attr.selected]="stopBit === printerDetails?.StopBits ? true : null">{{stopBit}}</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-md-5">
                        <button id="printerStopBitBtn" type="button" class="btn btn-primary mr-3" (click)="setPrinterStopBits($event)">Set stop bits</button>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-12 col-md-3">
                        <label class="col-form-label" for="printerDataBit">Data bits</label>
                    </div>        
                    <div class="col-sm-12 col-md-4">
                        <select id="printerDataBit" class="form-control" formControlName="DataBits">
                            <option *ngFor="let dataBit of dataBits" [ngValue]="dataBit" [attr.selected]="dataBit === printerDetails?.DataBits ? true : null">{{dataBit}}</option>
                        </select>
                    </div>
                    <div class="col-sm-12 col-md-5">
                        <button id="printerDataBitBtn" type="button" class="btn btn-primary mr-3" (click)="setPrinterDataBits($event)">Set data bits</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</ng-container>