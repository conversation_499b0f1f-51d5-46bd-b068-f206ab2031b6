import { Component, Input } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { NgbPanelChangeEvent } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-card-contactless-aids',
  templateUrl: './card-contactless-aids.component.html',
  styleUrls: ['./card-contactless-aids.component.css']
})
export class CardContactlessAidsComponent {

  @Input() form: FormGroup;
  @Input() formArrayName: string;

  // Active sections in sub-controls
  activeCtlsCardAid: string = '';

  /**
   * The getter for the card AIDs
   */
  get cardClessAids(): FormArray {
    return this.form?.get(this.formArrayName) as FormArray;
  }

  toggleActiveCtlsCardAid(props: NgbPanelChangeEvent): void {
    this.activeCtlsCardAid = props.panelId;
  }

}
