import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-switch-label',
  templateUrl: './switch-label.component.html',
  styleUrls: ['./switch-label.component.css']
})
export class SwitchLabelComponent implements OnInit {
  public form: FormGroup;
  public control : FormControl;
  
  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() errorInActionText: string = 'Field couldn\'t be updated';
  @Input() controlName: string = '';  
  @Input() errorInAction: boolean = false;
  @Input('id') private _id: string;
  @Input() disabled: boolean = false;

  @Output() action= new EventEmitter();

  public get id(): string {
    return this._id || this.controlName || '';
  }

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
    if (this.disabled) {
      this.control.disable();
    }
  }

  doAction(event?: Event): void {
    this.action.emit(event);
  }
}
