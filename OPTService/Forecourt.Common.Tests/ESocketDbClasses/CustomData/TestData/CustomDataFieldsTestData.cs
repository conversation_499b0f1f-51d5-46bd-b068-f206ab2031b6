using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData;
using System;

namespace OPT.Common.Tests.ESocketDbClasses.CustomData.TestData
{
    public class CustomDataFieldsTestData
    {
        public Func<CustomDataFields, string> GetMember { get; set; }
        public string Expected { get; set; }

        public CustomDataFieldsTestData(Func<CustomDataFields, string> propertyFunc, string expectedValue)
        {
            GetMember = propertyFunc;
            Expected = expectedValue;
        }
    }
}