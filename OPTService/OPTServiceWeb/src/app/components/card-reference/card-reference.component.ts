import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { CardProperty } from 'src/app/core/enums/cardProperty.enum';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';

@Component({
  selector: 'app-card-reference',
  templateUrl: './card-reference.component.html',
  styleUrls: ['./card-reference.component.css']
})
export class CardReferenceComponent implements OnInit {

  @HostBinding('attr.id') externalId = null;
  @Input() cardReference: any;
  @Input() id: string;

referenceError: boolean = false;
acquirerError: boolean = false;
cardTypeError: boolean = false;
externalError: boolean = false;
removeError: boolean = false;

  cardForm = this.fb.group({
    Reference: [0,[Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuelCard: [false],
    Acquirer: ['', Validators.required],
    External: ['', Validators.required]
  });

  constructor(private advancedConfigService: AdvancedConfigService, private fb: FormBuilder,private logger: NGXLogger) { }

  /**
     * The getter method for the form
     */
  get cardFormControl() {
    return this.cardForm.controls;
  }

  /**
   * Updates a card reference
   */
  updateCardReference(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating card reference', this.cardReference);
    this.advancedConfigService.setCardProperty(CardProperty.Reference,this.cardReference).subscribe(() => {
      this.logger.debug('Card reference updated ok');
      this.referenceError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating card reference');
      this.referenceError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes a card reference
   */
  removeCardReference(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Removing card reference', this.cardReference);
    this.advancedConfigService.clearCardProperty(CardProperty.Reference,this.cardReference).subscribe(() => {
      this.logger.debug('Card reference removed ok');
      this.removeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing card reference');
      this.removeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the acquirer reference for a card reference
   */
  updateAcquirerReference(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating acquirer reference', this.cardReference);
    this.advancedConfigService.setCardProperty(CardProperty.Acquirer,this.cardReference).subscribe(() => {
      this.logger.debug('Acquirer reference udpated ok');
      this.acquirerError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating acquirer reference');
      this.acquirerError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes the acquirer reference for a card reference
   */
  removeAcquirerReference(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Removing acquirer reference', this.cardReference);
    this.cardForm.patchValue({Acquirer:''});
    this.advancedConfigService.clearCardProperty(CardProperty.Acquirer,this.cardReference).subscribe(() => {
      this.logger.debug('Acquirer reference removed ok');
      this.acquirerError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing acquirer reference');
      this.acquirerError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a new card reference
   * Updates an existing card reference setting it as fuel card or not
   */
  setFuelCard(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Setting fuel card', this.cardReference);
    this.advancedConfigService.setCardProperty(CardProperty.FuelCard,this.cardReference).subscribe(() => {
      this.logger.debug('SetFuelCard executed ok');
      this.cardTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem executing SetFuelCard');
      this.cardTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a new card reference
   * Updates an existing card reference setting with external name
   */
  setExternalName(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Setting external name', this.cardReference);
    this.advancedConfigService.setCardProperty(CardProperty.External, this.cardReference).subscribe(() => {
      this.logger.debug('SetExternalName executed ok');
      this.cardTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem executing SetExternalName');
      this.externalError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes the external name for a card reference
   */
  removeExternalName(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Removing external name', this.cardReference);
    this.cardForm.patchValue({ Acquirer: '' });
    this.advancedConfigService.clearCardProperty(CardProperty.External, this.cardReference).subscribe(() => {
      this.logger.debug('Acquirer reference removed ok');
      this.externalError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing acquirer reference');
      this.externalError = true;
      ButtonHelper.reset(event);
    });
  }

  ngOnInit(): void {
    this.cardForm.patchValue(this.cardReference);
    Object.keys(this.cardForm.controls).forEach(key => {
      this.cardForm.controls[key].valueChanges.subscribe(val => {
        this.cardReference[key] = val;
      });
    });
  }

}
