<div [id]="id" class="card form-group card-local-account-card">
    <div class="card-header d-flex justify-content-between">
        <button type="button" (click)="cardCollapse.toggle();collapseModified()" class="btn btn-link pl-0">
            <span>PAN {{card.Pan}}</span>
        </button>
        <div id="cardButtons">
            <button [id]="'removeBtn'+id" class="btn btn-primary" (click)="removeCard('remove',$event)" [disabled]="disabled" *ngIf="disabled === false">Remove</button>
            <button [id]="(form.value.Hot?'hotBtn':'clearBtn')+id" class="btn btn-primary ml-2 btn-hot-card" (click)="changeHotCard('remove',$event)" 
                *ngIf="disabled === false" [disabled]="disabled">{{form.value.Hot ? 'Hot' : 'Clear'}}</button>
            <span [id]="'error'+id" class="text-danger ml-2 pt-1" placement="right top" container="body" *ngIf="errors.remove"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </div>
    <div #cardCollapse="ngbCollapse" [(ngbCollapse)]="collapse" class="card-body">    

        <app-label-text-button [id]="'description'+id" labelColClass="col-12 col-md-2 col-xl-2"
            textColClass="col-12 col-md-6 col-xl-3" labelText="Description" controlName="Description"
            [errorInAction]="errors.description" (action)="addLocalAccountCard('description',$event)" [formGroup]="form"
            maxLength="15" [disabled]="disabled">
        </app-label-text-button>

        <app-label-text-button [id]="'discount'+id" labelColClass="col-12 col-md-2 col-xl-2"
            textColClass="col-12 col-md-6 col-xl-3" labelText="Discount" controlName="Discount"
            [errorInAction]="errors.discount" (action)="addLocalAccountCard('discount',$event)" [formGroup]="form"
            maxLength="15" [disabled]="disabled">
        </app-label-text-button>

        <app-label-text-button [id]="'restrictions1'+id" labelColClass="col-12 col-md-2 col-xl-2"
            textColClass="col-12 col-md-6 col-xl-3" labelText="Restrictions 1" controlName="Restrictions1"
            [errorInAction]="errors.restrictions1" (action)="addLocalAccountCard('restrictions1',$event)" [formGroup]="form"
            [disabled]="disabled">
        </app-label-text-button>

        <app-label-text-button [id]="'restrictions2'+id" labelColClass="col-12 col-md-2 col-xl-2"
            textColClass="col-12 col-md-6 col-xl-3" labelText="Restrictions 2" controlName="Restrictions2"
            [errorInAction]="errors.restrictions2" (action)="addLocalAccountCard('restrictions2',$event)" [formGroup]="form"
            [disabled]="disabled">
        </app-label-text-button>

        <div class="card form-group">
            <div class="card-header">
                <app-switch-label [id]="'noRestrictions'+id" labelText="Restrictions" controlName="NoRestrictions"
                    [errorInAction]="errors.noRestrictions" (action)="addLocalAccountCard('noRestrictions',$event)" [formGroup]="form"
                    [disabled]="disabled">
                </app-switch-label>
            </div>
            <div class="card-body"> 

                <app-switch-label [id]="'adBlue'+id" labelText="AdBlue" controlName="AdBlue"
                    [errorInAction]="errors.AdBlue" (action)="addLocalAccountCard('adBlue',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'avgas'+id" labelText="Avgas" controlName="Avgas"
                    [errorInAction]="errors.Avgas" (action)="addLocalAccountCard('avgas',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'diesel'+id" labelText="Diesel" controlName="Diesel"
                    [errorInAction]="errors.Diesel" (action)="addLocalAccountCard('diesel',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'gasOil'+id" labelText="GasOil" controlName="GasOil"
                    [errorInAction]="errors.GasOil" (action)="addLocalAccountCard('gasOil',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'jet'+id" labelText="Jet" controlName="Jet"
                    [errorInAction]="errors.Jet" (action)="addLocalAccountCard('Jet',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'kerosene'+id" labelText="Kerosene" controlName="Kerosene"
                    [errorInAction]="errors.Kerosene" (action)="addLocalAccountCard('kerosene',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'lpg'+id" labelText="Lpg" controlName="Lpg"
                    [errorInAction]="errors.Lpg" (action)="addLocalAccountCard('lpg',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'lrp'+id" labelText="Lrp" controlName="Lrp"
                    [errorInAction]="errors.Lrp" (action)="addLocalAccountCard('lrp',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'mogas'+id" labelText="Mogas" controlName="Mogas"
                    [errorInAction]="errors.Mogas" (action)="addLocalAccountCard('mogas',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'oil'+id" labelText="Oil" controlName="Oil"
                    [errorInAction]="errors.Oil" (action)="addLocalAccountCard('oil',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'otherMotorRelatedGoods'+id" labelText="OtherMotorRelatedGoods" controlName="OtherMotorRelatedGoods"
                    [errorInAction]="errors.OtherMotorRelatedGoods" (action)="addLocalAccountCard('otherMotorRelatedGoods',$event)" [formGroup]="form"
                    [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'shopGoods'+id" labelText="ShopGoods" controlName="ShopGoods"
                    [errorInAction]="errors.ShopGoods" (action)="addLocalAccountCard('shopGoods',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'unleaded'+id" labelText="Unleaded" controlName="Unleaded"
                    [errorInAction]="errors.Unleaded" (action)="addLocalAccountCard('unleaded',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

                <app-switch-label [id]="'valeting'+id" labelText="Valeting" controlName="Valeting"
                    [errorInAction]="errors.Valeting" (action)="addLocalAccountCard('valeting',$event)" [formGroup]="form" [disabled]="disabled">
                </app-switch-label>

            </div>
        </div>

    </div>
</div>