USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetPumpDelivered') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetPumpDelivered
END
GO

CREATE PROCEDURE [dbo].[GetPumpDelivered]
 @pump INT
AS
BEGIN
  SELECT Number, OPTPayment, Delivered, Grade, Volume, Amount, Name, Price, NetAmount, VatAmount, VatRate, TransSeqNum, Hose
  FROM PumpDelivered 
  WHERE Number = @pump
END
GO

GRANT EXECUTE ON GetPumpDelivered TO UserRole
GO
