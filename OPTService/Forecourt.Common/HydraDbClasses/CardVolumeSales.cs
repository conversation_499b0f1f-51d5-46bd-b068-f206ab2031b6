namespace OPT.Common.HydraDbClasses
{
    public class CardVolumeSales
    {
        public short CardRef { get; }
        public byte Grade { get; }
        public uint Volume { get; }
        public string ProductName { get; }

        // ReSharper disable once UnusedMember.Global
        public CardVolumeSales(int cardRef, int grade, long volume, string productName)
        {
            CardRef = (short) cardRef;
            Grade = (byte)grade;
            Volume = (uint) volume;
            ProductName = productName;
        }
    }
}