USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[FetchCardReferences]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE FetchCardReferences
END
GO

CREATE PROCEDURE [dbo].FetchCardReferences AS
BEGIN
    SELECT
   c.CardRef, c.CardProductName, c.FuelCard, a.AcquirerName,
   CAST
    (CASE WHEN
      EXISTS (SELECT * FROM CardSales s WHERE s.CardRef = c.CardRef) or
      EXISTS (SELECT * FROM DayCardSales d WHERE d.CardRef = c.CardRef) or
      EXISTS (SELECT * FROM CardVolumeSales v WHERE v.CardRef = c.CardRef) THEN 1 ELSE 0 END AS BIT) AS InUse, c.CardExternalName
   FROM CardReference c left join AcquirerReference a ON c.AcquirerRef = a.AcquirerRef
END
GO

GRANT EXECUTE ON FetchCardReferences TO UserRole
GO