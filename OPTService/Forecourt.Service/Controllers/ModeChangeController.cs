using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using OPTService;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using Unity;

namespace Forecourt.Service.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all Mode Changes requests
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "WebUi,Swagger,ThirdParty,Orbis")]
    [RoutePrefix("api/pos")]
    public class ModeChangeController : LinkedLoggableApiController<IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>>
    {
        /// <inheritdoc/>
        public ModeChangeController([Dependency(UnityConfig.MessageBrokerName)] IPosIntegratorInMode<IMessageTracking> loggable, IConfigurationManager configurationManager = null) : base(loggable as Loggable, configurationManager)
        {
        }

        /// <summary>
        /// Request that the current pump Mode be changed
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="mode">Mode requested</param>
        /// <param name="loggindReference">Logging reference</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPut, Route("pumps/{pump:range(1,256)/mode")]
        public async Task<IHttpActionResult> RequestModeChange([FromUri] byte pump, [FromBody] ModeChangeType mode, [FromUri]string loggindReference = null)
        {
            return await DoHttpActionAsync(async () => Loggable.RequestModeChange(pump, mode, loggindReference.ToMessageTracking())).ConfigureAwait(false);
        }

        /// <summary>
        /// Request the current pump Mode be changed, to whatever the Default is
        /// </summary>
        /// <param name="loggindReference">Logging reference</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPut, Route("pumps/mode/default")]
        public async Task<IHttpActionResult> RequestDefaultMode([FromUri]string loggindReference = null)
        {
            return await DoActionAsync(async () => Loggable.RequestDefaultMode(loggindReference), (r) => ResultMapper(r)).ConfigureAwait(false);
        }

        /// <summary>
        /// Request that the current pump (Time) Mode be changed
        /// </summary>
        /// <param name="mode">Mode requested</param>
        /// <param name="loggindReference">Logging reference</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPut, Route("pumps/timeMode")]
        public async Task<IHttpActionResult> RequestTimeModeChange([FromBody] TimeModeChangeType mode, [FromUri] string loggindReference = null)
        {
            return await DoActionAsync(async () => Loggable.RequestTimeModeChange(mode, loggindReference.ToMessageTracking()), (r) => ResultMapper(r)).ConfigureAwait(false);
        }
    }
}
