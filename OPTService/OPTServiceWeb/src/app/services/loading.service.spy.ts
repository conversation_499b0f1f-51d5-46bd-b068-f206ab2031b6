import { TestBed } from '@angular/core/testing';
import { LoadingService } from './loading.service';

export function LOADING_SERVICE_PROVIDER(): { provide: typeof LoadingService, useValue: any } {
  return {
    provide: LoadingService,
    useValue: jasmine.createSpyObj('LoadingService', ['showLoading', 'lastRefresh', 'showLoadingScreen', 'hideLoadingScreen', 'errorDuringLoading']),
  };
}

export function LOADING_SERVICE_SPY(): jasmine.SpyObj<LoadingService> {
  return TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
}