USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

if not exists (select * from sys.objects where object_id = OBJECT_ID('PumpGradePriceInfo') and type = 'u')
 begin
  create table dbo.PumpGradePriceInfo
   (Id bigint identity(1, 1),
    Pump int not null,
    Hose int not null,
    Grade int not null,
    Price int not null,
	CreatedDate DateTime2 NOT NULL,
	ModifiedDate DateTime2 NOT NULL,
    constraint pk_PumpGradePriceInfo primary key clustered (Id),
    constraint ix_PumpGradePriceInfoIds unique nonclustered (Pump,Hose,Grade)
    )
 end

go
