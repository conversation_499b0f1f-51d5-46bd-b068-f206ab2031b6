<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>9.0</LangVersion>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<Folder Include="Sql\Functions\" />
		<EmbeddedResource Include="Sql\Functions\*.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Sql\Data\*.sql" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Sql\Schema\" />
		<EmbeddedResource Include="Sql\Schema\*.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Sql\Stored Procedures\*.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\InstallHydraDb.sql" Link="Sql\Schema\InstallHydraDb.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\001_Upgrade_contactless_install.sql" Link="Sql\Schema\001_Upgrade_contactless_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\001_Upgrade_contactless_rollback.sql" Link="Sql\Schema\001_Upgrade_contactless_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\002_Upgrade_transaction-cycling_install.sql" Link="Sql\Schema\002_Upgrade_transaction-cycling_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\002_Upgrade_transaction-cycling_rollback.sql" Link="Sql\Schema\002_Upgrade_transaction-cycling_rollback.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\003_Upgrade_app_config_install.sql" Link="Sql\Schema\003_Upgrade_app_config_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\003_Upgrade_app_config_rollback.sql" Link="Sql\Schema\003_Upgrade_app_config_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\004_Upgrade_site_type_install.sql" Link="Sql\Schema\004_Upgrade_site_type_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\004_Upgrade_site_type_rollback.sql" Link="Sql\Schema\004_Upgrade_site_type_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\005_Upgrade_pos_type_install.sql" Link="Sql\Schema\005_Upgrade_pos_type_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\005_Upgrade_pos_type_rollback.sql" Link="Sql\Schema\005_Upgrade_pos_type_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\006_Upgrade_local_accounts_enabled_install.sql" Link="Sql\Schema\006_Upgrade_local_accounts_enabled_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\006_Upgrade_local_accounts_enabled_rollback.sql" Link="Sql\Schema\006_Upgrade_local_accounts_enabled_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\007_Upgrade_log_folders_install.sql" Link="Sql\Schema\007_Upgrade_log_folders_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\007_Upgrade_log_folders_rollback.sql" Link="Sql\Schema\007_Upgrade_log_folders_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\008_Upgrade_receipt_layout_mode_install.sql" Link="Sql\Schema\008_Upgrade_receipt_layout_mode_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\008_Upgrade_receipt_layout_mode_rollback.sql" Link="Sql\Schema\008_Upgrade_receipt_layout_mode_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\009_Upgrade_get_loyalty_terminal_install.sql" Link="Sql\Schema\009_Upgrade_get_loyalty_terminal_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\009_Upgrade_get_loyalty_terminal_rollback.sql" Link="Sql\Schema\009_Upgrade_get_loyalty_terminal_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\010_Upgrade_receipt_cycling_install.sql" Link="Sql\Schema\010_Upgrade_receipt_cycling_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\010_Upgrade_receipt_cycling_rollback.sql" Link="Sql\Schema\010_Upgrade_receipt_cycling_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\011_Upgrade_ConfigCategory_IsEditable_install.sql" Link="Sql\Schema\011_Upgrade_ConfigCategory_IsEditable_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\011_Upgrade_ConfigCategory_IsEditable_rollback.sql" Link="Sql\Schema\011_Upgrade_ConfigCategory_IsEditable_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\012_Upgrade_app_config_timer_interval_install.sql" Link="Sql\Schema\012_Upgrade_app_config_timer_interval_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\012_Upgrade_app_config_timer_interval_rollback.sql" Link="Sql\Schema\012_Upgrade_app_config_timer_interval_rollback.sql" />
		<EmbeddedResource Include="..\SQL Scripts\050_upgrade_app_config_install.sql" Link="Sql\Data\050_upgrade_app_config_install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\050_upgrade_app_config_rollback.sql" Link="Sql\Data\050_upgrade_app_config_rollback.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\ASDASettings.sql" Link="Sql\Data\ASDASettings.sql" />
		<EmbeddedResource Include="..\SQL Scripts\IndiSettings.sql" Link="Sql\Data\IndiSettings.sql" />
		<EmbeddedResource Include="..\SQL Scripts\MorrisonsSettings.sql" Link="Sql\Data\MorrisonsSettings.sql" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CSharpFunctionalExtensions" Version="[2.40.0,)" />
		<PackageReference Include="FluentMigrator" Version="[3.3.2,)" />
		<PackageReference Include="FluentMigrator.Abstractions" Version="[3.3.2,)" />
		<PackageReference Include="FluentMigrator.Extensions.SqlServer" Version="[3.3.2,)" />
		<PackageReference Include="FluentMigrator.Runner" Version="[3.3.2,)" />
		<PackageReference Include="FluentMigrator.Runner.Core" Version="[3.3.2,)" />
		<PackageReference Include="Htec.Common" Version="[3.0.0,)" />
		<PackageReference Include="Htec.DapperWrapper" Version="[2.1.0,)" />
		<PackageReference Include="Htec.Foundation" Version="[4.3.0,)" />
		<PackageReference Include="Htec.HubClient" Version="[2.0.0,)" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="[5.2.9,)" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="[5.1.1,)" ExcludeAssets="All" />
		<PackageReference Include="System.IO.Abstractions" Version="[19.2.51,)" />
		<PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="[6.0.0,)" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.GetOPTMode.sql" Link="Sql\Stored Procedures\dbo.GetOPTMode.sql" />
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.SetReceiptFooter.sql" Link="Sql\Stored Procedures\dbo.SetReceiptFooter.sql" />
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.SetReceiptHeader.sql" Link="Sql\Stored Procedures\dbo.SetReceiptHeader.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\Functions\dbo.GetReceiptHash.sql" Link="Sql\Functions\dbo.GetReceiptHash.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.AddReceipt.sql" Link="Sql\Stored Procedures\dbo.AddReceipt.sql" />
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.GetReceipt.sql" Link="Sql\Stored Procedures\dbo.GetReceipt.sql" />
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.GetReceiptForOpt.sql" Link="Sql\Stored Procedures\dbo.GetReceiptForOpt.sql" />
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.GetReceipts.sql" Link="Sql\Stored Procedures\dbo.GetReceipts.sql" />
	</ItemGroup>
	
	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\013_SLIB-160-Upgrade_ConfigurationDetail_Install.sql" Link="Sql\Data\013_SLIB-160-Upgrade_ConfigurationDetail_Install.sql" />
		<EmbeddedResource Include="..\SQL Scripts\013_SLIB-160-Upgrade_ConfigurationDetail_Rollback.sql" Link="Sql\Data\013_SLIB-160-Upgrade_ConfigurationDetail_Rollback.sql" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\SQL Scripts\Stored Procedures\dbo.SetConfigValue.sql" Link="Sql\Stored Procedures\dbo.SetConfigValue.sql" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>
	
</Project>