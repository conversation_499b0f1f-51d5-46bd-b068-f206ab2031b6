using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class DomsFuellingPointDetails
    {
        public byte Id { get; set; }
        public byte GradeId { get; set; }
        public byte PumpTotalFpId { get; set; }
        public double PumpTotalVolume { get; set; }
        public double PumpTotalCash { get; set; }
        public bool CachedStatusValid { get; set; }
        public byte Descriptor { get; set; }
        public bool FuellingDataPresent { get; set; }
        public bool InfoPresent { get; set; }
        public bool IsSupplStatusSet { get; set; }
        public bool GetPaymentControlParm { get; set; }
        public byte DataFpId { get; set; }
        public double Money { get; set; }
        public double Volume { get; set; }
        public byte ActSmId { get; set; }
        public string MainState { get; set; }
        public byte SubState { get; set; }
        public byte LockId { get; set; }
        public List<DomsGradeTotalDetails> GradeTotals { get; set; }
        public List<DomsTransactionDetails> Transactions { get; set; }
        public List<DomsGradeDetails> Grades { get; set; }
    }
}