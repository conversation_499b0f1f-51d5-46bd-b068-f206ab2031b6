using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;
using OPTService.OPTServiceClasses;
using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    /// <summary>
    /// Local account controller implementation
    /// </summary>
    public class LocalAccountController : LoggableApiController
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        public IWeb Web => _web;

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        public LocalAccountController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger) : base(logger)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        #endregion Constructor

        #region Get Data

        /// <summary>
        /// Retrieves all the local accounts information
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <response code="200">Success</response>
        /// <response code="404">Local account information not found</response>
        /// <returns>The local account information</returns>
        [HttpGet]
        [ResponseType(typeof(LocalAccounts))]
        public IHttpActionResult GetLocalAccounts(string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            var localAccountCustomers = _web.GetLocalAccountCustomers();
            if (localAccountCustomers is null)
            {
                GetLogger().Debug(FormatMessage($"Local accounts configuration not found. Returning Not Found. Reference: {reference}"));
                return NotFound();
            }

            var config = _web.GetAdvancedConfig();
            if (config is null)
            {
                GetLogger().Debug(FormatMessage($"Configuration not found. Returning Not Found. Reference: {reference}"));
                return NotFound();
            }

            var localAccounts = new LocalAccounts()
            {
                LocalAccountEnabled = config.LocalAccountsEnabled,
                LocalAccountCustomers = localAccountCustomers
            };

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);

            return Ok(localAccounts);
        }

        #endregion Get Data

        #region Set Data

        /// <summary>
        /// Sets local accounts enabled 
        /// </summary>
        /// <param name="enabled">Local accounts enabled value</param>
        /// <param name="reference">The logging reference</param>
        /// <response code="200">Success</response>
        [HttpPost]
        public IHttpActionResult SetLocalAccountsEnabled(bool enabled, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            var result = _web.SetLocalAccountsEnabled(enabled);
            if (result != null)
            {
                GetLogger().Debug(FormatMessage($"LocalAccountsEnabled could not be set: {reference} - {result}"));
                return BadRequest(result);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);            

            return Ok();
        }

        [HttpPost]
        public HttpResponseMessage RemoveLocalAccountCustomer(LocalAccountCustomerDetails details)
        {
            string customerReference = details.CustomerReference?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Remove Local Account Customer {customerReference}");
            string result = Web.RemoveLocalAccountCustomer(customerReference);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage AddLocalAccountCustomer(LocalAccountCustomerDetails details)
        {
            string customerReference = details.CustomerReference?.Trim() ?? string.Empty;
            string name = details.Name?.Trim() ?? string.Empty;
            bool transactionsAllowed = details.TransactionsAllowed;
            uint transactionLimit = details.TransactionLimit;
            bool fuelOnly = details.FuelOnly;
            bool registrationEntry = details.RegistrationEntry;
            bool mileageEntry = details.MileageEntry;
            bool prepayAccount = details.PrePayAccount;
            bool lowCreditWarning = details.LowCreditWarning;
            bool maxCreditReached = details.MaxCreditReached;
            bool pin = details.Pin;
            bool printValue = details.PrintValue;
            bool allowLoyalty = details.AllowLoyalty;
            if (transactionLimit > 0)
            {
                Web.SendChatMessage(WebName,
                    $"Add Local Account Customer, Reference {customerReference}, Name {name}" +
                    $", Transactions{(transactionsAllowed ? string.Empty : " Not")} Allowed" + $", Transaction Limit {transactionLimit}");
                string result = Web.AddLocalAccountCustomer(customerReference, name, transactionsAllowed, transactionLimit, fuelOnly,
                    registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, pin, printValue, allowLoyalty);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Transaction Limit");
            }
        }

        [HttpPost]
        public HttpResponseMessage SetLocalAccountCustomerBalance(LocalAccountCustomerDetails details)
        {
            string customerReference = details.CustomerReference?.Trim() ?? string.Empty;
            uint balance = (uint)(details.Balance * 100);
            if (balance > 0)
            {
                Web.SendChatMessage(WebName, $"Set Local Account Customer Balance, Reference {customerReference}, Balance {balance}");
                string result = Web.SetLocalAccountCustomerBalance(customerReference, balance);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Balance");
            }
        }

        [HttpPost]
        public HttpResponseMessage ClearLocalAccountCustomerBalance(LocalAccountCustomerDetails details)
        {
            string customerReference = details.CustomerReference?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Clear Local Account Customer Balance, Reference {customerReference}");
            string result = Web.SetLocalAccountCustomerBalance(customerReference, 0);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage AddLocalAccountCard(LocalAccountCardDetails details)
        {
            string customerReference = details.CustomerReference?.Trim() ?? string.Empty;
            string pan = details.Pan?.Trim() ?? string.Empty;
            string description = details.Description?.Trim() ?? string.Empty;
            float discount = details.Discount;
            bool noRestrictions = details.NoRestrictions;
            bool unleaded = details.Unleaded;
            bool diesel = details.Diesel;
            bool lpg = details.Lpg;
            bool lrp = details.Lrp;
            bool gasOil = details.GasOil;
            bool adBlue = details.AdBlue;
            bool kerosene = details.Kerosene;
            bool oil = details.Oil;
            bool avgas = details.Avgas;
            bool jet = details.Jet;
            bool mogas = details.Mogas;
            bool valeting = details.Valeting;
            bool otherMotorRelatedGoods = details.OtherMotorRelatedGoods;
            bool shopGoods = details.ShopGoods;
            if (float.IsNaN(discount) || discount < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Discount");
            }
            else
            {
                Web.SendChatMessage(WebName,
                    $"Add Local Account Card, Reference {customerReference}, PAN {pan}" +
                    $", Description {description}, Discount {discount}{(noRestrictions ? ", No Restrictions" : string.Empty)}");
                if (noRestrictions)
                {
                    string result = Web.AddLocalAccountCardWithoutRestrictions(customerReference, pan, description, discount);
                    return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
                }
                else
                {
                    string result = Web.AddLocalAccountCardWithRestrictions(customerReference, pan, description, discount, unleaded, diesel,
                        lpg, lrp, gasOil, adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods);
                    return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
                }
            }
        }

        [HttpPost]
        public HttpResponseMessage SetLocalAccountCardHot(LocalAccountCardDetails details)
        {
            string pan = details.Pan?.Trim() ?? string.Empty;
            bool hot = details.Hot;
            Web.SendChatMessage(WebName, $"Set Local Account Card {(hot ? "Hot" : "OK")}, PAN {pan}");
            string result = Web.SetLocalAccountCardHot(pan, hot);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveLocalAccountCard(LocalAccountCardDetails details)
        {
            string pan = details.Pan?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Remove Local Account Card, PAN {pan}");
            string result = Web.RemoveLocalAccountCard(pan);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion Set Data
    }
}