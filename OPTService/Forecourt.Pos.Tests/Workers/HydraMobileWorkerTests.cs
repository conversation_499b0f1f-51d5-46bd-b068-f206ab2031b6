using FluentAssertions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using NSubstitute;
using System.Collections.Generic;
using System.Xml.Linq;
using Xunit;

namespace Forecourt.Pos.Tests.Workers;

public class HydraMobileWorkerTests
{
    private readonly IHtecLogManager _logManager = Substitute.For<IHtecLogManager>();
    private readonly IHydraDb _hydraDb = Substitute.For<IHydraDb>();
    private readonly IConfigurationManager _configurationManager = Substitute.For<IConfigurationManager>();
    private readonly ITelemetryWorker _telemetryWorker = Substitute.For<ITelemetryWorker>();
    private readonly IClientConnectionThread<XElement> _connectionThread = Substitute.For<IClientConnectionThread<XElement>>();
    private readonly Htec.Hydra.Core.Pos.Interfaces.IPosIntegratorOutTransient<IMessageTracking> _posOut = Substitute.For<Htec.Hydra.Core.Pos.Interfaces.IPosIntegratorOutTransient<IMessageTracking>>();
    private readonly IJournalWorkerReceipt _journalWorker = Substitute.For<IJournalWorkerReceipt>();
    private readonly IBosIntegratorOut<IMessageTracking> _bosOut = Substitute.For<IBosIntegratorOut<IMessageTracking>>();
    private readonly IOptCollection _allOpt = Substitute.For<IOptCollection>();
    private readonly IPumpCollection _allPumps = Substitute.For<IPumpCollection>();
    private readonly IGradeHelper _gradeHelper = Substitute.For<IGradeHelper>();
    private readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOut = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();
    private readonly ITimerFactory _timerFactory = Substitute.For<ITimerFactory>();

    #region OnMessageReceived

    [Fact]
    public void on_message_received_zero_hose_returns_result_failure()
    {
        // Arrange
        var message = GetDefaultTransactionMessage(hose: 0);

        var hydraMobileWorker = CreateDefaultHydraMobileWorker();

        // Act
        var result = hydraMobileWorker.OnMessageReceived(new MessageTracking<XElement>(XElement.Parse(message)));

        // Assert
        result.IsSuccess.Should().BeFalse();
    }

    [Fact]
    public void on_message_received_zero_unit_price_returns_result_failure()
    {
        // Arrange
        var pump = Substitute.For<IPump>();
        _allPumps.TryGetPump(Arg.Any<byte>(), out Arg.Any<IPump>())
            .Returns(x =>
            {
                x[1] = pump;
                return true;
            });

        var message = GetDefaultTransactionMessage(ppl: 0m);

        var hydraMobileWorker = CreateDefaultHydraMobileWorker();

        // Act
        var result = hydraMobileWorker.OnMessageReceived(new MessageTracking<XElement>(XElement.Parse(message)));

        // Assert
        result.IsSuccess.Should().BeFalse();
    }

    [Fact]
    public void on_message_received_zero_grade_returns_result_failure()
    {
        // Arrange
        var pump = Substitute.For<IPump>();
        _allPumps.TryGetPump(Arg.Any<byte>(), out Arg.Any<IPump>())
            .Returns(x =>
            {
                x[1] = pump;
                return true;
            });

        var message = GetDefaultTransactionMessage(grade: string.Empty);

        var hydraMobileWorker = CreateDefaultHydraMobileWorker();

        // Act
        var result = hydraMobileWorker.OnMessageReceived(new MessageTracking<XElement>(XElement.Parse(message)));

        // Assert
        result.IsSuccess.Should().BeFalse();
    }

    [Fact]
    public void on_message_received_valid_message_returns_result_success()
    {
        // Arrange
        var pump = Substitute.For<IPump>();
        _allPumps.TryGetPump(Arg.Any<byte>(), out Arg.Any<IPump>())
            .Returns(x =>
            {
                x[1] = pump;
                return true;
            });

        _gradeHelper.Grades.Returns(new List<GradeName> { new(1, "Unleaded", 20) });

        var message = GetDefaultTransactionMessage();

        var hydraMobileWorker = CreateDefaultHydraMobileWorker();

        // Act
        var result = hydraMobileWorker.OnMessageReceived(new MessageTracking<XElement>(XElement.Parse(message)));

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void on_message_received_valid_message_returns_book_transaction_response()
    {
        var message = GetDefaultTransactionMessage(amount: 12.58m, volume: 8.68m);

        do_on_message_received_valid_message_returns_book_transaction_response(message);
    }

    private void do_on_message_received_valid_message_returns_book_transaction_response(string message)
    { 
        // Arrange
        var pump = Substitute.For<IPump>();
        _allPumps.TryGetPump(Arg.Any<byte>(), out Arg.Any<IPump>())
            .Returns(x =>
            {
                pump.Number.Returns(x[0]);
                x[1] = pump;
                return true;
            });

        _gradeHelper.Grades.Returns(new List<GradeName> { new(1, "Unleaded", 20) });

        var hydraMobileWorker = CreateDefaultHydraMobileWorker();

        // Act
        var result = hydraMobileWorker.OnMessageReceived(new MessageTracking<XElement>(XElement.Parse(message)));

        // Assert
        var expected = new XElement("POSResponse", 
            new XAttribute("Action", "BookTransaction"));
        result.Value.Should().BeEquivalentTo(expected);
    }

    [Fact]
    public void on_message_received_sets_pump_delivered_correctly()
    {
        // Arrange
        var message = GetDefaultTransactionMessage(pump: 1, hose: 2, grade: "1", amount: 79.44m, ppl: 136.9m, volume: 58.03m, cardCircuit: "amex", id: 6704);

        // Act
        do_on_message_received_valid_message_returns_book_transaction_response(message);

        // Assert
        _hydraDb.Received(1).SetDelivered(Arg.Any<IMessageTracking<XElement>>(), 1, 1, Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<string>(), 1369, Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<float>(), Arg.Any<int>(), 2);
    }

    #endregion

    #region Helper Methods

    private IHydraMobileWorker CreateDefaultHydraMobileWorker()
    {
        return new HydraMobileFullWorker(_logManager, _hydraDb, _configurationManager, _telemetryWorker, _connectionThread, _posOut, _journalWorker, _bosOut, _allOpt, _allPumps, _gradeHelper, _secAuthOut, _timerFactory);
    }

    private static string GetDefaultTransactionMessage(byte pump = 1, byte hose = 1, string timeStamp = "2024-08-07T05:01:30", decimal amount = 1.449m, string grade = "Unleaded", decimal ppl = 144.9m, decimal volume = 1.00m, string cardCircuit = "Visa", int id = 1)
    {
        return $"<TransactionData Pump=\"{pump}\" Hose=\"{hose}\">" +
                       $"<TimeStamp>{timeStamp}</TimeStamp>" +
                       $"<TotalAmount>{amount}</TotalAmount>" +
                       $"<SaleItem ItemID=\"{grade}\" UnitPrice=\"{ppl}\" Quantity=\"{volume}\" Amount=\"{amount}\" />" +
                       $"<CardCircuit>{cardCircuit}</CardCircuit>" +
                       "</TransactionData>";
    }

    #endregion
}