import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { OptsComponent } from './components/opts/opts.component';
import { PumpsComponent } from './components/pumps/pumps.component';
import { FuelPricesComponent } from './components/fuel-prices/fuel-prices.component';
import { ConnectionsComponent } from './components/connections/connections.component';
import { GenericOptConfigComponent } from './components/generic-opt-config/generic-opt-config.component';
import { AdvancedConfigComponent } from './components/advanced-config/advanced-config.component';
import { FileLocationsComponent } from './components/file-locations/file-locations.component';
import { ShiftEndComponent } from './components/shift-end/shift-end.component';
import { TransactionsComponent } from './components/transactions/transactions.component';
import { LocalAccountsComponent } from './components/local-accounts/local-accounts.component';
import { DomsComponent } from './components/doms/doms.component';
import { InfoMessagesComponent } from './components/info-messages/info-messages.component';
import { AboutComponent } from './components/about/about.component';

const routes: Routes = [
  {path: 'opts' , component: OptsComponent, data: {routeTitle: 'OPTs', routeId: 'opts'}},
  {path: 'pumps' , component: PumpsComponent, data: {routeTitle: 'Pumps', routeId: 'pumps'}},
  {path: 'fuelPrices' , component: FuelPricesComponent, data: {routeTitle: 'Fuel prices', routeId: 'fuel-prices'}},
  {path: 'connections' , component: ConnectionsComponent, data: {routeTitle: 'Connections', routeId: 'connections'}},
  {path: 'genericOptConfig' , component: GenericOptConfigComponent, data: {routeTitle: 'Generic OPT config', routeId: 'generic-opt-config'}},
  {path: 'advancedConfig' , component: AdvancedConfigComponent, data: {routeTitle: 'Advanced config', routeId: 'advanced-config'}},
  {path: 'fileLocations' , component: FileLocationsComponent, data: {routeTitle: 'File locations', routeId: 'file-locations'}},
  {path: 'shiftEnd' , component: ShiftEndComponent, data: {routeTitle: 'Shift end', routeId: 'shift-end'}},
  {path: 'transactions' , component: TransactionsComponent, data: {routeTitle: 'Transactions', routeId: 'transactions'}},
  {path: 'localAccounts' , component: LocalAccountsComponent, data: {routeTitle: 'Local accounts', routeId: 'local-accounts'}},
  {path: 'doms' , component: DomsComponent, data: {routeTitle: 'DOMs', routeId: 'doms'}},
  {path: 'infoMessages' , component: InfoMessagesComponent, data: {routeTitle: 'Info messages', routeId: 'info-messages'}},
  {path: 'about' , component: AboutComponent, data: {routeTitle: 'About', routeId: 'about'}},
  {path: '',  redirectTo: '/opts', pathMatch: 'full'},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
