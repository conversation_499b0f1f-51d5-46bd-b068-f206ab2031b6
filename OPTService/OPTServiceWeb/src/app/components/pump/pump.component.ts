import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { PumpService } from 'src/app/services/pump.service';

@Component({
  selector: 'app-pump',
  templateUrl: './pump.component.html',
  styleUrls: ['./pump.component.css']
})

/**
 * The Pump component class
 */
export class PumpComponent implements OnInit {

  @Input()
  pump: any;

  @Input()
  tids: any;

  @Input()
  opts: any;

  @Input()
  maxFillOverride: any;

  pumpForm = this.fb.group({
    Number: [''],
    Tid: [''],
    Closed: [''],
    ClosePending: [''],
    OptStringId: [''],
    InUse: [''],
    NozzleUp: [''],
    HasPayment: [''],
    AnprRequested: [''],
    AnprOk: [''],
    Delivering: [''],
    Delivered: [''],
    ThirdPartyPending: [''],
    PodMode: [''],
    KioskOnly: [''],
    OutsideOnly: [''],
    DefaultKioskOnly: [''],
    DefaultOutsideOnly: [''],
    MaxFillOverrideForFuelCards: [''],
    MaxFillOverrideForPaymentCards: [''],
    DefDayMode: ['']
  });

  static readonly defDayModeMixed: string = 'Mixed';
  static readonly defDayModeOutside: string = 'Outside Only';
  static readonly defDayModeKiosk: string = 'Kiosk Only';
  defDayModes: any = [PumpComponent.defDayModeMixed, PumpComponent.defDayModeOutside, PumpComponent.defDayModeKiosk];

  maxFillOvFuelCardError: boolean = false;
  maxFillOvPaymentCardError: boolean = false;

  mapToOptError: boolean = false;
  mapToTidError: boolean = false;
  setDefaultDayError: boolean = false;
  mapToOptErrorText: string = "Error mapping to OPT";
  mapToTidErrorTest: string = "Error mapping to TID";
  setDefaultDayErrorText: string = "Error setting default day mode";

  /**
   * The Pump component constructor
   * @param fb The angular reactive forms Form Builder
   * @param pumpService The Pump service
   * @param logger The logger
   */
  constructor(private fb: FormBuilder,
    private pumpService: PumpService,
    private logger: NGXLogger) { }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    this.pumpForm.patchValue(this.pump);
    Object.keys(this.pumpForm.controls).forEach(key => {
      this.pumpForm.controls[key].valueChanges.subscribe(val => {
        this.pump[key] = val;
        if ((key == 'OptStringId' || key == 'Tid') && val == 'null'){
          this.pump[key] = '';
        }
      });
    });

    // Setting the Default Day Mode string value
    this.pumpForm.controls.DefDayMode.setValue(this.pump.DefaultKioskOnly ? 
      PumpComponent.defDayModeKiosk : this.pump.DefaultOutsideOnly ? 
      PumpComponent.defDayModeOutside : PumpComponent.defDayModeMixed);
  }

  /**
   * Sets the Pump TID
   */
  setTid(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('updating TID', this.pump);
    this.pumpService.setTid(this.pump).subscribe(res => {
      this.logger.debug('TID udpated ok');
      this.mapToTidError = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating TID for pump' + this.pump.Number, error);
      this.mapToTidError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets the Pump OPT
   */
  setOpt(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('updating OPT', this.pump);
    this.pumpService.setOpt(this.pump).subscribe(res => {
      this.logger.debug('OPT udpated ok');
      this.mapToOptError = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating OPT for pump' + this.pump.Number, error);
      this.mapToOptError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets the Pump DefaultDayMode
   */
  setDefaultDayMode(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('updating Default Day Mode', this.pump);
    switch (this.pump.DefDayMode) {
      case PumpComponent.defDayModeMixed:
        this.pump.DefaultKioskOnly = false;
        this.pump.DefaultOutsideOnly = false;
        break;
      case PumpComponent.defDayModeKiosk:
        this.pump.DefaultKioskOnly = true;
        this.pump.DefaultOutsideOnly = false;
        break;
      case PumpComponent.defDayModeOutside:
        this.pump.DefaultKioskOnly = false;
        this.pump.DefaultOutsideOnly = true;
        break;
      default:
        break;
    }

    this.pumpService.setDefaultDayMode(this.pump).subscribe(res => {
      this.logger.debug('OPT udpated ok');
      this.setDefaultDayError = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating OPT for pump' + this.pump.Number, error);
      this.setDefaultDayError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Changes the Pump status to Open or Closed
   */
  changePumpStatus(event?: Event): void {
    ButtonHelper.clicked(event);
    this.pump.Closed = !(this.pump.Closed || this.pump.ClosePending);
    this.pump.ClosePending = false;
    this.logger.info(this.pump.Closed ? 'Closing ' : 'Opening ', 'Pump', this.pump);
    this.pumpService.changePumpStatus(this.pump).subscribe(res => {
      this.logger.debug(this.pump.Closed ? 'Pump Closed ' : 'Pump Open ', this.pump);
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem changing pump status' + this.pump.Number, error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Forces the closure of the pump
   */
  forceClosePump(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Forcing close of pump', this.pump);
    this.pump.ClosePending = false;
    this.pumpService.forceClosePump(this.pump).subscribe(res => {
      this.logger.debug('Pump forcelly closed ', this.pump);
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem forcing close of pump' + this.pump.Number, error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets MaxFillOverrideForFuelCards for the pump
   */
  setMaxFillOverrideForFuelCards() {
    this.logger.info('Setting MaxFillOverrideForFuelCards for the pump', this.pump);
    this.pumpService.setMaxFillOverrideForFuelCards(this.pump).subscribe(res => {
      this.logger.debug('Pump MaxFillOverrideForFuelCards set ', this.pump);
      this.maxFillOvFuelCardError = false;
    }, error => {
      this.logger.error('Problem setting MaxFillOverrideForFuelCards for the pump' + this.pump.Number, error);
      this.maxFillOvFuelCardError = true;
    });
  }

  /**
   * Sets MaxFillOverrideForPaymentCards for the pump
   */
  setMaxFillOverrideForPaymentCards() {
    this.logger.info('Setting MaxFillOverrideForPaymentCards for the pump', this.pump);
    this.pumpService.setMaxFillOverrideForPaymentCards(this.pump).subscribe(res => {
      this.logger.debug('Pump MaxFillOverrideForPaymentCards set ', this.pump);
      this.maxFillOvPaymentCardError = false;
    }, error => {
      this.logger.error('Problem setting MaxFillOverrideForPaymentCards for the pump' + this.pump.Number, error);
      this.maxFillOvPaymentCardError = true;
    });
  }

}
