import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { PumpService } from 'src/app/services/pump.service';

import { PumpComponent } from './pump.component';

describe('PumpComponent', () => {
  let component: PumpComponent;
  let fixture: ComponentFixture<PumpComponent>;
  let serviceSpy: jasmine.SpyObj<PumpService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('PumpService', ['setTid', 'setOpt', 'setDefaultDayMode', 'changePumpStatus', 'forceClosePump']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        HttpClientModule],
      providers: [
        PumpComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: PumpService, useValue: serviceObjSpy }
      ]
    });

    serviceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    component = TestBed.inject(PumpComponent);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    //Arrange
    spyOn(component.pumpForm, 'patchValue').and.stub();
    component.pump = {DefaultKioskOnly:true};

    //Act
    component.ngOnInit();

    //Assert
    expect(component.pumpForm.patchValue).toHaveBeenCalledTimes(1);
  });

  it('.setTid() should handle success response from service', () => {
    //Arrange
    serviceSpy.setTid.and.returnValue(of({}));

    //Act
    component.setTid();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setTid() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setTid.and.returnValue(throwError({ status: 400 }));
    component.pump = {Number:1};

    //Act
    component.setTid();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setOpt() should handle success response from service', () => {
    //Arrange
    serviceSpy.setOpt.and.returnValue(of({}));

    //Act
    component.setOpt();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setOpt() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setOpt.and.returnValue(throwError({ status: 400 }));
    component.pump = {Number:1};

    //Act
    component.setOpt();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setDefaultDayMode() should handle success response from service', () => {
    //Arrange
    serviceSpy.setDefaultDayMode.and.returnValue(of({}));
    component.pump = {Number:1, DefaultKioskOnly:true};

    //Act
    component.setDefaultDayMode();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setDefaultDayMode() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setDefaultDayMode.and.returnValue(throwError({ status: 400 }));
    component.pump = {Number:1, DefaultKioskOnly:true};

    //Act
    component.setDefaultDayMode();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.changePumpStatus() should handle success response from service', () => {
    //Arrange
    serviceSpy.changePumpStatus.and.returnValue(of({}));
    component.pump = {Number:1, Closed:false};

    //Act
    component.changePumpStatus();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.changePumpStatus() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.changePumpStatus.and.returnValue(throwError({ status: 400 }));
    component.pump = {Number:1};

    //Act
    component.changePumpStatus();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.forceClosePump() should handle success response from service', () => {
    //Arrange
    serviceSpy.forceClosePump.and.returnValue(of({}));
    component.pump = {Number:1, ClosePending:true};

    //Act
    component.forceClosePump();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.forceClosePump() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.forceClosePump.and.returnValue(throwError({ status: 400 }));
    component.pump = {Number:1};

    //Act
    component.forceClosePump();

    //Assert
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

});
