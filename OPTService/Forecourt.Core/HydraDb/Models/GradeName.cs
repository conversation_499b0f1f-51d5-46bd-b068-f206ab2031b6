namespace Forecourt.Core.HydraDb.Models
{
    public class GradeName
    {
        public byte Grade { get; }
        public string Name { get; }
        public float VatRate { get; }

        // ReSharper disable once UnusedMember.Global
        public GradeName(int grade, string gradeName, double vatRate)
        {
            Grade = (byte) grade;
            Name = gradeName;
            VatRate = (float) vatRate;
        }
    }
}