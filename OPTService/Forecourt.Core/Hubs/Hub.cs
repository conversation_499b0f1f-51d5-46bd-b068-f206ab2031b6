using Htec.Foundation.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace Htec.Foundation.Web.Hubs
{
    /// <summary>
    /// Extends <see cref="Microsoft.AspNet.SignalR.Hub"/> to allow connections and inbound ACK/Request messages to be easily managed
    /// </summary>
    /// <typeparam name="TMessage"></typeparam>
    /// <typeparam name="TMessageTracking"></typeparam>
    public abstract class Hub<TMessage, TMessageTracking> : Microsoft.AspNet.SignalR.Hub, IHub
        where TMessage : class
        where TMessageTracking : ILogTracking
    {
        internal Hubbable<TMessage, TMessageTracking> AsHubbable => Hubbable as Hubbable<TMessage, TMessageTracking>;

        /// <summary>
        /// The <see cref="IHubbableIn{TMessage, TMessageTracking}"/> instance that will process any ACK messages received
        /// </summary>
        protected IHubbableIn<TMessage, TMessageTracking> Hubbable { get; private set; }

        /// <inheritdoc/>
        protected Hub(IHubbableIn<TMessage, TMessageTracking> hubbable) : base()
        {
            Hubbable = hubbable ?? throw new ArgumentNullException(nameof(hubbable));
        }

        /// <summary>
        /// This will get the IP Address of the hub client.
        /// </summary>
        /// <returns></returns>
        private static string GetIpAddress(IDictionary<string, object> dict)
        {
            var ipAddress = dict.TryGetValue("server.RemoteIpAddress", out var temp) ? (string)temp : string.Empty;
            return string.IsNullOrWhiteSpace(ipAddress) ? string.Empty : IPAddress.IsLoopback(IPAddress.Parse(ipAddress)) ? "127.0.0.1" : ipAddress;
        }

        /// <inheritdoc/>
        public override Task OnConnected()
        {
            AsHubbable.AddConnection(Context.ConnectionId, GetIpAddress(Context.Request.Environment));

            return base.OnConnected();
        }

        /// <inheritdoc/>
        public override Task OnDisconnected(bool stopCalled)
        {
            AsHubbable.RemoveConnection(Context.ConnectionId);

            return base.OnDisconnected(stopCalled);
        }

        /// <inheritdoc/>
        public override Task OnReconnected()
        {
            AsHubbable.AddConnection(Context.ConnectionId, GetIpAddress(Context.Request.Environment));

            return base.OnReconnected();
        }

        /// <inheritdoc/>
        public void AcknowledgeHubMessage(string messageId)
        {
            AsHubbable.ProcessAckResponse(messageId);
        }
    }
}
