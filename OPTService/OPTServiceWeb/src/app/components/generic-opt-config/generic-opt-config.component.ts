import { <PERSON><PERSON><PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { Component, NgZone, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbPanelChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { forkJoin, of, Subscription } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { GenericOptConfig } from 'src/app/core/models/genericOptConfig.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { Constants } from 'src/app/helpers/constants';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';
import { LoadingService } from 'src/app/services/loading.service';
import { LoyaltyService } from 'src/app/services/loyalty.service';
import { OptService } from 'src/app/services/opt.service';
import { SignalRService } from 'src/app/services/signal-r.service';

@Component({
  selector: 'app-generic-opt-config',
  templateUrl: './generic-opt-config.component.html',
  styleUrls: ['./generic-opt-config.component.css']
})
export class GenericOptConfigComponent implements OnInit {

  showContent: boolean = false;
  optDataSubscription: Subscription;

  // API call fail vars
  updateServiceAddressError: boolean = false;
  addEsocketError: boolean = false;
  addTariffMappingError: boolean = false;
  addDiscountCardError: boolean = false;
  addWashingProgramError: boolean = false;
  addPredefinedAmountError: boolean = false;
  removeWashingProgramError: boolean = false;
  layoutModeError: boolean = false;
  reloadOptConfigurationError: boolean = false;
  batchError: boolean = false;
  contactlessIsEnabledError: boolean = false;
  contactlessCardPreAuthLimitError: boolean = false;
  contactlessDevicePreAuthLimitError: boolean = false;
  contactlessTtqError: boolean = false;
  contactlessSingleButtonError: boolean = false;
  morrisonsLoyaltyError: boolean = false;
  receiptReprintAvailabilityError: boolean = false;
  receiptMaxCountError: boolean = false;

  genericOptConfigFormError: boolean = false;

  // Section collapse vars
  endpointsIsCollapsed: boolean = true;
  eSocketConfigIsCollapsed: boolean = true;
  termProcCategoryIsCollapsed: boolean = true;
  cardAidsIsCollapsed: boolean = true;
  cardContactlessAidsIsCollapsed: boolean = true;
  cardCapksIsCollapsed: boolean = true;
  fuelCardsIsCollapsed: boolean = true;
  tariffMappingsIsCollapsed: boolean = true;
  discountCardsIsCollapsed: boolean = true;
  loyaltyAvailabilityIsCollapsed: boolean = true;
  loyaltySchemesIsCollapsed: boolean = true;
  washesIsCollapsed: boolean = true;
  predefinedAmountsIsCollapsed: boolean = true;
  receiptLayoutIsCollapsed: boolean = true;
  contactlessIsCollapsed: boolean = true;

  // Active IDs in accordion controls
  activeDiscountCard: string = '';
  activeLoyaltyScheme: string = '';

  // Active sections in sub-controls
  activeLoyaltySection: string = '';
  activeWash: string = '';

  // IsConfigBatch var
  isConfigBatch: boolean = false;

  // Forms
  // The main form for generic OPT config data
  genericOptConfigForm: FormGroup;
  // The form for adding a new esocket endpoint
  addEsocketForm: FormGroup;
  // The form for adding a new tariff mapping
  addTariffMappingForm: FormGroup;
  // The for for adding a new discount card
  addDiscountCardForm: FormGroup;
  // The form for adding a new predefined amount
  addPredefinedAmountForm: FormGroup;
  // The form for adding a new washing program
  addWashingProgramForm: FormGroup;

  /**
   * The constructor
   * @param fb 
   * @param signalRService 
   * @param zone 
   * @param optService 
   * @param loyaltyService 
   * @param logger 
   * @param modalService 
   */
  constructor(
    private fb: FormBuilder,
    private zone: NgZone,
    private optService: OptService,
    private advancedConfigService: AdvancedConfigService,
    private loyaltyService: LoyaltyService,
    private logger: NGXLogger,
    private modalService: NgbModal,
    private currencyPipe: CurrencyPipe,
    private decimalPipe: DecimalPipe,
    private loadingService: LoadingService,
    private signalRService: SignalRService,
  ) {

    //Create the main form
    this.createGenericOptConfigForm();

    // Generic OPT signalR message
    this.optDataSubscription = this.signalRService.getGenericOptConfigSignalRMessage().subscribe((data) => {
      this.zone.run(() => {
        const showLoading = data === Constants.SignalRRefreshItemId;
        this.refreshData(showLoading);
      });
    });
  }

  /**
   * Creates or resets the main form
   */
  createGenericOptConfigForm(): void {
    this.genericOptConfigForm = this.fb.group({
      ServiceAddress: ['', [Validators.required, Validators.pattern(Constants.IpRegxPattern)]],
      EsocketEndPoints: this.fb.array([]),
      Timestamp: [''],
      TermCategory: [''],
      ProcCategory: [''],
      CardAids: this.fb.array([]),
      CardClessAids: this.fb.array([]),
      CoockedCardCapks: this.fb.array([]),
      FuelCards: this.fb.array([]),
      TariffMappings: this.fb.array([]),
      DiscountCards: this.fb.array([]),
      MorrisonLoyaltyAvailable: [false],
      Loyalty: this.fb.array([]),
      Washes: this.fb.array([]),
      PredefinedAmounts: this.fb.array([]),
      ReceiptLayoutMode: this.fb.group({
        Mode: [0],
        ReceiptReprintAvailability: [0, [Validators.required,Validators.pattern(Constants.PositiveNumberRegxPattern),Validators.maxLength(10)]],
        ReceiptMaxCount:[0, [Validators.required,Validators.pattern(Constants.PositiveNumberRegxPattern),Validators.maxLength(10)]],
        ReceiptHeaders: this.fb.array([]),
        ReceiptFooters: this.fb.array([]),
      }),
      ContactlessDetails: this.fb.group({
        IsEnabled: [false],
        FormattedCardPreAuthLimit: ['', [Validators.pattern(Constants.DecimalGreaterThanZero), Validators.min(1), Validators.required]],
        FormattedDevicePreAuthLimit: ['', [Validators.pattern(Constants.DecimalGreaterThanZero), Validators.min(1), Validators.required]],
        Ttq: ['', [Validators.pattern(Constants.HexNumberRegxPattern), Validators.minLength(8), Validators.maxLength(8), Validators.required]],
        ShowSingleButton: [false]
      }),
    });
  }

  /**
   * Creates or resets the form for adding a new esocket
   */
  createAddEsocketForm(): void {
    this.addEsocketForm = this.fb.group({
      IpAddress: ['', [Validators.required, Validators.pattern(Constants.IpRegxPattern)]],
      Port: [0, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10), Validators.min(0), Validators.max(65535)]]
    });
  }

  /**
   * Creates or resets the form for adding a new tariff mapping
   */
  createAddTariffMappingForm(): void {
    this.addTariffMappingForm = this.fb.group({
      Grade: [0, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10)]],
      ProductCode: ['', [Validators.required, Validators.pattern(Constants.AlphaNumericPattern)]],
      FuelCardsOnly: [false]
    });
  }

  /**
   * Creates or resets the form for adding a new discount card
   */
  createAddDiscountCardForm(): void {
    this.addDiscountCardForm = this.fb.group({
      Iin: ['', [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern)]],
      Name: ['', Validators.required],
      Type: ['', Validators.required],
      Value: [0, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10), Validators.min(0)]],
      Grade: [0, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10)]]
    });
  }

  /**
   * Creates or resets the form for adding a new washing program
   */
  createAddWashingProgramForm(): void {
    this.addWashingProgramForm = this.fb.group({
      ProgramId: [0, [Validators.pattern(Constants.PositiveNumberRegxPattern)]],
      ProductCode: ['', [Validators.pattern(Constants.AlphaNumericPattern)]],
      Description: [''],
      Price: [0, [Validators.pattern(Constants.DecimalRegxPattern), Validators.min(0)]],
      VatRate: [0, [Validators.pattern(Constants.DecimalRegxPattern), Validators.min(0), Validators.max(100)]],
      Category: [0, [Validators.pattern(Constants.NumberRegxPattern)]],
      Subcategory: [0, [Validators.pattern(Constants.NumberRegxPattern)]]
    })
  }

  /**
   * Creates or resets the form for adding a new predefined amount
   */
  createAddPredefinedAmountForm(): void {
    this.addPredefinedAmountForm = this.fb.group({
      Amount: [1, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10), Validators.min(1)]]
    });
  }

  /**
   * The getter for the GenericOptConfigForm
   */
  get genericOptConfigFormControl() {
    return this.genericOptConfigForm.controls;
  }

  /**
   * The getter for the eSocketEndPoints
   */
  get esocketEndPoints() {
    return this.genericOptConfigForm.get('EsocketEndPoints') as FormArray;
  }

  /**
   * The getter for the AddEsocketForm
   */
  get addEsocketFormControl() {
    return this.addEsocketForm.controls;
  }

  /**
   * The getter for the card AIDs
   */
  get cardAids() {
    return this.genericOptConfigForm.get('CardAids') as FormArray;
  }

  /**
     * The getter for the card contactless AIDs
     */
  get cardClessAids() {
    return this.genericOptConfigForm.get('CardClessAids') as FormArray;
  }

  /**
   * The getter for the coocked card Capks
   */
  get coockedCardCapks() {
    return this.genericOptConfigForm.get('CoockedCardCapks') as FormArray;
  }

  /**
   * The getter for the fuel cards
   */
  get fuelCards() {
    return this.genericOptConfigForm.get('FuelCards') as FormArray;
  }

  /**
   * The getter for the tariff mappings
   */
  get tariffMappings() {
    return this.genericOptConfigForm.get('TariffMappings') as FormArray;
  }

  /**
   * The getter for the AddTariffMappingForm
   */
  get addMapFormControl() {
    return this.addTariffMappingForm.controls;
  }

  /**
   * The getter for the discount cards
   */
  get discountCards() {
    return this.genericOptConfigForm.get('DiscountCards') as FormArray;
  }

  /**
   * The getter for the AddDiscountCardForm
   */
  get addDiscountCardFormControl() {
    return this.addDiscountCardForm.controls;
  }

  /**
   * The getter for the loyalty schemes
   */
  get loyaltySchemes() {
    return this.genericOptConfigForm.get('Loyalty') as FormArray;
  }

  /**
   * The getter for the form for adding washing program
   */
  get addWashingProgramFormControl() {
    return this.addWashingProgramForm.controls;
  }

  /**
   * The getter for the washes array in main form
   */
  get washes() {
    return this.genericOptConfigForm.get('Washes') as FormArray;
  }

  /**
   * The getter for the PredefinedAmounts in main form
   */
  get predefinedAmounts() {
    return this.genericOptConfigForm.get('PredefinedAmounts') as FormArray;
  }

  /**
   * The getter for the Contactless in main form
   */
  get contactlessDetails() {
    return this.genericOptConfigForm.get('ContactlessDetails') as FormGroup;
  }

  /**
   * The getter for the form for adding a new predefined amount
   */
  get addPredefinedAmountFormControl() {
    return this.addPredefinedAmountForm.controls;
  }

  /**
   * The getter for the ReceiptHeaders in main form
   */
  get receiptHeaders(): FormArray {
    return this.genericOptConfigForm.controls.ReceiptLayoutMode.get('ReceiptHeaders') as FormArray;
  }

  /**
   * The getter for the ReceiptFooters in main form
   */
    get receiptFooters(): FormArray {
    return this.genericOptConfigForm.controls.ReceiptLayoutMode.get('ReceiptFooters') as FormArray;
  }

  /**
   * Updates the service address value
   */
  updateServiceAddress(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var serviceAddress = this.genericOptConfigForm.get('ServiceAddress').value;
    //Call the API
    this.logger.info('Updating service address', serviceAddress);
    this.optService.setGenericServiceAdress(serviceAddress).subscribe(() => {
      this.logger.debug('Service address updated ok');
      this.updateServiceAddressError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating service address');
      this.updateServiceAddressError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds an e-socket endpoint
   */
  onAddEsocketFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var ipAddress = this.addEsocketForm.get('IpAddress').value;
    var port = this.addEsocketForm.get('Port').value;
    //Call the API
    this.logger.info('Adding esocket endpoint', ipAddress, port);
    this.optService.addEsocketEndpoint(ipAddress, port).subscribe(() => {
      this.logger.debug('Esocket endpoint added ok');
      this.addEsocketError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding esocket endpoint');
      this.addEsocketError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes an e-socket endpoint
   * @param index 
   */
  removeEsocket(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de esocket data
    var ipAddress = this.esocketEndPoints.at(index).get('IpAddress').value;
    var port = this.esocketEndPoints.at(index).get('Port').value;
    //Call the API
    this.logger.info('Removing esocket endpoint', ipAddress, port);
    this.optService.removeEsocketEndpoint(ipAddress, port).subscribe(() => {
      this.logger.debug('Esocket endpoint removed ok');
      //Mark form group as valid
      this.esocketEndPoints.removeAt(index);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing esocket endpoint');
      //Mark form group as invalid
      this.esocketEndPoints.at(index).setErrors({ 'incorrect': true });
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a new tariff mapping
   */
  onAddTariffMappingFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de tariff mapping data
    var grade = this.addTariffMappingForm.get('Grade').value;
    var productCode = this.addTariffMappingForm.get('ProductCode').value;
    var fuelCardsOnly = this.addTariffMappingForm.get('FuelCardsOnly').value;
    //Call the API
    this.logger.info('Adding tariff mapping', grade, productCode, fuelCardsOnly);
    this.optService.addTariffMapping(grade, productCode, fuelCardsOnly).subscribe(() => {
      this.logger.debug('Tariff mapping added ok');
      this.addTariffMappingError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding tariff mapping');
      this.addTariffMappingError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * 
   * @param index Removes a tariff mapping
   */
  removeTariffMapping(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de tariff mapping data
    var grade = this.tariffMappings.at(index).get('Grade').value;
    var productCode = this.tariffMappings.at(index).get('ProductCode').value;
    //Call the API
    this.logger.info('Removing tariff mapping', grade, productCode);
    this.optService.removeTariffMapping(grade, productCode).subscribe(() => {
      this.logger.debug('Tariff mapping removed ok');
      //Mark form group as valid
      this.tariffMappings.removeAt(index);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing tariff mapping');
      //Mark form group as invalid
      this.tariffMappings.at(index).setErrors({ 'incorrect': true });
      ButtonHelper.reset(event);
    });
  }

  /**
   * Set the tariff mapping fuel cards only
   */
  setTariffMappingFuelCardsOnly(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var grade = this.tariffMappings.at(index).get('Grade').value;
    var fuelCardsOnly = this.tariffMappings.at(index).get('FuelCardsOnly').value;
    //Call the API
    this.logger.info('Settings tariff mapping fuel cards only', grade, fuelCardsOnly);
    this.optService.setTariffMappingFuelCardsOnly(grade, fuelCardsOnly).subscribe(() => {
      this.logger.debug('Tariff mapping fuel cards only set ok');
      //Mark form group as valid
      this.tariffMappings.at(index).setErrors(null);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting tariff mapping fuel cards only');
      //Mark form group as invalid
      this.tariffMappings.at(index).setErrors({ 'incorrect': true });
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a new discount card
   */
  onAddDiscountCardFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de discount card data
    var iin = this.addDiscountCardForm.get('Iin').value;
    var name = this.addDiscountCardForm.get('Name').value;
    var type = this.addDiscountCardForm.get('Type').value;
    var value = this.addDiscountCardForm.get('Value').value;
    var grade = this.addDiscountCardForm.get('Grade').value;

    //Call the API
    this.logger.info('Adding discount card', iin, name, type, value, grade);
    this.optService.addDiscountCard(iin, name, type, value, grade).subscribe(() => {
      this.logger.debug('Discount card added ok');
      this.addDiscountCardError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding discount card');
      this.addDiscountCardError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * 
   * @param index Removes a discount card
   */
  removeDiscountCard(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de tariff mapping data
    var iin = this.discountCards.at(index).get('Iin').value;

    //Call the API
    this.logger.info('Removing discount card', iin);
    this.optService.removeDiscountCard(iin).subscribe(() => {
      this.logger.debug('Discount card removed ok');
      //Mark form group as valid
      this.discountCards.removeAt(index);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing discount card');
      //Mark form group as invalid
      this.discountCards.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  updateDiscountCard(index: number, fieldName: string, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de discount card data
    var iin = this.discountCards.at(index).get('Iin').value;
    var name = this.discountCards.at(index).get('Name').value;
    var type = this.discountCards.at(index).get('Type').value;
    var value = this.discountCards.at(index).get('Value').value;
    var grade = this.discountCards.at(index).get('Grade').value;

    //Call the API
    this.logger.info('Updating discount card', iin, name, type, value, grade);
    this.optService.addDiscountCard(iin, name, type, value, grade).subscribe(() => {
      this.logger.debug('Discount card updated ok');
      this.addDiscountCardError = false;
      this.discountCards.at(index).get(fieldName + 'Error').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating discount card');
      this.discountCards.at(index).get(fieldName + 'Error').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Adds a new washing program
   */
  onAddWashingProgramFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de discount card data
    var programId = this.addWashingProgramForm.get('ProgramId').value;
    var productCode = this.addWashingProgramForm.get('ProductCode').value;
    var description = this.addWashingProgramForm.get('Description').value;
    var price = this.addWashingProgramForm.get('Price').value;
    var vatRate = this.addWashingProgramForm.get('VatRate').value;
    var category = this.addWashingProgramForm.get('Category').value;
    var subcategory = this.addWashingProgramForm.get('Subcategory').value;

    //Call the API
    this.logger.info('Adding washing program', programId, productCode, description, price, vatRate, category, subcategory);
    this.optService.addWash(programId, productCode, description, price, vatRate, category, subcategory).subscribe(() => {
      this.logger.debug('Washing program added ok');
      this.addWashingProgramError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding washing program');
      this.addWashingProgramError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Removes an existing washing program
   * @param index 
   */
  removeWashingProgram(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de discount card data
    var programId = this.washes.at(index).get('ProgramId').value;
    var productCode = this.washes.at(index).get('ProductCode').value;
    var description = this.washes.at(index).get('Description').value;
    var price = this.washes.at(index).get('Price').value;
    var vatRate = this.washes.at(index).get('VatRate').value;
    var category = this.washes.at(index).get('Category').value;
    var subcategory = this.washes.at(index).get('Subcategory').value;

    //Call the API
    this.logger.info('Removing washing program', programId, productCode, description, price, vatRate, category, subcategory);
    this.optService.removeWash(programId, productCode, description, price, vatRate, category, subcategory).subscribe(() => {
      this.logger.debug('Washing program removed ok');
      this.washes.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing washing program');
      this.washes.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
     * Adds a new predefined amount
     */
  onAddPredefinedAmountFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de discount card data
    var amount = this.addPredefinedAmountForm.get('Amount').value;

    //Call the API
    this.logger.info('Adding predefined amount', amount);
    this.optService.addPredefinedAmount(amount).subscribe(() => {
      this.logger.debug('Predefined amount added ok');
      this.addPredefinedAmountError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding predefined amount');
      this.addPredefinedAmountError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
     * Removes an existing predefined amount
     * @param index 
     */
  removePredefinedAmount(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var amount = this.predefinedAmounts.at(index).get('Amount').value;

    //Call the API
    this.logger.info('Removing predefined amount', amount);
    this.optService.removePredefinedAmount(amount).subscribe(() => {
      this.logger.debug('Predefined amount removed ok');
      this.predefinedAmounts.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing predefined amount');
      this.predefinedAmounts.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for layout mode
   */
  updateLayoutMode(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var mode = this.genericOptConfigForm.get('ReceiptLayoutMode').get('Mode').value;

    //Call the API
    this.logger.info('Updating receipt layout mode', mode);
    this.optService.setReceiptLayoutMode(mode).subscribe(() => {
      this.logger.debug('Receipt layout mode set ok');
      this.layoutModeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting receipt layout mode');
      this.layoutModeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the receipt reprint timeout
   */
  updateReceiptReprintAvailability(event?: Event): void {
    ButtonHelper.clicked(event);
    // Retrieve the data
    var receiptTimeout = this.genericOptConfigForm.get('ReceiptLayoutMode').get('ReceiptReprintAvailability').value;
    // Call the API
    this.logger.info('Updating receipt timeout', receiptTimeout);
    this.optService.setReceiptReprintAvailability(receiptTimeout).subscribe(() => {
      this.logger.debug('Receipt timeout updated ok');
      this.receiptReprintAvailabilityError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating receipt timeout');
      this.receiptReprintAvailabilityError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the receipt maximum count
   */
  updateReceiptMaxCount(event?: Event): void {
    ButtonHelper.clicked(event);
    // Retrieve the data
    var receiptMaxCount = this.genericOptConfigForm.get('ReceiptLayoutMode').get('ReceiptMaxCount').value;
    // Call the API
    this.logger.info('Updating receipt max count', receiptMaxCount);
    this.optService.setReceiptMaxCount(receiptMaxCount).subscribe(() => {
      this.logger.debug('Receipt max count updated ok');
      this.receiptMaxCountError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating receipt max count');
      this.receiptMaxCountError = true;
      ButtonHelper.reset(event);
    });
 }

   /**
   * Updates the value for contactless allowed
   */
  updateContactlessAllowed(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var isEnabled = this.genericOptConfigForm.get('ContactlessDetails').get('IsEnabled').value;

    //Call the API
    this.logger.info('Updating contactless allowed', isEnabled);
    this.optService.setContactlessAllowed(isEnabled).subscribe(() => {
      this.logger.debug('Contactless allowed set ok');
      this.contactlessIsEnabledError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting contactless allowed');
      this.contactlessIsEnabledError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for contactless card preauth limit
   */
  updateContactlessCardPreAuthLimit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var cardPreAuthLimit = (this.genericOptConfigForm.get('ContactlessDetails').get('FormattedCardPreAuthLimit').value)?.replaceAll(',', '') * 100;

    //Call the API
    this.logger.info('Updating contactless card preauth limit', cardPreAuthLimit);
    this.optService.setContactlessCardPreAuth(cardPreAuthLimit).subscribe(() => {
      this.logger.debug('Contactless card preauth limit set ok');
      this.contactlessCardPreAuthLimitError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting contactless card preauth limit');
      this.contactlessCardPreAuthLimitError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for contactless Device preauth limit
   */
  updateContactlessDevicePreAuthLimit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var devicePreAuthLimit = (this.genericOptConfigForm.get('ContactlessDetails').get('FormattedDevicePreAuthLimit').value)?.replaceAll(',', '') * 100;

    //Call the API
    this.logger.info('Updating contactless device preauth limit', devicePreAuthLimit);
    this.optService.setContactlessDevicePreAuth(devicePreAuthLimit).subscribe(() => {
      this.logger.debug('Contactless device preauth limit set ok');
      this.contactlessDevicePreAuthLimitError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting contactless device preauth limit');
      this.contactlessDevicePreAuthLimitError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for contactless TTQ
   */
  updateContactlessTtq(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var ttq = this.genericOptConfigForm.get('ContactlessDetails').get('Ttq').value;

    //Call the API
    this.logger.info('Updating contactless TTQ', ttq);
    this.optService.setContactlessTtq(ttq).subscribe(() => {
      this.logger.debug('Contactless TTQ set ok');
      this.contactlessTtqError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting contactless TTQ');
      this.contactlessTtqError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
* Updates the value for contactless allowed
*/
  updateContactlessSingleButton(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var showSingleButton = this.genericOptConfigForm.get('ContactlessDetails').get('ShowSingleButton').value;

    //Call the API
    this.logger.info('Updating contactless single button', showSingleButton);
    this.optService.setContactlessSingleButton(showSingleButton).subscribe(() => {
      this.logger.debug('Contactless single button set ok');
      this.contactlessSingleButtonError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting contactless single button');
      this.contactlessSingleButtonError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Update loyalty scheme
   * @param name The name of the loyalty scheme
   */
  updateLoyalty(name: string, event?: Event): void {
    ButtonHelper.clicked(event);
    if (this.genericOptConfigForm.get('MorrisonLoyaltyAvailable').value) {
      this.logger.info('Adding loyalty scheme', name);
      this.loyaltyService.addLoyalty(name).subscribe(() => {
        this.logger.debug('Loyalty scheme added ok');
        this.morrisonsLoyaltyError = false;
        ButtonHelper.reset(event);
      }, () => {
        this.logger.error('Problem adding loyalty scheme');
        this.morrisonsLoyaltyError = true;
        ButtonHelper.reset(event);
      });
    }
    else {
      this.logger.info('Deleting loyalty scheme', name);
      this.loyaltyService.deleteLoyalty(name).subscribe(() => {
        this.logger.debug('Loyalty scheme deleted ok');
        this.morrisonsLoyaltyError = false;
        ButtonHelper.reset(event);
      }, () => {
        this.logger.error('Problem deleting loyalty scheme');
        this.morrisonsLoyaltyError = true;
        ButtonHelper.reset(event);
      });
    }
  }

  /**

   * Reloads OPT configuration
   */
  reloadOptConfiguration(event?: Event): void {
    ButtonHelper.clicked(event);
    //Call the API
    this.logger.info('Reload OPT configuration');
    this.optService.reloadOptConfiguration().subscribe(() => {
      this.logger.debug('OPT configuration reloaded ok');
      this.reloadOptConfigurationError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem reloading OPT configuration');
      this.reloadOptConfigurationError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Toggle the is config batch value
   */
  toggleIsConfigBatch(event?: Event): void {
    ButtonHelper.clicked(event);
    if (this.isConfigBatch) {
      this.logger.info('Stopping config batch');
      this.optService.stopConfigBatch().subscribe(() => {
        this.logger.debug('Config batch stopped ok');
        this.batchError = false;
        ButtonHelper.reset(event);
      }, () => {
        this.logger.error('Problem stopping config batch');
        this.batchError = true;
        ButtonHelper.reset(event);
      });
    } else {
      this.logger.info('Starting config batch');
      this.optService.startConfigBatch().subscribe(() => {
        this.logger.debug('Config batch started ok');
        this.batchError = false;
        ButtonHelper.reset(event);
      }, () => {
        this.logger.error('Problem starting config batch');
        this.batchError = true;
        ButtonHelper.reset(event);
      });
    }

  }

  /**
   * Resets the addEsocketForm and opens a modal windows containing the form
   * @param content
   */
  openAddEsocketForm(content: any): void {
    this.createAddEsocketForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-esocket' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addTariffMappingForm and opens a modal windows containing the form
   * @param content
   */
  openAddTariffMappingForm(content: any): void {
    this.createAddTariffMappingForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-product-code-mapping' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addDiscountCardForm and opens a modal windows containing the form
   * @param content 
   */
  openAddDiscountCardForm(content: any): void {
    this.createAddDiscountCardForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-discount-card' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addWashingProgramForm and opens a modal window containing the form
   * @param content 
   */
  openAddWashingProgram(content: any): void {
    this.createAddWashingProgramForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-wash' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addPredefinedAmountForm and opens a modal windows containing the form
   * @param content 
   */
  openAddPredefinedAmountForm(content: any): void {
    this.createAddPredefinedAmountForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-predefined-amount' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param showLoading It displays loading
   * @param forceReload It forces a call to the API to get fresh data
   */
  refreshData(showLoading: boolean = true): void {
    if (showLoading) {
      //Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }

    let isConfigBatchObserver = this.advancedConfigService.getIsConfigBatch().pipe(
      tap(() => {
        this.batchError = false;
      }),
      catchError(() => {
        this.logger.error('Problem getting is config batch');
        this.loadingService.errorDuringLoading();
        this.batchError = true;
        return of(undefined);
      })
    );

    let genericOptConfigObserver = this.optService.getGenericOptConfig(true).pipe(
      tap(() => {
        this.genericOptConfigFormError = false;
      }),
      catchError(() => {
        this.logger.error('Problem getting generic OPT configuration');
        this.loadingService.errorDuringLoading();
        this.genericOptConfigFormError = true;
        return of(undefined);
      })
    );

    forkJoin([isConfigBatchObserver, genericOptConfigObserver]).subscribe(
      results => {
        //isConfigBatchObserver success = results[0]!==undefined
        if (results[0] !== undefined) {
          this.logger.debug('IsConfigBatch', results[0]);
          this.isConfigBatch = results[0].IsConfigBatch;
        }

        //genericOptConfigObserver success = results[1]!==undefined
        if (results[1] !== undefined) {
          this.logger.debug('Generic OPT configuration', results[1]);

          //Prepare array of esocket
          this.esocketEndPoints.clear();
          results[1].EsocketEndPoints?.forEach(() => {
            this.esocketEndPoints.push(this.fb.group({
              IpAddress: [''],
              Port: [0]
            }))
          });

          //Prepare array of card AIDs
          this.cardAids.clear();
          results[1].CardAids?.forEach(() => {
            this.cardAids.push(this.fb.group({
              Aid: [''],
              AppVerTerm: [''],
              Ddol: [''],
              EmvMaxTarget: [''],
              EmvTarget: [''],
              EmvThreshold: [''],
              FloorLimit: [''],
              PartialMatch: [''],
              TacDefault: [''],
              TacDenial: [''],
              TacOnline: [''],
              Tdol: ['']
            }))
          });

          //Prepare array of card ctless AIDs
          this.cardClessAids.clear();
          results[1].CardClessAids?.forEach(() => {
            this.cardClessAids.push(this.fb.group({
              Aid: [''],
              AppVerTerm: [''],
              TransLimit: [''],
              FloorLimit: [''],
              CvmLimit: [''],
              OdcvmLimit: [''],
              TermAddCapabilities: [''],
              TermCapabilitiesCvm: [''],
              TermCapabilitiesNoCvm: [''],
              TermRiskData: [''],
              Udol: [''],
              TacDefault: [''],
              TacDenial: [''],
              TacOnline: ['']
            }))
          });

          //Prepare array of card cAPKs
          this.coockedCardCapks.clear();
          var targetObject: Array<any> = [];
          results[1].CardCapks?.forEach(value => {
            const newCardCapkIndex = {
              TheIndex: value.TheIndex,
              Modulus: value.Modulus,
              Exponent: value.Exponent,
              Checksum: value.Checksum,
              ExpiryDate: value.ExpiryDate
            }
            //Search the rid in the target array
            var capkIndex = targetObject.findIndex(x => x.Rid === value.Rid);
            if (capkIndex < 0) {
              targetObject.push({
                Rid: value.Rid,
                Indexes: [newCardCapkIndex]
              });
            }
            else {
              targetObject[capkIndex].Indexes.push(newCardCapkIndex);
            }
          });

          targetObject.forEach(rid => {
            var ridForm = this.fb.group({
              Rid: [''],
              Indexes: this.fb.array([])
            })

            var ridFormIndexes = ridForm.get('Indexes') as FormArray;
            rid.Indexes.forEach(index => {
              ridFormIndexes.push(this.fb.group({
                TheIndex: [''],
                Modulus: [''],
                Exponent: [''],
                Checksum: [''],
                ExpiryDate: ['']
              }));
            });

            this.coockedCardCapks.push(ridForm);
          });
          results[1].CoockedCardCapks = targetObject;

          //Prepare fuel cards array
          this.fuelCards.clear();
          results[1].FuelCards?.forEach(value => {
            this.fuelCards.push(this.fb.group({
              IinStart: [''],
              IinEnd: [''],
              OnlinePin: [''],
              FullValue: ['IIN ' + value.IinStart + (value.IinEnd !== value.IinStart ? ' to ' + value.IinEnd : '') + (value.OnlinePin !== '0' ? ' (Online PIN ' + value.OnlinePin + ')' : '')]
            }))
          });

          //Prepare tariff mappings array
          this.tariffMappings.clear();
          results[1].TariffMappings?.forEach(value => {
            this.tariffMappings.push(this.fb.group({
              Grade: [0],
              GradeName: [''],
              ProductCode: [''],
              FuelCardsOnly: [false],
              FullGrade: ['Grade ' + value.Grade + (value.GradeName ? ' (' + value.GradeName + ')' : ''), { disabled: true }],
              FullProductCode: ['Product code ' + value.ProductCode]
            }))
          });

          //Prepare discount cards array
          this.discountCards.clear();
          results[1].DiscountCards?.forEach(value => {
            this.discountCards.push(this.fb.group({
              Iin: [''],
              Name: [''],
              Type: [''],
              Value: [0],
              Grade: [0],
              OriginalType: [value.Type],
              NameError: [false],
              TypeError: [false],
              ValueError: [false],
              GradeError: [false],
              RemoveError: [false]
            }))
          });

          //Prepare loyalty schemes array
          this.loyaltySchemes.clear();
          results[1].Loyalty?.forEach(value => {
            var hostnamesArray = this.fb.array([]);
            value.Hostnames.forEach(hostname => {
              hostnamesArray.push(this.fb.group({
                Name: [''],
                IpAddres: [''],
                Port: [0],
                Hostname: [''],
                RemoveError: [false]
              }))
            });

            var hostsArray = this.fb.array([]);
            value.Hosts.forEach(host => {
              hostsArray.push(this.fb.group({
                Name: [''],
                IpAddress: [''],
                Port: [0],
                Hostname: [''],
                FullAddress: [host.IpAddress + ':' + host.Port],
                RemoveError: [false]
              }))
            });

            var iinsArray = this.fb.array([]);
            value.Iins.forEach(iin => {
              iinsArray.push(this.fb.group({
                Name: [''],
                Low: [''],
                High: [''],
                FullIin: ['From ' + iin.Low + (iin.Low !== iin.High ? ' to ' + iin.High : '')],
                RemoveError: [false]
              }))
            });

            var mappingsArray = this.fb.array([]);
            value.LoyaltyMappings.forEach(map => {
              mappingsArray.push(this.fb.group({
                Name: [''],
                ProductCode: [''],
                LoyaltyCode: [''],
                FullProductCode: ['Product code ' + map.ProductCode],
                FullLoyaltyCode: ['Loyalty code ' + map.LoyaltyCode],
                RemoveError: [false]
              }))
            });

            this.loyaltySchemes.push(this.fb.group({
              ApiKey: [''],
              Footer1: [''],
              Footer2: [''],
              Hostnames: hostnamesArray,
              Hosts: hostsArray,
              HttpHeader: [''],
              Iins: iinsArray,
              LoyaltyMappings: mappingsArray,
              Name: [''],
              Present: [false],
              SiteId: [''],
              TerminalId: [''],
              Timeout: [0]
            }))
          });

          // Loyalty availability information
          let morrisonsAvailability = results[1].Loyalty?.findIndex(x => x.Name === 'Morrisons') > -1;
          this.genericOptConfigForm.get('MorrisonLoyaltyAvailable').setValue(morrisonsAvailability);

          //Prepare washing programs array
          this.washes.clear();
          results[1].Washes?.forEach(value => {
            this.washes?.push(this.fb.group({
              ProgramId: [0],
              ProductCode: [''],
              Description: [''],
              Price: [0],
              VatRate: [0],
              Category: [''],
              Subcategory: [''],
              FormattedPrice: [this.currencyPipe.transform(value.Price / 100, "GBP")],
              RemoveError: [false]
            }))
          });

          //Prepare predefined amounts array
          this.predefinedAmounts.clear();
          results[1].PredefinedAmounts?.forEach(value => {
            this.predefinedAmounts.push(this.fb.group({
              Amount: [0],
              FormattedAmount: [this.currencyPipe.transform(value.Amount / 100, "GBP")],
              RemoveError: [false]
            }))
          });

          //Receipt headers array
          this.receiptHeaders.clear();
          results[1].ReceiptLayoutMode?.ReceiptHeaders?.forEach(value => {
            this.receiptHeaders.push(new FormControl(value))
          });

          //Receipt footers array
          this.receiptFooters.clear();
          results[1].ReceiptLayoutMode?.ReceiptFooters?.forEach(value => {
            this.receiptFooters.push(new FormControl(value))
          });

          this.genericOptConfigForm.patchValue(results[1]);

          //Formatted contactless details
          this.genericOptConfigForm.controls['ContactlessDetails'].get('FormattedCardPreAuthLimit').patchValue(this.decimalPipe.transform(results[1].ContactlessDetails?.CardPreAuthLimit / 100, '1.2-2'));
          this.genericOptConfigForm.controls['ContactlessDetails'].get('FormattedDevicePreAuthLimit').patchValue(this.decimalPipe.transform(results[1].ContactlessDetails?.DevicePreAuthLimit / 100, '1.2-2'));
        }
      },
      error => {
        this.logger.error('Error processing array of Generic OPT Config observables', error);
      },
      () => {
        if (showLoading) {
          this.loadingService.hideLoadingScreen();
          this.showContent = true;
        }
      }
    )
  }

  toggleActiveLoyaltySection(sectionId: string) {
    this.activeLoyaltySection = sectionId;
  }

  toggleDiscountCards(props: NgbPanelChangeEvent) {
    this.activeDiscountCard = props.panelId;
  }

  toggleLoyaltyScheme(props: NgbPanelChangeEvent) {
    this.activeLoyaltyScheme = props.panelId;
  }

  toggleWash(props: NgbPanelChangeEvent) {
    this.activeWash = props.panelId;
  }

  ngOnInit(): void {
    setTimeout(() => { this.refreshData(); });
  }

  ngOnDestroy(): void {
    this.optDataSubscription?.unsubscribe();
  }

}
