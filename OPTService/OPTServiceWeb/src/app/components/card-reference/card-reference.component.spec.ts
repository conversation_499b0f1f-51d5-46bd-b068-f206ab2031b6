import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';

import { CardReferenceComponent } from './card-reference.component';

describe('CardReferenceComponent', () => {
  let cardReferenceComponent: CardReferenceComponent;
  let advancedConfigServiceSpy: jasmine.SpyObj<AdvancedConfigService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(() => {
    const advancedConfigSpy = jasmine.createSpyObj('AdvancedConfigService', ['setCardProperty','clearCardProperty']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      imports: [
        HttpClientModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [ 
        CardReferenceComponent ,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: AdvancedConfigService, useValue: advancedConfigSpy },
      ]
    })

    advancedConfigServiceSpy = TestBed.inject(AdvancedConfigService) as jasmine.SpyObj<AdvancedConfigService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    cardReferenceComponent = TestBed.inject(CardReferenceComponent);
  });

  it('should create', () => {
    expect(cardReferenceComponent).toBeTruthy();
  });

  it('.updateCardReference() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(of({}));
    
    //Act
    cardReferenceComponent.updateCardReference();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.updateCardReference() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    cardReferenceComponent.updateCardReference();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.removeCardReference() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.clearCardProperty.and.returnValue(of({}));
    
    //Act
    cardReferenceComponent.removeCardReference();

    //Assert
    expect(advancedConfigServiceSpy.clearCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.removeCardReference() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.clearCardProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    cardReferenceComponent.removeCardReference();

    //Assert
    expect(advancedConfigServiceSpy.clearCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.updateAcquirerReference() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(of({}));
    
    //Act
    cardReferenceComponent.updateAcquirerReference();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.updateAcquirerReference() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    cardReferenceComponent.updateAcquirerReference();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.removeAcquirerReference() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.clearCardProperty.and.returnValue(of({}));
    
    //Act
    cardReferenceComponent.removeAcquirerReference();

    //Assert
    expect(advancedConfigServiceSpy.clearCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.removeAcquirerReference() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.clearCardProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    cardReferenceComponent.removeAcquirerReference();

    //Assert
    expect(advancedConfigServiceSpy.clearCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setFuelCard() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(of({}));
    
    //Act
    cardReferenceComponent.setFuelCard();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setFuelCard() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    cardReferenceComponent.setFuelCard();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setExternalName() should handle success response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(of({}));

    //Act
    cardReferenceComponent.setExternalName();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setExternalName() should handle unsuccess response from service', () => {
    //Arrange
    advancedConfigServiceSpy.setCardProperty.and.returnValue(throwError({ status: 500 }))

    //Act
    cardReferenceComponent.setExternalName();

    //Assert
    expect(advancedConfigServiceSpy.setCardProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });
});
