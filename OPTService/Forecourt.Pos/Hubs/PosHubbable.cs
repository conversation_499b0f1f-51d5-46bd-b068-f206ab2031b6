using Forecourt.Pos.Hubs.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using Microsoft.AspNet.SignalR;

namespace Forecourt.Pos.Hubs
{
    /// <summary>
    /// Pos version of <see cref="Hubbable{TMessage, TMessageTracking}"/> dealing with <see cref="PosMessage"/> based messaging
    /// </summary>
    /// <inheritdoc/>
    public class PosHubbable : Hubbable<PosMessage, IMessageTracking>, IPosHubbable, IPosHubbableIn
    {
        /// <inheritdoc/>
        public PosHubbable(IHtecLogManager logManager, IConfigurationManager configurationManager = null) : 
            base(logManager, nameof(PosHubbable), GlobalHost.ConnectionManager.GetHubContext<PosHub>(), configurationManager, "statusResponse")
        {
        }
    }
}
