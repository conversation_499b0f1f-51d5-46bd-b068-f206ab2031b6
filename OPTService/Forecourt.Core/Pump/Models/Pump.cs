using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;

namespace Forecourt.Core.Pump.Models
{
    [HasConfiguration]
    public class Pump : Loggable
    {
        public const string HeaderPump = nameof(Pump);
        public const string LoggerName = ConfigConstants.LoggerPrefixOptService + nameof(Pump);

        /// <inheritdoc/>
        public Pump(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager = null, string typeName = null, bool useXmlConfigurator = false, ILogFormatter logFormatter = null) : 
            base(logManager, loggerName, configurationManager, typeName, useXmlConfigurator, logFormatter)
        {
        }
    }
}
