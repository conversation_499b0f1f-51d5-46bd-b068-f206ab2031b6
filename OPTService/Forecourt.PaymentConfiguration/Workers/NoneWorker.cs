using Forecourt.PaymentConfiguration.HydraDb.Interfaces;
using Forecourt.PaymentConfiguration.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace Forecourt.PaymentConfiguration.Workers
{
    /// <summary>
    /// Dummy implementation of the <see cref="IPaymentConfigIntegrator"/>
    /// </summary>
    public class NoneWorker : HydraDbable, INoneWorker
    {
        /// <inheritdoc/>
        public NoneWorker(IHtecLogManager logManager, IHydraDb hydraDb, IFileSystem fileSystem, IConfigurationManager configurationManager = null) : 
            base(logManager, nameof(NoneWorker), hydraDb, fileSystem, configurationManager: configurationManager)
        {
        }

        /// <inheritdoc/>
        public override bool StrictPumpTidValidation => false;

        /// <inheritdoc/>
        public override IList<Tuple<string, int>> EndPoints => new List<Tuple<string, int>>();
    }
}
