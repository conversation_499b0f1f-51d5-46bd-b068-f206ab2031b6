namespace OPT.Common
{
    /// <summary>
    /// EventType items
    /// </summary>
    public enum EventItem
    {
        /// <summary>
        /// Media channel
        /// </summary>
        MediaChannel,

        /// <summary>
        /// Esocket Override Contactless
        /// </summary>
        EsocketOverrideContactless,

        /// <summary>
        /// Retalix Transaction File Directory
        /// </summary>
        RetalixTransactionFileDirectory,

        /// <summary>
        /// Transaction File Directory
        /// </summary>
        TransactionFileDirectory,

        /// <summary>
        /// Whitelist Directory
        /// </summary>
        WhitelistDirectory,

        /// <summary>
        /// Layout Directory
        /// </summary>
        LayoutDirectory,

        /// <summary>
        /// Software Directory
        /// </summary>
        SoftwareDirectory,

        /// <summary>
        /// Media Directory
        /// </summary>
        MediaDirectory,

        /// <summary>
        /// Playlist Directory
        /// </summary>
        PlaylistDirectory,

        /// <summary>
        /// Log File Directory
        /// </summary>
        LogFileDirectory,

        /// <summary>
        /// Trace File Directory
        /// </summary>
        TraceFileDirectory,

        /// <summary>
        /// Journal File Directory
        /// </summary>
        JournalFileDirectory,

        /// <summary>
        /// Received Update Directory
        /// </summary>
        ReceivedUpdateDirectory,

        /// <summary>
        /// Contactless Properties File
        /// </summary>
        ContactlessPropertiesFile,

        /// <summary>
        /// Fuel Data Update File
        /// </summary>
        FuelDataUpdateFile,

        /// <summary>
        /// Upgrade File Directory
        /// </summary>
        UpgradeFileDirectory,

        /// <summary>
        /// Rollback File Directory
        /// </summary>
        RollbackFileDirectory,

        /// <summary>
        /// OPT Log File Directory
        /// </summary>
        OptLogFileDirectory,

        /// <summary>
        /// Database Backup Directory
        /// </summary>
        DatabaseBackupDirectory
    }
}

