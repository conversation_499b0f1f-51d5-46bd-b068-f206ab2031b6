<ng-container *ngIf="showContent">
  <form [formGroup]="advancedForm">
    <div id="card-advanced-integrations" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="integrationsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Integrations</span>
          <div id="integrationsConnectedBadge">
            <app-esocket-connected-badge [eSocketConnected]="eSocketConnected"></app-esocket-connected-badge>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':integrationsIsCollapsed,'bi-chevron-up':integrationsIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #integrationsCollapse="ngbCollapse" [(ngbCollapse)]="integrationsIsCollapsed" class="card-body">
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="siteType" labelText="Site type" controlName="SiteType" [options]="siteTypeOptions" [formGroup]="advancedForm"
                                   (action)="updateSiteType($event)" [errorInAction]="siteTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="posType" labelText="POS type" controlName="PosType" [options]="posTypeOptions" [formGroup]="advancedForm"
                                   (action)="updatePosType($event)" [errorInAction]="posTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="bosType" labelText="BOS type" controlName="BosType" [options]="bosTypeOptions" [formGroup]="advancedForm"
                                   (action)="updateBackOfficeType($event)" [errorInAction]="bosTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="pumpType" labelText="Pump/Tank Gauge type" controlName="PumpType" [options]="pumpTypeOptions" [formGroup]="advancedForm"
                                   (action)="updatePumpType($event)" [errorInAction]="pumpTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="mobilePosType" labelText="Mobile (POS) type" controlName="MobilePosType" [options]="mobilePosTypeOptions" [formGroup]="advancedForm"
                                   (action)="updateMobilePosType($event)" [errorInAction]="mobilePosTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="secAuthType" labelText="Secondary Auth type" controlName="SecAuthType" [options]="secAuthTypeOptions" [formGroup]="advancedForm"
                                   (action)="updateSecAuthType($event)" [errorInAction]="secAuthTypeError"></app-label-dropdown-button>
        <app-label-dropdown-button labelColClass="col-12 col-md-2 col-xl-2" dropdownColClass="col-12 col-md-6 col-xl-3"
                                   id="paymentConfigType" labelText="Payment Configuration type" controlName="PaymentConfigType" [options]="paymentConfigTypeOptions" [formGroup]="advancedForm"
                                   (action)="updatePaymentConfigType($event)" [errorInAction]="paymentConfigTypeError"></app-label-dropdown-button>
        <app-esocket-pos-config (eSocketConnected)="updateESocketConnected($event)"></app-esocket-pos-config>
      </div>
    </div>

    <div id="card-advanced-general-settings" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="generalSettingsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>General settings</span><i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':generalSettingsIsCollapsed,'bi-chevron-up':generalSettingsIsCollapsed===false }"></i>
        </button>
      </div>
      <div #generalSettingsCollapse="ngbCollapse" [(ngbCollapse)]="generalSettingsIsCollapsed" class="card-body">
        <app-switch-label id="autoAuth" labelText="Automatic authentication" controlName="AutoAuth"
                          [errorInAction]="autoAuthError" (action)="updateAutoAuth($event)" [formGroup]="advancedForm"></app-switch-label>
        <app-switch-label id="mediaChannel" labelText="Media channel" controlName="MediaChannel"
                          [errorInAction]="mediaChannelError" (action)="updateMediaChannel($event)" [formGroup]="advancedForm">
        </app-switch-label>
        <app-switch-label id="unmannedPseudoPos" labelText="Unmanned pseudo POS" controlName="UnmannedPseudoPos"
                          [errorInAction]="unmannedPseudoPosError" (action)="updateUnmannedPseudoPos($event)"
                          [formGroup]="advancedForm"></app-switch-label>
        <app-switch-label id="forwardFuelPriceUpdate" labelText="Forward fuel price update"
                          controlName="ForwardFuelPriceUpdate" [errorInAction]="forwardFuelPriceUpdateError"
                          (action)="updateForwardFuelPriceUpdate($event)" [formGroup]="advancedForm"></app-switch-label>
        <app-switch-label id="localAccountsEnabled" labelText="Local accounts enabled" controlName="LocalAccountsEnabled"
                          [errorInAction]="localAccountsEnabledError" (action)="updateLocalAccountsEnabled($event)" [formGroup]="advancedForm"></app-switch-label>
        <app-label-text-button id="siteName" labelColClass="col-12 col-md-2 col-xl-2"
                          textColClass="col-12 col-md-6 col-xl-3" labelText="Site name" controlName="SiteName"
                          [errorInAction]="siteNameError" (action)="updateSiteName($event)"
                          [formGroup]="advancedForm"></app-label-text-button>
        <app-label-text-button id="vatNumber" labelColClass="col-12 col-md-2 col-xl-2"
                          textColClass="col-12 col-md-6 col-xl-3" labelText="VAT number" controlName="VatNumber"
                          [errorInAction]="vatNumberError" (action)="updateVatNumber($event)"
                          [formGroup]="advancedForm"></app-label-text-button>
        <app-label-text-button id="currencyCode" labelColClass="col-12 col-md-2 col-xl-2"
                          textColClass="col-12 col-md-6 col-xl-3" labelText="Currency code" controlName="CurrencyCode"
                          [errorInAction]="currencyCodeError" (action)="updateCurrencyCode($event)"
                          [formGroup]="advancedForm"></app-label-text-button>
        <app-label-text-button id="tillNumber" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Till number" controlName="TillNumber"
                               [errorInAction]="tillNumberError" (action)="updateTillNumber($event)" [formGroup]="advancedForm"
                               maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="posClaimNumber" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="POS claim number" controlName="PosClaimNumber"
                               [errorInAction]="posClaimNumberError" (action)="updatePosClaimNumber($event)" [formGroup]="advancedForm"
                               maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="fuelCategory" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Fuel category" controlName="FuelCategory"
                               [errorInAction]="fuelCategoryError" (action)="updateFuelCategory($event)" [formGroup]="advancedForm"
                               maxLength="10">
        </app-label-text-button>
      </div>
    </div>

    <div id="card-advanced-housekeeping" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="housekeepingCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Housekeeping</span><i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':housekeepingIsCollapsed,'bi-chevron-up':housekeepingIsCollapsed===false }"></i>
        </button>
      </div>
      <div #housekeepingCollapse="ngbCollapse" [(ngbCollapse)]="housekeepingIsCollapsed" class="card-body">
        <app-label-text-button id="filePruneDays" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="File prune days" controlName="FilePruneDays"
                               [errorInAction]="filePruneDaysError" (action)="updateFilePruneDays($event)" [formGroup]="advancedForm"
                               maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="transactionPruneDays" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Transaction prune days"
                               controlName="TransactionPruneDays" [errorInAction]="transactionPruneDaysError"
                               (action)="updateTransactionPruneDays($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="receiptPruneDays" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Receipt prune days" controlName="ReceiptPruneDays"
                               [errorInAction]="receiptPruneDaysError" (action)="updateReceiptPruneDays($event)" [formGroup]="advancedForm"
                               maxLength="10">
        </app-label-text-button>
      </div>
    </div>

    <div id="card-advanced-payment-timeouts" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="timeoutCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Timeouts</span><i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':timeoutIsCollapsed,'bi-chevron-up':timeoutIsCollapsed===false }"></i>
        </button>
      </div>
      <div #timeoutCollapse="ngbCollapse" [(ngbCollapse)]="timeoutIsCollapsed" class="card-body">
        <app-label-text-button id="paymentTimeoutOpt" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="OPT" appendText="sec" controlName="PaymentTimeoutOpt"
                               [errorInAction]="paymentTimeoutOptError" (action)="updatePaymentTimeoutOpt($event)" [formGroup]="advancedForm"
                               maxLength="10"></app-label-text-button>
        <app-label-text-button id="paymentTimeoutPod" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Pod" appendText="sec" controlName="PaymentTimeoutPod"
                               [errorInAction]="paymentTimeoutPodError" (action)="updatePaymentTimeoutPod($event)" [formGroup]="advancedForm"
                               maxLength="10"></app-label-text-button>
        <app-label-text-button id="paymentTimeoutMixed" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Mixed" appendText="sec"
                               controlName="PaymentTimeoutMixed" [errorInAction]="paymentTimeoutMixedError"
                               (action)="updatePaymentTimeoutMixed($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="paymentTimeoutNozzleDown" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Nozzle down" appendText="sec"
                               controlName="PaymentTimeoutNozzleDown" [errorInAction]="paymentTimeoutNozzleDownError"
                               (action)="updatePaymentTimeoutNozzleDown($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="TimeoutKiosk" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Kiosk" appendText="sec"
                               controlName="TimeoutKiosk" [errorInAction]="timeoutKioskError"
                               (action)="updateTimeoutKiosk($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="TimeoutSecAuth" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Secondary Auth" appendText="sec"
                               controlName="TimeoutSecAuth" [errorInAction]="timeoutSecAuthError"
                               (action)="updateTimeoutSecAuth($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
      </div>
    </div>

    <div id="card-advanced-fuelling-settings" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="fuellingSettingsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Fuelling settings</span><i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':fuellingSettingsIsCollapsed,'bi-chevron-up':fuellingSettingsIsCollapsed===false }"></i>
        </button>
      </div>
      <div #fuellingSettingsCollapse="ngbCollapse" [(ngbCollapse)]="fuellingSettingsIsCollapsed" class="card-body">
        <app-switch-label id="nozzleUpForKioskUse" labelText="Nozzle up for kiosk mode" controlName="NozzleUpForKioskUse"
                          [errorInAction]="nozzleUpForKioskUseError" (action)="updateNozzleUpForKioskUse($event)" 
                          [formGroup]="advancedForm">
        </app-switch-label>
        <app-switch-label id="useReplaceNozzleScreen" labelText="Use replace nozzle screen" controlName="UseReplaceNozzleScreen"
                          [errorInAction]="useReplaceNozzleScreenError" (action)="updateUseReplaceNozzleScreen($event)" 
                          [formGroup]="advancedForm">
        </app-switch-label>
        <app-switch-label id="IndefiniteFuellingWait" labelColClass="col-12 col-md-2 col-xl-2"
                          textColClass="col-12 col-md-6 col-xl-3" labelText="Indefinite fuelling wait"
                          controlName="FuellingIndefiniteWait" [errorInAction]="indefiniteFuellingWaitError"
                          (action)="updateFuellingIndefiniteWait($event)" [formGroup]="advancedForm"></app-switch-label>
        <app-label-text-button id="fuellingWaitMinutes" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Fuelling wait" appendText="min"
                               controlName="FuellingWaitMinutes" [errorInAction]="fuellingWaitMinutesError"
                               (action)="updateFuellingWaitMinutes($event)" [formGroup]="advancedForm"
                               [disabled]="advancedFormControl.FuellingWaitMinutes.invalid || fuellingWaitMinutesDisabled" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="backoffAuth" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Backoff auth" appendText="p"
                               controlName="FuellingBackoffAuth" [errorInAction]="backoffAuthError"
                               (action)="updateFuellingBackoffAuth($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="backoffPreAuth" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Backoff pre auth" appendText="p"
                               controlName="FuellingBackoffPreAuth" [errorInAction]="backoffPreAuthError"
                               (action)="updateFuellingBackoffPreAuth($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="backoffStopStart" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Backoff stop start" appendText="p"
                               controlName="FuellingBackoffStopStart" [errorInAction]="backoffStopStartError"
                               (action)="updateFuellingBackoffStopStart($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button id="backoffStopOnly" labelColClass="col-12 col-md-2 col-xl-2"
                               textColClass="col-12 col-md-6 col-xl-3" labelText="Backoff stop only" appendText="p"
                               controlName="FuellingBackoffStopOnly" [errorInAction]="backoffStopOnlyError"
                               (action)="updateFuellingBackoffStopOnly($event)" [formGroup]="advancedForm" maxLength="10">
        </app-label-text-button>
        <app-label-text-button-button labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" 
                                      labelText="Max fill override"  appendText="p" controlName="MaxFillOverride"
                                      [errorInAction]="maxFillOverrideError" (primaryAction)="updateMaxFillOverride($event)"
                                      (secondaryAction)="clearMaxFillOverride($event)" [formGroup]="advancedForm" 
                                      maxLength="10" placeHolder="- Not set -">
        </app-label-text-button-button>
      </div>
    </div>

    <div id="card-advanced-card-references" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="cardReferencesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Card references</span>
          <div>
            <span id="card-advanced-card-references-summary" class="mx-1">{{paymentCards}} Payment, {{fuelCards}} Fuel</span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':cardReferencesIsCollapsed,'bi-chevron-up':cardReferencesIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #cardReferencesCollapse="ngbCollapse" [(ngbCollapse)]="cardReferencesIsCollapsed" class="card-body">
        <div class="row form-group">
          <button type="button" id="addCardReferenceBtn" class="btn btn-success m-auto btn-labeled" (click)="open(content)">
            <span class="btn-label"><i class="bi bi-plus"></i></span>Add card
          </button>
        </div>
        <ngb-accordion [closeOthers]="true" [activeIds]="activeCardReference">
          <ngb-panel *ngFor="let item of advancedConfigData?.CardReferences; let i = index" [id]="'item'+i">
            <ng-template ngbPanelHeader>
              <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle
                        class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                  <span>
                    <span>{{item.Name}}</span>
                    <span class="badge badge-success badge-card-new ml-2" *ngIf="isNewCardReference(item,i)">New</span>
                  </span>
                  <span class="badge badge-card-type"
                        [ngClass]="item.FuelCard ? 'badge-secondary' : 'badge-info'">
                    {{
item.FuelCard ?
                                        'Fuel' : 'Payment'
                    }}
                  </span>

                </button>
              </div>
            </ng-template>
            <ng-template ngbPanelContent>
              <app-card-reference [cardReference]="item" [id]="'cardReference'+i"></app-card-reference>
            </ng-template>
          </ngb-panel>
        </ngb-accordion>
      </div>
    </div>
    
    <div id="card-advanced-misc" class="card form-group">
      <div class="card-header">
        <button type="button" (click)="miscConfigurablesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Miscellaneous Configurables, by Category</span>
          <div>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':miscConfigurablesIsCollapsed,'bi-chevron-up':miscConfigurablesIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #miscConfigurablesCollapse="ngbCollapse" [(ngbCollapse)]="miscConfigurablesIsCollapsed" class="card-body">
        <ngb-accordion #accordionConfigKeys>
          <ngb-panel *ngFor="let category of advancedConfigData?.ConfigurationCategories; let i = index" [id]="'category-'+i">
            <ng-template ngbPanelHeader>
              <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle
                        class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                  <span>{{category.Category}} ({{getCategoryCount(category)}} config keys)</span>
                  <span [ngClass]="{'bi-chevron-up': accordionConfigKeys.panels._results[i].isOpen, 'bi-chevron-down': !accordionConfigKeys.panels._results[i].isOpen}"></span>
                </button>
              </div>
            </ng-template>
            <ng-template ngbPanelContent>
              <app-misc-setting *ngFor="let setting of category.Settings | keyvalue; let j = index" [category]="category.Category" [categoryId]="category.Id" [key]="setting.key" [setting]="setting" [id]="'setting-'+i+'-'+j"></app-misc-setting>
            </ng-template>
          </ngb-panel>
        </ngb-accordion>
      </div>
    </div>
  </form>
    <ng-template #content let-modal>
        <form id="addCardForm" [formGroup]="addCardForm" (ngSubmit)="onCardFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add card reference</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="cardName">Name</label>
                    <input type="text" class="form-control" id="cardName" formControlName="Name"
                        [ngClass]="{'is-invalid':addCardFormControl.Name.invalid && (addCardFormControl.Name.dirty || addCardFormControl.Name.touched)}"
                        required aria-describedby="cardNameFeedback" ngbAutofocus>
                    <div id="cardNameFeedback" class="invalid-feedback">
                        Please provide a valid value
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-auto">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" id="cardTypePayment" [value]=false name="FuelCard"
                                formControlName="FuelCard">
                            <label class="form-check-label" for="cardTypePayment">Payment</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" id="cardTypeFuel" [value]=true name="FuelCard"
                                formControlName="FuelCard">
                            <label class="form-check-label" for="cardTypeFuel">Fuel</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addCardFormControl.Name.invalid">Save</button>
                <span id="addCardReferenceError" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                    *ngIf="addCardError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>
</ng-container>
