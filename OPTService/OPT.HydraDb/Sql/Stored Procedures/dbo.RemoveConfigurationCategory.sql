USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('RemoveConfigurationCategory') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE RemoveConfigurationCategory
END
GO

CREATE PROCEDURE RemoveConfigurationCategory
@category NVARCHAR(100)
AS		
BEGIN

	DECLARE @catId INT;
	SELECT @catId = cc.Id FROM dbo.ConfigurationCategory cc WHERE cc.Category = @category;

	DELETE FROM dbo.ConfigurationDetail WHERE CategoryId = @catId;

	DELETE FROM dbo.ConfigurationCategory WHERE Id = @catId;

END
GO

GRANT EXECUTE ON RemoveConfigurationCategory TO UserRole
