using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers;
using System;
using System.Collections.Generic;
using System.Net;
using IHydraDb = Forecourt.Pump.HydraDb.Interfaces.IHydraDb;
using ITelemetryWorker = Forecourt.Pump.Workers.Interfaces.ITelemetryWorker;

namespace Forecourt.Pump.Workers
{
    /// <inheritdoc />
    public class TankGaugeWorker : HydraDbable<IHydraDb>, ITankGaugeWorker
    {
        private ITankGaugeController _tankGaugeController;
        private readonly ITankGaugeControllerFactory _tankGaugeControllerFactory;

        private INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        /// <inheritdoc/>
        public IPEndPoint EndPoint => _tankGaugeController?.CurrentEndPoint?.EndPoint;

        /// <summary>Constructor for ANPR Worker.</summary>
        /// <param name="tankGaugeControllerFactory">TankGauge controller factory</param>
        /// <param name="telemetryWorker">Telemetry Worker for this class.</param>
        /// <param name="tankGaugeJournal">Journal Worker for this class.</param>
        /// <param name="hydraDb">Hydra Database.</param>
        /// <param name="logger">Htec Logger for this class.</param>
        /// <param name="configurationManager"><see cref="IConfigurationManager"/> instance</param>
        /// <param name="timerFactory"><see cref="ITimerFactory"/> instance</param>
        public TankGaugeWorker(ITankGaugeControllerFactory tankGaugeControllerFactory, ITelemetryWorker telemetryWorker, ITankGaugeIntegratorOutJournal tankGaugeJournal, IHydraDb hydraDb,
            IHtecLogger logger, IConfigurationManager configurationManager, ITimerFactory timerFactory) : 
            base(hydraDb, logger, configurationManager)
        {
            _tankGaugeControllerFactory = tankGaugeControllerFactory ?? throw new ArgumentNullException(nameof(tankGaugeControllerFactory));
            RegisterWorker(tankGaugeJournal ?? throw new ArgumentNullException(nameof(tankGaugeJournal)));
        }

        /// <inheritdoc />
        protected override Result DoStart(params object[] startParams)
        {
            var result = base.DoStart(startParams);
            if (!result.IsSuccess)
            {
                return result;
            }

            _tankGaugeController = _tankGaugeControllerFactory.GetInstance(LoggingReference, HydraDb.AdvancedConfig.PumpType);
            _tankGaugeController.RegisterWorker(this);
            _tankGaugeController.Start(startParams);

            return Result.Success();
        }

        /// <inheritdoc />
        protected override Result DoStop()
        {
            _tankGaugeController.Stop();
            DeregisterWorker(_tankGaugeController);
            _tankGaugeController = null;

            return base.DoStop();
        }

        /// <inheritdoc/>
        protected override bool DoIsConnected()
        {
            return base.DoIsConnected() && (_tankGaugeController?.IsConnected() ?? false);
        }

        /// <inheritdoc/>
        public Result RequestDips(string loggingReference = null)
        {
            return DoAction(() =>
            {
                return _tankGaugeController.RequestDips(loggingReference);
            }, loggingReference);
        }

        /// <inheritdoc />
        public void OnDips(IEnumerable<Dip> dips, string loggingReference = null)
        {
            GetWorker<ITankGaugeIntegratorOutJournal>().OnDips(dips, loggingReference);
        }

        /// <inheritdoc/>
        public Result SetIpAddress(IPAddress ipAddress, int port = 0, string loggingReference = null)
        {
            return DoAction(() => {
                var result = _tankGaugeController.SetIpAddress(ipAddress, port, loggingReference);
                DoDeferredLogging(LogLevel.Info, _tankGaugeController.LogHeader, () => new[] { !result.IsSuccess ? result.Error : $"{ipAddress}:{port}" });
                return Restart();
            }, loggingReference);
        }
    }
}