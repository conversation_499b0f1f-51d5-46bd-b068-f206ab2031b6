using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models.Doms;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Workers;
using Forecourt.Pump.Workers.Interfaces;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using HSC;
using Htec.Common.Abstractions.Configuration;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Models.MediaMessage;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPT.Common.HydraDb.Models;
using OPT.Common.HydraDbClasses;
using OPT.Common.HydraOPT;
using OPT.Common.Models;
using OPT.Common.Repositories.Interfaces;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.IO.Abstractions;
using System.Net;
using System.Text;
using System.Threading;
using System.Xml.Linq;
using Xunit;
using cacheConstants = Forecourt.Core.Configuration.Constants;
using CorePumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using HscDispenser = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Dispenser6;
using HscPumpCommand = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpCommand;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using HscTransaction = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Transaction;
using HscVehicleRegistrationData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.VehicleRegistrationData;
using IJournalWorkerReceipt = OPT.Common.Workers.Interfaces.IJournalWorkerReceipt;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using Payment = OPT.Common.HydraOPT.Payment;
using PaymentResult = OPT.Common.HydraOPT.PaymentResult;
using ProductItem = OPT.Common.HydraOPT.ProductItem;
using PumpDelivered = OPT.Common.HydraDbClasses.PumpDelivered;
using PumpStateType = OPT.Common.HydraOPT.PumpStateType;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace OPT.Common.Integration.Tests.Workers
{
    public class DatabaseFixture : IDisposable
    {
        public const string ControllerName = "Controller";
        public const string VersionString = "Version";
        private const string HydraId = "Hydra 1";
        public const string OptId1 = "1234";
        public const string OptId2 = "12345";
        public const string OptId3 = "123456";
        public const string SoftwareVersion = "1.1";
        public const string NewSoftwareVersion = "1.2";
        public const string SecureAssetsVersion = "4.4";
        public const string MultimediaAssetsVersion = "5.5";
        public const string CpatAssetsVersion = "6.6";
        public const string OptFirmwareVersion = "7.7";
        public const string Tid1 = "99979901";
        public const string Tid2 = "99979902";
        public const string Tid3 = "99979903";
        public const string Tid4 = "99979904";
        public const string Grade1 = "Diesel";
        public const string Grade2 = "Super Unleaded";
        public const string Grade3 = "Unleaded";
        public const string FuelProductCode1 = "11";
        public const string FuelProductCode2 = "22";
        public const string FuelProductCode3 = "33";
        public const string WashProductCode1 = "55";
        public const string WashProductCode2 = "66";
        private const string TicketNumber = "654321";
        private const string TxnNumber = "6543";
        public const byte Pump = 1;
        public const byte OtherPump = 3;
        public const byte Hose = 2;
        public const byte Grade = 3;
        public const ushort Price = 1469;
        public const uint Amount = 10000;
        public const uint FuelQuantity = 1000;
        public const float VatRate = 20;
        public const uint CarWashQuantity = 1;
        public const uint CarWashCost = 200;
        private const string WhitelistDirectory = "Whitelist";
        private const string SoftwareDirectory = "Software";
        private const int ReceiptLayoutMode = 1;
        public const int OptNumber = 1;
        public const int TransactionNumber = 10;

        public static readonly uint FuelNetAmount = (uint)Math.Round(FuelQuantity / 100.0 * (Price / 10.0) * 100.0 / (100.0 + VatRate));

        public static readonly uint FuelVatAmount = (uint)Math.Round(FuelNetAmount * VatRate / 100.0);
        public static readonly uint FuelCost = FuelNetAmount + FuelVatAmount;
        public const string CardNumber = "1234********7890";
        public const string CardProductName = "Visa";

        public static OptEndPoints OptEndPoints { get; } =
            new OptEndPoints(HydraId, "***************", 12341, 12342, 12343, 12344, 12345, 12346, 12347);

        public static AnprEndPoint AnprEndPoint { get; } = new AnprEndPoint("***************", 12347);
        public static PumpEndPoint PumpEndPoint { get; } = new PumpEndPoint("***************", 12348);
        public static CarWashEndPoint CarWashEndPoint { get; } = new CarWashEndPoint("***************", 12349);
        public static TankGaugeEndPoint TankGaugeEndPoint { get; } = new TankGaugeEndPoint("***************", 12350);
        public static ESocketEndPoint EsocketEndPoint1 { get; } = new ESocketEndPoint("***************", 12351);
        public static ESocketEndPoint EsocketEndPoint2 { get; } = new ESocketEndPoint("***************", 12352);
        public IHydraDb HydraDb { get; }
        public IPaymentConfigIntegrator PaymentConfig { get; }
        public IDbExecutor HydraDbExecutor { get; }

        public ICacheHelper CacheHelper { get; }

        public static readonly XElement ExpectedSoftwarePending =
            HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId, new Notification(NotificationType.SoftwarePending));

        public static readonly XElement ExpectedConfigPending =
            HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId, new Notification(NotificationType.ConfigPending));

        public static readonly XElement ExpectedWhitelistPending =
            HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId, new Notification(NotificationType.WhitelistPending));

        public static readonly XElement ExpectedMediaUpdatePending =
            HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId, new Notification(NotificationType.MediaUpdatePending));

        public static readonly XElement ExpectedRequestLogFile =
            HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId, new Notification(NotificationType.RequestLogFile));


        public static readonly string SignInInformation =
            $"Sign In request received from {OptId1} with software version {SoftwareVersion}," +
            $" secure assets version {SecureAssetsVersion}," +
            $" multimedia assets version {MultimediaAssetsVersion}, CPAT assets version {CpatAssetsVersion}" +
            $", OPT firmware version {OptFirmwareVersion}";

        public static readonly string SignedInInformation = $"OPT {OptId1} Signed In";
        public static readonly string ConfigPendingInformation = $"Sending notification to OPT {OptId1}, Type is ConfigPending";
        public static readonly string GetConfigInformation = $"Get Config received from {OptId1}";
        public static readonly string WhitelistPendingInformation = $"Sending notification to OPT {OptId1}, Type is WhitelistPending";
        public static readonly string LayoutPendingInformation = $"Sending notification to OPT {OptId1}, Type is LayoutPending";
        public static readonly string GetWhitelistInformation = $"Get Whitelist request received from {OptId1}";
        public static readonly string GetLayoutInformation = $"Get Layout request received from {OptId1}";
        public static readonly string MediaUpdatePendingInformation = $"Sending notification to OPT {OptId1}, Type is MediaUpdatePending";
        public static readonly string GetMediaFilesListInformation = $"Get Media Files List request received from {OptId1}";
        public static readonly string RequestLogFileInformation = $"Sending notification to OPT {OptId1}, Type is RequestLogFile";
        public static readonly string SoftwarePendingInformation = $"Sending notification to OPT {OptId1}, Type is SoftwarePending";

        public static readonly string PumpStateRequestInformation =
            $"Pump state Request received from pump {Pump}, hose is {Hose}, grade is {Grade}, ppu is {Price / 10.0:F1}p / litre";

        public static readonly string NozzleUpInformation = $"Sending notification to OPT {OptId1}, Type is NozzleUp";

        public static readonly string GetSoftwareRequestInformation =
            $"Get Software request received from {OptId1} for software version {SoftwareVersion}";

        public static readonly XElement UnknownHeartbeatRequest = HydraOptMessage.ConstructHeartbeatMessage(true, null, HydraId, null);
        public static readonly IMessageTracking<XElement> HeartbeatRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructHeartbeatMessage(true, OptId1, HydraId, ""));
        public static readonly IMessageTracking<XElement> HeartbeatResponse = new MessageTracking<XElement>(HydraOptMessage.ConstructHeartbeatMessage(false, OptId1, HydraId, "Success"));

        public static readonly IMessageTracking<XElement> SignInRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructSignInMessage(true, OptId1, HydraId, "",
            softwareVersion: SoftwareVersion, secureAssetsVersion: SecureAssetsVersion, multimediaAssetsVersion: MultimediaAssetsVersion,
            cpatAssetsVersion: CpatAssetsVersion, optFirmwareVersion: OptFirmwareVersion));

        public static readonly XElement SignInResponse =
            HydraOptMessage.ConstructSignInMessage(false, OptId1, HydraId, "Success", softwareVersion: VersionString);

        public static readonly IMessageTracking<XElement> NotificationResponse = new MessageTracking<XElement>(HydraOptMessage.ConstructNotificationResponse(OptId1, HydraId, "Success"));

        public static readonly IMessageTracking<XElement> CardInsertedRequest = 
            new MessageTracking<XElement>(HydraOptMessage.ConstructOptNotificationRequest(OptId1, HydraId, new Notification(NotificationType.CardInserted, Pump)));

        public static readonly XElement NozzleUpNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.NozzleUp, Pump, new DeliveryDetails(Pump, Grade, 0, 0, Grade3, Price, 0, 0, 0)));

        public static readonly XElement NozzleDownNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.NozzleDown, Pump, new DeliveryDetails(Pump, Grade, 0, 0, Grade3, Price, 0, 0, 0)));

        public static readonly XElement DeliveringNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.Delivering, Pump, new DeliveryDetails(Pump, Grade, 0, 0, Grade3, Price, 0, 0, 0)));

        public static readonly XElement DeliveredNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.Delivered, Pump,
                new DeliveryDetails(Pump, Grade, FuelQuantity, FuelCost, Grade3, Price, FuelNetAmount, FuelVatAmount, VatRate)));

        public static readonly XElement TakeFuelNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.TakeFuel, Pump, new DeliveryDetails(Pump, Grade, 0, 0, Grade3, Price, 0, 0, 0)));

        public static readonly XElement PumpStateKioskOnlyNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.PumpStateChanged, pumpState: new NotificationPumpState(Pump, PumpStateType.KioskOnly)));

        public static readonly XElement PumpStateOpenNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.PumpStateChanged, pumpState: new NotificationPumpState(Pump, PumpStateType.Open)));

        public static readonly XElement PumpStateMixedNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.PumpStateChanged, pumpState: new NotificationPumpState(Pump, PumpStateType.Mixed)));

        public static readonly XElement PumpStateClosedNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.PumpStateChanged, pumpState: new NotificationPumpState(Pump, PumpStateType.Closed)));

        public static readonly XElement PaidAtKioskNotification = HydraOptMessage.ConstructNotificationRequest(OptId1, HydraId,
            new Notification(NotificationType.PaidAtKiosk, Pump, new DeliveryDetails(Pump, Grade, 0, 0, Grade3, Price, 0, 0, 0)));

        public static readonly IMessageTracking<XElement> PaymentApprovedRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructPaymentRequest(OptId1, HydraId,
            new Payment(PaymentResult.Approved, Pump, Amount, CardNumber, CardProductName)));

        public static readonly IMessageTracking<XElement> PaymentClearedRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructPaymentRequest(OptId1, HydraId,
            new Payment(PaymentResult.Cleared, Pump, FuelCost + CarWashCost, CardNumber, CardProductName, TxnNumber, TicketNumber,
                products: new ProductsList(new List<ProductItem>
                {
                    new ProductItem(FuelProductCode3, FuelQuantity, FuelCost),
                    new ProductItem(WashProductCode1, CarWashQuantity, CarWashCost)
                }))));

        public static readonly IMessageTracking<XElement> PaymentApprovedWithCardRestrictionsOkRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructPaymentRequest(OptId1,
            HydraId,
            new Payment(PaymentResult.Approved, Pump, Amount, CardNumber, CardProductName, null, null,
                new CardRestrictionsList(new List<string> { FuelProductCode1, FuelProductCode3 }))));

        public static readonly IMessageTracking<XElement> PaymentApprovedWithCardRestrictionsFailRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructPaymentRequest(OptId1,
            HydraId,
            new Payment(PaymentResult.Approved, Pump, Amount, CardNumber, CardProductName, null, null,
                new CardRestrictionsList(new List<string> { FuelProductCode1, FuelProductCode2 }))));

        public static readonly IMessageTracking<XElement> GetSoftwareRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructGetSoftwareRequest(OptId1, HydraId, SoftwareVersion));
        public static readonly IMessageTracking<XElement> GetConfigRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructGetConfigRequest(OptId1, HydraId));
        public static readonly IMessageTracking<XElement> GetWhitelistRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructGetWhitelistRequest(OptId1, HydraId));
        public static readonly IMessageTracking<XElement> GetLayoutRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructGetLayoutRequest(OptId1, HydraId));
        public static readonly IMessageTracking<XElement> GetMediaFilesListRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructGetMediaFilesListRequest(OptId1, HydraId));

        public static readonly HscPumpData HscPumpRequest = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser { State = (byte)PumpState.Request, CurrentHose = Hose, Grade2 = Grade, Price2 = Price },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpData HscPumpIdle = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser { State = (byte)PumpState.Idle, CurrentHose = Hose, Grade2 = Grade, Price2 = Price },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpData HscPumpDelivering = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser { State = (byte)PumpState.Delivering, CurrentHose = Hose, Grade2 = Grade, Price2 = Price },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpData HscPumpDelivered = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser
            {
                State = (byte)PumpState.Idle,
                Grade2 = Grade,
                Price2 = Price,
                Transaction1 = new HscTransaction { Cash = FuelCost, Hose = Hose, Volume = FuelQuantity, Paid = false }
            },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpData HscPumpAuthorised = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser
            {
                State = (byte)PumpState.Authorise,
                CurrentHose = Hose,
                Grade2 = Grade,
                Price2 = Price
            },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpData HscPumpPaid = new HscPumpData
        {
            PumpNumber = Pump,
            Dispenser = new HscDispenser
            {
                State = (byte)PumpState.Idle,
                CurrentHose = Hose,
                Grade2 = Grade,
                Price2 = Price,
                CurrentCash = FuelCost,
                CurrentVolume = FuelQuantity,
                Transaction1 = new HscTransaction { Cash = FuelCost, Paid = true },
                Transaction2 = new HscTransaction { Cash = 0, Paid = true }
            },
            RegistrationData = new HscVehicleRegistrationData()
        };

        public static readonly HscPumpCommand HscExpectedAuth = new HscPumpCommand
        { Cash = Amount, Command = (byte)PumpCommandType.AuthWithLimit, Pump = Pump };

        public static readonly IList<JournalTotalSalesItem> ExpectedTotalSales = new List<JournalTotalSalesItem>
        {
            new JournalTotalSalesItem
            {
                PumpNumber = Pump,
                Hose = Hose,
                Amount = FuelCost + CarWashCost,
                CardNumber = CardNumber,
                CardProductName = CardProductName
            }
        };

        public static readonly IList<JournalFuelSalesItem> ExpectedFuelSales = new List<JournalFuelSalesItem>
        {
            new JournalFuelSalesItem {Amount = FuelCost, Grade = Grade, GradeName = Grade3, Quantity = FuelQuantity}
        };

        public static readonly IList<JournalCarWashSalesItem> ExpectedCarWashSales = new List<JournalCarWashSalesItem>
        {
            new JournalCarWashSalesItem
            {
                ProgramId = 1,
                WashName = "Wash One",
                Amount = CarWashCost,
                Quantity = CarWashQuantity,
                Category = 98,
                Subcategory = 1
            }
        };

        public static readonly string ExpectedRetalixPosTransactionStart =
            $"TRANSACTION,{Pump},{Hose},{Grade},{Grade3},{FuelProductCode3},{Price},{OptNumber},{TransactionNumber},{CardNumber},";

        public static readonly string ExpectedRetalixPosTransactionEnd =
            $",,{FuelCost + CarWashCost},0,,0,{FuelQuantity},,1,0,0,0,1,{TicketNumber},{CarWashCost},1,{Amount},0,,,,0,0,0,0,0,0,0,{CardProductName},END";


        public static readonly XElement ExpectedGetSoftwareResponse = HydraOptMessage.ConstructGetSoftwareResponse(OptId1, HydraId,
            "Success", new Software("OPTApp", Convert.ToBase64String(Encoding.ASCII.GetBytes("Software"))));

        public static readonly XElement ExpectedGetConfigResponse = HydraOptMessage.ConstructGetConfigResponse(OptId1, HydraId, "Success",
            new configType
            {
                site = new site { vatNumber = "", name = "", currencyCodeSpecified = true, currencyCode = 826 },
                opt = new opt
                {
                    mode = (int)OptModeType.OptModeOpt,
                    modeSpecified = true,
                    receiptLayoutMode = ReceiptLayoutMode,
                    receiptLayoutModeSpecified = true,
                    predefinedAmounts = new amount[0],
                    mixedModeKioskTriggerModeSpecified = true,
                    mixedModeKioskTriggerMode = 1
                },
                pumps = new pump[]
                {
                    new pump
                    {
                        number = 1,
                        numberSpecified = true,
                        tid = Tid1,
                        transactionNumber = 100345,
                        transactionNumberSpecified = true
                    },
                    new pump
                    {
                        number = 2,
                        numberSpecified = true,
                        tid = Tid2,
                        transactionNumber = 101345,
                        transactionNumberSpecified = true
                    }
                },
                hydraOpt = new hydraOpt
                {
                    id = OptId1,
                    inbound = new host { ip = "***************", port = 12342 },
                    outbound = new host { ip = "***************", port = 12341 },
                    heartbeat = new host { ip = "***************", port = 12343 },
                    media = new host { ip = "***************", port = 12347 }
                },
                esocket = new host[] { new host { ip = "***************", port = 12351 }, new host { ip = "***************", port = 12352 } },
                cards = new cards
                {
                    aids = new aid[0],
                    cless_aids = new cless_aid[0],
                    capks = new capk[0],
                    fuelcards = new fuelcard[0],
                    tariffMapping = new gradeMapping[]
                    {
                        new gradeMapping {grade = 1, productCode = FuelProductCode1},
                        new gradeMapping {grade = 2, productCode = FuelProductCode2},
                        new gradeMapping {grade = 3, productCode = FuelProductCode3}
                    },
                    discountCards = new discountRange[0],
                    localAccounts = new localAccountCustomer[0]
                },
                loyalty = new loyalty(),
                washlink = new wash[]
                {
                    new wash {programId = 1, productCode = WashProductCode1, description = "Wash One", price = "200", vatRate = "20"},
                    new wash {programId = 2, productCode = WashProductCode2, description = "Wash Two", price = "300", vatRate = "20"}
                }
            });

        public static readonly XElement ExpectedGetWhitelistResponse = HydraOptMessage.ConstructGetWhitelistResponse(OptId1, HydraId,
            "Success", new Whitelist(new List<WhitelistFile>
            {
                new WhitelistFile("whitelist.txt", Convert.ToBase64String(Encoding.ASCII.GetBytes("Whitelist")))
            }));

        public static readonly XElement ExpectedGetMediaFilesListResponse =
            HydraOptMessage.ConstructGetMediaFilesListResponse(OptId1, HydraId, "Success", new FilesList(new List<string>()));

        // ReSharper disable once UnusedMember.Global
        public DatabaseFixture()
        {
            var configurationManager = new ConfigurationManagerWrapper();
            IHtecLogger logger = Substitute.For<IHtecLogger>();
            ITelemetryWorker telemetryWorker = Substitute.For<ITelemetryWorker>();
            PumpTid pumpTid1 = new PumpTid(1, Tid1, OptId1, false, false, false, false, false, false, false);
            PumpTid pumpTid2 = new PumpTid(2, Tid2, OptId1, false, false, false, false, false, false, false);
            PumpTid pumpTid3 = new PumpTid(3, "", OptId2, false, false, false, true, false, false, false);
            PumpTid pumpTid4 = new PumpTid(4, Tid4, "", false, false, false, false, false, false, false);
            PumpTid pumpTid5 = new PumpTid(5, "", OptId3, false, false, false, false, true, false, false);
            OptMode optMode1 = new OptMode(OptId3, true);
            HydraDbExecutor = Substitute.For<IDbExecutor>();
            IDbExecutorFactory hydraDbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            hydraDbExecutorFactory.CreateExecutor().Returns(HydraDbExecutor);
            PaymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            CacheHelper = Substitute.For<ICacheHelper>();
            CacheHelper.GetCachedItem(cacheConstants.ConfigKeySuffixEndPoints, $"{cacheConstants.CachedItemHydraOpt}{HydraId}", Arg.Any<Func<OptEndPoints>>()).Returns(OptEndPoints);
            var siteInfo = new SiteInfo(1, "Site Name", "VAT Number", true, true, 100, true, 100, 200, 0);
            CacheHelper.GetCachedItem(cacheConstants.ConfigKeySuffixHydraDb, cacheConstants.CategoryNameSiteInfo, Arg.Any<Func<SiteInfo>>()).Returns(siteInfo);

            var configurationRepository = Substitute.For<IConfigurationRepository>();
            HydraDb = new Forecourt.Common.HydraDbClasses.HydraDb(hydraDbExecutorFactory, logger, telemetryWorker, new ConfigurationManagerWrapper(), configurationRepository, CacheHelper);

            // TODO:
            var advConfig = new AdvancedConfig(HydraDb, logger, configurationManager);
            CacheHelper.GetCachedItem(cacheConstants.ConfigKeySuffixHydraDb, cacheConstants.CachedItemAdvancedConfig, Arg.Any<Func<AdvancedConfig>>()).Returns(advConfig);

            PaymentConfig.TermProcCategory.Returns(new TermProcCategory("", "", ""));
            PaymentConfig.AllPumpTids.Returns(new List<TermId>
            {
                new TermId(Tid1, "345"),
                new TermId(Tid2, "1345"),
                new TermId(Tid3, "2345"),
                new TermId(Tid4, "3345")
            });
            HydraDbExecutor.Query<TariffMapping>("GetTariffMappings", commandType: CommandType.StoredProcedure).Returns(
                new List<TariffMapping>
                {
                    new TariffMapping(1, FuelProductCode1, false),
                    new TariffMapping(2, FuelProductCode2, false),
                    new TariffMapping(3, FuelProductCode3, false)
                });
            HydraDbExecutor.Query<Wash>("GetWashes", commandType: CommandType.StoredProcedure).Returns(new List<Wash>
            {
                new Wash(1, WashProductCode1, "Wash One", "200", "20", 98, 1),
                new Wash(2, WashProductCode2, "Wash Two", "300", "20", 98, 2)
            });
            HydraDbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(new List<OptEndPoints> { OptEndPoints });
            HydraDbExecutor.Query<AnprEndPoint>("GetANPREndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<AnprEndPoint> { AnprEndPoint });
            HydraDbExecutor.Query<PumpEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { PumpEndPoint });
            HydraDbExecutor.Query<CarWashEndPoint>("GetCarWashEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<CarWashEndPoint> { CarWashEndPoint });
            HydraDbExecutor.Query<TankGaugeEndPoint>("GetTankGaugeEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<TankGaugeEndPoint> { TankGaugeEndPoint });
            HydraDbExecutor.Query<ESocketEndPoint>("GetESocketEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(new List<ESocketEndPoint> { EsocketEndPoint1, EsocketEndPoint2 });
            HydraDbExecutor.Query<PumpTid>("GetPumpTids", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpTid> { pumpTid1, pumpTid2, pumpTid3, pumpTid4, pumpTid5 });
            HydraDbExecutor.Query<OptMode>("GetOPTMode", commandType: CommandType.StoredProcedure).Returns(new List<OptMode> { optMode1 });
            HydraDbExecutor.Query<GradeName>("GetGradeNames", commandType: CommandType.StoredProcedure).Returns(new List<GradeName>
            {
                new GradeName(1, Grade1, VatRate),
                new GradeName(2, Grade2, VatRate),
                new GradeName(3, Grade3, VatRate)
            });
            HydraDbExecutor.Query<AllFileLocations>("GetFileLocations", commandType: CommandType.StoredProcedure).Returns(
                new List<AllFileLocations>
                {
                    new AllFileLocations("", "", WhitelistDirectory, "", SoftwareDirectory, "", "", "", "", "", "", "", "", "", "", "", "",
                        "", true, "", "", "", false, false, false, false)
                });
            HydraDbExecutor.Query<int>("GetReceiptLayoutMode", commandType: CommandType.StoredProcedure)
                .Returns(new List<int> { ReceiptLayoutMode });
            HydraDbExecutor.Query<FuellingInfo>("GetFuelling", commandType: CommandType.StoredProcedure)
                .Returns(new List<FuellingInfo> { new FuellingInfo(true, 0, 0, 0, 0, 0) });
            HydraDbExecutor.Query<DomsInfo>("GetDomsInfo", commandType: CommandType.StoredProcedure)
                .Returns(new List<DomsInfo> { new DomsInfo("", "", false, false) });

            //var siteInfo = new SiteInfo(1, "Site Name", "VAT Number", true, true, 100, true, 100, 200, 0);
            //HydraDb.GetSiteInfo().Returns(siteInfo);
            //HydraDb.FetchEndPoints(Arg.Any<string>()).ReturnsForAnyArgs(OptEndPoints);
        }

        public void Dispose()
        {
        }
    }

    public class DatabaseFixtureWithOptPayment : DatabaseFixture
    {
        // ReSharper disable once UnusedMember.Global
        public DatabaseFixtureWithOptPayment()
        {
            HydraDbExecutor
                .Query<PumpDelivered>("GetPumpDelivered",
                    Arg.Is<object>(x => (byte)x.GetType().GetProperty("pump").GetValue(x, null) == Pump),
                    commandType: CommandType.StoredProcedure).Returns(new List<PumpDelivered>
                    {new PumpDelivered(Pump, true, false, 0, 0, 0, "", 0, 0, 0, 0, 0, 0)});
        }
    }

    public class DatabaseFixtureWithDelivered : DatabaseFixture
    {
        // ReSharper disable once UnusedMember.Global
        public DatabaseFixtureWithDelivered()
        {
            HydraDbExecutor
                .Query<PumpDelivered>("GetPumpDelivered",
                    Arg.Is<object>(x => (byte)x.GetType().GetProperty("pump").GetValue(x, null) == Pump),
                    commandType: CommandType.StoredProcedure).Returns(new List<PumpDelivered>
                {
                    new PumpDelivered(Pump, false, true, Grade, FuelQuantity, FuelCost, Grade3, Price, FuelNetAmount, FuelVatAmount, VatRate, 0, 0)
                });
        }
    }

    public class DatabaseFixtureWithReload : DatabaseFixture
    {
        public static AnprEndPoint AnprEndPoint2 { get; } = new AnprEndPoint("***************", 12348);
        public static CarWashEndPoint CarWashEndPoint2 { get; } = new CarWashEndPoint("***************", 12340);

        // ReSharper disable once UnusedMember.Global
        public DatabaseFixtureWithReload()
        {
            HydraDbExecutor.Query<AnprEndPoint>("GetANPREndPoint", commandType: CommandType.StoredProcedure).Returns(
                new List<AnprEndPoint> { AnprEndPoint }, new List<AnprEndPoint> { AnprEndPoint }, new List<AnprEndPoint> { AnprEndPoint2 });
            HydraDbExecutor.Query<CarWashEndPoint>("GetCarWashEndPoint", commandType: CommandType.StoredProcedure).Returns(
                new List<CarWashEndPoint> { CarWashEndPoint }, new List<CarWashEndPoint> { CarWashEndPoint },
                new List<CarWashEndPoint> { CarWashEndPoint2 });
        }
    }

    public class DatabaseFixtureWithMixedModeOpt : DatabaseFixture
    {
        // ReSharper disable once UnusedMember.Global
        public DatabaseFixtureWithMixedModeOpt()
        {
            PumpTid pumpTid1 = new PumpTid(1, Tid1, OptId1, false, true, false, true, false, false, false);
            HydraDbExecutor.Query<PumpTid>("GetPumpTids", commandType: CommandType.StoredProcedure).Returns(new List<PumpTid> { pumpTid1 });
        }
    }

    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class WorkerTests : IDisposable, IClassFixture<DatabaseFixture>
    {
        protected const string HydraId = "Hydra 1";
        protected readonly DatabaseFixture Db;

        protected readonly IFromOptWorker OptWorker;
        protected readonly IControllerWorker ControllerWorker;
        protected readonly IHydraPosWorker HydraPosWorker;
        protected readonly IRetalixPosWorker RetalixPosWorker;
        protected readonly IThirdPartyPosWorker ThirdPartyPosWorker;
        protected readonly IMediaChannelWorker MediaChannelWorker;
        protected readonly IUpdateWorker UpdateWorker;
        protected readonly IConfigUpdateWorker ConfigUpdateWorker;
        protected readonly ILocalAccountWorker LocalAccountWorker;
        protected readonly IDomsWorker DomsWorker;
        protected readonly IAnprWorker AnprWorker;
        protected readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOutWorker;
        protected readonly ISecAuthIntegratorInTransient<IMessageTracking> _secAuthInWorker;
        protected readonly ICarWashWorker CarWashWorker;
        protected readonly ITankGaugeWorker TankGaugeWorker;
        protected readonly IHydraMobileWorker HydraMobileWorker;
        protected readonly IPumpWorker HscWorker;
        protected readonly IToOptWorker ToOptProxy;
        protected readonly IOptHeartbeatWorker HeartbeatProxy;
        private readonly ILoggingHelper _loggingHelper;
        private readonly IConfigurationManager _configurationManager;
        private readonly NameValueCollection _appSettings;
        private readonly ITimerFactory _timerFactory;
        private readonly IFileSystem _fileSystem;
        protected readonly IHydraTransactionFile TransactionFile;
        protected readonly IRetalixTransactionFile RetalixTransactionFile;
        protected readonly IPosIntegratorInMode<IMessageTracking> _posInModeWorker;
        protected readonly IPumpController _siteControllerActions;
        protected readonly IPumpControllerFactory _siteControllerFactory;
        protected readonly ITankGaugeControllerFactory _tankGaugeControllerFactory;

        protected readonly IListenerConnectionThread<XElement> ConnectionThreadToOpt;
        protected readonly IListenerConnectionThread<XElement> ConnectionThreadFromOpt;
        protected readonly IListenerConnectionThread<XElement> ConnectionThreadHeartbeat;
        protected readonly IListenerConnectionThread<string> ConnectionThreadHydraPos;
        protected readonly IListenerConnectionThread<string> ConnectionThreadRetalixPos;
        protected readonly IListenerConnectionThread<string> ConnectionThreadThirdPartyPos;
        protected readonly IListenerConnectionThread<MediaMessage> ConnectionThreadMediaChannel;
        protected readonly IClientConnectionThread<string> ConnectionThreadAnpr;
        protected readonly IClientConnectionThread<string> ConnectionThreadCarWash;
        protected readonly IClientConnectionThread<string> ConnectionThreadTankGauge;
        protected readonly IClientConnectionThread<XElement> ConnectionThreadHydraMobile;
        protected readonly IJournalWorker JournalWorker;
        protected readonly ITelemetryWorker TelemetryWorker;
        protected readonly ISiteController SiteController;
        protected readonly IConfigurationRepository ConfigurationRepository;
        protected readonly ICore Core;
        protected readonly IInfoMessagesConfig _infoMessagesConfig;

        protected readonly IList<XElement> ToOptSoftwarePendingElements = new List<XElement>();
        protected readonly IList<XElement> ToOptConfigPendingElements = new List<XElement>();
        protected readonly IList<XElement> ToOptWhitelistPendingElements = new List<XElement>();
        protected readonly IList<XElement> ToOptMediaUpdatePendingElements = new List<XElement>();
        protected readonly IList<XElement> ToOptRequestLogFileElements = new List<XElement>();
        protected readonly IList<XElement> ToOptNozzleUpElements = new List<XElement>();
        protected readonly IList<XElement> ToOptNozzleDownElements = new List<XElement>();
        protected readonly IList<XElement> ToOptDeliveringElements = new List<XElement>();
        protected readonly IList<XElement> ToOptDeliveredElements = new List<XElement>();
        protected readonly IList<XElement> ToOptTakeFuelElements = new List<XElement>();
        protected readonly IList<XElement> ToOptPaidAtKioskElements = new List<XElement>();
        protected readonly IList<XElement> ToOptModeChangeOptElements = new List<XElement>();
        protected readonly IList<XElement> ToOptModeChangeMixedElements = new List<XElement>();
        protected readonly IList<XElement> ToOptModeChangeKioskOnlyElements = new List<XElement>();
        protected readonly IList<XElement> ToOptPumpStateChangedKioskOnlyElements = new List<XElement>();
        protected readonly IList<XElement> ToOptPumpStateChangedOpenElements = new List<XElement>();
        protected readonly IList<XElement> ToOptPumpStateChangedMixedElements = new List<XElement>();
        protected readonly IList<XElement> ToOptPumpStateChangedClosedElements = new List<XElement>();
        protected readonly IList<XElement> ToOptEndOfDayElements = new List<XElement>();
        protected readonly IList<XElement> ToOptSignInRequiredElements = new List<XElement>();
        protected readonly IList<int> ToOptIds = new List<int>();
        protected readonly IList<XElement> ToUnknownOptHeartbeatElements = new List<XElement>();
        protected readonly IList<string> HydraPosStrings = new List<string>();
        protected readonly IList<HscPumpCommand> HscElements = new List<HscPumpCommand>();
        protected readonly IList<int> AnprStopInts = new List<int>();
        protected readonly IList<string> AnprStopStrings = new List<string>();
        protected readonly IList<int> AnprUpdateInts = new List<int>();
        protected readonly IList<string> AnprUpdateStrings = new List<string>();
        protected readonly IList<string> AnprStrings = new List<string>();
        protected readonly IList<string> RetalixPosTransactions = new List<string>();

        protected readonly List<JournalFuelSalesItem> FuelSales = new List<JournalFuelSalesItem>();
        protected readonly List<JournalCarWashSalesItem> CarWashSales = new List<JournalCarWashSalesItem>();
        protected readonly List<JournalOtherSalesItem> OtherSales = new List<JournalOtherSalesItem>();
        protected readonly IList<JournalTotalSalesItem> TotalSales = new List<JournalTotalSalesItem>();
        protected readonly IList<JournalDiscountItem> Discounts = new List<JournalDiscountItem>();
        protected readonly IList<JournalLocalAccountItem> LocalAccounts = new List<JournalLocalAccountItem>();
        protected readonly IList<string> TxnNumbers = new List<string>();

        protected readonly IList<object> StoredOptPaymentList = new List<object>();
        protected readonly IList<object> ClearedOptPaymentList = new List<object>();
        protected readonly IList<object> StoredDeliveredList = new List<object>();
        protected readonly IList<object> ClearedDeliveredList = new List<object>();

        protected readonly PushChangeDelegate PushChangeDelegate;
        protected readonly IPrinterHelper<IMessageTracking> PrinterHelper;
        protected readonly IPosIntegratorOut<IMessageTracking> PosOut;
        protected readonly IPosIntegratorInMode<IMessageTracking> PosInMode;
        protected readonly Helpers.Interfaces.IFileVersionInfoHelper _fileVersionInfoHelper;
        protected readonly IMessageBroker _messageBroker;
        protected readonly ISecAuthIntegratorInTransient<IMessageTracking> SecAuthInWorker;

        public WorkerTests(DatabaseFixture db)
        {
            IHtecLogger logger = Substitute.For<IHtecLogger>();
            IHtecLogManager logManager = Substitute.For<IHtecLogManager>();
            _appSettings = new NameValueCollection();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(_appSettings);
            _timerFactory = Substitute.For<ITimerFactory>();
            _fileSystem = Substitute.For<IFileSystem>();

            var retalixTransactionFile = Substitute.For<IRetalixTransactionFile>();
            Db = db;
            ConnectionThreadToOpt = Substitute.For<IListenerConnectionThread<XElement>>();
            ConnectionThreadFromOpt = Substitute.For<IListenerConnectionThread<XElement>>();
            ConnectionThreadHeartbeat = Substitute.For<IListenerConnectionThread<XElement>>();
            ConnectionThreadHydraPos = Substitute.For<IListenerConnectionThread<string>>();
            ConnectionThreadRetalixPos = Substitute.For<IListenerConnectionThread<string>>();
            ConnectionThreadThirdPartyPos = Substitute.For<IListenerConnectionThread<string>>();
            ConnectionThreadMediaChannel = Substitute.For<IListenerConnectionThread<MediaMessage>>();
            ConnectionThreadAnpr = Substitute.For<IClientConnectionThread<string>>();
            ConnectionThreadCarWash = Substitute.For<IClientConnectionThread<string>>();
            ConnectionThreadTankGauge = Substitute.For<IClientConnectionThread<string>>();
            ConnectionThreadHydraMobile = Substitute.For<IClientConnectionThread<XElement>>();
            JournalWorker = Substitute.For<IJournalWorker>();
            TelemetryWorker = Substitute.For<ITelemetryWorker>();
            SiteController = Substitute.For<ISiteController>();
            TransactionFile = Substitute.For<IHydraTransactionFile>();
            RetalixTransactionFile = Substitute.For<IRetalixTransactionFile>();
            ConfigurationRepository = Substitute.For<IConfigurationRepository>();
            Core = Substitute.For<ICore>();

            PushChangeDelegate = Substitute.For<PushChangeDelegate>();
            PrinterHelper = Substitute.For<IPrinterHelper<IMessageTracking>>();
            PosOut = Substitute.For<IPosIntegratorOut<IMessageTracking>>();
            PosInMode = Substitute.For<IPosIntegratorInMode<IMessageTracking>>();
            SecAuthInWorker = Substitute.For<ISecAuthIntegratorInTransient<IMessageTracking>>();

            ConnectionThreadToOpt.Send(Arg.Do<XElement>(x =>
            {
                switch (x.Element("Request")?.Attribute("Type")?.Value)
                {
                    case "Notification":
                        switch (x.Element("Request")?.Element("Notification")?.Attribute("Type")?.Value)
                        {
                            case "SoftwarePending":
                                ToOptSoftwarePendingElements.Add(x);
                                break;
                            case "ConfigPending":
                                ToOptConfigPendingElements.Add(x);
                                break;
                            case "WhitelistPending":
                                ToOptWhitelistPendingElements.Add(x);
                                break;
                            case "MediaUpdatePending":
                                ToOptMediaUpdatePendingElements.Add(x);
                                break;
                            case "RequestLogFile":
                                ToOptRequestLogFileElements.Add(x);
                                break;
                            case "NozzleUp":
                                ToOptNozzleUpElements.Add(x);
                                break;
                            case "NozzleDown":
                                ToOptNozzleDownElements.Add(x);
                                break;
                            case "Delivering":
                                ToOptDeliveringElements.Add(x);
                                break;
                            case "Delivered":
                                ToOptDeliveredElements.Add(x);
                                break;
                            case "TakeFuel":
                                ToOptTakeFuelElements.Add(x);
                                break;
                            case "PaidAtKiosk":
                                ToOptPaidAtKioskElements.Add(x);
                                break;
                            case "ModeChange":
                                switch (x.Element("Request")?.Element("Notification")?.Element("ModeChange")?.Attribute("Mode")?.Value)
                                {
                                    case "Opt":
                                        ToOptModeChangeOptElements.Add(x);
                                        break;
                                    case "Mixed":
                                        ToOptModeChangeMixedElements.Add(x);
                                        break;
                                    case "KioskOnly":
                                        ToOptModeChangeKioskOnlyElements.Add(x);
                                        break;
                                }

                                break;
                            case "PumpStateChanged":
                                switch (x.Element("Request")?.Element("Notification")?.Element("PumpState")?.Attribute("State")?.Value)
                                {
                                    case "KioskOnly":
                                        ToOptPumpStateChangedKioskOnlyElements.Add(x);
                                        break;
                                    case "Open":
                                        ToOptPumpStateChangedOpenElements.Add(x);
                                        break;
                                    case "Mixed":
                                        ToOptPumpStateChangedMixedElements.Add(x);
                                        break;
                                    case "Closed":
                                        ToOptPumpStateChangedClosedElements.Add(x);
                                        break;
                                }

                                break;
                            case "EndOfDay":
                                ToOptEndOfDayElements.Add(x);
                                break;
                            case "SignInRequired":
                                ToOptSignInRequiredElements.Add(x);
                                break;
                        }

                        break;
                }
            }), string.Empty);

            ConnectionThreadToOpt.Send(Arg.Do<XElement>(x =>
            {
                switch (x.Element("Request")?.Attribute("Type")?.Value)
                {
                    case "Heartbeat":
                        ToUnknownOptHeartbeatElements.Add(x);
                        break;
                }
            }), "");
            ConnectionThreadHydraPos.Send(Arg.Do<byte[]>(x => HydraPosStrings.Add(BitConverter.ToString(x))), Arg.Any<string>());
            SiteController.Command(Arg.Do<HscPumpCommand>(x => HscElements.Add(x)));
            SiteController.ANPR_Stop(Arg.Do<int>(x => AnprStopInts.Add(x)), Arg.Do<string>(x => AnprStopStrings.Add(x)));
            SiteController.ANPR_Update(Arg.Do<int>(x => AnprUpdateInts.Add(x)), Arg.Do<string>(x => AnprUpdateStrings.Add(x)));
            ConnectionThreadAnpr.Send(Arg.Do<string>(x => AnprStrings.Add(x)), Arg.Any<string>());
            ConnectionThreadRetalixPos.WhenForAnyArgs(x => x.Send(Arg.Any<string>(), Arg.Any<IPAddress>(), out _, Arg.Any<string>())).Do(
                x =>
                {
                    RetalixPosTransactions.Add((string)x[0]);
                    x[2] = new List<IPAddress>();
                });
            JournalWorker.When(x => x.WriteSalesItems(Arg.Any<JournalTotalSalesItem>(), Arg.Any<IList<JournalFuelSalesItem>>(),
                Arg.Any<IList<JournalCarWashSalesItem>>(), Arg.Any<IList<JournalOtherSalesItem>>(), Arg.Any<JournalDiscountItem>(),
                Arg.Any<JournalLocalAccountItem>(), Arg.Any<string>(), out _)).Do(x =>
                {
                    TotalSales.Add((JournalTotalSalesItem)x[0]);
                    FuelSales.AddRange((IList<JournalFuelSalesItem>)x[1]);
                    CarWashSales.AddRange((IList<JournalCarWashSalesItem>)x[2]);
                    OtherSales.AddRange((IList<JournalOtherSalesItem>)x[3]);
                    Discounts.Add((JournalDiscountItem)x[4]);
                    LocalAccounts.Add((JournalLocalAccountItem)x[5]);
                    TxnNumbers.Add((string)x[6]);
                    x[7] = DatabaseFixture.TransactionNumber;
                });
            db.HydraDbExecutor.Execute("SetPumpOPTPayment", Arg.Do<object>(x => StoredOptPaymentList.Add(x)), null, Arg.Any<int?>(),
                Arg.Any<CommandType?>());
            db.HydraDbExecutor.Execute("ClearPumpOPTPayment", Arg.Do<object>(x => ClearedOptPaymentList.Add(x)), null, Arg.Any<int?>(),
                Arg.Any<CommandType?>());
            db.HydraDbExecutor.Execute("SetPumpDelivered", Arg.Do<object>(x => StoredDeliveredList.Add(x)), null, Arg.Any<int?>(),
                Arg.Any<CommandType?>());
            db.HydraDbExecutor.Execute("ClearPumpDelivered", Arg.Do<object>(x => ClearedDeliveredList.Add(x)), null, Arg.Any<int?>(),
                Arg.Any<CommandType?>());

            var allOpts = new OptCollection(logManager, "Opt", db.HydraDb, _configurationManager, Substitute.For<IPrinterHelper<IMessageTracking>>(), Substitute.For<IReceiptHelper>());
            var allPumps = new PumpCollection(logManager, "Pump", db.HydraDb, allOpts, _configurationManager);
            var contactlessProperties = new ContactlessProperties(logger);
            _fileVersionInfoHelper = Substitute.For<Helpers.Interfaces.IFileVersionInfoHelper>();
            _messageBroker = new MessageBroker(logManager, _configurationManager);
            _loggingHelper = Substitute.For<ILoggingHelper>();
            _posInModeWorker = Substitute.For<IPosIntegratorInMode<IMessageTracking>>();
            _siteControllerActions = Substitute.For<IPumpController>();
            _siteControllerFactory = Substitute.For<IPumpControllerFactory>();
            _tankGaugeControllerFactory = Substitute.For<ITankGaugeControllerFactory>();
            _secAuthOutWorker = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();
            _secAuthInWorker = Substitute.For<ISecAuthIntegratorInTransient<IMessageTracking>>();
            _infoMessagesConfig = Substitute.For<IInfoMessagesConfig>();

            DomsWorker = new DomsWorker(db.HydraDb, logger, JournalWorker, _configurationManager, _timerFactory);
            TankGaugeWorker = new TankGaugeWorker(_tankGaugeControllerFactory, TelemetryWorker, JournalWorker, db.HydraDb, logger, _configurationManager, _timerFactory);
            HscWorker = new PumpWorker(_siteControllerFactory, JournalWorker, _messageBroker, db.HydraDb, logManager, _configurationManager, _timerFactory, allPumps, Substitute.For<IGradeHelper>(), _fileVersionInfoHelper);
            AnprWorker = new AnprWorker(TelemetryWorker, db.HydraDb, SecAuthInWorker, allPumps, logger, ConnectionThreadAnpr);
            CarWashWorker = new CarWashWorker(TelemetryWorker, db.HydraDb, logger, ConnectionThreadCarWash, _configurationManager, _timerFactory);
            HydraMobileWorker = new HydraMobileFullWorker(logManager, db.HydraDb, _configurationManager, TelemetryWorker, ConnectionThreadHydraMobile, Substitute.For<IPosIntegratorOutTransient<IMessageTracking>>(),
                Substitute.For<IJournalWorkerReceipt>(), Substitute.For<IBosIntegratorOut<IMessageTracking>>(), Substitute.For<IOptCollection>(), Substitute.For<IPumpCollection>(), Substitute.For<IGradeHelper>(), 
                Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>(),
                Substitute.For<ITimerFactory>());
            HydraPosWorker = new HydraPosWorker(JournalWorker, TelemetryWorker, ConnectionThreadHydraPos, db.HydraDb, allPumps, logger, _configurationManager, PrinterHelper, PosOut, PosInMode);
            RetalixPosWorker = new RetalixPosWorker(db.HydraDb, logManager, TelemetryWorker, ConnectionThreadRetalixPos, _configurationManager, HydraPosWorker, null);
            ThirdPartyPosWorker = new ThirdPartyPosWorker(db.HydraDb, HscWorker, allPumps, logger, TelemetryWorker, ConnectionThreadThirdPartyPos);
            MediaChannelWorker = new MediaChannelWorker(logger, db.HydraDb, TelemetryWorker, ConnectionThreadMediaChannel, new ConfigurationManagerWrapper(), _fileSystem, allOpts);
            LocalAccountWorker = new LocalAccountWorker(db.HydraDb, logger);
            var timerFactory = Substitute.For<ITimerFactory>();
            var configurationManager = Substitute.For<IConfigurationManager>();
            UpdateWorker = new UpdateWorker(db.HydraDb, logger, JournalWorker, TransactionFile, LocalAccountWorker, configurationManager, timerFactory);
            ConfigUpdateWorker = new ConfigUpdateWorker(db.HydraDb, db.PaymentConfig, logger, _configurationManager, _timerFactory,_fileSystem);
            ControllerWorker = new ControllerWorker((HydraPosWorker, HydraMobileWorker), MediaChannelWorker, _secAuthInWorker, _secAuthOutWorker,
                CarWashWorker, TankGaugeWorker, _posInModeWorker, HydraMobileWorker, HscWorker, HscWorker, JournalWorker, UpdateWorker, ConfigUpdateWorker,
                LocalAccountWorker, allOpts, allPumps, db.HydraDb, db.PaymentConfig, TransactionFile, RetalixTransactionFile,
                logger, "Hydra", _loggingHelper, _configurationManager, TelemetryWorker, _timerFactory, _infoMessagesConfig);
            ControllerWorker.RegisterWorker(_messageBroker);

            ToOptProxy = new ToOptWorker(logManager, TelemetryWorker, ConnectionThreadToOpt, db.HydraDb, _configurationManager, allPumps, allOpts, ControllerWorker, HydraPosWorker);
            HeartbeatProxy = new OptHeartbeatWorker(logManager, TelemetryWorker, ConnectionThreadHeartbeat, db.HydraDb, _configurationManager, allPumps, allOpts, ControllerWorker, HydraPosWorker);
            OptWorker = new FromOptWorker(ToOptProxy, HeartbeatProxy, ConnectionThreadFromOpt, ControllerWorker, HscWorker, _secAuthInWorker, _secAuthOutWorker,
                CarWashWorker, HydraPosWorker, HydraPosWorker, MediaChannelWorker, JournalWorker, TelemetryWorker,
                ConfigUpdateWorker, LocalAccountWorker, db.HydraDb, db.PaymentConfig, allPumps, allOpts,
                logManager, DatabaseFixture.VersionString, _configurationManager, _timerFactory, Substitute.For<IGradeHelper>(), Substitute.For<IVatCalculator>());
            
            ControllerWorker.Start(HydraId);
            OptWorker.Start(HydraId);
            HscWorker.Start();
            HydraPosWorker.Start(HydraId);
            RetalixPosWorker.Start(HydraId);
            ThirdPartyPosWorker.Start(HydraId);
            AnprWorker.Start();
            CarWashWorker.Start();
            TankGaugeWorker.Start();
            Thread.Sleep(2000);
        }

        #region Private Message Construction

        protected static string ConstructHydraPosUpdate
        (byte pump, bool anyOpt, bool noKiosk, bool autoAuth, HydraPosPumpState state, bool optOffline, bool inUse, bool kioskUse,
            uint cashLimit)
        {
            List<byte> messageBytes = new List<byte>();
            messageBytes.AddRange(new byte[] { (byte)"OPTDATA".Length });
            messageBytes.AddRange(Encoding.ASCII.GetBytes("OPTDATA"));
            messageBytes.Add(pump);
            messageBytes.Add((byte)(anyOpt ? 1 : 0));
            messageBytes.Add((byte)(noKiosk ? 1 : 0));
            messageBytes.Add((byte)(autoAuth ? 1 : 0));
            messageBytes.AddRange(BitConverter.GetBytes((ushort)state));
            messageBytes.Add((byte)(optOffline ? 1 : 0));
            messageBytes.Add((byte)(inUse ? 1 : 0));
            messageBytes.Add((byte)(kioskUse ? 1 : 0));
            messageBytes.AddRange(new byte[] { 0, 0, 0 });
            messageBytes.AddRange(BitConverter.GetBytes(cashLimit));
            return BitConverter.ToString(messageBytes.ToArray());
        }

        #endregion

        #region IDisposable Support

        private bool _disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    ControllerWorker.Stop();
                    OptWorker.Stop();
                    HscWorker.Stop();
                    HydraPosWorker.Stop();
                    ThirdPartyPosWorker.Stop();
                    AnprWorker.Stop();
                    CarWashWorker.Stop();
                    TankGaugeWorker.Stop();
                    OptWorker.Dispose();
                    ControllerWorker.Dispose();
                    HydraPosWorker.Dispose();
                    ThirdPartyPosWorker.Dispose();
                    AnprWorker.Dispose();
                    CarWashWorker.Dispose();
                    TankGaugeWorker.Dispose();
                    HscWorker.Dispose();
                    ToOptProxy.Dispose();
                    HeartbeatProxy.Dispose();
                    CarWashWorker?.Dispose();
                }

                _disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
        }

        ~WorkerTests()
        {
            Dispose(false);
        }

        #endregion
    }

    public class WorkerTestsWithOpt : WorkerTests
    {
        protected int ToOptId;
        protected int FromOptId;
        protected int HeartbeatId;

        public WorkerTestsWithOpt(DatabaseFixture db) : base(db)
        {
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, ToOptId);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
        }
    }

    public class WorkerTestsWithOptAndSoftware : WorkerTests
    {
        protected int ToOptId;
        protected int FromOptId;
        protected int HeartbeatId;

        public WorkerTestsWithOptAndSoftware(DatabaseFixture db) : base(db)
        {
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, ToOptId);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
        }
    }

    public class WorkerTestsWithOptPayment : WorkerTestsWithOpt, IClassFixture<DatabaseFixtureWithOptPayment>
    {
        public WorkerTestsWithOptPayment(DatabaseFixtureWithOptPayment db) : base(db)
        {
        }
    }

    public class WorkerTestsWithDelivered : WorkerTestsWithOpt, IClassFixture<DatabaseFixtureWithDelivered>
    {
        public WorkerTestsWithDelivered(DatabaseFixtureWithDelivered db) : base(db)
        {
        }
    }

    public class WorkerTestsWithReload : WorkerTests, IClassFixture<DatabaseFixtureWithReload>
    {
        public WorkerTestsWithReload(DatabaseFixtureWithReload db) : base(db)
        {
        }
    }

    public class WorkerTestsWithOptAndConfig : WorkerTests
    {
        protected int ToOptId;
        protected int FromOptId;
        protected int HeartbeatId;

        public WorkerTestsWithOptAndConfig(DatabaseFixture db) : base(db)
        {
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, ToOptId);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(2000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetConfigRequest, FromOptId);
            Thread.Sleep(1000);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetWhitelistRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetMediaFilesListRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetLayoutRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(100);
        }
    }

    public class WorkerTestsWithMixedModeOptAndConfig : WorkerTests, IClassFixture<DatabaseFixtureWithMixedModeOpt>
    {
        protected int ToOptId;
        protected int FromOptId;
        protected int HeartbeatId;

        public WorkerTestsWithMixedModeOptAndConfig(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, ToOptId);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(2000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetConfigRequest, FromOptId);
            Thread.Sleep(1000);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetWhitelistRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetMediaFilesListRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.GetLayoutRequest, FromOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(100);
        }
    }

    public class WorkerTestsOne : WorkerTests
    {
        public WorkerTestsOne(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_start_uses_database_endpoints()
        {
            // Arrange

            // Act

            // Assert
            ConnectionThreadToOpt.Received(1).Start(DatabaseFixture.OptEndPoints.ToOptBindEndPoint);
            ConnectionThreadFromOpt.Received(1).Start(DatabaseFixture.OptEndPoints.FromOptBindEndPoint);
            ConnectionThreadHeartbeat.Received(1).Start(DatabaseFixture.OptEndPoints.HeartbeatBindEndPoint);
            ConnectionThreadHydraPos.Received(1).Start(DatabaseFixture.OptEndPoints.HydraPosBindEndPoint);
            ConnectionThreadThirdPartyPos.Received(1).Start(DatabaseFixture.OptEndPoints.ThirdPartyPosBindEndPoint);
            ConnectionThreadAnpr.Received(1).Start(DatabaseFixture.AnprEndPoint.EndPoint);
            ConnectionThreadCarWash.Received(1).Start(DatabaseFixture.CarWashEndPoint.EndPoint);
            SiteController.Received(1).Start(DatabaseFixture.PumpEndPoint.IpAddress, DatabaseFixture.PumpEndPoint.Port, _siteControllerActions as ISiteControllerEvents);
        }
    }

    public class WorkerTestsTwo : WorkerTests
    {
        public WorkerTestsTwo(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_controller_initial_messages()
        {
            // Arrange

            // Act

            // Assert
            PushChangeDelegate.DidNotReceiveWithAnyArgs().Invoke(EventType.InfoMessageChanged, "", "");
            PushChangeDelegate.DidNotReceive().Invoke(EventType.GenericOptConfigChanged, null);
            PushChangeDelegate.DidNotReceive().Invoke(EventType.AdvancedConfigChanged, null);
            PushChangeDelegate.DidNotReceive().Invoke(EventType.FuelPriceChanged, Arg.Any<string>());
            PushChangeDelegate.DidNotReceive().Invoke(EventType.ShiftEndChanged);
            PushChangeDelegate.DidNotReceive().Invoke(EventType.DivertDetailsChanged, null);
            PushChangeDelegate.DidNotReceive().Invoke(EventType.AboutChanged);
            PushChangeDelegate.DidNotReceive().Invoke(EventType.TransactionsChanged, null);
            PushChangeDelegate.DidNotReceive().Invoke(Arg.Any<EventType>(), Arg.Any<string>());

        }
    }

    public class WorkerTestsThree : WorkerTests
    {
        public WorkerTestsThree(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_connections_message()
        {
            // Arrange

            // Act
            var connectionsBefore = ControllerWorker.GetConnections();
            ThirdPartyPosWorker.OnConnected();
            CarWashWorker?.OnConnected();
            ((IConnectable)HscWorker).OnConnected(IPAddress.Loopback, 1259);
            Thread.Sleep(1500);
            Connections connectionsAfter = ControllerWorker.GetConnections();

            // Assert
            ConnectionThreadToOpt.Received(1).Start(DatabaseFixture.OptEndPoints.ToOptBindEndPoint);
            ConnectionThreadFromOpt.Received(1).Start(DatabaseFixture.OptEndPoints.FromOptBindEndPoint);
            ConnectionThreadHeartbeat.Received(1).Start(DatabaseFixture.OptEndPoints.HeartbeatBindEndPoint);
            ConnectionThreadHydraPos.Received(1).Start(DatabaseFixture.OptEndPoints.HydraPosBindEndPoint);
            ConnectionThreadThirdPartyPos.Received(1).Start(DatabaseFixture.OptEndPoints.ThirdPartyPosBindEndPoint);
            ConnectionThreadAnpr.Received(1).Start(DatabaseFixture.AnprEndPoint.EndPoint);
            ConnectionThreadCarWash.Received(1).Start(DatabaseFixture.CarWashEndPoint.EndPoint);
            SiteController.Received(1).Start(DatabaseFixture.PumpEndPoint.IpAddress, DatabaseFixture.PumpEndPoint.Port, _siteControllerActions as ISiteControllerEvents);

            connectionsBefore.AllOpt.Should().Be(0);
            connectionsBefore.FromOpt.Should().Be(0);
            connectionsBefore.ToOpt.Should().Be(0);
            connectionsBefore.Heartbeat.Should().Be(0);
            connectionsBefore.HydraPos.Should().Be(0);
            connectionsBefore.ThirdPartyPos.Should().Be(0);
            connectionsBefore.SecAuth.Should().BeFalse();
            connectionsBefore.CarWash.Should().BeFalse();
            connectionsBefore.SiteController.Should().BeFalse();

            connectionsAfter.AllOpt.Should().Be(0);
            connectionsAfter.FromOpt.Should().Be(0);
            connectionsAfter.ToOpt.Should().Be(0);
            connectionsAfter.Heartbeat.Should().Be(0);
            connectionsAfter.HydraPos.Should().Be(2);
            connectionsAfter.ThirdPartyPos.Should().Be(1);
            connectionsAfter.SecAuth.Should().BeTrue();
            connectionsAfter.CarWash.Should().BeTrue();
            connectionsAfter.SiteController.Should().BeTrue();
        }
    }

    public class WorkerTestsFour : WorkerTests
    {
        public WorkerTestsFour(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_incomplete_opt_connection()
        {
            // Arrange

            // Act
            Connections connectionsBefore = ControllerWorker.GetConnections();
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, 1);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, 1);
            Thread.Sleep(2000);
            Connections connectionsAfter = ControllerWorker.GetConnections();

            // Assert
            connectionsBefore.AllOpt.Should().Be(0);
            connectionsBefore.FromOpt.Should().Be(0);
            connectionsBefore.ToOpt.Should().Be(0);
            connectionsBefore.Heartbeat.Should().Be(0);

            connectionsAfter.AllOpt.Should().Be(0);
            connectionsAfter.FromOpt.Should().Be(1);
            connectionsAfter.ToOpt.Should().Be(1);
            connectionsAfter.Heartbeat.Should().Be(0);
        }
    }

    public class WorkerTestsFive : WorkerTests
    {
        public WorkerTestsFive(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_complete_opt_connection()
        {
            // Arrange

            // Act
            Connections connectionsBefore = ControllerWorker.GetConnections();
            var toOptResult = ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, 1);
            var signInResult = OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, 1);
            var heartbeatResult = HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, 1);
            DateTime timestamp = DateTime.Now;
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, 1);
            Thread.Sleep(500);
            Connections connectionsAfter = ControllerWorker.GetConnections();

            // Assert
            toOptResult.Should().BeNull();
            signInResult.Should().NotBeNull();
            XElement response = DatabaseFixture.SignInResponse;
            response.Element("Response")?.SetAttributeValue("Timestamp", $"{timestamp:yyyyMMddHHmmss}");
            signInResult.Value.Should().NotBeNull();
            signInResult.Value.Should().BeEquivalentTo(response);
            heartbeatResult.Should().NotBeNull();
            heartbeatResult.Value.Should().NotBeNull();
            heartbeatResult.Value.Should().BeEquivalentTo(DatabaseFixture.HeartbeatResponse.Response);

            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().BeEmpty();

            ToUnknownOptHeartbeatElements.Should().NotBeEmpty();
            foreach (XElement element in ToUnknownOptHeartbeatElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.UnknownHeartbeatRequest);
            }

            connectionsBefore.AllOpt.Should().Be(0);
            connectionsBefore.FromOpt.Should().Be(0);
            connectionsBefore.ToOpt.Should().Be(0);
            connectionsBefore.Heartbeat.Should().Be(0);

            connectionsAfter.AllOpt.Should().Be(1);
            connectionsAfter.FromOpt.Should().Be(1);
            connectionsAfter.ToOpt.Should().Be(1);
            connectionsAfter.Heartbeat.Should().Be(1);   
        }
    }

    public class WorkerTestsSix : WorkerTests
    {
        public WorkerTestsSix(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_response_timeout()
        {
            // Arrange

            // Act
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, 1);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, 1);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, 1);
            Thread.Sleep(4000);
            Connections connections = ControllerWorker.GetConnections();

            // Assert
            connections.AllOpt.Should().Be(0);
            connections.FromOpt.Should().Be(0);
            connections.ToOpt.Should().Be(0);
            connections.Heartbeat.Should().Be(0);
        }
    }

    public class WorkerTestsSeven : WorkerTests
    {
        public WorkerTestsSeven(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_response_not_timeout()
        {
            // Arrange

            // Act
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.HeartbeatResponse, 1);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, 1);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, 1);
            Thread.Sleep(2000);
            Connections connections = ControllerWorker.GetConnections();

            // Assert
            connections.AllOpt.Should().Be(1);
            connections.FromOpt.Should().Be(1);
            connections.ToOpt.Should().Be(1);
            connections.Heartbeat.Should().Be(1);
        }
    }

    public class WorkerTestsEight : WorkerTestsWithOptAndConfig
    {
        public WorkerTestsEight(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_heartbeat_timeout()
        {
            // Arrange

            // Act
            Thread.Sleep(11000);
            Connections connections = ControllerWorker.GetConnections();

            // Assert
            connections.AllOpt.Should().Be(0);
            connections.FromOpt.Should().Be(0);
            connections.ToOpt.Should().Be(0);
            connections.Heartbeat.Should().Be(0);
        }
    }

    public class WorkerTestsNine : WorkerTestsWithOptAndConfig
    {
        public WorkerTestsNine(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_heartbeat_not_timeout()
        {
            // Arrange

            // Act
            Thread.Sleep(6000);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            Thread.Sleep(7000);
            Connections connections = ControllerWorker.GetConnections();

            // Assert
            connections.AllOpt.Should().Be(1);
            connections.FromOpt.Should().Be(1);
            connections.ToOpt.Should().Be(1);
            connections.Heartbeat.Should().Be(1);
        }
    }

    public class WorkerTestsTen : WorkerTestsWithOpt
    {
        public WorkerTestsTen(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_hydra_pos_update()
        {
            // Arrange
            string expectedHydraPosString = ConstructHydraPosUpdate(DatabaseFixture.Pump, true, true, true, HydraPosPumpState.Idle, false,
                false, false, 10000);

            // Act

            // Assert
            HydraPosStrings.Should().Contain(expectedHydraPosString);
        }
    }

    public class WorkerTestsEleven : WorkerTestsWithOpt
    {
        public WorkerTestsEleven(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_card_inserted()
        {
            // Arrange
            var expectedAnprString = $"GetReg={DatabaseFixture.Pump}/";

            // Act
            AnprWorker.OnConnected();
            OptWorker.OnMessageReceived(DatabaseFixture.CardInsertedRequest, FromOptId);

            // Assert
            AnprStrings.Should().Contain(expectedAnprString);
        }
    }

    public class WorkerTestsTwelve : WorkerTestsWithOpt
    {
        public WorkerTestsTwelve(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_card_inserted_invalid_pump()
        {
            // Arrange
            IMessageTracking<XElement> cardInsertedOtherPumpRequest = new MessageTracking<XElement>(HydraOptMessage.ConstructOptNotificationRequest(DatabaseFixture.OptId1, HydraId,
                new Notification(NotificationType.CardInserted, DatabaseFixture.OtherPump)));

            // Act
            AnprWorker.OnConnected();
            OptWorker.OnMessageReceived(cardInsertedOtherPumpRequest, FromOptId);

            // Assert
            AnprStrings.Should().BeEmpty();
        }
    }

    public class WorkerTestsThirteen : WorkerTests
    {
        public WorkerTestsThirteen(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_kiosk_use()
        {
            // Arrange
            const string optId = DatabaseFixture.OptId2;
            const byte pump = DatabaseFixture.OtherPump;
            string expectedHydraPosString =
                ConstructHydraPosUpdate(pump, true, false, true, HydraPosPumpState.KioskUse, false, false, true, 10000);
            IMessageTracking<XElement> heartbeatOtherOptResponse =  new MessageTracking<XElement>(HydraOptMessage.ConstructHeartbeatMessage(false, optId, HydraId, "Success"));
            IMessageTracking<XElement> signInOtherOptRequest =  new MessageTracking<XElement>(HydraOptMessage.ConstructSignInMessage(true, optId, HydraId, ""));
            IMessageTracking<XElement> kioskUseRequest
                = new MessageTracking<XElement>(HydraOptMessage.ConstructOptNotificationRequest(optId, HydraId, new Notification(NotificationType.KioskUse, pump)));

            // Act
            ToOptProxy.OnMessageReceived(heartbeatOtherOptResponse, 1);
            OptWorker.OnMessageReceived(signInOtherOptRequest, 1);
            HeartbeatProxy.OnMessageReceived(heartbeatOtherOptResponse, 1);
            OptWorker.OnMessageReceived(kioskUseRequest, 1);

            // Assert
            HydraPosStrings.Should().Contain(expectedHydraPosString);
        }
    }

    public class WorkerTestsFourteen : WorkerTestsWithOptAndConfig
    {
        public WorkerTestsFourteen(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_opt_and_config()
        {
            // Arrange

            // Act

            // Assert
            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }

            ToOptMediaUpdatePendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptMediaUpdatePendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedMediaUpdatePending);
            }

            ToOptRequestLogFileElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptRequestLogFileElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedRequestLogFile);
            }
        }
    }

    public class WorkerTestsFifteen : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsFifteen(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }

        [Fact]
        public void test_hsc_request()
        {
            // Arrange

            // Act
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }

            ToOptMediaUpdatePendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptMediaUpdatePendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedMediaUpdatePending);
            }

            ToOptRequestLogFileElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptRequestLogFileElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedRequestLogFile);
            }

            ToOptNozzleUpElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleUpElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleUpNotification);
            }
        }
    }

    public class WorkerTestsSixteen : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsSixteen(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }

        [Fact]
        public void test_hsc_request_and_replace()
        {
            // Arrange

            // Act
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpIdle);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }

            ToOptNozzleUpElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleUpElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleUpNotification);
            }

            ToOptNozzleDownElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleDownElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleDownNotification);
            }
        }
    }

    public class WorkerTestsSeventeen : WorkerTestsWithOpt
    {
        public WorkerTestsSeventeen(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_approved_without_anpr()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);

            // Assert
            HscElements.Should().Contain(DatabaseFixture.HscExpectedAuth);
        }
    }

    public class WorkerTestsEighteen : WorkerTestsWithOpt
    {
        public WorkerTestsEighteen(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_approved_with_anpr_reject()
        {
            // Arrange
            const byte pump = DatabaseFixture.Pump;
            const string reg = "GEN11";
            IMessageTracking<string> anprCommand = new MessageTracking<string>($"REG={pump},{reg},Reject:");
            string expectedAnprCommand = $"GetReg={pump}/";

            // Act
            AnprWorker.OnConnected(IPAddress.Loopback);
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            AnprWorker.OnMessageReceived(anprCommand);

            // Assert
            AnprStrings.Should().Contain(expectedAnprCommand);
            AnprStopInts.Should().Contain(pump);
            AnprStopStrings.Should().Contain(reg);
            HscElements.Should().BeEmpty();
        }
    }

    public class WorkerTestsNineteen : WorkerTestsWithOpt
    {
        public WorkerTestsNineteen(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_approved_with_anpr_ok()
        {
            // Arrange
            const byte pump = DatabaseFixture.Pump;
            const string reg = "GEN11";
            IMessageTracking<string> anprCommand = new MessageTracking<string>($"REG={pump},{reg},OK:");
            string expectedAnprCommand = $"GetReg={pump}/";

            // Act
            AnprWorker.OnConnected(IPAddress.Loopback);
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            AnprWorker.OnMessageReceived(anprCommand);

            // Assert
            AnprStrings.Should().Contain(expectedAnprCommand);
            AnprUpdateInts.Should().Contain(pump);
            AnprUpdateStrings.Should().Contain(reg);
            HscElements.Should().Contain(DatabaseFixture.HscExpectedAuth);
        }
    }

    public class WorkerTestsTwenty : WorkerTestsWithOpt
    {
        public WorkerTestsTwenty(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_opt_payment_approved_with_anpr_timeout()
        {
            // Arrange
            const byte pump = DatabaseFixture.Pump;
            string expectedAnprCommand = $"GetReg={pump}/";

            // Act
            AnprWorker.OnConnected(IPAddress.Loopback);
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            Thread.Sleep(14000);

            // Assert
            AnprStrings.Should().Contain(expectedAnprCommand);
            AnprUpdateInts.Should().BeEmpty();
            AnprUpdateStrings.Should().BeEmpty();
            HscElements.Should().Contain(DatabaseFixture.HscExpectedAuth);
        }
    }

    public class WorkerTestsTwentyOne : WorkerTestsWithOpt
    {
        public WorkerTestsTwentyOne(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_approved_with_card_restriction_fail()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedWithCardRestrictionsFailRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);

            // Assert
            HscElements.Should().BeEmpty();
        }
    }

    public class WorkerTestsTwentyTwo : WorkerTestsWithOpt
    {
        public WorkerTestsTwentyTwo(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_approved_with_card_restriction_ok()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedWithCardRestrictionsOkRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);

            // Assert
            HscElements.Should().Contain(DatabaseFixture.HscExpectedAuth);
        }
    }

    public class WorkerTestsTwentyThree : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsTwentyThree(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }


        [Fact]
        public void test_opt_delivering_fuel()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            // HscElements.Should().ContainInOrder(DatabaseFixture.HscExpectedAuth, DatabaseFixture.HscExpectedClaim,
            //     DatabaseFixture.HscExpectedCashOut);
            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }

            ToOptNozzleUpElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleUpElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleUpNotification);
            }

            ToOptDeliveringElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptDeliveringElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.DeliveringNotification);
            }

            ToOptDeliveredElements.Should().NotBeEmpty();
            // foreach (XElement element in ToOptDeliveredElements)
            // {
            //     element.Should().BeEquivalentTo(DatabaseFixture.DeliveredNotification);
            // }
        }
    }

    public class WorkerTestsTwentyFour : WorkerTestsWithOpt
    {
        public WorkerTestsTwentyFour(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_opt_payment_clearance_fuel_and_car_wash()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentClearedRequest, FromOptId);

            // Assert
            TotalSales.Should().BeEquivalentTo(DatabaseFixture.ExpectedTotalSales);
            FuelSales.Should().BeEquivalentTo(DatabaseFixture.ExpectedFuelSales);
            CarWashSales.Should().BeEquivalentTo(DatabaseFixture.ExpectedCarWashSales);
            OtherSales.Should().BeEmpty();
            RetalixPosTransactions.Should().NotBeEmpty();
            RetalixPosTransactions.Should().HaveCount(1);
            foreach (string trans in RetalixPosTransactions)
            {
                trans.Length.Should().Be(DatabaseFixture.ExpectedRetalixPosTransactionStart.Length + 12 +
                                         DatabaseFixture.ExpectedRetalixPosTransactionEnd.Length);
                trans.Should().StartWith(DatabaseFixture.ExpectedRetalixPosTransactionStart);
                trans.Should().EndWith(DatabaseFixture.ExpectedRetalixPosTransactionEnd);
            }

            FromOptId.Should().Be(DatabaseFixture.OptNumber);
        }
    }

    public class WorkerTestsTwentyFive : WorkerTestsWithOpt
    {
        public WorkerTestsTwentyFive(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_opt_payment_clearance_multiple_products()
        {
            // Arrange
            const string optId = DatabaseFixture.OptId1;
            const byte pump = DatabaseFixture.Pump;
            const byte hose = DatabaseFixture.Hose;
            const byte grade2 = DatabaseFixture.Grade;
            const string grade = DatabaseFixture.Grade3;

            const string cardNumber = DatabaseFixture.CardNumber;
            const string cardProductName = DatabaseFixture.CardProductName;
            const uint fuelQuantity1 = DatabaseFixture.FuelQuantity;
            uint fuelValue1 = DatabaseFixture.FuelCost;
            const uint fuelQuantity2 = 500;
            const uint fuelValue2 = 1000;
            const uint carWashQuantity1 = DatabaseFixture.CarWashQuantity;
            const uint carWashValue1 = DatabaseFixture.CarWashCost;
            const uint carWashQuantity2 = 2;
            const uint carWashValue2 = 400;
            const string otherProductCode = "77";
            const uint otherQuantity = 1;
            const uint otherValue = 600;
            uint amountTotal = fuelValue1 + fuelValue2 + carWashValue1 + carWashValue2 + otherValue;
            ProductsList products = new ProductsList(new List<ProductItem>
            {
                new ProductItem(DatabaseFixture.FuelProductCode3, fuelQuantity1, fuelValue1),
                new ProductItem(DatabaseFixture.FuelProductCode3, fuelQuantity2, fuelValue2),
                new ProductItem(DatabaseFixture.WashProductCode1, carWashQuantity1, carWashValue1),
                new ProductItem(DatabaseFixture.WashProductCode2, carWashQuantity2, carWashValue2),
                new ProductItem(otherProductCode, otherQuantity, otherValue)
            });
            IList<JournalTotalSalesItem> expectedTotalSales = new List<JournalTotalSalesItem>
            {
                new JournalTotalSalesItem
                {
                    PumpNumber = pump,
                    Hose = hose,
                    Amount = amountTotal,
                    CardNumber = cardNumber,
                    CardProductName = cardProductName
                }
            };
            IList<JournalFuelSalesItem> expectedFuelSales = new List<JournalFuelSalesItem>
            {
                new JournalFuelSalesItem {Amount = fuelValue1, Grade = grade2, GradeName = grade, Quantity = fuelQuantity1},
                new JournalFuelSalesItem {Amount = fuelValue2, Grade = grade2, GradeName = grade, Quantity = fuelQuantity2}
            };
            IList<JournalCarWashSalesItem> expectedCarWashSales = new List<JournalCarWashSalesItem>
            {
                new JournalCarWashSalesItem
                {
                    ProgramId = 1,
                    WashName = "Wash One",
                    Amount = carWashValue1,
                    Quantity = carWashQuantity1,
                    Category = 98,
                    Subcategory = 1
                },
                new JournalCarWashSalesItem
                {
                    ProgramId = 2,
                    WashName = "Wash Two",
                    Amount = carWashValue2,
                    Quantity = carWashQuantity2,
                    Category = 98,
                    Subcategory = 2
                }
            };
            IList<JournalOtherSalesItem> expectedOtherSales = new List<JournalOtherSalesItem>
            {
                new JournalOtherSalesItem {ProductCode = otherProductCode, Amount = otherValue, Quantity = otherQuantity}
            };

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            OptWorker.OnMessageReceived(
                new MessageTracking<XElement>(HydraOptMessage.ConstructPaymentRequest(optId, HydraId,
                    new Payment(PaymentResult.Cleared, pump, amountTotal, cardNumber, cardProductName, products: products))), FromOptId);

            // Assert
            TotalSales.Should().BeEquivalentTo(expectedTotalSales);
            FuelSales.Should().BeEquivalentTo(expectedFuelSales);
            CarWashSales.Should().BeEquivalentTo(expectedCarWashSales);
            OtherSales.Should().BeEquivalentTo(expectedOtherSales);
        }
    }

    public class WorkerTestsTwentySix : WorkerTestsWithOpt
    {
        public WorkerTestsTwentySix(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_opt_store_and_clear_opt_payment_and_delivered()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            StoredOptPaymentList.Should().NotBeEmpty();
            foreach (object item in StoredOptPaymentList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be(DatabaseFixture.Pump);
            }

            ClearedOptPaymentList.Should().NotBeEmpty();
            foreach (object item in ClearedOptPaymentList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be(DatabaseFixture.Pump);
            }

            StoredDeliveredList.Should().NotBeEmpty();
            foreach (object item in StoredDeliveredList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be((int)DatabaseFixture.Pump);
                item?.GetType().GetProperty("grade")?.GetValue(item).Should().Be((int)DatabaseFixture.Grade);
                // item?.GetType().GetProperty("volume")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelQuantity);
                // item?.GetType().GetProperty("amount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelCost);
                item?.GetType().GetProperty("name")?.GetValue(item).Should().Be(DatabaseFixture.Grade3);
                item?.GetType().GetProperty("price")?.GetValue(item).Should().Be((int)DatabaseFixture.Price);
                // item?.GetType().GetProperty("netAmount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelNetAmount);
                // item?.GetType().GetProperty("vatAmount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelVatAmount);
                // item?.GetType().GetProperty("vatRate")?.GetValue(item).Should().Be(DatabaseFixture.VatRate);
            }

            ClearedDeliveredList.Should().NotBeEmpty();
            foreach (object item in ClearedDeliveredList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be(DatabaseFixture.Pump);
            }
        }
    }

    public class WorkerTestsTwentySeven : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsTwentySeven(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }


        [Fact]
        public void test_opt_store_delivered()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            StoredDeliveredList.Should().NotBeEmpty();
            foreach (object item in StoredDeliveredList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be((int)DatabaseFixture.Pump);
                item?.GetType().GetProperty("grade")?.GetValue(item).Should().Be((int)DatabaseFixture.Grade);
                // item?.GetType().GetProperty("volume")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelQuantity);
                // item?.GetType().GetProperty("amount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelCost);
                item?.GetType().GetProperty("name")?.GetValue(item).Should().Be(DatabaseFixture.Grade3);
                item?.GetType().GetProperty("price")?.GetValue(item).Should().Be((int)DatabaseFixture.Price);
                // item?.GetType().GetProperty("netAmount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelNetAmount);
                // item?.GetType().GetProperty("vatAmount")?.GetValue(item).Should().Be((long) DatabaseFixture.FuelVatAmount);
                // item?.GetType().GetProperty("vatRate")?.GetValue(item).Should().Be(DatabaseFixture.VatRate);
            }

            // ClearedDeliveredList.Should().BeEmpty();
        }
    }

    public class WorkerTestsTwentyEight : WorkerTestsWithOpt
    {
        public WorkerTestsTwentyEight(DatabaseFixture db) : base(db)
        {
        }


        [Fact]
        public void test_opt_store_opt_payment()
        {
            // Arrange

            // Act
            OptWorker.OnMessageReceived(DatabaseFixture.PaymentApprovedRequest, FromOptId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            // Assert
            StoredOptPaymentList.Should().NotBeEmpty();
            foreach (object item in StoredOptPaymentList)
            {
                item?.GetType().GetProperty("pump")?.GetValue(item).Should().Be(DatabaseFixture.Pump);
            }

            ClearedOptPaymentList.Should().BeEmpty();
        }
    }

    public class WorkerTestsTwentyNine : WorkerTestsWithDelivered
    {
        public WorkerTestsTwentyNine(DatabaseFixtureWithDelivered db) : base(db)
        {
        }


        [Fact]
        public void test_stored_delivered()
        {
            // Arrange

            // Act

            // Assert
            ToOptDeliveredElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptDeliveredElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.DeliveredNotification);
            }
        }
    }

    public class WorkerTestsThirty : WorkerTestsWithOptPayment
    {
        public WorkerTestsThirty(DatabaseFixtureWithOptPayment db) : base(db)
        {
        }


        [Fact]
        public void test_stored_opt_payment()
        {
            // Arrange

            // Act
            Thread.Sleep(1500);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(2500);

            // Assert
            ToOptDeliveredElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptDeliveredElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.DeliveredNotification);
            }
        }
    }

    public class WorkerTestsThirtyOne : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsThirtyOne(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }


        [Fact]
        public void test_queued_notifications()
        {
            // Arrange

            // Act
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpIdle);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpIdle);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(200);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(200);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(200);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(200);

            // Assert
            ToOptNozzleUpElements.Should().HaveCount(2);
            foreach (XElement element in ToOptNozzleUpElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleUpNotification);
            }

            ToOptNozzleDownElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleDownElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleDownNotification);
            }
        }
    }

    public class WorkerTestsThirtyTwo : WorkerTestsWithReload
    {
        public WorkerTestsThirtyTwo(DatabaseFixtureWithReload db) : base(db)
        {
        }

        [Fact]
        public void test_reload()
        {
            // Arrange

            // Act
            OptWorker.Restart();

            // Assert
        }
    }

    public class WorkerTestsThirtyThree : WorkerTestsWithOpt
    {
        public WorkerTestsThirtyThree(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_config_request_and_whitelist_request()
        {
            // Arrange

            // Act
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            var result = OptWorker.OnMessageReceived(DatabaseFixture.GetConfigRequest, FromOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            Thread.Sleep(2000);
            var result2 = OptWorker.OnMessageReceived(DatabaseFixture.GetWhitelistRequest, FromOptId);

            // Assert
            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetConfigResponse);

            result2.Should().NotBeNull();
            result2.Value.Should().NotBeNull();
            result2.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetWhitelistResponse);

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }
        }
    }

    public class WorkerTestsThirtyFour : WorkerTestsWithOptAndSoftware
    {
        public WorkerTestsThirtyFour(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_software_request_config_request_and_whitelist_request()
        {
            // Arrange

            // Act
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            var result = OptWorker.OnMessageReceived(DatabaseFixture.GetSoftwareRequest, FromOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            var  result2 = OptWorker.OnMessageReceived(DatabaseFixture.GetConfigRequest, FromOptId);
            Thread.Sleep(100);
            OptWorker.OnMessageReceived(DatabaseFixture.SignInRequest, FromOptId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            var result3 = OptWorker.OnMessageReceived(DatabaseFixture.GetWhitelistRequest, FromOptId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            var result4 = OptWorker.OnMessageReceived(DatabaseFixture.GetMediaFilesListRequest, FromOptId);
            Thread.Sleep(1000);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);

            // Assert
            ToOptSoftwarePendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptSoftwarePendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedSoftwarePending);
            }

            ToOptConfigPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptConfigPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedConfigPending);
            }

            ToOptWhitelistPendingElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptWhitelistPendingElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.ExpectedWhitelistPending);
            }

            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetSoftwareResponse);

            result2.Should().NotBeNull();
            result2.Value.Should().NotBeNull();
            result2.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetConfigResponse);

            result3.Should().NotBeNull();
            result3.Value.Should().NotBeNull();
            result3.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetWhitelistResponse);

            result4.Should().NotBeNull();
            result4.Value.Should().NotBeNull();
            result4.Value.Should().BeEquivalentTo(DatabaseFixture.ExpectedGetMediaFilesListResponse);
        }
    }

    public class WorkerTestsThirtyFive : WorkerTestsWithOptAndConfig
    {
        public WorkerTestsThirtyFive(DatabaseFixture db) : base(db)
        {
        }

        [Fact]
        public void test_kiosk_auth_pod_mode_opt()
        {
            // Arrange

            // Act
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpAuthorised);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpPaid);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            ToOptNozzleUpElements.Should().BeEmpty();

            ToOptTakeFuelElements.Should().BeEmpty();

            ToOptDeliveringElements.Should().BeEmpty();

            ToOptDeliveredElements.Should().BeEmpty();

            ToOptPaidAtKioskElements.Should().BeEmpty();

            ToOptPumpStateChangedKioskOnlyElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptPumpStateChangedKioskOnlyElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateKioskOnlyNotification);
            }

            ToOptPumpStateChangedOpenElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptPumpStateChangedOpenElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateOpenNotification);
            }

            ToOptPumpStateChangedMixedElements.Should().BeEmpty();

            ToOptPumpStateChangedClosedElements.Should().BeEmpty();

            ToOptModeChangeOptElements.Should().BeEmpty();
            ToOptModeChangeMixedElements.Should().BeEmpty();
            ToOptModeChangeKioskOnlyElements.Should().BeEmpty();
        }
    }

    public class WorkerTestsThirtySix : WorkerTestsWithMixedModeOptAndConfig
    {
        public WorkerTestsThirtySix(DatabaseFixtureWithMixedModeOpt db) : base(db)
        {
        }

        [Fact]
        public void test_kiosk_auth_mixed_mode_opt()
        {
            // Arrange

            // Act
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpRequest);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpAuthorised);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivering);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpDelivered);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            HscWorker.OnPumpData((CorePumpData)DatabaseFixture.HscPumpPaid);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);
            HeartbeatProxy.OnMessageReceived(DatabaseFixture.HeartbeatRequest, HeartbeatId);
            ToOptProxy.OnMessageReceived(DatabaseFixture.NotificationResponse, ToOptId);
            Thread.Sleep(100);

            // Assert
            ToOptNozzleUpElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptNozzleUpElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.NozzleUpNotification);
            }

            ToOptTakeFuelElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptTakeFuelElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.TakeFuelNotification);
            }

            ToOptDeliveringElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptDeliveringElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.DeliveringNotification);
            }

            ToOptDeliveredElements.Should().NotBeEmpty();
            // foreach (XElement element in ToOptDeliveredElements)
            // {
            //     element.Should().BeEquivalentTo(DatabaseFixture.DeliveredNotification);
            //  }

            ToOptPaidAtKioskElements.Should().NotBeEmpty();
            foreach (XElement element in ToOptPaidAtKioskElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PaidAtKioskNotification);
            }

            ToOptPumpStateChangedKioskOnlyElements.Should().BeEmpty();
            foreach (XElement element in ToOptPumpStateChangedKioskOnlyElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateKioskOnlyNotification);
            }

            ToOptPumpStateChangedOpenElements.Should().BeEmpty();
            foreach (XElement element in ToOptPumpStateChangedOpenElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateOpenNotification);
            }

            ToOptPumpStateChangedMixedElements.Should().BeEmpty();
            foreach (XElement element in ToOptPumpStateChangedMixedElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateMixedNotification);
            }

            ToOptPumpStateChangedClosedElements.Should().BeEmpty();
            foreach (XElement element in ToOptPumpStateChangedClosedElements)
            {
                element.Should().BeEquivalentTo(DatabaseFixture.PumpStateClosedNotification);
            }

            ToOptModeChangeOptElements.Should().BeEmpty();
            ToOptModeChangeMixedElements.Should().NotBeEmpty();
            ToOptModeChangeKioskOnlyElements.Should().NotBeEmpty();
        }
    }
}
