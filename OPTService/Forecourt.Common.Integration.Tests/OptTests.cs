using FluentAssertions;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;
using NSubstitute;
using System.Collections.Specialized;
using System.Threading;
using Xunit;

namespace OPT.Common.Integration.Tests
{
    public class OptTests
    {
        private const string HydraId = "Hydra 1";
        private const string IdString = "1234";
        private const int Id = 1;
        private readonly IOpt _opt;
        private readonly IConfigurationManager _configurationManager;
        private readonly IHtecLogManager _logManager;

        public OptTests()
        {
            _configurationManager = Substitute.For<IConfigurationManager>();
            var appSettings = Substitute.For<NameValueCollection>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(appSettings);

            _logManager = Substitute.For<IHtecLogManager>();

            _opt = CreateDefaultOpt();
        }

        [Fact]
        public void test_received_from_heartbeat_and_timeout()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.Heartbeat);
            Thread.Sleep(11000);

            // Assert
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.HasTimeoutExpired().Should().BeTrue();
        }

        [Fact]
        public void test_received_from_heartbeat_and_not_timeout()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.Heartbeat);
            Thread.Sleep(6000);
            _opt.Received(SocketType.Heartbeat);
            Thread.Sleep(6000);

            // Assert
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.HasTimeoutExpired().Should().BeFalse();
        }

        [Fact]
        public void test_sending_request_to_opt_and_timeout()
        {
            // Arrange

            // Act
            _opt.SendingRequestToOpt();
            Thread.Sleep(3000);

            // Assert
            _opt.RequestSentToOpt.Should().BeTrue();
            _opt.HasTimeoutExpired().Should().BeTrue();
        }

        [Fact]
        public void test_sending_request_to_opt_and_not_timeout()
        {
            // Arrange

            // Act
            _opt.SendingRequestToOpt();
            Thread.Sleep(1500);
            _opt.SendingRequestToOpt();
            Thread.Sleep(1500);

            // Assert
            _opt.RequestSentToOpt.Should().BeTrue();
            _opt.HasTimeoutExpired().Should().BeFalse();
        }

        #region Helpers

        private Opt CreateDefaultOpt()
        {
            return new Opt(_logManager, IdString, Id, HydraId, _configurationManager);
        }

        #endregion

    }
}
