using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using System.Collections.Generic;

namespace Forecourt.PaymentConfiguration.ESocketDbClasses
{
    public interface IEsocketDb
    {
        void RegisterWorker(IWorkerable worker);
        string ConnectionString { get; }
        bool UseConnectionString { get; }
        string ConfigFile { get; }
        string CurrentConfigFile { get; }
        string KeystoreFile { get; }
        string CurrentKeystoreFile { get; }
        string DbUrl { get; }
        string ContactlessFile { get; }
        string CurrentContactlessFile { get; }
        bool OverrideProperties { get; }
        bool OverrideKeystore { get; }
        bool OverrideUrl { get; }
        bool OverrideContactless { get; }
        bool ConnectionMade { get; }
        void MakeConnection();
        void SetConnectionString(string newConnectionString, bool connect = true);
        void SetUseConnectionString(bool useConnectionString, bool connect = true);
        void SetConfigFile(string fileName, bool connect = true);
        void SetKeystoreFile(string fileName, bool connect = true);
        void SetDbUrl(string url, bool connect = true);
        void SetContactlessFile(string fileName);
        void SetOverrideProperties(bool flag, bool connect = true);
        void SetOverrideKeystore(bool flag, bool connect = true);
        void SetOverrideUrl(bool flag, bool connect = true);
        void SetOverrideContactless(bool flag);
        bool CheckTermProcCategory(string loggingReference);
        TermProcCategory TermProcCategory { get; }
        IList<CardAid> CardAids { get; }
        IList<CardCapk> CardCapks { get; }
        IList<FuelCard> FuelCards { get; }
        IList<TermId> AllPumpTids { get; }
        string TermCapabilities { get; }
        string TermAddCapabilities { get; }
        void FetchContactlessProperties(IList<CardClessAid> cards, IList<CardClessDrl> drls, string loggingReference);
        void SetServiceDirectory(string serviceDirectory);
        void GetAllPumpTids(string loggingReference);

        void RefreshCustomDataOverrides(string loggingReference);
    }
}
