using FluentAssertions;
using Forecourt.Core.Payment.Enums;
using Forecourt.Core.Pump.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.System;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using OPT.TransactionValidator.Common;
using OPT.TransactionValidator.Interfaces;
using OPT.TransactionValidator.Models;
using System;
using System.Collections.Specialized;
using Xunit;

namespace OPT.TransactionValidator.Tests
{
    public class PumpTransactionValidatorTests
    {
        private const int DefaultAuthTimeThreshold = 1;
        private const int DefaultFlowSpeedThreshold = 1;
        private const int DefaultVendTimeThreshold = 1;

        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly ValidationConfiguration _validationConfiguration;
        private readonly IConfigurationManager _configurationManager;

        public PumpTransactionValidatorTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _logManager.GetLogger(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<bool>(), Arg.Any<ILogFormatter>()).Returns(_logger);

            var appSettings = Substitute.For<NameValueCollection>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(appSettings);

            _validationConfiguration = new ValidationConfiguration(_logManager, _configurationManager);
        }

        #region Constructor Tests

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            Func<object> constructor = () => new PumpTransactionValidator(null, _validationConfiguration);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(constructor, "logger");
        }

        [Fact]
        public void constructor_null_validation_configuration_throws_argument_null_exception()
        {
            // Arrange
            Func<object> constructor = () => new PumpTransactionValidator(_logger, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(constructor, "validationConfiguration");
        }

        #endregion

        #region Validate Transaction

        [Fact]
        public void validate_transaction_null_transaction_states_returns_false()
        {
            // Arrange
            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, _validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(null);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        [Fact]
        public void validate_transaction_empty_transaction_states_returns_false()
        {
            // Arrange
            var transaction = new PumpTransaction(1);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, _validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public void validate_transaction_no_vend_start_states_returns_false()
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Finished, new PumpTransactionState(PumpState.Finished, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, _validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        [Fact]
        public void validate_transaction_no_vend_complete_states_returns_false()
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, _validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        private ValidationConfiguration CreateValidationConfiguration(int vendTimeThreshold = DefaultVendTimeThreshold, int flowSpeedThreshold = DefaultFlowSpeedThreshold, int authTimeThreshold = DefaultAuthTimeThreshold)
        {
            // Arrange
            _configurationManager.AppSettings[Constants.ConfigKeyVendTimeThreshold] = $"{vendTimeThreshold}";
            _configurationManager.AppSettings[Constants.ConfigKeyFlowSpeedThreshold] = $"{flowSpeedThreshold}";
            _configurationManager.AppSettings[Constants.ConfigKeyAuthTimeThreshold] = $"{authTimeThreshold}";

            return new ValidationConfiguration(_logManager, _configurationManager);
        }

        [Fact]
        public void validate_transaction_between_gap_vend_start_and_vend_complete_states_below_threshold_returns_false()
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });

            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 1);
            transaction.TransactionStates.Add(PumpState.Finished, new PumpTransactionState(PumpState.Finished, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });

            var validationConfiguration = CreateValidationConfiguration(vendTimeThreshold: 10);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger,validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        [Fact]
        public void validate_transaction_flow_speed_above_threshold_returns_false()
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });
            transaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()) { Amount = 5, Volume = 5 });

            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 1, 0);
            transaction.TransactionStates.Add(PumpState.Finished, new PumpTransactionState(PumpState.Finished, SystemTime.UtcNow())
            {
                Amount = 10,
                Volume = 1000
            });
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()));

            var validationConfiguration = CreateValidationConfiguration(vendTimeThreshold: 10);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void validate_transaction_gap_between_auth_and_vend_complete_states_below_threshold_returns_false(PaymentResult paymentResult)
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()));

            transaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState
            {
                WhenProcessed = new DateTime(2020, 1, 1, 13, 0, 19)
            });

            var lastEntered = new DateTime(2020, 1, 1, 13, 0, 20);
            SystemTime.UtcNow = () => lastEntered;
            transaction.TransactionStates.Add(PumpState.Finished, new PumpTransactionState(PumpState.Finished, SystemTime.UtcNow()) { LastEntered = lastEntered, Amount = 5, Volume = 5 });
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = lastEntered, Amount = 5, Volume = 5 });

            var validationConfiguration = CreateValidationConfiguration(vendTimeThreshold: 10, 10, 10);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }

        [Fact]
        public void validate_transaction_request_idle_value_returns_true()
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));

            var lastEntered = new DateTime(2020, 1, 1, 13, 0, 20);
            SystemTime.UtcNow = () => lastEntered;
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = lastEntered });

            var validationConfiguration = CreateValidationConfiguration(vendTimeThreshold: 50, 3000, 50);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeTrue();
        }

        [Theory, InlineData(PaymentResult.Approved), InlineData(PaymentResult.ApprovedAndAuthorised)]
        public void validate_transaction_zero_value_returns_true(PaymentResult paymentResult)
        {
            // Arrange
            SystemTime.UtcNow = () => new DateTime(2020, 1, 1, 13, 0, 0);
            var transaction = new PumpTransaction(1);
            transaction.TransactionStates.Add(PumpState.Request, new PumpTransactionState(PumpState.Request, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Authorise, new PumpTransactionState(PumpState.Authorise, SystemTime.UtcNow()));
            transaction.TransactionStates.Add(PumpState.Delivering, new PumpTransactionState(PumpState.Delivering, SystemTime.UtcNow()));

            transaction.PaymentStates.Add(paymentResult, new PumpTransactionPaymentState
            {
                WhenProcessed = new DateTime(2020, 1, 1, 13, 0, 19)
            });

            var lastEntered = new DateTime(2020, 1, 1, 13, 0, 20);
            SystemTime.UtcNow = () => lastEntered;
            transaction.TransactionStates.Add(PumpState.Finished, new PumpTransactionState(PumpState.Finished, SystemTime.UtcNow()) { LastEntered = lastEntered });
            transaction.TransactionStates.Add(PumpState.Idle, new PumpTransactionState(PumpState.Idle, SystemTime.UtcNow()) { LastEntered = lastEntered });

            var validationConfiguration = CreateValidationConfiguration(vendTimeThreshold: 150, 3000, 30);

            IPumpTransactionValidator pumpTransactionValidator = new PumpTransactionValidator(_logger, validationConfiguration);

            // Act
            var result = pumpTransactionValidator.ValidateTransaction(transaction);

            // Assert
            result.IsSuccess.Should().BeTrue();
        }

        #endregion
    }
}
