import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGX<PERSON>ogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { AdvancedConfig } from 'src/app/core/models/advancedConfig.model';
import { ConnectionFlags } from 'src/app/core/models/connectionFlags.model';
import { Connections } from 'src/app/core/models/connections.model';
import { GenericOptConfig } from 'src/app/core/models/genericOptConfig.model';
import { ADVANCED_SERVICE_PROVIDER, ADVANCED_SERVICE_SPY } from 'src/app/services/advanced-config.service.spy';
import { CONNECTION_SERVICE_PROVIDER, CONNECTION_SERVICE_SPY } from 'src/app/services/connection.service.spy';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { OPT_SERVICE_PROVIDER, OPT_SERVICE_SPY } from 'src/app/services/opt.service.spy';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { ConnectionsComponent } from './connections.component';

describe('ConnectionsComponent', () => {
  let component: ConnectionsComponent;  
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let fakeConnSpy: jasmine.SpyObj<Connections>;

  const fakeGenericConfig = {
  } as GenericOptConfig;

  const fakeAdvancedConfig = {
    PosType: "POS",
    BosType: "BOS"
  } as AdvancedConfig;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);
    const fakeConn = {
      ToOptPort: 0,
      FromOptPort: 0,
      HeartbeatPort: 0,
      HydraPosPort: 0,
      RetalixPosPort: 0,
      ThirdPartyPosPort: 0,
      MediaChannelPort: 0,
      AnprIpAddress: '127.0.0.1',
      AnprPort: 0,
      CarWashIpAddress: '127.0.0.1',
      CarWashPort: 0,
      PumpControllerIpAddress: '127.0.0.1',
      PumpControllerPort: 0,
      PumpControllerLogonInfo: '',
      TankGaugeIpAddress: '127.0.0.1',
      TankGaugePort: 0,
      HydraMobileIpAddress: '127.0.0.1',
      HydraMobilePort: 0,
      OptConnectedCount: 0,
      HydraPosConnectedCount: 0,
      RetalixPosConnectedCount: 0,
      ThirdPartyPosConnectedCount: 1,
      MediaChannelConnectedCount: 888,
      AnprConnected: false,
      CarWashConnected: false,
      TankGaugeConnected: false,
      HydraMobileConnected: false,
      PumpControllerConnected: true,
      PaymentConfigConnected: false,
      AutoAuth: false,
      MediaChannel: true,
      UnmannedPseudoPos: false,
      RetalixDefined: false,
      OptIpAddresses: [ ],
      HydraPosIpAddresses: [ '127.0.0.1' ],
      RetalixPosIpAddresses: [ '127.0.0.1', '***********' ],
      ThirdPartyPosIpAddresses: [ '127.0.0.1', '***********' ],
      MediaChannelIpAddresses: [ '127.0.0.1', '***********', '***********' ],
      RetalixPosPrimaryIpAddress: '127.0.0.1'
    };

    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        ConnectionsComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        CONNECTION_SERVICE_PROVIDER(),
        ADVANCED_SERVICE_PROVIDER(),
        OPT_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        { provide: Connections, useValue: fakeConn }
      ]
    });

    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    fakeConnSpy = TestBed.inject(Connections) as jasmine.SpyObj<Connections>;

    SIGNAL_R_SERVICE_SPY().getConnectionSignalRMessage.and.returnValue(of());
    SIGNAL_R_SERVICE_SPY().getAdvancedConfigSignalRMessage.and.returnValue(of());
    OPT_SERVICE_SPY().getOptDataChanged.and.returnValue(of({}));
    ADVANCED_SERVICE_SPY().getConfigDataChanged.and.returnValue(of({}));

    CONNECTION_SERVICE_SPY().getConnections.and.returnValue(of(undefined));
    OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(of(undefined));
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of(undefined));

    component = TestBed.inject(ConnectionsComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(component).toBeTruthy();
  });

  describe('.refreshData()', () => {
    
    it('.refreshData() should handle success responses from services', ()=>{
      //Arrange
      CONNECTION_SERVICE_SPY().getConnections.and.returnValue(of(fakeConnSpy));
      OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(of(fakeGenericConfig));
      ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of(fakeAdvancedConfig));
  
      //Act
      component.refreshData();
  
      //Assert
      expect(component.connectionsData).toEqual(fakeConnSpy);
      expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalled();
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalled();
    });
  
    it('.refreshData() should handle unsuccess responses from services', ()=>{
      //Arrange
      CONNECTION_SERVICE_SPY().getConnections.and.returnValue(throwError({status:500}));
      OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(throwError({status:500}));
      ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(throwError({status:500}));
  
      //Act
      component.refreshData();
  
      //Assert
      expect(component.connectionsData).toBeUndefined();
      expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalled();
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalled();
    });
  });

  describe('.setConnectionFlags()', () => {

    it('.setConnectionFlags() should convert advanced config to connection flags', () => {
      // Arrange
      const advancedConfig = {
        ConfigurationCategories: [
          { 
            Category: 'TEST',
            Settings: {
              'ConnectionThread:Execute:Active:TankGaugeConnectionThread': { Value: 'true' },
            },
          },
          { 
            Category: 'CONNECTIVITY', 
            Settings: {
              'ConnectionThread:Log:RxTx:AnprConnectionThread': { Value: 'False' },
              'ConnectionThread:Log:RxTx:HydraMobileConnectionThread': { Value: 'True' },
              'ConnectionThread:Execute:Active:AnprConnectionThread': { Value: 'TRUE' }, // should parse this as "true"
              'ConnectionThread:Execute:Active:HydraMobileConnectionThread': { Value: 'FALSE' }, // should parse this as "false"
            },
          },
        ]
      } as AdvancedConfig;

      // Act
      component.setConnectionFlags(advancedConfig);

      // Assert
      const expected = new ConnectionFlags();
      expected.AnprConnectionThread = true;

      expect(component.connectionFlags).toEqual(expected);
    });
  });

  it('.setFromOptPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setFromOptPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setToOptPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setToOptPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setHeartbeatPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setHeartbeatPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setHydraPosPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setHydraPosPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setRetalixPosPrimaryIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setRetalixPosPrimaryIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setRetalixPosPrimaryIpAddress() should handle success response from service and remove address when address is empty', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().removeRetalixPosPrimaryIpAddress.and.returnValue(of({}));
    fakeConnSpy.RetalixPosPrimaryIpAddress = "";
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setRetalixPosPrimaryIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().removeRetalixPosPrimaryIpAddress).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setRetalixPosPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setRetalixPosPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setThirdPartyPosPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setThirdPartyPosPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPumpControllerIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setPumpControllerIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setMediaChannelPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setMediaChannelPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPumpControllerPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setPumpControllerPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPumpControllerLogonInfo() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setPumpControllerLogonInfo.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;

    //Act
    component.setPumpControllerLogonInfo();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setPumpControllerLogonInfo).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setAnprIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setAnprIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setAnprPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setAnprPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setCarWashIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setCarWashIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setCarWashPort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setCarWashPort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setTankGaugeIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setTankGaugeIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setTankGaugePort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setTankGaugePort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setHydraMobileIpAddress() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setHydraMobileIpAddress();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setHydraMobilePort() should handle success response from service', () => {
    //Arrange
    CONNECTION_SERVICE_SPY().setProperty.and.returnValue(of({}));
    component.connectionsData = fakeConnSpy;
    
    //Act
    component.setHydraMobilePort();

    //Assert
    expect(CONNECTION_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

});
