using Htec.Hydra.Core.SecAuth.Enums;

namespace OPTService.OPTServiceClasses
{
    public class PumpDetails
    {
        public byte Number { get; set; }
        public string Tid { get; set; }
        public bool Closed { get; set; }
        public bool ClosePending { get; set; }
        public string OptStringId { get; set; }
        public bool InUse { get; set; }
        public bool NozzleUp { get; set; }
        public bool HasPayment { get; set; }
        public string SecAuthState { get; set; }
        public bool HasSecAuthRequestTimedOut { get; set; }
        public bool Delivering { get; set; }
        public bool Delivered { get; set; }
        public bool ThirdPartyPending { get; set; }
        public bool PodMode { get; set; }
        public bool KioskOnly { get; set; }
        public bool OutsideOnly { get; set; }
        public bool DefaultKioskOnly { get; set; }
        public bool DefaultOutsideOnly { get; set; }
        public bool MaxFillOverrideForFuelCards { get; set; }
        public bool MaxFillOverrideForPaymentCards { get; set; }
        public bool IsMixedMode { get; set; }
        public bool IsKioskUse { get; set;}
        public bool IsMobile { get; set; }
    }
}
