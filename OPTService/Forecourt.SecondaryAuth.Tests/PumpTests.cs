using FluentAssertions;
using Forecourt.Pump.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Logger.Interfaces;
using NSubstitute;
using Xunit;

namespace Forecourt.SecondaryAuth.Tests
{
    public class PumpTests
    {
        private readonly Pump.Pump _pump;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;
        private readonly IHydraDb _hydraDb;
        private readonly string _logRef = "LOG-REF";
        private const int TimeOutIntervalInSecs = 12;

        public PumpTests()
        {
            _logManager = Substitute.For<IHtecLogManager>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.FetchPaymentTimeout(Core.HydraDb.Enums.PaymentTimeoutType.SecAuth).Returns(TimeOutIntervalInSecs);

            _pump = new Pump.Pump(1, _hydraDb, _logManager, nameof(Pump), configurationManager: _configurationManager);
        }

        private void do_check_response_is_processed_as_expected(bool isAuthorised = true, SecAuthState expectedState = SecAuthState.Success, int interval = TimeOutIntervalInSecs)
        {
            // Arrange
            _hydraDb.FetchPaymentTimeout(Core.HydraDb.Enums.PaymentTimeoutType.SecAuth).Returns(interval);
            _pump.SecAuthRequestSent(_logRef);

            // Act
            _pump.SetSecAuthResponse(isAuthorised, _logRef);

            // Assert
            _pump.SecAuthState.Should().Be(expectedState);
        }

        [Fact]
        public void check_approved_response_is_processed_as_expected()
        {
            // Arrange/Act/Assert
            do_check_response_is_processed_as_expected();
        }

        [Fact]
        public void check_rejected_response_is_processed_as_expected()
        {
            // Arrange/Act/Assert
            do_check_response_is_processed_as_expected(false, SecAuthState.Failure);
        }

        [Fact]
        public void check_approved_response_after_timeout_is_processed_as_expected()
        {
            // Arrange/Act/Assert
            do_check_response_is_processed_as_expected(interval: -TimeOutIntervalInSecs, expectedState: SecAuthState.Requested);
        }

        [Fact]
        public void check_rejected_response_after_timeout_is_processed_as_expected()
        {
            // Arrange/Act/Assert
            do_check_response_is_processed_as_expected(interval: -TimeOutIntervalInSecs, isAuthorised: false, expectedState: SecAuthState.Failure);
        }
    }
}
