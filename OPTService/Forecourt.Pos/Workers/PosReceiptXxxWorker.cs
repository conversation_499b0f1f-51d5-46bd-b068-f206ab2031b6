using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Pos.Workers
{
    public class PosReceiptXxxWorker: PosXxxWorker
    {
        /// <summary>
        /// Helper class for everything receipts
        /// </summary>
        protected IReceiptHelper ReceiptHelper { get; private set; }

        /// <inheritdoc/>
        protected PosReceiptXxxWorker(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IPumpCollection allPumps, IReceiptHelper receiptHelper, IConfigurationManager configurationManager = null) : base(logManager, loggerName, hydraDb, allPumps, configurationManager)
        {
            if (AllPumps == null)
            {
                throw new ArgumentNullException(nameof(allPumps));
            }

            ReceiptHelper = receiptHelper ?? throw new ArgumentNullException(nameof(receiptHelper));
        }

    }
}
