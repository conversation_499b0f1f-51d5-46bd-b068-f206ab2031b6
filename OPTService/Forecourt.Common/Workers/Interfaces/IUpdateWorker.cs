using Htec.Foundation.Connections.Core.Interfaces;

namespace OPT.Common.Workers.Interfaces
{
    public interface IUpdateWorker : IConnectable
    {
        string ReceivedUpdateDirectory { get; }
        bool ForwardFuelPriceUpdate { get; }
        
        /// <summary>Set the directory for received updates.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetReceivedUpdateDirectory(string directory);

        /// <summary>Set Forward Fuel Price Update flag.</summary>
        /// <param name="isOn">New value of flag.</param>
        void SetForwardFuelPriceUpdate(bool isOn);
    }
}