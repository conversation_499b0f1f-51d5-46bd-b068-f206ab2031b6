using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace OPT.Common
{
    public class OptCollection : Loggable, IOptCollection
    {
        public const string GlobalOptId = "GLOBAL";

        private readonly IDictionary<int, IOptCore> _allOpts = new ConcurrentDictionary<int, IOptCore>();
        private int _nextOptId = 1;
        private readonly IHydraDb _hydraDb;

        private IEnumerable<IOptAllConnected> _allConnectedOpts => _allOpts.Values.OfType<IOptAllConnected>();
        public int AllConnectedCount => _allConnectedOpts.Count(x => x.AllConnected);
        public int ToOptConnectedCount => _allConnectedOpts.Count(x => x.ToOptConnected);
        public int FromOptConnectedCount => _allConnectedOpts.Count(x => x.FromOptConnected);
        public int HeartbeatConnectedCount => _allConnectedOpts.Count(x => x.HeartbeatConnected);
        public IEnumerable<IOpt> AllOpts => GetAllOpts<IOpt>();
        public IEnumerable<TOpt> GetAllOpts<TOpt>() where TOpt : IOptCore
        {
            return _allOpts.Values.OfType<TOpt>(); //Where(x => typeof(TOpt).IsAssignableFrom(x.GetType())).Select(x => (TOpt)x);
        }

        public IEnumerable<IOpt> AllOptsAllConnected => GetAllOptsAllConnected<IOpt>();

        public IEnumerable<TOpt> GetAllOptsAllConnected<TOpt>() where TOpt : IOptCore
        {
            return _allConnectedOpts.Where(x => x.AllConnected).OfType<TOpt>();
        }

        public string GlobalOptStringId => GlobalOptId;

        public int LogInterval { get; private set; }

        private string _hydraId;

        private readonly IPrinterHelper<IMessageTracking> _printerHelper;
        private readonly IReceiptHelper _receiptHelper;

        public OptCollection(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IConfigurationManager configurationManager, IPrinterHelper<IMessageTracking> printerHelper, IReceiptHelper receiptHelper) :
            base(logManager, loggerName, configurationManager)
        {
            _hydraDb = hydraDb;
            LogInterval = _hydraDb.GetLogInterval();

            _printerHelper = printerHelper ?? throw new ArgumentNullException(nameof(printerHelper));
            _receiptHelper = receiptHelper ?? throw new ArgumentNullException(nameof(receiptHelper));
        }

        public bool TryGetOpt(int optId, out IOpt opt)
        {
            return TryGetOpt<IOpt>(optId, out opt);
        }

        public bool TryGetOpt<TOpt>(int optId, out TOpt opt) where TOpt : IOptCore
        {
            var result = _allOpts.TryGetValue(optId, out var _opt);
            opt = result && typeof(TOpt).IsAssignableFrom(_opt.GetType()) ? (TOpt)_opt : default;
            return result && opt != null;
        }

        public IOpt GetOptForIdString(string idString)
        {
            return GetOptForIdString<IOpt>(idString);
        }

        public TOpt GetOptForIdString<TOpt>(string idString, Func<string, OptMode, TOpt> ctor = null) where TOpt : IOptCore
        {
            var result = string.IsNullOrWhiteSpace(idString)
                ? default
                : _allOpts.Values.FirstOrDefault(x => idString.Equals(x.IdString)) ?? GetNewOpt(idString, ctor);

            return result == default || !typeof(TOpt).IsAssignableFrom(result.GetType()) ? default : (TOpt)result;
        }

        public int GetOptIdForIdString(string idString)
        {
            return GetOptForIdString(idString)?.Id ?? 0;
        }

        private IOpt CreateDefaultInstance(string idString, int id, OptMode optMode)
        {
            var result = new Opt(LogManager, idString, id, _hydraId, ConfigurationManager, optMode, _printerHelper, _receiptHelper);
            result.SetLogInterval(LogInterval);
            return result;
        }

        private TOpt GetNewOpt<TOpt>(string idString, Func<string, OptMode, TOpt> ctor = null) where TOpt: IOptCore
        {
            var optMode = _hydraDb.FetchOptMode(idString);
            var newOpt = ctor == null ? (TOpt)CreateDefaultInstance(idString, _nextOptId++, optMode) : ctor(idString, optMode);
            _allOpts.Add(newOpt.Id, newOpt);
            return newOpt;
        }

        public void SetLogInterval(int interval)
        {
            LogInterval = interval;
            _hydraDb.SetLogInterval(interval);
            foreach (var opt in AllOpts)
            {
                opt.SetLogInterval(LogInterval);
            }
        }

        public void SetHydraId(string hydraId)
        {
            _hydraId = hydraId;
        }
    }
}