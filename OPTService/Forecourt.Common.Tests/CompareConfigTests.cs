using System;
using FluentAssertions;
using Htec.Hydra.Messages.Opt.Xsd;
using Xunit;

namespace OPT.Common.Tests
{
    public class CompareConfigTests
    {
        [Fact]
        public void test_cards_equal_empty_and_null()
        {
            // Arrange
            const cards cardsa1 = null;
            const cards cardsb1 = null;
            cards cardsa2 = new cards();
            cards cardsb2 = new cards();

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_aids_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {aids = new aid[0]};
            cards cardsb2 = new cards {aids = new aid[0]};
            cards cardsa3 = new cards {aids = new aid[] {null}};
            cards cardsb3 = new cards {aids = new aid[] {null}};
            cards cardsa4 = new cards {aids = new aid[] {new aid()}};
            cards cardsb4 = new cards {aids = new aid[] {new aid()}};
            cards cardsa5 = new cards {aids = new aid[] {new aid {aid1 = ""}}};
            cards cardsb5 = new cards {aids = new aid[] {new aid {aid1 = ""}}};
            cards cardsa6 = new cards {aids = new aid[] {new aid {aid1 = ""}, new aid {aid1 = ""}}};
            cards cardsb6 = new cards {aids = new aid[] {new aid {aid1 = ""}, new aid {aid1 = ""}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_aid_aid1_and_emv_threshold_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                aids = new aid[] {new aid {aid1 = "A", emvThreshold = "C"}, new aid {aid1 = "B", emvThreshold = "D"}}
            };
            cards cardsb1 = new cards
            {
                aids = new aid[] {new aid {aid1 = "B", emvThreshold = "D"}, new aid {aid1 = "A", emvThreshold = "C"}}
            };
            cards cardsa2 = new cards
            {
                aids = new aid[] {new aid {aid1 = "A", emvThreshold = "D"}, new aid {aid1 = "B", emvThreshold = "C"}}
            };
            cards cardsb2 = new cards
            {
                aids = new aid[] {new aid {aid1 = "B", emvThreshold = "C"}, new aid {aid1 = "A", emvThreshold = "D"}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_cless_aids_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {cless_aids = new cless_aid[0]};
            cards cardsb2 = new cards {cless_aids = new cless_aid[0]};
            cards cardsa3 = new cards {cless_aids = new cless_aid[] {null}};
            cards cardsb3 = new cards {cless_aids = new cless_aid[] {null}};
            cards cardsa4 = new cards {cless_aids = new cless_aid[] {new cless_aid()}};
            cards cardsb4 = new cards {cless_aids = new cless_aid[] {new cless_aid()}};
            cards cardsa5 = new cards {cless_aids = new cless_aid[] {new cless_aid {aid = ""}}};
            cards cardsb5 = new cards {cless_aids = new cless_aid[] {new cless_aid {aid = ""}}};
            cards cardsa6 = new cards {cless_aids = new cless_aid[] {new cless_aid {aid = ""}, new cless_aid {aid = ""}}};
            cards cardsb6 = new cards {cless_aids = new cless_aid[] {new cless_aid {aid = ""}, new cless_aid {aid = ""}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_cless_aid_aid_and_tac_online_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {aid = "A", tacOnline = "C"}, new cless_aid {aid = "B", tacOnline = "D"}}
            };
            cards cardsb1 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {aid = "B", tacOnline = "D"}, new cless_aid {aid = "A", tacOnline = "C"}}
            };
            cards cardsa2 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {aid = "A", tacOnline = "D"}, new cless_aid {aid = "B", tacOnline = "C"}}
            };
            cards cardsb2 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {aid = "B", tacOnline = "C"}, new cless_aid {aid = "A", tacOnline = "D"}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_cless_aid_drls_equal()
        {
            // Arrange
            cards cardsa1 = new cards {cless_aids = new cless_aid[] {new cless_aid()}};
            cards cardsb1 = new cards {cless_aids = new cless_aid[] {new cless_aid()}};
            cards cardsa2 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[0]}}};
            cards cardsb2 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[0]}}};
            cards cardsa3 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {null}}}};
            cards cardsb3 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {null}}}};
            cards cardsa4 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl()}}}};
            cards cardsb4 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl()}}}};
            cards cardsa5 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl {programId = ""}}}}};
            cards cardsb5 = new cards {cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl {programId = ""}}}}};
            cards cardsa6 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl {programId = ""}, new drl {programId = ""}}}}
            };
            cards cardsb6 = new cards
            {
                cless_aids = new cless_aid[] {new cless_aid {drl = new drl[] {new drl {programId = ""}, new drl {programId = ""}}}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_cless_aid_drl_program_id_and_cvm_limit_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                cless_aids = new cless_aid[]
                    {new cless_aid {drl = new drl[] {new drl {programId = "A", cvmLimit = "C"}, new drl {programId = "B", cvmLimit = "D"}}}}
            };
            cards cardsb1 = new cards
            {
                cless_aids = new cless_aid[]
                    {new cless_aid {drl = new drl[] {new drl {programId = "B", cvmLimit = "D"}, new drl {programId = "A", cvmLimit = "C"}}}}
            };
            cards cardsa2 = new cards
            {
                cless_aids = new cless_aid[]
                    {new cless_aid {drl = new drl[] {new drl {programId = "A", cvmLimit = "D"}, new drl {programId = "B", cvmLimit = "C"}}}}
            };
            cards cardsb2 = new cards
            {
                cless_aids = new cless_aid[]
                    {new cless_aid {drl = new drl[] {new drl {programId = "B", cvmLimit = "C"}, new drl {programId = "A", cvmLimit = "D"}}}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_capks_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {capks = new capk[0]};
            cards cardsb2 = new cards {capks = new capk[0]};
            cards cardsa3 = new cards {capks = new capk[] {null}};
            cards cardsb3 = new cards {capks = new capk[] {null}};
            cards cardsa4 = new cards {capks = new capk[] {new capk()}};
            cards cardsb4 = new cards {capks = new capk[] {new capk()}};
            cards cardsa5 = new cards {capks = new capk[] {new capk {rid = ""}}};
            cards cardsb5 = new cards {capks = new capk[] {new capk {rid = ""}}};
            cards cardsa6 = new cards {capks = new capk[] {new capk {rid = ""}, new capk {rid = ""}}};
            cards cardsb6 = new cards {capks = new capk[] {new capk {rid = ""}, new capk {rid = ""}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_capk_rid_and_expiry_date_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                capks = new capk[] {new capk {rid = "A", expiryDate = "C"}, new capk {rid = "B", expiryDate = "D"}}
            };
            cards cardsb1 = new cards
            {
                capks = new capk[] {new capk {rid = "B", expiryDate = "D"}, new capk {rid = "A", expiryDate = "C"}}
            };
            cards cardsa2 = new cards
            {
                capks = new capk[] {new capk {rid = "A", expiryDate = "D"}, new capk {rid = "B", expiryDate = "C"}}
            };
            cards cardsb2 = new cards
            {
                capks = new capk[] {new capk {rid = "B", expiryDate = "C"}, new capk {rid = "A", expiryDate = "D"}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_fuel_cards_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {fuelcards = new fuelcard[0]};
            cards cardsb2 = new cards {fuelcards = new fuelcard[0]};
            cards cardsa3 = new cards {fuelcards = new fuelcard[] {null}};
            cards cardsb3 = new cards {fuelcards = new fuelcard[] {null}};
            cards cardsa4 = new cards {fuelcards = new fuelcard[] {new fuelcard()}};
            cards cardsb4 = new cards {fuelcards = new fuelcard[] {new fuelcard()}};
            cards cardsa5 = new cards {fuelcards = new fuelcard[] {new fuelcard {iinStart = ""}}};
            cards cardsb5 = new cards {fuelcards = new fuelcard[] {new fuelcard {iinStart = ""}}};
            cards cardsa6 = new cards {fuelcards = new fuelcard[] {new fuelcard {iinStart = ""}, new fuelcard {iinStart = ""}}};
            cards cardsb6 = new cards {fuelcards = new fuelcard[] {new fuelcard {iinStart = ""}, new fuelcard {iinStart = ""}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_fuel_card_iin_start_and_online_pin_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "A", onlinePin = "C"}, new fuelcard {iinStart = "B", onlinePin = "D"}}
            };
            cards cardsb1 = new cards
            {
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "B", onlinePin = "D"}, new fuelcard {iinStart = "A", onlinePin = "C"}}
            };
            cards cardsa2 = new cards
            {
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "A", onlinePin = "D"}, new fuelcard {iinStart = "B", onlinePin = "C"}}
            };
            cards cardsb2 = new cards
            {
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "B", onlinePin = "C"}, new fuelcard {iinStart = "A", onlinePin = "D"}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_tariff_mapping_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {tariffMapping = new gradeMapping[0]};
            cards cardsb2 = new cards {tariffMapping = new gradeMapping[0]};
            cards cardsa3 = new cards {tariffMapping = new gradeMapping[] {null}};
            cards cardsb3 = new cards {tariffMapping = new gradeMapping[] {null}};
            cards cardsa4 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping()}};
            cards cardsb4 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping()}};
            cards cardsa5 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1}}};
            cards cardsb5 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1}}};
            cards cardsa6 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1}, new gradeMapping {grade = 1}}};
            cards cardsb6 = new cards {tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1}, new gradeMapping {grade = 1}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_tariff_mapping_grade_and_product_code_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                tariffMapping = new gradeMapping[]
                    {new gradeMapping {grade = 1, productCode = "A"}, new gradeMapping {grade = 2, productCode = "B"}}
            };
            cards cardsb1 = new cards
            {
                tariffMapping = new gradeMapping[]
                    {new gradeMapping {grade = 2, productCode = "B"}, new gradeMapping {grade = 1, productCode = "A"}}
            };
            cards cardsa2 = new cards
            {
                tariffMapping = new gradeMapping[]
                    {new gradeMapping {grade = 1, productCode = "B"}, new gradeMapping {grade = 2, productCode = "A"}}
            };
            cards cardsb2 = new cards
            {
                tariffMapping = new gradeMapping[]
                    {new gradeMapping {grade = 2, productCode = "A"}, new gradeMapping {grade = 1, productCode = "B"}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_discount_cards_equal()
        {
            // Arrange
            cards cardsa1 = new cards();
            cards cardsb1 = new cards();
            cards cardsa2 = new cards {discountCards = new discountCardList {discountRange = new discountRange[0]}};
            cards cardsb2 = new cards {discountCards = new discountCardList { discountRange = new discountRange[0]}};
            cards cardsa3 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {null}}};
            cards cardsb3 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {null}}};
            cards cardsa4 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange()}}};
            cards cardsb4 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange()}}};
            cards cardsa5 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange {iin = ""}}}};
            cards cardsb5 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange {iin = ""}}}};
            cards cardsa6 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange {iin = ""}, new discountRange {iin = ""}}}};
            cards cardsb6 = new cards {discountCards = new discountCardList { discountRange = new discountRange[] {new discountRange {iin = ""}, new discountRange {iin = ""}}}};

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_discount_card_iin_and_grade_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[] {new discountRange {iin = "A", grade = 1}, new discountRange {iin = "B", grade = 2}}}
            };
            cards cardsb1 = new cards
            {
                discountCards = new discountCardList
                {
                discountRange = new discountRange[] {new discountRange {iin = "B", grade = 2}, new discountRange {iin = "A", grade = 1}}}
            };
            cards cardsa2 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[] {new discountRange {iin = "A", grade = 2}, new discountRange {iin = "B", grade = 1}}}
            };
            cards cardsb2 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[] {new discountRange {iin = "B", grade = 1}, new discountRange {iin = "A", grade = 2}}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_discount_card_whitelist_equal()
        {
            // Arrange
            cards cardsa1 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange() } } };
            cards cardsb1 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange() } } };
            cards cardsa2 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange { whitelist = new whitelistCard[0] } } } };
            cards cardsb2 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange { whitelist = new whitelistCard[0] } } } };
            cards cardsa3 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange { whitelist = new whitelistCard[] { null } } } } };
            cards cardsb3 = new cards { discountCards = new discountCardList { discountRange = new discountRange[] { new discountRange { whitelist = new whitelistCard[] { null } } } } };
            cards cardsa4 = new cards
            {
                discountCards = new discountCardList
                {
                discountRange = new discountRange[] {new discountRange {whitelist = new whitelistCard[] {new whitelistCard()}}}}
            };
            cards cardsb4 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[] {new discountRange {whitelist = new whitelistCard[] {new whitelistCard()}}}}
            };
            cards cardsa5 = new cards
            {
                discountCards = new discountCardList
                {
                discountRange = new discountRange[] {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = ""}}}}}
            };
            cards cardsb5 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[] {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = ""}}}}}
            };
            cards cardsa6 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = ""}, new whitelistCard {pan = ""}}}}}
            };
            cards cardsb6 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = ""}, new whitelistCard {pan = ""}}}}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);
            bool result5 = CompareConfig.CardsEqual(cardsa2, cardsb3);
            bool result6 = CompareConfig.CardsEqual(cardsa3, cardsb2);
            bool result7 = CompareConfig.CardsEqual(cardsa3, cardsb3);
            bool result8 = CompareConfig.CardsEqual(cardsa3, cardsb4);
            bool result9 = CompareConfig.CardsEqual(cardsa4, cardsb3);
            bool result10 = CompareConfig.CardsEqual(cardsa4, cardsb4);
            bool result11 = CompareConfig.CardsEqual(cardsa4, cardsb5);
            bool result12 = CompareConfig.CardsEqual(cardsa5, cardsb4);
            bool result13 = CompareConfig.CardsEqual(cardsa5, cardsb5);
            bool result14 = CompareConfig.CardsEqual(cardsa5, cardsb6);
            bool result15 = CompareConfig.CardsEqual(cardsa6, cardsb5);
            bool result16 = CompareConfig.CardsEqual(cardsa6, cardsb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_discount_card_whitelist_pan_equal_with_values()
        {
            // Arrange
            cards cardsa1 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = "A"}, new whitelistCard {pan = "B"}}}}}
            };
            cards cardsb1 = new cards
            {
                discountCards = new discountCardList
                {
                discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = "B"}, new whitelistCard {pan = "A"}}}}}
            };
            cards cardsa2 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = "A"}, new whitelistCard {pan = "C"}}}}}
            };
            cards cardsb2 = new cards
            {
                discountCards = new discountCardList
                {
                    discountRange = new discountRange[]
                    {new discountRange {whitelist = new whitelistCard[] {new whitelistCard {pan = "C"}, new whitelistCard {pan = "A"}}}}}
            };

            // Act
            bool result1 = CompareConfig.CardsEqual(cardsa1, cardsb1);
            bool result2 = CompareConfig.CardsEqual(cardsa1, cardsb2);
            bool result3 = CompareConfig.CardsEqual(cardsa2, cardsb1);
            bool result4 = CompareConfig.CardsEqual(cardsa2, cardsb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        #region Local Account Cards

        public class LocalAccountCardTestData
        {
            public string CaseName { get; }
            internal cards CardsA { get; set; }
            internal cards CardsB { get; set; }
            public bool ExpectedMatch { get; set; }

            public LocalAccountCardTestData(Func<cards> cardsA, Func<cards> cardsB, bool expectedMatch)
            {
                CaseName = $"{cardsA.Method.Name} | {cardsB.Method.Name}";
                CardsA = cardsA();
                CardsB = cardsB();
                ExpectedMatch = expectedMatch;
            }
        }

        private static void CompareLocalAccountCards(LocalAccountCardTestData cardData)
        {
            // Arrange & Act
            var result = CompareConfig.CardsEqual(cardData.CardsA, cardData.CardsB);

            // Assert
            result.Should().Be(cardData.ExpectedMatch);
        }

        private static cards CreateCardsWithCustomers(params localAccountCustomer[] localAccountCustomers)
        {
            return new cards { localAccounts = localAccountCustomers };
        }
        private static cards EmptyCards() => new cards();

        private static cards EmptyArray() => CreateCardsWithCustomers(Array.Empty<localAccountCustomer>());

        private static cards NullCustomerArray() => CreateCardsWithCustomers(new localAccountCustomer[] { null });

        private static cards EmptyCustomer() => CreateCardsWithCustomers(new localAccountCustomer());

        private static cards OneCustomer() => CreateCardsWithCustomers(new localAccountCustomer { name = "" });

        private static cards TwoCards() => CreateCardsWithCustomers(new localAccountCustomer { name = "" }, new localAccountCustomer { name = "" });

        public static TheoryData<LocalAccountCardTestData> LocalAccountBasicData => new TheoryData<LocalAccountCardTestData>
        {
            new LocalAccountCardTestData(EmptyCards, EmptyCards, true),
            new LocalAccountCardTestData(EmptyCards, EmptyArray, false),
            new LocalAccountCardTestData(EmptyArray, EmptyCards, false),
            new LocalAccountCardTestData(EmptyArray, EmptyArray, true),
            new LocalAccountCardTestData(EmptyArray, NullCustomerArray, false),
            new LocalAccountCardTestData(NullCustomerArray, EmptyArray, false),
            new LocalAccountCardTestData(NullCustomerArray, NullCustomerArray, true),
            new LocalAccountCardTestData(NullCustomerArray, EmptyCustomer, false),
            new LocalAccountCardTestData(EmptyCustomer, NullCustomerArray, false),
            new LocalAccountCardTestData(EmptyCustomer, EmptyCustomer, true),
            new LocalAccountCardTestData(EmptyCustomer, OneCustomer, false),
            new LocalAccountCardTestData(OneCustomer, EmptyCustomer, false),
            new LocalAccountCardTestData(OneCustomer, OneCustomer, true),
            new LocalAccountCardTestData(OneCustomer, TwoCards, false),
            new LocalAccountCardTestData(TwoCards, OneCustomer, false),
            new LocalAccountCardTestData(TwoCards, TwoCards, true)
        };
        
        [Theory]
        [MemberData(nameof(LocalAccountBasicData), MemberType = typeof(CompareConfigTests))]
        public void local_accounts_basic_customer_details_are_equal(LocalAccountCardTestData cardData) => CompareLocalAccountCards(cardData);


        private static cards CreateCardsWithCustomersLimit(string name1, string limit1, string name2, string limit2)
        {
            return new cards
            {
                localAccounts = new[]
                {
                    new localAccountCustomer { name = name1, transLimit = limit1 },
                    new localAccountCustomer { name = name2, transLimit = limit2 }
                }
            };
        }

        private static cards Customer_BC_AD() => CreateCardsWithCustomersLimit("B", "C", "A", "D");

        private static cards Customer_AD_BC() => CreateCardsWithCustomersLimit("A", "D", "B", "C");

        private static cards Customer_BD_AC() => CreateCardsWithCustomersLimit("B", "D", "A", "C");

        private static cards Customer_AC_BD() => CreateCardsWithCustomersLimit("A", "C", "B", "D");

        public static TheoryData<LocalAccountCardTestData> LocalAccountCustomerData => new TheoryData<LocalAccountCardTestData>
        {
            new LocalAccountCardTestData(Customer_AC_BD, Customer_BD_AC, true),
            new LocalAccountCardTestData(Customer_AC_BD, Customer_BC_AD, false),
            new LocalAccountCardTestData(Customer_AD_BC, Customer_BD_AC, false),
            new LocalAccountCardTestData(Customer_AD_BC, Customer_BC_AD, true)
        };

        [Theory]
        [MemberData(nameof(LocalAccountCustomerData), MemberType = typeof(CompareConfigTests))]
        public void local_account_name_and_trans_limit_are_equal(LocalAccountCardTestData cardData) => CompareLocalAccountCards(cardData);


        private static cards CreateCardsWithPan(params card[] cards)
        {
            return new cards { localAccounts = new[] { new localAccountCustomer { cards = cards } } };
        }

        private static cards NoCards() => CreateCardsWithPan(Array.Empty<card>());

        private static cards NullCards() => CreateCardsWithPan(new card[] { null });

        private static cards EmptyCard() => CreateCardsWithPan(new card());

        private static cards EmptyPan() => CreateCardsWithPan(new card { pan = "" });

        private static cards TwoEmptyPans() => CreateCardsWithPan(new card { pan = "" }, new card { pan = "" });

        public static TheoryData<LocalAccountCardTestData> LocalAccountCardData => new TheoryData<LocalAccountCardTestData>
        {
            new LocalAccountCardTestData(EmptyCustomer, NoCards, false),
            new LocalAccountCardTestData(NoCards, EmptyCustomer, false),
            new LocalAccountCardTestData(NoCards, NoCards, true),
            new LocalAccountCardTestData(NoCards, NullCards, false),
            new LocalAccountCardTestData(NullCards, NoCards, false),
            new LocalAccountCardTestData(NullCards, NullCards, true),
            new LocalAccountCardTestData(NullCards, EmptyCard, false),
            new LocalAccountCardTestData(EmptyCard, NullCards, false),
            new LocalAccountCardTestData(EmptyCard, EmptyCard, true),
            new LocalAccountCardTestData(EmptyCard, EmptyPan, false),
            new LocalAccountCardTestData(EmptyPan, EmptyCard, false),
            new LocalAccountCardTestData(EmptyPan, EmptyPan, true),
            new LocalAccountCardTestData(EmptyPan, TwoEmptyPans, false),
            new LocalAccountCardTestData(TwoEmptyPans, EmptyPan, false),
            new LocalAccountCardTestData(TwoEmptyPans, TwoEmptyPans, true)
        };
        
        [Theory]
        [MemberData(nameof(LocalAccountCardData), MemberType = typeof(CompareConfigTests))]
        public void local_account_cardlists_are_equal(LocalAccountCardTestData cardData) => CompareLocalAccountCards(cardData);


        private static cards CreateCardWithRestriction(string pan1, int restrictions1, string pan2, int restrictions2)
        {
            return new cards
            {
                localAccounts = new[]
                {
                    new localAccountCustomer
                    {
                        cards = new[] { new card { pan = pan1, restrictions2 = restrictions1 }, new card { pan = pan2, restrictions2 = restrictions2 } }
                    }
                }
            };
        }
        private static cards Pan_A1_B2() => CreateCardWithRestriction("A", 1, "B", 2);

        private static cards Pan_B2_A1() => CreateCardWithRestriction("B", 2, "A", 1);

        private static cards Pan_A2_B1() => CreateCardWithRestriction("A", 2, "B", 1);

        private static cards Pan_B1_A2() => CreateCardWithRestriction("B", 1, "A", 2);

        public static TheoryData<LocalAccountCardTestData> LocalAccountCardRestrictions => new TheoryData<LocalAccountCardTestData>
        {
            new LocalAccountCardTestData(Pan_A1_B2, Pan_B2_A1, true),
            new LocalAccountCardTestData(Pan_A1_B2, Pan_B1_A2, false),
            new LocalAccountCardTestData(Pan_A2_B1, Pan_B2_A1, false),
            new LocalAccountCardTestData(Pan_A2_B1, Pan_B1_A2, true)
        };

        [Theory]
        [MemberData(nameof(LocalAccountCardRestrictions), MemberType = typeof(CompareConfigTests))]
        public void local_account_cardlist_pan_and_restrictions_are_equal(LocalAccountCardTestData cardData) => CompareLocalAccountCards(cardData);
        
        #endregion

        [Fact]
        public void test_populated_cards()
        {
            // Arrange
            cards cards1 = new cards
            {
                aids = new aid[]
                {
                    new aid
                    {
                        aid1 = "AID 1",
                        appVerTerm = "App Ver Term 1",
                        tacDefault = "TAC Default 1",
                        tacDenial = "TAC Denial 1",
                        tacOnline = "TAC Online 1",
                        partialMatch = "Partial Match 1",
                        tdol = "TDOL 1",
                        ddol = "DDOL 1",
                        floorLimit = "Floor Limit 1",
                        emvTarget = "EMV Target 1",
                        emvMaxTarget = "EMV Max Target 1",
                        emvThreshold = "EMV Threshold 1"
                    },
                    new aid
                    {
                        aid1 = "AID 2",
                        appVerTerm = "App Ver Term 2",
                        tacDefault = "TAC Default 2",
                        tacDenial = "TAC Denial 2",
                        tacOnline = "TAC Online 2",
                        partialMatch = "Partial Match 2",
                        tdol = "TDOL 2",
                        ddol = "DDOL 2",
                        floorLimit = "Floor Limit 2",
                        emvTarget = "EMV Target 2",
                        emvMaxTarget = "EMV Max Target 2",
                        emvThreshold = "EMV Threshold 2"
                    },
                    new aid
                    {
                        aid1 = "AID 3",
                        appVerTerm = "App Ver Term 3",
                        tacDefault = "TAC Default 3",
                        tacDenial = "TAC Denial 3",
                        tacOnline = "TAC Online 3",
                        partialMatch = "Partial Match 3",
                        tdol = "TDOL 3",
                        ddol = "DDOL 3",
                        floorLimit = "Floor Limit 3",
                        emvTarget = "EMV Target 3",
                        emvMaxTarget = "EMV Max Target 3",
                        emvThreshold = "EMV Threshold 3"
                    }
                },
                cless_aids = new cless_aid[]
                {
                    new cless_aid
                    {
                        aid = "AID 1",
                        appVerTerm = "App Ver Term 1",
                        transLimit = "Trans Limit 1",
                        floorLimit = "Floor Limit 1",
                        cvmLimit = "CVM Limit 1",
                        odcvmLimit = "ODCVM Limit 1",
                        termAddCapabilities = "Term Add Capabilities 1",
                        termCapabilitiesCvm = "Term Capabilities CVM 1",
                        termCapabilitiesNoCvm = "Term Capabilities No CVM 1",
                        termRiskData = "Term Risk Data 1",
                        udol = "UDOL 1",
                        tacDefault = "TAC Default 1",
                        tacDenial = "TAC Denial 1",
                        tacOnline = "TAC Online 1",
                        drl = new drl[]
                        {
                            new drl
                            {
                                programId = "Program Id 1",
                                transLimit = "Trans Limit 1",
                                floorLimit = "Floor Limit 1",
                                cvmLimit = "CVM Limit 1"
                            },
                            new drl
                            {
                                programId = "Program Id 2",
                                transLimit = "Trans Limit 2",
                                floorLimit = "Floor Limit 2",
                                cvmLimit = "CVM Limit 2"
                            }
                        }
                    }
                },
                capks = new capk[]
                {
                    new capk
                    {
                        rid = "RID 1",
                        index = "Index 1",
                        modulus = "Modulus 1",
                        exponent = "Exponent 1",
                        checksum = "Checksum 1",
                        expiryDate = "Expiry Date 1"
                    }
                },
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "IIN Start 1", iinEnd = "IIN End 1", onlinePin = "Online PIN 1"}},
                tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1, productCode = "Product Code 1"}},
                discountCards = new discountCardList { discountRange = new discountRange[]
                {
                    new discountRange
                    {
                        iin = "IIN 1",
                        name = "Name 1",
                        type = "Type 1",
                        value = "Value 1",
                        grade = 1,
                        whitelist = new whitelistCard[] {new whitelistCard {pan = "PAN 2"}, new whitelistCard {pan = "PAN 1"}}
                    }
                }},
                localAccounts = new localAccountCustomer[]
                {
                    new localAccountCustomer
                    {
                        name = "Name 1",
                        pin = 1,
                        printValue = 2,
                        allowLoyalty = 3,
                        fuelOnly = 4,
                        getMiles = 5,
                        getReg = 6,
                        isPrePay = 7,
                        transLimit = "Trans Limit 1",
                        cards = new card[]
                        {
                            new card
                            {
                                pan = "PAN 1",
                                description = "Description 1",
                                discount = "Discount 1",
                                restrictions1 = 1,
                                restrictions2 = 2
                            }
                        }
                    }
                }
            };
            cards cards2 = new cards
            {
                aids = new aid[]
                {
                    new aid
                    {
                        aid1 = "AID 3",
                        appVerTerm = "App Ver Term 3",
                        tacDefault = "TAC Default 3",
                        tacDenial = "TAC Denial 3",
                        tacOnline = "TAC Online 3",
                        partialMatch = "Partial Match 3",
                        tdol = "TDOL 3",
                        ddol = "DDOL 3",
                        floorLimit = "Floor Limit 3",
                        emvTarget = "EMV Target 3",
                        emvMaxTarget = "EMV Max Target 3",
                        emvThreshold = "EMV Threshold 3"
                    },
                    new aid
                    {
                        aid1 = "AID 1",
                        appVerTerm = "App Ver Term 1",
                        tacDefault = "TAC Default 1",
                        tacDenial = "TAC Denial 1",
                        tacOnline = "TAC Online 1",
                        partialMatch = "Partial Match 1",
                        tdol = "TDOL 1",
                        ddol = "DDOL 1",
                        floorLimit = "Floor Limit 1",
                        emvTarget = "EMV Target 1",
                        emvMaxTarget = "EMV Max Target 1",
                        emvThreshold = "EMV Threshold 1"
                    },
                    new aid
                    {
                        aid1 = "AID 2",
                        appVerTerm = "App Ver Term 2",
                        tacDefault = "TAC Default 2",
                        tacDenial = "TAC Denial 2",
                        tacOnline = "TAC Online 2",
                        partialMatch = "Partial Match 2",
                        tdol = "TDOL 2",
                        ddol = "DDOL 2",
                        floorLimit = "Floor Limit 2",
                        emvTarget = "EMV Target 2",
                        emvMaxTarget = "EMV Max Target 2",
                        emvThreshold = "EMV Threshold 2"
                    }
                },
                cless_aids = new cless_aid[]
                {
                    new cless_aid
                    {
                        aid = "AID 1",
                        appVerTerm = "App Ver Term 1",
                        transLimit = "Trans Limit 1",
                        floorLimit = "Floor Limit 1",
                        cvmLimit = "CVM Limit 1",
                        odcvmLimit = "ODCVM Limit 1",
                        termAddCapabilities = "Term Add Capabilities 1",
                        termCapabilitiesCvm = "Term Capabilities CVM 1",
                        termCapabilitiesNoCvm = "Term Capabilities No CVM 1",
                        termRiskData = "Term Risk Data 1",
                        udol = "UDOL 1",
                        tacDefault = "TAC Default 1",
                        tacDenial = "TAC Denial 1",
                        tacOnline = "TAC Online 1",
                        drl = new drl[]
                        {
                            new drl
                            {
                                programId = "Program Id 2",
                                transLimit = "Trans Limit 2",
                                floorLimit = "Floor Limit 2",
                                cvmLimit = "CVM Limit 2"
                            },
                            new drl
                            {
                                programId = "Program Id 1",
                                transLimit = "Trans Limit 1",
                                floorLimit = "Floor Limit 1",
                                cvmLimit = "CVM Limit 1"
                            }
                        }
                    }
                },
                capks = new capk[]
                {
                    new capk
                    {
                        rid = "RID 1",
                        index = "Index 1",
                        modulus = "Modulus 1",
                        exponent = "Exponent 1",
                        checksum = "Checksum 1",
                        expiryDate = "Expiry Date 1"
                    }
                },
                fuelcards = new fuelcard[] {new fuelcard {iinStart = "IIN Start 1", iinEnd = "IIN End 1", onlinePin = "Online PIN 1"}},
                tariffMapping = new gradeMapping[] {new gradeMapping {grade = 1, productCode = "Product Code 1"}},
                discountCards = new discountCardList {discountRange = new discountRange[]
                {
                    new discountRange
                    {
                        iin = "IIN 1",
                        name = "Name 1",
                        type = "Type 1",
                        value = "Value 1",
                        grade = 1,
                        whitelist = new whitelistCard[] {new whitelistCard {pan = "PAN 1"}, new whitelistCard {pan = "PAN 2"}}
                    }
                }},
                localAccounts = new localAccountCustomer[]
                {
                    new localAccountCustomer
                    {
                        name = "Name 1",
                        pin = 1,
                        printValue = 2,
                        allowLoyalty = 3,
                        fuelOnly = 4,
                        getMiles = 5,
                        getReg = 6,
                        isPrePay = 7,
                        transLimit = "Trans Limit 1",
                        cards = new card[]
                        {
                            new card
                            {
                                pan = "PAN 1",
                                description = "Description 1",
                                discount = "Discount 1",
                                restrictions1 = 1,
                                restrictions2 = 2
                            }
                        }
                    }
                }
            };

            // Act
            bool result = CompareConfig.CardsEqual(cards1, cards2);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void test_loyalty_equal_empty_null_and_empty()
        {
            // Arrange
            const loyalty loyaltya1 = null;
            const loyalty loyaltyb1 = null;
            loyalty loyaltya2 = new loyalty();
            loyalty loyaltyb2 = new loyalty();
            loyalty loyaltya3 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltyb3 = new loyalty {morrisons = new morrisons()};

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);
            bool result5 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb3);
            bool result6 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb2);
            bool result7 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb3);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_terminal_equal_empty_null_and_empty()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltyb1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltya2 = new loyalty {morrisons = new morrisons {terminal = new terminal()}};
            loyalty loyaltyb2 = new loyalty {morrisons = new morrisons {terminal = new terminal()}};
            loyalty loyaltya3 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = ""}}};
            loyalty loyaltyb3 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = ""}}};

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);
            bool result5 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb3);
            bool result6 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb2);
            bool result7 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb3);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_terminal_site_id_and_footer2_equal_with_values()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = "A", footer2 = "B"}}};
            loyalty loyaltyb1 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = "A", footer2 = "B"}}};
            loyalty loyaltya2 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = "A", footer2 = "C"}}};
            loyalty loyaltyb2 = new loyalty {morrisons = new morrisons {terminal = new terminal {siteId = "A", footer2 = "C"}}};

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_hosts_equal()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltyb1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltya2 = new loyalty {morrisons = new morrisons {hosts = new host[0]}};
            loyalty loyaltyb2 = new loyalty {morrisons = new morrisons {hosts = new host[0]}};
            loyalty loyaltya3 = new loyalty {morrisons = new morrisons {hosts = new host[] {null}}};
            loyalty loyaltyb3 = new loyalty {morrisons = new morrisons {hosts = new host[] {null}}};
            loyalty loyaltya4 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host()}}};
            loyalty loyaltyb4 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host()}}};
            loyalty loyaltya5 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host {ip = ""}}}};
            loyalty loyaltyb5 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host {ip = ""}}}};
            loyalty loyaltya6 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host {ip = ""}, new host {ip = ""}}}};
            loyalty loyaltyb6 = new loyalty {morrisons = new morrisons {hosts = new host[] {new host {ip = ""}, new host {ip = ""}}}};

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);
            bool result5 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb3);
            bool result6 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb2);
            bool result7 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb3);
            bool result8 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb4);
            bool result9 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb3);
            bool result10 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb4);
            bool result11 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb5);
            bool result12 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb4);
            bool result13 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb5);
            bool result14 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb6);
            bool result15 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb5);
            bool result16 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_hosts_ip_and_port_equal_with_values()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty
            {
                morrisons = new morrisons {hosts = new host[] {new host {ip = "A", port = 1}, new host {ip = "B", port = 2}}}
            };
            loyalty loyaltyb1 = new loyalty
            {
                morrisons = new morrisons {hosts = new host[] {new host {ip = "B", port = 2}, new host {ip = "A", port = 1}}}
            };
            loyalty loyaltya2 = new loyalty
            {
                morrisons = new morrisons {hosts = new host[] {new host {ip = "A", port = 2}, new host {ip = "B", port = 1}}}
            };
            loyalty loyaltyb2 = new loyalty
            {
                morrisons = new morrisons {hosts = new host[] {new host {ip = "B", port = 1}, new host {ip = "A", port = 2}}}
            };

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_iins_equal()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltyb1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltya2 = new loyalty {morrisons = new morrisons {iins = new iin[0]}};
            loyalty loyaltyb2 = new loyalty {morrisons = new morrisons {iins = new iin[0]}};
            loyalty loyaltya3 = new loyalty {morrisons = new morrisons {iins = new iin[] {null}}};
            loyalty loyaltyb3 = new loyalty {morrisons = new morrisons {iins = new iin[] {null}}};
            loyalty loyaltya4 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin()}}};
            loyalty loyaltyb4 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin()}}};
            loyalty loyaltya5 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin {low = ""}}}};
            loyalty loyaltyb5 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin {low = ""}}}};
            loyalty loyaltya6 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin {low = ""}, new iin {low = ""}}}};
            loyalty loyaltyb6 = new loyalty {morrisons = new morrisons {iins = new iin[] {new iin {low = ""}, new iin {low = ""}}}};

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);
            bool result5 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb3);
            bool result6 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb2);
            bool result7 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb3);
            bool result8 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb4);
            bool result9 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb3);
            bool result10 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb4);
            bool result11 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb5);
            bool result12 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb4);
            bool result13 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb5);
            bool result14 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb6);
            bool result15 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb5);
            bool result16 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_iins_low_and_high_equal_with_values()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty
            {
                morrisons = new morrisons {iins = new iin[] {new iin {low = "A", high = "C"}, new iin {low = "B", high = "D"}}}
            };
            loyalty loyaltyb1 = new loyalty
            {
                morrisons = new morrisons {iins = new iin[] {new iin {low = "B", high = "D"}, new iin {low = "A", high = "C"}}}
            };
            loyalty loyaltya2 = new loyalty
            {
                morrisons = new morrisons {iins = new iin[] {new iin {low = "A", high = "D"}, new iin {low = "B", high = "C"}}}
            };
            loyalty loyaltyb2 = new loyalty
            {
                morrisons = new morrisons {iins = new iin[] {new iin {low = "B", high = "C"}, new iin {low = "A", high = "D"}}}
            };

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_tariff_mapping_equal()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltyb1 = new loyalty {morrisons = new morrisons()};
            loyalty loyaltya2 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[0]}};
            loyalty loyaltyb2 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[0]}};
            loyalty loyaltya3 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {null}}};
            loyalty loyaltyb3 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {null}}};
            loyalty loyaltya4 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {new mapping()}}};
            loyalty loyaltyb4 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {new mapping()}}};
            loyalty loyaltya5 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {new mapping {productCode = ""}}}};
            loyalty loyaltyb5 = new loyalty {morrisons = new morrisons {tariffMapping = new mapping[] {new mapping {productCode = ""}}}};
            loyalty loyaltya6 = new loyalty
            {
                morrisons = new morrisons {tariffMapping = new mapping[] {new mapping {productCode = ""}, new mapping {productCode = ""}}}
            };
            loyalty loyaltyb6 = new loyalty
            {
                morrisons = new morrisons {tariffMapping = new mapping[] {new mapping {productCode = ""}, new mapping {productCode = ""}}}
            };

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);
            bool result5 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb3);
            bool result6 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb2);
            bool result7 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb3);
            bool result8 = CompareConfig.LoyaltyEqual(loyaltya3, loyaltyb4);
            bool result9 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb3);
            bool result10 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb4);
            bool result11 = CompareConfig.LoyaltyEqual(loyaltya4, loyaltyb5);
            bool result12 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb4);
            bool result13 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb5);
            bool result14 = CompareConfig.LoyaltyEqual(loyaltya5, loyaltyb6);
            bool result15 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb5);
            bool result16 = CompareConfig.LoyaltyEqual(loyaltya6, loyaltyb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_morrisons_tariff_mapping_product_code_and_loyalty_code_equal_with_values()
        {
            // Arrange
            loyalty loyaltya1 = new loyalty
            {
                morrisons = new morrisons
                {
                    tariffMapping = new mapping[]
                        {new mapping {productCode = "A", loyaltyCode = "C"}, new mapping {productCode = "B", loyaltyCode = "D"}}
                }
            };
            loyalty loyaltyb1 = new loyalty
            {
                morrisons = new morrisons
                {
                    tariffMapping = new mapping[]
                        {new mapping {productCode = "B", loyaltyCode = "D"}, new mapping {productCode = "A", loyaltyCode = "C"}}
                }
            };
            loyalty loyaltya2 = new loyalty
            {
                morrisons = new morrisons
                {
                    tariffMapping = new mapping[]
                        {new mapping {productCode = "A", loyaltyCode = "D"}, new mapping {productCode = "B", loyaltyCode = "C"}}
                }
            };
            loyalty loyaltyb2 = new loyalty
            {
                morrisons = new morrisons
                {
                    tariffMapping = new mapping[]
                        {new mapping {productCode = "B", loyaltyCode = "C"}, new mapping {productCode = "A", loyaltyCode = "D"}}
                }
            };

            // Act
            bool result1 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb1);
            bool result2 = CompareConfig.LoyaltyEqual(loyaltya1, loyaltyb2);
            bool result3 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb1);
            bool result4 = CompareConfig.LoyaltyEqual(loyaltya2, loyaltyb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_populated_loyalty()
        {
            // Arrange
            loyalty loyalty1 = new loyalty
            {
                morrisons = new morrisons
                {
                    terminal =
                        new terminal {siteId = "Site ID 1", terminalId = "Terminal ID 1", footer1 = "Footer 1", footer2 = "Footer 2"},
                    hosts = new host[] {new host {ip = "IP 1", port = 1}, new host {ip = "IP2", port = 2}},
                    iins = new iin[]
                    {
                        new iin {low = "Low 1", high = "high1"}, new iin {low = "Low 2", high = "High 2"},
                        new iin {low = "Low 3", high = "High 3"}
                    },
                    tariffMapping = new mapping[]
                    {
                        new mapping {productCode = "Product Code 1", loyaltyCode = "Loyalty Code 1"},
                        new mapping {productCode = "Product Code 2", loyaltyCode = "Loyalty Code 2"}
                    }
                }
            };
            loyalty loyalty2 = new loyalty
            {
                morrisons = new morrisons
                {
                    terminal =
                        new terminal {siteId = "Site ID 1", terminalId = "Terminal ID 1", footer1 = "Footer 1", footer2 = "Footer 2"},
                    hosts = new host[] {new host {ip = "IP2", port = 2}, new host {ip = "IP 1", port = 1}},
                    iins = new iin[]
                    {
                        new iin {low = "Low 3", high = "High 3"}, new iin {low = "Low 1", high = "high1"},
                        new iin {low = "Low 2", high = "High 2"}
                    },
                    tariffMapping = new mapping[]
                    {
                        new mapping {productCode = "Product Code 2", loyaltyCode = "Loyalty Code 2"},
                        new mapping {productCode = "Product Code 1", loyaltyCode = "Loyalty Code 1"}
                    }
                }
            };

            // Act
            bool result = CompareConfig.LoyaltyEqual(loyalty1, loyalty2);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void test_washes_equal_empty_null_and_empty()
        {
            // Arrange
            const wash[] washesa1 = null;
            const wash[] washesb1 = null;
            wash[] washesa2 = new wash[0];
            wash[] washesb2 = new wash[0];
            wash[] washesa3 = {null};
            wash[] washesb3 = {null};
            wash[] washesa4 = {new wash()};
            wash[] washesb4 = {new wash()};
            wash[] washesa5 = {new wash {programId = 1}};
            wash[] washesb5 = {new wash {programId = 1}};
            wash[] washesa6 = {new wash {programId = 1}, new wash {programId = 1}};
            wash[] washesb6 = {new wash {programId = 1}, new wash {programId = 1}};

            // Act
            bool result1 = CompareConfig.WashesEqual(washesa1, washesb1);
            bool result2 = CompareConfig.WashesEqual(washesa1, washesb2);
            bool result3 = CompareConfig.WashesEqual(washesa2, washesb1);
            bool result4 = CompareConfig.WashesEqual(washesa2, washesb2);
            bool result5 = CompareConfig.WashesEqual(washesa2, washesb3);
            bool result6 = CompareConfig.WashesEqual(washesa3, washesb2);
            bool result7 = CompareConfig.WashesEqual(washesa3, washesb3);
            bool result8 = CompareConfig.WashesEqual(washesa3, washesb4);
            bool result9 = CompareConfig.WashesEqual(washesa4, washesb3);
            bool result10 = CompareConfig.WashesEqual(washesa4, washesb4);
            bool result11 = CompareConfig.WashesEqual(washesa4, washesb5);
            bool result12 = CompareConfig.WashesEqual(washesa5, washesb3);
            bool result13 = CompareConfig.WashesEqual(washesa5, washesb5);
            bool result14 = CompareConfig.WashesEqual(washesa5, washesb6);
            bool result15 = CompareConfig.WashesEqual(washesa6, washesb5);
            bool result16 = CompareConfig.WashesEqual(washesa6, washesb6);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            result6.Should().BeFalse();
            result7.Should().BeTrue();
            result8.Should().BeFalse();
            result9.Should().BeFalse();
            result10.Should().BeTrue();
            result11.Should().BeFalse();
            result12.Should().BeFalse();
            result13.Should().BeTrue();
            result14.Should().BeFalse();
            result15.Should().BeFalse();
            result16.Should().BeTrue();
        }

        [Fact]
        public void test_washes_program_id_and_vat_rate_equal_with_values()
        {
            // Arrange
            wash[] washesa1 = {new wash {programId = 1, vatRate = "A"}, new wash {programId = 2, vatRate = "B"}};
            wash[] washesb1 = {new wash {programId = 2, vatRate = "B"}, new wash {programId = 1, vatRate = "A"}};
            wash[] washesa2 = {new wash {programId = 1, vatRate = "B"}, new wash {programId = 2, vatRate = "A"}};
            wash[] washesb2 = {new wash {programId = 2, vatRate = "A"}, new wash {programId = 1, vatRate = "B"}};

            // Act
            bool result1 = CompareConfig.WashesEqual(washesa1, washesb1);
            bool result2 = CompareConfig.WashesEqual(washesa1, washesb2);
            bool result3 = CompareConfig.WashesEqual(washesa2, washesb1);
            bool result4 = CompareConfig.WashesEqual(washesa2, washesb2);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeFalse();
            result3.Should().BeFalse();
            result4.Should().BeTrue();
        }

        [Fact]
        public void test_populated_washes()
        {
            // Arrange
            wash[] washes1 =
            {
                new wash
                {
                    programId = 1,
                    productCode = "Product Code 1",
                    displayDescription = "Description 1",
                    price = "Price 1",
                    vatRate = "VAT Rate 1"
                },
                new wash
                {
                    programId = 2,
                    productCode = "Product Code 2",
                    displayDescription = "Description 2",
                    price = "Price 2",
                    vatRate = "VAT Rate 2"
                }
            };
            wash[] washes2 =
            {
                new wash
                {
                    programId = 2,
                    productCode = "Product Code 2",
                    displayDescription = "Description 2",
                    price = "Price 2",
                    vatRate = "VAT Rate 2"
                },
                new wash
                {
                    programId = 1,
                    productCode = "Product Code 1",
                    displayDescription = "Description 1",
                    price = "Price 1",
                    vatRate = "VAT Rate 1"
                }
            };

            // Act
            bool result = CompareConfig.WashesEqual(washes1, washes2);

            // Assert
            result.Should().BeTrue();
        }
    }
}