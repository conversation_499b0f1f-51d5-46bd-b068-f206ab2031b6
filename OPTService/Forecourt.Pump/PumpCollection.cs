using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.Pump.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Forecourt.Pump
{
    public class PumpCollection : Loggable, IPumpCollection
    {
        private readonly ConcurrentDictionary<byte, IPump> _allPumps;
        private readonly IHydraDb _hydraDb;
        private readonly IOptCollection _allOpts;
        private bool _unmanned = false;

        public IEnumerable<IPump> AllPumps => _allPumps.Values;

        /// <inheritdoc cref="IPumpCollection"/>
        public ConcurrentDictionary<byte, OnPumpStateActiveInfo> OnPumpStateActive { get; } = new ConcurrentDictionary<byte, OnPumpStateActiveInfo>();

        public PumpCollection(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IOptCollection allOpts, IConfigurationManager configurationManager) : base(logManager, loggerName,
            configurationManager)
        {
            _allPumps = new ConcurrentDictionary<byte, IPump>();
            _hydraDb = hydraDb;
            _allOpts = allOpts;
        }

        public bool TryGetPump(byte number, out IPump pump)
        {
            return _allPumps.TryGetValue(number, out pump);
        }

        public IPump GetPump(byte number)
        {
            if (_allPumps.TryGetValue(number, out IPump pump))
            {
                return pump;
            }
            else
            {
                IPump newPump = new Pump(number, _hydraDb, LogManager, Pump.LoggerName, configurationManager:ConfigurationManager);
                newPump.SetUnmannedPseudoPos(_unmanned);
                _allPumps.AddOrUpdate(number, newPump, (k, v) => newPump);
                return newPump;
            }
        }

        public IList<PumpStateItem> GetPumpsStates(int optId)
        {
            IList<PumpStateItem> results = new List<PumpStateItem>();
            foreach (IPump thePump in _allPumps.Values.Where(x => x.Opt?.Id == optId))
            {
                results.Add(new PumpStateItem(thePump.Number, thePump.PumpState));
            }

            return results;
        }

        public IPump GetPumpForTid(string tid)
        {
            return string.IsNullOrWhiteSpace(tid) ? null : _allPumps.Values.FirstOrDefault(x => tid.Equals(x.Tid));
        }

        public void AllocatePumps(IEnumerable<string> allTids)
        {
            DoAction(() =>
            {
                var pumpTids = _hydraDb.FetchAllPumps(allTids);
                if (pumpTids.Any())
                {
                    foreach (IOpt opt in _allOpts.AllOpts)
                    {
                        opt.ClearPumps();
                    }
                }

                var oldPumps = new ConcurrentDictionary<byte, IPump>(_allPumps);
                _allPumps.Clear();

                foreach (var pumpTid in pumpTids)
                {
                    var opt = _allOpts.GetOptForIdString(pumpTid.OptId);
                    var pumpNumber = (byte)pumpTid.Number;
                    if (oldPumps.TryGetValue(pumpNumber, out var pump))
                    {
                        pump.SetOpt(opt);
                        pump.SetTid(pumpTid.Tid);
                    }
                    else
                    {
                        pump = new Pump(pumpNumber, _hydraDb, LogManager, Pump.LoggerName, opt, pumpTid.Tid, ConfigurationManager);
                        pump.SetUnmannedPseudoPos(_unmanned);
                    }

                    _allPumps.AddOrUpdate(pumpNumber, pump, (k, v) => pump);
                    opt?.AddPump(pump);

                    AllocatePumpsSetPumpMode(opt, pump, pumpTid);
                }
            }, null);
        }

        private void AllocatePumpsSetPumpMode(IOpt opt, IPump pump, PumpTid pumpTid)
        {
            if (opt?.Connected ?? false)
            {
                DoDeferredLogging(LogLevel.Info,
                    "Initialised (BEFORE) Opt/Pump", () => new[]
                    {
                        $"{opt.Id}/{pump.Number}; TID: {pump.Tid}; OPT/Pump Mode: {opt.Mode}/{pump.State};",
                        $"Current: KioskOnly/Mixed: {pumpTid.CurrentKioskOnly}/{pumpTid.CurrentMixed}; Default: KioskOnly/Mixed: {pumpTid.DefaultKioskOnly}/{pumpTid.DefaultMixed}"
                    });
            }

            if (pumpTid.DefaultKioskOnly)
            {
                pump.SetKioskOnly(true, false);
            }
            else if (pumpTid.DefaultMixed)
            {
                pump.SetMixed(true, false);
            }
            else
            {
                pump.SetOutsideOnly(true, false);
            }

            if (pump.KioskUse)
            {
                // Do nothing as pump is already in the correct state!
            }
            else if (pumpTid.CurrentKioskOnly)
            {
                pump.SetKioskOnly(false, false);
            }
            else if (pumpTid.CurrentMixed)
            {
                pump.SetMixed(false, false);
            }
            else
            {
                pump.SetOutsideOnly(false, false);
            }

            if (pumpTid.Closed && !pump.PumpIsClosed)
            {
                pump.ClosePump(false);
            }
            else if (!pumpTid.Closed && pump.PumpIsClosed)
            {
                pump.OpenPump(false);
            }

            pump.SetMaxFillOverrideForFuelCards(pumpTid.MaxFillOverrideForFuelCards, false);
            pump.SetMaxFillOverrideForPaymentCards(pumpTid.MaxFillOverrideForPaymentCards, false);

            if (opt?.Connected ?? false)
            {
                DoDeferredLogging(LogLevel.Info, "Initialised (AFTER)  Opt/Pump", () => new[] {$"{opt.Id}/{pump.Number}; TID: {pump.Tid}; OPT/Pump Mode: {opt.Mode}/{pump.State};"});
            }
        }

        public bool IsUnmannedPseudoPos => _unmanned;

        public void SetUnmannedPseudoPos(bool isOn)
        {
            if (_unmanned != isOn)
            {
                _unmanned = isOn;
                foreach (IPump pump in AllPumps)
                {
                    pump.SetUnmannedPseudoPos(_unmanned);
                }
            }
        }

        public void SetKioskPayment()
        {
            foreach (var pump in _allPumps.Keys)
            {
                _allPumps[pump].SetHasKioskPayment();
            }
        }

        public void UpdateParentId(byte pump, IMessageTracking messageTracking)
        {
            if (TryGetPump(pump, out var thePump))
            {
                if (Guid.TryParse(thePump.TransactionSummary?.LoggingReference, out var txnRef))
                {
                    messageTracking.ParentId = txnRef;
                }
            }
        }
    }
}
