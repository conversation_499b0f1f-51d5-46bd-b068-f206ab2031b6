using System.Collections.Generic;
using System.Net;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;

namespace Forecourt.Pos.HydraDb.Interfaces
{
    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pos, split down by area
    /// </summary>
    public interface IHydraDb: 
        Core.HydraDb.Interfaces.IHydraDb,
        IHydraDbPos,
        IHydraDbPosMobile
    {
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pos, mobile functionality
    /// </summary>
    public interface IHydraDbPosMobile
    {
        /// <summary>
        /// Fetch the Hydra Mobile end point from the database.
        /// </summary>
        /// <returns>The Hydra Mobile end point.</returns>
        connGenericEndPoint FetchHydraMobileEndPoint();

        /// <summary>
        /// Store End Point for Hydra Mobile in the database.
        /// </summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="port">Port number to set.</param>
        void SetHydraMobileEndPoint(IPAddress ip, int port);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pos, core functionality
    /// </summary>
    public interface IHydraDbPos
    {
        /// <summary>Get the receipts for the given OPT from the database.</summary>
        /// <param name="opt">OPT Id to fetch receipts for.</param>
        /// <returns>List of receipts for this OPT.</returns>
        IList<ReceiptInfo> GetReceiptsForOpt(string opt);

        /// <summary>Get the receipt for the given card number from the database.</summary>
        /// <param name="cardNumber">Card number to reference receipt.</param>
        /// <param name="transactionNumber">Transaction number to reference receipt.</param>
        /// <param name="includeExpired">Include expired receipts if true, otherwise exclude expired receipts.</param>
        /// <returns>The receipt, or null if card number not found.</returns>
        ReceiptInfo GetReceipt(string cardNumber, long transactionNumber = 0L, bool includeExpired = false);


        /// <summary>Gets receipts for the given card number from the database.</summary>
        /// <param name="cardNumber">Card number to reference receipt.</param>
        /// <param name="includeExpired">Include expired receipts if true, otherwise exclude expired receipts.</param>
        /// <returns>The receipts, or empty collection if card number not found.</returns>
        IList<ReceiptInfo> GetReceipts(string cardNumber, bool includeExpired = false);
    }
}
