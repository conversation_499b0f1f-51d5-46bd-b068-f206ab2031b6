using Forecourt.Core.Opt.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;

namespace OPT.Common.Workers.Interfaces
{
    public interface ICarWashWorker : IClientWorker<string>
    {
        /// <summary>Send Request request to Car Wash server.</summary>
        /// <param name="opt">OPT for which the request is being sent.</param>
        /// <param name="machine">Machine for which to send request.</param>
        /// <param name="prog">Programme for which to send request.</param>
        void SendRequestToCarWash(IOpt opt, string machine, byte prog);
    }
}