using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Custom PosInModeWorker, specfic to RetalixPos
    /// </summary>
    public class RetalixPosInModeWorker : PosInModeWorker, IRetalixPosInModeWorker
    {
        /// <inheritdoc/>
        public RetalixPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutWorker, IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutWorker, configurationManager)
        {
        }

        /// <inheritdoc/>
        protected override bool DoRequestModeChange(IPump pump, ModeChangeType mode, string reference, bool overridePumpIsClosed)
        {
            // false = Don't want to break Retalix behaviour
            switch (mode)
            {
                case ModeChangeType.Close:
                    base.DoRequestModeChange(pump, mode, reference, false);
                    base.DoRequestModeChange(pump, ModeChangeType.Open, reference, false);
                    return base.DoRequestModeChange(pump, ModeChangeType.KioskOnly, reference, false);

                default:
                    return base.DoRequestModeChange(pump, mode, reference, false);
            }
        }
    }
}
