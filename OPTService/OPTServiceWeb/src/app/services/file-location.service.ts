import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { map, catchError } from 'rxjs/operators';
import { Observable, throwError } from 'rxjs';
import { FileLocations } from '../core/models/fileLocations.model';
import { environment } from '../../environments/environment';
import { NGXLogger } from 'ngx-logger';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class FileLocationService {

  private baseUrl: string;

  /**
   * The Service constructor
   * @param http The http client used for requests
   */
  constructor(private http: HttpClient,
    private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.fileLocationsController.base;
  }

  /**
   * Gets the list of file locations
   */
  public getFileLocations(itemId: string): Observable<any> {
    const url = this.baseUrl + environment.fileLocationsController.getFileLocations;  
    const options = {
      params: {}
    };
    if (itemId) {
      this.logger.debug('Setting fileLocationName =', itemId);
      options.params = new HttpParams().set("fileLocationName", itemId);
    }

    this.logger.debug(`Getting File Locations via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Sets a property
   * @param fileLocations The file locations object
   */
  public setProperty(fileLocations: FileLocations, serviceName: string): Observable<any> {
    const url = this.baseUrl + serviceName;
    const options = {
      params: {}
    };
    this.logger.debug(`Setting property in ${url}`);
    return this.http.post(url, fileLocations, options);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
