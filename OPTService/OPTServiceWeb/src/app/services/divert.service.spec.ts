import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { DivertService } from './divert.service';
import { environment } from 'src/environments/environment';
import { DivertDetails } from '../core/models/divertDetails.model';
import { DOCUMENT } from '@angular/common';

describe('DivertService', () => {
  let divertService: DivertService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        DivertService,
        {provide: NGXLogger, useValue: ngxLoggertSpy},
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    divertService = TestBed.inject(DivertService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(divertService).toBeTruthy();
  });

  it('.getDivertDetails() should handle success response from API', () => {
    //Arrange
    let divertDetails = {
      AsdaDayEndReport: false,
      AutoAuth: true,
      CardReferences: [{
        Acquirer: "Visa,Mastercard,Delta&Electron",
        FuelCard: false,
        InUse: false,
        Name: "VISA",
        Reference: 1
      }],
      FilePruneDays: 30,
      ForwardFuelPriceUpdate: true,
      FuelCategory: 99,
      FuellingBackoffAuth: 0,
      FuellingBackoffPreAuth: 0,
      FuellingBackoffStopOnly: 0,
      FuellingBackoffStopStart: 0,
      FuellingIndefiniteWait: true,
      FuellingWaitMinutes: 5,
      LoyaltyAvailable: ["Morrisons"],
      MediaChannel: true,
      MorrisonLoyaltyAvailable: true,
      PaymentTimeoutMixed: 15,
      PaymentTimeoutNozzleDown: 15,
      PaymentTimeoutOpt: 15,
      PaymentTimeoutPod: 300,
      PosClaimNumber: 99,
      ReceiptMaxCount: 12,
      ReceiptPruneDays: 14,
      ReceiptTimeout: 3600,
      TillNumber: 99,
      TransactionPruneDays: 50,
      UnmannedPseudoPos: true
    };
    
    //Act
    divertService.getDivertDetails().subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(divertDetails);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.getDivertDetails);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(divertDetails);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getDivertDetails() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    divertService.getDivertDetails().subscribe(DataTransfer =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.getDivertDetails);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.cancelDivertOptService() should handle success response from API', () => {

    //Arrange
    //Act
    divertService.cancelDivertOptService().subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.cancelDivertOptService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.cancelDivertOptService() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    divertService.cancelDivertOptService().subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.cancelDivertOptService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.divertOptService() should handle success response from API', () => {
    //Arrange
    let divertDetailsData = new DivertDetails();
        
    //Act
    divertService.divertOptService(divertDetailsData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.divertOptService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.divertOptService() should handle unsuccess response from API', () => {
    //Arrange
    let divertDetailsData = new DivertDetails();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    divertService.divertOptService(divertDetailsData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.divertController.base + environment.divertController.divertOptService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

});
