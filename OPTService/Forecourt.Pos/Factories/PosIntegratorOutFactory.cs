using Forecourt.Core.Pos.Enums;
using Forecourt.Pos.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Pos.Factories
{
    /// <summary>
    /// POS integrator (Out) factory
    /// </summary>
    public class PosIntegratorOutFactory : PosIntegratorFactory<IPosIntegratorOut<IMessageTracking>>, IPosIntegratorOutFactory
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public PosIntegratorOutFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IPosIntegratorOut<IMessageTracking>> resolveTypeInstance) :
            base(logManager, $"{nameof(PosIntegratorOutFactory)}", configurationManager, resolveTypeInstance)
        {
            AddItem($"{PosType.None}", ConfigConstants.None, (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.HydraPos}", "Htec HydraPOS", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.Retalix}", "NCR Retalix", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.GenericSignalRApi}", "Generic SignalR and RestAPI", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.MadicApiSignalR}", "MADIC RestAPI and SignalR", (key) => ResolveTypeInstance(key));
        }
    }
}
