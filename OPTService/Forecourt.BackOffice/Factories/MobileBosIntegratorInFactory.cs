using Forecourt.Bos.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Bos.Factories
{
    /// <summary>
    /// (Mobile) BOS integrator factory
    /// </summary>
    public class MobileBosIntegratorInFactory : Factory<string, IBosIntegratorInJournal<IMessageTracking>>, IMobileBosIntegratorInFactory
    {
        private readonly Func<string, IBosIntegratorInJournal<IMessageTracking>> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public MobileBosIntegratorInFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IBosIntegratorInJournal<IMessageTracking>> resolveTypeInstance) :
            base(logManager, $"{nameof(MobileBosIntegratorInFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => null);
            AddItem(Core.Configuration.Constants.Integrator.PosMobileTypeHydraMobile, "Htec HydraMobile (Orbis)", (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.PosMobileTypeHydraMobileStatus, "Htec HydraMobile (Status only)", (key) => _resolveTypeInstance(key));
        }
    }
}
