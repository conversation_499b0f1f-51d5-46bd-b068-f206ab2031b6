$configuration_name = $args[0]
$node_url = "https://nodejs.org/dist/latest-fermium/"
$node_install_file = ".\node_install.msi"

Write-host "Building OPTServiceWeb for configuration: " $configuration_name

Get-Command node >$null 2>&1
IF ($? -ne $true)
{
    Write-Host "node.js not found. Attempting to install it."

    # Gets the latest node 14 Windows x64 installer filename
    $r=Invoke-WebRequest $node_url -UseBasicParsing  
    $node_file = $r.Links |Where-Object{$_.href -match "-x64.msi"} |Select-Object -unique "href" |Sort-Object -desc date |Select-Object -First 1
    $node_file = $node_file -replace "@{href="
    $node_file = $node_file -replace "}"

    # Downloads node, silently installs it and then removes the installer
    Invoke-WebRequest -Uri $node_url$node_file -OutFile $node_install_file
    msiexec.exe /i $node_install_file /qn
    Remove-Item $node_install_file    
}

Get-Command ng >$null 2>&1
IF ($? -ne $true)
{
    Write-Host "Angular not found. Intalling it..."
    npm install -g angular-cli
}

npm install

IF ($configuration_name -eq "Release")
{
    ng build --prod
}
ELSE
{
    ng build
}
