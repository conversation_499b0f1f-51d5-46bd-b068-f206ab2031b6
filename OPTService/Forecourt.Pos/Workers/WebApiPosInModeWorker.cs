using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc/>
    public class WebApiPosInModeWorker: PosInModeWorker, IWebApiPosInModeWorker
    {
        /// <inheritdoc/>
        public WebApiPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutTransientWorker,
            IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutTransientWorker, configurationManager)
        {
        }

        /// <inheritdoc/>
        Result<StatusCodeResult> Htec.Hydra.Core.Pos.Interfaces.Core.IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>.RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            var result = RequestModeChange(pump, mode, message);

            return Result.Success<StatusCodeResult>(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(System.Net.HttpStatusCode.BadRequest, new System.Exception(result.Error)));
        }
    }
}
