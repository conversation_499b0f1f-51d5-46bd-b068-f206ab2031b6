using System;
using System.Net;

namespace Forecourt.Service.OPTServiceClasses
{
    public class RequestBooking : GetTransactionsDetails
    {
        /// <summary>
        /// Transaction id (external)
        /// </summary>
        public string ExternalTransId { get; set; }
    }

    public abstract class UpdateBooking : RequestBooking
    {
        /// <summary>
        /// Booking Id
        /// </summary>
        public long Id { get; set; }
    }

    public class UpdateBookingExternal : UpdateBooking
    {
        /// <summary>
        /// Shift id
        /// </summary>
        public int ShiftId { get; set; }

        /// <summary>
        /// Period id
        /// </summary>
        public int PeriodId { get; set; }

        /// <summary>
        /// Business Date
        /// </summary>
        public DateTime BusinessDate { get; set; }
    }

    public class UpdateBookingFail : UpdateBooking
    {
        /// <summary>
        /// Status code, in <see cref="HttpStatusCode"/> format
        /// </summary>
        public HttpStatusCode StatusCode { get; set; }

        /// <summary>
        /// Any status response
        /// </summary>
        public string StatusResponse { get; set; }

        /// <summary>
        /// Retry count
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Next time to retry booking
        /// </summary>
        public DateTime NextRetryDate { get; set; }
    }

    public class CompleteBooking : UpdateBooking
    {
        /// <summary>
        /// Date the transaction was succesfully booked
        /// </summary>
        public DateTime BookedDate { get; set; }
    }
}
