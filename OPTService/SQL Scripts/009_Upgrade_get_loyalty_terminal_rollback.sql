USE [Hydra]

PRINT N'HOPT-1491 - OPT Service - Web UI - Unable to update Loyalty settings if loyalty is not set to present...Begin';
PRINT N'Updating Procedure [dbo].[GetLoyaltyTerminal]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyTerminal') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyTerminal
END
GO

CREATE PROCEDURE GetLoyaltyTerminal
  @name VARCHAR(MAX) AS
BEGIN
    SELECT SiteID, TerminalID, Footer1, Footer2, Timeout, ApiKey, HttpHeader
	FROM LoyaltyTerminal T JOIN LoyaltyReference R ON T.LoyaltyRef = R.LoyaltyRef AND R.LoyaltyName = @name AND R.LoyaltyPresent = 1
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddLoyaltyReference') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE AddLoyaltyReference
END
GO

CREATE procedure AddLoyaltyReference
 @name varchar(max) as
 begin
  begin try
   begin transaction
    declare @loyaltyRef int
    if not exists (select * from LoyaltyReference where LoyaltyName = @name)
     begin
      if exists (select * from LoyaltyReference)
       begin
        set @loyaltyRef = (select max(LoyaltyRef) from LoyaltyReference) + 1
       end
      else
       begin
        set @loyaltyRef = 1
       end
      insert into LoyaltyReference (LoyaltyRef, LoyaltyName, LoyaltyPresent) values (@loyaltyRef, @name, 1)
      if not exists (select * from LoyaltyTerminal where LoyaltyRef = @loyaltyRef)
       begin
        insert into LoyaltyTerminal (LoyaltyRef) values (@loyaltyRef)
       end
     end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyHosts') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyHosts
END
GO

CREATE procedure [dbo].[GetLoyaltyHosts]
 @name varchar(max) as
 begin
  select IPAddress, Port
   from LoyaltyHosts H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyIINs') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyIINs
END
GO

CREATE procedure [dbo].[GetLoyaltyIINs]
 @name varchar(max) as
 begin
  select Low, High
   from LoyaltyIINs I join LoyaltyReference R
    on I.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyMappings') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyMappings
END
GO

CREATE procedure [dbo].[GetLoyaltyMappings]
 @name varchar(max) as
 begin
  select ProductCode, LoyaltyCode
   from LoyaltyMappings M join LoyaltyReference R
    on M.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyHostnames') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyHostnames
END
GO

CREATE procedure [dbo].[GetLoyaltyHostnames]
 @name varchar(max) as
 begin
  select Hostname
   from LoyaltyHostnames H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end
GO

PRINT N'HOPT-1491 - OPT Service - Web UI - Unable to update Loyalty settings if loyalty is not set to present...End';
PRINT N'';

GO
