using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Forecourt.Service.OPTServiceClasses;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Web.Http;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers.Interfaces;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;
using foundationSCR = Htec.Foundation.Models.StatusCodeResult;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class PumpController : LinkedLoggableApiController<IWeb>
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private IWeb _web => Loggable;

        // TODO: Until we have the time to change all non HOPT-1964 routes
        private readonly IHtecLogger _logger;

        private readonly IMessageBroker _messageBroker;
        private readonly IPumpWorker _pumpWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IPumpCollection _allPumps;
        private readonly IFromOptWorker _fromOptWorker;

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        /// <param name="messageBroker">IMessageBroker instance</param>
        /// <param name="pumpWorker">IPumpWorker instance</param>
        /// <param name="hydraDb">IHydraDb instance</param>
        /// <param name="allPumps">IPumpCollection instance</param>
        /// <param name="fromOptWorker">IFromOptWorker instance</param>
        public PumpController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger, IMessageBroker messageBroker, IPumpWorker pumpWorker,
            IHydraDb hydraDb, IPumpCollection allPumps, IFromOptWorker fromOptWorker) : base(web as Loggable)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _messageBroker = messageBroker ?? throw new ArgumentNullException(nameof(messageBroker));
            _pumpWorker = pumpWorker ?? throw new ArgumentNullException(nameof(pumpWorker));
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            _allPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));
            _fromOptWorker = fromOptWorker ?? throw new ArgumentNullException(nameof(fromOptWorker));
        }

        #endregion Constructor

        #region Get Data

        /// <summary>
        /// Get pump(s)
        /// This method can be used to retrieve only one pump data (pumpNumber > 0) or all pumps data (pumpNumber zero or not supplied)
        /// </summary>
        /// <param name="pumpNumber">Optional. If supplied only data for one pump is returned</param>
        /// <param name="reference">The logging reference</param>
        /// <remarks>TBC xxx</remarks>
        /// <response code="400">Invalid parameters</response>
        /// <response code="500">Route exceptioned</response>
        /// <response code="200">Pump information located, or default pump used</response>
        /// <response code="404">Pump not found for given Number</response>
        /// <returns>A collection with the pump(s) details</returns>
        [HttpGet]
        [ResponseType(typeof(IEnumerable<PumpDetails>))]
        public async Task<IHttpActionResult> GetPumpInfo(int pumpNumber = 0, string reference = null)
        {
            var results = Loggable.GetPumps(pumpNumber, reference);

            return GetActionResult(results);
        }

        [HttpGet]
        [ResponseType(typeof(bool))]
        [Obsolete("Use PumpControllerEnables")]
        public IHttpActionResult SiteControllerEnabled()
        {
            return IsPumpControllerEnabled();
        }

        [HttpGet, ResponseType(typeof(bool))]
        public IHttpActionResult IsPumpControllerEnabled()
        {
            var results = _web.IsPumpControllerEnabled();

            return Ok(results);
        }

        #endregion Get Data

        #region Update Data

        /// <summary>
        /// Map a pump to a TID.
        /// </summary>
        /// <param name="details">Includes number of pump and TID to be mapped, or "No TID" to unmap.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage MapToTid(PumpDetails details)
        {
            string tid = details.Tid?.Trim();
            byte pump = details.Number;
            if (string.IsNullOrWhiteSpace(tid) || tid.Equals("No TID"))
            {
                _web.SendChatMessage(WebName, $"Map to No TID, Pump {pump}");
                string result = _web.MapToTid(pump, null);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else if (tid.All(char.IsDigit))
            {
                _web.SendChatMessage(WebName, $"Map to TID, Pump {pump}, TID {tid}");
                string result = _web.MapToTid(pump, tid);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in TID");
            }
        }

        /// <summary>
        /// Map a pump to an OPT.
        /// </summary>
        /// <param name="details">Includes number of pump and serial number of OPT to be mapped, or "No OPT" to unmap.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage MapToOpt(PumpDetails details)
        {
            var opt = details.OptStringId?.Trim() ?? string.Empty;
            var pump = details.Number;
            var result = Result.Failure("Invalid Characters in OPT");

            if (string.IsNullOrWhiteSpace(opt) || opt.Equals("No OPT"))
            {
                _web.SendChatMessage(WebName, $"Map to No OPT, Pump {pump}");
                result = _web.MapToOpt(pump, null);
            }
            else if (opt.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName, $"Map to OPT, Pump {pump}, OPT {opt}");
                result = _web.MapToOpt(pump, opt);
            }

            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result.IsSuccess ? null : result.Error);
        }

        /// <summary>
        /// Set the default mode for a pump.
        /// Mode should be "Kiosk Only", "Mixed" or "Outside Only".
        /// </summary>
        /// <param name="details">Includes number of pump and default mode to be set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetDefaultMode(PumpDetails details)
        {
            var pump = details.Number;
            var kioskOnly = details.DefaultKioskOnly;
            var outsideOnly = details.DefaultOutsideOnly;
            string modeString;
            if (kioskOnly)
            {
                modeString = "Kiosk Only";
            }
            else if (outsideOnly)
            {
                modeString = "Outside Only";
            }
            else
            {
                modeString = "Mixed";
            }

            _web.SendChatMessage(WebName, $"Set Default Mode, Pump {pump}, Mode {modeString}");
            var result = _web.SetDefaultMode(pump, kioskOnly, outsideOnly);
            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result.IsSuccess ? null : result.Error);
        }

        /// <summary>
        /// Set a pump to be closed.
        /// </summary>
        /// <param name="details">Includes number of pump and closed flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ClosePump(PumpDetails details)
        {
            byte pump = details.Number;
            bool closed = details.Closed;
            _web.SendChatMessage(WebName, $"{(closed ? "Close" : "Open")} Pump {pump}");
            string result = _web.ClosePump(pump, closed);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set a pump to be force closed.
        /// </summary>
        /// <param name="details">Includes number of pump.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ForceClosePump(PumpDetails details)
        {
            byte pump = details.Number;
            _web.SendChatMessage(WebName, $"Force Close Pump {pump}");
            string result = _web.ForceClosePump(pump);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set a pump to be forced to outside only mode.
        /// </summary>
        /// <param name="details">Includes number of pump.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ForcePumpOutside(PumpDetails details)
        {
            byte pump = details.Number;
            _web.SendChatMessage(WebName, $"Force Pump {pump} Outside");
            string result = _web.ForcePumpOutside(pump);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Max Fill Override For Fuel Cards.
        /// </summary>
        /// <param name="details">Includes number of pump and flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetMaxFillOverrideForFuelCards(PumpDetails details)
        {
            byte pump = details.Number;
            bool flag = details.MaxFillOverrideForFuelCards;
            _web.SendChatMessage(WebName, $"{(flag ? "Set" : "Clear")} Max Fill Override For Fuel Cards for pump {pump}");
            string result = _web.SetMaxFillOverrideForFuelCards(pump, flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Max Fill Override For Payment Cards.
        /// </summary>
        /// <param name="details">Includes number of pump and flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetMaxFillOverrideForPaymentCards(PumpDetails details)
        {
            byte pump = details.Number;
            bool flag = details.MaxFillOverrideForPaymentCards;
            _web.SendChatMessage(WebName, $"{(flag ? "Set" : "Clear")} Max Fill Override For Payment Cards for pump {pump}");
            string result = _web.SetMaxFillOverrideForPaymentCards(pump, flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set price for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and price, in pence per litre, to set .</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("SetPrice in PumpController is deprecated, please use FuelPriceController instead.")]
        public HttpResponseMessage SetPrice(GradePrices details)
        {
            return new FuelPriceController(_web, _logger).SetPrice(details);
        }

        /// <summary>
        /// Set name for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and name to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("SetGradeName in PumpController is deprecated, please use FuelPriceController instead.")]
        public HttpResponseMessage SetGradeName(GradePrices details)
        {
            return new FuelPriceController(_web, _logger).SetGradeName(details);
        }

        /// <summary>
        /// Set VAT Rate for a grade of fuel.
        /// </summary>
        /// <param name="details">Includes number of grade and VAT Rate to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("SetGradeVatRate in PumpController is deprecated, please use FuelPriceController instead.")]
        public HttpResponseMessage SetGradeVatRate(GradePrices details)
        {
            return new FuelPriceController(_web, _logger).SetGradeVatRate(details);
        }

        #endregion


        #region Developer actions

        /// <summary>
        /// Sends an OnPumpStateChange message to the Service, simulating a PumpController event
        /// </summary>
        /// <returns>HttpStatusCode</returns>
        [HttpPost]
        public async Task<IHttpActionResult> OnPumpStateChangeAsync(OnPumpStateChange request, string reference = null)
        {
            return await DoPumpAction(request.Pump, (p, msg) =>
            {
                var grade = _pumpWorker.GradePrices[request.Pump].FirstOrDefault(x => x.Id == request.Grade);
                if (grade == null)
                {
                    return foundationSCR.Specific(HttpStatusCode.BadRequest, new Exception($"Invalid Grade: {grade}"));
                    //return BadRequest("Invalid Grade");
                }

                var hose = new Hose(request.Hose, 0, request.Grade, grade.Price);

                var pumpData = new PumpData
                {
                    Number = request.Pump,
                    RegistrationData = new VehicleRegistrationData(),
                    Dispenser = new Dispenser
                    {
                        HoseInfo = new Dictionary<byte, Hose>
                        {
                            [hose.Number] = hose
                        },
                        CurrentInfo = new HoseTotals
                        {
                            Hose = hose,
                            Number = request.Grade,
                            Price = hose.Grade.Price,
                            Amount = request.Amount * 10,
                            Volume = request.Volume
                        },
                        IsOptAvailable = request.IsOptAvailable,
                        IsOptInControl = request.IsOptInControl,
                        Pos = request.PosClaim,
                        Number = request.Pump,
                        PumpInfo = new PumpInfo(),
                        State = request.State,
                        CommErr = request.IsError ? Htec.Hydra.Core.Pump.Common.CommunicationState.Bad : Htec.Hydra.Core.Pump.Common.CommunicationState.Ok,
                        Transaction1 = new Transaction
                        {
                            Number = 1,
                            Hose = hose,
                            Amount = request.Amount  * 10,
                            Volume = request.Volume,
                            Price = hose.Grade.Price,
                            TimeStamp = request.TimeStamp,
                            IsPaid = request.IsPaid,
                            SequencNumber = request.TransSeqNum
                        }
                    }
                };

                var pumpStateChange = (PumpStateChange)pumpData;
                pumpStateChange.IsFromTimerEvent = request.IsTimer;
                pumpStateChange.IsPendingTransactionOnReboot = request.IsPendingTxnOnReboot;
                pumpStateChange.PreviousState = request.PreviousState;
                pumpStateChange.TransactionPaymentUsage = request.TransactionPaymentType;

                _messageBroker.OnPumpState(pumpStateChange, reference.ToMessageTracking());
                return foundationSCR.Success;
            }, reference).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the current Delivered details
        /// </summary>
        /// <returns>HttpStatusCode</returns>
        [HttpGet]
        public async Task<IHttpActionResult> GetDeliveredInfoAsync(byte pump, string reference = null)
        {
            return await DoPumpAction<PumpDelivered>(pump, (p, msg) =>
            {
                var result = _hydraDb.GetDelivered(pump, reference.ToMessageTracking());
                return !result.IsSuccess ?
                    Htec.Foundation.Models.StatusCodeResult<PumpDelivered>.Specific(HttpStatusCode.BadRequest, null, new Exception(result.Error)) :
                    Htec.Foundation.Models.StatusCodeResult<PumpDelivered>.Specific(HttpStatusCode.OK, result.Value);
            }, reference).ConfigureAwait(false);
        }

        /// <summary>
        /// Sets the current Delivered status
        /// </summary>
        /// <returns>HttpStatusCode</returns>
        [HttpPost]
        public async Task<IHttpActionResult> SetDeliveredInfoAsync(SetPumpDeliveredInfo request, string reference = null)
        {
            return await DoPumpAction(request.Pump, (p, msg) =>
            {
                var result = _hydraDb.SetDelivered(reference.ToMessageTracking(),
                request.Pump, request.Grade, request.Volume, request.Amount, request.Name, request.Price,
                request.NetAmount, request.VatAmount, request.VatRate, request.TransSeqNum, request.Hose);

                if (!result.IsSuccess)
                {
                    return foundationSCR.Specific(HttpStatusCode.BadRequest, new Exception(result.Error));
                    //BadRequest(result.Error);
                }

                if (request.HasOptPayment)
                {
                    _hydraDb.SetOptPayment(request.Pump, reference.ToMessageTracking());
                }
                else
                {
                    _hydraDb.ClearOptPayment(request.Pump, reference.ToMessageTracking());
                }

                return foundationSCR.Success;
                //return Ok(result.Value);
            }, reference).ConfigureAwait(false);
        }

        /// <summary>
        /// Clears the current Delivered status
        /// </summary>
        /// <returns>HttpStatusCode</returns>
        [HttpPost]
        public async Task<IHttpActionResult> ClearDeliveredInfoAsync(byte pump, string reference = null)
        {
            return await DoPumpAction(pump, (p, msg) =>
            {
                var result = _hydraDb.ClearDelivered(pump, reference.ToMessageTracking());
                if (!result.IsSuccess)
                {
                    return foundationSCR.Specific(HttpStatusCode.BadRequest, new Exception(result.Error));
                    //return Result.Failure(result.Error);
                }

                _hydraDb.ClearOptPayment(pump, reference.ToMessageTracking());
                return foundationSCR.Success;
                //return Ok(result.Value);
            }, reference).ConfigureAwait(false);
        }

        /// <summary>
        /// Calls IPump.ResetPump()
        /// </summary>
        /// <returns>HttpStatusCode</returns>
        [HttpPost]
        public async Task<IHttpActionResult> ResetPumpAsync(byte pump, string reference = null)
        {
            return await DoPumpAction(pump, (p, msg) =>
            {
                var prevState = p.PumpState;
                var result = p.ResetPump();
                if (result)
                {
                    _messageBroker?.StatusResponse(p.Number, msg);
                    _fromOptWorker.CheckPumpState(p, prevState);
                }
                return foundationSCR.Success;
            }, reference).ConfigureAwait(false);
        }

        private async Task<IHttpActionResult> DoPumpAction(byte pump, Func<IPump, IMessageTracking, foundationSCR> action, string reference = null)
        {
            var message = reference.ToMessageTracking();
            return await DoHttpActionAsync(async () =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Success(foundationSCR.Specific(HttpStatusCode.BadRequest, new Exception($"Invalid Pump: {pump}")));
                }

                var result = action.Invoke(thePump, message);
                return Result.Success(result);
            }, null, message.FullId).ConfigureAwait(false);
        }

        private async Task<IHttpActionResult> DoPumpAction<T>(byte pump, Func<IPump, IMessageTracking, Htec.Foundation.Models.StatusCodeResult<T>> action, string reference = null)
        {
            var message = reference.ToMessageTracking();
            return await DoHttpActionAsync<T>(async () =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Htec.Foundation.Models.StatusCodeResult<T>.Specific(HttpStatusCode.BadRequest, new Exception($"Invalid Pump: {pump}"));
                }

                var result = action.Invoke(thePump, message);
                return result;
            }, null, message.FullId).ConfigureAwait(false);
        }

        #endregion
    }
}