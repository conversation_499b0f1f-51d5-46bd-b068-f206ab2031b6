using System;
using System.Collections.Generic;
using FluentAssertions;
using OPT.Common.MediaChannelClasses;
using Xunit;

namespace OPT.Common.Tests
{
    public class MediaChannelMessageTests
    {
        private const byte TagByte = 0xFF;
        private const byte FileNameByte = (byte) MediaChannelMessageComponentType.FileName;
        private const byte FileContentByte = (byte) MediaChannelMessageComponentType.FileContent;
        private const byte OptIdByte = (byte) MediaChannelMessageComponentType.OptId;
        private const byte GetFileByte = (byte) MediaChannelMessageType.GetFile;
        private const byte GetFileResponseByte = (byte) MediaChannelMessageType.GetFileResponse;
        private const byte FileDownloadedByte = (byte) MediaChannelMessageType.FileDownloaded;
        private const byte LogFileByte = (byte) MediaChannelMessageType.LogFile;

        [Fact]
        public void test_empty_bytes()
        {
            // Arrange
            byte[] bytes = { };

            // Act
            IList<MediaChannelMessageComponent> components = MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            components.Should().NotBeNull();
            components.Should().BeEmpty();
        }

        [Fact]
        public void test_short_bytes()
        {
            // Arrange
            byte[] bytes = {TagByte, FileNameByte, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Too short");
        }

        [Fact]
        public void test_bad_tag_byte()
        {
            // Arrange
            byte[] bytes = {TagByte - 1, FileNameByte, 0, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Bad tag");
        }

        [Fact]
        public void test_bad_type_byte()
        {
            // Arrange
            byte[] bytes = {TagByte, FileNameByte - 1, 0, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Bad component type");
        }

        [Fact]
        public void test_empty_file_name()
        {
            // Arrange
            byte[] bytes = {TagByte, FileNameByte, 0, 0, 0, 0};

            // Act
            IList<MediaChannelMessageComponent> components = MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            components.Should().NotBeNull();
            components.Should().HaveCount(1);
            components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            components[0].Length.Should().Be(0);
            components[0].Bytes.Should().BeEmpty();
        }

        [Fact]
        public void test_file_name()
        {
            // Arrange
            byte[] bytes = {TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b'};

            // Act
            IList<MediaChannelMessageComponent> components = MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            components.Should().NotBeNull();
            components.Should().HaveCount(1);
            components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            components[0].Length.Should().Be(2);
            components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
        }

        [Fact]
        public void test_file_name_and_file_content()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, FileContentByte, 3, 0, 0, 0, 1, 2, 3
            };

            // Act
            IList<MediaChannelMessageComponent> components = MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            components.Should().NotBeNull();
            components.Should().HaveCount(2);
            components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            components[0].Length.Should().Be(2);
            components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            components[1].Type.Should().Be(MediaChannelMessageComponentType.FileContent);
            components[1].Length.Should().Be(3);
            components[1].Bytes.Should().BeEquivalentTo((byte) 1, (byte) 2, (byte) 3);
        }

        [Fact]
        public void test_file_name_file_content_and_opt_id()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, FileContentByte, 3, 0, 0, 0, 1, 2, 3, TagByte,
                OptIdByte, 4, 0, 0, 0, (byte) '1', (byte) '2', (byte) '3', (byte) '4'
            };

            // Act
            IList<MediaChannelMessageComponent> components = MediaChannelMessageComponent.ExtractComponents(bytes);

            // Assert
            components.Should().NotBeNull();
            components.Should().HaveCount(3);
            components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            components[0].Length.Should().Be(2);
            components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            components[1].Type.Should().Be(MediaChannelMessageComponentType.FileContent);
            components[1].Length.Should().Be(3);
            components[1].Bytes.Should().BeEquivalentTo((byte) 1, (byte) 2, (byte) 3);
            components[2].Type.Should().Be(MediaChannelMessageComponentType.OptId);
            components[2].Length.Should().Be(4);
            components[2].Bytes.Should().BeEquivalentTo((byte) '1', (byte) '2', (byte) '3', (byte) '4');
        }

        [Fact]
        public void test_file_name_get_bytes()
        {
            // Arrange
            byte[] bytes = {TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b'};
            MediaChannelMessageComponent component =
                new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileName, new byte[] {(byte) 'a', (byte) 'b'});

            // Act
            byte[] result = component.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_file_content_get_bytes()
        {
            // Arrange
            byte[] bytes = {TagByte, FileContentByte, 3, 0, 0, 0, 1, 2, 3};
            MediaChannelMessageComponent component =
                new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileContent, new byte[] {1, 2, 3});

            // Act
            byte[] result = component.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_opt_id_get_bytes()
        {
            // Arrange
            byte[] bytes = {TagByte, OptIdByte, 4, 0, 0, 0, (byte) '1', (byte) '2', (byte) '3', (byte) '4'};
            MediaChannelMessageComponent component = new MediaChannelMessageComponent(MediaChannelMessageComponentType.OptId,
                new byte[] {(byte) '1', (byte) '2', (byte) '3', (byte) '4'});

            // Act
            byte[] result = component.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_message_empty_bytes()
        {
            // Arrange
            byte[] bytes = { };

            // Act
            IList<MediaChannelMessage> messages = MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            messages.Should().NotBeNull();
            messages.Should().BeEmpty();
        }

        [Fact]
        public void test_message_short_bytes()
        {
            // Arrange
            byte[] bytes = {TagByte, GetFileByte, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Too short");
        }

        [Fact]
        public void test_message_bad_tag_byte()
        {
            // Arrange
            byte[] bytes = {TagByte - 1, GetFileByte, 0, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Bad tag");
        }

        [Fact]
        public void test_message_bad_type_byte()
        {
            // Arrange
            byte[] bytes = {TagByte, GetFileByte - 1, 0, 0, 0, 0};

            // Act
            Action act = () => MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Bad message type");
        }

        [Fact]
        public void test_message_get_file_and_bad_file_name()
        {
            // Arrange
            byte[] bytes = {TagByte, GetFileByte, 8, 0, 0, 0, TagByte - 1, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b'};

            // Act
            Action act = () => MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            act.Should().Throw<ArgumentException>().WithMessage("Bad tag");
        }

        [Fact]
        public void test_message_empty_get_file()
        {
            // Arrange
            byte[] bytes = {TagByte, GetFileByte, 0, 0, 0, 0};

            // Act
            IList<MediaChannelMessage> messages = MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            messages.Should().NotBeNull();
            messages.Should().HaveCount(1);
            messages[0].Type.Should().Be(MediaChannelMessageType.GetFile);
            messages[0].Length.Should().Be(0);
            messages[0].Components.Should().BeEmpty();
        }

        [Fact]
        public void test_message_get_file_and_file_name()
        {
            // Arrange
            byte[] bytes = {TagByte, GetFileByte, 8, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b'};

            // Act
            IList<MediaChannelMessage> messages = MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            messages.Should().NotBeNull();
            messages.Should().HaveCount(1);
            messages[0].Type.Should().Be(MediaChannelMessageType.GetFile);
            messages[0].Length.Should().Be(8);
            messages[0].Components.Should().HaveCount(1);
            messages[0].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[0].Components[0].Length.Should().Be(2);
            messages[0].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
        }

        [Fact]
        public void test_message_get_file_file_name_get_file_response_file_name_and_file_content()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, GetFileByte, 8, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, GetFileResponseByte,
                17, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, FileContentByte, 3, 0, 0, 0, 1, 2, 3
            };

            // Act
            IList<MediaChannelMessage> messages = MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            messages.Should().NotBeNull();
            messages.Should().HaveCount(2);
            messages[0].Type.Should().Be(MediaChannelMessageType.GetFile);
            messages[0].Length.Should().Be(8);
            messages[0].Components.Should().HaveCount(1);
            messages[0].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[0].Components[0].Length.Should().Be(2);
            messages[0].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            messages[1].Type.Should().Be(MediaChannelMessageType.GetFileResponse);
            messages[1].Length.Should().Be(17);
            messages[1].Components.Should().HaveCount(2);
            messages[1].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[1].Components[0].Length.Should().Be(2);
            messages[1].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            messages[1].Components[1].Type.Should().Be(MediaChannelMessageComponentType.FileContent);
            messages[1].Components[1].Length.Should().Be(3);
            messages[1].Components[1].Bytes.Should().BeEquivalentTo((byte) 1, (byte) 2, (byte) 3);
        }

        [Fact]
        public void test_message_get_file_file_name_get_file_response_file_name_file_content_file_downloaded_file_name_and_opt_id()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, GetFileByte, 8, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, GetFileResponseByte,
                17, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, FileContentByte, 3, 0, 0, 0, 1, 2, 3,
                TagByte, FileDownloadedByte, 18, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, OptIdByte, 4,
                0, 0, 0, (byte) '1', (byte) '2', (byte) '3', (byte) '4'
            };

            // Act
            IList<MediaChannelMessage> messages = MediaChannelMessage.ExtractMessages(bytes);

            // Assert
            messages.Should().NotBeNull();
            messages.Should().HaveCount(3);
            messages[0].Type.Should().Be(MediaChannelMessageType.GetFile);
            messages[0].Length.Should().Be(8);
            messages[0].Components.Should().HaveCount(1);
            messages[0].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[0].Components[0].Length.Should().Be(2);
            messages[0].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            messages[1].Type.Should().Be(MediaChannelMessageType.GetFileResponse);
            messages[1].Length.Should().Be(17);
            messages[1].Components.Should().HaveCount(2);
            messages[1].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[1].Components[0].Length.Should().Be(2);
            messages[1].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            messages[1].Components[1].Type.Should().Be(MediaChannelMessageComponentType.FileContent);
            messages[1].Components[1].Length.Should().Be(3);
            messages[1].Components[1].Bytes.Should().BeEquivalentTo((byte) 1, (byte) 2, (byte) 3);
            messages[2].Type.Should().Be(MediaChannelMessageType.FileDownloaded);
            messages[2].Length.Should().Be(18);
            messages[2].Components.Should().HaveCount(2);
            messages[2].Components[0].Type.Should().Be(MediaChannelMessageComponentType.FileName);
            messages[2].Components[0].Length.Should().Be(2);
            messages[2].Components[0].Bytes.Should().BeEquivalentTo((byte) 'a', (byte) 'b');
            messages[2].Components[1].Type.Should().Be(MediaChannelMessageComponentType.OptId);
            messages[2].Components[1].Length.Should().Be(4);
            messages[2].Components[1].Bytes.Should().BeEquivalentTo((byte) '1', (byte) '2', (byte) '3', (byte) '4');
        }

        [Fact]
        public void test_message_get_file_get_bytes()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, GetFileByte, 8, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b'
            };
            MediaChannelMessage message = new MediaChannelMessage(MediaChannelMessageType.GetFile, new List<MediaChannelMessageComponent>
            {
                new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileName, new byte[] {(byte) 'a', (byte) 'b'})
            });

            // Act
            byte[] result = message.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_message_get_file_response_get_bytes()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, GetFileResponseByte, 17, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte,
                FileContentByte, 3, 0, 0, 0, 1, 2, 3
            };
            MediaChannelMessage message = new MediaChannelMessage(MediaChannelMessageType.GetFileResponse,
                new List<MediaChannelMessageComponent>
                {
                    new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileName, new byte[] {(byte) 'a', (byte) 'b'}),
                    new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileContent, new byte[] {1, 2, 3})
                });

            // Act
            byte[] result = message.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_message_file_downloaded_get_bytes()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, FileDownloadedByte, 18, 0, 0, 0, TagByte, FileNameByte, 2, 0, 0, 0, (byte) 'a', (byte) 'b', TagByte, OptIdByte, 4,
                0, 0, 0, (byte) '1', (byte) '2', (byte) '3', (byte) '4'
            };
            MediaChannelMessage message = new MediaChannelMessage(MediaChannelMessageType.FileDownloaded,
                new List<MediaChannelMessageComponent>
                {
                    new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileName, new byte[] {(byte) 'a', (byte) 'b'}),
                    new MediaChannelMessageComponent(MediaChannelMessageComponentType.OptId,
                        new byte[] {(byte) '1', (byte) '2', (byte) '3', (byte) '4'})
                });

            // Act
            byte[] result = message.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }

        [Fact]
        public void test_message_log_file_get_bytes()
        {
            // Arrange
            byte[] bytes =
            {
                TagByte, LogFileByte, 19, 0, 0, 0, TagByte, OptIdByte, 4, 0, 0, 0, (byte) '1', (byte) '2', (byte) '3', (byte) '4', TagByte,
                FileContentByte, 3, 0, 0, 0, (byte) 'a', (byte) 'b', (byte) 'c'
            };
            MediaChannelMessage message = new MediaChannelMessage(MediaChannelMessageType.LogFile, new List<MediaChannelMessageComponent>
            {
                new MediaChannelMessageComponent(MediaChannelMessageComponentType.OptId,
                    new byte[] {(byte) '1', (byte) '2', (byte) '3', (byte) '4'}),
                new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileContent,
                    new byte[] {(byte) 'a', (byte) 'b', (byte) 'c'})
            });

            // Act
            byte[] result = message.GetBytes();

            // Assert
            result.Should().BeEquivalentTo(bytes);
        }
    }
}