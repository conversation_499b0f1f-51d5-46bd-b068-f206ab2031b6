using CSharpFunctionalExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.UpdateFileClasses;
using Forecourt.Core.UpdateFileClasses.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.UpdateFileClasses;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Timers;

namespace OPT.Common.Workers
{
    public class UpdateWorker : Connectable, IUpdateWorker
    {
        private const string FilePatternUpd = "*.upd";
        private const string FailedDirectorySuffix = @"\Failed";
        private const string ArchiveDirectorySuffix = @"\Archive";
        public string ReceivedUpdateDirectory { get; private set; } = string.Empty;

        public bool ForwardFuelPriceUpdate { get; private set; }

        private volatile bool _checkForUpdates = false;
        private volatile bool _receivedUpdateDirectoryValid;
        private volatile bool _archiveDirectoryValid;
        private volatile bool _failedDirectoryValid;
        private string _archiveDirectory;
        private string _failedDirectory;

        private IControllerWorker _controllerWorker => GetWorker<IControllerWorker>();
        private IJournalWorker _journalWorker;
        private IHydraTransactionFile _transactionFile;
        private ILocalAccountWorker _localAccountWorker;

        #region Initialisation

        public UpdateWorker(IHydraDb hydraDb, IHtecLogger logger, IJournalWorker journalWorker, IHydraTransactionFile transactionFile, ILocalAccountWorker localAccountWorker,
            IConfigurationManager configurationManager, ITimerFactory timerFactory) : base(hydraDb, logger, configurationManager, nameof(UpdateWorker), timerFactory: timerFactory,
            defaultTimerInterval: "00:01:00")
        {
            _journalWorker = journalWorker ?? throw new ArgumentNullException(nameof(journalWorker));
            _transactionFile = transactionFile ?? throw  new ArgumentNullException(nameof(transactionFile));
            _localAccountWorker = localAccountWorker ?? throw new ArgumentNullException(nameof(localAccountWorker));

            var allFileLocations = HydraDb.GetFileLocations();
            SetDirectories(allFileLocations);
            var siteInfo = HydraDb.GetSiteInfo();
            ForwardFuelPriceUpdate = siteInfo.ForwardFuelPriceUpdate;
        }

        #endregion

        protected override Result DoStart(params object[] startParams)
        {
            DoBackgroundTask(HasSomethingChanged, CreateFileWatcher, methodName: "Execute");

            return base.DoStart(startParams);
        }

        #region Public Actions

        public void SetReceivedUpdateDirectory(string directory)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Setting Received Update Directory {directory}");
                if (string.IsNullOrWhiteSpace(directory))
                {
                    GetLogger().Warn($"Received Update Directory empty");
                    _receivedUpdateDirectoryValid = false;
                    _checkForUpdates = false;
                    return;
                }

                ReceivedUpdateDirectory = directory;

                HydraDb.SetReceivedUpdateDirectory(ReceivedUpdateDirectory);
                _receivedUpdateDirectoryValid = CheckDirectory(ReceivedUpdateDirectory);
                _checkForUpdates = _receivedUpdateDirectoryValid && _archiveDirectoryValid && _failedDirectoryValid;
            }, LoggingReference);
        }

        public void SetForwardFuelPriceUpdate(bool isOn)
        {
            DoAction(() =>
            {
                if (ForwardFuelPriceUpdate != isOn)
                {
                    ForwardFuelPriceUpdate = isOn;

                    HydraDb.SetForwardFuelPriceUpdate(isOn);
                }
            }, LoggingReference);
        }

        #endregion

        #region Checks

        private void CheckForUpdates()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Checking Directory {ReceivedUpdateDirectory}");
            if (!CheckDirectory(ReceivedUpdateDirectory))
            {
                GetLogger().Warn($"Directory {ReceivedUpdateDirectory} does not exist and cannot be created");
                _receivedUpdateDirectoryValid = false;
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            if (!CheckDirectory(_archiveDirectory))
            {
                GetLogger().Warn($"Directory {_archiveDirectory} does not exist and cannot be created");
                _archiveDirectoryValid = false;
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            if (!CheckDirectory(_failedDirectory))
            {
                GetLogger().Warn($"Directory {_failedDirectory} does not exist and cannot be created");
                _failedDirectoryValid = false;
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            ProcessFiles();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void ProcessFiles()
        {
            var logRef = LoggingReference;
            DoAction(() =>
            {
                foreach (string filename in Directory.GetFiles(ReceivedUpdateDirectory, FilePatternUpd))
                {
                    bool success = true;
                    bool fileError = false;
                    IList<IUpdateItem> unknownItems = new List<IUpdateItem>();
                    IList<FuelPriceItem> fuelPriceItems = new List<FuelPriceItem>();
                    IList<LocalAccountAddItem> localAccountAddItems = new List<LocalAccountAddItem>();
                    IList<LocalAccountCustomerItem> localAccountDeleteItems = new List<LocalAccountCustomerItem>();
                    IList<LocalAccountCustomerItem> localAccountStopItems = new List<LocalAccountCustomerItem>();
                    IList<LocalAccountCustomerItem> localAccountGoItems = new List<LocalAccountCustomerItem>();
                    IList<LocalAccountCardItem> localAccountCardItems = new List<LocalAccountCardItem>();
                    IList<LocalAccountPanItem> localAccountHotItems = new List<LocalAccountPanItem>();
                    IList<LocalAccountPanItem> localAccountOkItems = new List<LocalAccountPanItem>();
                    IList<LocalAccountBalanceItem> localAccountBalanceItems = new List<LocalAccountBalanceItem>();
                    IList<LocalAccountBalanceItem> localAccountTopUpItems = new List<LocalAccountBalanceItem>();
                    IList<TransactionFileItem> transactionFileItems = new List<TransactionFileItem>();
                    _controllerWorker?.SendInformation($"Checking Directory {ReceivedUpdateDirectory}, found {filename}");
                    try
                    {
                        using (TextReader reader = new StreamReader(filename))
                        {
                            var csvConfiguration = new CsvConfiguration(CultureInfo.CurrentCulture) { HasHeaderRecord = false, MissingFieldFound = null };
                            using (CsvReader csv = new CsvReader(reader, csvConfiguration))
                            {
                                while (csv.Read())
                                {
                                    if (csv.TryGetField(0, out string recType) && csv.TryGetField(1, out string action))
                                    {
                                        try
                                        {
                                            switch (recType)
                                            {
                                                case "F":
                                                    switch (action)
                                                    {
                                                        case "PRICE":
                                                            fuelPriceItems.Add(csv.GetRecord<FuelPriceItem>());
                                                            break;
                                                        default:
                                                            DoDeferredLogging(LogLevel.Warn, "Unknown.RecType.Action", () => new[] { $"{action}, {recType}-Fuel" });
                                                            unknownItems.Add(csv.GetRecord<UnknownItem>());
                                                            break;
                                                    }

                                                    break;
                                                case "L":
                                                    switch (action)
                                                    {
                                                        case "ADD":
                                                            localAccountAddItems.Add(csv.GetRecord<LocalAccountAddItem>());
                                                            break;
                                                        case "DEL":
                                                            localAccountDeleteItems.Add(csv.GetRecord<LocalAccountCustomerItem>());
                                                            break;
                                                        case "STOP":
                                                            localAccountStopItems.Add(csv.GetRecord<LocalAccountCustomerItem>());
                                                            break;
                                                        case "GO":
                                                            localAccountGoItems.Add(csv.GetRecord<LocalAccountCustomerItem>());
                                                            break;
                                                        case "CARD":
                                                            localAccountCardItems.Add(csv.GetRecord<LocalAccountCardItem>());
                                                            break;
                                                        case "HOT":
                                                            localAccountHotItems.Add(csv.GetRecord<LocalAccountPanItem>());
                                                            break;
                                                        case "OK":
                                                            localAccountOkItems.Add(csv.GetRecord<LocalAccountPanItem>());
                                                            break;
                                                        case "BAL":
                                                            localAccountBalanceItems.Add(csv.GetRecord<LocalAccountBalanceItem>());
                                                            break;
                                                        case "TOP":
                                                            localAccountTopUpItems.Add(csv.GetRecord<LocalAccountBalanceItem>());
                                                            break;
                                                        default:
                                                            DoDeferredLogging(LogLevel.Warn, "Unknown.RecType.Action", () => new[] { $"{action}, {recType}-LocalAccount" });
                                                            unknownItems.Add(csv.GetRecord<UnknownItem>());
                                                            break;
                                                    }

                                                    break;
                                                default:
                                                    DoDeferredLogging(LogLevel.Warn, "Unknown.RecType.Action", () => new[] { $"{action}, {recType}-Update" });
                                                    unknownItems.Add(csv.GetRecord<UnknownItem>());
                                                    break;
                                            }
                                        }
                                        catch (CsvHelperException ex)
                                        {
                                            DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message}, ex);
                                        }
                                    }
                                    else
                                    {
                                        DoDeferredLogging(LogLevel.Warn, "Unknown.RecType", () => new[] { $"{recType}-Update" });
                                        csv.GetRecord<object>();
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Filename: {filename}; Error: {ex.Message}" }, ex);
                        fileError = true;
                        success = false;
                    }

                    transactionFileItems.Add(TransactionFileItem.ConstructUpdateFromOffice(_journalWorker.TillNumber,
                        Path.GetFileName(filename), success, DateTime.Now));

                    if (GetLogger().IsDebugEnabled && !success && !fileError)
                    {
                        using (TextReader reader = new StreamReader(filename))
                        {
                            DoDeferredLogging(LogLevel.Debug, "Filename", () => new[] { $"{filename}; Contentes: {Environment.NewLine}{reader.ReadToEnd()}" });
                        }
                    }

                    fileError = fileError || !success;

                    if (!fileError)
                    {
                        PerformFileActions(unknownItems, fuelPriceItems, localAccountAddItems, localAccountDeleteItems, localAccountStopItems, localAccountGoItems, 
                            localAccountCardItems, localAccountHotItems, localAccountOkItems, localAccountBalanceItems, localAccountTopUpItems, transactionFileItems, logRef);
                    }
                    else
                    {
                        _controllerWorker?.SendInformation($"File {filename} has failed processing.");
                    }

                    MoveProcessedFile(filename, fileError);
                }
            }, logRef);
        }

        private void PerformFileActions(IList<IUpdateItem> unknownItems, IList<FuelPriceItem> fuelPriceItems, IList<LocalAccountAddItem> localAccountAddItems,
            IList<LocalAccountCustomerItem> localAccountDeleteItems, IList<LocalAccountCustomerItem> localAccountStopItems, IList<LocalAccountCustomerItem> localAccountGoItems,
            IList<LocalAccountCardItem> localAccountCardItems, IList<LocalAccountPanItem> localAccountHotItems, IList<LocalAccountPanItem> localAccountOkItems,
            IList<LocalAccountBalanceItem> localAccountBalanceItems, IList<LocalAccountBalanceItem> localAccountTopUpItems, IList<TransactionFileItem> transactionFileItems,
            string logRef)
        {
            DoAction(() =>
            {
                var anyLocalAccountChange = false;
                _transactionFile.WriteTransactionFile(transactionFileItems.ToArray(), DateTime.Now);

                foreach (FuelPriceItem item in fuelPriceItems)
                {
                    _controllerWorker?.SendInformation("Update fuel price item received," + $" grade is {item.Fuel}," +
                                                       $" price is {item.Ppu / 10.0:F1}p / litre");
                }

                foreach (LocalAccountAddItem item in localAccountAddItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Add item received," + $" reference is {item.Reference}," +
                                                       $" name is {item.Name}," + $" active is {item.Active}," +
                                                       $" transaction limit is {item.TransactionLimit}," + $" fuel only is {item.FuelOnly}," +
                                                       $" registration is {item.Registration}," + $" miles is {item.Miles}," +
                                                       $" credit status is {item.CreditStatus}," +
                                                       $" credit status string is {item.CreditStatusString()}" +
                                                       (item.IsTransactionsAllowed() ? ", transactions allowed" : string.Empty) +
                                                       (item.IsFuelOnly() ? ", fuel only" : string.Empty) +
                                                       (item.IsRegistrationEntry() ? ", registration entry" : string.Empty) +
                                                       (item.IsMileageEntry() ? ", mileage entry" : string.Empty) +
                                                       (item.IsPrePayAccount() ? ", pre-pay account" : string.Empty) +
                                                       (item.IsLowCreditWarning() ? ", low credit warning" : string.Empty) +
                                                       (item.IsMaxCreditReached() ? ", max credit reached" : string.Empty));
                    _localAccountWorker.Add(item.Reference, item.Name, item.IsTransactionsAllowed(), item.TransactionLimit, item.IsFuelOnly(),
                        item.IsRegistrationEntry(), item.IsMileageEntry(), item.IsPrePayAccount(), item.IsLowCreditWarning(),
                        item.IsMaxCreditReached(), logRef, false);
                }

                foreach (LocalAccountCustomerItem item in localAccountDeleteItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Delete item received," + $" reference is {item.Reference}");
                    _localAccountWorker.Delete(item.Reference, logRef, false);
                }

                foreach (LocalAccountCustomerItem item in localAccountStopItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Stop item received," + $" reference is {item.Reference}");
                    _localAccountWorker.Stop(item.Reference, logRef, false);
                }

                foreach (LocalAccountCustomerItem item in localAccountGoItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Go item received," + $" reference is {item.Reference}");
                    _localAccountWorker.Go(item.Reference, logRef, false);
                }

                foreach (LocalAccountCardItem item in localAccountCardItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Card item received," + $" PAN is {item.Pan}," +
                                                       $" reference is {item.Reference}," + $" description is {item.Description}," +
                                                       $" disc is {item.Disc}," + $" restrictions 1 is {item.Restrictions1}," +
                                                       $" restrictions 2 is {item.Restrictions2}" + (item.IsNoRestriction()
                                                           ? ", No Restrictions"
                                                           : $"{(item.IsUnleadedAllowed() ? ", Unleaded Allowed" : string.Empty)}" +
                                                             $"{(item.IsDieselAllowed() ? ", Diesel Allowed" : string.Empty)}" +
                                                             $"{(item.IsLpgAllowed() ? ", LPG Allowed" : string.Empty)}" +
                                                             $"{(item.IsLrpAllowed() ? ", LRP Allowed" : string.Empty)}" +
                                                             $"{(item.IsGasOilAllowed() ? ", Gas Oil Allowed" : string.Empty)}" +
                                                             $"{(item.IsAdBlueAllowed() ? ", Ad blue Allowed" : string.Empty)}" +
                                                             $"{(item.IsKeroseneAllowed() ? ", Kerosene Allowed" : string.Empty)}" +
                                                             $"{(item.IsOilAllowed() ? ", Oil Allowed" : string.Empty)}" +
                                                             $"{(item.IsAvgasAllowed() ? ", Avgas Allowed" : string.Empty)}" +
                                                             $"{(item.IsJetAllowed() ? ", Jet Allowed" : string.Empty)}" +
                                                             $"{(item.IsMogasAllowed() ? ", Mogas Allowed" : string.Empty)}" +
                                                             $"{(item.IsValetingAllowed() ? ", Valeting Allowed" : string.Empty)}" +
                                                             $"{(item.IsOtherMotorRelatedGoodsAllowed() ? ", Other motor related goods Allowed" : string.Empty)}" +
                                                             $"{(item.IsShopGoodsAllowed() ? ", Shop goods Allowed" : string.Empty)}"));
                    if (string.IsNullOrEmpty(item.Reference))
                    {
                        _localAccountWorker.DeleteCard(item.Pan, logRef, false);
                    }
                    else if (item.IsNoRestriction())
                    {
                        _localAccountWorker.AddCardWithoutRestrictions(item.Pan, item.Reference, item.Description, item.Disc, logRef, false);
                    }
                    else
                    {
                        _localAccountWorker.AddCardWithRestrictions(item.Pan, item.Reference, item.Description, item.Disc,
                            item.IsUnleadedAllowed(), item.IsDieselAllowed(), item.IsLpgAllowed(), item.IsLrpAllowed(), item.IsGasOilAllowed(),
                            item.IsAdBlueAllowed(), item.IsKeroseneAllowed(), item.IsOilAllowed(), item.IsAvgasAllowed(), item.IsJetAllowed(),
                            item.IsMogasAllowed(), item.IsValetingAllowed(), item.IsOtherMotorRelatedGoodsAllowed(), item.IsShopGoodsAllowed(), logRef, false);
                    }
                }

                foreach (LocalAccountPanItem item in localAccountHotItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Hot item received," + $" PAN is {item.Pan}");
                    _localAccountWorker.HotCard(item.Pan, logRef, false);
                }

                foreach (LocalAccountPanItem item in localAccountOkItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account OK item received," + $" PAN is {item.Pan}");
                    _localAccountWorker.OkCard(item.Pan, logRef, false);
                }

                foreach (LocalAccountBalanceItem item in localAccountBalanceItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Balance item received," + $" reference is {item.Reference}," +
                                                       $" new balance value is £{item.Balance}");
                    _localAccountWorker.Balance(item.Reference, (uint)(item.Balance * 100), logRef, false);
                }

                foreach (LocalAccountBalanceItem item in localAccountTopUpItems)
                {
                    anyLocalAccountChange = true;
                    _controllerWorker?.SendInformation("Local Account Top Up item received," + $" reference is {item.Reference}," +
                                                       $" value to add is £{item.Balance}");
                    _localAccountWorker.TopUp(item.Reference, (uint)(item.Balance * 100), logRef, false);
                }

                foreach (IUpdateItem item in unknownItems)
                {
                    _controllerWorker?.SendInformation("Unknown update item received," + $" record type is {item.RecordType}," +
                                                       $" action is {item.Action}");
                }

                if (ForwardFuelPriceUpdate && fuelPriceItems.Any())
                {
                    if (_controllerWorker?.SetGradePrices(fuelPriceItems).IsSuccess ?? false)
                    {
                        _controllerWorker?.SendInformation("Fuel price sent");
                    }
                }

                if (anyLocalAccountChange)
                {
                    _controllerWorker?.PushChange(EventType.LocalAccountsChanged);
                    _controllerWorker?.NotifyOptConfigChange();
                }

            }, logRef);
        }

        private void MoveProcessedFile(string sourceFilename, bool fileError)
        {
            DoAction(() =>
            {
                var destFolder = fileError ? _failedDirectory : _archiveDirectory;
                var destFilename = Path.Combine(destFolder, Path.GetFileName(sourceFilename));
                try
                {
                    if (!File.Exists(destFilename))
                    {
                        var message = $"Moving update file from \"{sourceFilename}\" to \"{destFilename}\"";
                        GetLogger().Debug(message);
                        _controllerWorker?.SendInformation(message);
                        File.Move(sourceFilename, destFilename);
                    }
                    else
                    {
                        var message = $"Update file \"{destFilename}\" already exist in destination, attempting to rename and move.";
                        GetLogger().Warn(message);
                        _controllerWorker?.SendInformation(message);
                        var renamedDestFilename = Path.GetFileNameWithoutExtension(destFilename) + "_" + DateTime.Now.ToString("yyyyMMddhhmmss") + Path.GetExtension(destFilename);
                        destFilename = Path.Combine(destFolder, renamedDestFilename);

                        message = $"Moving update file from \"{sourceFilename}\" to \"{destFilename}\"";
                        GetLogger().Debug(message);
                        _controllerWorker?.SendInformation(message);
                        File.Move(sourceFilename, destFilename);
                    }
                }
                catch (Exception e)
                {
                    GetLogger().Error($"Error moving update file from \"{sourceFilename}\" to \"{destFilename}\"", e);

                }
            }, LoggingReference);
        }

        private bool CheckDirectory(string directory)
        {
            return DoAction<bool>(() =>
            {
                try
                {
                    Directory.CreateDirectory(directory);
                    return true;
                }
                catch (Exception e)
                {
                    GetLogger().Error($"Error creating directory {directory}", e);
                    return false;
                }
            }, LoggingReference).Value;
        }

        private void FilesChanged(object source, FileSystemEventArgs e)
        {
            DoAction(() =>
            {
                _checkForUpdates = true;
                HasSomethingChanged();
            }, LoggingReference);
        }

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            DoAction(() => { _checkForUpdates = true; }, LoggingReference);
        }

        private void SetDirectories(AllFileLocations allFileLocations)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Setting other directories");
                _archiveDirectory = ControllerWorker.DirectoryString(allFileLocations?.ReceivedUpdateDirectory + ArchiveDirectorySuffix);
                _failedDirectory = ControllerWorker.DirectoryString(allFileLocations?.ReceivedUpdateDirectory + FailedDirectorySuffix);
                _archiveDirectoryValid = CheckDirectory(_archiveDirectory);
                _failedDirectoryValid = CheckDirectory(_failedDirectory);

                SetReceivedUpdateDirectory(ControllerWorker.DirectoryString(allFileLocations?.ReceivedUpdateDirectory));
            }, LoggingReference);
        }

        #endregion

        #region Main Background Loop

        private FileSystemWatcher _watcher;

        private Result CreateFileWatcher()
        {
            _watcher = new FileSystemWatcher();
            _watcher.Changed += FilesChanged;
            _watcher.Created += FilesChanged;
            _watcher.Renamed += FilesChanged;
            _watcher.Deleted += FilesChanged;
            _watcher.Filter = FilePatternUpd;

            return SetWatcherPath();
        }
        private Result SetWatcherPath()
        {
            try
            {
                _watcher.Path = ReceivedUpdateDirectory;
                _watcher.EnableRaisingEvents = true;
            }
            catch (Exception e)
            {
                var msg = $"Error watching directory: {ReceivedUpdateDirectory}";
                GetLogger().Error(HeaderException, () => new[] { msg }, e);
                return Result.Failure($"{msg}; Exception: {e.Message}");
            }

            return Result.Success();
        }

        private Result HasSomethingChanged()
        {
            if (_checkForUpdates)
            {
                _watcher.EnableRaisingEvents = false;
                _checkForUpdates = false;

                CheckForUpdates();

                return SetWatcherPath();
            }

            return Result.Success();
        }

        #endregion

        protected override void DoDisposeDisposing()
        {
            Stop();

            _watcher?.Dispose();
            _watcher = null;

            base.DoDisposeDisposing();
        }

        protected override void DoDisposeUnHookInstances()
        {
            _journalWorker = null;
            _localAccountWorker = null;
            _transactionFile = null;

            base.DoDisposeUnHookInstances();
        }
    }
}
