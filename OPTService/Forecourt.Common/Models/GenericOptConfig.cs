using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.PaymentConfiguration.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Logger.Interfaces;
using OPT.Common.HydraDbClasses;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace OPT.Common.Models
{
    [HasConfiguration()]
    public class GenericOptConfig : Loggable
    {
        /// <summary>
        /// ConfigKey for, the Marina duty split string
        /// </summary>
        public const string ConfigKeyMarinaDutySplitString = ConfigurationConstants.CategoryNameMarina + ConfigurationConstants.CategorySeparator + "DutySplitString";

        /// <summary>
        /// Default value for, the Marina duty split string
        /// </summary>
        public const string DefaultValueMarinaDutySplitString = "60/40";

        private readonly ConfigurableString _dutySplitString;

        /// <summary>
        /// The Marina duty split string
        /// </summary>
        public const string ConfigKeyEnhancedOptLogging = ConfigConstants.ConfigCategoryLogging + EnhancedLoggingKey;
        public const string EnhancedLoggingKey = "EnhancedOptLogging";
        public const bool DefaultValueEnhancedOptLogging = false;

        public const string ConfigKeyPredefinedAmountFuelCard = ConfigConstants.ConfigCategoryOpt + FuelCardEnabledKey;
        public const string FuelCardEnabledKey = "PredefinedAmount:Enabled:FuelCard";
        public const bool DefaultValuePredefinedAmountFuelCard = false;

        public const string ConfigKeyPredefinedAmountPaymentCard = ConfigConstants.ConfigCategoryOpt + PaymentCardEnabledKey;
        public const string PaymentCardEnabledKey = "PredefinedAmount:Enabled:PaymentCard";
        public const bool DefaultValuePredefinedAmountPaymentCard = false;

        public const string ConfigKeyPredefinedAmountLocalAccount = ConfigConstants.ConfigCategoryOpt + LocalAccountEnabledKey;
        public const string LocalAccountEnabledKey = "PredefinedAmount:Enabled:LocalAccount";
        public const bool DefaultValuePredefinedAmountLocalAccount = false;

        /// <summary>
        /// ConfigKey (part) Constant for, when the Opt PCI required restart time is
        /// </summary>
        public const string PciRestartTimeKey = "PCI:RestartTime";
        /// <summary>
        /// ConfigKey for, when the Opt PCI required restart time is
        /// </summary>
        public const string ConfigKeyPciRestartTime = ConfigConstants.ConfigCategoryOpt + PciRestartTimeKey;
        /// <summary>
        /// DefaultValue for, when the Opt PCI required restart time is
        /// </summary>
        public const string DefaultValuePciRestartTime = "03:00:00";

        /// <summary>
        /// ConfigKey (part) Constant for, number of attempts for OPT to GetConfig
        /// </summary>
        public const string GetConfigRetryAttemptsKey = "GetConfig:Retry:Attempts";
        /// <summary>
        /// ConfigKey for, number of attempts for OPT to GetConfig
        /// </summary>
        public const string ConfigKeyGetConfigRetryAttempts = ConfigConstants.ConfigCategoryOpt + GetConfigRetryAttemptsKey;
        /// <summary>
        /// DefaultValue for, number of attempts for OPT to GetConfig
        /// </summary>
        public const int DefaultValueGetConfigRetryAttempts = 3;

        /// <summary>
        /// ConfigKey (part) Constant for, what the minimum authorsied amount allowed is
        /// </summary>
        public const string MinimumAuthorisedAmountKey = "AuthorisedAmount:Minimum";
        /// <summary>
        /// ConfigKey for, when the minimum authorsied amount allowed is
        /// </summary>
        public const string ConfigKeyMinimumAuthorisedAmount = ConfigConstants.ConfigCategoryOpt + MinimumAuthorisedAmountKey;
        /// <summary>
        /// DefaultValue for, when the minimum authorsied amount allowed is
        /// </summary>
        public const int DefaultValueMinimumAuthorisedAmount = 200;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for card removal is
        /// </summary>
        public const string CardRemovalTimeoutKey = "Timeout:Interval:CardRemoval";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for card removal is
        /// </summary>
        public const string ConfigKeyCardRemovalTimeout = ConfigConstants.ConfigCategoryOpt + CardRemovalTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for card removal is
        /// </summary>
        public const int DefaultValueCardRemovalTimeout = 180;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for main thread hearbeat timeout is
        /// </summary>
        public const string MainThreadHeartbeatTimeoutKey = "Timeout:Interval:MainThreadHeartbeat";
        /// <summary>
        /// ConfigKey for, when the timeout for main thread hearbeat timeout is
        /// </summary>
        public const string ConfigKeyMainThreadHeartbeatTimeout = ConfigConstants.ConfigCategoryOpt + MainThreadHeartbeatTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for main thread hearbeat timeout is
        /// </summary>
        public const int DefaultValueMainThreadHeartbeatTimeout = 600;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for ack reponse to signin is
        /// </summary>
        public const string SignInMsgTimeoutKey = "Message:Ack:Timeout:Interval:SignIn";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for ack response to signin is
        /// </summary>
        public const string ConfigKeySignInMsgTimeout = ConfigConstants.ConfigCategoryOpt + SignInMsgTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for ack reponse to signin is
        /// </summary>
        public const int DefaultValueSignInMsgTimeout = 20;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for ack reponse to signin is
        /// </summary>
        public const string ConfigMsgTimeoutKey = "Message:Ack:Timeout:Interval:Config";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for ack response to signin is
        /// </summary>
        public const string ConfigKeyConfigMsgTimeout = ConfigConstants.ConfigCategoryOpt + ConfigMsgTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for ack reponse to signin is
        /// </summary>
        public const int DefaultValueConfigMsgTimeout = 60;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for ack response to payment approved is
        /// </summary>
        public const string PaymentApprovedMsgTimeoutKey = "Message:Ack:Timeout:Interval:PaymentApproved";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for ack response to payment approved is
        /// </summary>
        public const string ConfigKeyPaymentApprovedMsgTimeout = ConfigConstants.ConfigCategoryOpt + PaymentApprovedMsgTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for ack reponse to payment approved is
        /// </summary>
        public const int DefaultValuePaymentApprovedMsgTimeout = 20;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for ack response to payment cleared is
        /// </summary>
        public const string PaymentClearedMsgTimeoutKey = "Message:Ack:Timeout:Interval:PaymentCleared";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for ack response to payment cleared is
        /// </summary>
        public const string ConfigKeyPaymentClearedMsgTimeout = ConfigConstants.ConfigCategoryOpt + PaymentClearedMsgTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for ack reponse to payment cleared is
        /// </summary>
        public const int DefaultValuePaymentClearedMsgTimeout = 20;

        /// <summary>
        /// ConfigKey (part) Constant for, what the timeout for waiting for ack response to general messages is
        /// </summary>
        public const string GeneralMessageAckTimeoutKey = "Message:Ack:Timeout:Interval:GeneralMessage";
        /// <summary>
        /// ConfigKey for, when the timeout for waiting for ack response to general messages is
        /// </summary>
        public const string ConfigKeyGeneralMessageAckTimeout = ConfigConstants.ConfigCategoryOpt + GeneralMessageAckTimeoutKey;
        /// <summary>
        /// DefaultValue for, when the timeout for waiting for ack reponse to general messages is
        /// </summary>
        public const int DefaultValueGeneralMessageAckTimeout = 20;

        public const string MorrisonsName = ConfigConstants.DefaultValueClientName;

        private readonly ConfigurableBool _enhancedLogging;
        private readonly ConfigurableBool _predefinedAmountFuel;
        private readonly ConfigurableBool _predefinedAmountPayment;
        private readonly ConfigurableBool _predefinedAmountLocalAccount;
        private readonly ConfigurableString _pciRestartTimeString;
        private readonly ConfigurableInt _getConfigRetryAttempts;
        private readonly ConfigurableInt _minimumAuthorisedAmount;
        private readonly ConfigurableInt _cardRemovalTimeout;
        private readonly ConfigurableInt _mainThreadHeartbeatTimeout;
        private readonly ConfigurableInt _signInMsgTimeout;
        private readonly ConfigurableInt _configMsgTimeout;
        private readonly ConfigurableInt _paymentApprovedMsgTimeout;
        private readonly ConfigurableInt _paymentClearedMsgTimeout;
        private readonly ConfigurableInt _generalMessageAckTimeout;

        public static string[] NameList { get; } = { MorrisonsName, "TEST" };
        public string ServiceAddress { get; }
        public IList<connGenericEndPoint> ESocketEndPoints { get; }
        public TermProcCategory TermProcCategory { get; }
        public IList<CardAid> CardAids { get; }
        public IList<CardClessAid> CardClessAids { get; }
        public IList<CardClessDrl> CardClessDrls { get; }
        public IList<CardCapk> CardCapks { get; }
        public IList<FuelCard> FuelCards { get; }
        public IDictionary<string, GenericLoyalty> LoyaltyList { get; } = new ConcurrentDictionary<string, GenericLoyalty>();
        public IList<Wash> Washes { get; }
        public IList<TariffMapping> TariffMappings { get; }
        public IList<TermId> Tids { get; }
        public virtual IList<int> PredefinedAmounts { get; }
        public int ReceiptLayoutMode { get; }
        public IList<DiscountCard> DiscountCards { get; }
        public ContactlessConfiguration ContactlessConfiguration { get; set; }
        public string DutySplit => _dutySplitString.GetValue();
        public bool IgnoreMessageId { get; set; }
        public int OptPaymentTimeout { get; }
        public int PodPaymentTimeout { get; }
        public int MixedPaymentTimeout { get; }
        public int KioskTimeout { get; }
        public int NozzleDownTimeout { get; }
        public int SecAuthTimeout { get; }
        public string TermCapabilities { get; }
        public string TermAddCapabilities { get; }
        public bool EnhancedLogging => _enhancedLogging.GetValue();

        public virtual bool PredefinedAmountFuel => _predefinedAmountFuel.GetValue();
        public virtual bool PredefinedAmountPayment => _predefinedAmountPayment.GetValue();
        public virtual bool PredefinedAmountLocalAccount => _predefinedAmountLocalAccount.GetValue();
        public virtual string PciRestartTime => _pciRestartTimeString.GetValue();
        public virtual int MinimumAuthorisedAmount => _minimumAuthorisedAmount.GetValue();

        public virtual int GetConfigRetryAttempts => _getConfigRetryAttempts.GetValue();

        public virtual int CardRemovalTimeout => _cardRemovalTimeout.GetValue();
        public virtual int MainThreadHeartbeatTimeout => _mainThreadHeartbeatTimeout.GetValue();
        public virtual int SignInMsgTimeout => _signInMsgTimeout.GetValue();
        public virtual int ConfigMsgTimeout => _configMsgTimeout.GetValue();
        public virtual int PaymentApprovedMsgTimeout => _paymentApprovedMsgTimeout.GetValue();
        public virtual int PaymentClearedMsgTimeout => _paymentClearedMsgTimeout.GetValue();
        public virtual int generalMessageAckTimeout => _generalMessageAckTimeout.GetValue()
            ;


        public string[] DefaultReceiptHeaderLines { get; set; }
        public string[] DefaultReceiptFooterLines { get; set; }

        #region Contactless Configurables

        private const string ConfigCategoryContactless = ConfigurationConstants.CategoryNameContactless + ConfigurationConstants.CategorySeparator;

        /// <summary>
        /// Config key for, if Contactless is enabled
        /// </summary>
        public const string ConfigKeyIsContactlessEnabled = ConfigCategoryContactless + "ENABLED";

        /// <summary>
        /// Default vaue for, if Contactless is enabled
        /// </summary>
        public const bool DefaultValueIsContactlessEnabled = false;

        /// <summary>
        /// Configurable value for, if Contactless is enabled
        /// </summary>
        protected ConfigurableBool ConfigValueIsContactlessEnabled { get; set; }

        /// <summary>
        /// Is Contactless enabled
        /// </summary>
        public bool IsContactlessEnabled => ConfigValueIsContactlessEnabled.GetValue();

        /// <summary>
        /// Config key for, Contactless Card PreAuth
        /// </summary>
        public const string ConfigKeyContactlessCardPreAuth = ConfigCategoryContactless + "CARDPREAUTH";

        /// <summary>
        /// Default vaue for, Contactless Card PreAuth
        /// </summary>
        public const uint DefaultValueContactlessCardPreAuth = 0;

        /// <summary>
        /// Configurable value for, Contactless Card PreAuth
        /// </summary>
        protected ConfigurableUInt ConfigValueContactlessCardPreAuth { get; set; }

        /// <summary>
        /// Contactless Card PreAuth value
        /// </summary>
        public uint ContactlessCardPreAuth => ConfigValueContactlessCardPreAuth.GetValue();

        /// <summary>
        /// Config key for, Contactless Device PreAuth
        /// </summary>
        public const string ConfigKeyContactlessDevicePreAuth = ConfigCategoryContactless + "DEVICEPREAUTH";

        /// <summary>
        /// Default vaue for, Contactless Device PreAuth
        /// </summary>
        public const uint DefaultValueContactlessDevicePreAuth = 0;

        /// <summary>
        /// Configurable value for, Contactless Device PreAuth
        /// </summary>
        protected ConfigurableUInt ConfigValueContactlessDevicePreAuth { get; set; }

        /// <summary>
        /// Contactless Device PreAuth value
        /// </summary>
        public uint ContactlessDevicePreAuth => ConfigValueContactlessDevicePreAuth.GetValue();

        /// <summary>
        /// Config key for, Contactless Single Button
        /// </summary>
        public const string ConfigKeyContactlessSingleButton = ConfigCategoryContactless + "SingleButton";

        /// <summary>
        /// Default vaue for, Contactless Single Button
        /// </summary>
        public const bool DefaultValueContactlessSingleButton = false;

        /// <summary>
        /// Configurable value for, Contactless Single Button
        /// </summary>
        protected ConfigurableBool ConfigValueContactlessSingleButton { get; set; }

        /// <summary>
        /// Contactless Single Button
        /// </summary>
        public bool ContactlessSingleButton => ConfigValueContactlessSingleButton.GetValue();

        /// <summary>
        /// Config key for, Contactless TTQ
        /// </summary>
        public const string ConfigKeyContactlessTtq = ConfigCategoryContactless + "TTQ";

        /// <summary>
        /// Default vaue for, Contactless TTQ
        /// </summary>
        public readonly string DefaultValueContactlessTtq = string.Empty;

        /// <summary>
        /// Configurable value for, Contactless TTQ
        /// </summary>
        protected ConfigurableString ConfigValueContactlessTtq { get; set; }

        /// <summary>
        /// Contactless TTQ
        /// </summary>
        public string ContactlessTtq => ConfigValueContactlessTtq.GetValue();

        #endregion

        public GenericOptConfig(IPaymentConfigIntegrator paymentConfig, IHydraDb hydraDb, string hydraId, IHtecLogManager logger, IConfigurationManager configurationManager)
            : base(logger, "OPTWorker", configurationManager)
        {
            _enhancedLogging = new ConfigurableBool(this, ConfigKeyEnhancedOptLogging, DefaultValueEnhancedOptLogging);
            _dutySplitString = new ConfigurableString(this, ConfigKeyMarinaDutySplitString, DefaultValueMarinaDutySplitString);

            ConfigValueIsContactlessEnabled = new ConfigurableBool(this, ConfigKeyIsContactlessEnabled, DefaultValueIsContactlessEnabled);
            ConfigValueContactlessCardPreAuth = new ConfigurableUInt(this, ConfigKeyContactlessCardPreAuth, DefaultValueContactlessCardPreAuth);
            ConfigValueContactlessDevicePreAuth = new ConfigurableUInt(this, ConfigKeyContactlessDevicePreAuth, DefaultValueContactlessDevicePreAuth);
            ConfigValueContactlessTtq = new ConfigurableString(this, ConfigKeyContactlessTtq, DefaultValueContactlessTtq);
            ConfigValueContactlessSingleButton = new ConfigurableBool(this, ConfigKeyContactlessSingleButton, DefaultValueContactlessSingleButton);

            ServiceAddress = hydraDb.FetchEndPoints(hydraId).HeartbeatEndPoint.Address.ToString();
            ESocketEndPoints = paymentConfig.EndPoints.Select(x => new connGenericEndPoint(x.Item1, x.Item2)).ToList();

            TermProcCategory = paymentConfig.TermProcCategory;
            paymentConfig.ReadContactlessProperties();
            CardClessAids = paymentConfig.Cards;
            CardClessDrls = paymentConfig.Drls;
            CardAids = new List<CardAid>(paymentConfig.CardAids);
            CardCapks = new List<CardCapk>(paymentConfig.CardCapks);
            FuelCards = new List<FuelCard>(paymentConfig.FuelCards);
            Tids = new List<TermId>(paymentConfig.AllPumpTids);
            TermCapabilities = paymentConfig.TermCapabilities;
            TermAddCapabilities = paymentConfig.TermAddCapabilities;

            foreach (var name in NameList)
            {
                LoyaltyList[name] = hydraDb.FetchGenericLoyalty(name);
            }

            Washes = hydraDb.FetchWashes().Where(x => float.TryParse(x.Price, out _) && float.TryParse(x.VatRate, out _)).ToList();
            TariffMappings = hydraDb.FetchTariffMappings();
            PredefinedAmounts = hydraDb.FetchPredefinedAmounts();
            DiscountCards = hydraDb.FetchDiscountCards();

            var siteInfo = hydraDb.AdvancedConfig;
            ReceiptLayoutMode = siteInfo.ReceiptLayoutMode;

            ContactlessConfiguration = ExtractContactlessConfiguration(this, siteInfo);
 
            OptPaymentTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Opt);
            PodPaymentTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Pod);
            MixedPaymentTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Mixed);
            KioskTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Kiosk);
            NozzleDownTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.NozzleDown);
            SecAuthTimeout = hydraDb.FetchPaymentTimeout(PaymentTimeoutType.SecAuth);

            var optMode = hydraDb.FetchOptMode(OptCollection.GlobalOptId);
            DefaultReceiptHeaderLines = optMode.ReceiptHeader?.Split('\n');
            DefaultReceiptFooterLines = optMode.ReceiptFooter?.Split('\n');
            _predefinedAmountFuel = new ConfigurableBool(this, ConfigKeyPredefinedAmountFuelCard, DefaultValuePredefinedAmountFuelCard);
            _predefinedAmountPayment = new ConfigurableBool(this, ConfigKeyPredefinedAmountPaymentCard, DefaultValuePredefinedAmountPaymentCard);
            _predefinedAmountLocalAccount = new ConfigurableBool(this, ConfigKeyPredefinedAmountLocalAccount, DefaultValuePredefinedAmountLocalAccount);
            _pciRestartTimeString = new ConfigurableString(this, ConfigKeyPciRestartTime, DefaultValuePciRestartTime);
            _getConfigRetryAttempts = new ConfigurableInt(this, ConfigKeyGetConfigRetryAttempts, DefaultValueGetConfigRetryAttempts);
            _minimumAuthorisedAmount = new ConfigurableInt(this, ConfigKeyMinimumAuthorisedAmount, DefaultValueMinimumAuthorisedAmount);
            _cardRemovalTimeout = new ConfigurableInt(this, ConfigKeyCardRemovalTimeout, DefaultValueCardRemovalTimeout);
            _mainThreadHeartbeatTimeout = new ConfigurableInt(this, ConfigKeyMainThreadHeartbeatTimeout, DefaultValueMainThreadHeartbeatTimeout);
            _signInMsgTimeout = new ConfigurableInt(this, ConfigKeySignInMsgTimeout, DefaultValueSignInMsgTimeout);
            _configMsgTimeout = new ConfigurableInt(this, ConfigKeyConfigMsgTimeout, DefaultValueConfigMsgTimeout);
            _paymentApprovedMsgTimeout = new ConfigurableInt(this, ConfigKeyPaymentApprovedMsgTimeout, DefaultValuePaymentApprovedMsgTimeout);
            _paymentClearedMsgTimeout = new ConfigurableInt(this, ConfigKeyPaymentClearedMsgTimeout, DefaultValuePaymentClearedMsgTimeout);
            _generalMessageAckTimeout = new ConfigurableInt(this, ConfigKeyGeneralMessageAckTimeout, DefaultValueGeneralMessageAckTimeout);
        }

        private static ContactlessConfiguration ExtractContactlessConfiguration(GenericOptConfig config, AdvancedConfig siteInfo)
        {
            return new ContactlessConfiguration
            {
                // 678899 - When integration type for PaymentConfiguration = "None", HasContactless is always false
                HasContactless = config.IsContactlessEnabled && (siteInfo.PaymentConfigType.ToUpper() != ConfigConstants.NoneUpper),
                ContactlessCardPreAuthLimit = config.ContactlessCardPreAuth,
                ContactlessDevicePreAuthLimit = config.ContactlessDevicePreAuth,
                Ttq = config.ContactlessTtq,
                SingleContactlessButton = config.ContactlessSingleButton,
            };
        }
    }
}
