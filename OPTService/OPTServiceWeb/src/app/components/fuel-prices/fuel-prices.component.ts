import { DecimalPipe } from '@angular/common';
import { Component, NgZone, OnInit } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { RemoveCommaPipe } from 'src/app/core/pipes/removeCommaPipe';
import { FuelPriceService } from 'src/app/services/fuel-price.service';
import { LoadingService } from 'src/app/services/loading.service';
import { SignalRService } from 'src/app/services/signal-r.service';

@Component({
  selector: 'app-fuel-prices',
  templateUrl: './fuel-prices.component.html',
  styleUrls: ['./fuel-prices.component.css']
})
export class FuelPricesComponent implements OnInit {

  showContent: boolean = false;
  fuelPrices: Array<any> = [];
  signalRData: Subscription | undefined;

  /**
   * The FuelPrices component constructor.
   * @param signalRService The signalR service.
   * @param zone The angular zone service used to keep screen updated.
   * @param fuelPriceService The FuelPrice service.
   * @param logger The logger.
   */
  constructor(
    private signalRService: SignalRService,
    private zone: NgZone,
    private fuelPriceService: FuelPriceService,
    private logger: NGXLogger,
    private loadingService: LoadingService,
    private decimalPipe: DecimalPipe,
    private removeCommaPipe: RemoveCommaPipe
  ) {
    this.signalRData = this.signalRService.getFuelPriceSignalRMessage().subscribe(grade => {
      this.zone.run(() => {
        this.refreshData(grade, false);
      });
    });
  }

  /**
   * ngOnInit angular hook.
   */
  ngOnInit(): void {
    setTimeout(()=>{this.refreshData('');});
  }

  /**
   * ngOnDestroy angular hook.
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Refreshes one or all grades information via API call.
   * @param grade The grade to be refreshed. Optional.
   */
  refreshData(grade: string = '', showLoading: boolean = true): void {   
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();  
    }
    
    this.fuelPriceService.getFuelPrices(grade).subscribe(data => {
      if (data) {
        data.forEach(element => {
          element.PriceToSet = this.decimalPipe.transform(element.PriceToSet, '.1-1');
          element.PriceToSet = this.removeCommaPipe.transform(element.PriceToSet);
        });
      }
      if (grade) {
        this.logger.debug(`Refreshing single Fuel Price ${grade}`, data);
        if (data.length === 1) {
          // Assuming there is only 1 element coming back from the API at this point
          this.fuelPrices.forEach(element => {
            if (element.Grade.toString() === grade) {
              this.fuelPrices[this.fuelPrices.indexOf(element)] = data[0];
            }
          });
        } else {
          // In this case, something unexpected has happened. So refreshing all.
          this.logger.debug('Unexpected number of Fuel Prices returned by the API. Refresing all of them', data);
          this.refreshData('', false);
        }
      } else {
        this.logger.debug('Refreshing all Fuel Prices data', data);
        this.fuelPrices = data;
      }
    }, error => {
      this.logger.error('Error getting Fuel Prices: ', error);
      this.loadingService.errorDuringLoading();
    },() => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

}
