using System;

namespace Forecourt.Core.Helpers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of an ICacheHelper
    /// </summary>
    public interface ICacheHelper
    {
        /// <summary>
        /// Gets the item from the cache, optionally running the action if the cache has expired or is not present
        /// </summary>
        /// <typeparam name="T">The type of the cached item</typeparam>
        /// <param name="itemType">Key to the type of cached item</param>
        /// <param name="item">Key to the instance of the cached item</param>
        /// <param name="action">Action to perform if the item needs refreshing</param>
        /// <returns>The cached item instance</returns>
        T GetCachedItem<T>(string itemType, string item, Func<T> action);

        /// <summary>
        /// Force the Expiration of the cached item
        /// </summary>
        /// <param name="itemType">Key to the type of cached item</param>
        /// <param name="item">Key to the instance of the cached item</param>
        void ForceExpirationOnCachedItem(string itemType, string item);

        /// <summary>
        /// Is the current cache entirely valid?
        /// </summary>
        bool IsCacheValid { get; }

        /// <summary>
        /// Is the current cache entirely valid?
        /// </summary>
        /// <param name="itemType">Key to the type of cached item</param>
        /// <param name="item">Key to the instance of the cached item</param>
        bool IsCachedItemValid(string itemType, string item);

        /// <summary>
        /// Force the Expiration of all cached items
        /// </summary>
        void ForceExpirationOnAllItems();

        /// <summary>
        /// Default cache interval, read from configuration
        /// </summary>
        string ConfigValueDefaultCacheInterval { get; }

        /// <summary>
        /// Return the cache item interval, for the given type and item
        /// </summary>
        /// <returns><see cref="TimeSpan"/></returns>
        /// <param name="itemType">Key to the type of cached item</param>
        /// <param name="item">Key to the instance of the cached item</param>
        TimeSpan GetCacheInterval(string itemType, string item);
    }
}
