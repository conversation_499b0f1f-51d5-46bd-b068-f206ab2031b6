using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    [Migration(20030002, "HOPT-1680 - related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class Hopt1680: Migrationable
    {
        public Hopt1680(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetOPTMode", "dbo.SetReceiptHeader", "dbo.SetReceiptFooter" };
        }

        public override void Up()
        {
            var result = BackupDbObjects(nameof(Hopt1680), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        public override void Down()
        {
            RestoreDbObjects(nameof(Hopt1680), DbObjects.Reverse().ToArray());
        }
    }
}
