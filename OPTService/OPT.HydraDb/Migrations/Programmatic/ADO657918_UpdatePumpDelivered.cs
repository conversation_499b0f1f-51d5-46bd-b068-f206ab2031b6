using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(20050023, "ADO#657918 - Add dbo.PumpDelivered.[TransSeqNum] and update all related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO657918_UpdatePumpDelivered : Migrationable
    {
        private const string TableName = "PumpDelivered";
        private const string ColumnName = "TransSeqNum";

        /// <inheritdoc/>
        public ADO657918_UpdatePumpDelivered(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetPumpDelivered", "dbo.SetPumpDelivered" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO657918_UpdatePumpDelivered), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            if (!Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Alter.Table(TableName).AddColumn(ColumnName).AsInt32().Nullable();
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }         
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO657918_UpdatePumpDelivered), DbObjects.Reverse().ToArray());

            if (Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Delete.Column(ColumnName).FromTable(TableName);
            }
        }
    }
}
