using FluentMigrator;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to update SecAuth timer config needed for MADIC/MFG
    /// </summary>
    [Migration(26200006, "ADO#699297 - Update SecAuth timeout"), Tags(Constants.MigrationTypeData)]
    public class ADO699297_SecAuthConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);

            if (client.ToLower() == ClientSettings.ClientNameMADIC)
            {
                // update PaymentTimeoutType.SecAuth
                Execute.Sql($"EXEC dbo.SetPaymentTimeout @mode = 5, @timeout = 300");
            }
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
