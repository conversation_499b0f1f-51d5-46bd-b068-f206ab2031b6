using Forecourt.Core.Opt.Interfaces;
using OPT.Common.Workers.Interfaces;
using System;

namespace Forecourt.Common.Workers.Interfaces
{
    /// <summary>
    /// Any and all defintions of ITelemetryWorker, split down by area
    /// </summary>
    public interface ITelemetryWorker: ICoreTelemetryWorker, 
        Forecourt.Pump.Workers.Interfaces.ITelemetryWorker,
        Forecourt.Pos.Workers.Interfaces.ITelemetryWorker,
        Forecourt.PaymentConfiguration.Workers.Interfaces.ITelemetryWorker,
        Forecourt.SecondaryAuth.Workers.Interfaces.ITelemetryWorker
    {
        /// <summary>
        /// Register message sent to OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been sent to.</param>
        void MessageSentToOpt(IOpt opt);

        /// <summary>
        /// Register message response received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        /// <param name="message">Type of message received.</param>
        /// <param name="loggingReference">The logging reference</param>
        void MessageReceivedFromOpt(IOpt opt, string message, string loggingReference = null);

        /// <summary>
        /// Register config request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void ConfigRequestReceivedFromOpt(IOpt opt);

        /// <summary>
        /// Register whitelist request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void WhitelistRequestReceivedFromOpt(IOpt opt);

        /// <summary>
        /// Register layout request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void LayoutRequestReceivedFromOpt(IOpt opt);

        /// <summary>
        /// Register software request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void SoftwareRequestReceivedFromOpt(IOpt opt);

        /// <summary>
        /// Register asset request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void AssetRequestReceivedFromOpt(IOpt opt);

        /// <summary>
        /// Register sign in request received from OPT.
        /// </summary>
        /// <param name="opt">OPT that message has been received from.</param>
        void SignInReceivedFromOpt(string opt);

        /// <summary>
        /// Register delivered notification sent to OPT.
        /// </summary>
        /// <param name="pump">Pump for which message has been sent.</param>
        /// <param name="loggingReference">Logging reference</param>
        void DeliveredSentToOpt(byte pump, string loggingReference);

        /// <summary>
        /// Register payment cleared received from OPT.
        /// </summary>
        /// <param name="pump">Pump for which message has been received.</param>
        /// <param name="loggingReference">Logging reference</param>
        void PaymentClearedReceivedFromOpt(byte pump, string loggingReference);

        /// <summary>
        /// Register timeout waiting for response from OPT.
        /// </summary>
        /// <param name="opt">OPT that notification had been sent to.</param>
        /// <param name="message">Notification that had been sent.</param>
        /// <param name="loggingReference">Logging reference</param>
        void MessageTimeoutFromOpt(IOpt opt, string message, string loggingReference);

        /// <summary>
        /// Register message sent to Car Wash Link.
        /// </summary>
        void MessageSentToCarWash();

        /// <summary>
        /// Register message received from Car Wash Link.
        /// </summary>
        void MessageReceivedFromCarWash();

        /// <summary>
        /// Register timeout waiting for response from Car Wash Link.
        /// </summary>
        void MessageTimeoutFromCarWash();
      
        /// <summary>
        /// Register staged message started.
        /// </summary>
        /// <param name="guid">Identifier of staged message started, to correlate with completion.</param>
        void StagedMessageStarted(Guid guid);

        /// <summary>
        /// Register staged message finished.
        /// </summary>
        /// <param name="guid">Identifier of staged message completed, to correlate with start.</param>
        /// <param name="filename">Filename of media file.</param>
        /// <param name="length">Length of media file.</param>
        void StagedMessageFinished(Guid guid, string filename, int length);

        void DipsRequested();
        void DipsReceived();
        void DipsTimedOut();
        void MetersRequested();
        void MetersReceived();
        void MetersTimedOut();
    }
}