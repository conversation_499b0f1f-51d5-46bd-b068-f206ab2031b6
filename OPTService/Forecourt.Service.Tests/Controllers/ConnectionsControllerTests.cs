using FluentAssertions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System.Collections.Generic;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class ConnectionsControllerTests
    {
        private readonly IWeb _web;
        private readonly IHtecLogger _logger;
        private readonly ILoggingHelper _loggingHelper;
        private readonly IConfigurationManager _configurationManager;
        
        public ConnectionsControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _web = Substitute.For<IWeb>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
        }

        [Fact]
        public void test_GetConnections_returns_Ok_EmptyData_result()
        {
            // Arrange

            var connectionDetails = new ConnectionDetails();
            _web.ConnectionsDetails().Returns(connectionDetails);

            ConnectionController controller = new ConnectionController(_web, _configurationManager);

            // Act
            var actual = controller.GetConnections();

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<ConnectionDetails>>().Which.Content.Should().BeEquivalentTo(connectionDetails);
        }

        [Fact]
        public void test_GetConnections_returns_Ok_result()
        {
            // Arrange
            var connectionDetails = new ConnectionDetails
            {
                ToOptPort = 1,
                FromOptPort = 2,
                HeartbeatPort = 3,
                HydraPosPort = 4,
                RetalixPosPort = 5,
                ThirdPartyPosPort = 6,
                MediaChannelPort = 7,
                AnprIpAddress = "default",
                AnprPort = 8,
                CarWashIpAddress = "default",
                CarWashPort = 9,
                PumpControllerIpAddress = "default",
                PumpControllerPort = 10,
                TankGaugeIpAddress = "default",
                TankGaugePort = 11,
                HydraMobileIpAddress = "default",
                HydraMobilePort = 12,
                OptConnectedCount = 13,
                HydraPosConnectedCount = 14,
                RetalixPosConnectedCount = 15,
                ThirdPartyPosConnectedCount = 16,
                MediaChannelConnectedCount = 17,
                IsSecAuthConnected = true,
                CarWashConnected = false,
                TankGaugeConnected = true,
                HydraMobileConnected = true,
                PumpControllerConnected = false,
                AutoAuth = true,
                MediaChannel = false,
                UnmannedPseudoPos = true,
                RetalixDefined = false,
                OptIpAddresses = new List<string>(),
                HydraPosIpAddresses = new List<string>(),
                RetalixPosIpAddresses = new List<string>(),
                ThirdPartyPosIpAddresses = new List<string>(),
                MediaChannelIpAddresses = new List<string>(),
                RetalixPosPrimaryIpAddress = "default"
            };
            _web.ConnectionsDetails().Returns(connectionDetails);

            ConnectionController controller = new ConnectionController(_web, _configurationManager);

            // Act
            var actual = controller.GetConnections();

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<ConnectionDetails>>().Which.Content.Should().BeEquivalentTo(connectionDetails);
        }

        [Fact]
        public void test_GetConnections_returns_NotFound_result()
        {
            // Arrange
            ConnectionDetails connectionDetails = null;
            _web.ConnectionsDetails().Returns(connectionDetails);

            var controller = new ConnectionController(_web, _configurationManager);

            // Act
            var actual = controller.GetConnections();

            // Assert
            actual.Should().BeOfType<NotFoundResult>();
        }
    }
}

