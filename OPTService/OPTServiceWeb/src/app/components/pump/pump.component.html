<form [formGroup]="pumpForm">
    <div class="row mb-3">
        <div class="col-sm-12 col-md-2 col-lg-2 col-xl-2">
            <label class="col-form-label" for="pumpTidInput">TID</label>
        </div>        
        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-5">
            <select id="pumpTidInput" class="custom-select" formControlName="Tid">
                <option value="null" enabled>No TID</option>
                <option *ngFor="let tid of tids" [ngValue]="tid" [attr.selected]="tid == pump.Tid ? true : null">{{tid}}</option>
            </select>
        </div>
        <div class="col-sm-12 col-md-3 col-lg-3 col-xl-3">
            <button type="button" name="pumpTidSaveBtn" class="btn btn-primary w-100" (click)="setTid($event)">Map to TID</button>
        </div>
        <div class="col-sm-12 col-md-1 col-lg-1 col-xl-1">
            <span id="pumpTidError" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="mapToTidText"
            *ngIf="mapToTidError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-sm-12 col-md-2 col-lg-2 col-xl-2">
            <label class="col-form-label" for="pumpOptInput">OPT</label>
        </div>
        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-5">
            <select id="pumpOptInput" class="custom-select" formControlName="OptStringId">
                <option value="null" enabled>No OPT</option>
                <option *ngFor="let opt of opts" [ngValue]="opt.StringId" [attr.selected]="opt.StringId === pump.OptStringId ? true : null">{{opt.StringId}}</option>
            </select>
        </div>
        <div class="col-sm-12 col-md-3 col-lg-3 col-xl-3">
            <button type="button" name="pumpOptSaveBtn" class="btn btn-primary w-100" (click)="setOpt($event)">Map to OPT</button>
        </div>
        <div class="col-sm-12 col-md-1 col-lg-1 col-xl-1">
            <span id="pumpOptError" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="mapToOptErrorText"
            *ngIf="mapToOptError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-sm-12 col-md-2 col-lg-2 col-xl-2">
            <label class="col-form-label" for="pumpModeInput">Default day mode</label>
        </div>
        <div class="col-sm-12 col-md-6 col-lg-6 col-xl-5">
            <select id="pumpModeInput" class="custom-select" formControlName="DefDayMode">
                <option *ngFor="let defDayMode of defDayModes" [ngValue]="defDayMode" [attr.selected]="defDayMode === pump.DefDayMode ? true : null">{{defDayMode}}</option>
            </select>
        </div>
        <div class="col-sm-12 col-md-3 col-lg-3 col-xl-3">
            <button type="button" name="pumpModeSaveBtn" class="btn btn-primary w-100" (click)="setDefaultDayMode($event)">Set default mode</button>
        </div>
        <div class="col-sm-12 col-md-1 col-lg-1 col-xl-1">
            <span id="pumpModeError" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="setDefaultDayText"
            *ngIf="setDefaultDayError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </div>

    <div *ngIf="maxFillOverride">
        <app-switch-label [id]="'maxFillOvFuelCard' + pump.Number"
            [labelText]="'Max fill override for fuel cards is ' + (pump.MaxFillOverrideForFuelCards ? '£' + (maxFillOverride / 100.0).toFixed(2) : 'off')"
            controlName="MaxFillOverrideForFuelCards" [errorInAction]="maxFillOvFuelCardError"
            (action)="setMaxFillOverrideForFuelCards()" [formGroup]="pumpForm"></app-switch-label>
        <app-switch-label [id]="'maxFillOvPaymentCard' + pump.Number"
            [labelText]="'Max fill override for payment cards is ' + (pump.MaxFillOverrideForPaymentCards ? '£' + (maxFillOverride / 100.0).toFixed(2) : 'off')"
            controlName="MaxFillOverrideForPaymentCards" [errorInAction]="maxFillOvPaymentCardError"
            (action)="setMaxFillOverrideForPaymentCards()" [formGroup]="pumpForm"></app-switch-label>
    </div>

    <div class="row mb-3">
        <div class="col-12 text-center">
            <button type="button" name="pumpCloseBtn" class="mr-3 btn btn-primary" (click)="changePumpStatus($event)">
                {{pump.Closed ? 'Open' : pump.ClosePending ? 'Cancel close' : 'Close'}}
            </button>
            <button *ngIf="pump.ClosePending && !pump.Closed" name="pumpForceCloseBtn" type="button" class="mr-3 btn btn-primary" (click)="forceClosePump($event)">
                Force Close
            </button>
        </div>
    </div>    

</form>