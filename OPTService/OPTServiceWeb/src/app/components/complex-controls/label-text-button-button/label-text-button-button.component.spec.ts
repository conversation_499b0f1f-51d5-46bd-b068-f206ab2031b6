import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ControlContainer, FormGroup, FormGroupDirective, FormsModule, ReactiveFormsModule } from '@angular/forms';

import { LabelTextButtonButtonComponent } from './label-text-button-button.component';
import { NO_ERRORS_SCHEMA } from "@angular/core";

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('LabelTextButtonButtonComponent', () => {
  let fixture: ComponentFixture<LabelTextButtonButtonComponent>;
  let component: LabelTextButtonButtonComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [LabelTextButtonButtonComponent],
      imports: [FormsModule, ReactiveFormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: ControlContainer, useValue: fgd }
      ]
    });
    fixture = TestBed.createComponent(LabelTextButtonButtonComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(fixture.componentInstance).toBeTruthy();
  });

  describe('fieldFeedbackId', () => {

    it('should set fieldFeedbackId when no ID set', () => {
      expect(component.fieldFeedbackId).toEqual('FieldFeedback');
    });

    it('should set fieldFeedbackId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.fieldFeedbackId).toEqual('inputFieldFeedback');
    });
  });
  
  describe('primaryButtonId', () => {

    it('should set primaryButtonId when no ID set', () => {
      expect(component.primaryButtonId).toEqual('PrimaryBtn');
    });

    it('should set primaryButtonId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.primaryButtonId).toEqual('inputPrimaryBtn');
    });
  });
    
  describe('secondaryButtonId', () => {

    it('should set secondaryButtonId when no ID set', () => {
      expect(component.secondaryButtonId).toEqual('SecondaryBtn');
    });

    it('should set secondaryButtonId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.secondaryButtonId).toEqual('inputSecondaryBtn');
    });
  });
    
  describe('errorId', () => {

    it('should set errorId when no ID set', () => {
      expect(component.errorId).toEqual('Error');
    });

    it('should set errorId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.errorId).toEqual('inputError');
    });
  });

  describe('doPrimaryAction', () => {

    it('should emit action', () => {
      spyOn(component.primaryAction, 'emit');

      component.doPrimaryAction();

      expect(component.primaryAction.emit).toHaveBeenCalled();
    });
  });
  
  describe('doSecondaryAction', () => {

    it('should emit action', () => {
      spyOn(component.secondaryAction, 'emit');

      component.doSecondaryAction();

      expect(component.secondaryAction.emit).toHaveBeenCalled();
    });
  });
});
