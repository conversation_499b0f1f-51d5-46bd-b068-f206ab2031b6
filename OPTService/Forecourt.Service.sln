
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Service", "Forecourt.Service\Forecourt.Service.csproj", "{C1DD5D41-F2A6-4C65-9D02-762471560A85}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Common.Tests", "Forecourt.Common.Tests\Forecourt.Common.Tests.csproj", "{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Common", "Forecourt.Common\Forecourt.Common.csproj", "{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Service.Tests", "Forecourt.Service.Tests\Forecourt.Service.Tests.csproj", "{F51BB6D6-D89E-4535-B022-649E127D05CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.TransactionValidator", "OPT.TransactionValidator\OPT.TransactionValidator.csproj", "{5921AF5E-9E18-401A-B334-536730DFFC49}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{84A03DAA-44E0-48E4-ACC5-CA79110C482F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.TransactionValidator.Tests", "OPT.TransactionValidator.Tests\OPT.TransactionValidator.Tests.csproj", "{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPTServiceWeb", "OPTServiceWeb\OPTServiceWeb.csproj", "{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8D2C88DC-AA8B-4DAF-AB99-002A2E3D57AE}"
	ProjectSection(SolutionItems) = preProject
		HydraOpt.OptService.nuspec = HydraOpt.OptService.nuspec
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Common.Integration.Tests", "Forecourt.Common.Integration.Tests\Forecourt.Common.Integration.Tests.csproj", "{B26D73BE-9E30-4CD8-8667-330321BA462C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.HydraDb", "OPT.HydraDb\OPT.HydraDb.csproj", "{1F959829-00E4-42F3-A477-4F30AB2EDE5E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OPT.HydraDb.Tests", "OPT.HydraDb.Tests\OPT.HydraDb.Tests.csproj", "{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Forecourt Integrators", "Forecourt Integrators", "{657C4431-6F59-4D27-8741-EA9B4DAD214C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core Forecourt", "Core Forecourt", "{64619897-209D-43E7-90E5-FCE18DD93B2A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Pump", "Forecourt.Pump\Forecourt.Pump.csproj", "{C702319E-D7D6-4305-B5CA-BB4D29BED88A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Pos", "Forecourt.Pos\Forecourt.Pos.csproj", "{F8A2CEB7-6392-49A4-90C8-E319417CE679}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.BackOffice", "Forecourt.BackOffice\Forecourt.BackOffice.csproj", "{951247BB-E144-4CD2-A02B-D3160283A897}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.SecondaryAuth", "Forecourt.SecondaryAuth\Forecourt.SecondaryAuth.csproj", "{E832DC85-810F-4C97-B89A-31263DEE0F56}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Core", "Forecourt.Core\Forecourt.Core.csproj", "{B141C9E7-1B95-466C-9842-C9200DFF4B6C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.SecondaryAuth.Tests", "Forecourt.SecondaryAuth.Tests\Forecourt.SecondaryAuth.Tests.csproj", "{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.PaymentConfiguration", "Forecourt.PaymentConfiguration\Forecourt.PaymentConfiguration.csproj", "{34939C70-A7B2-4A05-A41F-EFFF144A67AC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PowerShellUtilities", "PowerShellUtilities", "{4A54AD0E-2465-4C26-9BE6-C05BB3AC4040}"
	ProjectSection(SolutionItems) = preProject
		PowerShell Utilities\OPTServiceLogDataExtractionUtility.ps1 = PowerShell Utilities\OPTServiceLogDataExtractionUtility.ps1
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Forecourt.Pos.Tests", "Forecourt.Pos.Tests\Forecourt.Pos.Tests.csproj", "{71F93634-0592-4CB0-B0F0-82D5842E7067}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|Any CPU.ActiveCfg = Debug|x86
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|Any CPU.Build.0 = Debug|x86
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|x64.ActiveCfg = Debug|x64
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|x64.Build.0 = Debug|x64
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|x86.ActiveCfg = Debug|x86
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Debug|x86.Build.0 = Debug|x86
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|x64.ActiveCfg = Release|x64
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|x64.Build.0 = Release|x64
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|x86.ActiveCfg = Release|x86
		{C1DD5D41-F2A6-4C65-9D02-762471560A85}.Release|x86.Build.0 = Release|x86
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|x64.ActiveCfg = Debug|x64
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|x64.Build.0 = Debug|x64
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Debug|x86.Build.0 = Debug|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|x64.ActiveCfg = Release|x64
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|x64.Build.0 = Release|x64
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|x86.ActiveCfg = Release|Any CPU
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF}.Release|x86.Build.0 = Release|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|x64.ActiveCfg = Debug|x64
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|x64.Build.0 = Debug|x64
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Debug|x86.Build.0 = Debug|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|x64.ActiveCfg = Release|x64
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|x64.Build.0 = Release|x64
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|x86.ActiveCfg = Release|Any CPU
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D}.Release|x86.Build.0 = Release|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|x64.ActiveCfg = Debug|x64
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|x64.Build.0 = Debug|x64
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Debug|x86.Build.0 = Debug|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|x64.ActiveCfg = Release|x64
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|x64.Build.0 = Release|x64
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|x86.ActiveCfg = Release|Any CPU
		{F51BB6D6-D89E-4535-B022-649E127D05CC}.Release|x86.Build.0 = Release|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|x64.ActiveCfg = Debug|x64
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|x64.Build.0 = Debug|x64
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Debug|x86.Build.0 = Debug|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|Any CPU.Build.0 = Release|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|x64.ActiveCfg = Release|x64
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|x64.Build.0 = Release|x64
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|x86.ActiveCfg = Release|Any CPU
		{5921AF5E-9E18-401A-B334-536730DFFC49}.Release|x86.Build.0 = Release|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|x64.ActiveCfg = Debug|x64
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|x64.Build.0 = Debug|x64
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Debug|x86.Build.0 = Debug|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|x64.ActiveCfg = Release|x64
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|x64.Build.0 = Release|x64
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|x86.ActiveCfg = Release|Any CPU
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA}.Release|x86.Build.0 = Release|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|x64.ActiveCfg = Debug|x64
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|x64.Build.0 = Debug|x64
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Debug|x86.Build.0 = Debug|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|x64.ActiveCfg = Release|x64
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|x64.Build.0 = Release|x64
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|x86.ActiveCfg = Release|Any CPU
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8}.Release|x86.Build.0 = Release|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|x64.ActiveCfg = Debug|x64
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|x64.Build.0 = Debug|x64
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Debug|x86.Build.0 = Debug|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|x64.ActiveCfg = Release|x64
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|x64.Build.0 = Release|x64
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|x86.ActiveCfg = Release|Any CPU
		{B26D73BE-9E30-4CD8-8667-330321BA462C}.Release|x86.Build.0 = Release|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|x64.ActiveCfg = Debug|x64
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|x64.Build.0 = Debug|x64
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Debug|x86.Build.0 = Debug|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|x64.ActiveCfg = Release|x64
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|x64.Build.0 = Release|x64
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|x86.ActiveCfg = Release|Any CPU
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E}.Release|x86.Build.0 = Release|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|x64.ActiveCfg = Debug|x64
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|x64.Build.0 = Debug|x64
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Debug|x86.Build.0 = Debug|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|x64.ActiveCfg = Release|x64
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|x64.Build.0 = Release|x64
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|x86.ActiveCfg = Release|Any CPU
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8}.Release|x86.Build.0 = Release|Any CPU
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|Any CPU.ActiveCfg = Debug|x86
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|Any CPU.Build.0 = Debug|x86
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|x64.ActiveCfg = Debug|x64
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|x64.Build.0 = Debug|x64
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|x86.ActiveCfg = Debug|x86
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Debug|x86.Build.0 = Debug|x86
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|Any CPU.Build.0 = Release|Any CPU
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|x64.ActiveCfg = Release|x64
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|x64.Build.0 = Release|x64
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|x86.ActiveCfg = Release|x86
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A}.Release|x86.Build.0 = Release|x86
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|x64.ActiveCfg = Debug|x64
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|x64.Build.0 = Debug|x64
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Debug|x86.Build.0 = Debug|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|x64.ActiveCfg = Release|x64
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|x64.Build.0 = Release|x64
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|x86.ActiveCfg = Release|Any CPU
		{F8A2CEB7-6392-49A4-90C8-E319417CE679}.Release|x86.Build.0 = Release|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|x64.ActiveCfg = Debug|x64
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|x64.Build.0 = Debug|x64
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|x86.ActiveCfg = Debug|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Debug|x86.Build.0 = Debug|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|Any CPU.Build.0 = Release|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|x64.ActiveCfg = Release|x64
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|x64.Build.0 = Release|x64
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|x86.ActiveCfg = Release|Any CPU
		{951247BB-E144-4CD2-A02B-D3160283A897}.Release|x86.Build.0 = Release|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|x64.ActiveCfg = Debug|x64
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|x64.Build.0 = Debug|x64
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Debug|x86.Build.0 = Debug|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|Any CPU.Build.0 = Release|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|x64.ActiveCfg = Release|x64
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|x64.Build.0 = Release|x64
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|x86.ActiveCfg = Release|Any CPU
		{E832DC85-810F-4C97-B89A-31263DEE0F56}.Release|x86.Build.0 = Release|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|x64.ActiveCfg = Debug|x64
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|x64.Build.0 = Debug|x64
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Debug|x86.Build.0 = Debug|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|x64.ActiveCfg = Release|x64
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|x64.Build.0 = Release|x64
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|x86.ActiveCfg = Release|Any CPU
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C}.Release|x86.Build.0 = Release|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|x64.ActiveCfg = Debug|x64
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|x64.Build.0 = Debug|x64
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Debug|x86.Build.0 = Debug|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|x64.ActiveCfg = Release|x64
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|x64.Build.0 = Release|x64
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|x86.ActiveCfg = Release|Any CPU
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2}.Release|x86.Build.0 = Release|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|x64.ActiveCfg = Debug|x64
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|x64.Build.0 = Debug|x64
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Debug|x86.Build.0 = Debug|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|x64.ActiveCfg = Release|x64
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|x64.Build.0 = Release|x64
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|x86.ActiveCfg = Release|Any CPU
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC}.Release|x86.Build.0 = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|x64.ActiveCfg = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|x64.Build.0 = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|x86.ActiveCfg = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Debug|x86.Build.0 = Debug|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|Any CPU.Build.0 = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|x64.ActiveCfg = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|x64.Build.0 = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|x86.ActiveCfg = Release|Any CPU
		{71F93634-0592-4CB0-B0F0-82D5842E7067}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C1DD5D41-F2A6-4C65-9D02-762471560A85} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{EB80C035-BFC0-4BD2-AE18-AF377D45D5CF} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{9144A424-9B6B-43A1-A92E-C1AEA3B6438D} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{F51BB6D6-D89E-4535-B022-649E127D05CC} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{5921AF5E-9E18-401A-B334-536730DFFC49} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{C54030D7-2CD4-4F2D-8E56-C2387635FDEA} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{61AD5685-A5B3-48CD-8F67-F36D7AFBD0C8} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{B26D73BE-9E30-4CD8-8667-330321BA462C} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{1F959829-00E4-42F3-A477-4F30AB2EDE5E} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{1CDE8E13-D06B-4BB8-B1D1-AF87728B8BF8} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{C702319E-D7D6-4305-B5CA-BB4D29BED88A} = {657C4431-6F59-4D27-8741-EA9B4DAD214C}
		{F8A2CEB7-6392-49A4-90C8-E319417CE679} = {657C4431-6F59-4D27-8741-EA9B4DAD214C}
		{951247BB-E144-4CD2-A02B-D3160283A897} = {657C4431-6F59-4D27-8741-EA9B4DAD214C}
		{E832DC85-810F-4C97-B89A-31263DEE0F56} = {657C4431-6F59-4D27-8741-EA9B4DAD214C}
		{B141C9E7-1B95-466C-9842-C9200DFF4B6C} = {64619897-209D-43E7-90E5-FCE18DD93B2A}
		{7D07C1B9-0DBE-4A08-9EDE-6C2D3AD68CC2} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
		{34939C70-A7B2-4A05-A41F-EFFF144A67AC} = {657C4431-6F59-4D27-8741-EA9B4DAD214C}
		{4A54AD0E-2465-4C26-9BE6-C05BB3AC4040} = {8D2C88DC-AA8B-4DAF-AB99-002A2E3D57AE}
		{71F93634-0592-4CB0-B0F0-82D5842E7067} = {84A03DAA-44E0-48E4-ACC5-CA79110C482F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3299AE9D-8789-4778-B060-5DD5368692F2}
	EndGlobalSection
EndGlobal
