using System;
using System.Collections.Generic;
using System.Linq;

namespace OPT.Common.MediaChannelClasses
{
    [Obsolete("Use Htec.Foundation.Connection.Models.MediaMessage versions now!")]
    public class MediaChannelMessage
    {
        public MediaChannelMessageType Type { get; }

        public int Length { get; }

        public int FullLength => Length + MediaChannelMessageComponent.TagAndLength;

        public IList<MediaChannelMessageComponent> Components { get; }

        public string FileName { get; }

        public MediaChannelMessage(MediaChannelMessageType type, IList<MediaChannelMessageComponent> components)
        {
            Type = type;
            Components = components ?? new List<MediaChannelMessageComponent>();
            Length = Components.Sum(x => x.FullLength);
        }

        public MediaChannelMessage(string fileName) : this(MediaChannelMessageType.GetFileResponse,
            new List<MediaChannelMessageComponent>() {new MediaChannelMessageComponent(MediaChannelMessageComponentType.FileContent, null)})
        {
            FileName = fileName;
        }

        public byte[] GetBytes()
        {
            var result = new List<byte> {MediaChannelMessageComponent.TagByte, (byte) Type};

            var bytes = BitConverter.GetBytes(Length);
            result.AddRange(BitConverter.IsLittleEndian ? bytes : bytes.Reverse());

            foreach (var component in Components)
            {
                result.AddRange(component.GetBytes());
            }

            return result.ToArray();
        }

        public static IList<MediaChannelMessage> ExtractMessages(byte[] bytes)
        {
            var messages = new List<MediaChannelMessage>();
            var count = 0;
            while (count < bytes.Length)
            {
                var length = MediaChannelMessageComponent.GetLength(bytes, count);

                var type = (MediaChannelMessageType) bytes[count + 1];
                if (!Enum.IsDefined(typeof(MediaChannelMessageType), type))
                {
                    throw new ArgumentException("Bad message type");
                }

                var components = MediaChannelMessageComponent.ExtractComponents(bytes.Skip(count + MediaChannelMessageComponent.TagAndLength).Take(length).ToArray());
                messages.Add(new MediaChannelMessage(type, components));

                count += length + MediaChannelMessageComponent.TagAndLength;
            }

            return messages;
        }
    }
}