using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class TransactionKey
    {
        [JsonProperty("companyId")] 
        public int CompanyId { get; set; }
        [JsonProperty("storeId")] 
        public int StoreId { get; set; }
        [JsonProperty("workstationId")] 
        public int WorkstationId { get; set; }
        [JsonProperty("businessDay")] 
        public string BusinessDay { get; set; }
        [JsonProperty("transactionSequenceNumber")] 
        public int TransactionSequenceNumber { get; set; }
    }
}
