using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using System;
using System.Runtime.CompilerServices;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <summary>
    /// Abstract/base implementation of the <see cref="ISecAuthIntegratorOut{IMessageTracking}"/>
    /// </summary>
    public abstract class SecAuthOutWorker: SecAuthWorker, ISecAuthIntegratorOut<IMessageTracking>
    {
        /// <summary>
        /// PreAuth marker/header constant
        /// </summary>
        public const string HeaderPreAuth = "PreAuth";

        /// <summary>
        /// PostAuth marker/header constant
        /// </summary>
        public const string HeaderPostAuth = "PostAuth";

        /// <summary>
        /// Failure 
        /// </summary>
        protected const string FailureReasonPrefix = "Invalid request: ";

        /// <inheritdoc/>
        protected SecAuthOutWorker(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker)
            : base(logManager, loggerName, configurationManager, allPumps, telemetryWorker)
        {
        }

        /// <inheritdoc/>
        public virtual Result PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public virtual Result PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public virtual Result RequestTimedOutRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <summary>
        /// Provide a template for processing the SecAuth request, with override action point
        /// </summary>
        /// <param name="request">Request details</param>
        /// <param name="message">Current message tracking information</param>
        /// <param name="action">Action to execute, optional</param>
        /// <param name="startTimer">Start the SecAuth timer</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns>Result</returns>
        protected Result DoProcessRequest(SecAuthRequest request, IMessageTracking message, Func<IPump, Result<string>> action = null, bool startTimer = true, [CallerMemberName]string methodName = null)
        {
            message ??= new MessageTracking();
            var logRef = message.FullId;

            return DoAction(() =>
            {
                if (!AllPumps.TryGetPump(request.Pump, out var pump))
                {
                    return Result.Failure($"{FailureReasonPrefix} Pump");
                }

                var result = action?.Invoke(pump) ?? Result.Failure<string>("No action defined - therefore ignore!");
                if (result.IsSuccess)
                {
                    ControllerWorker?.SendInformation($"SecondaryAuth request ({result.Value}) sent, for Pump {pump.Number}");
                    ControllerWorker?.PushChange(Core.Enums.EventType.PumpChanged, $"{pump.Number}");
                    if (startTimer)
                    {
                        pump.SecAuthRequestSent(logRef);
                    }
                    GetWorker<ITelemetryWorker>()?.MessageSentToSecAuthHost(pump.Number, logRef);
                }

                return result.IsSuccess ? Result.Success() : Result.Failure(result.Error);
            }, message.FullId, methodName: methodName);
        }
    }
}
