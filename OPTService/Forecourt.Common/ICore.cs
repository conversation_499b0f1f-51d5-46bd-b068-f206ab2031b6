using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Net;
using IControllerWorker = OPT.Common.Workers.Interfaces.IControllerWorker;

namespace OPT.Common
{
    public interface ICore : IConnectableWorker
    {
        IControllerWorker ControllerWorker { get; }
        IOptCollection AllOpts { get; }
        IPumpCollection AllPumps { get; }
        IList<TermId> FetchedTids { get; }
        ICollection<GradeName> GradeNames { get; }
        IDictionary<byte, IEnumerable<Grade>> GradePrices { get; }
        IDictionary<byte, float> GradePriceToSet { get; }
        IList<long> ReceiptTrans { get; }

        /// <summary>
        /// Retrieves all categories information
        /// </summary>
        IList<CategoryConfiguration> GetCategoriesConfiguration { get; }

        IList<string> AvailableSoftware { get; }
        IList<string> AvailableSecureAssets { get; }
        IList<string> AvailableCpatAssets { get; }
        ICollection<LocalAccountCustomer> LocalAccountCustomers { get; }

        bool FuellingIndefiniteWait { get; }
        int FuellingWaitMinutes { get; }
        int FuellingBackoffAuth { get; }
        int FuellingBackoffPreAuth { get; }
        int FuellingBackoffStopStart { get; }
        int FuellingBackoffStopOnly { get; }
        DateTime DayEndTime { get; }
        DateTime ShiftEndTime { get; }
        bool AutoAuth { get; }
        bool MediaChannel { get; }
        bool UnmannedPseudoPos { get; }
        bool AsdaDayEndReport { get; }
        PrinterConfig PrinterConfig { get; }
        int ReceiptTimeout { get; }
        int ReceiptMaxCount { get; }
        short TillNumber { get; }
        byte PosClaimNumber { get; }
        short FuelCategory { get; }
        bool ForwardFuelPriceUpdate { get; }
        IList<CardReference> CardReferences { get; }
        AdvancedConfig AdvancedConfig { get; }
        GenericOptConfig GenericOptConfig { get; }
        int FilePruneDays { get; }
        int TransactionPruneDays { get; }
        int ReceiptPruneDays { get; }
        IPAddress RetalixPosPrimaryIpAddress { get; }
        string TransactionFileDirectory { get; }
        string RetalixTransactionFileDirectory { get; }
        string WhitelistDirectory { get; }
        string LayoutDirectory { get; }
        string SoftwareDirectory { get; }
        string MediaDirectory { get; }
        string PlaylistDirectory { get; }
        string OptLogFileDirectory { get; }
        string ContactlessPropertiesFile { get; }
        string ReceivedUpdateDirectory { get; }
        string EsocketConnectionString { get; }
        bool EsocketUseConnectionString { get; }
        string EsocketConfigFile { get; }
        string EsocketKeystoreFile { get; }
        string EsocketDbUrl { get; }
        bool EsocketOverrideProperties { get; }
        bool EsocketOverrideKeystore { get; }
        bool EsocketOverrideUrl { get; }
        bool EsocketOverrideContactless { get; }
        bool EsocketConnectionMade { get; }
        bool IsPrinterBusy { get; }
        IPAddress DomsIpAddress { get; }

        string DomsLoginString { get; }

        bool DomsStateEnabled { get; }
        bool DomsStateConnected { get; }
        IDomsSetup DomsFetchedSetup { get; }
        IDomsSetup DomsPreparedSetup { get; }
        IDomsSetup DomsTcpSetup { get; }

        DomsState DomsState { get; }

        /// <summary>
        /// Fetch all the Payment Timeouts within the system
        /// </summary>
        /// <returns>List of Payment Timeouts</returns>
        IEnumerable<PaymentTimeout> GetPaymentTimeouts();

        /// <summary>
        /// Get a list of transactions for fuel (or car wash) for a given time period.
        /// </summary>
        /// <param name="startTime">Start of time period.</param>
        /// <param name="endTime">End of time period.</param>
        /// <returns>List of transactions.</returns>
        IEnumerable<FuelTransaction> GetFuelTransactions(DateTime startTime, DateTime endTime);

        /// <summary>
        /// Get a list of non-transaction events, such as shift end or day end, for a given time period.
        /// </summary>
        /// <param name="startTime">Start of time period.</param>
        /// <param name="endTime">End of time period.</param>
        /// <returns>List of events.</returns>
        IEnumerable<OtherEvent> GetOtherEvents(DateTime startTime, DateTime endTime);

        /// <summary>
        /// Fetch a receipt for a given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number to search with.</param>
        /// <returns>Receipt or null if card number not found.</returns>
        string FetchReceipt(long transactionNumber);

        /// <summary>
        /// Print a receipt for a given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number to search with.</param>
        /// <returns>Error message if failed.</returns>
        string PrintReceipt(long transactionNumber);

        /// <summary>
        /// Save a receipt for a given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number to search with.</param>
        /// <returns>Error message if failed.</returns>
        string SaveReceipt(long transactionNumber);

        /// <summary>
        /// Determine if the given loyalty is available to be included in OPT config.
        /// </summary>
        /// <param name="name">Name of config.</param>
        /// <returns>True if that loyalty section can be included in OPT config, false otherwise.</returns>
        bool IsLoyaltyAvailable(string name);

        /// <summary>
        /// Determine if the given loyalty is present to be included in OPT config.
        /// </summary>
        /// <param name="name">Name of config.</param>
        /// <returns>True if that loyalty section is present in OPT config, false otherwise.</returns>
        bool IsLoyaltyPresent(string name);

        string GetOptIpAddressString(IOpt opt);
        string SetTillNumber(short number);
        string SetFuelCategory(short category);
        string SetCardReference(string name, int reference);
        string ClearCardReference(string name);
        string SetAcquirerReference(string cardName, string acquirerName);
        string ClearAcquirerReference(string cardName);
        string SetFuelCard(string cardName, bool isFuelCard);
        string SetExternalName(string cardName, string externalCardName);
        string ClearExternalName(string cardName);
        string SetPrinterEnabled(bool isEnabled);
        string SetPrinterPortName(string portName);
        string SetPrinterBaudRate(int baudRate);
        string SetPrinterHandshake(Handshake handshake);
        string SetPrinterStopBits(StopBits stopBits);
        string SetPrinterDataBits(int dataBits);
        string SetFilePruneDays(int days);
        string SetTransactionPruneDays(int days);
        string SetReceiptPruneDays(int days);

        /// <summary>
        /// Sets all categories in the list
        /// </summary>
        /// <param name="categories">List of categories to set</param>
        /// <returns>Result</returns>
        Result SetCategories(IEnumerable<CategoryConfiguration> categories);
        
        /// <summary>
        /// Handle all integration connection types
        /// </summary>
        /// <param name="integrationType">Type of integration</param>
        /// <param name="typeValue">Value of this integration type</param>
        /// <param name="openConnection">Is the connection being opened, or closed</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="doOpen">Instigate the open/start, as well as set-up all workers and links, etc</param>
        /// <returns>Result</returns>
        Result HandleIntegrationConnection(IntegrationType integrationType, string typeValue, bool openConnection, string loggingReference, bool doOpen = true);
       
        bool IsPumpControllerEnabled();
    }
}