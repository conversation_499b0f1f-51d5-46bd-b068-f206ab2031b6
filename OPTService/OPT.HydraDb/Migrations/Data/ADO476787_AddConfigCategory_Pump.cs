using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add PUMP Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20040004, "ADO#476787 - add PUMP Configuraion Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO476787_AddPumpConfigurationCategory : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO476787_upgrade_config_categories_install.sql");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO476787_upgrade_config_categories_rollback.sql");
        }
    }
}
