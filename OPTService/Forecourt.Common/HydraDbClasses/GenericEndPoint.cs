using System;
using System.Net;

namespace OPT.Common.HydraDbClasses
{
    [Obsolete("Use Htec.Foundation.Connections.Models.GenericEndPoint instead")]
    public class GenericEndPoint
    {
        private IPAddress Ip
        {
            get
            {
                if (IPAddress.TryParse(IpAddress, out IPAddress value))
                {
                    return value;
                }
                else
                {
                    return IPAddress.Loopback;
                }
            }
        }

        public string IpAddress { get; }
        public int Port { get; }
        public IPEndPoint EndPoint => ValidPort ? new IPEndPoint(Ip, Port) : new IPEndPoint(Ip, IPEndPoint.MinPort);
        public bool ValidPort => Port >= IPEndPoint.MinPort && Port <= IPEndPoint.MaxPort;
        public bool Active => ValidPort && Port > IPEndPoint.MinPort && !Ip.Equals(IPAddress.Any);

        public GenericEndPoint(string ipAddress, int port)
        {
            IpAddress = ipAddress;
            if (port >= IPEndPoint.MinPort && port <= IPEndPoint.MaxPort)
            {
                Port = port;
            }
            else
            {
                Port = IPEndPoint.MinPort;
            }
        }

        public GenericEndPoint(IPAddress ipAddress, int port)
        {
            IpAddress = ipAddress.ToString();
            if (port >= IPEndPoint.MinPort && port <= IPEndPoint.MaxPort)
            {
                Port = port;
            }
            else
            {
                Port = IPEndPoint.MinPort;
            }
        }
    }
}