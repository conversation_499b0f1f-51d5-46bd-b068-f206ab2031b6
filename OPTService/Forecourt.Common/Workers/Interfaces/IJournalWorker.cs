using CSharpFunctionalExtensions;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using DiscountItem = Htec.Hydra.Core.Bos.Messages.DiscountItem;
using HscMeterReadings = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.MeterReadings;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;

namespace OPT.Common.Workers.Interfaces
{
    public interface IJournalWorker: IConnectable, 
        IJournalWorkerReceipt,
        IPumpIntegratorOutJournal<IMessageTracking>, 
        ITankGaugeIntegratorOutJournal, 
        IBosIntegratorInJournal<IMessageTracking>,
        IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>,
        IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>
    {
        DateTime ShiftEndTime { get; }
        DateTime DayEndTime { get; }

        IList<CardReference> CardReferences { get; }

        short TillNumber { get; }
        short FuelCategory { get; }
        PrinterConfig PrinterConfig { get; }

        /// <summary>Create transaction file and database entry for sales items.</summary>
        /// <param name="total">Total sale item.</param>
        /// <param name="fuelSales">List of fuel sales items.</param>
        /// <param name="carWashSales">List of car wash sales items.</param>
        /// <param name="otherSales">List of other sales items.</param>
        /// <param name="discount">Discount item.</param>
        /// <param name="localAccount">Local Account item.</param>
        /// <param name="txnNumber">OPT Transaction Number.</param>
        /// <param name="transactionNumber">Transaction number created.</param>
        /// <param name="message">Current message.</param>
        void WriteSalesItems
        (JournalTotalSalesItem total, IList<JournalFuelSalesItem> fuelSales, IList<JournalCarWashSalesItem> carWashSales,
            IList<JournalOtherSalesItem> otherSales, JournalDiscountItem discount, JournalLocalAccountItem localAccount, string txnNumber,
            out int transactionNumber,
            IMessageTracking message = null);      
        string SetTillNumber(short number);
        string SetFuelCategory(short category);
        string SetCardReference(string name, int reference);
        string ClearCardReference(string name);
        string SetAcquirerReference(string cardName, string acquirerName);
        string ClearAcquirerReference(string cardName);
        string SetFuelCard(string cardName, bool isFuelCard);
        string SetExternalName(string cardName, string externalCardName);
        string ClearExternalName(string cardName);
        string SetPrinterEnabled(bool isEnabled);
        string SetPrinterPortName(string portName);
        string SetPrinterBaudRate(int baudRate);
        string SetPrinterHandshake(Handshake handshake);
        string SetPrinterStopBits(StopBits stopBits);
        string SetPrinterDataBits(int dataBits);
        [Obsolete("// TODO: Left in as DomsWorker and Unit tests referenced")]
        void DipsReceived(IList<Dip> dips);
        [Obsolete("// TODO: Left in as DomsWorker and Unit tests referenced")]
        void MetersReceived(IList<HscMeterReadings> meters);
        [Obsolete("// TODO: Left in as DomsWorker and Unit tests referenced")]
        void PumpDataReceived(HscPumpData pumpData, string loggingReference = null);
        void SetUnmannedJournalFile(string fileName);
        void SetPrinterBitmapFile(string fileName);
        bool IsPrinterBusy { get; }
        uint ConfigValueMaxRetalixTransactionNumber { get; }

        /// <summary>
        /// Manages the complete process of finalising the transaction, ... 
        /// </summary>
        /// <param name="id">Internal OPT Id</param>
        /// <param name="opt"><see cref="IOptCore"/> instance</param>
        /// <param name="pump"><see cref="IPump"/> instance</param>
        /// <param name="totalSales"></param>
        /// <param name="grade">Grade id</param>
        /// <param name="gradeName">Grade name</param>
        /// <param name="productCode">Product code</param>
        /// <param name="fuelSales">Fuel sales amount</param>
        /// <param name="carWashSales">Carwash sales amount</param>
        /// <param name="otherSales">Other sales amount</param>
        /// <param name="discountItem">Discount details</param>
        /// <param name="discount">Discount details</param>
        /// <param name="localAccountItem">Local account details</param>
        /// <param name="localAccountPayment">Local account payment</param>
        /// <param name="washProgram">Carwash program details</param>
        /// <param name="washPrice">Carwash price</param>
        /// <param name="txnNumber">OPT transaction number</param>
        /// <param name="transactionNumber">Internal transaction number/id</param>
        /// <param name="cardNumber">Card bumber (masked)</param>
        /// <param name="cardProductName">Card type</param>
        /// <param name="amountAuthed">Amount authorised</param>
        /// <param name="transactionVolume">Fuel transaction volume</param>
        /// <param name="message">Current message</param>
        /// <param name="payment">Generice payment details, used for PaymentCancelled and PaymentCleared</param>
        /// <returns><see cref="Result"/> wrapped int (transaction number)</returns>
        Result<int> RecordTransaction(int id, IOptCore opt, IPump pump, IMessageTracking message,
            string txnNumber, out int transactionNumber, string cardNumber, string cardProductName,
            JournalTotalSalesItem totalSales, uint amountAuthed = 0, uint transactionVolume = 0, byte grade = 0, string gradeName = null, string productCode = null,
            IList<JournalFuelSalesItem> fuelSales = null, IList<JournalCarWashSalesItem> carWashSales = null,
            IList<JournalOtherSalesItem> otherSales = null, JournalDiscountItem discountItem = null, DiscountItem discount = null,
            JournalLocalAccountItem localAccountItem = null, LocalAccountPayment localAccountPayment = null, byte washProgram = 0, uint washPrice = 0, Payment payment = null);
    }

    public interface IJournalWorkerReceipt : Forecourt.Pos.Workers.Interfaces.IJournalWorkerReceipt
    {
        /// <summary>
        /// Create journal entry for receipt.
        /// </summary>
        /// <param name="receipt">Receipt to add to journal.</param>
        /// <param name="message">Current message</param>
        void ReceiptJournal(string receipt, IMessageTracking message);

        /// <summary>
        /// Print a receipt
        /// </summary>
        /// <param name="transactionNumber">Transaction Number of the receipt</param>
        /// <returns></returns>
        string PrintReceipt(long transactionNumber);

        /// <summary>
        /// Save receipt (to file)
        /// </summary>
        /// <param name="transactionNumber">Transaction Number of the receipt</param>
        /// <returns></returns>
        string SaveReceipt(long transactionNumber);

        /// <summary>
        /// Format the receipt text
        /// </summary>
        /// <param name="receipt"></param>
        /// <param name="isPrint"></param>
        /// <param name="useControlChars"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        string FormatReceipt(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null);

        /// <summary>
        /// Get Receipts details from previous transactions.
        /// </summary>
        /// <param name="id">OPT Id.</param>
        /// <param name="cardNumber">Card number used to find receipt(s).</param>
        /// <param name="message">Current message</param>
        /// <returns>Result wrapped receipt list</returns>
        Result<IEnumerable<ReceiptData>> GetReceipts(int id, string cardNumber, IMessageTracking message);

        /// <summary>
        /// Get Receipt details from previous transactions.
        /// </summary>
        /// <param name="opt">Internal Opt instance</param>
        /// <param name="cardNumber">Card number used to find receipt(s).</param>
        /// <param name="message">Current message</param>
        /// <returns>Result wrapped receipt list</returns>
        Result<IEnumerable<ReceiptData>> GetReceipts(IOptCore opt, string cardNumber, IMessageTracking message);

        /// <summary>
        /// Respond to receipt being printed, i.e. update printed count
        /// </summary>
        /// <param name="id">OPT Id</param>
        /// <param name="receipt">Receipt details</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result ReceiptPrinted(int id, ReceiptPrintedDetails receipt, IMessageTracking message);

        /// <summary>
        /// Respond to receipt being printed, i.e. update printed count
        /// </summary>
        /// <param name="opt">Internal Opt instance</param>
        /// <param name="receipt">Receipt details</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result ReceiptPrinted(IOptCore opt, ReceiptPrintedDetails receipt, IMessageTracking message);

        /// <summary>
        /// Respond to the payment being authorised
        /// </summary>
        /// <param name="result">The <see cref="PaymentResult"/> instance</param>
        /// <param name="thePump">The <see cref="IPump"/> instance</param>
        /// <param name="opt">The <see cref="IOpt"/> instance</param>
        /// <param name="message">Current message</param>
        /// <param name="payment">The <see cref="Htec.Hydra.Messages.Opt.Models.PaymentApproved"/> instance</param>
        /// <param name="optConfig">The <see cref="GenericOptConfig"/> instance</param>
        /// <param name="autoAuth">Is auto auth in operation</param>
        /// <returns>Result</returns>
        Result PaymentApproved(PaymentResult result, IPump thePump, IOpt opt, IMessageTracking message, PaymentApproved payment, GenericOptConfig optConfig, bool autoAuth);

        /// <summary>
        /// Respond to the payment being cancelled
        /// </summary>
        /// <param name="result">The <see cref="PaymentResult"/> instance</param>
        /// <param name="thePump">The <see cref="IPump"/> instance</param>
        /// <param name="opt">The <see cref="IOpt"/> instance</param>
        /// <param name="message">Current message</param>
        /// <param name="payment">The <see cref="Htec.Hydra.Messages.Opt.Models.PaymentCancelled"/> instance</param>
        /// <returns>Result</returns>
        Result PaymentCancelled(PaymentResult result, IPump thePump, IOpt opt, IMessageTracking message, PaymentCancelled payment);

        /// <summary>
        /// Respond to the payment being cleared
        /// </summary>
        /// <param name="result">The <see cref="PaymentResult"/> instance</param>
        /// <param name="discount">The <see cref="DiscountItem"/> instance for the payment</param>
        /// <param name="thePump">The <see cref="IPump"/> instance</param>
        /// <param name="opt">The <see cref="IOptCore"/> instance</param>
        /// <param name="message">Current message</param>
        /// <param name="payment">The <see cref="Htec.Hydra.Messages.Opt.Models.PaymentCancelled"/> instance</param>
        /// <param name="optConfig">The <see cref="GenericOptConfig"/> instance</param>
        /// <returns>Result</returns>
        Result PaymentCleared(PaymentResult result, DiscountItem discount, IPump thePump, IOptCore opt, IMessageTracking message, PaymentCleared payment, GenericOptConfig optConfig);
    }
}