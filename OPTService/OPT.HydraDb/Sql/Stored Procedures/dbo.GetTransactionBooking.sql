USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetTransactionBooking') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetTransactionBooking
END
GO

CREATE PROCEDURE [dbo].[GetTransactionBooking]
 @transId BIGINT,
 @txnNumber NVARCHAR(20),
 @externalTransId NVARCHAR(30)
AS
BEGIN
	SELECT 
		t.Id, 
		t.TransactionId,
        t.TxnNumber, 
        t.TransactionDate, 
        t.ExternalTransactionId,
        t.ShiftId,
        t.PeriodId,
        t.BusinessDate,
        t.HttpStatusCode,
        t.HttpResponse,
        t.BookedDate,
        t.RetryCount,
        t.NextRetryDate,
        t.CreatedDate,
        t.ModifiedDate,
        t.SendTransactionItem
	FROM TransactionBookingState t
	WHERE 
        (t.TransactionId = @transId) AND
        (ISNULL(@txnNumber, '') = '' OR t.TxnNumber = @txnNumber) AND
        (ISNULL(@externalTransId, '') = '' OR t.ExternalTransactionId = @externalTransId);

END
GO

GRANT EXECUTE ON GetTransactionBooking TO UserRole
GO
