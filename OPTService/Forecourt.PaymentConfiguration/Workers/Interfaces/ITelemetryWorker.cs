using System;

namespace Forecourt.PaymentConfiguration.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the ITelemetryWorker, related to Payment Configuration
    /// </summary>
    public interface ITelemetryWorker: Htec.Foundation.Connections.Workers.Interfaces.ITelemetryWorker
    {
        /// <summary>
        /// Register query passed to eSocket.POS DB.
        /// </summary>
        /// <param name="guid">Identifier of query sent, to correlate with result.</param>
        void QuerySentToEsocketDb(Guid guid);

        /// <summary>
        /// Register query returned from Hydra DB.
        /// </summary>
        /// <param name="query">The name of the query sent.</param>
        /// <param name="guid">Identifier of query returned, to correlate with sending.</param>
        void QueryReturnedFromEsocketDb(string query, Guid guid);

    }
}
