USE [Hydra]
GO

PRINT N'ADO#608163 - add SECONDARYAUTH Configuraion Category (to be used by advanced config keys)..Begin';

IF NOT EXISTS(SELECT 1 FROM dbo.ConfigurationCategory t WHERE t.Category = 'SECONDARYAUTH')
BEGIN
	INSERT INTO dbo.ConfigurationCategory(Category, [Description], IsStandardEditable) VALUES('SECONDARYAUTH', 'All Secondary Authorisation related options', 1);
END

PRINT N'ADO#608163 - add SECONDARYAUTH Configuraion Category (to be used by advanced config keys)..End';
