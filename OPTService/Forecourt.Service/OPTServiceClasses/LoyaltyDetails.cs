using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class LoyaltyDetails
    {
        public string Name { get; set; }
        public bool Present { get; set; }
        public string SiteId { get; set; }
        public string TerminalId { get; set; }
        public string Footer1 { get; set; }
        public string Footer2 { get; set; }
        public int Timeout { get; set; }
        public string ApiKey { get; set; }
        public string HttpHeader { get; set; }
        public List<LoyaltyEndPointDetails> Hosts { get; set; }
        public List<LoyaltyEndPointDetails> Hostnames { get; set; }
        public List<LoyaltyIinDetails> Iins { get; set; }
        public List<LoyaltyMappingDetails> LoyaltyMappings { get; set; }
    }
}