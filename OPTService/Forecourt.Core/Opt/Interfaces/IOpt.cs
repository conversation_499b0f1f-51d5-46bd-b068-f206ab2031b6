using CSharpFunctionalExtensions;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.RequestResponse;
using OPT.Common;
using System;
using System.Collections.Generic;
using optMessages = Htec.Hydra.Messages.Opt;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;

namespace Forecourt.Core.Opt.Interfaces
{
    public interface IOptCore
    {
        string IdString { get; }

        int Id { get; }
        string FullIdentifier(string header = ConfigConstants.NameOptUpper, string separator = "");

        bool SignedIn { get; }
        bool SignInRequired { get; }
        bool Connected { get; }
        bool Offline { get; }

        /// <summary>Set the Sign In Required flag for the OPT.</summary>
        void SetSignInRequired(bool signInRequired);

        /// <summary>Called when OPT is signed in.</summary>
        void SignIn();

        /// <summary>Called when OPT is signed out.</summary>
        void SignOut();
        OptModeType Mode { get; }


        long FetchTxnTransaction(string txnNumber);
        void StoreTxnReceipt(string txnNumber, ReceiptTransaction receipt);
        void StoreTxnTransaction(string txnNumber, long transactionNumber);
        ReceiptTransaction FetchTxnReceipt(string txnNumber);

        string FormatReceiptForLogging(string receipt);

        string FormatReceiptForJournal(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null);

        Result<string> GetRawReceiptText(ReceiptInfo info, bool centreAlign = false);

        /// <summary>Fetch the list of pump numbers for OPT.</summary>
        /// <returns>List of pump numbers.</returns>
        IList<byte> PumpList();
    }

    public interface IOptAllConnected: IOptCore
    {
        int ToOptReconnectionCount { get; set; }
        bool ToOptConnected { get; }
        bool FromOptConnected { get; }
        bool HeartbeatConnected { get; }
        bool AllConnected { get; }
    }

    public interface IOpt: 
        IOptCore, 
        IOptAllConnected
    {
        bool PrinterError { get; }
        bool PaperLow { get; }
        bool SoftwareNotificationPending { get; }
        bool ConfigNotificationPending { get; }
        bool WhitelistNotificationPending { get; }
        bool LayoutNotificationPending { get; }
        bool MediaUpdateNotificationPending { get; }
        bool AssetNotificationPending { get; }

        bool LogFileRequestSent { get; set; }
        bool LogFileRequestPending { get; }
        bool ConfigCheckPending { get; }
        bool WhitelistCheckPending { get; }
        bool LayoutCheckPending { get; }
        bool MediaUpdateCheckPending { get; }
        bool ModeChangePending { get; }
        bool ConfigChangePending { get; }
        string SoftwareToSend { get; }
        bool IsSecureAssetsToSend { get; }
        string AssetToSend { get; }
        optMessages.Xsd.configType SentConfig { get; }
        WhitelistResponse SentWhitelist { get; }
        LayoutResponse SentLayout { get; }
        IList<string> SentMediaFilesList { get; }
        bool InUse { get; }
        bool InKioskUse { get; }
        bool HasContactless { get; }
        uint CashLimit { get; }
        int PaymentTimeoutInSeconds { get; }
        int PayAtKioskPressedTimeoutInSeconds { get; set; }
        OptRequest GetCurrentNotification();
        bool RequestSentToOpt { get; }
        string SoftwareVersion { get; }
        string SecureAssetsVersion { get; }
        string MultimediaAssetsVersion { get; }
        string CpatAssetsVersion { get; }
        string OptFirmwareVersion { get; }
        public string EmvKernelVersion { get; }
        public string PluginType { get; }
        public string PluginVersion { get; }
        string ReceiptHeader { get; }
        string ReceiptFooter { get; }
        string PlaylistFileName { get; }
        string Subnet { get; }
        string Gateway { get; }
        string Dns1 { get; }
        string Dns2 { get; }

        /// <summary>
        /// Adds the notification to the OPT, and if one is already being processed queue it for later processing
        /// </summary>
        /// <param name="notification">The notification</param>
        /// <returns>Should the notification be sent immediately</returns>
        bool AddNotification(OptRequest notification);

        /// <summary>Called when message received from OPT.</summary>
        /// <param name="socket">The socket, to, from or heartbeat, message is received from.</param>
        void Received(Opt.Enums.SocketType socket);

        /// <summary>
        /// Notifies that a response has been received, dequeue any pending notifications and returns the current one
        /// </summary>
        /// <returns>Current notification</returns>
        OptRequest NotificationResponseReceived();

        /// <summary>Called when socket disconnected from OPT.</summary>
        /// <param name="socket">The disconnected socket, to, from or heartbeat.</param>
        void Disconnected(Opt.Enums.SocketType socket);

        /// <summary>Called when a request is sent to the OPT.</summary>
        void SendingRequestToOpt(DateTime? expiresAt = null);

        /// <summary>
        /// Determines if the given pump for the Opt is in NozzleUp (Requested) state
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <returns>bool</returns>
        bool IsNozzleUp(byte pump);

        /// <summary>Check whether a timeout has expired, either heartbeat timeout or response timeout.</summary>
        /// <returns>True if either timeout has expired, false otherwise.</returns>
        bool HasTimeoutExpired();

        /// <summary>Clear list of pumps for OPT.</summary>
        void ClearPumps();

        /// <summary>Remove a pump from the list of pumps for OPT.</summary>
        /// <param name="pump">Pump to remove.</param>
        void RemovePump(IPump pump);

        /// <summary>Add a pump to the list of pumps for OPT.</summary>
        /// <param name="pump">Pump to add.</param>
        void AddPump(IPump pump);

        /// <summary>Set the payment timeout for OPT.</summary>
        /// <param name="timeout">Timeout.</param>
        void SetPaymentTimeoutInSeconds(int timeout);

        /// <summary>
        /// Set the contactless flag for OPT.
        /// </summary>
        /// <param name="value">True if contactless enabled, false otherwise.</param>
        void SetContactless(bool value);

        /// <summary>Set the printer status for OPT.</summary>
        /// <param name="error">True if in error state, false otherwise.</param>
        /// <param name="paperLow">True if in paper low state, false otherwise.</param>
        void SetPrinterStatus(bool error = false, bool paperLow = false);

        /// <summary>Set the device state for OPT.</summary>
        /// <param name="state">DeviceState enum to set.</param>
        void SetDeviceState(DeviceState state);

        /// <summary>Store the software version that has been sent to the OPT.</summary>
        void SoftwareSent();

        /// <summary>Store the asset version that has been sent to the OPT.</summary>
        void AssetSent();

        /// <summary>Store the config that has been sent to the OPT.</summary>
        /// <param name="config">The config that has been sent.</param>>
        void ConfigSent(optMessages.Xsd.configType config);

        /// <summary>Store the whitelist that has been sent to the OPT.</summary>
        /// <param name="whitelist">The whitelist that has been sent.</param>>
        void WhitelistSent(WhitelistResponse whitelist);

        /// <summary>Store the layout that has been sent to the OPT.</summary>
        /// <param name="layout">The layout that has been sent.</param>>
        void LayoutSent(LayoutResponse layout);

        void MediaFilesListSent(IList<string> files);

        /// <summary>Called when a Software Pending Notification has been sent to the OPT.</summary>
        void SoftwarePendingSent();
        /// <summary>Called when an Asset Pending Notification has been sent to the OPT.</summary>
        void AssetPendingSent();

        /// <summary>Called when a Config Pending Notification has been sent to the OPT.</summary>
        void ConfigPendingSent();

        /// <summary>Called when a Whitelist Pending Notification has been sent to the OPT.</summary>
        void WhitelistPendingSent();

        /// <summary>Called when a Layout Pending Notification has been sent to the OPT.</summary>
        void LayoutPendingSent();

        void MediaUpdatePendingSent();

        /// <summary>Called when a Config Check is needed.</summary>
        void ConfigCheckRequired();

        /// <summary>Called when a Whitelist Check is needed.</summary>
        void WhitelistCheckRequired();

        /// <summary>Called when a Layout Check is needed.</summary>
        void LayoutCheckRequired();

        void MediaUpdateCheckRequired();

        /// <summary>Called when a Config Check has been completed.</summary>
        void ConfigCheckCleared();

        /// <summary>Called when a Whitelist Check has been completed.</summary>
        void WhitelistCheckCleared();

        /// <summary>Called when a Layout Check has been completed.</summary>
        void LayoutCheckCleared();

        void MediaUpdateCheckCleared();

        /// <summary>Called when a Config Pending Notification needs to be sent.</summary>
        void ConfigNeeded();

        /// <summary>Called when a Whitelist Pending Notification needs to be sent.</summary>
        void WhitelistNeeded();

        /// <summary>Called when a Layout Pending Notification needs to be sent.</summary>
        void LayoutNeeded();

        void MediaUpdateNeeded();

        /// <summary>Called when a Mode Change Notification has been sent to the OPT.</summary>
        void ModeChangeSent();

        /// <summary>Set the Software Version for the OPT.</summary>
        void SetSoftwareVersion(string softwareVersion);

        /// <summary>Set the Secure Assets Version for the OPT.</summary>
        void SetSecureAssetsVersion(string secureAssestsVersion);

        /// <summary>Set the Multimedia Assets Version for the OPT.</summary>
        void SetMultimediaAssetsVersion(string multimediaAssetsVersion);

        /// <summary>Set the CPAT Assets Version for the OPT.</summary>
        void SetCpatAssetsVersion(string cpatAssetsVersion);

        /// <summary>Set the OPT Firmware Version for the OPT.</summary>
        void SetOptFirmwareVersion(string optFirmwareVersion);

        /// <summary>Set the EMV Kernel Version for the OPT.</summary>
        void SetEmvKernelVersion(string emvKernelVersion);

        /// <summary>Set the PluginType for the OPT.</summary>
        void SetPluginType(string plugInType);

        /// <summary>Set the PluginVersion for the OPT.</summary>
        void SetPluginVersion(string plugInVersion);

        /// <summary>Set IP Subnet for the OPT.</summary>
        void SetSubnet(string subnet);

        /// <summary>Set the IP Gateway for the OPT.</summary>
        void SetGateway(string gateway);

        /// <summary>Set the IP Dns1 for the OPT.</summary>
        void SetDns1(string dns1);

        /// <summary>Set the IP Dns2 for the OPT.</summary>
        void SetDns2(string dns2);

        void SetReceiptHeader(string receiptHeader);
        void SetReceiptFooter(string receiptFooter);
        void SetPlaylistFileName(string playlistFileName);
        void SetLastLogTime(DateTime? logTime);
        void SetLogInterval(int interval);
        optMessages.Messages.MessageBuilder MessageBuilder { get; }

        /// <summary>
        /// Indicates if the OPT is configured for "Pod mode", i.e. has more than 1 Pump associated
        /// </summary>
        bool IsInPodMode { get; }

        /// <summary>
        /// Returns the mode or status description the Opt is currently in
        /// </summary>
        /// <returns></returns>
        string GetModeStatusDescription();

        /// <summary>
        /// Returns the <see cref="DeviceState"/> of the <see cref="IOpt"/>
        /// </summary>
        DeviceState DeviceStatus { get; }
    }
}