/* :host{
  display:flex;
  height:100%;
} */

.header{
  background-color: #fcfcfc;
}

.header-button{
  cursor: pointer;
}

.header-button:hover{
  color:#4ca0fb !important;
}

.header-button i, .header-button-disabled i{
  font-size: 1.5em;
}

.header-button span, .header-button-disabled span{
  font-size: 0.75em;
  text-align: center;
}

.left-panel, .left-panel a {
    background: #353638;
    color: #93cddd;
}

.custom-header {
    border-bottom: 1px solid #353638;
}

.white-background {
    background-color: white;
}

.app-header {
    font-size: 30px !important;
}

.nav-pills .nav-link {
    border-radius: 0%;
    padding-right: 49px;
}

.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    background-color: #29b9cb;
    color: #353638;
}

.nav-content{
    position:relative;
    flex-grow: 1;
    overflow: auto;
}

.content-header{
  position: sticky;
  top:0;
  z-index:100;
}

.alert {
  margin: 1rem 1.5rem;
}

.alert a:hover {
  cursor: pointer;
}

.spinner-wrapper{
    position: absolute;
    width:100%;
    height:100vh;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 99;
}

.spinner {   
    width: 40px;
    height: 40px;
    z-index: 100;
    left: 50%;
    top: 50%;
    margin-left: -20px;
    margin-top: -20px;
    position: absolute;
  }
  
  .cube1, .cube2 {
    background-color: #29b9cb;
    width: 15px;
    height: 15px;
    position: absolute;
    top: 0;
    left: 0;
    
    -webkit-animation: sk-cubemove 1.8s infinite ease-in-out, sk-cubecolor 1.8s infinite ease-in-out;
    animation: sk-cubemove 1.8s infinite ease-in-out, sk-cubecolor 1.8s infinite ease-in-out;
  }
  
  .cube2 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s;
  }
  
  @-webkit-keyframes sk-cubemove {
    25% { -webkit-transform: translateX(42px) rotate(-90deg) scale(0.5) }
    50% { -webkit-transform: translateX(42px) translateY(42px) rotate(-180deg) }
    75% { -webkit-transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5) }
    100% { -webkit-transform: rotate(-360deg) }
  }
  
  @keyframes sk-cubemove {
    25% { 
      transform: translateX(42px) rotate(-90deg) scale(0.5);
      -webkit-transform: translateX(42px) rotate(-90deg) scale(0.5);
    } 50% { 
      transform: translateX(42px) translateY(42px) rotate(-179deg);
      -webkit-transform: translateX(42px) translateY(42px) rotate(-179deg);
    } 50.1% { 
      transform: translateX(42px) translateY(42px) rotate(-180deg);
      -webkit-transform: translateX(42px) translateY(42px) rotate(-180deg);
    } 75% { 
      transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);
      -webkit-transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);
    } 100% { 
      transform: rotate(-360deg);
      -webkit-transform: rotate(-360deg);
    }
  }

  @keyframes sk-cubecolor {
      50%{
          background-color:#333;;
      }
      100%{
        background-color:#29b9cb;
    }
  }