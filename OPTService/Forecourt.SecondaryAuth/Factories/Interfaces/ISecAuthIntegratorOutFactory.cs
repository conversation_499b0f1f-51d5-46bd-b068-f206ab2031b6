using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;

namespace Forecourt.SecondaryAuth.Factories.Interfaces
{
    /// <summary>
    /// Standard IFactory based definition for the Secondary Authorisation Integrator Out
    /// </summary>
    public interface ISecAuthIntegratorOutFactory: IFactory<string, ISecAuthIntegratorOut<IMessageTracking>>
    {
    }
}
