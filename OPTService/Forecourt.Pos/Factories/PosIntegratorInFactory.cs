using CSharpFunctionalExtensions;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Pos.Factories
{
    /// <summary>
    /// POS integrator factory
    /// </summary>
    public class PosIntegratorInFactory : PosIntegratorFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>, IPosIntegratorInFactory
    {

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public PosIntegratorInFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IPosIntegratorInTransient<IMessageTracking, IPump, Result>> resolveTypeInstance) :
            base(logManager, $"{nameof(PosIntegratorInFactory)}", configurationManager, resolveTypeInstance)
        {
            AddItem($"{PosType.None}", ConfigConstants.None, (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.HydraPos}", "Htec HydraPOS", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.Retalix}", "NCR Retalix", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.GenericSignalRApi}", "Generic SignalR and RestAPI", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.MadicApiSignalR}", "MADIC RestAPI and SignalR", (key) => ResolveTypeInstance(key));
        }
    }
}
