using FluentMigrator;
using OPT.HydraDb.Common;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed for HydraMobileWorker embedded timer
    /// </summary>
    [Migration(28100001, "ADO#782405 - Revert further configuration needed for HydraMobile worker"), Tags(Constants.MigrationTypeData)]
    public class ADO782405_BOSDupTransCheckConfigUpdate : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Timers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'RecordTransaction:DuplicateCheck:Interval', @value = '04:00:00', @category = '{cfgConstants.CategoryNameBOS}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
