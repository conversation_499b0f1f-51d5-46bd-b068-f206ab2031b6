import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { WebService } from './web.service';
import { environment } from 'src/environments/environment';
import { VersionInfo } from '../core/models/versionInfo.model';
import { UploadFileDetails } from '../core/models/uploadFileDetails.model';
import { DOCUMENT } from '@angular/common';

describe('WebService', () => {
  const URL = "http://localhost";

  let webService: WebService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        WebService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    webService = TestBed.inject(WebService);
    httpMock = TestBed.get(HttpTestingController);
  });

  it('should be created', () => {
    expect(webService).toBeTruthy();
  });

  it('.getVersionInfo() should handle success response from API', () => {
    //Arrange
    let mockedResult = new VersionInfo();

    //Act
    webService.getVersionInfo().subscribe((result) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(result).toEqual(mockedResult);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.GetVersionInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResult);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getVersionInfo() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    webService.getVersionInfo().subscribe(result => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.GetVersionInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.upgradeService() should handle success response from API', () => {
    //Arrange
    let mockedResult = {};

    //Act
    webService.upgradeService().subscribe((result) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(result).toEqual(mockedResult);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.UpgradeService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResult);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.upgradeService() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    webService.upgradeService().subscribe(result => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.UpgradeService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.rollbackService() should handle success response from API', () => {
    //Arrange
    let mockedResult = {};

    //Act
    webService.rollbackService().subscribe((result) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(result).toEqual(mockedResult);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.RollbackService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResult);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.rollbackService() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    webService.rollbackService().subscribe(result => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.RollbackService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.restartService() should handle success response from API', () => {
    //Arrange
    let mockedResult = {};

    //Act
    webService.restartService().subscribe((result) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(result).toEqual(mockedResult);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.RestartService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResult);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.restartService() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    webService.restartService().subscribe(result => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.RestartService);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.backupDatabase() should handle success response from API', () => {
    //Arrange
    let mockedResult = {};

    //Act
    webService.backupDatabase().subscribe((result) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(result).toEqual(mockedResult);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.BackupDatabase);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResult);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.backupDatabase() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    webService.backupDatabase().subscribe(result => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + environment.webController.BackupDatabase);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.commonFileAction() should handle success response from API', () => {
    //Arrange
    let service = environment.webController.RemoveDatabaseBackupFile;
    let fileData = new UploadFileDetails();
        
    //Act
    webService.commonFileAction(service, fileData).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + service);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.commonFileAction() should handle unsuccess response from API', () => {
    //Arrange
    let service = environment.webController.RemoveDatabaseBackupFile;
    let fileData = new UploadFileDetails();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    webService.commonFileAction(service, fileData).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.webController.base + service);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
});
