import { Byte } from "@angular/compiler/src/util";
import { SecAuthState } from '../enums/secAuthState.enum';

export class Pump{
    Number: Byte = 0;
    Tid: string = '';
    Closed: boolean = false;
    ClosePending: boolean = false;
    OptStringId: string = '';
    InUse: boolean = false;
    NozzleUp: boolean = false;
    HasPayment: boolean = false;
    SecAuthState: SecAuthState = SecAuthState.Idle;
    HasSecAuthRequestTimedOut: boolean = false;
    Delivering: boolean = false;
    Delivered: boolean = false;
    ThirdPartyPending: boolean = false;
    PodMode: boolean = false;
    KioskOnly: boolean = false;
    IsMobile: boolean = false;
    OutsideOnly: boolean = false;
    DefaultKioskOnly: boolean = false;
    DefaultOutsideOnly: boolean = false;
    MaxFillOverrideForFuelCards: boolean = false;
    MaxFillOverrideForPaymentCards: boolean = false;
}
