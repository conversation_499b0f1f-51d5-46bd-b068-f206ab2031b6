using Forecourt.Core.UpdateFileClasses.Interfaces;

namespace OPT.Common.UpdateFileClasses  
{
    public class LocalAccountCardItem : IUpdateItem
    {
        private const byte UnleadedMask = 1;
        private const byte DieselMask = 2;
        private const byte LpgMask = 4;
        private const byte LrpMask = 8;
        private const byte GasOilMask = 16;
        private const byte AdBlueMask = 32;
        private const byte KeroseneMask = 64;
        private const byte OilMask = 1;
        private const byte AvgasMask = 2;
        private const byte JetMask = 4;
        private const byte MogasMask = 8;
        private const byte ValetingMask = 16;
        private const byte OtherMotorRelatedGoodsMask = 32;
        private const byte ShopGoodsMask = 64;
        public string RecordType { get; set; }
        public string Action { get; set; }
        public string Pan { get; set; }
        public string Reference { get; set; }
        public string Description { get; set; }
        public float Disc { get; set; }
        public byte? Restrictions1 { get; set; }
        public byte? Restrictions2 { get; set; }

        public bool IsNoRestriction()
        {
            return (Restrictions1 == null || Restrictions1 == 0) && (Restrictions2 == null || Restrictions2 == 0);
        }

        public bool IsUnleadedAllowed()
        {
            return (Restrictions1 & UnleadedMask) > 0;
        }
        public bool IsDieselAllowed()
        {
            return (Restrictions1 & DieselMask) > 0;
        }
        public bool IsLpgAllowed()
        {
            return (Restrictions1 & LpgMask) > 0;
        }
        public bool IsLrpAllowed()
        {
            return (Restrictions1 & LrpMask) > 0;
        }
        public bool IsGasOilAllowed()
        {
            return (Restrictions1 & GasOilMask) > 0;
        }
        public bool IsAdBlueAllowed()
        {
            return (Restrictions1 & AdBlueMask) > 0;
        }
        public bool IsKeroseneAllowed()
        {
            return (Restrictions1 & KeroseneMask) > 0;
        }
        public bool IsOilAllowed()
        {
            return (Restrictions2 & OilMask) > 0;
        }
        public bool IsAvgasAllowed()
        {
            return (Restrictions2 & AvgasMask) > 0;
        }
        public bool IsJetAllowed()
        {
            return (Restrictions2 & JetMask) > 0;
        }
        public bool IsMogasAllowed()
        {
            return (Restrictions2 & MogasMask) > 0;
        }
        public bool IsValetingAllowed()
        {
            return (Restrictions2 & ValetingMask) > 0;
        }
        public bool IsOtherMotorRelatedGoodsAllowed()
        {
            return (Restrictions2 & OtherMotorRelatedGoodsMask) > 0;
        }
        public bool IsShopGoodsAllowed()
        {
            return (Restrictions2 & ShopGoodsMask) > 0;
        }
    }
}
