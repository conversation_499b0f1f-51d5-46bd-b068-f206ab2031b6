<div [formGroup]="form">
    <ngb-accordion [closeOthers]="true" [activeIds]="activeCardAid" (panelChange)="toggleActiveCardAid($event)" [formArrayName]="formArrayName">
        <ngb-panel *ngFor="let item of cardAids?.controls; let i = index" [formGroupName]="i">
            <ng-template ngbPanelHeader>
                <div class="d-flex align-items-center justify-content-between">
                    <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                        <span>{{item.get('Aid').value}}</span>
                    </button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidAppVerTerm" labelText="App Ver Term" controlName="AppVerTerm">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidTacDefault" labelText="TAC Default" controlName="TacDefault">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidTacDenial" labelText="TAC Denial" controlName="TacDenial">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidTacOnline" labelText="TAC Online" controlName="TacOnline">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidPartialMatch" labelText="Partial Match" controlName="PartialMatch">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidTdol" labelText="TDOL" controlName="Tdol">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidDdol" labelText="DDOL" controlName="Ddol">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidFloorLimit" labelText="Floor Limit" controlName="FloorLimit">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidEmvTarget" labelText="EMV Target" controlName="EmvTarget">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidEmvMaxTarget" labelText="EMV Max Target" controlName="EmvMaxTarget">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="cardAidEmvThreshold" labelText="EMV Threshold" controlName="EmvThreshold">
                </app-label-text>                        
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>