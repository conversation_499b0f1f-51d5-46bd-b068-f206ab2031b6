USE [Hydra]
GO

BEGIN TRY

	DECLARE @configType TABLE 
	(
		[Type] [nvarchar](255) ,
		[Description] [nvarchar](255) 
	);
		
	DECLARE @configHeader TABLE 
	(
		[Type] [nvarchar](255),
		[Description] [nvarchar](255)
	);
	
	DECLARE @configCategory TABLE
	(
	   [Header] NVARCHAR(128), 
	   [Category] NVARCHAR(128), 
	   [CategoryDescription] NVARCHAR(128),
	   [IsStandardEditable] BIT DEFAULT 1
	);
	
    DECLARE @configDetail TABLE
    (
	   [Category] NVARCHAR(128), 
	   [Key] NVARCHAR(128) NULL, 
	   [Value] NVARCHAR(128) NULL
	 );

    -- This is what we want to end up with
	INSERT INTO @configType([Type], Description)
	VALUES
		-- HOPT-1172
		('OPTService', 'The OPT Service'),
		('OPT', 'An OPT'),
		('Pump', 'Pump details')
		;
	
	INSERT INTO @configHeader([Type], Description)
	VALUES
		-- HOPT-1172
		('OPTService', 'Site Controller')
		;
	
	INSERT INTO @configCategory(Header, Category, CategoryDescription, IsStandardEditable)
	VALUES
		-- HOPT-1172
		('Site Controller', 'CONTACTLESS', 'Settings for contactless on OPTs', 0),
		-- HOPT-750/1294
		('Site Controller', 'SITEINFO', 'All site related options', 0);
		
	INSERT INTO @configCategory(Header, Category, CategoryDescription)
	VALUES
	    -- HOPT-1062
		('Site Controller', 'GENERAL', 'General settings'),
		('Site Controller', 'TRANSACTIONQUALITY', 'Configuration of Transaction Quality validation options'),
		('Site Controller', 'POS', 'PoS integration options'),
		('Site Controller', 'CONNECTIONS', 'Connection details for exposing the service and connecting to other components'),
		('Site Controller', 'LOGGING', 'Options for trace/instrumentation logging'),
		('Site Controller', 'WEB', 'Options for the web UI/data retrieval'),
		('Site Controller', 'CONNECTIVITY', 'Configuration for network timeouts, polling'),
		-- HOPT-1292
		('Site Controller', 'CACHE', 'All Cache related options')
		-- HOPT-1383
		,('Site Controller', 'MARINA', 'Settings for Marina sites')
		-- SLIB-92
		, ('Site Controller', 'LOGGING:DEVELOPER', 'All develop loggging related options')	
		-- HOPT-1628
		, ('Site Controller', 'TIMERS', 'All timer related options')		
		-- HOPT-1931
		, ('Site Controller', 'OPT', 'OPT behaviour options')	
		-- SLIB-160
		, ('Site Controller', 'WORKERS', 'All core Worker related options')	
		;
	
	DECLARE @defaultCategory VARCHAR(64) = 'GENERAL';

	INSERT INTO @configDetail(Category, [Key], [Value]) 
	VALUES 
		-- HOPT-1172
		('CONTACTLESS', 'ENABLED', 'False'),
	    ('CONTACTLESS', 'CARDPREAUTH', '4500'),
	    ('CONTACTLESS', 'DEVICEPREAUTH', '10000'),
	    ('CONTACTLESS', 'TTQ', '32204000'), -- HOPT-1973
		-- HOPT-1062
		('GENERAL', 'HydraId', 'Hydra 1'),
		('GENERAL', 'HydraIdInteractive', 'Hydra 3'),
		('GENERAL', 'IsSwaggerEnabled', 'True'),
		('GENERAL', 'GenericOptConfig:MaxFillOverride:Limit:pence', '99000'),
		('CONNECTIONS', 'WebAppURL', 'http://*'),
		('CONNECTIONS', 'WebAppURLAlternate', 'http://*:8000'),
		('POS', 'Retalix:OfflineTransactionFile:InhibitMovement', 'True'),
		('LOGGING', 'log4net.Internal.Debug', 'False'),
		('WEB', 'TransactionLimit', '1000'),
		('CONNECTIONS', 'OfflineTransactionServicePath', 'C:\HydraOPTService\OfflineTransactionService\Htec.HydraOpt.OfflineTransactionService.exe'),
		('CONNECTIVITY', 'Socket:Send:Timeout:', '00:00:05'),
		('CONNECTIVITY', 'Socket:Receive:Timeout:', '00:00:05'),
		('CONNECTIVITY', 'Socket:Send:Timeout:HydraPosConnectionThread', '00:00:07.500'),
		('CONNECTIVITY', 'Poll:Interval:MilliSeconds:SocketWithBuffer', '500'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Action:Block:Pump', 'True'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Action:Block:Opt', 'True'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Action:Clear:Txn', 'True'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Threshold:VendTime', '2'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Threshold:FlowSpeed', '3000'),
		('TRANSACTIONQUALITY', 'TransactionQuality:Threshold:AuthTime', '2'),
		-- HOPT-1076/1770
		('POS', 'Retalix:MaxTransactionNumber', '99999'),
		-- HOPT-750/1294
		('SITEINFO', 'SITETYPE', 'Retail'),
		-- HOPT-1416
		('SITEINFO', 'POSTYPE', 'Retalix'),
		-- HOPT-1117
		('SITEINFO', 'LOCALACCOUNTSENABLED', 'false'),
		-- HOPT-1292
		('CACHE', 'Cache:Interval:HydraDb:EndPoints:', '04:00:00')
		-- HOPT-1383
		,('MARINA', 'DutySplitString', '60/40')
		-- HOPT-1511
		,('CONNECTIVITY', 'ConnectionThread:Log:RxTx:OptHearbeatConnectionThread', 'False')
		-- SLIB-92
		, ('Site Controller', 'LOGGING:DEVELOPER', 'All develop loggging related options')	
		-- HOPT-1453
		,('CONNECTIVITY', 'BackgroundTask:Interval:Web', '00:00:02')
		-- HOPT-1464
		,('TIMERS', 'Timer:Interval:FromOptWorker+EmbeddedTimerable', '00:00:03')
		-- HOPT-954
		,('CONNECTIVITY', 'ConnectionThread:Execute:Active:CarWashConnectionThread', 'False')
		,('CONNECTIVITY', 'ConnectionThread:Execute:Active:HydraMobileConnectionThread', 'False')
		-- HOPT-1024
		,('LOGGING','SumoLogic:ExternalLoggers', 'Hsc' )
		-- HOPT-1628
		,('CONNECTIVITY', 'BackgroundTask:Interval:ConfigUpdateWorker', '00:30:00') 
		,('CONNECTIVITY', 'BackgroundTask:Interval:UpdateWorker', '00:01:00') -- HOPT-1706
		,('TIMERS', 'Timer:Interval:ConfigUpdateWorker+EmbeddedTimerable', '00:30:00')
		,('TIMERS', 'Timer:Interval:ControllerWorker+EmbeddedTimerable', '00:00:01')
		,('TIMERS', 'Timer:Interval:HscWorker+EmbeddedTimerable', '00:00:01')
		,('TIMERS', 'Timer:Interval:JournalWorker+EmbeddedTimerable', '00:00:01')
		-- HOPT-1931
		,('OPT', 'PredefinedAmount:Enabled:FuelCard', 'False')
		,('OPT', 'PredefinedAmount:Enabled:PaymentCard', 'False')
		,('OPT', 'PredefinedAmount:Enabled:LocalAccount', 'False')
		-- HOPT-2030
		,('CONNECTIVITY', 'ConnectionThread:QueuedMessage:Expiry:Interval:', '00:05:00') 
		;
		
	BEGIN TRANSACTION;
	
	    PRINT N' ';
		PRINT N'Updating Configuration Types...';		
		MERGE INTO [dbo].[ConfigurationType] WITH (HOLDLOCK) AS t
		USING @configType AS s
			ON s.[Type] = t.[Type] AND s.Description = t.Description
		WHEN NOT MATCHED BY TARGET THEN
			INSERT ([Type], Description)
			VALUES (s.[Type], s.Description);
-- TODO: Why no [ModifiedDateTime] here!???
			
		
		PRINT N' ';
		PRINT N'Updating Configuration Headers...';		
		MERGE INTO [dbo].[ConfigurationHeader] WITH (HOLDLOCK) AS t
		USING 
		(
			SELECT ch.[Description], ct.Id AS TypeId
			FROM @configHeader ch
				JOIN [dbo].[ConfigurationType] ct ON ct.[Type] = ch.[Type]
		) AS s
			ON s.Description = t.Description
		WHEN NOT MATCHED BY TARGET THEN
			INSERT (TypeId, Description, ModifiedDateTime)
			VALUES (s.TypeId, s.Description, GetDate());
			
			
		PRINT N' ';
		PRINT N'Updating Configuration Categories...';
		MERGE INTO [dbo].[ConfigurationCategory] WITH (HOLDLOCK) AS t
		USING (SELECT DISTINCT Category, CategoryDescription, IsStandardEditable FROM @configCategory) AS s
			ON s.Category = t.Category
		WHEN MATCHED AND (t.[Description] != s.CategoryDescription) OR (t.[IsStandardEditable] != s.IsStandardEditable) THEN
			UPDATE SET t.[Description] = s.CategoryDescription, t.[IsStandardEditable] = s.IsStandardEditable
		WHEN NOT MATCHED BY TARGET THEN
			INSERT (Category, Description, IsStandardEditable)
			VALUES (s.Category, s.CategoryDescription, s.IsStandardEditable);
-- TODO: Why no [ModifiedDateTime] here!???


		PRINT N' ';
		PRINT N'Updating Configuration Details...';
		MERGE INTO [dbo].[ConfigurationDetail] WITH (HOLDLOCK) AS t
		USING
		(
			SELECT cd.*, h.Id AS HeaderId, c.ID AS CategoryId
			FROM @configDetail cd
				JOIN @configCategory cc ON cd.Category = cc.Category
				JOIN [dbo].[ConfigurationHeader] h ON h.Description = cc.Header
				JOIN [dbo].[ConfigurationCategory] c ON c.Category = cd.Category			
		) AS s
			ON s.[Key] = t.[Key] AND s.CategoryId = t.CategoryId AND s.HeaderId = t.HeaderId
		WHEN MATCHED AND (t.Value != s.Value) THEN
			UPDATE SET t.[Value] = s.[Value], t.ModifiedDateTime = GetDate()
		WHEN NOT MATCHED BY TARGET THEN
			INSERT ([HeaderId],[CategoryId],[Key],[Value],[ModifiedDateTime])
			VALUES (s.HeaderId, s.CategoryId, s.[Key], s.[Value], GetDate());

	SELECT ch.Description, cc.Category, cd.* 
		FROM [dbo].[ConfigurationDetail] cd
			JOIN [dbo].[ConfigurationHeader] ch ON ch.Id = cd.HeaderId
			JOIN [dbo].[ConfigurationCategory] cc ON cc.Id = cd.CategoryId;
			
  COMMIT TRANSACTION;
  
END TRY
BEGIN CATCH

    IF (@@TRANCOUNT <> 0)
    BEGIN
        ROLLBACK TRANSACTION; --Required if transaction open above
    END;
    THROW;

END CATCH;
