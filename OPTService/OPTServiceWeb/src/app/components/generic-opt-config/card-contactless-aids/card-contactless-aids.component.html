<div [formGroup]="form">
    <ngb-accordion [closeOthers]="true" [activeIds]="activeCtlsCardAid" (panelChange)="toggleActiveCtlsCardAid($event)" [formArrayName]="formArrayName">
        <ngb-panel *ngFor="let item of cardClessAids?.controls; let i = index" [formGroupName]="i" [id]="'clessaid'+ item.get('Aid').value">
            <ng-template ngbPanelHeader>
                <div class="d-flex align-items-center justify-content-between">
                    <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                        <span>{{item.get('Aid').value}}</span>
                    </button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidAppVerTerm" labelText="App Ver Term" controlName="AppVerTerm">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTransLimit" labelText="Trans limit" controlName="TransLimit">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidFloorLimit" labelText="Floor limit" controlName="FloorLimit">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidCvmLimit" labelText="CVM limit" controlName="CvmLimit">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidOdcvmLimit" labelText="ODCVM limit" controlName="OdcvmLimit">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTermAddCapabilities" labelText="Term add capabilities" controlName="TermAddCapabilities">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTermCapabilitiesCvm" labelText="Term capabilities CVM" controlName="TermCapabilitiesCvm">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTermCapabilitiesNoCvm" labelText="Term capabilities no CVM" controlName="TermCapabilitiesNoCvm">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTermRiskData" labelText="Term risk data" controlName="TermRiskData">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidUdol" labelText="UDOL" controlName="Udol">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTacDefault" labelText="TAC Default" controlName="TacDefault">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTacDenial" labelText="TAC Denial" controlName="TacDenial">
                </app-label-text>
                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                    id="contactlessAidTacOnline" labelText="TAC Online" controlName="TacOnline">
                </app-label-text>    
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>