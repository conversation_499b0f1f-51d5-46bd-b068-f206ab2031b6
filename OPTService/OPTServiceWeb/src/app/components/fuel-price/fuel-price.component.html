<form [formGroup]="fuelPriceForm">
    <div class="card form-group">
        <div class="card-header">
            <button type="button" (click)="dataContainerCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                <span>Grade {{fuelPrice.Grade}} - {{fuelPrice.GradeName}} {{fuelPrice.PriceToSet}} p/l</span>
                <div>
                    <span>Pumps: </span>
                    <span *ngFor="let price of fuelPrice.Prices; let i = index" class="badge badge-pill badge-primary badge-pump mx-1">{{price.Pump}}</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':dataContainerIsCollapsed,'bi-chevron-up':dataContainerIsCollapsed===false }"></i>
                </div>
            </button>            
        </div>
        <div #dataContainerCollapse="ngbCollapse" [(ngbCollapse)]="dataContainerIsCollapsed" class="card-body">
            <div class="row">
                <div class="col-12 col-xl-6">
                    <app-label-text-button id="gradeName" labelColClass="col-12 col-md-2" textColClass="col-12 col-md-7"
                        labelText="Name" controlName="GradeName" [errorInAction]="fuelGradeNameError"
                        (action)="setGradeName()" [formGroup]="fuelPriceForm" [disabled]="pumpType == 'DOMS'" maxLength="50"></app-label-text-button>
                    <app-label-text-button id="priceToSet" labelColClass="col-12 col-md-2"
                        textColClass="col-12 col-md-7" labelText="Price" appendText="p/l" controlName="PriceToSet"
                        [errorInAction]="fuelPriceError" (action)="setFuelPrice()" [formGroup]="fuelPriceForm"
                        [disabled]="pumpType == 'DOMS'" maxLength="6">
                    </app-label-text-button>
                    <app-label-text-button id="vatRate" labelColClass="col-12 col-md-2" textColClass="col-12 col-md-7"
                        labelText="VAT rate" appendText="%" controlName="VatRate" [errorInAction]="fuelGradeVATError"
                        (action)="setGradeVatRate()" [formGroup]="fuelPriceForm" maxLength="6"></app-label-text-button>
                </div>
                <div class="col-12 col-xl-6">
                    <div class="row align-items-center">
                        <div class="col-1">
                        </div>
                        <div class="col-2 col-sm-3">
                            <label class="col-form-label">Price</label>
                        </div>
                        <div class="col-9 col-sm-8">
                            <label class="col-form-label">Pumps</label>
                        </div>
                    </div>
                    <div class="row align-items-center" *ngFor="let ppm of pricePumpMapping; let i = index"
                        [id]="'price'+fuelPrice.Grade+i">
                        <div class="col-1 text-right pr-0">
                            <i
                                [ngClass]="ppm.Price === fuelPrice.PriceToSet ? 'bi bi-check2' : 'bi bi-exclamation-triangle'"></i>
                        </div>
                        <div class="col-2 col-sm-3">
                            <label class="col-form-label">{{ppm.Price}} p/l</label>
                        </div>
                        <div class="col-9 col-sm-8">
                            <label *ngFor="let pump of ppm.Pumps; let j = index" class="badge badge-pill badge-primary badge-pump mx-1">{{pump}}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
