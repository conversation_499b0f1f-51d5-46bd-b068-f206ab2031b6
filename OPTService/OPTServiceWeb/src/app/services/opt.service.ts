import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { map, catchError } from 'rxjs/operators';
import { Observable, of, Subject, throwError } from 'rxjs';
import { Opt } from '../core/models/opt.model';
import { environment } from '../../environments/environment';
import { NGXLogger } from 'ngx-logger';
import Utils from '../core/utils/utils';
import * as _ from 'lodash';
import { GenericOptConfig } from '../core/models/genericOptConfig.model';



@Injectable({
  providedIn: 'root'
})
export class OptService {

  private baseUrl: string;
  private cachedGenericOptConfig: GenericOptConfig | undefined;
  private optDataChanged = new Subject<any>();

  /**
   * The OptService constructor
   * @param http The http client used for requests
   */
  constructor(private http: HttpClient,
    private logger: NGXLogger) {
    const apiHeader = new HttpHeaders().set('ApiKey', '06AB47638CED484C80EBEB40100B0AA');

    
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.optController.base;
  }

  /** 
   * Gets the OPT data changed event
   */
  public getOptDataChanged(): Observable<any> {
    return this.optDataChanged.asObservable();
  }

  /**
   * Gets the list of Opts fitering by @param optIdString 
   * @param optIdString The optional OPT IdString parameter.
   */
  public getOpts(optIdString: string = ''): Observable<any> {
    const url = this.baseUrl + environment.optController.getOptInfo;
    const params = {
      idString: optIdString
    };
    const options = {
      params: optIdString ? params : {}
    };

    this.logger.debug(`Getting ${optIdString? 'single' : 'all'} OPT(s) via url: ${url} ${optIdString? 'optIdString: ' + optIdString : ''}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Sets a new receipt header for the given Opt
   * @param opt The target Opt to be updated
   */
  public setReceiptHeader(opt: Opt) {
    const url = this.baseUrl + environment.optController.setReceiptHeader;
    this.logger.debug(`Setting receipt header: ${opt.ReceiptHeaders} to Opt: ${opt.StringId} via ${url}`);
    return this.http.post(url, {ReceiptHeaders: opt.ReceiptHeaders, StringId: opt.StringId});
  }

  /**
   * Sets the value for global receipt headers
   * @param receiptHeaders 
   */
  public setGlobalReceiptHeader(receiptHeaders: string[], optstringId: string = null) {
    const url = this.baseUrl + environment.optController.setReceiptHeader;
    this.logger.debug(`Setting global receipt headers: ${receiptHeaders} via ${url}`);
    return this.http.post(url, {ReceiptHeaders: receiptHeaders, StringId: optstringId, IsGlobal: true});
  }

  /**
   * Sets a new receipt footer for the given Opt
   * @param opt The target Opt to be updated
   */
    public setReceiptFooter(opt: Opt) {
    const url = this.baseUrl + environment.optController.setReceiptFooter;
    this.logger.debug(`Setting receipt footer: ${opt.ReceiptFooters} to Opt: ${opt.StringId} via ${url}`);
    return this.http.post(url, {ReceiptFooters: opt.ReceiptFooters, StringId: opt.StringId});
  }

  /**
   * Sets the value for global receipt footers
   * @param receiptFooters 
   */
  public setGlobalReceiptFooter(receiptFooters: string[], optstringId: string = null) {
    const url = this.baseUrl + environment.optController.setReceiptFooter;
    this.logger.debug(`Setting global receipt footers: ${receiptFooters} via ${url}`);
    return this.http.post(url, {ReceiptFooters: receiptFooters, StringId: optstringId, IsGlobal: true});
  }

  /**
   * Gets the generic OPT config
   * @param forceReload It forces a call to the API to get fresh data
   */
  public getGenericOptConfig(forceReload: boolean = false): Observable<GenericOptConfig> {
    let self = this;

    // Only call the API if forceReload is set to true or cached data is undefined
    if(forceReload || self.cachedGenericOptConfig === undefined){
      const url = self.baseUrl + environment.optController.getGenericOptConfig;
      const params = {
      };
      const options = {
        params: params
      };
  
      self.logger.debug(`Getting generic OPT config via url: ${url}`);
  
      return self.http.get<GenericOptConfig>(url, options)
        .pipe(map(response => {
          self.cachedGenericOptConfig = response;
          self.optDataChanged.next();
          return response;
        }, catchError(error => self.handleErrorObservable(error))));
    }
    else{
      return of(self.cachedGenericOptConfig);
    }    
  }

  /**
   * Sets the service address for the generic OPT config
   */
  public setGenericServiceAdress(ipAddress: string, port: number = 0) {
    const url = this.baseUrl + environment.optController.setSetGenericServiceAddress;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting generic service address: ${ipAddress} via ${url}`);
    return this.http.post(url, {IpAddress: ipAddress, Port: port}, options);
  }

  /**
   * Add an e-socket endpoint
   * @param ipAddress 
   * @param port 
   */
  public addEsocketEndpoint(ipAddress: string, port: number){
    const url = this.baseUrl + environment.optController.addEsocket;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Adding esocket endpoint: ${ipAddress}:${port} via ${url}`);
    return this.http.post(url, {IpAddress: ipAddress, Port: port}, options);
  }

  /**
   * Removes an e-socket endpoint
   * @param ipAddress 
   * @param port 
   */
  public removeEsocketEndpoint(ipAddress: string, port: number){
    const url = this.baseUrl + environment.optController.removeEsocket;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Removing esocket endpoint: ${ipAddress}:${port} via ${url}`);
    return this.http.post(url, {IpAddress: ipAddress, Port: port}, options);
  }

  /**
   * Add a tariff mapping
   * @param grade 
   * @param productCode 
   * @param fuelCardsOnly 
   */
  public addTariffMapping(grade: number, productCode: string, fuelCardsOnly: boolean){
    const url = this.baseUrl + environment.optController.addTariffMapping;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Adding tariff mapping via ${url}`);
    return this.http.post(url, {Grade: grade, ProductCode: productCode, FuelCardsOnly: fuelCardsOnly}, options);
  }

  /**
   * Removes a tariff mapping
   * @param grade 
   * @param productCode 
   */
  public removeTariffMapping(grade: number, productCode: string){
    const url = this.baseUrl + environment.optController.removeTariffMapping;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Removing tariff mapping via ${url}`);
    return this.http.post(url, {Grade: grade, ProductCode: productCode}, options);
  }

  /**
   * Sets the tariff mapping for only fuel cards or not
   */
  public setTariffMappingFuelCardsOnly(grade: number, fuelCardsOnly: boolean){
    const url = this.baseUrl + environment.optController.setTariffMappingFuelCardsOnly;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Settings tariff mapping fueld cards only via ${url}`);
    return this.http.post(url, {Grade: grade, FuelCardsOnly: fuelCardsOnly}, options);
  }

  /**
   * Adds or updates a discount card
   * @param iin 
   * @param name 
   * @param type 
   * @param value 
   * @param grade 
   */
  public addDiscountCard(iin: string, name: string, type: string, value: number, grade: number){
    const url = this.baseUrl + environment.optController.addDiscountCard;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Adding discount card via ${url}`);
    return this.http.post(url, {Iin: iin, Name: name, Type: type, Value: value, Grade: grade}, options);
  }

  /**
   * Removes a discount card
   * @param iin 
   */
  public removeDiscountCard(iin: string){
    const url = this.baseUrl + environment.optController.removeDiscountCard;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Removing discount card via ${url}`);
    return this.http.post(url, {Iin: iin}, options);
  }

  /**
   * Adds a new washing program
   * @param programId 
   * @param productCode 
   * @param description 
   * @param price 
   * @param vatRate 
   * @param category 
   * @param subcategory 
   * @returns 
   */
  public addWash(programId: number, productCode: string, description: string, price:number, vatRate: number, category: string, subcategory: string){
    const url = this.baseUrl + environment.optController.addWash;
    this.logger.debug(`Adding wash: ${programId}, ${productCode}, ${description}, ${price}, ${vatRate}, ${category}, ${subcategory} via ${url}`);
    return this.http.post(url, {ProgramId: programId, ProductCode: productCode, Description: description, Price: price, VatRate: vatRate, Category: category, Subcategory: subcategory});
  }

/**
 * Removes an existing washing program
 * @param programId 
 * @param productCode 
 * @param description 
 * @param price 
 * @param vatRate 
 * @param category 
 * @param subcategory 
 * @returns 
 */
  public removeWash(programId: number, productCode: string, description: string, price:number, vatRate: number, category: string, subcategory: string){
    const url = this.baseUrl + environment.optController.removeWashByProgramId;
    this.logger.debug(`Removing wash: ${programId}, ${productCode}, ${description}, ${price}, ${vatRate}, ${category}, ${subcategory} via ${url}`);
    return this.http.post(url, {ProgramId: programId, ProductCode: productCode, Description: description, Price: price, VatRate: vatRate, Category: category, Subcategory: subcategory});
  }

  /**
   * Add a new predefined amount
   * @param amount 
   */
  public addPredefinedAmount(amount: number){
    const url = this.baseUrl + environment.optController.addPredefinedAmount;
    this.logger.debug(`Adding predefined amount wash: ${amount} via ${url}`);
    return this.http.post(url, {Amount: amount});
  }

  /**
   * Removes a new predefined amount
   * @param amount 
   */
   public removePredefinedAmount(amount: number){
    const url = this.baseUrl + environment.optController.removePredefinedAmount;
    this.logger.debug(`Removing predefined amount wash: ${amount} via ${url}`);
    return this.http.post(url, {Amount: amount});
  }

  /**
   * Sets the value for receipt reprint availability
   * @param timeout 
   */
  public setReceiptReprintAvailability(timeout: number) {
    const url = this.baseUrl + environment.optController.setReceiptReprintAvailability;
    this.logger.debug(`Setting receipt reprint availability: ${timeout} via ${url}`);
    return this.http.post(url, {ReceiptReprintAvailability: timeout});
  }

  /**
   * Sets the value for receipt max count
   * @param count 
   */
  public setReceiptMaxCount(count: number) {
    const url = this.baseUrl + environment.optController.setReceiptMaxCount;
    this.logger.debug(`Setting receipt max count: ${count} via ${url}`);
    return this.http.post(url, {ReceiptMaxCount: count});
  }

  /**
   * Sets the value for receipt layout mode
   * @param mode 
   */
  public setReceiptLayoutMode(mode: number){
    const url = this.baseUrl + environment.optController.setReceiptLayoutMode;
    this.logger.debug(`Setting receipt layout mode: ${mode} via ${url}`);
    return this.http.post(url, {Mode: mode});
  }

  /**
   * Reloads OPT configuration
   */
  public reloadOptConfiguration(){
    const url = this.baseUrl + environment.optController.reloadOptConfiguration;
    this.logger.debug(`Reloading OPT configuration via ${url}`);
    return this.http.get(url);
  }

  /**
   * Starts the config batch
   * @returns 
   */
  public startConfigBatch(){
    const url = this.baseUrl + environment.optController.startConfigBatch;
    this.logger.debug(`Starting config batch via ${url}`);
    return this.http.get(url);
  }

  /**
   * Stops the config batch
   * @returns 
   */
  public stopConfigBatch(){
    const url = this.baseUrl + environment.optController.stopConfigBatch;
    this.logger.debug(`Stopping config batch via ${url}`);
    return this.http.get(url);
  }

  /**
   * Restarts the received Opt
   * @param opt The target Opt to be restarted
   */
  public restartOpt(opt: Opt) {
    const url = this.baseUrl + environment.optController.RestartOpt;
    const params = {};
    const options = {
      params: params
    };
    this.logger.debug(`Sending restart to Opt: ${opt.StringId} via ${url}`);
    return this.http.post(url, opt, options);
  }

  /**
   * Refresh one or all Opt
   * @param optIdString The target Opt to be refreshed
   */
  public refreshOpt(optIdString: string) {
    const url = this.baseUrl + environment.optController.SendConfigPending;
    const params = {};
    const options = {
      params: params
    };

    var body = {};
    if (optIdString != null) {
      _.set(options, "params.optId", optIdString);
      this.logger.debug(`Sending refresh to Opt: ${optIdString} via ${url}`);
    } else{
      this.logger.debug(`Sending refresh to all Opt via ${url}`);
    }
    
    return this.http.post(url, body, options);
  }

  /**
   * Request log for one or all Opt
   * @param optIdString The target Opt to obtain log from
   */
  public requestOptLog(optIdString: string) {
    const url = this.baseUrl + environment.optController.SendRequestLogFile;
    const params = {};
    const options = {
      params: params
    };

    var body = {};
    if (optIdString != null) {
      _.set(options, "params.optId", optIdString);
      this.logger.debug(`Sending request log to Opt: ${optIdString} via ${url}`);
    } else {
      this.logger.debug(`Sending request log to all Opt via ${url}`);
    }

    return this.http.post(url, body, options);
  }

  /**
   * Sets the playlist filename to the Opt
   * @param opt The target Opt to be updated
   */
  public setPlaylistFileName(opt: Opt) {
    const url = this.baseUrl + environment.optController.SetPlaylistFileName;
    const options = { params: {} };
    this.logger.debug(`Setting playlist filename: ${opt.PlaylistFileName} to Opt: ${opt.StringId} via ${url}`);
    return this.http.post(url, opt, options);
  }

  /**
   * Enables or disbales contactless
   * @param isEnabled The value to be set
   */
   public setContactlessAllowed(isEnabled: boolean){
    const url = this.baseUrl + environment.optController.SetContactlessAllowed;
    this.logger.debug(`Setting contactless allowed: ${isEnabled} via ${url}`);
    return this.http.post(url, {IsEnabled: isEnabled});
  }

  /**
   * Sets the value for contactless card preauth limit
   * @param cardPreAuthLimit The value to be set
   */
   public setContactlessCardPreAuth(cardPreAuthLimit: number){
    const url = this.baseUrl + environment.optController.setContactlessCardPreAuth;
    this.logger.debug(`Setting contactless card preauth limit: ${cardPreAuthLimit} via ${url}`);
    return this.http.post(url, {CardPreAuthLimit: cardPreAuthLimit});
  }

  /**
   * Sets the value for contactless device preauth limit
   * @param devicePreAuthLimit The value to be set
   */
   public setContactlessDevicePreAuth(devicePreAuthLimit: number){
    const url = this.baseUrl + environment.optController.setContactlessDevicePreAuth;
    this.logger.debug(`Setting contactless device preauth limit: ${devicePreAuthLimit} via ${url}`);
    return this.http.post(url, {DevicePreAuthLimit: devicePreAuthLimit});
  }

  /**
   * Sets the value for contactless ttq
   * @param ttq The value to be set
   */
   public setContactlessTtq(ttq: string){
    const url = this.baseUrl + environment.optController.setContactlessTtq;
    this.logger.debug(`Setting contactless TTQ: ${ttq} via ${url}`);
    return this.http.post(url, {Ttq: ttq});
  }

   /**
  * Enables or disbales contactless
  * @param showSingleButton The value to be set
  */
  public setContactlessSingleButton(showSingleButton: boolean) {
     const url = this.baseUrl + environment.optController.SetContactlessSingleButton;
     this.logger.debug(`Setting contactless single button: ${showSingleButton} via ${url}`);
     return this.http.post(url, { showSingleButton: showSingleButton });
   }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
