using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    /// <inheritdoc/>
    [Migration(28000001, "ADO#715510 - Add dbo.PumpGradePriceInfo table"), Tags(Constants.MigrationTypeSchema)]
    public class ADO715510_Add_PumpGradePriceInfo : Migrationable
    {
        private const string TableName = "PumpGradePriceInfo";

        /// <inheritdoc/>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.dbo.{TableName}.sql");
        }

        /// <inheritdoc/>
        public override void Down()
        {
            if (Schema.Table(TableName).Exists())
            {
                Delete.Table(TableName);
            }
        }
    }
}
