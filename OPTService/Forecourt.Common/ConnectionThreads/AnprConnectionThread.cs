using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Logger.Interfaces;

namespace OPT.Common.ConnectionThreads
{
    [HasConfiguration]
    public class AnprConnectionThread : PlainTextClientConnectionThread
    {
        public AnprConnectionThread(IHtecLogManager logMan, ISocketWrapperFactory socketWrapperFactory, IConfigurationManager configurationManager,
            MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.None, bool logRxTx = true) : base(logMan, "ANPR", socketWrapperFactory, configurationManager, messageLengthFlow, logRxTx)
        {
        }
    }
}
