USE [Hydra]

PRINT N'HOPT-750/1294 - Site Type...Begin';
PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
 
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'TTQ')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ 
	FROM SiteInfo
END

GO
PRINT N'HOPT-750/1294 - Site Type...End';

GO
