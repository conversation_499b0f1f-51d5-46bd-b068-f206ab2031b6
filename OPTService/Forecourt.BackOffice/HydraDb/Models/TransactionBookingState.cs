using System;
using System.Net;

namespace Forecourt.Bos.HydraDb.Models
{
    /// <summary>
    /// Transaction Booking State
    /// </summary>
    public class TransactionBookingState
    {
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// Transaction Id (internal)
        /// </summary>
        public long TransactionId { get; set; }

        /// <summary>
        /// OPT transaction number
        /// </summary>
        public string TxnNumber { get; set; }

        /// <summary>
        /// Transaction Id (external)
        /// </summary>
        public string ExternalTransactionId { get; set; }

        /// <summary>
        /// Transaction date
        /// </summary>
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// Shift Id
        /// </summary>
        public int ShiftId { get; set; }

        /// <summary>
        /// Period Id
        /// </summary>
        public int PeriodId { get; set; }

        /// <summary>
        /// Business day
        /// </summary>
        public DateTime BusinessDate {get; set; }

        /// <summary>
        /// Booking result code, in HttpStatusCode format
        /// </summary>
        public HttpStatusCode HttpStatusCode { get; set; }

        /// <summary>
        /// Booking response code
        /// </summary>
        public string HttpResponse { get; set; }

        /// <summary>
        /// Date the transaction was booked, in the external system
        /// </summary>
        public DateTime BookedDate { get; set; }

        /// <summary>
        /// If transaction failed to book, the current Retry count
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// If transaction failed to book, when is the next retry time
        /// </summary>
        public DateTime NextRetryDate { get; set; }

        /// <summary>
        /// Date created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date modified
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// The <see cref="SendTransactionItem"/> state at the time of booking, as json string
        /// </summary>
        public string SendTransactionItem { get; set; }
    }
}
