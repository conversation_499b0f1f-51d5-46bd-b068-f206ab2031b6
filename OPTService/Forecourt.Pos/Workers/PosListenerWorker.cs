using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System.Net;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc cref="ListenerWorker{T, THydraDb, TTelemetryWorker}" />
    public abstract class PosListenerWorker<T, THydraDb, TTelemetryWorker> : ListenerWorker<T, THydraDb, TTelemetryWorker>, IPosListenerWorker<T>
        where THydraDb : HydraDb.Interfaces.IHydraDb
        where TTelemetryWorker : ITelemetryWorker
    {
        /// <inheritdoc />
        public IPAddress PrimaryIpAddress { get; protected set; } = null;

        /// <inheritdoc cref="ListenerWorker{T, THydraDb, TTelemetryWorker}" />
        protected PosListenerWorker(IHtecLogger logger, TTelemetryWorker telemetryWorker, IListenerConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null, string defaultTimerInterval = null) : base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, defaultTimerInterval)
        {
        }

        /// <inheritdoc cref="ListenerWorker{T, THydraDb, TTelemetryWorker}" />
        protected PosListenerWorker(IHtecLogManager logMan, string loggerName, TTelemetryWorker telemetryWorker, IListenerConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null, string defaultTimerInterval = null) : base(logMan, loggerName, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, defaultTimerInterval)
        {
        }

        /// <inheritdoc cref="IPosIntegratorOutMode{IMessageTracking}" />
        protected virtual Result DoModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking message = null, bool suppressModeChange = false)
        {
            return Result.Success();
        }

        /// <inheritdoc cref="IPosIntegratorOutMode{IMessageTracking}" />
        public Result ModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking message = null, bool suppressModeChange = false)
        {
            message ??= new MessageTracking();
            return DoAction(() => { return DoModeChangedResponse(pump, mode, previousState, message, suppressModeChange); }, message.FullId);
        }      

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        protected virtual Result DoReceiptResponse(byte pump, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        public Result ReceiptResponse(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => { return DoReceiptResponse(pump, message); }, message.FullId);
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        protected virtual Result DoReceiptResponse(ReceiptInfo receipt, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        public Result ReceiptResponse(ReceiptInfo receipt, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => { return DoReceiptResponse(receipt, message); }, message.FullId);
        }

        /// <inheritdoc cref="IPosIntegratorInTransient{TMessageTracking, TPump}" />
        public Result RequestReceipt(byte pump, IMessageTracking message = null)
        {
            return ReceiptResponse(pump, message);
        }

        public Result RequestReceipt(GetReceiptRequest request, IMessageTracking message = null)
        {
            return ReceiptResponse(request.Pump, message);
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        protected virtual Result DoStatusResponse(byte pump, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        public Result StatusResponse(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => { return DoStatusResponse(pump, message); }, message.FullId);
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        protected virtual Result DoStatusResponse(StatusResponse status, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc cref="IPosIntegratorOutTransient{TMessageTracking}" />
        public Result StatusResponse(StatusResponse status, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => { return DoStatusResponse(status, message); }, message.FullId);
        }

        /// <inheritdoc cref="IPosIntegratorInTransient{TMessageTracking, TPump}" />
        public Result RequestStatus(byte pump, IMessageTracking message = null)
        {
            return StatusResponse(pump, message);
        }

        /// <inheritdoc cref="IPosIntegratorInTransient{TMessageTracking, TPump}" />
        public Result RequestStatus(IPump pump, string loggingReference = null)
        {
            var message = new MessageTracking { IdAsString = loggingReference };
            return StatusResponse(pump.Number, message);
        }
    }
}
