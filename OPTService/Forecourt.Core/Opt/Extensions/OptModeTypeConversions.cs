using Forecourt.Core.Opt.Enums;
using Htec.Hydra.Core.Pos.Common;

namespace Forecourt.Core.Opt.Extensions
{
    /// <summary>
    /// Helper methods to convert from <see cref="OptModeType"/> to 
    /// </summary>
    public static class OptModeTypeConversions
    {
        public static ModeChangeType ToModeChangeType(this OptModeType type)
        {
            return type switch
            {
                OptModeType.OptModeMixed => ModeChangeType.Mixed,
                OptModeType.OptModeNotSet => ModeChangeType.Close,
                OptModeType.OptModeOpt => ModeChangeType.OutsideOnly,
                OptModeType.OptModeKioskOnly => ModeChangeType.KioskOnly,
                _ => ModeChangeType.Close
            };
        }

        public static OptModeType ToOptModeType(this ModeChangeType type)
        {
            return type switch
            {
                ModeChangeType.Open => OptModeType.OptModeMixed,
                ModeChangeType.Close => OptModeType.OptModeNotSet,
                ModeChangeType.Mixed => OptModeType.OptModeMixed,
                ModeChangeType.KioskOnly => OptModeType.OptModeKioskOnly,
                ModeChangeType.OutsideOnly => OptModeType.OptModeOpt,
                _ => OptModeType.OptModeMixed
            };
        }
    }
}
