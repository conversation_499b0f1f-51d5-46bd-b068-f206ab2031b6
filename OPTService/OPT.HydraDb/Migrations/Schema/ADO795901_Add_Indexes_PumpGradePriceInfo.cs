using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(28200005, "ADO#795901 - Add indexes to dbo.PumpGradePriceInfo"), Tags(Constants.MigrationTypeSchema)]
    public class ADO795901_Add_Indexes_PumpGradePriceInfo : Migrationable
    {
        private const string TableName = "PumpGradePriceInfo";
        private const string IndexNamePrefix = "uix_" + TableName + "_";

        /// <inheritdoc/>
        public ADO795901_Add_Indexes_PumpGradePriceInfo(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var indexName = IndexNamePrefix + "ids";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("Pump").Ascending()
                    .OnColumn("Hose").Ascending()
                    .OnColumn("Grade").Ascending()
                    .OnColumn("Id").Ascending()
                    .WithOptions().Unique()
                    .WithOptions().NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            var indexName = IndexNamePrefix + "ids";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }      
        }
    }
}

