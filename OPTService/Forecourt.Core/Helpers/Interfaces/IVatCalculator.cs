namespace Forecourt.Core.Helpers.Interfaces
{
    /// <summary>
    /// Any and all capabilities related to the calculation of VAT
    /// </summary>
    public interface IVatCalculator
    {
        /// <summary>
        /// Calculate the VAT and Net amount 
        /// </summary>
        /// <param name="amount">Gross amount</param>
        /// <param name="vatrate">VAT Rate</param>
        /// <param name="decPlaces">No. decimal places to round 2</param>
        /// <returns>(vatAmount, netAmount)</returns>
        (double, double) CalculateVat(double amount, double vatrate, byte decPlaces = 2);

        /// <summary>
        /// Calculate the VAT and Net amount 
        /// </summary>
        /// <param name="amount">Gross amount</param>
        /// <param name="vatrate">VAT Rate</param>
        /// <param name="decPlaces">No. decimal places to round 2</param>
        /// <returns>(vatAmount, netAmount)</returns>
        (uint, uint) CalculateVat(uint amount, float vatrate, byte decPlaces = 0);
    }
}
