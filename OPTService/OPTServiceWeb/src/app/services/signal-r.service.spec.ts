import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { SignalREventType } from '../core/enums/signalREventType';
import { SignalRService } from './signal-r.service';

describe('SignalRService', () => {
  let signalRService: SignalRService;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['debug','warn','error']);

    TestBed.configureTestingModule({
      providers: [
        SignalRService,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
      ]
    });
    signalRService = TestBed.inject(SignalRService);
  });

  it('should be created', () => {
    expect(signalRService).toBeTruthy();
  });

  describe('.processPushedEvent()', () => {

    it('should push message for event type OPTChanged', () => {
      spyOn(signalRService['optSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.OPTChanged);

      expect(signalRService['optSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type PumpChanged', () => {
      spyOn(signalRService['pumpSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.PumpChanged);

      expect(signalRService['pumpSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type FileLocationsChanged', () => {
      spyOn(signalRService['fileLocationSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.FileLocationsChanged);

      expect(signalRService['fileLocationSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type AdvancedConfigChanged', () => {
      spyOn(signalRService['advancedConfigSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.AdvancedConfigChanged);

      expect(signalRService['advancedConfigSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type GenericOptConfigChanged', () => {
      spyOn(signalRService['genericOptConfigSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.GenericOptConfigChanged);

      expect(signalRService['genericOptConfigSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type TransactionChanged', () => {
      spyOn(signalRService['transactionSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.TransactionChanged);

      expect(signalRService['transactionSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type FuelPriceChanged', () => {
      spyOn(signalRService['fuelPriceSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.FuelPriceChanged);

      expect(signalRService['fuelPriceSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type ConnectionChanged', () => {
      spyOn(signalRService['connectionSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.ConnectionChanged);

      expect(signalRService['connectionSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type InfoMessageChanged', () => {
      spyOn(signalRService['infoMessageSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.InfoMessageChanged);

      expect(signalRService['infoMessageSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type DivertDetailsChanged', () => {
      spyOn(signalRService['divertDetailsSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.DivertDetailsChanged);

      expect(signalRService['divertDetailsSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type NewMessageChanged', () => {
      spyOn(signalRService['newMesaggeSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.NewMessageChanged);

      expect(signalRService['newMesaggeSignalRMessage'].next).toHaveBeenCalledOnceWith({ itemId: '', additionalData:'' });
    });

    it('should push message for event type ShiftEndChanged', () => {
      spyOn(signalRService['shiftEndSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.ShiftEndChanged);

      expect(signalRService['shiftEndSignalRMessage'].next).toHaveBeenCalledOnceWith({ itemId: '' });
    });

    it('should push message for event type AboutChanged', () => {
      spyOn(signalRService['aboutSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.AboutChanged);

      expect(signalRService['aboutSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });

    it('should push message for event type LocalAccountsChanged', () => {
      spyOn(signalRService['localAccountsSignalRMessage'], 'next');

      signalRService.processPushedEvent(SignalREventType.LocalAccountsChanged);

      expect(signalRService['localAccountsSignalRMessage'].next).toHaveBeenCalledOnceWith('');
    });
  });
});
