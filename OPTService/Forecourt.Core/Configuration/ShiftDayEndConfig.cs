using Forecourt.Core.Configuration.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces;
using System;
using cfgConstants = OPT.Common.ConfigConstants;

namespace Forecourt.Core.Configuration
{
    /// <summary>
    /// Contains all configuration around Shift and Day End
    /// </summary>
    [HasConfiguration]
    public class ShiftDayEndConfig : Loggable, IShiftDayEndConfig
    {
        private const string Dips = "Dips";
        private const string Meters = "Meters";
        private const string ExpiryInterval = cfgConstants.Interval + ":Expiry:";
        private const string MaxAttempts = "MaxAttempts:";

        public const string ConfigKeyMinimumShiftToDayEndInterval = Constants.CategoryNameShiftAndDayEnd + Constants.CategorySeparator + cfgConstants.Interval + ":Minimum:Shift-To-Day-End";
        public const string DefaultValueMinimumShiftToDayEndInterval = "00:01:00";
        protected ConfigurableTimeSpan ConfigMinimumShifToDayEndInterval { get; set; }
        /// <inheritdoc/>
        public TimeSpan MinimumIntervalFromShiftToDayEnd => ConfigMinimumShifToDayEndInterval.GetValue();


        public const string ConfigKeyExpiryIntervalDips = Constants.CategoryNameShiftAndDayEnd + Constants.CategorySeparator + ExpiryInterval + Dips;
        public const string DefaultValueExpiryIntervalDips = "00:00:04";
        protected ConfigurableTimeSpan ConfigExpiryIntervalDips { get; set; }        
        /// <inheritdoc/>
        public TimeSpan ExpiryIntervalDips => ConfigExpiryIntervalDips.GetValue();


        public const string ConfigKeyExpiryIntervalMeters = Constants.CategoryNameShiftAndDayEnd + Constants.CategorySeparator + ExpiryInterval + Meters;
        public const string DefaultValueExpiryIntervalMeters = "00:00:04";
        protected ConfigurableTimeSpan ConfigExpiryIntervalMeters { get; set; }
        /// <inheritdoc/>
        public TimeSpan ExpiryIntervalMeters => ConfigExpiryIntervalMeters.GetValue();


        public const string ConfigKeyMaxAttemptsDips = Constants.CategoryNameShiftAndDayEnd + Constants.CategorySeparator + MaxAttempts + Dips;
        public const int DefaultValueMaxAttemptsDips = 3;
        protected ConfigurableInt ConfigMaxAttemptsDips { get; set; }
        /// <inheritdoc/>
        public int MaxAttemptsDips => ConfigMaxAttemptsDips.GetValue();


        public const string ConfigKeyMaxAttemptsMeters = Constants.CategoryNameShiftAndDayEnd + Constants.CategorySeparator + MaxAttempts + Meters;
        public const int DefaultValueMaxAttemptsMeters = 1;
        protected ConfigurableInt ConfigMaxAttemptsMeters { get; set; }
        /// <inheritdoc/>
        public int MaxAttemptsMeters => ConfigMaxAttemptsMeters.GetValue();

        /// <inheritdoc/>
        public TimeSpan ExpiryIntervalOverall => TimeSpan.FromSeconds(
            Math.Max(ExpiryIntervalDips.TotalSeconds * (MaxAttemptsDips + 1), ExpiryIntervalMeters.TotalSeconds * (MaxAttemptsMeters + 1)));

        /// <inheritdoc />
        public ShiftDayEndConfig(IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(ShiftDayEndConfig), configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            ConfigMinimumShifToDayEndInterval = new ConfigurableTimeSpan(this, ConfigKeyMinimumShiftToDayEndInterval, DefaultValueMinimumShiftToDayEndInterval);
            ConfigExpiryIntervalDips = new ConfigurableTimeSpan(this, ConfigKeyExpiryIntervalDips, DefaultValueExpiryIntervalDips);
            ConfigExpiryIntervalMeters = new ConfigurableTimeSpan(this, ConfigKeyExpiryIntervalMeters, DefaultValueExpiryIntervalMeters);
            ConfigMaxAttemptsDips = new ConfigurableInt(this, ConfigKeyMaxAttemptsDips, DefaultValueMaxAttemptsDips);
            ConfigMaxAttemptsMeters = new ConfigurableInt(this, ConfigKeyMaxAttemptsMeters, DefaultValueMaxAttemptsMeters);
        }
    }
}
