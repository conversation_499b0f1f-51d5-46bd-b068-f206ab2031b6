import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { TransactionService } from './transaction.service';
import { environment } from 'src/environments/environment';
import { FuelTransactionDetails } from '../core/models/fuelTransactionDetails.model';
import { DOCUMENT } from '@angular/common';

describe('TransactionService', () => {
  let transactionService: TransactionService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        TransactionService,
        {provide: NGXLogger, useValue: ngxLoggertSpy},
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    transactionService = TestBed.inject(TransactionService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(transactionService).toBeTruthy();
  });

  it('.getFuelTransactions() should handle success response from API', () => {
    //Arrange
    let mockData = [{
      Amount: 15.01,
      CardNumber: "test",
      DiscountCardNumber: "test",
      DiscountCode: "test",
      DiscountName: "test",
      DiscountValue: 10,
      FuelCategory: "test",
      FuelQuantity: 0.5,
      FuelSubcategory: "test",
      GradeCode: "1",
      GradeName: "test",
      HasReceipt: true,
      LocalAccountMileage: 9,
      LocalAccountRegistration: "test",
      PrinterEnabled: true,
      PumpDetailsString: "PUMP 1:",
      TransactionId: 1,
      TransactionTime: "0001-01-01T00:00:00",
      TxnNumber: "test",
      WashCategory: "test",
      WashCode: "2",
      WashName: "test",
      WashQuantity: 10,
      WashSubcategory: "test"
    }];
    let startDate = new Date('1995-12-16T00:00:00');
    let endDate = new Date('1995-12-17T23:59:59');
    
    //Act
    transactionService.getFuelTransactions(startDate, endDate).subscribe((data) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(mockData);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + 
      environment.transactionController.getFuelTransactions + '?startTime=Sat,%2016%20Dec%201995%2000:00:00%20GMT&endTime=Sun,%2017%20Dec%201995%2023:59:59%20GMT');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockData);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getFuelTransactions() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    let startDate = new Date('1995-12-16T00:00:00');
    let endDate = new Date('1995-12-17T23:59:59');
    
    //Act
    transactionService.getFuelTransactions(startDate, endDate).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + 
      environment.transactionController.getFuelTransactions + '?startTime=Sat,%2016%20Dec%201995%2000:00:00%20GMT&endTime=Sun,%2017%20Dec%201995%2023:59:59%20GMT');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getOtherEvents() should handle success response from API', () => {
    //Arrange
    let mockData = [{
      TransactionId: 1,
      TransactionTime: "0001-01-01T00:00:00",
    },{
      TransactionId: 2,
      TransactionTime: "0001-01-01T00:00:00",
    }];
    let startDate = new Date('1995-12-16T00:00:00');
    let endDate = new Date('1995-12-17T23:59:59');
    
    //Act
    transactionService.getOtherEvents(startDate, endDate).subscribe((data) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(mockData);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + 
      environment.transactionController.getOtherEvents + '?startTime=Sat,%2016%20Dec%201995%2000:00:00%20GMT&endTime=Sun,%2017%20Dec%201995%2023:59:59%20GMT');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockData);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getOtherEvents() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    let startDate = new Date('1995-12-16T00:00:00');
    let endDate = new Date('1995-12-17T23:59:59');
    
    //Act
    transactionService.getOtherEvents(startDate, endDate).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + 
      environment.transactionController.getOtherEvents + '?startTime=Sat,%2016%20Dec%201995%2000:00:00%20GMT&endTime=Sun,%2017%20Dec%201995%2023:59:59%20GMT');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getReceiptURL() should return correct URL', () => {
    //Arrange
     let number = 1;
    
    //Act
    let url = transactionService.getReceiptURL(number);

    //Assert 1: Verify the expected URL value including parameters
    expect(url).toEqual(URL + environment.optServiceBase + environment.transactionController.base + 
      environment.transactionController.getReceipt + '?transactionNumber=' + number);
  });

  it('.printReceipt() should handle success response from API', () => {
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
        
    //Act
    transactionService.printReceipt(fuelTransactionDetails).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + environment.transactionController.printReceipt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.printReceipt() should handle unsuccess response from API', () => {
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    transactionService.printReceipt(fuelTransactionDetails).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + environment.transactionController.printReceipt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.saveReceipt() should handle success response from API', () => {
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
        
    //Act
    transactionService.saveReceipt(fuelTransactionDetails).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + environment.transactionController.saveReceipt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.saveReceipt() should handle unsuccess response from API', () => {
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    transactionService.saveReceipt(fuelTransactionDetails).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.transactionController.base + environment.transactionController.saveReceipt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

});
