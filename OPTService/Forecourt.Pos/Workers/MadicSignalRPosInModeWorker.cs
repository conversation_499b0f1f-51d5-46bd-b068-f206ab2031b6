using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HubClients;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Models.EvoPos;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.HubClient;
using Htec.HubClient.Interfaces;
using Htec.HubClient.Managers;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Custom SignalRPosInModeWorker, specific to Madic SignalR
    /// </summary>
    public class MadicSignalRPosInModeWorker : SignalRPosInModeWorker, IMadicSignalRInModeWorker
    {
        private const string EventName = "AddMessage";

        /// <inheritdoc/>
        public MadicSignalRPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutTransientWorker,
            IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutTransientWorker, configurationManager)
        {
        }

        protected override Result<IHubClient> DoGetHubClient(string endPoint, string hubName, IRetryManager retryManager, params object[] startParams)
        {
            var hubProxyOnEvents = new Dictionary<string, HubClient.HubProxyOnEvent>
                {
                { EventName, MessageReceived }
            };

            return Result.Success<IHubClient>(new PosHubClient(LogManager, ConfigurationManager, endPoint, hubName, retryManager, null, hubProxyOnEvents));
        }

        /// <summary>
        /// Handles a message received from the SignalR hub
        /// </summary>
        /// <param name="parameters"></param>
        private void MessageReceived(dynamic parameters)
        {
            DoAction(() =>
            {
                if (parameters == "Day End completed")
                {
                    DoDeferredLogging(LogLevel.Info, "Event", () => new[] { "Day End completed" });
                    GetWorker<IBosIntegratorInJournal<IMessageTracking>>()?.RequestDayEnd();
                    return;
                }

                try
                {
                    var message = LoggingReference.ToMessageTracking();
                    var madicPumps = JsonConvert.DeserializeObject<List<MadicPump>>(parameters);

                    DoDeferredLogging(LogLevel.Info, "Event", () => new[] { "ModeChange" });
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Event", () => new[] { $"Content: {parameters}" });

                    foreach (var madicPump in madicPumps)
                    {
                        var messagePump = new MessageTracking() { ParentIdAsString = message.IdAsString};
                        DoDeferredLogging(LogLevel.Info, "ModeChange", () => new[] { $"Pump: {madicPump.FuelingPointId}; OPT Mode: {madicPump.OptMode}" }, reference: messagePump.FullId);

                        var modeChangeType = madicPump.OptMode switch
                        {
                            "PUMPONLY" => ModeChangeType.OutsideOnly,
                            "KIOSKONLY" => ModeChangeType.KioskOnly,
                            _ => ModeChangeType.Mixed,
                        };

                        RequestModeChange((byte)madicPump.FuelingPointId, modeChangeType, messagePump);
                    }
                }
                catch (JsonReaderException jsonReaderException)
                {
                    DoDeferredLogging(LogLevel.Error, "Event", () => new[] { $"Unexpected content deserializing to MadicPumps. Content: {parameters}" }, jsonReaderException);
                }
            }, null);
        }

        private bool _isConnected => HubClient.IsActive && HubClient.HubConnection.State == Microsoft.AspNet.SignalR.Client.ConnectionState.Connected;

        /// <inheritdoc/>
        protected override bool DoIsConnected() => _isConnected;

        /// <inheritdoc/>
        protected override int DoGetConnectedCount() => _isConnected ? 1 : 0;

        /// <inheritdoc/>
        protected override IEnumerable<IPAddress> DoGetAllIpAddresses() => new List<IPAddress>();
    }
}