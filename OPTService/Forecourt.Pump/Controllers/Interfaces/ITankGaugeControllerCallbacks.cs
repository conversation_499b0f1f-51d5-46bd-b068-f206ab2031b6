using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Pump.Controllers.Interfaces
{
    /// <summary>
    /// Any and all callbacks of a TankGaugeController
    /// </summary>
    public interface ITankGaugeControllerCallbacks: IWorkerable
    {
        /// <summary>
        /// Called when new Dip information has been received
        /// </summary>
        /// <param name="dips">Current <see cref="Dip"/> information</param>
        /// <param name="loggingReference">Current logging reference</param>
        void OnDips(IEnumerable<Dip> dips, string loggingReference = null);

        /// <summary>
        /// Called when the controller connects
        /// </summary>
        /// <param name="ipAddress">IpAddress of the controller</param>
        /// <param name="port">Port of the controller</param>
        void OnConnected(IPAddress ipAddress = null, int? port = null);

        /// <summary>
        /// Called when the controller disconnects
        /// </summary>
        /// <param name="id">Id of the connection</param>
        void OnDisconnected(int? id = null);
    }
}
