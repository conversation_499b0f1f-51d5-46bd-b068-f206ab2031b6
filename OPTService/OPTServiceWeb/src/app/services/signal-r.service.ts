import { Inject, Injectable, NgZone } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { NGXLogger } from 'ngx-logger';
import { environment } from '../../environments/environment';
import { SignalREventType } from '../core/enums/signalREventType';
import { ITS_JUST_ANGULAR } from '@angular/core/src/r3_symbols';
import { DOCUMENT } from '@angular/common';
import Utils from '../core/utils/utils';
import { Constants } from '../helpers/constants';

declare var $: any;

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  public readonly CONNECTION_STATUS_CONNECTING = 0;
  public readonly CONNECTION_STATUS_CONNECTED = 1;
  public readonly CONNECTION_STATUS_RECONNECTING = 2;
  public readonly CONNECTION_STATUS_DISCONNECTED = 4;

  private _connectionStatus : number|undefined;
  
  private optSignalRMessage = new Subject<any>();
  private pumpSignalRMessage = new Subject<any>();
  private fileLocationSignalRMessage = new Subject<any>();
  private advancedConfigSignalRMessage = new Subject<any>();
  private genericOptConfigSignalRMessage = new Subject<any>();
  private transactionSignalRMessage = new Subject<any>();
  private fuelPriceSignalRMessage = new Subject<any>();
  private connectionSignalRMessage = new Subject<any>();
  private connectionStatusChangeMessage = new Subject<number>();
  private infoMessageSignalRMessage = new Subject<any>();
  private divertDetailsSignalRMessage = new Subject<any>();
  private newMesaggeSignalRMessage = new Subject<any>();
  private aboutSignalRMessage = new Subject<any>();  
  private shiftEndSignalRMessage = new Subject<any>();
  private localAccountsSignalRMessage = new Subject<any>();  

  /**
   * The SignalRService constructor
   * @param logger The logger
   */
  constructor(private logger: NGXLogger) { }

  /**
   * The getter for the connection status
   */
  get connectionStatus(){
    return this._connectionStatus
  }

  /**
   * Starts the signalR connection
   */
  public startConnection() {
    var self = this;

    var url = Utils.getBaseURL(document);

    var connection = $.hubConnection(url);
    var serviceHubProxy = connection.createHubProxy('webHub');

    // Push received
    serviceHubProxy.on('pushChange', (eventType: any, itemId: string, additionalData: string) => {
      self.logger.debug(`pushChange - signalR message received (${eventType}) ${SignalREventType[(eventType)]} with arguments: `, itemId, additionalData);
      self.processPushedEvent(eventType, itemId, additionalData);
    });

    /**
     * Start the SignalR connection
     */
    connection.start().done(()=>{
      self._connectionStatus = self.CONNECTION_STATUS_CONNECTED;
      self.connectionStatusChangeMessage.next(self._connectionStatus);

      connection.stateChanged(async function(state){
        self.logger.debug('SignalR state changed from: ' + state.oldState + ' to: ' + state.newState);
  
        self._connectionStatus = state.newState;
        self.connectionStatusChangeMessage.next(self._connectionStatus);

        await new Promise( resolve => setTimeout(resolve, 10000) );
      });

      connection.error(function(error){
        self.logger.error('SignalR ' + error)
      })      
    });
  }

  /**
   * Processes a push event received via SignalR.
   * @param eventType The SignalR EventType
   * @param itemId (Optional) The item identifier
   * @param additionalData (Optional) The additional data received
   */
  processPushedEvent(eventType: SignalREventType, itemId: string = Constants.SignalRRefreshItemId, additionalData: string = ''): void {
    switch (eventType) {
      case SignalREventType.OPTChanged:
        this.optSignalRMessage.next(itemId);
        break;
      case SignalREventType.PumpChanged:
        this.pumpSignalRMessage.next(itemId);
        break;
      case SignalREventType.FileLocationsChanged:
        this.fileLocationSignalRMessage.next(itemId);
        break;
      case SignalREventType.AdvancedConfigChanged:
        this.advancedConfigSignalRMessage.next(itemId);
        break;
      case SignalREventType.GenericOptConfigChanged:
        this.genericOptConfigSignalRMessage.next(itemId);
        break;
      case SignalREventType.TransactionChanged:
        this.transactionSignalRMessage.next(itemId);
        break;
      case SignalREventType.FuelPriceChanged:
        this.fuelPriceSignalRMessage.next(itemId);
        break;
      case SignalREventType.ConnectionChanged:
        this.connectionSignalRMessage.next(itemId);
        break;
      case SignalREventType.InfoMessageChanged:
        this.infoMessageSignalRMessage.next(itemId);
        break;
      case SignalREventType.DivertDetailsChanged:
        this.divertDetailsSignalRMessage.next(itemId);
        break;
      case SignalREventType.NewMessageChanged:
        this.newMesaggeSignalRMessage.next({itemId: itemId, additionalData: additionalData});
        break;
      case SignalREventType.ShiftEndChanged:
        this.shiftEndSignalRMessage.next({itemId: itemId});
        break;
      case SignalREventType.AboutChanged:
        this.aboutSignalRMessage.next(itemId);
        break;
      case SignalREventType.LocalAccountsChanged:
        this.localAccountsSignalRMessage.next(itemId);
        break;
      case SignalREventType.TidsChanged, SignalREventType.DomsChanged:
          break;
      default:
        this.logger.warn('SignalR push notification received but not matched', eventType);
    }
  }

  /**
   * Gets the received Opt data via signalR
   */
  getOptSignalRMessage(): Observable<any> {
    return this.optSignalRMessage.asObservable();
  }

  /**
   * Gets the received Pump data via signalR
   */
  getPumpSignalRMessage(): Observable<any> {
    return this.pumpSignalRMessage.asObservable();
  }

  /**
   * Gets the received File Location data via signalR
   */
  getFileLocationSignalRMessage(): Observable<any> {
    return this.fileLocationSignalRMessage.asObservable();
  }

  /**
   * Gets the received Advanced Config data via signalR
   */
  getAdvancedConfigSignalRMessage(): Observable<any> {
    return this.advancedConfigSignalRMessage.asObservable();
  }

  /**
   * Gets the transctions data via signalR
   */
  getTransactionSignalRMessage(): Observable<any> {
    return this.transactionSignalRMessage.asObservable();
  }

  /**
   * Gets the received Generic OPT config data via signalR
   */
  getGenericOptConfigSignalRMessage(): Observable<any> {
    return this.genericOptConfigSignalRMessage.asObservable();
  }

  /**
   * Gets the received Fuel Price data via signalR
   */
  getFuelPriceSignalRMessage(): Observable<any> {
    return this.fuelPriceSignalRMessage.asObservable();
  }

  /**
   * Gets the received Divert Details data via signalR
   */
  getDivertDetailsSignalRMessage(): Observable<any> {
    return this.divertDetailsSignalRMessage.asObservable();
  }

  /**
   * Gets the received ConnectionDetails data via signalR
   */
  getConnectionSignalRMessage(): Observable<any> {
    return this.connectionSignalRMessage.asObservable();
  }
  
  /**
   * Gets the received Info Messages data via signalR
   */
  getInfoMessageSignalRMessage(): Observable<any> {
    return this.infoMessageSignalRMessage.asObservable();
  }

  /**
   * Gets the observable for changes in SignalR connection
   */
  getConnectionStatushChangeMessage(): Observable<number>{
    return this.connectionStatusChangeMessage.asObservable();
  }

  /**
   * Gets the received New Messages data via signalR
   */
   getNewMessageSignalRMessage(): Observable<any> {
    return this.newMesaggeSignalRMessage.asObservable();
  }

  /**
   * Gets the received New Messages data via signalR
   */
   getShiftEndSignalRMessage(): Observable<any> {
    return this.shiftEndSignalRMessage.asObservable();
  }

  /**
   * Gets the About details data via signalR
   */
  getAboutSignalRMessage(): Observable<any> {
    return this.aboutSignalRMessage.asObservable();
  }

  /**
   * Gets the Local Accounts data via signalR
   */
   getLocalAccountsSignalRMessage(): Observable<any> {
    return this.localAccountsSignalRMessage.asObservable();
  }
}
