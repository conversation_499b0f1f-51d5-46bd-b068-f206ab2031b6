using System.ComponentModel;

namespace Forecourt.Core.HydraDb.Enums
{
    /// <summary>
    /// Global enum for all (Payment) Timeout types, where [Descripton("defaultValue")]
    /// </summary>
    public enum PaymentTimeoutType
    {
        Opt = 0,
        Pod = 1,
        Mixed = 2,
        NozzleDown = 3,

        [Description("15")]
        Kiosk = 4,

        [Description("12")]
        SecAuth = 5
    }
}