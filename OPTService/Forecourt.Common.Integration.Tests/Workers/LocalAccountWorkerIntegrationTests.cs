using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System.Collections.Generic;
using Xunit;

namespace OPT.Common.Integration.Tests.Workers
{
    public class LocalAccountWorkerIntegrationTests
    {
        private const string CustRef1 = "Reference 1";
        private const string CustRef2 = "Reference 2";
        private const string CustName1 = "Name 1";
        private const string CustName2 = "Name 2";
        private const uint CustTransLimit1 = 100;
        private const uint CustTransLimit2 = 200;
        private const uint CustBalance1 = 1000;
        private const uint CustBalance2 = 2000;
        private const string CardPan1 = "Pan 1";
        private const string CardDesc1 = "Description 1";
        private const float CardDiscount1 = 10;
        private const string CardPan2 = "Pan 2";
        private const string CardDesc2 = "Description 2";
        private const float CardDiscount2 = 20;

        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _logger;
        private readonly ILocalAccountWorker _localAccountWorker;

        private readonly IList<LocalAccountCustomer> _addedCustomers = new List<LocalAccountCustomer>();
        private readonly IList<LocalAccountCustomer> _deletedCustomers = new List<LocalAccountCustomer>();
        private readonly IList<LocalAccountCard> _addedCards = new List<LocalAccountCard>();
        private readonly IList<LocalAccountCustomer> _addedCardCustomers = new List<LocalAccountCustomer>();
        private readonly IList<LocalAccountCard> _deletedCards = new List<LocalAccountCard>();

        public LocalAccountWorkerIntegrationTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            LocalAccountCustomer customer1 = new LocalAccountCustomer(CustRef1, CustName1, true, CustTransLimit1, true, true, true, true,
                true, true, true, false, false, CustBalance1, true);
            LocalAccountCustomer customer2 = new LocalAccountCustomer(CustRef2, CustName2, false, CustTransLimit2, true, false, true, false,
                true, false, false, true, false, CustBalance2, true);
            LocalAccountCard card1 = new LocalAccountCard(CardPan1, CardDesc1, CardDiscount1, true, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false);
            LocalAccountCard card2 = new LocalAccountCard(CardPan2, CardDesc2, CardDiscount2, false, true, false, true, false, true, false,
                true, false, true, false, true, false, true, false, true);
            customer1.SetCards(new List<LocalAccountCard> { card1, card2 });
            _hydraDb.FetchLocalAccountCustomers().Returns(new List<LocalAccountCustomer> { customer1, customer2 });
            _hydraDb.When(x => x.AddLocalAccountCustomer(Arg.Any<LocalAccountCustomer>()))
                .Do(x => _addedCustomers.Add((LocalAccountCustomer)x[0]));
            _hydraDb.When(x => x.DeleteLocalAccountCustomer(Arg.Any<LocalAccountCustomer>()))
                .Do(x => _deletedCustomers.Add((LocalAccountCustomer)x[0]));
            _hydraDb.When(x => x.AddLocalAccountCard(Arg.Any<LocalAccountCustomer>(), Arg.Any<LocalAccountCard>())).Do(x =>
            {
                _addedCardCustomers.Add((LocalAccountCustomer)x[0]);
                _addedCards.Add((LocalAccountCard)x[1]);
            });
            _hydraDb.When(x => x.DeleteLocalAccountCard(Arg.Any<LocalAccountCard>())).Do(x => _deletedCards.Add((LocalAccountCard)x[0]));
            _logger = Substitute.For<IHtecLogger>();
            _localAccountWorker = new LocalAccountWorker(_hydraDb, _logger);
        }

        [Fact]
        public void test_initial_state()
        {
            // Arrange

            // Act

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Name.Should().Be(CustName1);
                    item.TransactionsAllowed.Should().BeTrue();
                    item.TransactionLimit.Should().Be(CustTransLimit1);
                    item.Pin.Should().BeTrue();
                    item.PrintValue.Should().BeTrue();
                    item.AllowLoyalty.Should().BeTrue();
                    item.FuelOnly.Should().BeTrue();
                    item.RegistrationEntry.Should().BeTrue();
                    item.MileageEntry.Should().BeTrue();
                    item.PrePayAccount.Should().BeTrue();
                    item.LowCreditWarning.Should().BeFalse();
                    item.MaxCreditReached.Should().BeFalse();
                    item.Balance.Should().Be(CustBalance1);
                    item.CustomerExists.Should().BeTrue();
                }
                else
                {
                    item.Name.Should().Be(CustName2);
                    item.TransactionsAllowed.Should().BeFalse();
                    item.TransactionLimit.Should().Be(CustTransLimit2);
                    item.Pin.Should().BeTrue();
                    item.PrintValue.Should().BeFalse();
                    item.AllowLoyalty.Should().BeTrue();
                    item.FuelOnly.Should().BeFalse();
                    item.RegistrationEntry.Should().BeTrue();
                    item.MileageEntry.Should().BeFalse();
                    item.PrePayAccount.Should().BeFalse();
                    item.LowCreditWarning.Should().BeTrue();
                    item.MaxCreditReached.Should().BeFalse();
                    item.Balance.Should().Be(CustBalance2);
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_add_customer()
        {
            // Arrange
            const string custref = "Reference 3";
            const string custname = "Name 3";
            const uint custtranslimit = 300;

            // Act
            _localAccountWorker.Add(custref, custname, true, custtranslimit, false, false, false, false, false, true);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(3);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2, custref);
                if (string.Equals(item.CustomerReference, custref))
                {
                    item.Name.Should().Be(custname);
                    item.TransactionsAllowed.Should().BeTrue();
                    item.TransactionLimit.Should().Be(custtranslimit);
                    item.Pin.Should().BeTrue();
                    item.PrintValue.Should().BeFalse();
                    item.AllowLoyalty.Should().BeFalse();
                    item.FuelOnly.Should().BeFalse();
                    item.RegistrationEntry.Should().BeFalse();
                    item.MileageEntry.Should().BeFalse();
                    item.PrePayAccount.Should().BeFalse();
                    item.LowCreditWarning.Should().BeFalse();
                    item.MaxCreditReached.Should().BeTrue();
                    item.Balance.Should().Be(0);
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.CustomerReference.Should().Be(custref);
                item.Name.Should().Be(custname);
                item.TransactionsAllowed.Should().BeTrue();
                item.TransactionLimit.Should().Be(custtranslimit);
                item.Pin.Should().BeTrue();
                item.PrintValue.Should().BeFalse();
                item.AllowLoyalty.Should().BeFalse();
                item.FuelOnly.Should().BeFalse();
                item.RegistrationEntry.Should().BeFalse();
                item.MileageEntry.Should().BeFalse();
                item.PrePayAccount.Should().BeFalse();
                item.LowCreditWarning.Should().BeFalse();
                item.MaxCreditReached.Should().BeTrue();
                item.Balance.Should().Be(0);
                item.CustomerExists.Should().BeTrue();
            }

            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_modify_customer()
        {
            // Arrange
            const string custname = "Name 3";
            const uint custtranslimit = 300;

            // Act
            _localAccountWorker.Add(CustRef1, custname, false, custtranslimit, false, false, false, false, false, true);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Name.Should().Be(custname);
                    item.TransactionsAllowed.Should().BeFalse();
                    item.TransactionLimit.Should().Be(custtranslimit);
                    item.Pin.Should().BeTrue();
                    item.PrintValue.Should().BeTrue();
                    item.AllowLoyalty.Should().BeTrue();
                    item.FuelOnly.Should().BeFalse();
                    item.RegistrationEntry.Should().BeFalse();
                    item.MileageEntry.Should().BeFalse();
                    item.PrePayAccount.Should().BeFalse();
                    item.LowCreditWarning.Should().BeFalse();
                    item.MaxCreditReached.Should().BeTrue();
                    item.Balance.Should().Be(CustBalance1);
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(custname);
                item.TransactionsAllowed.Should().BeFalse();
                item.TransactionLimit.Should().Be(custtranslimit);
                item.Pin.Should().BeTrue();
                item.PrintValue.Should().BeTrue();
                item.AllowLoyalty.Should().BeTrue();
                item.FuelOnly.Should().BeFalse();
                item.RegistrationEntry.Should().BeFalse();
                item.MileageEntry.Should().BeFalse();
                item.PrePayAccount.Should().BeFalse();
                item.LowCreditWarning.Should().BeFalse();
                item.MaxCreditReached.Should().BeTrue();
                item.Balance.Should().Be(CustBalance1);
                item.CustomerExists.Should().BeTrue();
            }

            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_set_flags()
        {
            // Arrange

            // Act
            _localAccountWorker.SetFlags(CustRef1, false, false, false);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Name.Should().Be(CustName1);
                    item.TransactionsAllowed.Should().BeTrue();
                    item.TransactionLimit.Should().Be(CustTransLimit1);
                    item.Pin.Should().BeFalse();
                    item.PrintValue.Should().BeFalse();
                    item.AllowLoyalty.Should().BeFalse();
                    item.FuelOnly.Should().BeTrue();
                    item.RegistrationEntry.Should().BeTrue();
                    item.MileageEntry.Should().BeTrue();
                    item.PrePayAccount.Should().BeTrue();
                    item.LowCreditWarning.Should().BeFalse();
                    item.MaxCreditReached.Should().BeFalse();
                    item.Balance.Should().Be(CustBalance1);
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.TransactionsAllowed.Should().BeTrue();
                item.TransactionLimit.Should().Be(CustTransLimit1);
                item.Pin.Should().BeFalse();
                item.PrintValue.Should().BeFalse();
                item.AllowLoyalty.Should().BeFalse();
                item.FuelOnly.Should().BeTrue();
                item.RegistrationEntry.Should().BeTrue();
                item.MileageEntry.Should().BeTrue();
                item.PrePayAccount.Should().BeTrue();
                item.LowCreditWarning.Should().BeFalse();
                item.MaxCreditReached.Should().BeFalse();
                item.Balance.Should().Be(CustBalance1);
                item.CustomerExists.Should().BeTrue();
            }

            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_delete_customer_no_cards()
        {
            // Arrange

            // Act
            _localAccountWorker.Delete(CustRef2);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _deletedCustomers)
            {
                item.CustomerReference.Should().Be(CustRef2);
            }
        }

        [Fact]
        public void test_delete_customer_with_cards()
        {
            // Arrange

            // Act
            _localAccountWorker.Delete(CustRef1);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.CustomerExists.Should().BeFalse();
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _deletedCustomers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }
        }

        [Fact]
        public void test_delete_customer_no_customer()
        {
            // Arrange
            const string custref = "Reference 3";

            // Act
            _localAccountWorker.Delete(custref);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.CustomerExists.Should().BeTrue();
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.CustomerExists.Should().BeTrue();
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_stop_customer()
        {
            // Arrange

            // Act
            _localAccountWorker.Stop(CustRef1);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.TransactionsAllowed.Should().BeFalse();
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.TransactionsAllowed.Should().BeFalse();
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.TransactionsAllowed.Should().BeFalse();
                item.TransactionLimit.Should().Be(CustTransLimit1);
                item.Pin.Should().BeTrue();
                item.PrintValue.Should().BeTrue();
                item.AllowLoyalty.Should().BeTrue();
                item.FuelOnly.Should().BeTrue();
                item.RegistrationEntry.Should().BeTrue();
                item.MileageEntry.Should().BeTrue();
                item.PrePayAccount.Should().BeTrue();
                item.LowCreditWarning.Should().BeFalse();
                item.MaxCreditReached.Should().BeFalse();
                item.Balance.Should().Be(CustBalance1);
                item.CustomerExists.Should().BeTrue();
            }

            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_go_customer()
        {
            // Arrange

            // Act
            _localAccountWorker.Go(CustRef2);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.TransactionsAllowed.Should().BeTrue();
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.TransactionsAllowed.Should().BeTrue();
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName2);
                item.TransactionsAllowed.Should().BeTrue();
                item.TransactionLimit.Should().Be(CustTransLimit2);
                item.Pin.Should().BeTrue();
                item.PrintValue.Should().BeFalse();
                item.AllowLoyalty.Should().BeTrue();
                item.FuelOnly.Should().BeFalse();
                item.RegistrationEntry.Should().BeTrue();
                item.MileageEntry.Should().BeFalse();
                item.PrePayAccount.Should().BeFalse();
                item.LowCreditWarning.Should().BeTrue();
                item.MaxCreditReached.Should().BeFalse();
                item.Balance.Should().Be(CustBalance2);
                item.CustomerExists.Should().BeTrue();
            }

            _deletedCustomers.Should().BeEmpty();
        }

        [Fact]
        public void test_add_card_with_restrictions()
        {
            // Arrange
            const string cardpan = "Pan 3";
            const string carddesc = "Description 3";
            const float carddiscount = 30;

            // Act
            _localAccountWorker.AddCardWithRestrictions(cardpan, CustRef1, carddesc, carddiscount, true, false, true, false, true, false,
                true, false, true, false, true, false, true, false);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(3);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().BeOneOf(CardPan1, CardPan2, cardpan);
                        if (string.Equals(card.Pan, cardpan))
                        {
                            card.Description.Should().Be(carddesc);
                            card.Discount.Should().Be(carddiscount);
                            card.NoRestrictions.Should().BeFalse();
                            card.Unleaded.Should().BeTrue();
                            card.Diesel.Should().BeFalse();
                        }
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(cardpan);
                item.Description.Should().Be(carddesc);
                item.Discount.Should().Be(carddiscount);
                item.NoRestrictions.Should().BeFalse();
                item.Unleaded.Should().BeTrue();
                item.Diesel.Should().BeFalse();
            }

            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }

            _deletedCards.Should().BeEmpty();
        }

        [Fact]
        public void test_add_card_without_restrictions()
        {
            // Arrange
            const string cardpan = "Pan 3";
            const string carddesc = "Description 3";
            const float carddiscount = 30;

            // Act
            _localAccountWorker.AddCardWithoutRestrictions(cardpan, CustRef1, carddesc, carddiscount);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(3);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().BeOneOf(CardPan1, CardPan2, cardpan);
                        if (string.Equals(card.Pan, cardpan))
                        {
                            card.Description.Should().Be(carddesc);
                            card.Discount.Should().Be(carddiscount);
                            card.NoRestrictions.Should().BeTrue();
                        }
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(cardpan);
                item.Description.Should().Be(carddesc);
                item.Discount.Should().Be(carddiscount);
                item.NoRestrictions.Should().BeTrue();
            }

            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }

            _deletedCards.Should().BeEmpty();
        }

        [Fact]
        public void test_add_existing_card_retains_hot()
        {
            // Arrange
            const string carddesc = "Description 3";
            const float carddiscount = 30;

            // Act
            _localAccountWorker.AddCardWithoutRestrictions(CardPan2, CustRef2, carddesc, carddiscount);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan1);
                        card.Description.Should().Be(CardDesc1);
                        card.Discount.Should().Be(CardDiscount1);
                        card.NoRestrictions.Should().BeTrue();
                        card.Hot.Should().BeFalse();
                    }
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan2);
                        card.Description.Should().Be(carddesc);
                        card.Discount.Should().Be(carddiscount);
                        card.NoRestrictions.Should().BeTrue();
                        card.Hot.Should().BeTrue();
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(CardPan2);
                item.Description.Should().Be(carddesc);
                item.Discount.Should().Be(carddiscount);
                item.NoRestrictions.Should().BeTrue();
                item.Hot.Should().BeTrue();
            }

            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef2);
            }

            _deletedCards.Should().BeEmpty();
        }

        [Fact]
        public void test_add_existing_card_retains_not_hot()
        {
            // Arrange
            const string carddesc = "Description 3";
            const float carddiscount = 30;

            // Act
            _localAccountWorker.AddCardWithoutRestrictions(CardPan1, CustRef2, carddesc, carddiscount);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan2);
                        card.Description.Should().Be(CardDesc2);
                        card.Discount.Should().Be(CardDiscount2);
                        card.NoRestrictions.Should().BeFalse();
                        card.Hot.Should().BeTrue();
                    }
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan1);
                        card.Description.Should().Be(carddesc);
                        card.Discount.Should().Be(carddiscount);
                        card.NoRestrictions.Should().BeTrue();
                        card.Hot.Should().BeFalse();
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(CardPan1);
                item.Description.Should().Be(carddesc);
                item.Discount.Should().Be(carddiscount);
                item.NoRestrictions.Should().BeTrue();
                item.Hot.Should().BeFalse();
            }

            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef2);
            }

            _deletedCards.Should().BeEmpty();
        }

        [Fact]
        public void test_add_existing_card_to_new_customer()
        {
            // Arrange
            const string custref = "Reference 3";
            const string carddesc = "Description 3";
            const float carddiscount = 30;

            // Act
            _localAccountWorker.AddCardWithoutRestrictions(CardPan1, custref, carddesc, carddiscount);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(3);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2, custref);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan2);
                        card.Description.Should().Be(CardDesc2);
                        card.Discount.Should().Be(CardDiscount2);
                        card.NoRestrictions.Should().BeFalse();
                        card.Hot.Should().BeTrue();
                    }
                }
                else if (string.Equals(item.CustomerReference, CustRef2))
                {
                    item.Cards.Should().BeEmpty();
                }
                else if (string.Equals(item.CustomerReference, custref))
                {
                    item.Name.Should().BeEmpty();
                    item.TransactionsAllowed.Should().BeFalse();
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan1);
                        card.Description.Should().Be(carddesc);
                        card.Discount.Should().Be(carddiscount);
                        card.NoRestrictions.Should().BeTrue();
                        card.Hot.Should().BeFalse();
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(CardPan1);
                item.Description.Should().Be(carddesc);
                item.Discount.Should().Be(carddiscount);
                item.NoRestrictions.Should().BeTrue();
                item.Hot.Should().BeFalse();
            }

            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(custref);
            }

            _deletedCards.Should().BeEmpty();
        }

        [Fact]
        public void test_delete_card()
        {
            // Arrange

            // Act
            _localAccountWorker.DeleteCard(CardPan2);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(1);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().Be(CardPan1);
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().BeEmpty();
            _addedCardCustomers.Should().BeEmpty();
            _deletedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _deletedCards)
            {
                item.Pan.Should().Be(CardPan2);
            }
        }

        [Fact]
        public void test_hot_card()
        {
            // Arrange

            // Act
            _localAccountWorker.HotCard(CardPan1);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(2);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().BeOneOf(CardPan1, CardPan2);
                        if (string.Equals(card.Pan, CardPan1))
                        {
                            card.Hot.Should().BeTrue();
                        }
                        else if (string.Equals(card.Pan, CardPan2))
                        {
                            card.Hot.Should().BeTrue();
                        }
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(CardPan1);
                item.Hot.Should().BeTrue();
            }
            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }
            _deletedCards.Should().BeEmpty();
        }
        [Fact]
        public void test_ok_card()
        {
            // Arrange

            // Act
            _localAccountWorker.OkCard(CardPan2);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Cards.Should().HaveCount(2);
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        card.Pan.Should().BeOneOf(CardPan1, CardPan2);
                        if (string.Equals(card.Pan, CardPan1))
                        {
                            card.Hot.Should().BeFalse();
                        }
                        else if (string.Equals(card.Pan, CardPan2))
                        {
                            card.Hot.Should().BeFalse();
                        }
                    }
                }
            }

            _addedCustomers.Should().BeEmpty();
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().HaveCount(1);
            foreach (LocalAccountCard item in _addedCards)
            {
                item.Pan.Should().Be(CardPan2);
                item.Hot.Should().BeFalse();
            }
            _addedCardCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCardCustomers)
            {
                item.CustomerReference.Should().Be(CustRef1);
            }
            _deletedCards.Should().BeEmpty();
        }
        [Fact]
        public void test_balance()
        {
            // Arrange
            const uint balance = 1010;
            // Act
            _localAccountWorker.Balance(CustRef1, balance);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Balance.Should().Be(balance);
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.Balance.Should().Be(balance);
            }
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().BeEmpty();
            _addedCardCustomers.Should().BeEmpty();
            _deletedCards.Should().BeEmpty();
        }
        [Fact]
        public void test_top_up()
        {
            // Arrange
            const uint topup = 1010;
            // Act
            _localAccountWorker.TopUp(CustRef1, topup);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Balance.Should().Be(CustBalance1 + topup);
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.Balance.Should().Be(CustBalance1 + topup);
            }
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().BeEmpty();
            _addedCardCustomers.Should().BeEmpty();
            _deletedCards.Should().BeEmpty();
        }
        [Fact]
        public void test_reduce_balance()
        {
            // Arrange
            const uint reduction = 10;

            // Act
            _localAccountWorker.ReduceBalance(CardPan1, reduction);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Balance.Should().Be(CustBalance1 - reduction);
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.Balance.Should().Be(CustBalance1 - reduction);
            }
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().BeEmpty();
            _addedCardCustomers.Should().BeEmpty();
            _deletedCards.Should().BeEmpty();
        }
        [Fact]
        public void test_zero_balance()
        {
            // Arrange
            const uint reduction = 1010;

            // Act
            _localAccountWorker.ReduceBalance(CardPan1, reduction);

            // Assert
            _localAccountWorker.Customers.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in _localAccountWorker.Customers)
            {
                item.CustomerReference.Should().BeOneOf(CustRef1, CustRef2);
                if (string.Equals(item.CustomerReference, CustRef1))
                {
                    item.Balance.Should().Be(0);
                }
            }

            _addedCustomers.Should().HaveCount(1);
            foreach (LocalAccountCustomer item in _addedCustomers)
            {
                item.Name.Should().Be(CustName1);
                item.Balance.Should().Be(0);
            }
            _deletedCustomers.Should().BeEmpty();
            _addedCards.Should().BeEmpty();
            _addedCardCustomers.Should().BeEmpty();
            _deletedCards.Should().BeEmpty();
        }
    }
}
