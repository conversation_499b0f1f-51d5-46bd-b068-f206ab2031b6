using Forecourt.Core.Enums;
using Htec.DapperWrapper;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Workers;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.IO.Abstractions;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using JavaProperties = Htec.Hydra.Core.PaymentConfiguration.JavaProperties;

namespace Forecourt.PaymentConfiguration.ESocketDbClasses
{
    public class EsocketDb : Workerable,  IEsocketDb
    {
        private const string EncUserNameProperty = "postilion.esocketpos.encrypted.JdbcUser";
        private const string EncPasswordProperty = "postilion.esocketpos.encrypted.JdbcUserPassword";

        private const string DbUrlProperty = "postilion.esocketpos.JdbcUrl";
        private const string KeystoreLocationProperty = "postilion.esocketpos.install.KeyStore";
        private const string ContactlessLocationProperty = "postilion.esocketpos.ContactlessConfigurationFile";

        private const string DefaultPropertiesFileLocation = "C:\\postilion\\eSocket.POS\\properties.txt";
        private const string DefaultKeystoreFileLocation = "C:\\postilion\\eSocket.POS\\keystore\\esp.ks";
        private const string DefaultContactlessFileLocation = "C:\\postilion\\eSocket.POS\\contactless.properties";
        private const string DefaultDbUrl = "********************************";
        private const int MerchantCategoryCodeForOpt = 5542;

        private const int TimestampLength = 14;

        private const int InitialQueryTimeout = 10;

        private static readonly byte[] IvBytes = {0, 0, 0, 0, 0, 0, 0, 0};
        public string ConnectionString { get; private set; }
        public bool UseConnectionString { get; private set; }
        public string ConfigFile { get; private set; }
        public string CurrentConfigFile => OverrideProperties ? ConfigFile : DefaultPropertiesFileLocation;
        public string KeystoreFile { get; private set; }
        public string ContactlessFile { get; private set; }

        public string CurrentKeystoreFile => OverrideKeystore ? KeystoreFile :
            string.IsNullOrEmpty(_keystoreFileProperty) ? DefaultKeystoreFileLocation : _keystoreFileProperty;

        public string CurrentContactlessFile => UseConnectionString || OverrideContactless ? ContactlessFile :
            string.IsNullOrEmpty(_contactlessFileProperty) ? DefaultContactlessFileLocation : _contactlessFileProperty;

        public string DbUrl { get; private set; }
        public bool OverrideProperties { get; private set; }
        public bool OverrideKeystore { get; private set; }
        public bool OverrideUrl { get; private set; }
        public bool OverrideContactless { get; private set; }
        public bool ConnectionMade { get; private set; }
        private string _keystoreFileProperty;
        private string _contactlessFileProperty;
        private string _configConnectionString;
        private string _serviceDirectory = ".";
        private readonly Forecourt.PaymentConfiguration.Workers.Interfaces.ITelemetryWorker _telemetryWorker;
        private readonly IFileSystem _fileSystem;
        private readonly ICustomDataFields _customDataFields;
        private INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();
        private IDbExecutorFactory _esDbExecutorFactory;
        private string _jdbcConnectionString;
        private bool _registerDone;
        private string _prevEncUserName;
        private string _prevEncPassword;
        private string _prevTimestamp;
        private string _decryptedUserName;
        private string _decryptedPassword;
        private bool _decryptSuccess;
        private string _myConnectionString;
        public TermProcCategory TermProcCategory { get; private set; } = new TermProcCategory("", "", "");
        public IList<CardAid> CardAids { get; private set; } = new List<CardAid>();
        public IList<CardCapk> CardCapks { get; private set; } = new List<CardCapk>();
        public IList<FuelCard> FuelCards { get; private set; } = new List<FuelCard>();
        public IList<TermId> AllPumpTids { get; private set; } = new List<TermId>();
        public string TermCapabilities { get; private set; }
        public string TermAddCapabilities { get; private set; }

        private bool _jdbcRunning = false;
        private readonly object _lockObject = new object();

        private static bool _firstTime = true;

        /// <inheritdoc/>
        public EsocketDb(IHtecLogManager logMan, Workers.Interfaces.ITelemetryWorker telemetryWorker, IFileSystem fileSystem, ICustomDataFields customDataFields) : base(logMan, nameof(EsocketDb))
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _telemetryWorker = telemetryWorker ?? throw new ArgumentNullException(nameof(telemetryWorker));
            _esDbExecutorFactory = null;
            _jdbcConnectionString = null;
            _registerDone = false;

            _customDataFields = customDataFields ?? throw new ArgumentNullException(nameof(customDataFields));
        }

        [Obsolete("Internal method to allow injection for unit tests. DO NOT USE IN THE APPLICATION")]
        internal void SetDbExecutor(IDbExecutorFactory dbExecutorFactory)
        {
            _esDbExecutorFactory = dbExecutorFactory;
        }

        public void SetServiceDirectory(string serviceDirectory)
        {
            _serviceDirectory = serviceDirectory;
            MakeConnection();
        }

        private bool FetchKey(string timestamp, out byte[] bytes)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            bytes = new byte[0];
            if (string.IsNullOrWhiteSpace(CurrentKeystoreFile) || !_fileSystem.File.Exists(CurrentKeystoreFile))
            {
                ControllerWorker.SendInformation($"eSocket.POS failed to find keystore file {CurrentKeystoreFile}");
                GetLogger().Warn($"Failed to find Keystore File {CurrentKeystoreFile}");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            GetLogger().Info($"Fetching key with timestamp {timestamp}");
            string javaCommand =
                $"java -cp \"{_serviceDirectory}{_fileSystem.Path.DirectorySeparatorChar}Java\";\"{_serviceDirectory}{_fileSystem.Path.DirectorySeparatorChar}Java{_fileSystem.Path.DirectorySeparatorChar}bcprov-jdk15on-159.jar\" EspKey {timestamp} \"{CurrentKeystoreFile}\"";
            GetLogger().Info($"Java command is {javaCommand}");
            try
            {
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe", $"/c {javaCommand}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true
                };
                GetLogger().Info("Process ready to start");
                Process process = Process.Start(processInfo);
                if (process != null)
                {
                    GetLogger().Info("Process Started");
                    StreamReader reader = process.StandardOutput;
                    string outputString = reader.ReadToEnd().Trim();
                    StreamReader errorReader = process.StandardError;
                    string errorString = errorReader.ReadToEnd();
                    if (!string.IsNullOrWhiteSpace(errorString))
                    {
                        GetLogger().Info($"Process error is {errorString}");
                    }

                    if (string.IsNullOrWhiteSpace(outputString))
                    {
                        ControllerWorker.SendInformation(
                            $"eSocket.POS failed to fetch key {timestamp} from keystore file {CurrentKeystoreFile}");
                        GetLogger().Warn("No key returned");
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return false;
                    }

                    process.WaitForExit();
                    GetLogger().Info("Process finished");
                    bool success = ConvertHex(outputString, out bytes);
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return success;
                }
                else
                {
                    GetLogger().Debug("Process is null");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return false;
                }
            }
            catch (Exception e)
            {
                ControllerWorker.SendInformation($"eSocket.POS failed to fetch key {timestamp} from keystore file {CurrentKeystoreFile}");
                GetLogger().Error("Error reading output", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
        }

        private bool FetchProperties(out string encUserName, out string encPassword, out string timestamp, out string dbUrl)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            encUserName = string.Empty;
            encPassword = string.Empty;
            timestamp = string.Empty;
            dbUrl = string.Empty;
            _keystoreFileProperty = string.Empty;
            if (string.IsNullOrWhiteSpace(CurrentConfigFile) || !_fileSystem.File.Exists(CurrentConfigFile))
            {
                ControllerWorker?.SendInformation($"eSocket.POS failed to find properties file {CurrentConfigFile}");
                GetLogger().Warn($"Failed to find Properties File {CurrentConfigFile}");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            ControllerWorker?.SendInformation($"eSocket.POS reading properties from {CurrentConfigFile}");
            JavaProperties.JavaProperties props;
            try
            {
                string body = _fileSystem.File.ReadAllText(CurrentConfigFile);
                props = JavaProperties.JavaProperties.Read(body, Logger);
            }
            catch (ArgumentException e)
            {
                ControllerWorker?.SendInformation($"eSocket.POS error fetching properties from file {CurrentConfigFile}");
                GetLogger().Error("Argument exception fetching eSocket.POS properties", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
            catch (Exception e)
            {
                ControllerWorker?.SendInformation($"eSocket.POS error fetching properties from file {CurrentConfigFile}");
                GetLogger().Error("Exception fetching eSocket.POS properties", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            try
            {
                dbUrl = props.Contains(DbUrlProperty) ? props[DbUrlProperty].Value ?? string.Empty : string.Empty;
                _keystoreFileProperty = props.Contains(KeystoreLocationProperty)
                    ? props[KeystoreLocationProperty].Value ?? string.Empty
                    : string.Empty;
                _contactlessFileProperty = props.Contains(ContactlessLocationProperty)
                    ? props[ContactlessLocationProperty].Value ?? string.Empty
                    : string.Empty;

                if (!props.Contains(EncUserNameProperty) || !props.Contains(EncPasswordProperty))
                {
                    ControllerWorker?.SendInformation($"eSocket.POS missing properties from file {CurrentConfigFile}");
                    GetLogger().Warn("Missing eSocket.POS properties");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return false;
                }

                string encUserProperty = props[EncUserNameProperty].Value ?? string.Empty;
                string encPassProperty = props[EncPasswordProperty].Value ?? string.Empty;
                if (encUserProperty.Length < TimestampLength || encPassProperty.Length < TimestampLength)
                {
                    ControllerWorker?.SendInformation($"eSocket.POS missing timestamp in properties file {CurrentConfigFile}");
                    GetLogger().Warn("Missing eSocket.POS property timestamp");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return false;
                }

                if (!string.Equals(encUserProperty.Substring(0, TimestampLength), encPassProperty.Substring(0, TimestampLength)))
                {
                    ControllerWorker?.SendInformation($"eSocket.POS mismatched timestamps in properties file {CurrentConfigFile}");
                    GetLogger().Warn("Mismatched eSocket.POS property timestamp");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return false;
                }

                timestamp = encUserProperty.Substring(0, TimestampLength);
                encUserName = encUserProperty.Substring(TimestampLength);
                encPassword = encPassProperty.Substring(TimestampLength);
            }
            catch (Exception e)
            {
                ControllerWorker?.SendInformation($"eSocket.POS error fetching properties from file {CurrentConfigFile}");
                GetLogger().Error("Exception fetching eSocket.POS properties", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            GetLogger().Info($"Timestamp is {timestamp}, encrypted user name is {encUserName}, encrypted password is {encPassword}," +
                         $" DB URL is {dbUrl}, keystore location is {_keystoreFileProperty}, contactless location is {_contactlessFileProperty}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return true;
        }

        private bool ConvertHex(string hexString, out byte[] bytes)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            bytes = new byte[0];
            if (hexString.Length % 2 != 0)
            {
                GetLogger().Warn($"Invalid Hex String {hexString}");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            bytes = new byte[hexString.Length / 2];
            for (int i = 0; i < hexString.Length / 2; i++)
            {
                try
                {
                    bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
                }
                catch (Exception e)
                {
                    GetLogger().Error($"Invalid Hex String {hexString.Substring(i * 2, 2)}, full String {hexString}", e);
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return false;
                }
            }


            return true;
        }

        private bool Decrypt(byte[] encryptedBytes, byte[] keyBytes, out string decryptedString)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            decryptedString = string.Empty;
            try
            {
                byte[] bytes = new byte[encryptedBytes.Length];
                using (MemoryStream mem = new MemoryStream(encryptedBytes))
                {
                    using (TripleDESCryptoServiceProvider prov = new TripleDESCryptoServiceProvider())
                    {
                        using (ICryptoTransform transform = prov.CreateDecryptor(keyBytes, IvBytes))
                        {
                            CryptoStream crypt = new CryptoStream(mem, transform, CryptoStreamMode.Read);
                            crypt.Read(bytes, 0, bytes.Length);
                            crypt.Close();
                        }
                    }

                    mem.Close();
                }

                int newLength = bytes.Length;
                for (int i = bytes.Length - 1; i >= 0; i--)
                {
                    if (bytes[i] == 0)
                    {
                        newLength = i;
                    }
                }

                decryptedString = new ASCIIEncoding().GetString(bytes).Substring(0, newLength);

                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return true;
            }
            catch (Exception e)
            {
                GetLogger().Error("Decryption error", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
        }

        /*
        private bool Encrypt(string inputString, byte[] keyBytes, out byte[] encryptedBytes)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            encryptedBytes = new byte[0];
            try
            {
                MemoryStream mem = new MemoryStream();
                CryptoStream crypt = new CryptoStream(mem, new TripleDESCryptoServiceProvider().CreateEncryptor(keyBytes, IvBytes),
                    CryptoStreamMode.Write);
                byte[] inputBytes = new ASCIIEncoding().GetBytes(inputString);
                crypt.Write(inputBytes, 0, inputBytes.Length);
                crypt.FlushFinalBlock();
                encryptedBytes = mem.ToArray();
                crypt.Close();
                mem.Close();
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return true;
            }
            catch (Exception e)
            {
                GetLogger().Error("Encryption error", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
        }
        */

        private string MakeConnectionString()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (UseConnectionString)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return ConnectionString;
            }

            bool success = FetchProperties(out string encUserName, out string encPassword, out string timestamp, out string dbUrlProperty);
            if (!success)
            {
                GetLogger().Warn("No properties fetched");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return _configConnectionString ?? ConnectionString;
            }

            if (string.Equals(encUserName, _prevEncUserName) && string.Equals(encPassword, _prevEncPassword) &&
                string.Equals(timestamp, _prevTimestamp) && _decryptSuccess)
            {
                GetLogger().Info("Using previous decryption results");
            }
            else
            {
                _prevEncUserName = encUserName;
                _prevEncPassword = encPassword;
                _prevTimestamp = timestamp;

                _decryptSuccess = FetchKey(timestamp, out byte[] keyBytes) && ConvertHex(encUserName, out byte[] encUserNameBytes) &&
                                  ConvertHex(encPassword, out byte[] encPasswordBytes) &&
                                  Decrypt(encUserNameBytes, keyBytes, out _decryptedUserName) &&
                                  Decrypt(encPasswordBytes, keyBytes, out _decryptedPassword);

                if (!_decryptSuccess)
                {
                    ControllerWorker?.SendInformation("eSocket.POS decryption failed");
                    GetLogger().Warn("No encryption fetched");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return _configConnectionString ?? ConnectionString;
                }
            }

            string dbUrl = OverrideUrl ? DbUrl : string.IsNullOrWhiteSpace(dbUrlProperty) ? DefaultDbUrl : dbUrlProperty;

            if (string.IsNullOrWhiteSpace(dbUrl))
            {
                ControllerWorker?.SendInformation("eSocket.POS no DB URL found");
                GetLogger().Warn("Invalid database URL");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return ConnectionString;
            }

            if (string.IsNullOrWhiteSpace(_decryptedUserName))
            {
                _configConnectionString = $"{dbUrl}";
            }
            else if (string.IsNullOrWhiteSpace(_decryptedPassword))
            {
                _configConnectionString = $"{dbUrl};user={_decryptedUserName};password=;";
            }
            else
            {
                _configConnectionString = $"{dbUrl};user={_decryptedUserName};password={_decryptedPassword};";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);

            return _configConnectionString;
        }

        public void MakeConnection()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            string myConnectionString = MakeConnectionString();
            if (myConnectionString == null || (string.Equals(myConnectionString, _myConnectionString) && ConnectionMade))
            {
                GetLogger().Info("Using previous connection string");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            _myConnectionString = myConnectionString;

            ConnectionMade = false;

            string smallerConnectionString = myConnectionString.ToLower().Contains(";user")
                ? myConnectionString.Substring(0, myConnectionString.ToLower().IndexOf(";user"))
                : myConnectionString;

            ControllerWorker?.SendInformation($"eSocket.POS making new connection with {smallerConnectionString}");
            GetLogger().Info($"Making connection with connection string {smallerConnectionString}");

            if (myConnectionString.StartsWith("jdbc"))
            {
                _jdbcConnectionString = myConnectionString;
                _esDbExecutorFactory = null;
                if (!_registerDone)
                {
                    try
                    {
                        java.sql.DriverManager.registerDriver(new org.hsqldb.jdbcDriver());
                        _registerDone = true;
                    }
                    catch (Exception exception)
                    {
                        GetLogger().Warn("Exception registering HSQLDB Driver", exception);
                    }

                    try
                    {
                        java.sql.DriverManager.registerDriver(new net.sourceforge.jtds.jdbc.Driver());
                        _registerDone = true;
                    }
                    catch (Exception exception)
                    {
                        GetLogger().Warn("Exception registering JTDS Driver", exception);
                    }
                }
            }
            else
            {
                _jdbcConnectionString = null;
                _esDbExecutorFactory = new SqlExecutorFactory(myConnectionString);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetConnectionString(string newConnectionString, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received new connection string {newConnectionString ?? "Null string"}");
            if (newConnectionString == null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            ConnectionString = newConnectionString;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetUseConnectionString(bool useConnectionString, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received use {(useConnectionString ? "connection string" : "config file")}");

            UseConnectionString = useConnectionString;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetConfigFile(string fileName, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received new config file {fileName ?? "Null string"}");
            if (fileName == null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            ConfigFile = fileName;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetKeystoreFile(string fileName, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received new config file {fileName ?? "Null string"}");
            if (fileName == null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            KeystoreFile = fileName;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetDbUrl(string url, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received new database URL {url ?? "Null string"}");
            if (url == null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            DbUrl = url;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetContactlessFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received new contactless properties {fileName ?? "Null string"}");
            if (fileName == null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            ContactlessFile = fileName;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetOverrideProperties(bool flag, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received {(flag ? "" : "do not ")}override properties file location");
            OverrideProperties = flag;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetOverrideKeystore(bool flag, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received {(flag ? "" : "do not ")}override keystore file location");
            OverrideKeystore = flag;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetOverrideUrl(bool flag, bool connect = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received {(flag ? "" : "do not ")}override database URL");
            OverrideUrl = flag;
            if (connect)
            {
                MakeConnection();
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetOverrideContactless(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Received {(flag ? "" : "do not ")}override contactless properties");
            OverrideContactless = flag;

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        internal static string GetCardAidString(string procCategory, string timestamp)
        {
            return "select aid as aid, app_ver_nr as appVerTerm, term_act_code_default as tacDefault, term_act_code_denial as tacDenial," +
                   " term_act_code_online as tacOnline, partial_match as partialMatch, tdol as tdol, ddol as ddol," +
                   " floor_limit_chip as floorLimit, target_percent as emvTarget, max_target_percent as emvMaxTarget," +
                   " threshold_value as emvThreshold from esp_emv_aid" +
                   $" where proc_category = '{procCategory}' and timestamp = '{timestamp}'";
        }

        private void GetCardAids()
        {
            DoAction(() =>
            {
                var queryString = GetCardAidString(TermProcCategory.ProcCategory, TermProcCategory.Timestamp);

                CardAids = DoExec(queryString, Guid.NewGuid(), x => new CardAid(x.getString("aid"), x.getString("appVerTerm"),
                    x.getString("tacDefault"), x.getString("tacDenial"), x.getString("tacOnline"),
                    x.getString("partialMatch"), x.getString("tdol"), x.getString("ddol"),
                    x.getString("floorLimit"), x.getString("emvTarget"), x.getString("emvMaxTarget"),
                    x.getString("emvThreshold")), "fetching Card AIDs").ToList();

                if (CardAids.Any())
                {
                    DoDeferredLogging(LogLevel.Info, string.Empty, () => new[] { $"[{string.Join(", ", CardAids.Select(x => x.Aid))}]" });
                }
            }, string.Empty);
        }

        internal string GetCardCapksString(string procCategory, string timeStamp)
        {
            return "select rid as rid, key_index as theIndex, modulus as modulus, exponent as exponent, check_sum as checksum," +
                   " expiration_date as expiryDate from esp_ca_public_key where" +
                   $" proc_category = '{procCategory}' and timestamp = '{timeStamp}'";
        }

        private void GetCardCapks()
        {
            DoAction(() =>
            {
                var queryString = GetCardCapksString(TermProcCategory.ProcCategory, TermProcCategory.Timestamp);

                CardCapks = DoExec(queryString, Guid.NewGuid(), x => new CardCapk(x.getString("rid"), x.getString("theIndex"),
                    x.getString("modulus"), x.getString("exponent"), x.getString("checksum"),
                    x.getString("expiryDate")), "fetching Card CAPKs").ToList();

                if (CardCapks.Any())
                {
                    DoDeferredLogging(LogLevel.Info, string.Empty, () => new[] { $"[{string.Join(", ", CardCapks.Select(x => $"{x.Rid}/{x.TheIndex}"))}]" });                   
                }
            }, string.Empty);
        }

        internal static string GetFuelCardsString()
        {
            const string queryString = "select bin_start as iinStart, bin_end as iinEnd, '0' as onlinePin from esp_fuel_bins";
            return queryString;
        }

        private void GetFuelCards()
        {
            DoAction(() =>
            {
                var queryString = GetFuelCardsString();

                FuelCards = DoExec(queryString, Guid.NewGuid(), x => new FuelCard(x.getString("iinStart"), x.getString("iinEnd")), " fetching Fuel Cards").ToList();

                if (FuelCards.Any())
                {
                    DoDeferredLogging(LogLevel.Info, string.Empty, () => new[] { $"[{string.Join(", ", FuelCards.Select(x => $"{x.IinStart}/{x.IinEnd}"))}]" });                  
                }
            }, string.Empty);
        }

        private static string GetAllPumpTidsString(int cycleTranNum, int defaultTranNum, int merchantCategoryCodeForOpt)
        {
            return "SELECT " +
                   "   terms.term_id as tid, " +
                   "   (CASE " +
                   $"      WHEN COALESCE(CAST(trMin.sys_trace AS INT), 0) + COALESCE(CAST(trMax.sys_trace as int), 0) >= {cycleTranNum} THEN " +
                   $"        (select max(sys_trace) from esp_trans where sys_trace < '{cycleTranNum / 2}' and term_id = terms.term_id) " +
                   $"      ELSE COALESCE(terms.sys_trace_max, '{defaultTranNum}') " +
                   "   END) AS transactionNumber " +
                   "FROM " +
                   "(SELECT cfg.term_id, min(tr.tran_nr) as tran_nr_min, max(tr.tran_nr) as tran_nr_max, min(tr.sys_trace) as sys_trace_min, max(sys_trace) as sys_trace_max " +
                   "  FROM esp_term_config cfg " +
                   "   INNER JOIN " +
                   "       (SELECT term_category " +
                   "           FROM esp_merchant mc " +
                   $"           WHERE mc.timestamp = (SELECT MAX(timestamp) AS timestamp FROM esp_merchant WHERE merchant_type = {merchantCategoryCodeForOpt}) " +
                   $"             AND mc.merchant_type = {merchantCategoryCodeForOpt}" +
                   "       ) mcc ON mcc.term_category = cfg.term_category " +
                   "    LEFT OUTER JOIN esp_trans tr on tr.term_id = cfg.term_id and tr.sys_trace != '000000' group by cfg.term_id) terms " +
                   "LEFT OUTER JOIN esp_trans trMin on trMin.term_id = terms.term_id and trMin.TRAN_NR = terms.TRAN_NR_MIN " +
                   "LEFT OUTER JOIN esp_trans trMax on trMax.term_id = terms.term_id and trMax.TRAN_NR = terms.TRAN_NR_MAX";
        }

        public void GetAllPumpTids(string loggingReference)
        {
            DoAction(() =>
            {
                const int defaultTranNum = 100000;
                const int cycleTranNum = 1000000;

                var queryString = GetAllPumpTidsString(cycleTranNum, defaultTranNum, MerchantCategoryCodeForOpt);

                AllPumpTids = DoExec(queryString, Guid.NewGuid(), x => new TermId(x.getString("tid"), x.getString("transactionNumber")), "Pump TIDs").ToList();

                if (AllPumpTids.Any())
                {
                    DoDeferredLogging(LogLevel.Info, string.Empty, () => new[] { $"[{string.Join(", ", AllPumpTids.Select(x => $"{x.Tid}/{x.TransactionNumber}"))}]" });
                }
            }, loggingReference);
        }

        public bool CheckTermProcCategory(string loggingReference)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            TermProcCategory category = null;
            var queryString = "select distinct timestamp as timestamp, term_category as termCategory, proc_category as procCategory" +
                                 " from esp_term_config where term_category in" +
                                 $" (select term_category from esp_merchant where merchant_type = '{MerchantCategoryCodeForOpt}')" +
                                 " and timestamp = (select max(timestamp) from esp_term_config where" +
                                 $" term_category in (select term_category from esp_merchant where merchant_type = '{MerchantCategoryCodeForOpt}'))";
            Guid guid = Guid.NewGuid();
            _telemetryWorker.QuerySentToEsocketDb(guid);
            if (_esDbExecutorFactory != null)
            {
                // if this is the first time running this, then loop until we have an eSocket connection
                var eSocketConnected = false;
                var i = 0;
                do
                {
                    try
                    {
                        using (IDbExecutor db = _esDbExecutorFactory.CreateExecutor())
                        {
                            IEnumerable<TermProcCategory> categories = db.Query<TermProcCategory>(queryString, commandType: CommandType.Text,
                                commandTimeout: InitialQueryTimeout).ToList();
                            category = categories.Single();
                        }

                        eSocketConnected = true;
                    }
                    catch (Exception e)
                    {
                        ControllerWorker?.SendInformation("eSocket.POS SQL Server connection failed");
                        GetLogger().Error("Exception fetching Term Proc Category from eSocket.POS DB (SQL Server)", e);
                        if (_firstTime) Thread.Sleep(100);
                        i++;
                    }
                } while (_firstTime && !eSocketConnected && i < 2);

                if (eSocketConnected)
                {
                    _firstTime = true;
                }
            }
            else if (_jdbcConnectionString != null)
            {
                bool run = false;
                lock (_lockObject)
                {
                    if (_jdbcRunning)
                    {
                        ControllerWorker?.SendInformation("eSocket.POS JDBC connection already running");
                        GetLogger().Warn("JDBC already running");
                    }
                    else
                    {
                        _jdbcRunning = true;
                        run = true;
                    }
                }

                if (run)
                {
                    try
                    {
                        using (java.sql.Connection conn = java.sql.DriverManager.getConnection(_jdbcConnectionString))
                        {
                            java.sql.PreparedStatement sql = conn.prepareStatement(queryString);
                            sql.setQueryTimeout(InitialQueryTimeout);
                            using (java.sql.ResultSet results = sql.executeQuery())
                            {
                                if (results.next())
                                {
                                    category = new TermProcCategory(results.getString("timestamp"), results.getString("termCategory"),
                                        results.getString("procCategory"));
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        ControllerWorker?.SendInformation("eSocket.POS JDBC connection failed");
                        GetLogger().Error("Exception fetching Term Proc Category from eSocket.POS DB (JDBC)");
                        GetLogger().Error($"Exception is {e}");
                    }

                    lock (_lockObject)
                    {
                        _jdbcRunning = false;
                    }
                }
            }

            _telemetryWorker.QueryReturnedFromEsocketDb("FetchTermProcCategory", guid);

            if (category == null)
            {
                ControllerWorker?.SendInformation("eSocket.POS Term Proc Category not fetched");
                GetLogger().Warn("Could not fetch Term Proc Category from database");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            DoDeferredLogging(LogLevel.Info, string.Empty, () => new[] { $"TermCategory: {category.TermCategory}; ProcCategory: {category.ProcCategory}; Timestamp: {category.Timestamp}"});
            ConnectionMade = true;
            if (category.Equals(TermProcCategory))
            {
                ControllerWorker?.SendInformation("eSocket.POS Term Proc Category unchanged");
                GetLogger().Info("Term Proc Category unchanged");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }

            ControllerWorker?.SendInformation("eSocket.POS Term Proc Category changed, fetching data");
            TermProcCategory = category;

            GetCardAids();
            GetCardCapks();
            GetFuelCards();
            GetAllPumpTids(loggingReference);
            GetCapabilities();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return true;
        }

        internal string GetCapabilitiesString(int merchantType)
        {
            return "SELECT term_capabilities as termCapabilities, " +
                   "add_term_cap as termAddCapabilities " +
                   "FROM esp_pos_config pc " +
                   "INNER JOIN esp_merchant m ON pc.term_category = m.term_category " +
                   $"WHERE m.merchant_type = {merchantType}";
        }

        internal void GetCapabilities()
        {
            DoAction(() =>
            {
                var queryString = GetCapabilitiesString(MerchantCategoryCodeForOpt);

                var results = DoExec(queryString, Guid.NewGuid(), x => new Capabilities(x.getString("termCapabilities"), x.getString("termAddCapabilities")), "fetching capabilities");

                var result = results.FirstOrDefault();

                if (result == null)
                {
                    GetLogger().Warn(HeaderInformation, () => new[] { "Failed to return any capability configuration." });
                    return;
                }

                TermCapabilities = result.TermCapabilities;
                TermAddCapabilities = result.TermAddCapabilities;

                DoDeferredLogging(LogLevel.Info, nameof(TermCapabilities), () => new[] { $"{TermCapabilities}; {nameof(TermAddCapabilities)}: {TermAddCapabilities}" });
            }, string.Empty);
        }

        private IEnumerable<T> DoExec<T>(string queryString, Guid guid, Func<java.sql.ResultSet, T> func, string action)
        {
            _telemetryWorker.QuerySentToEsocketDb(guid);

            var resultList = new List<T>();

            if (_esDbExecutorFactory != null)
            {
                try
                {
                    using (var db = _esDbExecutorFactory.CreateExecutor())
                    {
                        resultList = db.Query<T>(queryString, commandType: CommandType.Text).ToList();
                    }
                }
                catch (Exception exception)
                {
                    GetLogger().Error($"Exception {action} from eSocket.POS DB (SQL Server)", exception);
                }
            }
            else if (_jdbcConnectionString != null)
            {
                try
                {
                    using (java.sql.Connection conn = java.sql.DriverManager.getConnection(_jdbcConnectionString))
                    {
                        java.sql.PreparedStatement sql = conn.prepareStatement(queryString);
                        using (java.sql.ResultSet results = sql.executeQuery())
                        {
                            while (results.next())
                            {
                                resultList.Add(func(results));
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    GetLogger().Error($"Exception {action} from eSocket.POS DB (HSQLDB)", exception);
                }
            }

            _telemetryWorker.QueryReturnedFromEsocketDb("FetchFuelCards", guid);

            return resultList;
        }

        internal string GetContactlessPropertiesString(string procCategory, string timestamp)
        {
            return "select aid as aid, app_ver_nr as appVerTerm, term_act_code_default_ctls as tacDefault, term_act_code_denial_ctls as tacDenial," +
                   " term_act_code_online_ctls as tacOnline," +
                   " floor_limit_ctls as floorLimit, tran_limit_ctls as transLimit, cvm_required_limit_ctls as cvmLimit, tran_limit_ctls_dcv as odcvmLimit," +
                   " custom_data as customData from esp_emv_aid" +
                   $" where proc_category = '{procCategory}' and timestamp = '{timestamp}'";
        }

        public void FetchContactlessProperties(IList<CardClessAid> cards, IList<CardClessDrl> drls, string loggingReference)
        {
            DoAction(() =>
            {
                if (TermProcCategory == null)
                {
                    GetLogger().Error("Term Proc Category was null");
                    GetLogger().Debug(HeaderEnd);
                    return;
                }

                var aidQueryString = GetContactlessPropertiesString(TermProcCategory.ProcCategory, TermProcCategory.Timestamp);

                var aids = DoExec(aidQueryString, Guid.NewGuid(), x => new DbCardClessAid(x.getString("aid"), x.getString("appVerTerm"),
                    x.getString("tacDefault"), x.getString("tacDenial"), x.getString("tacOnline"),
                    x.getString("floorLimit"), x.getString("transLimit"), x.getString("cvmLimit"),
                    x.getString("odcvmLimit"), x.getString("customData")), "fetching Contactless Properties").ToList();

                LogAids(aids);

                cards.Clear();
                drls.Clear();

                foreach (var aid in aids.Where(x => !string.IsNullOrWhiteSpace(x.CvmLimit))) // A contactless AID must have a CVM limit value, so we don't include it if not
                {
                    ExtractDbCardClessAids(cards, drls, aid);
                }

                foreach (var aid in cards)
                {
                    DoDeferredLogging(LogLevel.Info, "Aid", () => new[] { $"{aid.Aid}; Info: [App Ver Term {aid.AppVerTerm}," + $" Trans Limit {aid.TransLimit}," +
                                     $" Floor Limit {aid.FloorLimit}," + $" CVM Limit {aid.CvmLimit}," + $" ODCVM Limit {aid.OdcvmLimit}," +
                                     $" Term Add Capabilities {aid.TermAddCapabilities}," + $" Term Capabilities CVM {aid.TermCapabilitiesCvm}," +
                                     $" Term Capabilities No CVM {aid.TermCapabilitiesNoCvm}," + $" Term Risk Data {aid.TermRiskData}," +
                                     $" UDOL {aid.Udol}," + $" TAC Default {aid.TacDefault}," + $" TAC Denial {aid.TacDenial}," +
                                     $" TAC Online {aid.TacOnline}" + $" Terminal Type {aid.TerminalType}" + $" MCC {aid.MerchantCategoryCode}" +
                                     $" Max Torn {aid.MaxTorn}" + $" Max Torn Life {aid.MaxTornLife}" +
                                     $" Mchip CVM Cap Above Limit {aid.MchipCvmCapAboveLimit}" +
                                     $" Mchip CVM Cap Below Limit {aid.MchipCvmCapBelowLimit}" +
                                     $" Mstripe CVM Cap Above Limit {aid.MstripeCvmCapAboveLimit}" +
                                     $" Mstripe CVM Cap Below Limit {aid.MstripeCvmCapBelowLimit}" + $" Magstrip App Ver {aid.MagStripeAppVer}extr]" });
                }

                foreach (var drl in drls)
                {
                    DoDeferredLogging(LogLevel.Info, "Aid", () => new[] { $"{drl.Aid}; Info.DRL: [" +
                        $" ProgramID: {drl.ProgramId};" + $" TransLimit: {drl.TransLimit}," +
                        $" FloorLimit: {drl.FloorLimit};" + $" CVMLimit: {drl.CvmLimit}]" });
                }

            }, loggingReference);
        }

        private void LogAids(IList<DbCardClessAid> aids)
        {
            foreach (var aid in aids)
            {
                DoDeferredLogging(LogLevel.Info, "Aid", () => new[] { $"{aid.Aid}; CustomData: [{aid.CustomData}]" });
            }

            foreach (var cardAid in aids.Where(x => string.IsNullOrWhiteSpace(x.CvmLimit)))
            {
                DoDeferredLogging(LogLevel.Warn, "Aid", () => new[] { $"{cardAid.Aid}; Has Blank CvmLimit" });
            }
        }

        private void ExtractDbCardClessAids(ICollection<CardClessAid> cards, ICollection<CardClessDrl> drls, DbCardClessAid aid)
        {
            string termAddCapabilities = null;
            string termCapabilitiesCvm = null;
            string termCapabilitiesNoCvm = null;
            string termRiskData = null;
            string udol = null;
            string terminalType = null;
            string mcc = null;
            string maxTorn = null;
            string maxTornLife = null;
            string mchipCvmCapAboveLimit = null;
            string mchipCvmCapBelowLimit = null;
            string mstripeCvmCapAboveLimit = null;
            string mstripeCvmCapBelowLimit = null;
            string magStripeAppVer = null;
            string expresspayReaderCapabilities = null;
            string expresspayEnhancedReaderCapabilities = null;
            string expresspayKernelVersion = null;
            var prevItem = string.Empty;
            var items = new List<string>();
            foreach (var item in (aid.CustomData ?? string.Empty).Split(','))
            {
                if (item.Contains("="))
                {
                    if (prevItem.Contains("="))
                    {
                        items.Add(prevItem.Trim());
                    }

                    prevItem = item;
                }
                else
                {
                    prevItem = $"{prevItem},{item}";
                }
            }

            if (prevItem.Contains("="))
            {
                items.Add(prevItem.Trim());
            }

            foreach (var item in items)
            {
                if (item.StartsWith(_customDataFields.UdolIdent))
                {
                    udol = item.Substring(_customDataFields.UdolIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.AdditionalIdent))
                {
                    termAddCapabilities = item.Substring(_customDataFields.AdditionalIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.ContactlessIdent))
                {
                    termCapabilitiesCvm = item.Substring(_customDataFields.ContactlessIdent.Length);
                    termCapabilitiesNoCvm = item.Substring(_customDataFields.ContactlessIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.TerminalRiskIdent))
                {
                    termRiskData = item.Substring(_customDataFields.TerminalRiskIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.TerminalIdent))
                {
                    terminalType = item.Substring(_customDataFields.TerminalIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.MccIdent))
                {
                    mcc = item.Substring(_customDataFields.MccIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.TornIdent))
                {
                    maxTorn = item.Substring(_customDataFields.TornIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.TornLifeIdent))
                {
                    maxTornLife = item.Substring(_customDataFields.TornLifeIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.CvmCapAboveIdent))
                {
                    mchipCvmCapAboveLimit = item.Substring(_customDataFields.CvmCapAboveIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.CvmCapBelowIdent))
                {
                    mchipCvmCapBelowLimit = item.Substring(_customDataFields.CvmCapBelowIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.CapAboveIdent))
                {
                    mstripeCvmCapAboveLimit = item.Substring(_customDataFields.CapAboveIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.CapBelowIdent))
                {
                    mstripeCvmCapBelowLimit = item.Substring(_customDataFields.CapBelowIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.MagstripeIdent))
                {
                    magStripeAppVer = item.Substring(_customDataFields.MagstripeIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.ReaderIdent))
                {
                    expresspayReaderCapabilities = item.Substring(_customDataFields.ReaderIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.EnhancedReaderIdent))
                {
                    expresspayEnhancedReaderCapabilities = item.Substring(_customDataFields.EnhancedReaderIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.KernalIdent))
                {
                    expresspayKernelVersion = item.Substring(_customDataFields.KernalIdent.Length);
                }
                else if (item.StartsWith(_customDataFields.RiskParameterIdent))
                {
                    foreach (var drlString in item.Substring(_customDataFields.RiskParameterIdent.Length).Split(','))
                    {
                        var drl = ContactlessProperties.GetDrl(aid.Aid, drlString.Trim(), GetLogger());
                        if (drl != null)
                        {
                            drls.Add(drl);
                        }
                    }
                }
            }

            DoDeferredLogging(LogLevel.Info, "Aid", () => new[] { $"{aid.Aid}; CustomData: [{string.Join("; ", items)}]" });

            cards.Add(new CardClessAid(aid.Aid, aid.AppVerTerm, aid.TransLimit, aid.FloorLimit, aid.CvmLimit, aid.OdcvmLimit,
                termAddCapabilities, termCapabilitiesCvm, termCapabilitiesNoCvm, termRiskData, udol, aid.TacDefault, aid.TacDenial,
                aid.TacOnline, terminalType, mcc, maxTorn, maxTornLife, mchipCvmCapAboveLimit, mchipCvmCapBelowLimit,
                mstripeCvmCapAboveLimit, mstripeCvmCapBelowLimit, magStripeAppVer, expresspayReaderCapabilities,
                expresspayEnhancedReaderCapabilities, expresspayKernelVersion));
        }

        /// <inheritdoc/>
        public void RefreshCustomDataOverrides(string loggingReference)
        {
            _customDataFields?.RefreshKeys(loggingReference);
        }
    }
}
