using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed by Media Channel with large log file (mostly Magical Advanced Config keys)
    /// </summary>
    [Migration(20050022, "ADO#628022 - Set all configuration needed so that Media Channel can deal with large log files"), Tags(Constants.MigrationTypeData)]
    public class ADO628022_MediaChannelConfigUpdate : Migrationable
    {
        private const string CategoryNameConnectivity = "CONNECTIVITY";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'ConnectionThread:BufferSize:Read:Bytes:MediaChannelConnectionThread', @value = '8192', @category = '{CategoryNameConnectivity}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Socket:Send:Timeout:MediaChannelConnectionThread', @value = '00:00:30', @category = '{CategoryNameConnectivity}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
