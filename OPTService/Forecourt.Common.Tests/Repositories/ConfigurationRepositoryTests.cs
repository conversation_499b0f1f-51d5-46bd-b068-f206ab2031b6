using FluentAssertions;
using Forecourt.Common.Workers.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NSubstitute.ReturnsExtensions;
using OPT.Common.Repositories;
using System;
using System.Collections.Generic;
using Xunit;

namespace OPT.Common.Tests.Repositories
{
    public class ConfigurationRepositoryTests
    {
        private readonly IHtecLogger _logger;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly ITelemetryWorker _telemetryWorker;

        public ConfigurationRepositoryTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
        }

        #region Constructor

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigurationRepository(null, _dbExecutorFactory, _telemetryWorker);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        [Fact]
        public void constructor_null_db_execution_factory_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigurationRepository(_logger, null, _telemetryWorker);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "dbExecutorFactory");
        }

        [Fact]
        public void constructor_null_telemetry_worker_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new ConfigurationRepository(_logger, _dbExecutorFactory, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "telemetryWorker");
        }
        
        [Fact]
        public void constructor_db_execution_fails_throws_invalid_operation_exception()
        {
            // Arrange
            _dbExecutorFactory.CreateExecutor()
                .Throws(new Exception());

            // Act
            Action constructor = () => new ConfigurationRepository(_logger, _dbExecutorFactory, _telemetryWorker);

            // Assert
            constructor.Should().Throw<InvalidOperationException>();
        }

        [Fact]
        public void constructor_no_contactless_config_throws_invalid_operation_exception()
        {
            // Arrange
            var reader = Substitute.For<IGridReader>();
            reader.Read<ConfigurationRepository.ConfigType>()
                .ReturnsNull();
            
            var db = Substitute.For<IDbExecutor>();

            db.QueryMultiple(default, default, default, default, default)
                .ReturnsForAnyArgs(reader);
            _dbExecutorFactory.CreateExecutor()
                .Returns(db);

            // Act
            Action constructor = () => new ConfigurationRepository(_logger, _dbExecutorFactory, _telemetryWorker);

            // Assert
            constructor.Should().Throw<InvalidOperationException>();
        }

        [Fact]
        public void constructor_no_site_info_throws_invalid_operation_exception()
        {
            // Arrange
            var reader = Substitute.For<IGridReader>();
            reader.Read<ConfigurationRepository.ConfigType>()
                .Returns(new List<ConfigurationRepository.ConfigType> {new ConfigurationRepository.ConfigType {Id = 1, Type = "Contactless"}});

            reader.Read<ConfigurationRepository.ConfigCategory>()
                .ReturnsNull();

            var db = Substitute.For<IDbExecutor>();

            db.QueryMultiple(default, default, default, default, default)
                .ReturnsForAnyArgs(reader);
            _dbExecutorFactory.CreateExecutor()
                .Returns(db);

            // Act
            Action constructor = () => new ConfigurationRepository(_logger, _dbExecutorFactory, _telemetryWorker);

            // Assert
            constructor.Should().Throw<InvalidOperationException>();
        }

        [Fact]
        public void constructor_no_service_type_throws_invalid_operation_exception()
        {
            // Arrange
            var reader = Substitute.For<IGridReader>();
            reader.Read<ConfigurationRepository.ConfigType>()
                .Returns(new List<ConfigurationRepository.ConfigType> { new ConfigurationRepository.ConfigType { Id = 1, Type = "Contactless" } });


            reader.Read<ConfigurationRepository.ConfigCategory>()
                .Returns(new List<ConfigurationRepository.ConfigCategory> { new ConfigurationRepository.ConfigCategory { Id = 1, Category = "SiteInfo" } });

            reader.Read<ConfigurationRepository.ConfigHeader>()
                .ReturnsNull();

            var db = Substitute.For<IDbExecutor>();

            db.QueryMultiple(default, default, default, default, default)
                .ReturnsForAnyArgs(reader);
            _dbExecutorFactory.CreateExecutor()
                .Returns(db);

            // Act
            Action constructor = () => new ConfigurationRepository(_logger, _dbExecutorFactory, _telemetryWorker);

            // Assert
            constructor.Should().Throw<InvalidOperationException>();
        }

        #endregion
    }
}
