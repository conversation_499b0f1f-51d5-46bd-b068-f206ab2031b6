import { TestBed } from '@angular/core/testing';
import { AdvancedConfigService } from './advanced-config.service';

export function ADVANCED_SERVICE_PROVIDER(): { provide: typeof AdvancedConfigService, useValue: any } {
  return {
    provide: AdvancedConfigService,
    useValue: jasmine.createSpyObj('AdvancedConfigService', [
      'clearMaxFillOverride',
      'getAdvancedConfig', 
      'getConfigDataChanged', 
      'getIsConfigBatch', 
      'setCardProperty',
      'setESocketProperty',
      'setProperty',
      'setPaymentTimeout',
      'setIntegrationType',
    ]),
  };
}

export function ADVANCED_SERVICE_SPY(): jasmine.SpyObj<AdvancedConfigService> {
  return TestBed.inject(AdvancedConfigService) as jasmine.SpyObj<AdvancedConfigService>;
}
