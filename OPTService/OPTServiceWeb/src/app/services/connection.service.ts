import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, catchError } from 'rxjs/operators';
import { Observable, of, Subject, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { NGXLogger } from 'ngx-logger';
import { Connections } from '../core/models/connections.model';
import { PumpLogonInfo } from '../core/models/pumpLogonInfo.model';
import { EndPointDetails } from '../core/models/endPointDetails.model';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class ConnectionService {

  private baseUrl: string;
  private cachedConnections: Connections | undefined;
  private connectionsDataChanged = new Subject<any>();

  /**
   * The Service constructor
   * @param http The http client used for requests
   */
  constructor(private http: HttpClient,
    private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.connectionController.base;
  }

  /** 
   * Gets the Connections data changed event
   */
  public getConnectionsDataChanged(): Observable<any> {
    return this.connectionsDataChanged.asObservable();
  }

  /**
   * Gets the connection details
   */
  public getConnections(forceReload: boolean = false): Observable<Connections> {
    let self = this;

    // Only call the API if forceReload is set to true or cached data is undefined
    if (forceReload || self.cachedConnections === undefined) {
      const url = this.baseUrl + environment.connectionController.getConnections;  
      const options = {
        params: {}
      };

      this.logger.debug(`Getting connections via url: ${url}`);
      return this.http.get<Connections>(url, options)
        .pipe(map(response => {
          self.cachedConnections = response;
          self.connectionsDataChanged.next();
          return response;
        }, 
        catchError(error => this.handleErrorObservable(error)))
      );
    }
    else {
      return of(self.cachedConnections);
    } 
  }

  /**
   * Sets an EndPointDetails property
   * @param ipAddress IP address value
   * @param port port value
   * @param serviceName service name to call
   */
  public setProperty(ipAddress: string, port: number, serviceName: string): Observable<any> {
    const url = this.baseUrl + serviceName;
    const options = {
      params: {}
    };
    this.logger.debug(`Setting property in ${url}`);
    let endPointDetails = new EndPointDetails();
    endPointDetails.IpAddress = ipAddress;
    endPointDetails.Port = port;
    return this.http.post(url, endPointDetails, options);
  }

  /**
   * Sets the PumpControllerLogonInfo property
   * @param logonInfo login info
   */
  public setPumpControllerLogonInfo(logonInfo: string): Observable<any> {
    const url = this.baseUrl + environment.connectionController.setPumpControllerLogonInfo;
    const options = {
      params: {}
    };
    let info = new PumpLogonInfo();
    info.LogonInfo = logonInfo;
    this.logger.debug(`Setting PumpController logon info in ${url}; logonInfo: ${logonInfo}`);
    return this.http.post(url, info, options);
  }

  /**
   * Removes RetalixPosPrimaryIpAddress property
   */
  public removeRetalixPosPrimaryIpAddress(): Observable<any> {
    const url = this.baseUrl + environment.connectionController.removeRetalixPosPrimaryIpAddress;
    const options = {
      params: {}
    };
    this.logger.debug(`Removing RetalixPosPrimaryIpAddress in ${url}`);
    return this.http.get(url, options);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
