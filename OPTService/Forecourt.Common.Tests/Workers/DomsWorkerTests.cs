using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Pump.Models.Doms;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Threading;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class DomsWorkerTests
    {
        private readonly DomsWorker _domsWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IJournalWorker _journalWorker;
        private readonly IHtecLogger _logger;
        private readonly IControllerWorker _controllerWorker;
        private readonly IFromOptWorker _optWorker;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITimerFactory _timerFactory;
        private static readonly IPAddress IpAddress = IPAddress.None;
        private const string LoginString = "Login String";

        public DomsWorkerTests()
        {
            _journalWorker = Substitute.For<IJournalWorker>();
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.GetDomsInfo().Returns(new DomsInfo(IpAddress.ToString(), LoginString, false, false));
            _logger = Substitute.For<IHtecLogger>();
            _controllerWorker = Substitute.For<IControllerWorker>();
            _optWorker = Substitute.For<IFromOptWorker>(); 
            _configurationManager = Substitute.For<IConfigurationManager>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _domsWorker = new DomsWorker(_hydraDb, _logger, _journalWorker, _configurationManager, _timerFactory);
            _domsWorker.RegisterWorker(_controllerWorker);
            _domsWorker.RegisterWorker(_optWorker);
            _domsWorker.Start();
        }

        // TODO: Fix when doing DOMS interface
        [Fact(Skip = "So when doing DOMS")]
        public void test_initial_state()
        {
            // Arrange

            // Act

            // Assert
            _domsWorker.StateEnabled.Should().BeFalse();
            _domsWorker.StateConnected.Should().BeFalse();
            _domsWorker.IpAddress.Should().Be(IpAddress);
            _domsWorker.LoginString.Should().Be(LoginString);
        }

        // TODO: Fix when doing DOMS interface
        [Fact(Skip = "So when doing DOMS")]
        public void test_set_ip_address()
        {
            // Arrange
            IPAddress ipAddress = IPAddress.Loopback;

            // Act
            _domsWorker.SetIpAddress(ipAddress);

            // Assert
            _domsWorker.IpAddress.Should().Be(ipAddress);
            _hydraDb.Received(1).SetDomsIpAddress(ipAddress);
        }

        // TODO: Fix when doing DOMS interface
        [Fact(Skip = "So when doing DOMS")]
        public void test_set_login_string()
        {
            // Arrange
            const string loginString = "New Login String";

            // Act
            _domsWorker.SetLoginString(loginString);

            // Assert
            _domsWorker.LoginString.Should().Be(loginString);
            _hydraDb.Received(1).SetDomsLoginString(loginString);
        }

        // TODO: Fix when doing DOMS interface
        [Fact(Skip="So when doing DOMS")]
        public void test_enable()
        {
            // Arrange

            // Act
            _domsWorker.Enable();
            Thread.Sleep(1500);

            // Assert
            _logger.Received().Info($"Attempting DOMS connection, IP Address is {IpAddress}," + $" Fetched Login String is {LoginString}," +
                                    $" Prepared Login String is {LoginString}," + $" TCP 1 Login String is {LoginString}," +
                                    $" TCP 2 Login String is {LoginString}," + $" TCP 5 Login String is {LoginString}," +
                                    $" State Login String is {LoginString}");
        }
    }
}
