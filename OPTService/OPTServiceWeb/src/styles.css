/* You can add global styles to this file, and also import other style files */
html, body {
    margin: 0;
    height: 100%;
}

.btn-label {position: relative;left: -12px;display: inline-block;padding: 6px 12px;background: rgba(0,0,0,0.15);border-radius: 3px 0 0 3px;}
.btn-labeled {padding-top: 0;padding-bottom: 0;}
.button-accordion:focus {
    outline: none;
    box-shadow: none;
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    transition-timing-function: ease;
    transition-duration: .35s;
    transition-property: height;
  }
  
a[aria-expanded=true] .bi-chevron-down {
    display: none;
}
a[aria-expanded=false] .bi-chevron-up {
    display: none;
}

.text-blue{
    color: #0260c5 !important;
}

.border-blue{
    border-color: #0260c5 !important;
}

.text-size-075{
    font-size: 0.75rem;
}

.btn-link:focus,
.btn-link:hover{
    box-shadow: none !important;
    text-decoration: none !important;
}

.button-clicked {
    cursor: progress;
}

.badge-blink {
    animation: blinker 1s linear infinite;
}

.badge-pump {    
    color: white;
    background-color: cornflowerblue;
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}