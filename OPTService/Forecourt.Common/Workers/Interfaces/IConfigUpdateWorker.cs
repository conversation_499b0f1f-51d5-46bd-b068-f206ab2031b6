using Htec.Foundation.Connections.Core.Interfaces;
using System.Collections.Generic;

namespace OPT.Common.Workers.Interfaces
{
    public interface IConfigUpdateWorker : IConnectable
    {
        IEnumerable<string> WhitelistFiles { get; }
        IEnumerable<string> LayoutFiles { get; }
        IEnumerable<string> UpgradeFiles { get; }
        IEnumerable<string> SoftwareFiles { get; }
        IEnumerable<string> MediaFiles { get; }
        IEnumerable<string> PlaylistFiles { get; }
        IEnumerable<string> DatabaseBackupFiles { get; }
        IEnumerable<string> OptLogFiles { get; }
        int FilePruneDays { get; }
        int TransactionPruneDays { get; }
        int ReceiptPruneDays { get; }

        /// <summary>Set the directory for whitelist files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetWhitelistDirectory(string directory);

        /// <summary>Set the directory for layout files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetLayoutDirectory(string directory);

        /// <summary>Set the contactless properties file.</summary>
        /// <param name="filename">File name to set.</param>
        void SetContactlessPropertiesFile(string filename);

        /// <summary>Set the directory for media files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetMediaDirectory(string directory);

        /// <summary>Set the directory for playlist files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetPlaylistDirectory(string directory);

        /// <summary>Set the directory for Database Backup files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetDatabaseBackupDirectory(string directory);

        /// <summary>Set the directory for OPT log files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetOptLogsDirectory(string directory);

        /// <summary>Set the directory for log files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetLogDirectory(string directory);

        /// <summary>Set the directory for journal files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetJournalDirectory(string directory);

        /// <summary>Set the directory for trace files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetTracesDirectory(string directory);

        /// <summary>Set the directory for transaction files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetTransactionDirectory(string directory);

        /// <summary>Set the directory for retalix transaction files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetRetalixTransactionDirectory(string directory);

        /// <summary>Set the directory for OPT software files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetSoftwareDirectory(string directory);

        /// <summary>Set the directory for upgrade files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetUpgradeDirectory(string directory);

        /// <summary>Set the directory for rollback files.</summary>
        /// <param name="directory">Directory to set.</param>
        void SetRollbackDirectory(string directory);

        /// <summary>Set the number of days for pruning files.</summary>
        /// <param name="days">Days to prune.</param>
        /// <returns>Null if successful, error message otherwise.</returns>
        string SetFilePruneDays(int days);

        /// <summary>Set the number of days for pruning transactions.</summary>
        /// <param name="days">Days to prune.</param>
        /// <returns>Null if successful, error message otherwise.</returns>
        string SetTransactionPruneDays(int days);

        /// <summary>Set the number of days for pruning receipts.</summary>
        /// <param name="days">Days to prune.</param>
        /// <returns>Null if successful, error message otherwise.</returns>
        string SetReceiptPruneDays(int days);
    }
}