using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;

namespace Forecourt.Core.HydraDb.Models
{
    /// <summary>
    /// Holds all Endpoint Configuration options
    /// </summary>
    [HasConfiguration()]
    public class EndPointsConfig : Loggable
    {
        #region Endpoints Config Configurables

        public const string ConfigCategoryEndPointsApi = Constants.CategoryNameEndPointsApi + Constants.CategorySeparator;

        public const string ConfigCategoryEndPointsSignalR = Constants.CategoryNameEndPointsSignalR + Constants.CategorySeparator;

        /// <summary>
        /// Config key for Endpoints Api Config
        /// </summary>
        public const string ConfigKeyEndPointsApiConfig = ConfigCategoryEndPointsApi + "CONFIG";

        /// <summary>
        /// Default vaue for EndPoints Api Config
        /// </summary>
        public const string DefaultValueEndPointsApiConfig = "";

        /// <summary>
        /// Configurable value for, EndPoints Api Config
        /// </summary>
        public ConfigurableString ConfigValueEndPointsApiConfig { get; private set; }

        /// <summary>
        /// Config key for Endpoints Api Bos
        /// </summary>
        public const string ConfigKeyEndPointsApiBos = ConfigCategoryEndPointsApi + "BOS";

        /// <summary>
        /// Default vaue for, EndPoints Api Bos
        /// </summary>
        public const string DefaultValueEndPointsApiBos = "";

        /// <summary>
        /// Configurable value for, EndPoints Api Bos
        /// </summary>
        public ConfigurableString ConfigValueEndPointsApiBos { get; private set; }

        /// <summary>
        /// Config key for Endpoints Api Pos
        /// </summary>
        public const string ConfigKeyEndPointsApiPos = ConfigCategoryEndPointsApi + "POS";

        /// <summary>
        /// Default vaue for, EndPoints Api Pos
        /// </summary>
        public const string DefaultValueEndPointsApiPos = "";

        /// <summary>
        /// Configurable value for, EndPoints Api Pos
        /// </summary>
        public ConfigurableString ConfigValueEndPointsApiPos { get; private set; }

        /// <summary>
        /// Config key for Endpoints SignalR EndPoint
        /// </summary>
        public const string ConfigKeyEndPointsSignalREndPoint = ConfigCategoryEndPointsSignalR + ConfigConstants.EndPointUpper;

        /// <summary>
        /// Default vaue for EndPoints SignalR EndPoint
        /// </summary>
        public const string DefaultValueEndPointsSignalREndPoint = "";

        /// <summary>
        /// Configurable value for, EndPoints SignalR EndPoint
        /// </summary>
        public ConfigurableString ConfigValueEndPointsSignalREndPoint { get; private set; }

        /// <summary>
        /// Config key for Endpoints SignalR Hub Name
        /// </summary>
        public const string ConfigKeyEndPointsSignalRHubName = ConfigCategoryEndPointsSignalR + "HUB-NAME";

        /// <summary>
        /// Default vaue for EndPoints SignalR Hub Name
        /// </summary>
        public const string DefaultValueEndPointsSignalRHubName = "webHub";

        /// <summary>
        /// Configurable value for, EndPoints SignalR Hub Name
        /// </summary>
        public ConfigurableString ConfigValueEndPointsSignalRHubName { get; private set; }

        /// <summary>
        /// Config key for Endpoints SignalR Mode Change
        /// </summary>
        public const string ConfigKeyEndPointsSignalRHubNameModeChange = ConfigCategoryEndPointsSignalR + "MODE-CHANGE";

        /// <summary>
        /// Default vaue for EndPoints SignalR Mode Change
        /// </summary>
        public const string DefaultValueEndPointsSignalRHubNameModeChange = "";

        /// <summary>
        /// Configurable value for, EndPoints SignalR Mode Change
        /// </summary>
        public ConfigurableString ConfigValueEndPointsSignalRHubNameModeChange { get; private set; }

        /// <summary>
        /// Config key for Endpoints SignalR Mode Change
        /// </summary>
        public const string ConfigKeyEndPointsSignalRHubNameDayEnd = ConfigCategoryEndPointsSignalR + "DAY-END";

        /// <summary>
        /// Default vaue for EndPoints SignalR Day End
        /// </summary>
        public const string DefaultValueEndPointsSignalRHubNameDayEnd = "";

        /// <summary>
        /// Configurable value for, EndPoints SignalR Day End
        /// </summary>
        public ConfigurableString ConfigValueEndPointsSignalRHubNameDayEnd { get; private set; }

        #endregion

        /// <summary>
        /// Configurable value (as a string) for, Config API EndPoint
        /// </summary>
        public string ApiConfig => ConfigValueEndPointsApiConfig.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, BOS API EndPoint
        /// </summary>
        public string ApiBos => ConfigValueEndPointsApiBos.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, POS API EndPoint
        /// </summary>
        public string ApiPos => ConfigValueEndPointsApiPos.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, SignalR EndPoint
        /// </summary>
        public string SignalREndPoint => ConfigValueEndPointsSignalREndPoint.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, SignalR Hub name
        /// </summary>
        public string SignalRHubName => ConfigValueEndPointsSignalRHubName.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, SignalR Mode Change Hub name
        /// </summary>
        public string SignalHubNameModeChange => ConfigValueEndPointsSignalRHubNameModeChange.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, SignalR Day End Hub name
        /// </summary>
        public string SignalHubNameDayEnd => ConfigValueEndPointsSignalRHubNameDayEnd.GetValue();

        /// <summary>
        /// Perform basic validation on this model
        /// </summary>
        /// <returns>bool</returns>
        public Result IsValid()
        {
            if (ApiConfig.IsNullOrWhiteSpace())
            {
                return Result.Failure("No Config API Endpoint defined");
            }

            if (ApiBos.IsNullOrWhiteSpace())
            {
                return Result.Failure("No BOS API Endpoint defined");
            }

            if (ApiPos.IsNullOrWhiteSpace())
            {
                return Result.Failure("No POS API Endpoint defined");
            }

            if (SignalREndPoint.IsNullOrWhiteSpace())
            {
                return Result.Failure("No SignalR Endpoint defined");
            }

            if (SignalRHubName.IsNullOrWhiteSpace())
            {
                return Result.Failure("No SignalR Hub name defined");
            }

            return Result.Success();
        }


        // <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger"><see cref="IHtecLogger"/> instance</param>
        /// <param name="configurationManager"><see cref="IConfigurationManager"/> instance</param>
        public EndPointsConfig(IHtecLogger logger, IConfigurationManager configurationManager) : base(logger, configurationManager)
        {
            ConfigValueEndPointsApiConfig = new ConfigurableString(this, ConfigKeyEndPointsApiConfig, DefaultValueEndPointsApiConfig);
            ConfigValueEndPointsApiBos = new ConfigurableString(this, ConfigKeyEndPointsApiBos, DefaultValueEndPointsApiBos);
            ConfigValueEndPointsApiPos = new ConfigurableString(this, ConfigKeyEndPointsApiPos, DefaultValueEndPointsApiPos);

            ConfigValueEndPointsSignalREndPoint = new ConfigurableString(this, ConfigKeyEndPointsSignalREndPoint, DefaultValueEndPointsSignalREndPoint);
            ConfigValueEndPointsSignalRHubName = new ConfigurableString(this, ConfigKeyEndPointsSignalRHubName, DefaultValueEndPointsSignalRHubName);
            ConfigValueEndPointsSignalRHubNameModeChange = new ConfigurableString(this, ConfigKeyEndPointsSignalRHubNameModeChange, DefaultValueEndPointsSignalRHubNameModeChange);
            ConfigValueEndPointsSignalRHubNameDayEnd = new ConfigurableString(this, ConfigKeyEndPointsSignalRHubNameDayEnd, DefaultValueEndPointsSignalRHubNameDayEnd);
        }
    }
}
