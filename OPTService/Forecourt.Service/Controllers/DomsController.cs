using Forecourt.Pump.Controllers.Interfaces;
using Htec.Foundation.Connections.Models;
using OPTService.OPTServiceClasses;
using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    // TODO: Amalgamate into PumpController - see ADO#613151
    public class DomsController : ApiController
    {
        private const string WebName = "Web";
        private readonly IWeb _web;

        public IWeb Web => _web;

        private readonly IDomsController _domsController;

        public DomsController(IWeb web, IDomsController domsController)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));

            _domsController = domsController ?? throw new ArgumentNullException(nameof(domsController));
        }

        #region Update Data

        /// <summary>
        /// Set DOMS Connection Enabled flag.
        /// </summary>
        /// <param name="details">Includes the Enabled flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost, Obsolete("Not relevant now, as this is now part of Integrations.PumpType")]
        public IHttpActionResult SetDomsEnabled(DomsDetails details)
        {
            return BadRequest();
        }

        /// <summary>
        /// Set DOMS Connection IP Address.
        /// </summary>
        /// <param name="details">Includes the IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost, Obsolete("Use ConnectionsController.SetPumpControllerAddress instead")]
        public IHttpActionResult SetDomsIpAddress(DomsDetails details)
        {
            return BadRequest();
        }

        /// <summary>
        /// Set DOMS Connection Login String.
        /// </summary>
        /// <param name="details">Includes the Login String to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost, Obsolete("Use ConnectionsController.SetPumpControllerLogonInfo instead.")]
        public IHttpActionResult SetDomsLoginString(DomsDetails details)
        {
            return BadRequest();
        }

        /// <summary>
        /// Check DOMS State.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage CheckDomsState()
        {
            Web.SendChatMessage(WebName, "Check DOMS State");
            string result = Web.CheckDomsState();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Reset DOMS.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ResetDoms()
        {
            Web.SendChatMessage(WebName, "Reset DOMS");
            string result = Web.ResetDoms();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Master Reset DOMS.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage MasterResetDoms()
        {
            Web.SendChatMessage(WebName, "Master Reset DOMS");
            string result = Web.MasterResetDoms();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Show DOMS Sate.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ShowDomsState()
        {
            Web.SendChatMessage(WebName, "Show DOMS State");
            string result = Web.ShowDomsState();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Show DOMS Sate.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ShowDomsFetched()
        {
            Web.SendChatMessage(WebName, "Show DOMS Fetched");
            string result = Web.ShowDomsFetched();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }
        [HttpPost]
        public HttpResponseMessage ShowDomsTcp()
        {
            Web.SendChatMessage(WebName, "Show DOMS TCP");
            string result = Web.ShowDomsTcp();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Show DOMS Sate.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ShowDomsPrepared()
        {
            Web.SendChatMessage(WebName, "Show DOMS Prepared");
            string result = Web.ShowDomsPrepared();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Reconnect DOMS.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ReconnectDoms()
        {
            Web.SendChatMessage(WebName, "Reconnect DOMS");
            string result = Web.ReconnectDoms();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Clear a transaction.
        /// </summary>
        /// <param name="details">Includes the FP Id and Seq No of transaction to clear.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost] //, Obsolete("Invalid route - not part of any Transaction Workflow!")]
        public HttpResponseMessage ClearTransaction(DomsTransactionDetails details)
        {
            byte fpId = details.FpId;
            int seqNo = details.SeqNo;
            if (fpId < 1 || seqNo < 1)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Transaction");
            }

            Web.SendChatMessage(WebName, $"Clearing DOMS Transaction FP {fpId}, Seq No {seqNo}");
            var result = _domsController.ReserveWithLimit(fpId, 12000, 75, null);
            if (result.IsSuccess)
            {
                result = _domsController.RemoveAuth(fpId, 75, null);
            }
            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Remove the Reserve on a FpId/Pump
        /// </summary>
        /// <param name="fpId">FP Id</param>
        /// <param name="posId">POS Claim Id</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemovePumpReserve(byte fpId, byte posId)
        {
            if (fpId < 1 || posId == 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Transaction");
            }

            Web.SendChatMessage(WebName, $"Removing Reserve DOMS FP {fpId}; POS {posId}");
            var result = _domsController.RemoveReserve(fpId, posId, null);
            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Request forecourt data
        /// </summary>
        /// <param name="requestType">Type of request to fetch</param>
        /// <returns>OK</returns>
        [HttpPost]
        public HttpResponseMessage RequestForecourtData(Htec.Hydra.Core.Pump.Messages.Doms.Setup.FetchFcRequestType requestType)
        {
            Web.SendChatMessage(WebName, $"Sending request for Forecourt data, Request: {requestType}");
            var message = new MessageTracking();
            _domsController.CheckForRequestForecourtData(requestType, message.FullId);
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        /// <summary>
        /// Request status/info for a FpId
        /// </summary>
        /// <param name="fpId">FP Id, 0 for all</param>
        /// <param name="requestType">Type of request to fetch</param>
        /// <returns>OK</returns>
        [HttpPost]
        public HttpResponseMessage RequestPumpData(byte fpId, Htec.Hydra.Core.Pump.Messages.Doms.Setup.FetchFpRequestType requestType)
        {
            if (fpId < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid FpId");
            }

            Web.SendChatMessage(WebName, $"Sending request to DOMS for, Fp {fpId}; Request: {requestType}");
            _domsController.CheckForRequestPumpData(fpId, requestType, null);
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        /// <summary>
        /// Authorise a DOMS fuelling point.
        /// </summary>
        /// <param name="details">Includes the FP Id of fuelling point to authorise.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost, Obsolete("Invalid route - not part of any Transaction Workflow!")]
        public IHttpActionResult Authorise(DomsTransactionDetails details)
        {
            return BadRequest();
        }

        /// <summary>
        /// Emergency Stop a DOMS fuelling point.
        /// </summary>
        /// <param name="details">Includes the FP Id of fuelling point to stop.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage EmergencyStop(DomsTransactionDetails details)
        {
            byte fpId = details.FpId;
            if (fpId < 1)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Emergency Stop");
            }

            Web.SendChatMessage(WebName, $"DOMS Emergency Stop FP {fpId}");
            string result = Web.DomsEmergencyStop(fpId);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Cancel Emergency Stop for a DOMS fuelling point.
        /// </summary>
        /// <param name="details">Includes the FP Id of fuelling point to cancel.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage CancelEmergencyStop(DomsTransactionDetails details)
        {
            byte fpId = details.FpId;
            if (fpId < 1)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Emergency Stop");
            }

            Web.SendChatMessage(WebName, $"DOMS Emergency Stop FP {fpId}");
            string result = Web.DomsCancelEmergencyStop(fpId);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Places a reserve on a FpId/Pump, with limit
        /// </summary>
        /// <param name="fpId">FP Id</param>
        /// <param name="posId">POS Claim Id</param>
        /// <param name="limit">Auth amount</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddPumpReserve(byte fpId, uint limit, byte posId)
        {
            if (fpId < 1 || limit < 0 || posId == 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Transaction");
            }

            Web.SendChatMessage(WebName, $"Adding Reserve DOMS FP {fpId}; POS {posId}; Limit: {limit}");
            var result = _domsController.ReserveWithLimit(fpId, limit, posId, null);
            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion
    }
}