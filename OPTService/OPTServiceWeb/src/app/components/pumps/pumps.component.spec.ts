import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LoggerModule, NG<PERSON>Logger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { OptService } from 'src/app/services/opt.service';
import { PersistentDataService } from 'src/app/services/persistent-data.service';
import { PumpService } from 'src/app/services/pump.service';
import { SignalRService } from 'src/app/services/signal-r.service';

import { PumpsComponent } from './pumps.component';
import { AppComponent } from 'src/app/app.component';

describe('PumpsComponent', () => {
  let component: PumpsComponent;
  let serviceSpy: jasmine.SpyObj<PumpService>;
  let optServiceSpy: jasmine.SpyObj<OptService>;
  let persistentDataServiceSpy: jasmine.SpyObj<PersistentDataService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let pumpServiceSpy: jasmine.SpyObj<PumpService>;
  let fbSpy: jasmine.SpyObj<FormBuilder>;
  let modalServiceSpy: jasmine.SpyObj<NgbModal>;
 
  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('PumpService', ['getPumps', 'getTids', 'getMaxFillOverride', 'setOpt', 'setTid']);
    const optServiceObjSpy = jasmine.createSpyObj('OptService', ['getOpts']);
    const persistentDataServiceObjSpy = jasmine.createSpyObj('PersistentDataService', ['refreshTids', 'refreshMaxFillOverride', 'getTids', 'getMaxFillOverride']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getPumpSignalRMessage', 'getGenericOptConfigSignalRMessage']);
    const appComponentObjSpy =  jasmine.createSpyObj('AppComponent', ['integrators']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        PumpsComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: OptService, useValue: optServiceObjSpy },
        { provide: PumpService, useValue: serviceObjSpy },
        { provide: PersistentDataService, useValue: persistentDataServiceObjSpy },
        { provide: SignalRService, useValue: signalRSpy },
        LOADING_SERVICE_PROVIDER(),
        { provide: AppComponent, useValue: appComponentObjSpy}
      ]
    });

    serviceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    optServiceSpy = TestBed.inject(OptService) as jasmine.SpyObj<OptService>;
    persistentDataServiceSpy = TestBed.inject(PersistentDataService) as jasmine.SpyObj<PersistentDataService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    signalRServiceSpy.getPumpSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getGenericOptConfigSignalRMessage.and.returnValue(of());    
    component = TestBed.inject(PumpsComponent);
    pumpServiceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    pumpServiceSpy.setTid.and.returnValue(of());
    fbSpy = TestBed.inject(FormBuilder) as jasmine.SpyObj<FormBuilder>;
    modalServiceSpy = TestBed.get(NgbModal);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    jasmine.clock().install();

    //Arrange
    spyOn(component, 'refreshData').and.stub();

    //Act
    component.ngOnInit();
    jasmine.clock().tick(50);

    //Assert
    expect(component.refreshData).toHaveBeenCalledTimes(1);

    jasmine.clock().uninstall();
  });

  it('.ngOnDestroy() success', () => {
    //Arrange
    spyOn(component.signalRData, 'unsubscribe').and.stub();

    //Act
    component.ngOnDestroy();

    //Assert
    expect(component.signalRData.unsubscribe).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() success', () => {
    //Arrange
    let fakePumps = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    serviceSpy.getPumps.and.returnValue(of(fakePumps));
    let fakeOpts = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    optServiceSpy.getOpts.and.returnValue(of(fakeOpts));
    let fakeTids =  ['99979901', '99979902', '99979903', '99979904', '99979905', '99979906'];
    persistentDataServiceSpy.getTids.and.returnValue(fakeTids)
    let fakeMaxFillOverride =  1;
    persistentDataServiceSpy.getMaxFillOverride.and.returnValue(fakeMaxFillOverride);

    //Act
    component.refreshData().subscribe(()=>{
      expect(component.pumpsData).toEqual(fakePumps);
      expect(component.tids).toEqual(fakeTids);
      expect(component.maxFillOverride).toEqual(fakeMaxFillOverride);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
    });    
  });

  it('.refreshData() unsuccessful', () => {
    //Arrange
    let fakePumps = [];
    serviceSpy.getPumps.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshData().subscribe(()=>{
      expect(component.pumpsData).toEqual(fakePumps);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
    });    
  });

  it('.refreshData() success. .refreshPumps() with parent filter', () => {
    //Arrange
    component.parentOpt = '1';
    let fakePumps = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    let expectedPumpsResponse = new Array()
    {
      [{ OptStringId: '1' }];
    }
    serviceSpy.getPumps.and.returnValue(of(fakePumps));
    let fakeOpts = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    optServiceSpy.getOpts.and.returnValue(of(fakeOpts));
    let fakeTids =  ['99979901', '99979902', '99979903', '99979904', '99979905', '99979906'];
    persistentDataServiceSpy.getTids.and.returnValue(fakeTids)
    let fakeMaxFillOverride =  1;
    persistentDataServiceSpy.getMaxFillOverride.and.returnValue(fakeMaxFillOverride);

    //Act
    component.refreshData().subscribe(()=>{
      expect(component.pumpsData).toEqual(expectedPumpsResponse);
      expect(component.tids).toEqual(fakeTids);
      expect(component.maxFillOverride).toEqual(fakeMaxFillOverride);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(0);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(0);
    });    
  });

  it('.refreshData() success. Refresh single pump', () => {
    //Arrange
    let expectedPumpsResponse = new Array()
    {
      [{ Number: 1, OptStringId: '1' }, { Number: 2, OptStringId: '1' }, { Number: 3, OptStringId: '1' }];
    };
    
    let fakePumps = new Array()
    {
      [{ Number: 2, OptStringId: '1' }];
    };
    serviceSpy.getPumps.withArgs('2').and.returnValue(of(fakePumps));
    serviceSpy.getPumps.and.returnValue(of(fakePumps));
    let fakeOpts = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    optServiceSpy.getOpts.and.returnValue(of(fakeOpts));
    let fakeTids =  ['99979901', '99979902', '99979903', '99979904', '99979905', '99979906'];
    persistentDataServiceSpy.getTids.and.returnValue(fakeTids)
    let fakeMaxFillOverride =  1;
    persistentDataServiceSpy.getMaxFillOverride.and.returnValue(fakeMaxFillOverride);

    //Act
    component.refreshData('2').subscribe(()=>{
      expect(component.pumpsData).toEqual(expectedPumpsResponse);
      expect(component.tids).toEqual(fakeTids);
      expect(component.maxFillOverride).toEqual(fakeMaxFillOverride);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
    });    
  });

  it('.refreshPumps() should handle unsuccess response from service', () => {
    //Arrange
    let fakeConfig = [];
    serviceSpy.getPumps.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshPumps().subscribe(()=>{
      //Assert
      expect(component.pumpsData).toEqual(fakeConfig);
      expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);  
    });   
  });

  it('.refreshOpts() should handle unsuccess response from service', () => {
    //Arrange
    optServiceSpy.getOpts.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshOpts().subscribe(()=>{
      //Assert
      expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    });
  });

  it('.addPump() with OPT and TID should handle success response from service', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: [''],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['OptStringId'].setValue('2');
    component.addPumpForm.controls['Tid'].setValue('99979903');
    pumpServiceSpy.setOpt.and.returnValue(of({}));
    pumpServiceSpy.setTid.and.returnValue(of({}));
    component.addPumpModal = modalServiceSpy.open('');

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
  });

  it('.addPump() with OPT only should handle success response from service', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: [''],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['OptStringId'].setValue('2');
    pumpServiceSpy.setOpt.and.returnValue(of({}));
    component.addPumpModal = modalServiceSpy.open('');

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.addPump() with TID only should handle success response from service', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: [''],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['Tid'].setValue('99979903');
    pumpServiceSpy.setOpt.and.returnValue(of({}));
    pumpServiceSpy.setTid.and.returnValue(of({}));
    component.addPumpModal = modalServiceSpy.open('');

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
  });

  it('.addPump() should handle no action', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    });

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(0);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(0);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(0);
  });

  it('.addPump() should handle unsuccess response from service for setOpt', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['OptStringId'].setValue('1');
    component.addPumpForm.controls['Tid'].setValue('');

    pumpServiceSpy.setOpt.and.returnValue(throwError({ status: 500 }))

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(0);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.addPump() should handle unsuccess response from service for setTid', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['OptStringId'].setValue('');
    component.addPumpForm.controls['Tid'].setValue('23423');

    pumpServiceSpy.setOpt.and.returnValue(of({}));
    pumpServiceSpy.setTid.and.returnValue(throwError({ status: 500 }))

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });


  it('.addPump() should handle unsuccess response from service for setTid after Opt OK', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls['Number'].setValue(1);
    component.addPumpForm.controls['OptStringId'].setValue('12');
    component.addPumpForm.controls['Tid'].setValue('23423');

    pumpServiceSpy.setOpt.and.returnValue(of({}));
    pumpServiceSpy.setTid.and.returnValue(throwError({ status: 500 }))

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(1);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.addPump() should handle invalid formulary', () => {
    //Arrange
    component.addPumpForm = fbSpy.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    });
    component.addPumpForm.controls.Tid.setErrors({ Required: true });
    component.addPumpForm.controls.OptStringId.setErrors({ Required: true });

    //Act
    component.addPump();

    //Assert
    expect(pumpServiceSpy.setOpt).toHaveBeenCalledTimes(0);
    expect(pumpServiceSpy.setTid).toHaveBeenCalledTimes(0);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(0);
  });


});
