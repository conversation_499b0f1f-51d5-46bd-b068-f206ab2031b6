import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, NgZone, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { forkJoin, Observable, of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { LoadingService } from 'src/app/services/loading.service';
import { OptService } from 'src/app/services/opt.service';
import { PersistentDataService } from 'src/app/services/persistent-data.service';
import { PumpService } from 'src/app/services/pump.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { SecAuthState } from 'src/app/core/enums/secAuthState.enum';
import { AppComponent } from 'src/app/app.component';
import { IntegrationType } from '../../core/enums/integrationType.enum';

@Component({
  selector: 'app-pumps',
  templateUrl: './pumps.component.html',
  styleUrls: ['./pumps.component.css']
})

/**
 * The Pumps component class
 */
export class PumpsComponent implements OnInit {

  @Input()
  pumpsData: Array<any> = [];

  @Input()
  parentOpt: any = '';

  @Input()
  tids: any;

  @Input()
  opts: any;

  @Input()
  maxFillOverride: any;

  @Input()
  pumpCardStates: Map<string, string>;

  @Output()
  pumpCardStatesChange = new EventEmitter<Map<string, string>>();

  showContent: boolean = true;
  signalRData: Subscription | undefined;
  genericOptChangeSubscription: Subscription | undefined;
  
  submitted: boolean = false;
  addPumpForm: FormGroup;
  addPumpModal: NgbModalRef;

  integratorType: any;
  secAuthType: any;
  secAuthState: any;

  /**
   * The Pumps component constructor
   * @param signalRService The signalR service
   * @param zone The angular zone service used to keep screen updated
   * @param pumpService The Pump service
   * @param optService The OPT service
   * @param logger The logger
   * @param loadingService The loading service
   * @param persistentDataService The persistent data service
   * @param modalService The modal service
   * @param fb The form builder
   */
  constructor(
    private signalRService: SignalRService,
    private zone: NgZone,
    private pumpService: PumpService,
    private optService: OptService,
    private logger: NGXLogger,
    private loadingService: LoadingService,
    private persistentDataService: PersistentDataService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private appComp: AppComponent) {

    this.integratorType = IntegrationType;
    this.secAuthState = SecAuthState;

    this.signalRData = this.signalRService.getPumpSignalRMessage().subscribe(pumpNumber => {
      this.zone.run(() => {
        // Refreshing all pumps or only one if received 
        if ((!pumpNumber) || (this.pumpsData?.length != 0 && this.pumpsData?.find(x => x.Number.toString() === pumpNumber))) {
          this.setPumpCardStates();
          this.refreshData(pumpNumber, false, false);
        }
      });
    });
    
    this.genericOptChangeSubscription = this.signalRService.getGenericOptConfigSignalRMessage().subscribe(optStringId => {
      this.zone.run(() => {
        this.refreshMaxFillOverride().subscribe(maxFillOverrideData => {
          this.logger.debug('MaxFillOverride data', maxFillOverrideData);
          this.maxFillOverride = maxFillOverrideData;
        })
      });
    });
  }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    setTimeout(()=>{this.refreshData('',true)});

    this.resetAddPumpForm();
  }

  /**
   * ngOnDestroy angular hook
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
    this.genericOptChangeSubscription?.unsubscribe();
  }

  /**
   * The refresh data method called when received a push from signalR.
   * @param pumpNumber The signalR pushed pump number (optional).
   */
  refreshData(pumpNumber: string = '', onInitBehaviour: boolean = false, showLoading: boolean = true): Observable<any[]> {
    //Show loading screen
    if(this.parentOpt == '' && showLoading){
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }
    else{
      this.showContent = true;
    }
    
    var observables : Observable<any>[] = [];
    var seePumps : boolean = false;
    var seePump : boolean = false;
    var currentObservableIndex = 0;
    var pumpsObservableIndex;
    var optsObservableIndex;

    if(onInitBehaviour){
      if (this.pumpsData?.length === 0 && this.parentOpt == '') {
        observables.push(this.refreshPumps());
        seePumps = true;
        pumpsObservableIndex = currentObservableIndex++;
      }

      if (!this.opts || this.opts?.length === 0) {
        observables.push(this.refreshOpts());
        optsObservableIndex = currentObservableIndex++;
      }
    }    
    else{
      if(pumpNumber){
        observables.push(this.refreshPump(pumpNumber))
        pumpsObservableIndex = currentObservableIndex++;
        seePump = true;
      }
      else{
        observables.push(this.refreshPumps());
        pumpsObservableIndex = currentObservableIndex++;
        seePumps = true;
      } 
    }   

    let fjSubscription = forkJoin(observables);

    fjSubscription.subscribe((results) => {
        //pumps result
        var pumpsResult = results[pumpsObservableIndex];
        if(pumpsResult!== undefined){
          if(seePumps){
            this.logger.debug('All pumps data', pumpsResult);
            this.pumpsData = pumpsResult.filter((x: { OptStringId: any; }) => this.parentOpt == '' || x.OptStringId === this.parentOpt || x.OptStringId === (this.parentOpt === 'none' ? null : this.parentOpt));
            this.logger.debug('Filtered pumps', this.pumpsData);
          }
          else if(seePump){
            this.logger.debug('Pump data', pumpsResult);
            if (pumpsResult.length === 1) {
              // Assuming there is only 1 element coming back from the API at this point
              this.pumpsData.forEach(element => {
                if (element.Number.toString() === pumpNumber) {
                  this.pumpsData[this.pumpsData.indexOf(element)] = pumpsResult[0];
                }
              });
            } else {
              // In this case, something unexpected has happened. So refreshing all.
              this.logger.debug('Unexpected number of Pumps returned by the API. Refresing all of them', pumpsResult);
              this.refreshData('', false, false);
            }
          }
        }

        //opts result
        var optResult = results[optsObservableIndex];
        if(optResult!== undefined){
          this.logger.debug('All OPTs data', optResult);
          this.opts = optResult;
        }

        //tids result
        this.tids = this.persistentDataService.getTids();

        //maxFillOverride result
        this.maxFillOverride = this.persistentDataService.getMaxFillOverride();

        this.secAuthType = this.appComp.integrators[IntegrationType.SecAuth];
      },
      err => {},
      () => {
        if(this.parentOpt == '' && showLoading){
          this.loadingService.hideLoadingScreen();
          this.showContent = true;
        }
      }
    );

    return fjSubscription;
  }

  /**
   * Refreshes the pumps information via API call.
   */
  refreshPumps() {
    return this.pumpService.getPumps().pipe(
      catchError((error:HttpErrorResponse) => {
        this.logger.error('Error getting pumps', error);
        this.loadingService.errorDuringLoading();
        return of(undefined);
      })
    );
  }

  /**
   * Resfreshes single pump information via API call.
   */
  refreshPump(pumpNumber: string) {
    return this.pumpService.getPumps(pumpNumber).pipe(
      catchError((error:HttpErrorResponse)=>{
        this.logger.error('Error getting pumps', error);
        this.loadingService.errorDuringLoading();
        return of(undefined);
      })
    );
  }

  /**
   * Resfreshes the Opts information via API call.
   */
  refreshOpts() {
    return this.optService.getOpts().pipe(
      catchError((error:HttpErrorResponse)=>{
        this.logger.error('Error getting OPTs', error);
        this.loadingService.errorDuringLoading();
        return of(undefined);
      })
    );
  }

  /**
  * Resfreshes the MaxFillOverride information via API call.
  */
  refreshMaxFillOverride() {
    return this.pumpService.getMaxFillOverride().pipe(
      catchError((error: HttpErrorResponse) => {
        this.logger.error('Error getting MaxFillOverride', error);
        return of(undefined);
      })
    );
  }


  /**
   * Sets the current array of pump card states so it can be restored afterwards.
   */
  setPumpCardStates() {
    if(this.pumpCardStates === undefined){
      this.pumpCardStates = new Map<string,string>();
    }

    const cards = $("[id^=collapse-pump-body]").toArray();
    cards.forEach(card => {
      this.pumpCardStates.set(card.id, card.className);
    });
    this.pumpCardStatesChange.emit(this.pumpCardStates);
  }

  /**
   * Gets the state of the pump card so the according class can be set.
   * @param id The pump card identifier.
   */
  getPumpCardState(id: string) {
    return this.pumpCardStates?.get(id) ?? 'collapse';
  }

  /**
   * Opens the AddPump modal
   */
   openAddPumpModal(content: any) {
    this.submitted = false;
    this.refreshTids();
    this.refreshOpts().subscribe(data => {
      this.opts = data;
    })
    this.addPumpModal = this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-pump' });
  }

  /**
   * Resfreshes the tid list from the relevant persistent data service.
   */
   refreshTids() {
    this.tids = this.persistentDataService.getTids();
  }

  /**
   * Adds a new pump
   */
   addPump() {
    this.submitted = true;
    if (!this.addPumpForm.invalid) {
      const pumpNumber = this.addPumpForm.controls['Number'].value;
      const optStringId = this.addPumpForm.controls['OptStringId'].value;
      const tid = this.addPumpForm.controls['Tid'].value;
      const pump = { Number: pumpNumber, OptStringId: optStringId, Tid: tid };
      this.logger.info('Adding pump', pump);

      // Adding pump with OPT
      this.pumpService.setOpt(pump).subscribe(res => {
        this.logger.debug('Pump with OPT added. Mapping TID to it.');
          // Mapping TID to Pump
          this.pumpService.setTid(pump).subscribe(res => {
            this.logger.debug('TID mapped ok');
            this.closeAndResetAddPumpModal();
          }, error => {
            this.logger.error('Problem mapping TID for pump', pump);
          });
      }, error => {
        this.logger.error('Problem adding OPT for pump', pump);
      });
    }
  }

  /**
   * Closes the Add Pump modal form and initializes it.
   */
   closeAndResetAddPumpModal() {
    this.addPumpModal.close();
    this.resetAddPumpForm();
  }

  /**
   * Re-creates the formulary so it is initialized.
   */
   resetAddPumpForm() {
    this.addPumpForm = this.fb.group({
      Number: ['', Validators.required],
      Tid: [''],
      OptStringId: ['']
    },
    {
      validators: this.addPumpFormValidator
    });
  }

  /**
   * Add Pump livebox form validator
   */
   addPumpFormValidator(form: FormGroup): ValidatorFn {
    form.controls.Tid.setErrors(null);
    form.controls.OptStringId.setErrors(null);
    if (form.controls.Tid.value === '' && form.controls.OptStringId.value === '') {
      form.controls.Tid.setErrors({ Required: true });
      form.controls.OptStringId.setErrors({ Required: true });
    }
    return null;
  }
}
