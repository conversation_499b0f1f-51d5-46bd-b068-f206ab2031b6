using Forecourt.Core.Pump.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Forecourt.Core.Pump.Interfaces
{
    public interface IPumpCollection
    {
        IEnumerable<IPump> AllPumps { get; }

        /// <summary>
        /// Get a pump if it exists.
        /// </summary>
        /// <param name="number">Number of pump to fetch.</param>
        /// <param name="pump">Pump, if found.</param>
        /// <returns>True if pump exists, false otherwise.</returns>
        bool TryGetPump(byte number, out IPump pump);

        /// <summary>
        /// Get a pump if it exists, or create a new pump if not.
        /// </summary>
        /// <param name="number">Number of pump to fetch.</param>
        /// <returns>The pump.</returns>
        IPump GetPump(byte number);

        /// <summary>
        /// Get a list of pump states for the given OPT.
        /// </summary>
        /// <param name="optId">Internal ID of OPT.</param>
        /// <returns>List of pump states.</returns>
        IList<PumpStateItem> GetPumpsStates(int optId);

        /// <summary>
        /// Get a pump with the given TID if it exists, or create a new pump if not.
        /// </summary>
        /// <param name="tid">TID to find.</param>
        /// <returns>The pump.</returns>
        IPump GetPumpForTid(string tid);

        /// <summary>
        /// Set up the current list of pumps from the database.
        /// </summary>
        /// <param name="allTids">List of Tids</param>
        void AllocatePumps(IEnumerable<string> allTids);
        bool IsUnmannedPseudoPos { get; }
        void SetUnmannedPseudoPos(bool isOn);
        void SetKioskPayment();

        /// <summary>
        /// Allows sequencing of OnPumpState events
        /// </summary>
        ConcurrentDictionary<byte, OnPumpStateActiveInfo> OnPumpStateActive { get; }

        /// <summary>
        /// Updates the tracking ParentId with the Transaction Reference
        /// </summary>
        /// <param name="pump">Number of pump</param>
        /// <param name="messageTracking">Message tracking instance</param>
        void UpdateParentId(byte pump, IMessageTracking messageTracking);
    }
}
