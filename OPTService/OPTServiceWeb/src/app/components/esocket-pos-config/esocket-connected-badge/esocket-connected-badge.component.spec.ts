import { ComponentFixture, TestBed } from '@angular/core/testing';

import { EsocketConnectedBadgeComponent } from './esocket-connected-badge.component';

describe('EsocketConnectedBadgeComponent', () => {
  let component: EsocketConnectedBadgeComponent;
  let fixture: ComponentFixture<EsocketConnectedBadgeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ EsocketConnectedBadgeComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EsocketConnectedBadgeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('eSocketConnected', () => {
    const tests = [
      { input: undefined, expected: false },
      { input: null, expected: false },
      { input: true, expected: true },
      { input: false, expected: false },
      { input: 'true', expected: true },
      { input: 'false', expected: false },
    ];

    tests.forEach((test: { input: boolean, expected : boolean }) => {
      it(`should return ${test.expected} when _eSocketConnected is ${test.input}`, () => {
        component['_eSocketConnected'] = test.input;

        expect(component.eSocketConnected).toBe(test.expected);
      });
    });
  });
});
