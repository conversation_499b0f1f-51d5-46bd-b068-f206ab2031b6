/**
 * The Constants class
 */
export abstract class Constants {

    // Regex patterns
    static readonly AlphaNumericPattern: string = '[0-9a-zA-Z]*';
    static readonly NumberRegxPattern: string = '^[+-]?[0-9]*';
    static readonly PositiveNumberRegxPattern: string = '[0-9]*';
    static readonly HexNumberRegxPattern: string = '[0-9a-fA-F]+';
    static readonly DecimalRegxPattern: string = '^[+-]?([0-9]{1,3}(,[0-9]{3})*(\\.[0-9]+)?|\\d*\\.\\d+|\\d+)$';
    static readonly DecimalGreaterThanZero: string = '^\\s*(?=.*[1-9])\\d*(?:\\.\\d{1,2})?\\s*$';
    static readonly IpRegxPattern: string = '^((25[0-5]|(2[0-4]|1[0-9]|[1-9]|)[0-9])(\\.(?!$)|$)){4}$';
    static readonly DecimalTwoDecimalsMaximum: string = '^\\d*(?:\\.\\d{1,2})?$';

    // Site types
    static readonly RetailSiteType = 'Retail';
    static readonly RdcSiteType = 'Rdc';
    static readonly AirfieldSiteType = 'Airfield';
    static readonly MarinaSiteType = 'Marina';
    
    // POS Type
    static readonly POS_TYPE_NONE: string = 'NONE'
    static readonly POS_TYPE_RETALIX: string = 'RETALIX'
    static readonly POS_TYPE_HYDRAPOS: string = 'HYDRAPOS'
    static readonly POS_TYPE_3RDPARTY: string = '3RDPARTY'
    static readonly POS_TYPE_SIGNALR: string = 'GENERICSIGNALRAPI'
    static readonly POS_TYPE_SIGNALR_HUBCLIENT_MADIC: string = '// TODO: MADICAPISIGNALR'

    // SECAUTH Type
    static readonly SECAUTH_TYPE_NONE: string = 'NONE'
    static readonly SECAUTH_TYPE_ANPR: string = 'ANPR'
    static readonly SECAUTH_TYPE_SIGNALR_PRE: string = 'SIGNALR-API:PRE'
    static readonly SECAUTH_TYPE_SIGNALR_POST: string = 'SIGNALR-API:POST'
    
    // Date-Times
    static readonly DATE_TIME_LOCALE = 'en-GB';

    // SignalR
    static readonly SignalRRefreshItemId = '';
}
