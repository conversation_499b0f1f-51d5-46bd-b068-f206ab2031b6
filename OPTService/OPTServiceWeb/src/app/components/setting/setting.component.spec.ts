import { ComponentFixture, TestBed } from '@angular/core/testing';
import { <PERSON><PERSON><PERSON>r, FormBuilder, FormControl, FormGroup, FormGroupDirective } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';

import { SettingComponent } from './setting.component';

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('SettingComponent', () => {
  let component: SettingComponent;
  let fixture: ComponentFixture<SettingComponent>;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);
    const configSpy = jasmine.createSpyObj('AdvancedConfigService', ['setCategories']);

    TestBed.configureTestingModule({
      declarations: [SettingComponent],
      providers: [
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: AdvancedConfigService, useValue: configSpy },
        { provide: ControlContainer, useValue: fgd }
      ]
    })

    fixture = TestBed.createComponent(SettingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
