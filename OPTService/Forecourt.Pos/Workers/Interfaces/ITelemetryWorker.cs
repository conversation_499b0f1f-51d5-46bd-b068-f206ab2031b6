namespace Forecourt.Pos.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the ITelemetryWorker, related to Pump/TankGauge
    /// </summary>
    public interface ITelemetryWorker : Htec.Foundation.Connections.Workers.Interfaces.ITelemetryWorker
    {
        /// <summary>
        /// Register message sent to Third Party POS.
        /// </summary>
        /// <param name="pump">Pump for which message has been sent.</param>
        void MessageSentToThirdPartyPos(byte pump);

        /// <summary>
        /// Register message received from Third Party POS.
        /// </summary>
        /// <param name="pump">Pump for which message has been received.</param>
        void MessageReceivedFromThirdPartyPos(byte pump);
    }
}
