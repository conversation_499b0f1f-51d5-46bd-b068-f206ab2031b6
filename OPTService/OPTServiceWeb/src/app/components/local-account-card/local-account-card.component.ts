import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { LocalAccountCard } from 'src/app/core/models/LocalAccountCard.model';
import { LocalAccountCustomer } from 'src/app/core/models/localAccountCustomer.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { LocalAccountService } from 'src/app/services/local-account.service';

class CardErrors {
  remove: boolean = false;
  description: boolean = false;
  discount: boolean = false;
  restrictions1: boolean = false;
  restrictions2: boolean = false;
  noRestrictions: boolean = false;
  AdBlue: boolean = false;
  Avgas: boolean = false;
  Diesel: boolean = false;
  GasOil: boolean = false;
  Jet: boolean = false;
  Kerosene: boolean = false;
  Lpg: boolean = false;
  Lrp: boolean = false;
  Mogas: boolean = false;
  Oil: boolean = false;
  OtherMotorRelatedGoods: boolean = false;
  ShopGoods: boolean = false;
  Unleaded: boolean = false;
  Valeting: boolean = false;
}

@Component({
  selector: 'app-local-account-card',
  templateUrl: './local-account-card.component.html',
  styleUrls: ['./local-account-card.component.css']
})
export class LocalAccountCardComponent implements OnInit {

  @Input() form: FormGroup;
  @Input() card: LocalAccountCard;
  @Input() customer: LocalAccountCustomer;
  @Input() collapse: boolean;
  @Input() disabled: boolean = false;
  @Input() id: string = '';
  @Output() collapseChanged = new EventEmitter<boolean>();

  errors: CardErrors = new CardErrors();

  /**
   * The constructor
   * @param fb 
   * @param modalService 
   */
  constructor(
    private localAccountService: LocalAccountService,
    private logger: NGXLogger) { }

  ngOnInit(): void { }

  /**
   * Removes a local account card
   * @param error name of the error variable
   */
  removeCard(error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Card data ', this.form);

    this.logger.info('Removing card for customer');
    this.localAccountService.removeLocalAccountCard(this.form.value).subscribe(() => {
      this.logger.debug('Card removed ok');
      this.errors[error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing card');
      this.errors[error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Changes the hot status of a card
   * @param error name of the error variable
   */
  changeHotCard(error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Card data ', this.form);
   
    this.form.value.Hot = !this.form.value.Hot;
    
    this.logger.info('Changeing hot status of card');
    this.localAccountService.setLocalAccountCardHot(this.form.value).subscribe(() => {
      this.logger.debug('Hot status changed ok');
      this.errors[error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem changeing hot status');
      this.form.value.Hot = !this.form.value.Hot;
      this.errors[error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Add a local account card
   * @param error name of the error variable
   */
  addLocalAccountCard(error: string, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.debug('Card data ', this.form);

    this.logger.info('Adding card for customer');
    this.localAccountService.addLocalAccountCard(this.form.value).subscribe(() => {
      this.logger.debug('Card added ok');
      this.errors[error] = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding card');
      this.errors[error] = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Notifies collapse has changed value
   */
  collapseModified(): void {
    this.collapseChanged.emit(true);
  }
}
