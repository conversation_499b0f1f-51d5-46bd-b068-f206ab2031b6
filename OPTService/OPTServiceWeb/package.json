{"name": "optweb-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "coverage": "ng test --code-coverage", "sonar": "sonar-scanner"}, "private": true, "dependencies": {"@angular/animations": "~11.0.6", "@angular/common": "~11.0.6", "@angular/compiler": "~11.0.6", "@angular/core": "~11.0.6", "@angular/forms": "~11.0.6", "@angular/localize": "~11.0.6", "@angular/platform-browser": "~11.0.6", "@angular/platform-browser-dynamic": "~11.0.6", "@angular/router": "~11.0.6", "@fortawesome/angular-fontawesome": "^0.8.2", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@microsoft/signalr": "^5.0.2", "@ng-bootstrap/ng-bootstrap": "^8.0.1", "@types/jquery": "^3.5.5", "@types/signalr": "^2.2.36", "bootstrap": "^4.5.0", "bootstrap-icons": "^1.3.0", "jquery": "^3.5.1", "lodash": "^4.17.21", "ngx-logger": "^4.1.9", "rxjs": "~6.6.0", "signalr": "^2.4.1", "tslib": "^2.0.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1100.6", "@angular/cli": "~11.0.6", "@angular/compiler-cli": "~11.0.6", "@types/jasmine": "~3.6.0", "@types/jquery": "^3.5.5", "@types/lodash": "^4.14.168", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.1.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "sonar-scanner": "^3.1.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.0.2"}}