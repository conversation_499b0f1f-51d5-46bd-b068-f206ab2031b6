using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;

namespace Forecourt.Pos.Factories.Interfaces
{
    /// <summary>
    /// Standard IFactory based definition for the POS Integrator In
    /// </summary>
    public interface IPosIntegratorInFactory : IFactory<string, IPosIntegratorInTransient<IMessageTracking, IPump, Result>>
    {
    }
}
