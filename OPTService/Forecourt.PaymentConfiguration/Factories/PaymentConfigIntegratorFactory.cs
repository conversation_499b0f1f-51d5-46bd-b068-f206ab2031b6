using Forecourt.PaymentConfiguration.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.PaymentConfiguration.Factories
{
    /// <summary>
    /// PaymentConfig integrator out factory
    /// </summary>
    public class PaymentConfigIntegratorFactory : Factory<string, IPaymentConfigIntegrator>, IPaymentConfigIntegratorFactory
    {
        private readonly Func<string, IPaymentConfigIntegrator> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>     
        public PaymentConfigIntegratorFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IPaymentConfigIntegrator> resolveTypeInstance) :
            base(logManager, $"{nameof(PaymentConfigIntegratorFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.PaymenConfigeSocketSqlServer, "eSocket.POS (SqlServer)", (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.PaymenConfigeSocketHSqlDb, "eSocket.POS (HSqlDb)", (key) => _resolveTypeInstance(key));
        }
    }
}
