using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Concurrent;
using System.Globalization;
using System.Linq;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.Common.Helpers
{
    /// <summary>
    /// Implements an ICacheHelper using a ConcurrentDictionary.  Loosely based on the Htec.Foundation implementation!
    /// </summary>
    [HasConfiguration]
    public class CacheHelper : Loggable, ICacheHelper
    {
        /// <summary>
        /// Config key prefix, for the cached data interval.  Suffix with the item to be cached for uniqueness.
        /// </summary>
        public const string ConfigKeyPrefixCacheInterval = ConfigurationConstants.CategoryNameCache + ConfigurationConstants.CategorySeparator + "Cache:Interval:";

        /// <summary>
        /// Default value, for the cached data interval in minutes.  See <see cref="TimeSpanExtensions.DefaultTimeSpanFormatCode"/> for the format to use.
        /// </summary>
        public const string DefaultValueCacheInterval = "04:00:00";
        
        public const string HeaderForcingExpiryItemKey = "Key";

        public CacheHelper(IHtecLogger logger, IConfigurationManager configurationManager): base(logger, configurationManager)
        {
        }

        public string ConfigValueDefaultCacheInterval => ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyPrefixCacheInterval, DefaultValueCacheInterval, LoggerIConfigurationManager);

        /// <inheritdoc cref="ICacheHelper.GetCacheInterval"/>
        public TimeSpan GetCacheInterval(string itemType, string item)
        {
            // Stop the recursion, trying to re-read the cached Config!!
            if (itemType.Equals(ConfigurationConstants.CachedItemTypeConfiguration, StringComparison.InvariantCultureIgnoreCase))
            {
                return DefaultValueCacheInterval.GetTimeSpan();
            }

            var appSettings = ConfigurationManager.AppSettings;
            var key = $"{ConfigKeyPrefixCacheInterval}{itemType}";

            var interval = appSettings.GetAppSettingOrDefault(key, ConfigValueDefaultCacheInterval, LoggerIConfigurationManager, item);

            return interval.GetTimeSpan();
        }

        protected class CachedItem
        {
            /// <summary>
            /// When the instance was placed in the cache
            /// </summary>
            public DateTime CreatedAt { get; set; }

            /// <summary>
            /// When the item instance should be replaced
            /// </summary>
            public DateTime ExpiresAt { get; internal set; }

            /// <summary>
            /// Has this item expired
            /// </summary>
            public bool HasExpired => (DateTime.UtcNow > ExpiresAt);

            public int LifeTimeInMinutes { get; set; }
        }

        protected class CachedItem<T> : CachedItem
        {
            /// <summary>
            /// Main constructor
            /// </summary>
            /// <param name="lifeTime">Cached Lifetime, in minutes</param>
            /// <param name="item">Instance to be cached</param>
            public CachedItem(int lifeTime, T item = default(T))
            {
                LifeTimeInMinutes = lifeTime;

                UpdateItem(item);
            }

            /// <summary>
            /// The item instance
            /// </summary>
            public T Item { get; set; }

            /// <summary>
            /// Register a new item instance and reset its expiry 
            /// </summary>
            /// <param name="item">Item instance</param>
            public void UpdateItem(T item)
            {
                Item = item;
                CreatedAt = DateTime.UtcNow;
                ExpiresAt = CreatedAt.AddMinutes(LifeTimeInMinutes);
            }
        }

        private readonly object _cachedItemsSyncObject = new object();
        private readonly ConcurrentDictionary<string, CachedItem> _cachedItemsDictionary = new ConcurrentDictionary<string, CachedItem>();

        private string GetKey(string itemType, string item)
        {
            return $"{itemType}{(string.IsNullOrEmpty(item) ? string.Empty : $"{item}")}".ToLower(CultureInfo.InvariantCulture);
        }

        /// <inheritdoc cref="ICacheHelper.GetCachedItem{T}"/>
        public T GetCachedItem<T>(string itemType, string item, Func<T> action)
        {
            return DoAction(() =>
            {
                lock (_cachedItemsSyncObject)
                {
                    var key = GetKey(itemType, item);

                    T result;

                    if (_cachedItemsDictionary.TryGetValue(key, out var cache) && !cache.HasExpired)
                    {
                        result = ((CachedItem<T>) cache).Item;
                        return Result.Success(result);
                    }

                    var timeSpan = GetCacheInterval(itemType, item);
                    var cacheT = new CachedItem<T>((int) timeSpan.TotalMinutes);

                    result = action();
                    cacheT.UpdateItem(result);
                    _cachedItemsDictionary.AddOrUpdate(key, cacheT, (k, v) => cacheT);
                    GetLogger().Info($"AddingCachedItem.{HeaderForcingExpiryItemKey}", () => new[] {key});

                    return Result.Success(result);
                }
            }, LoggingReference).Value;
        }

        /// <inheritdoc cref="ICacheHelper.ForceExpirationOnCachedItem"/>
        public void ForceExpirationOnCachedItem(string itemType, string item)
        {
            DoAction(() =>
            {
                var key = GetKey(itemType, item);

                if (_cachedItemsDictionary.TryGetValue(key, out var cache))
                {
                    cache.ExpiresAt = DateTime.MinValue;
                    GetLogger().Info(HeaderForcingExpiryItemKey, () => new[] {key});
                }
            }, LoggingReference);
        }

        /// <inheritdoc cref="ICacheHelper.IsCacheValid"/>
        public bool IsCacheValid => !_cachedItemsDictionary.Values.Any(x => x.HasExpired);

        /// <inheritdoc cref="ICacheHelper.IsCachedItemValid"/>
        public bool IsCachedItemValid(string itemType, string item)
        {
            var key = GetKey(itemType, item);

            return _cachedItemsDictionary.TryGetValue(key, out var cache) ? !cache.HasExpired : false;
        }

        /// <inheritdoc cref="ICacheHelper.ForceExpirationOnAllItems"/>
        public void ForceExpirationOnAllItems()
        {
            DoAction(() =>
            {
                foreach (var kv in _cachedItemsDictionary)
                {
                    kv.Value.ExpiresAt = DateTime.MinValue;
                    GetLogger().Info(HeaderForcingExpiryItemKey, () => new[] { kv.Key });
                }

                // Force refresh
                ConfigurationManager.RefreshSection(null);
                
            }, LoggingReference);
        }
    }
}
