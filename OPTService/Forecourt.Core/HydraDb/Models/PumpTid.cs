namespace Forecourt.Core.HydraDb.Models
{
    public class PumpTid
    {
        public int Number { get; }
        public string Tid { get; }
        public string OptId { get; }
        public bool DefaultKioskOnly { get; }
        public bool DefaultMixed { get; }
        public bool CurrentKioskOnly { get; }
        public bool CurrentMixed { get; }
        public bool Closed { get; }
        public bool MaxFillOverrideForFuelCards { get; }
        public bool MaxFillOverrideForPaymentCards { get; }

        // ReSharper disable once UnusedMember.Global
        public PumpTid
        (int number, string tid, string opt, bool defaultKioskOnly, bool defaultMixed, bool currentKioskOnly, bool currentMixed,
            bool closed, bool maxFillOverrideForFuelCards, bool maxFillOverrideForPaymentCards)
        {
            Number = number;
            Tid = tid;
            OptId = opt;
            DefaultKioskOnly = defaultKioskOnly;
            DefaultMixed = defaultMixed;
            CurrentKioskOnly = currentKioskOnly;
            CurrentMixed = currentMixed;
            Closed = closed;
            MaxFillOverrideForFuelCards = maxFillOverrideForFuelCards;
            MaxFillOverrideForPaymentCards = maxFillOverrideForPaymentCards;
        }
    }
}