using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    [Migration(20030008, "HOPT-2010 - add GetVersionInfo procedure"), Tags(Constants.MigrationTypeProgrammatic)]
    public class Hopt2010: Migrationable
    {
        public Hopt2010(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetVersionInfo" };
        }

        public override void Up()
        {
            var result = BackupDbObjects(nameof(Hopt2010), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        public override void Down()
        {
            RestoreDbObjects(nameof(Hopt2010), DbObjects.Reverse().ToArray());
        }
    }
}
