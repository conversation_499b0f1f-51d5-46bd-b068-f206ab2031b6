using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add TIMEMODECHANGE Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(26200009, "ADO#465887 - add TIMEMODECHANGE Configuration Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO465887_AddConfigCategory_TimeModeChange : Migrationable
    {
        private const string _categoryName = Forecourt.Core.Configuration.Constants.CategoryNameTimeModeChange;

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{_categoryName}', @description = 'All TimeModeChange related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{_categoryName}'");
        }
    }
}
