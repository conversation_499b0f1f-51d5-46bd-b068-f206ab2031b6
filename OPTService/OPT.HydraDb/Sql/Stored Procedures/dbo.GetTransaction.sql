USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetTransaction') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetTransaction
END
GO

CREATE PROCEDURE [dbo].[GetTransaction]
 @transId BIGINT = 0,
 @txnNumber NVARCHAR(20) = '',
 @maxTransactionNumber BIGINT 
AS
BEGIN
	SELECT 
		 rowid % @maxTransactionNumber as TransactionId, TransactionTime, FuelCode as GradeCode, WashCode, GradeName,
         WashName, PumpDetails, CardNumber, FuelQuantity, WashQuantity, Amount, FuelCategory,
         WashCategory, FuelSubcategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
         DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration, TxnNumber
	FROM Transactions t
	WHERE 
        (@transId != 0 AND rowid % @maxTransactionNumber = @transId) AND 
		(ISNULL(@txnNumber, '') = '' OR t.TxnNumber = @txnNumber);
END
GO

GRANT EXECUTE ON GetTransaction TO UserRole
GO
