using System.Net;

namespace OPT.Common
{
    public class Endpoints
    {
        public IPEndPoint FromOpt { get; }
        public IPEndPoint ToOpt { get; }
        public IPEndPoint Heartbeat { get; }
        public IPEndPoint HydraPos { get; }
        public IPEndPoint RetalixPos { get; }
        public IPEndPoint ThirdPartyPos { get; }
        public IPEndPoint MediaChannel { get; }
        public IPEndPoint Pump { get; }
        public IPEndPoint Anpr { get; }
        public IPEndPoint CarWash { get; }
        public IPEndPoint TankGauge { get; }
        public IPEndPoint HydraMobile { get; }
        public IPEndPoint[] Esocket { get; }

        public Endpoints
        (IPEndPoint fromOpt = null, IPEndPoint toOpt = null, IPEndPoint heartbeat = null, IPEndPoint hydraPos = null,
            IPEndPoint retalixPos = null, IPEndPoint thirdPartyPos = null, IPEndPoint mediaChannel = null, IPEndPoint pump = null,
            IPEndPoint anpr = null, IPEndPoint carWash = null, IPEndPoint tankGauge = null, IPEndPoint hydraMobile = null, IPEndPoint[] esocket = null)
        {
            FromOpt = fromOpt;
            ToOpt = toOpt;
            Heartbeat = heartbeat;
            HydraPos = hydraPos;
            RetalixPos = retalixPos;
            ThirdPartyPos = thirdPartyPos;
            MediaChannel = mediaChannel;
            Pump = pump;
            Anpr = anpr;
            CarWash = carWash;
            TankGauge = tankGauge;
            HydraMobile = hydraMobile;
            Esocket = esocket;
        }
    }
}