using Microsoft.Owin;
using Microsoft.Owin.StaticFiles;

namespace OPTService.Extensions
{
    /// <summary>
    /// An Angular server options class to use with Owin
    /// </summary>
    public class AngularServerOptions
    {
        /// <summary>
        /// The file server options
        /// </summary>
        public FileServerOptions FileServerOptions { get; set; }

        /// <summary>
        /// The entry path
        /// </summary>
        public PathString EntryPath { get; set; }

        /// <summary>
        /// The HTML5 mode
        /// </summary>
        public bool IsHtml5Mode => EntryPath.HasValue;

        /// <summary>
        /// The AngularServerOptions constructor
        /// </summary>
        public AngularServerOptions()
        {
            FileServerOptions = new FileServerOptions();
            EntryPath = PathString.Empty;
        }
    }

}
