import { Component, NgZone, Input, OnInit } from '@angular/core';
import { FileLocationService } from 'src/app/services/file-location.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { NGXLogger } from 'ngx-logger';
import { FormBuilder, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { FileLocations } from 'src/app/core/models/fileLocations.model';
import { LoadingService } from 'src/app/services/loading.service';
import { merge as _merge } from 'lodash';

class Errors {
  RetalixTransactionFileDirectory: boolean = false;
  TransactionFileDirectory: boolean = false;
  WhitelistDirectory: boolean = false;
  LayoutDirectory: boolean = false;
  SoftwareDirectory: boolean = false;
  FuelDataUpdateFile: boolean = false;
  UpgradeFileDirectory: boolean = false;
  RollbackFileDirectory: boolean = false;
  OptLogFileDirectory: boolean = false;
  LogFileDirectory: boolean = false;
  TraceFileDirectory: boolean = false;
  JournalFileDirectory: boolean = false;
  ReceivedUpdateDirectory: boolean = false;
  DatabaseBackupDirectory: boolean = false;
  ContactlessPropertiesFile: boolean = false;
  EsocketOverrideContactless: boolean = false;
  MediaDirectory: boolean = false;
  PlaylistDirectory: boolean = false;
  EsocketConnectionMade: boolean = false;
};

@Component({
  selector: 'app-file-locations',
  templateUrl: './file-locations.component.html',
  styleUrls: ['./file-locations.component.css']
})
export class FileLocationsComponent implements OnInit {

  @Input()
  fileLocationsData: FileLocations | any;

  fileLocationsForm = this.fb.group({
    RetalixTransactionFileDirectory: [''],
    TransactionFileDirectory: ['', [ Validators.required]],
    WhitelistDirectory: ['', [ Validators.required]],
    LayoutDirectory: ['', [ Validators.required]],
    SoftwareDirectory: ['', [ Validators.required]],
    FuelDataUpdateFile: ['', [ Validators.required]],
    UpgradeFileDirectory: ['', [ Validators.required]],
    RollbackFileDirectory: ['', [ Validators.required]],
    OptLogFileDirectory: ['', [ Validators.required]],
    LogFileDirectory: ['', [ Validators.required]],
    TraceFileDirectory: ['', [ Validators.required]],
    JournalFileDirectory: ['', [ Validators.required]],
    ReceivedUpdateDirectory: ['', [ Validators.required]],
    DatabaseBackupDirectory: ['', [ Validators.required]],
    ContactlessPropertiesFile: [''],
    EsocketOverrideContactless: [''],
    MediaDirectory: ['', [ Validators.required]],
    PlaylistDirectory: ['', [ Validators.required]],
    EsocketConnectionMade: ['']
  });

  showContent: boolean = false;
  signalRData: Subscription | undefined;

  errors: Errors = new Errors;

  /**
   * The File Locations component constructor
   */
  constructor(
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private fileLocationService: FileLocationService,
    private logger: NGXLogger,
    private loadingService: LoadingService
  ) {
    this.signalRData = this.signalRService.getFileLocationSignalRMessage().subscribe(itemId => {
      this.zone.run(() => {
        this.refreshData(itemId, false);
      });
    });
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param data The signalR pushed object. 
   * Not used at the moment since all Opts will be gotten via API call.
   */
  refreshData(itemId: string, showLoading: boolean = true): void {
    this.logger.debug('File Locations itemId =', itemId);

    if(showLoading){
      //Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }
    
    this.fileLocationService.getFileLocations(itemId).subscribe(data => {
      this.logger.debug('All File Locations data', data);
      this.fileLocationsData = _merge(this.fileLocationsData, data);
      this.fileLocationsForm.patchValue(this.fileLocationsData);
      Object.keys(this.fileLocationsForm.controls).forEach(key => {
        this.fileLocationsForm.controls[key].valueChanges.subscribe(val => {
          this.fileLocationsData[key] = val;
        });
      });
    }, () => {
      this.logger.error('Problem getting file locations configuration');
      this.loadingService.errorDuringLoading();
    },()=>{
      if(showLoading){
        //Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    setTimeout(()=>{
      this.refreshData('');
    })    
  }

  /**
   * ngOnDestroy angular hook
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Updates ContactlessPropertiesFile property
   */
  setContactlessPropertiesFile() {
    this.logger.info('Updating ContactlessPropertiesFile');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setContactlessPropertiesFile).subscribe(res => {
      this.logger.debug('ContactlessPropertiesFile updated ok');
      this.errors.ContactlessPropertiesFile = false;
    }, error => {
      this.logger.error('Problem updating ContactlessPropertiesFile');
      this.errors.ContactlessPropertiesFile = true;
    });
  }

  /**
   * Updates RetalixTransactionFileDirectory property
   */
  setRetalixTransactionFileDirectory() {
    this.logger.info('Updating RetalixTransactionFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setRetalixTransactionFileDirectory).subscribe(res => {
      this.logger.debug('RetalixTransactionFileDirectory updated ok');
      this.errors.RetalixTransactionFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating RetalixTransactionFileDirectory');
      this.errors.RetalixTransactionFileDirectory = true;
    });
  }

  /**
   * Updates TransactionFileDirectory property
   */
  setTransactionFileDirectory() {
    this.logger.info('Updating TransactionFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setTransactionFileDirectory).subscribe(res => {
      this.logger.debug('TransactionFileDirectory updated ok');
      this.errors.TransactionFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating TransactionFileDirectory');
      this.errors.TransactionFileDirectory = true;
    });
  }

  /**
   * Updates WhitelistDirectory property
   */
  setWhitelistDirectory() {
    this.logger.info('Updating WhitelistDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setWhitelistDirectory).subscribe(res => {
      this.logger.debug('WhitelistDirectory updated ok');
      this.errors.WhitelistDirectory = false;
    }, error => {
      this.logger.error('Problem updating WhitelistDirectory');
      this.errors.WhitelistDirectory = true;
    });
  }

  /**
   * Updates LayoutDirectory property
   */
  setLayoutDirectory() {
    this.logger.info('Updating LayoutDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setLayoutDirectory).subscribe(res => {
      this.logger.debug('LayoutDirectory updated ok');
      this.errors.LayoutDirectory = false;
    }, error => {
      this.logger.error('Problem updating LayoutDirectory');
      this.errors.LayoutDirectory = true;
    });
  }

  /**
   * Updates SoftwareDirectory property
   */
  setSoftwareDirectory() {
    this.logger.info('Updating SoftwareDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setSoftwareDirectory).subscribe(res => {
      this.logger.debug('SoftwareDirectory updated ok');
      this.errors.SoftwareDirectory = false;
    }, error => {
      this.logger.error('Problem updating SoftwareDirectory');
      this.errors.SoftwareDirectory = true;
    });
  }

  /**
   * Updates FuelDataUpdateFile property
   */
  setFuelDataUpdateFile() {
    this.logger.info('Updating FuelDataUpdateFile');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setFuelDataUpdateFile).subscribe(res => {
      this.logger.debug('FuelDataUpdateFile updated ok');
      this.errors.FuelDataUpdateFile = false;
    }, error => {
      this.logger.error('Problem updating FuelDataUpdateFile');
      this.errors.FuelDataUpdateFile = true;
    });
  }

  /**
   * Updates UpgradeFileDirectory property
   */
  setUpgradeFileDirectory() {
    this.logger.info('Updating UpgradeFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setUpgradeFileDirectory).subscribe(res => {
      this.logger.debug('UpgradeFileDirectory updated ok');
      this.errors.UpgradeFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating UpgradeFileDirectory');
      this.errors.UpgradeFileDirectory = true;
    });
  }

  /**
   * Updates RollbackFileDirectory property
   */
  setRollbackFileDirectory() {
    this.logger.info('Updating RollbackFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setRollbackFileDirectory).subscribe(res => {
      this.logger.debug('RollbackFileDirectory updated ok');
      this.errors.RollbackFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating RollbackFileDirectory');
      this.errors.RollbackFileDirectory = true;
    });
  }

  /**
   * Updates OptLogFileDirectory property
   */
  setOptLogFileDirectory() {
    this.logger.info('Updating OptLogFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setOptLogFileDirectory).subscribe(res => {
      this.logger.debug('OptLogFileDirectory updated ok');
      this.errors.OptLogFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating OptLogFileDirectory');
      this.errors.OptLogFileDirectory = true;
    });
  }

  /**
   * Updates LogFileDirectory property
   */
  setLogFileDirectory() {
    this.logger.info('Updating LogFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setLogFileDirectory).subscribe(res => {
      this.logger.debug('LogFileDirectory updated ok');
      this.errors.LogFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating LogFileDirectory');
      this.errors.LogFileDirectory = true;
    });
  }

  /**
   * Updates TraceFileDirectory property
   */
  setTraceFileDirectory() {
    this.logger.info('Updating TraceFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setTraceFileDirectory).subscribe(res => {
      this.logger.debug('TraceFileDirectory updated ok');
      this.errors.TraceFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating TraceFileDirectory');
      this.errors.TraceFileDirectory = true;
    });
  }

  /**
   * Updates JournalFileDirectory property
   */
  setJournalFileDirectory() {
    this.logger.info('Updating JournalFileDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setJournalFileDirectory).subscribe(res => {
      this.logger.debug('JournalFileDirectory updated ok');
      this.errors.JournalFileDirectory = false;
    }, error => {
      this.logger.error('Problem updating JournalFileDirectory');
      this.errors.JournalFileDirectory = true;
    });
  }

  /**
   * Updates ReceivedUpdateDirectory property
   */
  setReceivedUpdateDirectory() {
    this.logger.info('Updating ReceivedUpdateDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setReceivedUpdateDirectory).subscribe(res => {
      this.logger.debug('ReceivedUpdateDirectory updated ok');
      this.errors.ReceivedUpdateDirectory = false;
    }, error => {
      this.logger.error('Problem updating ReceivedUpdateDirectory');
      this.errors.ReceivedUpdateDirectory = true;
    });
  }

  /**
   * Updates DatabaseBackupDirectory property
   */
  setDatabaseBackupDirectory() {
    this.logger.info('Updating DatabaseBackupDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setDatabaseBackupDirectory).subscribe(res => {
      this.logger.debug('DatabaseBackupDirectory updated ok');
      this.errors.DatabaseBackupDirectory = false;
    }, error => {
      this.logger.error('Problem updating DatabaseBackupDirectory');
      this.errors.DatabaseBackupDirectory = true;
    });
  }

  /**
   * Updates EsocketOverrideContactless property
   */
  setEsocketOverrideContactless() {
    this.logger.info('Updating EsocketOverrideContactless');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setEsocketOverrideContactless).subscribe(res => {
      this.logger.debug('EsocketOverrideContactless updated ok');
      this.errors.EsocketOverrideContactless = false;
    }, error => {
      this.logger.error('Problem updating EsocketOverrideContactless');
      this.errors.EsocketOverrideContactless = true;
    });
  }

  /**
   * Updates MediaDirectory property
   */
  setMediaDirectory() {
    this.logger.info('Updating MediaDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setMediaDirectory).subscribe(res => {
      this.logger.debug('MediaDirectory updated ok');
      this.errors.MediaDirectory = false;
    }, error => {
      this.logger.error('Problem updating MediaDirectory');
      this.errors.MediaDirectory = true;
    });
  }

  /**
   * Updates PlaylistDirectory property
   */
  setPlaylistDirectory() {
    this.logger.info('Updating MediaDirectory');
    this.fileLocationService.setProperty(this.fileLocationsData, environment.fileLocationsController.setPlaylistDirectory).subscribe(res => {
      this.logger.debug('PlaylistDirectory updated ok');
      this.errors.PlaylistDirectory = false;
    }, error => {
      this.logger.error('Problem updating PlaylistDirectory');
      this.errors.PlaylistDirectory = true;
    });
  }
}
