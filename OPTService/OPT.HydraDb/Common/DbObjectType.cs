using System.ComponentModel;

namespace OPT.HydraDb.Common
{
    /// <summary>
    /// Enum that links the type of object to the value SqlServer uses to classify this
    /// </summary>
    public enum DbObjectType
    {
        /// <summary>
        /// Stored Procedure
        /// </summary>
        [Description("P")]
        StoredProcedure,

        /// <summary>
        /// Function
        /// </summary>
        [Description("FN")]
        Function,

        /// <summary>
        /// Function
        /// </summary>
        [Description("V")]
        View     
    }    
}
