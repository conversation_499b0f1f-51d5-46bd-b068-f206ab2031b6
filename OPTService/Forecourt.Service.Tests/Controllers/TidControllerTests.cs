using FluentAssertions;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common;
using OPTService;
using OPTService.Controllers;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class TidControllerTests
    {
        private readonly IHtecLogger _logger;
        private readonly ICore _core;
        private readonly IWeb _web;
        public TidControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _core = Substitute.For<ICore>();
            _web = Substitute.For<IWeb>();
        }

        public static IEnumerable<string> GetTestData()
        {
            yield return "549687321";
            yield return "423423432";
            yield return "867876867";
            yield return "134534743";
            yield return "456678978";
        }

        [Fact]
        public void test_GetTidList_success_response()
        {
            // Arrange
            var testOpts = GetTestData().ToList();
            _web.Tids().Returns(testOpts);
            var tidController = new TidController(_logger, _web);

            // Act
            var result = tidController.GetTidList();

            // Assert
            result.Should().BeOfType<OkNegotiatedContentResult<IList<string>>>().Which.Content.Should().BeEquivalentTo(testOpts);
        }

        [Fact]
        public void test_GetTidList_notfound()
        {
            // Arrange
            _web.Tids().Returns(new List<string>());
            var tidController = new TidController(_logger, _web);

            // Act
            var result = tidController.GetTidList();

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }
    }
}
