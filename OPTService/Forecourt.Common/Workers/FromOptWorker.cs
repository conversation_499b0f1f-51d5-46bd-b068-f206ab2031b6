using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Enums;
using Forecourt.Core.Extensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Extensions;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using OPT.Common.Workers.Messaging;
using OPT.TransactionValidator.Interfaces;
using OPT.TransactionValidator.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Timers;
using System.Xml.Linq;
using CardAmountSalesItem = Htec.Hydra.Core.Bos.Messages.CardAmountSalesItem;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using DiscountItem = Htec.Hydra.Core.Bos.Messages.DiscountItem;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using IControllerWorker = OPT.Common.Workers.Interfaces.IControllerWorker;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using NotificationOptMode = OPT.Common.HydraOPT.NotificationOptMode;
using optMessages = Htec.Hydra.Messages.Opt;
using optModels = Htec.Hydra.Messages.Opt.Models;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;
using PrinterStatus = Htec.Hydra.Messages.Opt.Models.PrinterStatus;
using Pump = Forecourt.Pump.Pump;
using ReceiptTransaction = Htec.Hydra.Messages.Opt.Models.ReceiptTransaction;

namespace OPT.Common.Workers
{
    public class FromOptWorker : OptListenerWorker, IFromOptWorker
    {
        public const string ConfigKeyMaximumReconnectionAttemptsOptWorkerToOpt = ConfigConstants.ConfigKeyCategoryConnectivity + "Maximum:ReconnectionAttempts:OptWorker:ToOpt";
        public const int DefaultValueMaximumReconnectionAttemptsOptWorkerToOpt = 2;

        private int ConfigValueMaximumReconnectionAttemptsOptWorkerToOpt =>
            ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyMaximumReconnectionAttemptsOptWorkerToOpt, DefaultValueMaximumReconnectionAttemptsOptWorkerToOpt, LoggerIConfigurationManager);

        public const string ConfigKeyCacheIntervalGenericOptConfig = CacheHelper.ConfigKeyPrefixCacheInterval + "GenericOptConfig";
        public const string DefaultValueCacheIntervalGenericOptConfig = CacheHelper.DefaultValueCacheInterval;

        private string ConfigValueCacheIntervalGenericOptConfig =>
            ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyCacheIntervalGenericOptConfig, DefaultValueCacheIntervalGenericOptConfig, LoggerIConfigurationManager);

        private int _checkGenericOptConfigIntervalInMinutes => (int)ConfigValueCacheIntervalGenericOptConfig.GetTimeSpan().TotalMinutes;

        private const string OptAppPrefix = "OPTApp_";
        private const string SecureAssetsPrefix = "SecureAssets_";
        private const string CpatAssetsPrefix = "CPATAssets_";
        private const string SoftwareSuffix = ".pkg";
        private const float DefaultVatRate = 20;

        public const string ConfigKeyWaitForOptsOnStopRetryCount = ConfigurationConstants.CategoryNameGeneral + ConfigurationConstants.CategorySeparator + "OnStop:WaitForOpts:RetryCount";
        public const int DefaultValueWaitForOptsOnStopRetryCount = 600;
        private ConfigurableInt WaitForOptsRetryLimit;

        public const string ConfigKeyWaitForOptsOnStopInterval = ConfigurationConstants.CategoryNameGeneral + ConfigurationConstants.CategorySeparator + "OnStop:WaitForOpts:Interval";
        public const string DefaultValueWaitForOptsOnStopInterval = "00:00:00.500";
        private ConfigurableTimeSpan WaitForOptsInterval;

        private readonly OptConfigurationBuilder _optConfigurationBuilder;

        public bool AutoAuth { get; private set; } = true;
        public bool MediaChannel { get; private set; } = true;
        public bool UnmannedPseudoPos { get; private set; } = true;
        public bool AsdaDayEndReport { get; private set; }
        public IList<TermId> FetchedTids => GenericOptConfig?.Tids;
        private bool _hasCardClessAids;

        private INotificationWorker<EventType> NotificationWorker => GetWorker<INotificationWorker<EventType>>();
        private readonly IPumpIntegratorInTransient<IMessageTracking> _pumpWorker;
        private IPosIntegratorOutTransient<IMessageTracking> _posWorker => GetWorker<IPosIntegratorOutTransient<IMessageTracking>>();

        private string _redirectHydraId = OptEndPoints.DefaultHydraId;
        private bool _isOptServiceDiverted = false;
        private IPAddress _divertedServiceAddress;
        private int _divertedFromOptPort;
        private int _divertedToOptPort;
        private int _divertedHeartbeatPort;
        private int _divertedMediaChannelPort;

        private CancellationTokenSource _cancelTokenSource;
        private CancellationToken _cancelToken;

        private readonly string _versionString;
        private readonly IGradeHelper _gradeHelper;
        private readonly IPumpTransactionValidator _pumpTransactionValidator;
        private readonly ICacheHelper _cacheHelper;
        private readonly IVatCalculator _vatCalculator;

        private OptEndPoints _currentEndPoints => CurrentEndPoints;
        private IEnumerable<connGenericEndPoint> _currentEsocketEndPoints = new ESocketEndPoint[0];
        private IPaymentConfigIntegrator _paymentConfig => GetWorker<IPaymentConfigIntegrator>();
        public GenericOptConfig GenericOptConfig
        {
            get
            {
                lock (_genericOptConfigSyncObject)
                {
                    return _genericOptConfig;
                }
            }
            private set { _genericOptConfig = value; }
        }
        private GenericOptConfig _genericOptConfig;

        private readonly object _genericOptConfigSyncObject = new object();
        private DateTime _lastGenericOptConfigCheck = DateTime.MinValue;

        public string WhitelistDirectory { get; private set; }
        public string LayoutDirectory { get; private set; }
        public string SoftwareDirectory { get; private set; }
        public string MediaDirectory { get; private set; }
        public string PlaylistDirectory { get; private set; }

        public string OptLogFileDirectory { get; private set; }

        public IList<string> AvailableSoftware { get; } = new List<string>();
        public IList<string> AvailableSecureAssets { get; } = new List<string>();
        public IList<string> AvailableCpatAssets { get; } = new List<string>();
        private readonly IList<string> _mediaFilesList = new List<string>();
        private readonly IList<string> _playlistFilesList = new List<string>();

        private readonly IDictionary<PaymentTimeoutType, int> _paymentTimeoutDictionary = new ConcurrentDictionary<PaymentTimeoutType, int>();

        [Obsolete("Use IGradeHelper direct!")]
        public ICollection<GradeName> GradeNames => _gradeHelper.Grades.ToList();

        [Obsolete("Use IGradeHelper direct!")]
        public string GetGradeName(byte grade) => _gradeHelper.GetGradeName(grade);

        private readonly ISet<IOpt> _checkConfigs = new HashSet<IOpt>();

        private readonly object _checkConfigLock = new object();

        public void SendToOptHeartbeat() => GetWorker<IToOptWorker>().SendToOptHeartbeat();

        public int GetPaymentTimeout(PaymentTimeoutType mode) => _paymentTimeoutDictionary.TryGetValue(mode, out int timeout) ? timeout : 0;

        public void CarWashNoTicket(IOpt opt) => SendRequest(GetRequestMessage(opt, new GetCarWashRequest(new optModels.Wash())), opt.Id, null);

        public void CarWashTicket(IOpt opt, string ticketNum, string version, string pin = null) =>
            SendRequest(GetRequestMessage(opt, new GetCarWashRequest(new optModels.Wash() { TicketNumber = ticketNum, Pin = pin })), opt.Id, null);

        public IPAddress GetOptIpAddress(IOpt opt) => ConnectionThread.GetIpAddress(opt.Id);

        public void CheckRollbackFiles() => NotificationWorker?.PushChange(EventType.AboutChanged);

        private readonly ValidatorResponseActions _pumpTransactionValidatorActions;

        /// <summary>
        /// Config key for, the maximum number of CardInserted attempts before the Pump is reset
        /// </summary>
        public const string ConfigKeyMaximumCardInsertedAttempts = ConfigurationConstants.CategoryNameGeneral + ConfigurationConstants.CategorySeparator + "Opt:CardInserted:Max-Count";

        /// <summary>
        /// Default value for, the maximum number of CardInserted attempts before the Pump is reset
        /// </summary>
        public const int DefaultValueMaximumCardInsertedAttempts = 2500;

        /// <summary>
        /// Config value for, the maximum number of CardInserted attempts before the Pump is reset
        /// </summary>
        public ConfigurableInt MaximumCardInsertedAttempts { get; private set; }

        /// <summary>
        /// Config key for, LogLevel for the mismatch between the OptService PumpList and IPumpIntegrator PumpList
        /// </summary>
        public const string ConfigKeyLogLevelPumpListMismatch = ConfigurationConstants.ConfigCategoryLogging + "LogLevel:PumpListMismatch";

        /// <summary>
        /// Default value for, LogLevel for the mismatch between the OptService PumpList and IPumpIntegrator PumpList
        /// </summary>
        public const string DefaultValueLogLevelPumpListMismatch = ConfigConstants.None;

        /// <summary>
        /// Config value for, LogLevel for the mismatch between the OptService PumpList and IPumpIntegrator PumpList
        /// </summary>
        public ConfigurableString LogLevelPumpListMismatch { get; private set; }

        #region Initialisation

        /// <summary>Constructor for OptWorker.</summary>
        /// <param name="toOptWorker">To OPT Worker to use.</param>
        /// <param name="heartbeatOptWorker">Heartbeat OPT Worker to use.</param>
        /// <param name="connectionThread">From Connection to use.</param>
        /// <param name="controllerWorker">Controller Worker to use.</param>
        /// <param name="hscWorker">Pump Worker to use.</param>
        /// <param name="secAuthInWorker">SecAuth In Worker (ANPR) to use.</param>
        /// <param name="secAuthOutWorker">SecAuth Out Worker (ANPR) to use.</param>
        /// <param name="carWashWorker">Car Wash Worker to use.</param>
        /// <param name="posWorker">Current Pos Worker to use.</param>
        /// <param name="modeChangeWorker">Current Pos ModeChange worker</param>
        /// <param name="mediaChannelWorker">Media Channel Worker to use.</param>
        /// <param name="journalWorker">Journal Worker to use.</param>
        /// <param name="telemetryWorker">Telemetry Worker to use.</param>
        /// <param name="configUpdateWorker">Config Update Worker to use.</param>
        /// <param name="localAccountWorker">Local Account Worker to use.</param>
        /// <param name="hydraDb">DB instance to use.</param>
        /// <param name="paymentConfig">DB instance to use for eSocket.POS DB.</param>
        /// <param name="allPumps">Collection of pumps to use.</param>
        /// <param name="allOpts">Collection of OPTs to use.</param>
        /// <param name="logMan">Htec log manager.</param>
        /// <param name="versionString">Service version to send to OPT.</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        /// <param name="timerFactory">ITimerFactory instance</param>
        /// <param name="gradeHelper">IGradeHelper instance</param>
        /// <param name="pumpTransactionValidator">Transaction Validator to use.</param>
        /// <param name="cacheHelper">ICacheHelper to use.</param>
        public FromOptWorker(IToOptWorker toOptWorker, IOptHeartbeatWorker heartbeatOptWorker, IListenerConnectionThread<XElement> connectionThread, IControllerWorker controllerWorker,
            IPumpIntegratorInTransient<IMessageTracking> hscWorker, ISecAuthIntegratorInTransient<IMessageTracking> secAuthInWorker, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOutWorker,
            ICarWashWorker carWashWorker, IPosIntegratorOutTransient<IMessageTracking> posWorker, IPosIntegratorOutMode<IMessageTracking> modeChangeWorker,
            IMediaChannelWorker mediaChannelWorker, IJournalWorker journalWorker, ITelemetryWorker telemetryWorker,
            IConfigUpdateWorker configUpdateWorker, ILocalAccountWorker localAccountWorker, IHydraDb hydraDb, IPaymentConfigIntegrator paymentConfig,
            IPumpCollection allPumps, IOptCollection allOpts,
            IHtecLogManager logMan, string versionString, IConfigurationManager configurationManager, ITimerFactory timerFactory, IGradeHelper gradeHelper,
            IVatCalculator vatCalculator, IPumpTransactionValidator pumpTransactionValidator = null, ICacheHelper cacheHelper = null)
            : base(logMan, "OPT", telemetryWorker, connectionThread, hydraDb, configurationManager, allPumps, allOpts, controllerWorker, posWorker, timerFactory)
        {
            RegisterWorker(toOptWorker);
            RegisterWorker(heartbeatOptWorker);
            RegisterWorker(hscWorker);
            _pumpWorker = hscWorker;
            RegisterWorker(secAuthInWorker ?? throw new ArgumentNullException(nameof(secAuthInWorker)));
            RegisterWorker(secAuthOutWorker);
            RegisterWorker(carWashWorker);
            RegisterWorker(modeChangeWorker ?? throw new ArgumentNullException(nameof(modeChangeWorker)));
            RegisterWorker(mediaChannelWorker);
            RegisterWorker(journalWorker);
            RegisterWorker(configUpdateWorker);
            RegisterWorker(localAccountWorker);

            toOptWorker.RegisterWorker(this);
            heartbeatOptWorker.RegisterWorker(this);

            mediaChannelWorker.RegisterWorker(this);
            controllerWorker.RegisterWorker(this);
            carWashWorker.RegisterWorker(this);
            journalWorker.RegisterWorker(this);
            configUpdateWorker.RegisterWorker(this);

            WaitForOptsRetryLimit = new ConfigurableInt(this, ConfigKeyWaitForOptsOnStopRetryCount, DefaultValueWaitForOptsOnStopRetryCount);
            WaitForOptsInterval = new ConfigurableTimeSpan(this, ConfigKeyWaitForOptsOnStopInterval, DefaultValueWaitForOptsOnStopInterval);
            MaximumCardInsertedAttempts = new ConfigurableInt(this, ConfigKeyMaximumCardInsertedAttempts, DefaultValueMaximumCardInsertedAttempts);
            LogLevelPumpListMismatch = new ConfigurableString(this, ConfigKeyLogLevelPumpListMismatch, DefaultValueLogLevelPumpListMismatch);

            RegisterWorker(paymentConfig ?? throw new ArgumentNullException(nameof(paymentConfig)));
            _versionString = versionString;
            _gradeHelper = gradeHelper ?? throw new ArgumentNullException(nameof(gradeHelper));
            _pumpTransactionValidator = pumpTransactionValidator;
            _cacheHelper = cacheHelper;
            _vatCalculator = vatCalculator ?? throw new ArgumentNullException(nameof(vatCalculator));
            _pumpTransactionValidatorActions = new ValidatorResponseActions(LogManager, configurationManager);

            foreach (PaymentTimeoutType mode in Enum.GetValues(typeof(PaymentTimeoutType)))
            {
                _paymentTimeoutDictionary[mode] = HydraDb.FetchPaymentTimeout(mode);
            }

            var allFileLocations = HydraDb.GetFileLocations();
            SetWhitelistDirectory(ControllerWorker.DirectoryString(allFileLocations?.WhitelistDirectory));
            SetLayoutDirectory(ControllerWorker.DirectoryString(allFileLocations?.LayoutDirectory));
            SetSoftwareDirectory(ControllerWorker.DirectoryString(allFileLocations?.SoftwareDirectory));
            SetMediaDirectory(ControllerWorker.DirectoryString(allFileLocations?.MediaDirectory));
            SetPlaylistDirectory(ControllerWorker.DirectoryString(allFileLocations?.PlaylistDirectory));
            SetOptLogFileDirectory(ControllerWorker.DirectoryString(allFileLocations?.OptLogFileDirectory));

            FetchAvailableSoftware();
            FetchMediaFilesList();
            FetchPlaylistFilesList();

            _optConfigurationBuilder = new OptConfigurationBuilder(HydraDb, Logger);
        }

        protected override GenericEndPoint DoGetEndPoint()
        {
            var optEndPoints = GetEndPoints();
            if (optEndPoints == null)
            {
                return EndPoint;
            }

            AutoAuth = optEndPoints.AutoAuth;
            MediaChannel = optEndPoints.MediaChannel;
            UnmannedPseudoPos = optEndPoints.UnmannedPseudoPos;
            AsdaDayEndReport = optEndPoints.AsdaDayEndReport;

            var eSocketEndPoints = _paymentConfig?.EndPoints;
            if (eSocketEndPoints != null)
            {
                _currentEsocketEndPoints = eSocketEndPoints.Select(x => new connGenericEndPoint(x.Item1, x.Item2));
            }

            var divertOpt = HydraDb.FetchDivertOpt();
            _isOptServiceDiverted = divertOpt.IsDiverted;
            if (!IPAddress.TryParse(divertOpt.IpAddress, out _divertedServiceAddress))
            {
                _divertedServiceAddress = optEndPoints.FromOptEndPoint.Address;
            }

            _divertedFromOptPort = divertOpt.FromOptPort;
            _divertedToOptPort = divertOpt.ToOptPort;
            _divertedHeartbeatPort = divertOpt.HeartbeatPort;
            _divertedMediaChannelPort = divertOpt.MediaChannelPort;

            var endpoint = optEndPoints.FromOptBindEndPoint;

            return new GenericEndPoint(endpoint.Address, endpoint.Port);
        }

        protected override bool DoStartCanStart(GenericEndPoint endPoint)
        {
            var result = base.DoStartCanStart(endPoint);

            if (result)
            {
                _redirectHydraId = MyIdString;

                foreach (var opt in AllOpts.AllOpts)
                {
                    SetPaymentTimeout(opt);
                }

                FetchGenericOptConfig();

                AllPumps.SetUnmannedPseudoPos(UnmannedPseudoPos);

                SetGradeNames();
                CheckForDelivery(new MessageTracking());

                HydraDb.FetchReceipts();

                DoDeferredLogging(LogLevel.Info, $"HydraOPT Service identified as {MyIdString}");
            }

            return result;
        }

        public void WaitForOpts()
        {
            DoAction(() =>
            {
                foreach (var pump in AllPumps.AllPumps)
                {
                    var prevState = pump.SetPreviousPumpState();
                    pump.ClosePump(false, LoggingReference);
                    CheckPumpState(pump, prevState);
                }

                var interval = (int)WaitForOptsInterval.GetValue().TotalMilliseconds;
                var limit = WaitForOptsRetryLimit.GetValue();
                var retries = 0;
                var optsAreReady = AreOptsReady(ref retries);
                while (!optsAreReady && retries < limit)
                {
                    var count = retries;
                    DoDeferredLogging(LogLevel.Info, "Waiting started.RetryCount", () => new[] { $"{count}; {GetStuckOptCount()} OPTs are in use, or connected." });

                    Thread.Sleep(interval);
                    optsAreReady = AreOptsReady(ref retries);
                }

                if (retries >= limit)
                {
                    DoDeferredLogging(LogLevel.Info, "Waiting timed out.RetryCount", () => new[] { $"{limit}; {GetStuckOptCount()} OPTs were still in use, or connected" });
                }
                else
                {
                    DoDeferredLogging(LogLevel.Info, "All OPTs now ready to stop");
                }

                _cancelTokenSource?.Cancel();
            }, null);
        }

        private void PushChangePumpStateChanged(IPump pump, bool pushOpt = false, bool pushPump = false)
        {
            var workerController = NotificationWorker;
            if (pushOpt)
            {
                workerController.PushChange(EventType.OPTChanged, pump.Opt?.IdString);
            }

            if (pushPump)
            {
                workerController.PushChange(EventType.PumpChanged, pump.Number.ToString());
            }
        }

        private int GetStuckOptCount()
        {
            return AllOpts.AllOpts.Count(x => x.InUse || x.Connected);
        }

        private bool AreOptsReady(ref int retries)
        {
            foreach (var opt in AllOpts.AllOpts)
            {
                if (!(opt.InUse && opt.Connected))
                {
                    continue;
                }

                retries++;
                return false;
            }

            return true;
        }

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            var message = new MessageTracking();
            SetThreadName();

            CheckAuthorisedPumps(message);

            foreach (var opt in AllOpts.AllOpts)
            {
                if (IsOnPumpStateActive(opt))
                {
                    continue;
                }

                CheckHeartbeat(opt);
                CheckSignInRequired(opt);
                CheckLogFileRequest(opt);
                CheckPayAtKioskTimer(opt);
            }

            lock (_checkConfigs)
            {
                if (_checkConfigs.Any())
                {
                    foreach (var opt in _checkConfigs)
                    {
                        if (IsOnPumpStateActive(opt))
                        {
                            continue;
                        }

                        CheckOptConfig(opt);
                    }

                    _checkConfigs.Clear();
                }
            }
        }

        #endregion

        protected override Result<XElement> DoOnMessageReceived(IMessageTracking<XElement> message, int id)
        {
            var request = message.Request;
            SetOnPumpStateActive(message, id, SocketType.FromOpt, true, out var pump);
            try
            {
                AllPumps.UpdateParentId(pump, message);

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderOpt, () => new[] { LogCommandInfo(request, id) }, reference: message.FullId);

                RegisterReceived(id, SocketType.FromOpt);

                if (!(MessageParser.ParseXElement(request) is OptRequest optRequest))
                {
                    var msgInvalid = $"Incompatible message received, with Element name: {request.Name}";

                    DoDeferredLogging(LogLevel.Warn, HeaderEndFail, () => new[] { msgInvalid }, reference: message.FullId);
                    return Result.Failure<XElement>(msgInvalid);
                }

                message.Context = GetShortNotificationName(optRequest);

                var result = DoOnMessageReceivedSwitch(optRequest, id, message);
                return Result.SuccessIf<XElement>(result != null, result, "Unknown CommandFrom");
            }
            finally
            {
                SetOnPumpStateActive(message, pump, SocketType.FromOpt, false);
            }
        }

        private XElement DoOnMessageReceivedSwitch(OptRequest request, int id, IMessageTracking message, [CallerMemberName] string methodName = null)
        {
            var workerController = NotificationWorker;
            var workerTelemetry = GetWorker<ITelemetryWorker>();
            var logRefFull = message.FullId;

            if (!AllOpts.TryGetOpt(id, out var opt))
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { "Invalid OPT Id: {id}" }, reference: logRefFull, methodName: methodName);
                return null;
            }

            if (request is HeartbeatRequest)
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage, reference: logRefFull, methodName: methodName);
                return GetResponseMessage(opt, new HeartbeatResponse(ResultSuccess));
            }

            var printerStatus = request as PrinterStatusOptNotificationRequest;
            var deviceState = request as DeviceStateOptNotificationRequest;

            var logHeader = HeaderOpt;
            var logMessages = new[]
            {
                $"{request.Opt.Id} ({id})", $"{request.Type}",
                $"{(printerStatus == null ? string.Empty : $"; PrinterStatus: {printerStatus.Notification.PrinterStatus.Status}")}",
                $"{(deviceState == null ? string.Empty : $"; DeviceState: {deviceState.Notification.DeviceState.Status}")}"

            };

            if (!("|GetCarWashRequest|SignInRequest|PaymentApprovedRequest|PaymentClearedRequest|PaymentCancelledRequest|OptNotificationRequest|" +
                "|SoftwareRequest|KioskUseOptNotificationRequest|").ToLower().Contains($"|{request.GetType().Name}|".ToLower()))
            {
                DoDeferredLogging(LogLevel.Info, logHeader, () => logMessages, reference: logRefFull, methodName: methodName);
            }

            if (request is GetConfigRequest)
            {
                workerController?.SendInformation($"Get Config received from {request.Opt.Id}");
                if (opt != null)
                {
                    var result = Config(opt, true, out var config, true);
                    if (config != null)
                    {
                        opt.SignOut();
                        workerController?.PushChange(EventType.OPTChanged, request.Opt.Id);
                        opt.ConfigSent(config);
                    }

                    CheckConfigNeeded(opt);

                    workerTelemetry?.ConfigRequestReceivedFromOpt(opt);

                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevel.GetValue()), nameof(GetConfigRequest), () => new[] { JsonConvert.SerializeObject(config) }, reference: logRefFull);

                    return GetResponseMessage(opt, new GetConfigResponse(result, config));
                }
            }
            else if (request is WhitelistRequest)
            {
                workerController?.SendInformation($"Get Whitelist request received from {request.Opt.Id}");
                if (opt != null)
                {
                    var result = Whitelist(out var whitelist);
                    if (whitelist != null)
                    {
                        opt.WhitelistSent(whitelist);

                        workerTelemetry?.WhitelistRequestReceivedFromOpt(opt);
                        CheckConfigNeeded(opt);

                        return GetResponseMessage(opt, whitelist);
                    }
                }
            }
            else if (request is LayoutRequest)
            {
                workerController?.SendInformation($"Get Layout request received from {request.Opt.Id}");
                if (opt != null)
                {
                    var result = Layout(out var layout);
                    if (layout != null)
                    {
                        opt.LayoutSent(layout);

                        workerTelemetry?.LayoutRequestReceivedFromOpt(opt);
                        CheckConfigNeeded(opt);

                        return GetResponseMessage(opt, layout);
                    }
                }
            }
            else if (request is GetPumpsStatesRequest)
            {
                workerController?.SendInformation($"Get Pumps States request received from {request.Opt.Id}");
                if (opt != null)
                {
                    return GetResponseMessage(opt, new GetPumpsStatesResponse(ResultSuccess, AllPumps.GetPumpsStates(id).ToList()));
                }
            }
            else if (request is GetMediaFilesListRequest)
            {
                workerController?.SendInformation($"Get Media Files List request received from {request.Opt.Id}");
                if (opt != null)
                {
                    var files = MediaFilesPlusPlaylist(opt.PlaylistFileName);
                    opt.MediaFilesListSent(files);
                    CheckConfigNeeded(opt);
                    return GetResponseMessage(opt, new GetMediaFilesListResponse(ResultSuccess, files));
                }
            }
            else if (request is GetCarWashRequest messageCarWash && messageCarWash.Wash.ProgramId != null)
            {
                DoDeferredLogging(LogLevel.Info, logHeader, () => logMessages.Concat(new[] { $"ProgramId: {messageCarWash.Wash.ProgramId}" }).ToArray(), reference: logRefFull, methodName: methodName);
                workerController?.SendInformation($"Get Car Wash request received from {request.Opt.Id}" +
                                                  (messageCarWash.Wash.ProgramId == null ? "" : $" for Program Id {messageCarWash.Wash.ProgramId}"));

                var workerCarWash = GetWorker<ICarWashWorker>();
                if (opt != null && (workerCarWash?.IsConnected() ?? false))
                {
                    workerCarWash.SendRequestToCarWash(opt, "Brush", messageCarWash.Wash.ProgramId.Value);
                    return null;
                }

                return GetResponseMessage(opt, new GetCarWashResponse(ResultFailure, null));
            }
            else if (request is SignInRequest messageSignIn)
            {
                DoDeferredLogging(LogLevel.Info, logHeader, () => logMessages.Concat(new[] { $"Versions: {JsonConvert.SerializeObject(messageSignIn.Opt)}" })
                    .ToArray(), reference: logRefFull, methodName: methodName);

                workerController?.SendInformation($"Sign In request received from {request.Opt.Id}" +
                                                  $" with software version {messageSignIn.Opt.SoftwareVersion}" +
                                                  $", secure assets version {messageSignIn.Opt.SecureAssetsVersion}," +
                                                  $" multimedia assets version {messageSignIn.Opt.MultimediaAssetsVersion}," +
                                                  $" CPAT assets version {messageSignIn.Opt.CpatAssetsVersion}," +
                                                  $" OPT firmware version {messageSignIn.Opt.OptFirmwareVersion}," +
                                                  $" EMV kernel version {messageSignIn.Opt.EMVKernelVersion}," +
                                                  $" Plugin type {messageSignIn.Opt.PluginType}," +
                                                  $" Plugin type version {messageSignIn.Opt.PluginVersion}");
                if (opt != null)
                {
                    workerTelemetry?.SignInReceivedFromOpt(request.Opt.Id);
                    var result = SignInOut(message, true, id, messageSignIn.Opt, messageSignIn.Ip);

                    return GetResponseMessage(opt, new SignInResponse(result, DateTime.Now));
                }
            }
            else if (request is SignOutRequest)
            {
                workerController?.SendInformation($"Sign Out request received from {request.Opt.Id}");
                if (opt != null)
                {
                    var result = SignInOut(message, false, id, null, null);
                    return GetResponseMessage(opt, new SignOutResponse(result));
                }
            }
            else if (request is PaymentRequest messagePaymentRequest)
            {
                var thePayment = GetPayment(messagePaymentRequest, out var paymentResult);
                var infoMessage = new StringBuilder();

                // TODO: Need to consider what we want to be logging here, if anything
                //if (thePayment.CardRestrictions?.Codes?.Count > 0)
                //{
                //    infoMessage.Append(", card restrictions are");
                //    foreach (string code in thePayment.CardRestrictions.Codes)
                //    {
                //        infoMessage.Append($" code {code}");
                //    }
                //}

                //if (thePayment.Products?.Count > 0)
                //{
                //    infoMessage.Append(", products are");
                //    foreach (var product in thePayment.Products)
                //    {
                //        FindProductMapping(product.Code, out TariffMapping mapping, out _);
                //        infoMessage.Append(
                //            $" code {product.Code} / quantity {(mapping == null ? $"{product.Quantity}" : $"{product.Quantity / 1000.0:F3} litres")} / value �{product.Value / 100.0:F2}");
                //    }
                //}

                //if (thePayment.Discount != null)
                //{
                //    bool isPercent = thePayment.Discount.Type.StartsWith("P");
                //    infoMessage.Append($", discount is name {thePayment.Discount.Name} /" +
                //                       $" type {thePayment.Discount.Type} /" +
                //                       $" value {(isPercent ? $"{thePayment.Discount.Value}%" : $"�{thePayment.Discount.Value / 100.0:F2}")}" +
                //                       (string.IsNullOrWhiteSpace(thePayment.Discount.CardNumber)
                //                           ? ""
                //                           : $" / card number {thePayment.Discount.CardNumber}"));
                //}

                //if (thePayment.LocalAccount != null)
                //{
                //    infoMessage.Append($", local account is mileage {thePayment.LocalAccount.Mileage}" +
                //                       (string.IsNullOrWhiteSpace(thePayment.LocalAccount.Registration)
                //                           ? ""
                //                           : $" / registration {thePayment.LocalAccount.Registration}"));
                //}

                //if (!string.IsNullOrWhiteSpace(thePayment.TicketNumber))
                //{
                //    infoMessage.Append($", ticket is {thePayment.TicketNumber}");
                //}

                if (!string.IsNullOrWhiteSpace(thePayment.TxnNumber))
                {
                    infoMessage.Append($", Trans Number: {thePayment.TxnNumber}");
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), logHeader, () => logMessages.Concat(new[] { infoMessage.ToString(), $"; json: {JsonConvert.SerializeObject(thePayment)}" }).ToArray(), reference: logRefFull, methodName: methodName);
                //workerController?.SendInformation(infoMessage.ToString());
                if (opt != null)
                {
                    var result = Payment(paymentResult,
                        null, //thePayment.Discount, thePayment.LocalAccount,thePayment.TicketNumber,
                        id, message,
                        payment: thePayment);
                    return GetResponseMessage(opt, new PaymentResponse(result));
                }
            }
            else if (request is KioskUseOptNotificationRequest messageNotification)
            {
                // if Pay at Kiosk selected on OPT
                if (opt != null && AllPumps.TryGetPump((byte)messageNotification?.Notification?.Pump, out var thePump))
                {
                    // start the Pay at Kiosk timer if in mixed mode
                    if (opt.Mode == OptModeType.OptModeMixed && !opt.IsNozzleUp(thePump.Number))
                    {
                        DoDeferredLogging(LogLevel.Info, Pump.HeaderPump, () => new[] { $"{thePump.Number}", "Starting pay at kiosk timer" }, reference: logRefFull, methodName: methodName);
                        thePump.StartPayAtKioskPressedTimer(opt.PayAtKioskPressedTimeoutInSeconds);
                    }
                    else
                    {
                        DoDeferredLogging(LogLevel.Warn, logHeader, () => new[] { $"Received NotificationType.KioskUse, but optMode is {opt.Mode}" }, reference: logRefFull, methodName: methodName);
                    }
                }

                var result = OptNotification(message, messageNotification, id);
                return GetResponseMessage(opt, new OptNotificationResponse(result));
            }
            else if (request is GetReceiptRequest messageReceiptRequest)
            {
                if (opt != null)
                {
                    var result = GetReceipts(message, messageReceiptRequest.Receipt.CardNumber, id, out var receipt);
                    return GetResponseMessage(opt, new GetReceiptResponse(result, receipt));
                }
            }
            else if (request is StoreReceiptRequest messageStoreReceipt)
            {
                if (opt != null)
                {
                    var result = StoreReceipt(message, id, messageStoreReceipt.Receipt);
                    return GetResponseMessage(opt, new StoreReceiptResponse(result));
                }
            }
            else if (request is SoftwareRequest messageSoftware)
            {
                DoDeferredLogging(LogLevel.Info, logHeader, () => logMessages.Concat(new[] { $"Version: {messageSoftware.Opt.SoftwareVersion}" }).ToArray(), reference: logRefFull, methodName: methodName);
                workerController?.SendInformation($"Get Software request received from {messageSoftware.Opt.Id}" +
                                                  (string.IsNullOrWhiteSpace(messageSoftware.Opt.SoftwareVersion)
                                                      ? ""
                                                      : $" for software version {messageSoftware.Opt.SoftwareVersion}"));
                if (opt != null)
                {
                    var result = Software(messageSoftware.Opt.SoftwareVersion, opt.SoftwareToSend, out var fileName, out var softwareBase64);
                    if (softwareBase64 != null)
                    {
                        opt.ConfigSent(null);
                        opt.WhitelistSent(null);
                        opt.SignOut();
                        workerController?.PushChange(EventType.OPTChanged, request.Opt.Id);
                    }

                    opt.SoftwareSent();
                    CheckConfigNeeded(opt);

                    workerTelemetry?.SoftwareRequestReceivedFromOpt(opt);

                    return GetResponseMessage(opt, new SoftwareResponse(result, new List<FileDetails>() { new FileDetails(fileName, softwareBase64) }));
                }
            }
            else if (request is AssetRequest)
            {
                workerController?.SendInformation($"Get Asset request received from {request.Opt.Id}");
                if (opt != null)
                {
                    var result = Asset(opt.IsSecureAssetsToSend, opt.AssetToSend, out var fileName, out var assetBase64);
                    opt.AssetSent();
                    CheckConfigNeeded(opt);

                    workerTelemetry?.AssetRequestReceivedFromOpt(opt);

                    return GetResponseMessage(opt, new AssetResponse(result, new List<FileDetails>() { new FileDetails(fileName, assetBase64) }));
                }
            }
            else if (request is GetLocalAccountBalanceRequest messageLocalAccount)
            {
                var details = messageLocalAccount.LocalAccountDetails;
                DoDeferredLogging(LogLevel.Info, logHeader, () => logMessages.Concat(new[] { $"CardNumber: {details.CardNumber}" }).ToArray(), reference: logRefFull, methodName: methodName);
                workerController?.SendInformation($"Get Local Account Balance request received from {request.Opt.Id}" +
                                                  (string.IsNullOrWhiteSpace(details.CardNumber)
                                                      ? ""
                                                      : $" for card number {details.CardNumber}"));
                // TODO: IPaymentIntegratorInJournal
                var localAccountWorker = GetWorker<ILocalAccountWorker>();

                if (opt != null && localAccountWorker != null)
                {
                    var balanceResult = localAccountWorker.GetBalance(details.CardNumber);
                    var getLocalAccountBalanceResponse = balanceResult.IsSuccess
                        ? new GetLocalAccountBalanceResponse(ResultSuccess, balanceResult.Value)
                        : new GetLocalAccountBalanceResponse(ResultFailure, 0, false, false);

                    return GetResponseMessage(opt, getLocalAccountBalanceResponse);
                }
            }
            else if (request is RestartEsocketServiceRequest)
            {
                workerController?.SendInformation($"Restart eSocket.POS service request received from {request.Opt.Id}");
                if (opt != null)
                {
                    GetWorker<IControllerWorker>()?.RestartEsocketService();
                    return GetResponseMessage(opt, new RestartEsocketServiceResponse(ResultSuccess));
                }
            }
            else if (request is ReceiptPrintedRequest messageReceiptPrintedRequest)
            {
                if (opt != null)
                {
                    var result = ReceiptPrinted(message, id, messageReceiptPrintedRequest.Receipt);
                    foreach (var pump in opt.PumpList())
                    {
                        if (AllPumps.TryGetPump(pump, out var thePump))
                        {
                            CheckRemovePumpReserve((p) => !(p.InUse || p.HasReserveTimedOut), thePump, message, "#1");
                        }
                    }
                    return GetResponseMessage(opt, new ReceiptPrintedResponse(result));
                }
            }
            else if (opt != null)
            {
                var result = OptNotification(message, request, id);

                return GetResponseMessage(opt, new OptNotificationResponse(result));
            }

            return null;
        }

        private bool CheckRemovePumpReserve(Func<IPump, bool> action, IPump thePump, IMessageTracking message, string tag, bool forceOverrides = false)
        {
            var check = (thePump.IsReserved || forceOverrides) && action(thePump);
            if (!check)
            {
                return false;
            }

            var logRefFull = message.FullId;

            var result = _pumpWorker.PaymentCancelled(thePump.Number, message);
            if (result.IsSuccess && (thePump.CancelPayment(LoggingReference) || forceOverrides && thePump.ResetPump(LoggingReference)))
            {
                DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{thePump.Number}; Reserve/Lock removed {tag}" }, reference: logRefFull);
            }

            return true;
        }

        private static optModels.Payment GetPayment(PaymentRequest messagePaymentRequest, out PaymentResult result)
        {
            optModels.Payment payment = null;
            result = PaymentResult.Cancelled;

            switch (messagePaymentRequest)
            {
                case PaymentCancelledRequest paymentCancelledRequest:
                    payment = paymentCancelledRequest.Payment;
                    result = PaymentResult.Cancelled;
                    break;
                case PaymentClearedRequest paymentClearedRequest:
                    payment = paymentClearedRequest.Payment;
                    result = PaymentResult.Cleared;
                    break;
                case PaymentApprovedRequest paymentApprovedRequest:
                    payment = paymentApprovedRequest.Payment;
                    result = PaymentResult.Approved;
                    break;
            }

            return payment;
        }

        /// <summary>
        /// End the current Opt transaction once a successful Payment Cleared or Cancelled has been acknowledged
        /// </summary>
        /// <param name="message">Current message</param>
        /// <param name="id">Current Opt id</param>
        /// <returns>Result</returns>
        protected override Result DoOnAfterMessageResponse(IMessageTracking<XElement> message, int id)
        {
            if (MessageParser.ParseXElement(message.Request) is OptRequest optRequest && MessageParser.ParseXElement(message.Response) is OptResponse optResponse)
            {
                if (optResponse is PaymentResponse && optResponse.Result == ResultSuccess &&
                    (optRequest is PaymentRequest paymentRequest && (optRequest is PaymentCancelledRequest || optRequest is PaymentClearedRequest)))
                {
                    if (AllPumps.TryGetPump(paymentRequest.PaymentDetails?.Pump ?? 0, out var thePump))
                    {
                        thePump.ResetTransactionState(message.IdAsString);
                    }
                }
            }

            return Result.Success();
        }

        #region Public Actions

        /// <inheritdoc cref="IPumpIntegratorOutTransient{TMessageTracking}"/>
        public void OnPumpState(PumpStateChange pumpState, IMessageTracking message = default)
        {
            var ps = pumpState;
            var pumpData = ps.PumpData;
            var disp = pumpData.Dispenser;
            var hose = disp.HoseInfo;
            var current = disp.CurrentInfo;
            var hscCurrent = current.ToHscPumpTotals();

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"5.{HeaderStatus}.Current", () => new[] { $"{JsonConvert.SerializeObject(current)}" });
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"5.{HeaderStatus}.HoseInfo", () => new[] { $"{JsonConvert.SerializeObject(hose)}" });

            OnPumpState(message, disp.State.ToPumpState(), ps.Number,
                current.Hose?.Number ?? current.Number,
                current.Hose?.GradeNumber ?? (hose.ContainsKey(current.Number) ? hose[current.Number]?.GradeNumber ?? 0 : (byte)0),
                hscCurrent.Volume, hscCurrent.Cash, hscCurrent.Ppu, ps.IsPaid, ps.Grades.ToList(),
                ps.IsFromTimerEvent, ps.WasPumpBlocked, ps.IsPumpBlocked, disp.IsOptAvailable, disp.IsOptInControl, ps.IsPendingTransactionOnReboot, ps.IsError, ps.ClaimedPos, ps.TransactionSequenceNumber);
        }

        // TODO: Left in as Unit tests referenced
        /// <inheritdoc cref="IFromOptWorker"/>
        public void OnPumpState
        (IMessageTracking message, PumpState state, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, IList<byte> allGrades, bool isTimer = false,
            bool wasBlocked = false, bool isBlocked = false, bool isOptAvailable = false, bool isOptInControl = false, bool isPendingTxnOnReboot = false, bool isError = false, byte pos = 0, int transSeqNum = 0)
        {
            message ??= new MessageTracking();
            AllPumps.UpdateParentId(pump, message);
            var logRefFull = message.FullId;
            AssignLoggingReference(ref logRefFull);

            var incomingState = state;
            AllPumps.TryGetPump(pump, out var thePump);

            DoDeferredLogging(LogLevel.Debug, HeaderBegin, () => null, reference: logRefFull);
            DoDeferredLogging(wasBlocked ? LogLevel.Warn : ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{state}.Pump", () => new[]
            {
                $"{pump}, grade is {grade}," +
                $" volume is {volume}, amount is {amount}, price is {ppu}, paid is {paid}, wasBlocked is {wasBlocked}, isBlocked is {isBlocked}"
            }, reference: logRefFull);


            SetOnPumpStateActive(message, pump, SocketType.FromOpt, true);
            try
            {
                // Double-try!
                if (thePump == null)
                {
                    Thread.Sleep(50);
                    AllPumps.TryGetPump(pump, out thePump);
                    AllPumps.UpdateParentId(pump, message);
                }

                if (thePump == null)
                {
                    DoDeferredLogging(ToLogLevel(LogLevelPumpListMismatch.GetValue()), $"CouldNotRetrieve.{Pump.HeaderPump}", () => new[] { $"{pump}" }, reference: logRefFull);
                }
                else
                {
                    var info = state == PumpState.Request || (volume + amount == 0) ? string.Empty : $", Volume is {volume / 1000.0:F3} litres, Amount is �{amount / 100.0:F2}";
                    NotificationWorker.SendInformation($"Pump State {state} received from Pump {pump}, Hose is {hose}, Grade is {grade}{info}, Ppu is {ppu / 10.0:F1}p / litre{(pos == 0 ? string.Empty : $", Pos: {pos}")}");

                    DoOnPumpState(message, thePump, incomingState, state, pump, hose, grade, volume, amount, ppu, paid, allGrades, isTimer, wasBlocked, isBlocked,
                        isOptAvailable, isOptInControl, isPendingTxnOnReboot, isError, pos, transSeqNum);
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, $"{HeaderException}.{Pump.HeaderPump}", () => new[] { $"{pump}; Failed to process!" }, ex, reference: logRefFull);
            }
            finally
            {
                SetOnPumpStateActive(message, pump, SocketType.FromOpt, false);
            }

            DoDeferredLogging(LogLevel.Debug, HeaderEnd, () => null, reference: logRefFull);
        }

        private void DoOnPumpState(IMessageTracking message, IPump thePump, PumpState incomingState, PumpState state, byte pump, byte hose, byte grade, uint volume, uint amount,
            ushort ppu, bool paid, IList<byte> allGrades, bool isTimer = false, bool wasBlocked = false, bool isBlocked = false, bool isOptAvailable = false,
            bool isOptInControl = false, bool isPendingTxnOnReboot = false, bool isError = false, byte pos = 0, int transSeqNum = 0)
        {
            if (thePump == null)
            {
                return;
            }

            var logRef = message.IdAsString;
            var logRefFull = message.FullId;

            DoDeferredLogging(wasBlocked ? LogLevel.Warn : ToLogLevel(DeveloperLoggingLevelState.GetValue()), "RestartOptions.Pump", () => new[]
            {
                $"{pump}; State: {state}; isPendingTxnOnReboot: {isPendingTxnOnReboot} && isOptInControl: {isOptInControl} && !paid: {!paid}",
                $"IsPaid: {thePump.IsPaid}; OptUse: {thePump.OptUse}; KioskUse: {thePump.KioskUse}; InUseByOpt: {thePump.InUseByOpt}; OptIsClose: {thePump.OptIsClosed}; ",
                $"HasOptPayment: {thePump.HasOptPayment}; HasKioskPayment: {thePump.HasKioskPayment}"
            }, reference: logRefFull);

            if (isPendingTxnOnReboot && isOptInControl && !paid)
            {
                thePump.SetOptPayment(logRef);
                thePump.SetHasKioskPayment(false);
            }

            SetPumpError(message, thePump, isError);

            if ((thePump.KioskUse || thePump.OptUse) && !thePump.OptIsClosed && (state != PumpState.Closed) && (thePump.PumpIsClosed || thePump.ClosePending))
            {
                ProcessPseudoOpenState(message, thePump);
            }

            var useOpt = isOptInControl || thePump.OptUse && thePump.Opt != null;
            var tellOpt = thePump.Opt != null;
            var kioskOnly = thePump.Opt?.Mode == OptModeType.OptModeKioskOnly;

            var gradeName = _gradeHelper.GetGradeInfo(grade);
            var name = gradeName.Name;
            var vatRate = gradeName.VatRate;

            thePump.SetAllGrades(allGrades, logRef);

            thePump.LogTransactionState(incomingState, useOpt, tellOpt, kioskOnly, volume, amount, logRef);

            ProcessPumpState(message, state, pump, hose, grade, volume, amount, ppu, paid, allGrades, wasBlocked, isBlocked, isPendingTxnOnReboot, isError, thePump,
                tellOpt, name, kioskOnly, useOpt, vatRate, pos, transSeqNum, isOptInControl);

            if (!isBlocked && wasBlocked)
            {
                //SendNotificationToOpt(thePump.Opt, new OptUnblockNotificationRequest(), message);

                if (!thePump.PumpIsClosed)
                {
                    SendNotificationToOpt(thePump.Opt, new PumpStateChangedNotificationRequest(thePump.Number, thePump.PumpState), message);
                }
            }

            thePump.LastPumpState = incomingState;
        }

        private void ProcessPumpState(IMessageTracking message, PumpState state, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, IList<byte> allGrades,
            bool wasBlocked, bool isBlocked, bool isPendingTxnOnReboot, bool isError, IPump thePump, bool tellOpt, string name, bool kioskOnly, bool useOpt, float vatRate, byte pos,
            int transSeqNum, bool isOptInControl)
        {
            var prevState = thePump.SetPreviousPumpState();
            switch (state)
            {
                case PumpState.Initialise:
                    ProcessInitialiseState(pump);
                    break;
                case PumpState.Idle:
                    ProcessIdleState(message, pump, hose, grade, volume, amount, ppu, paid, thePump, tellOpt, name, kioskOnly, useOpt, vatRate, wasBlocked, isBlocked, isPendingTxnOnReboot, transSeqNum, isOptInControl);
                    break;
                case PumpState.Reserved:
                    ProcessReserveState(thePump, message);
                    break;
                case PumpState.Request:
                    if (pos > 0)
                    {
                        DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] { $"{pump}; Forcing Idle as Out of sequence PumpState detected, POS: {pos}" });
                        ProcessIdleState(message, pump, hose, grade, volume, amount, ppu, paid, thePump, tellOpt, name, kioskOnly, useOpt, vatRate, wasBlocked, isBlocked, isPendingTxnOnReboot, transSeqNum, isOptInControl);
                    }
                    ProcessRequestState(message, pump, grade, ppu, allGrades, thePump, tellOpt, name, isError);
                    break;
                case PumpState.Authorise:
                    ProcessAuthoriseState(message, pump, hose, grade, volume, amount, ppu, paid, useOpt, thePump, vatRate, name, tellOpt, isError, isOptInControl);
                    break;
                case PumpState.Delivering:
                    ProcessDeliveringState(message, pump, hose, grade, volume, amount, ppu, paid, thePump, useOpt, vatRate, name, tellOpt, kioskOnly, isError, isOptInControl);
                    break;
                case PumpState.Finished:
                    ProcessFinishedState(message, thePump, kioskOnly, isError);
                    break;
                case PumpState.Closed:
                    ProcessClosedState(message, thePump);
                    break;
            }

            CheckPumpState(thePump, prevState, message);
        }

        private void SetPumpError(IMessageTracking message, IPump thePump, bool isError)
        {
            if (!isError)
            {
                return;
            }

            SetTransactionPumpError(message, thePump, true);
        }

        private void CheckErrorState(IMessageTracking message, IPump thePump, bool isError)
        {
            if (isError || !thePump.TransactionSummary.HasPumpError)
            {
                return;
            }

            SetTransactionPumpError(message, thePump, false);
        }

        private void SetTransactionPumpError(IMessageTracking message, IPump thePump, bool isPumpError)
        {
            var txnSummary = thePump?.TransactionSummary;
            if (txnSummary == null)
            {
                return;
            }

            thePump.TransactionSummary.HasPumpError = isPumpError;
            var enabling = isPumpError ? "Enabling" : "Disabling";
            DoDeferredLogging(LogLevel.Info, $"{enabling}.ErrorState.{Pump.HeaderPump}", () => new[] { $"{thePump.Number}" }, reference: message.FullId);
        }

        private void ProcessInitialiseState(byte pump)
        {
        }

        private void ProcessPseudoOpenState(IMessageTracking message, IPump thePump)
        {
            DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{thePump.Number}" });

            var prevState = thePump.SetPreviousPumpState();
            thePump.OpenPump(reference: message.IdAsString);
            CheckPumpState(thePump, prevState, message);
        }

        private void ProcessClosedState(IMessageTracking message, IPump thePump)
        {
            thePump.ClosePump(reference: message.IdAsString);
        }

        private void ProcessReserveState(IPump thePump, IMessageTracking message)
        {
            DoProcessKioskUse(thePump, message, pumpInstigated: false, isMobile: true);
        }

        private void ProcessAuthoriseState(IMessageTracking message, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, bool useOpt, IPump thePump,
            float vatrate, string name, bool tellOpt, bool isError, bool isOptInControl)
        {
            var logRef = message.IdAsString;
            CheckErrorState(message, thePump, isError);

            if (useOpt && thePump.HasOptPayment && !paid)
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}",
                    () => new[] { $"{pump}", "Forcing Delivered (from Authorise)...No longer allowed, now preventing Delivered!" }, reference: message.FullId);
            }

            var tq = thePump.TransactionSummary;
            var allConnected = thePump.Opt?.AllConnected ?? false;
            var pumpInfo = $"{pump}; {HeaderOpt}: {thePump.Opt?.Id} ({thePump.Opt?.Id})";
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"OPTOfflineCheck.{Pump.HeaderPump}",
                () => new[]
                {
                    pumpInfo,
                    $"Opt.AllConnected: {allConnected}; isOptInControl: {isOptInControl};",
                    $"IsDefaultState: {tq.IsDefaultKioskState()}",
                    $"json: {JsonConvert.SerializeObject(tq)}"
                }, reference: message.FullId);
            if (!allConnected && !isOptInControl && tq.IsDefaultKioskState())
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] { pumpInfo, "Forcing KioskUse whilst OPT Offline" }, reference: message.FullId);
                thePump.SetKioskUse(logRef);
            }
            else if (!(isOptInControl || thePump.InUseByOpt || thePump.KioskUse || thePump.HasOptPayment || thePump.HasKioskPayment))
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] { pumpInfo, "Forcing KioskUse from Mixed (Authorise)" }, reference: message.FullId);
                DoProcessKioskUse(thePump, message, false);
            }

            if (useOpt)
            {
                thePump.SetPaid(paid, logRef);
            }

            thePump.Authorised(logRef);
            // Don't update pump state here, as OPT is not expecting it: the authorise should be for whatever mode it is already in. State will remain the same during delivery.

            if (thePump.Opt != null)
            {
                CheckOptConfig(thePump.Opt, true);
            }

            if (tellOpt)
            {
                SendNotificationToOpt(thePump.Opt, new TakeFuelNotificationRequest(pump, grade, name, ppu), message);
            }
        }

        private void ProcessIdleState(IMessageTracking message, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, IPump thePump, bool tellOpt,
            string name, bool kioskOnly, bool useOpt, float vatRate, bool wasBlocked, bool isBlocked, bool isPendingTxnOnReboot, int transSeqNum, bool isOptInControl)
        {
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;

            var prevState = thePump.SetPreviousPumpState();

            var sendEndTransactionState = false;

            var workerPos = _posWorker;

            if (tellOpt && paid && !thePump.IsPaid && thePump.HasKioskPayment)
            {
                SendNotificationToOpt(thePump.Opt, new PaidAtKioskNotificationRequest(pump), message);

                var allConnected = thePump.Opt?.AllConnected ?? false;
                var pumpInfo = $"{pump}; {HeaderOpt}: {thePump.Opt?.Id} ({thePump.Opt?.Id})";
                if (!allConnected) // Can't wait for ACK to reset as OPT dis-connected, but Kiosk transactions can continue!!!
                {
                    DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] { pumpInfo, "Forcing TQ reset whilst OPT Offline" }, reference: logRefFull);
                    thePump.ResetTransactionState(logRef);
                }

                if (kioskOnly || thePump.HasKioskPayment) //Kiosk mode check ala hydaemvopt
                {
                    sendEndTransactionState = true;
                }

                workerPos?.StatusResponse(thePump.Number, message); // Second HydraEMVOPT message to confirm payment
            }

            thePump.SetPaid(paid, logRef);

            if (sendEndTransactionState)
            {
                workerPos?.StatusResponse(thePump.Number, message); // Final transaction message in case kiosk mode
            }

            if (useOpt && thePump.HasOptPayment)
            {
                if (amount == 0)
                {
                    // TODO: Change HSC to _pumpWorker.LogHeader, when this has been added
                    DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{pump}", "Paying zero-value transaction from HSC" }, reference: logRefFull);
                }

                var validatorResult = Result.Success();
                if (isPendingTxnOnReboot)
                {
                    DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{pump}", "Validation not required as Pending Transaction following ReBoot encountered" }, reference: logRefFull);
                }
                else
                {
                    thePump.TransactionSummary.LocalEndTime = DateTime.UtcNow;
                    validatorResult = _pumpTransactionValidator.ValidateTransaction(thePump.TransactionSummary, logRef);
                }

                if (thePump.IsPaid || !isOptInControl)
                {
                    ManageDelivered(message, pump, hose, grade, 0, 0, name, ppu, vatRate, thePump, paid, transSeqNum, true);
                }
                else
                {
                    ManageDelivered(message, pump, hose, grade, volume, amount, name, ppu, vatRate, thePump, paid, transSeqNum,
                        !validatorResult.IsSuccess && _pumpTransactionValidatorActions.IsTransactionCancelled);

                    if (!validatorResult.IsSuccess && !(thePump.TransactionSummary?.HasBeenActioned ?? true))
                    {
                        if (_pumpTransactionValidatorActions.IsPumpStopped)
                        {
                            _pumpTransactionValidator?.GetLogger().Warn($"{HeaderOnPumpState}.{Pump.HeaderPump}", () => new[]
                            {
                                $"{pump}",
                                $"TransactionQuality: Sending notification, Type is ANPR_Stop", $"{HeaderLoggingReference}: {logRefFull}"
                            });
                            _pumpWorker.EmergencyStop(pump, $"Pump{pump}", message);

                            if (_pumpTransactionValidatorActions.IsOptStopped)
                            {
                                _pumpTransactionValidator?.GetLogger().Warn($"{HeaderOnPumpState}.{Pump.HeaderPump}", () => new[]
                                {
                                    $"{pump}",
                                    $"TransactionQuality: Sending notification to OPT {thePump.Opt?.IdString}, Type is {GetShortNotificationName(thePump.Opt?.GetCurrentNotification())}",
                                    $"{HeaderLoggingReference}: {logRefFull}"
                                });

                                SendNotificationToOpt(thePump.Opt, new OptBlockNotificationRequest(), message);
                            }
                        }

                        thePump.TransactionSummary?.SetActionsComplete();
                    }
                }
            }
            else if (useOpt && thePump.IsNozzleUp)
            {
                DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{pump}", "Validation not required as No OptPayment" }, reference: logRefFull);

                if (tellOpt)
                {
                    SendNotificationToOpt(thePump.Opt, new NozzleDownNotificationRequest(pump, grade, name, ppu), message);
                }

                if (thePump.HasPayment)
                {
                    thePump.ResetPaymentTimeout(_paymentTimeoutDictionary[PaymentTimeoutType.NozzleDown], logRef);
                }
            }
            else if (tellOpt && thePump.HasKioskPayment)
            {
                DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{pump}", "Validation not required as Kiosk transaction" }, reference: logRefFull);
            }

            CheckRemovePumpReserve((p) => useOpt && p.OptUse && p.InUse && !p.HasPayment && !p.IsDelivering && p.IsPaid && p.HasReserveTimedOut, thePump, message, "#3");

            thePump.Idle(logRef);
            HydraDb.ClearOptPayment(pump, message);

            if (thePump.Opt != null)
            {
                CheckOptConfig(thePump.Opt, !paid);

                if (sendEndTransactionState)
                {
                    NotificationWorker.SendInformation($"Setting pump {thePump.Number} to Mixed");
                }
            }

            AllPumps.UpdateParentId(pump, message);
        }

        private void ManageDelivered(IMessageTracking message, byte pump, byte hose, byte grade, uint volume, uint amount, string name, ushort ppu, float vatRate,
            IPump thePump, bool isPaid, int transSeqNum, bool isTqFailure = false)
        {
            _pumpWorker.PaymentCleared(pump, amount, isPaid && isTqFailure || amount == 0, message, transSeqNum);

            if (isTqFailure)
            {
                amount = 0;
                volume = 0;
            }

            var (vat, net) = _vatCalculator.CalculateVat(amount, vatRate);

            thePump.SetDeliveredHose(hose, ppu);
            thePump.Delivered(true);
            HydraDb.SetDelivered(message, pump, grade, volume, amount, name, ppu, net, vat, vatRate, transSeqNum, hose);

            SendNotificationToOpt(thePump.Opt, new DeliveredNotificationRequest(pump, grade, volume, amount, name, ppu, net, vat, vatRate), message);
        }

        private void ProcessRequestState(IMessageTracking message, byte pump, byte grade, ushort ppu, IList<byte> allGrades, IPump thePump, bool tellOpt, string name, bool isError)
        {
            var logRef = message.IdAsString;

            CheckErrorState(message, thePump, isError);

            if (grade > 0)
            {
                thePump.Requesting(new List<byte> { grade }, logRef);
            }
            else
            {
                thePump.Requesting(allGrades, logRef);
            }

            if (thePump.IsAuthorised(out uint authAmount, logRef) && _pumpWorker.PaymentApproved(thePump.Number, authAmount, message).IsSuccess)
            {
                HydraDb.SetOptPayment(pump, message);
            }
            else
            {
                if (!(thePump.InUseByOpt || thePump.InUse) && thePump.PumpState == PumpStateType.Mixed && HydraDb.AdvancedConfig.NozzleUpForKioskUse)
                {
                    DoProcessKioskUse(thePump, message);
                }

                if (thePump.IsKioskMode)
                {
                    DoDeferredLogging(LogLevel.Info, $"{Pump.HeaderPump}", () => new[] { $"{pump}", "PumpNotAuthorised: ", thePump.GetNotAuthorisedReasons() }, reference: message.FullId);
                }
            }

            if (tellOpt)
            {
                DoDeferredLogging(LogLevel.Info, $"OPT: {thePump.Opt?.IdString}; ({thePump.Opt?.Id}) {Pump.HeaderPump}", () => new[] { $"{pump}", "Clearing pay at kiosk timer" }, reference: message.FullId);

                thePump.ClearPayAtKioskPressedTimer();
                SendNotificationToOpt(thePump.Opt, new NozzleUpNotificationRequest(pump, grade, name, ppu), message);
            }
        }

        private void ProcessFinishedState(IMessageTracking message, IPump thePump, bool kioskOnly, bool isError)
        {
            CheckErrorState(message, thePump, isError);

            thePump.Delivered(reference: message.IdAsString);
            if (!kioskOnly) //Kiosk mode check ala hydaemvopt
            {
                _posWorker?.StatusResponse(thePump.Number, message);
            }
        }

        private void ProcessDeliveringState(IMessageTracking message, byte pump, byte hose, byte grade, uint volume, uint amount, ushort ppu, bool paid, IPump thePump, bool useOpt,
            float vatrate, string name, bool tellOpt, bool kioskOnly, bool isError, bool isOptInControl)
        {
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;

            CheckErrorState(message, thePump, isError);

            var pumpInfo = $"{pump}; {HeaderOpt}: {thePump.Opt?.Id} ({thePump.Opt?.Id})";
            if (!(isOptInControl || thePump.InUseByOpt || thePump.KioskUse || thePump.HasOptPayment || thePump.HasKioskPayment))
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] { pumpInfo, "Forcing KioskUse from Mixed (Delivering)" }, reference: message.FullId);
                DoProcessKioskUse(thePump, message, false);
            }

            // Have seen lack of authorise call from Site Controller when pump has been lifted.
            if (thePump.LastPumpState == PumpState.Request)
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}",
                    () => new[] { $"{pump}", $"Forcing Authorise (from Delivering); kioskOnly: {kioskOnly}; useOpt: {useOpt}" }, reference: logRefFull);
                thePump.SetPaid(paid, logRef);
                thePump.Authorised(logRef);
            }

            if (useOpt && thePump.HasOptPayment && !paid)
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}",
                    () => new[] { $"{pump}", $"Forcing Delivered (from Delivering)...No longer allowed, now preventing Delivered! kioskOnly: {kioskOnly}; useOpt: {useOpt}" }, reference: logRefFull);
            }

            //PumpStateType prevState = thePump.PumpState;
            if (useOpt)
            {
                thePump.SetPaid(paid, logRef);
            }

            thePump.Delivering(logRef);
            // Don't update pump state here, as it is delivering. State will remain the same during delivery.
            // Changing pump state here would return the OPT to idle screen.

            if (thePump.Opt != null)
            {
                CheckOptConfig(thePump.Opt, true);
            }

            if (tellOpt)
            {
                SendNotificationToOpt(thePump.Opt, new DeliveringNotificationRequest(pump, grade, name, ppu), message);
            }

            if (!kioskOnly) //Kiosk mode check ala hydaemvopt
            {
                _posWorker?.StatusResponse(thePump.Number, message);
            }
        }

        public void FetchGenericOptConfig(bool checkEsocket = false, bool forceRefresh = false)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);

            lock (_genericOptConfigSyncObject)
            {
                if (!forceRefresh && (_cacheHelper?.IsCacheValid ?? false) && ((_genericOptConfig != null) && (DateTime.UtcNow <= _lastGenericOptConfigCheck.AddMinutes(_checkGenericOptConfigIntervalInMinutes))))
                {
                    return;
                }

                if (forceRefresh)
                {
                    _cacheHelper.ForceExpirationOnAllItems();
                }

                if (_paymentConfig == null)
                {
                    return;
                }

                if (checkEsocket)
                {
                    _paymentConfig?.CheckTermProcCategory(LoggingReference);
                }

                GenericOptConfig = new GenericOptConfig(_paymentConfig, HydraDb, MyIdString, LogManager, ConfigurationManager) { IgnoreMessageId = ConnectionThread.IgnoreMessageIdTracking?.GetValue() ?? true };

                if (!_paymentConfig?.Cards.Any() ?? false)
                {
                    DoDeferredLogging(LogLevel.Warn, "No contactless cards found");
                }

                _lastGenericOptConfigCheck = DateTime.UtcNow;
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void FetchMediaFilesList()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            _mediaFilesList.Clear();
            try
            {
                IList<string> files = Directory.GetFiles(MediaDirectory).Select(x => x.Replace(MediaDirectory, string.Empty).Trim())
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                foreach (string file in files)
                {
                    _mediaFilesList.Add(file);
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, $"Error fetching media files list from {MediaDirectory}", exception: ex);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void FetchPlaylistFilesList()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            _playlistFilesList.Clear();
            try
            {
                IList<string> files = Directory.GetFiles(PlaylistDirectory).Select(x => x.Replace(PlaylistDirectory, string.Empty).Trim())
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                foreach (string file in files)
                {
                    _playlistFilesList.Add(file);
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, $"Error fetching playlist files list from {PlaylistDirectory}", exception: ex);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void FetchAvailableSoftware()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            AvailableSoftware.Clear();
            AvailableSecureAssets.Clear();
            AvailableCpatAssets.Clear();
            try
            {
                IList<string> softwareFiles = Directory.GetFiles(SoftwareDirectory, OptAppPrefix + "*" + SoftwareSuffix)
                    .Select(x => x.Substring(SoftwareDirectory.Length + OptAppPrefix.Length,
                        x.Length - SoftwareDirectory.Length - OptAppPrefix.Length - SoftwareSuffix.Length))
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                IList<string> secureAssetsFiles = Directory.GetFiles(SoftwareDirectory, SecureAssetsPrefix + "*" + SoftwareSuffix)
                    .Select(x => x.Substring(SoftwareDirectory.Length + SecureAssetsPrefix.Length,
                        x.Length - SoftwareDirectory.Length - SecureAssetsPrefix.Length - SoftwareSuffix.Length))
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                IList<string> cpatAssetsFiles = Directory.GetFiles(SoftwareDirectory, CpatAssetsPrefix + "*" + SoftwareSuffix)
                    .Select(x => x.Substring(SoftwareDirectory.Length + CpatAssetsPrefix.Length,
                        x.Length - SoftwareDirectory.Length - CpatAssetsPrefix.Length - SoftwareSuffix.Length))
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                foreach (string file in softwareFiles)
                {
                    AvailableSoftware.Add(file);
                }

                foreach (string file in secureAssetsFiles)
                {
                    AvailableSecureAssets.Add(file);
                }

                foreach (string file in cpatAssetsFiles)
                {
                    AvailableCpatAssets.Add(file);
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, $"Error fetching available software from {SoftwareDirectory}", exception: ex);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CheckOptConfig(IOpt opt, bool suppressModeChange = false, [CallerMemberName] string caller = "")
        {
            DoDeferredLogging(LogLevel.Debug, $"{HtecLoggingConstants.EntryMessage}.Start Caller: {caller}");

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderInformation, () => new[]
            {
                $"Opt: {opt?.IdString}; InUse: {opt?.InUse}; Connected: {opt?.Connected}; SignedIn: {opt?.SignedIn}; Is{nameof(UnmannedPseudoPos)}: {UnmannedPseudoPos}",
                $"ModeChangePending:  {opt?.ModeChangePending}; SuppressModeChange: {suppressModeChange}; ConfigNotificationPending: {opt?.ConfigNotificationPending}; " +
                $"Mode: {opt?.Mode}; InKioskUse: {opt?.InKioskUse}",
            });

            // ReSharper disable once MergeSequentialChecksWhenPossible
            if (GetWorker<IControllerWorker>().IsConfigBatch || opt == null || opt.InUse || !opt.Connected || !opt.SignedIn)
            {
                return;
            }

            lock (_checkConfigLock)
            {
                if (ConfigChanged(opt))
                {
                    opt.ConfigNeeded();
                }

                if (WhitelistChanged(opt))
                {
                    opt.WhitelistNeeded();
                }

                if (LayoutChanged(opt))
                {
                    opt.LayoutNeeded();
                }

                if (MediaFilesListChanged(opt))
                {
                    opt.MediaUpdateNeeded();
                }

                if (opt.SoftwareNotificationPending)
                {
                    SendNotificationToOpt(opt, new SoftwarePendingNotificationRequest());
                    opt.SoftwarePendingSent();
                }

                if (opt.AssetNotificationPending)
                {
                    SendNotificationToOpt(opt, new AssetPendingNotificationRequest());
                    opt.AssetPendingSent();
                }

                if (opt.ConfigNotificationPending)
                {
                    SendConfigPendingNotification(opt);
                }
                else if (opt.ModeChangePending && !suppressModeChange)
                {
                    var modeChange = false;

                    var optMode = NotificationOptMode.Opt;
                    if (opt.Mode == OptModeType.OptModeOpt)
                    {
                        modeChange = true;
                    }
                    else if (opt.Mode == OptModeType.OptModeKioskOnly)
                    {
                        optMode = NotificationOptMode.KioskOnly;
                        modeChange = true;
                    }
                    else if (opt.Mode == OptModeType.OptModeMixed && !opt.InKioskUse)
                    {
                        optMode = NotificationOptMode.Mixed;
                        modeChange = true;
                    }
                    else if (!opt.InKioskUse)
                    {
                        modeChange = true;
                    }

                    if (modeChange)
                    {
                        SetPaymentTimeout(opt);

                        var isMobile = opt.PumpList().Any(x => AllPumps.TryGetPump(x, out var thePump)
                                                               && (thePump?.IsMobile ?? false));

                        SendNotificationToOpt(opt, new ModeChangeNotificationRequest((optModels.NotificationOptMode)optMode, isMobile));
                        opt.ModeChangeSent();
                    }
                }

                if (opt.WhitelistNotificationPending)
                {
                    SendNotificationToOpt(opt, new WhitelistPendingNotificationRequest());
                    opt.WhitelistPendingSent();
                }

                if (opt.LayoutNotificationPending)
                {
                    SendNotificationToOpt(opt, new LayoutPendingNotificationRequest());
                    opt.LayoutPendingSent();
                }

                if (opt.MediaUpdateNotificationPending)
                {
                    SendNotificationToOpt(opt, new MediaUpdatePendingNotificationRequest());
                    opt.MediaUpdatePendingSent();
                }
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SendConfigPendingNotification(IOpt opt)
        {
            SendNotificationToOpt(opt, new ConfigPendingNotificationRequest());
            opt.ConfigPendingSent();
        }

        public void SendRequestLogNotification(IOpt opt)
        {
            CheckLogFileRequest(opt);
        }

        public void EngineerResetOptNotification(IOpt opt)
        {
            SendNotificationToOpt(opt, new DeleteFilesNotificationRequest());
        }

        public void CheckPumpState(IPump pump, optModels.PumpStateType prevState, IMessageTracking message = null, bool sendPushChangeNotifications = true)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                var changed = (pump?.Opt != null && pump.PumpState != prevState);

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), Pump.HeaderPump, () => new[]
                {
                    $"{pump.Number}; Previous: {prevState}; Current: {pump.PumpState}; IsInPodMode: {(pump.Opt?.IsInPodMode ?? false)}; changed: {changed}"
                });

                if (changed && ((pump.Opt?.IsInPodMode ?? false) || pump.PumpState == PumpStateType.Open || pump.PumpState == PumpStateType.Closed || (pump.PumpState != PumpStateType.Closed && prevState == PumpStateType.Closed)))
                {
                    DoDeferredLogging(LogLevel.Info, $"Sending.PumpStateChanged.{pump.PumpState}.{Pump.HeaderPump}", () => new[] { $"{pump.Number}" }, reference: message.FullId);

                    SendNotificationToOpt(pump.Opt, new PumpStateChangedNotificationRequest(pump.Number, pump.PumpState), message);
                }

                if (changed || sendPushChangeNotifications)
                {
                    PushChangePumpStateChanged(pump, pushPump: true);
                }
            }, message.FullId);
        }

        public void CheckEsocketChanges()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            DoDeferredLogging(LogLevel.Info, "Checking eSocket.POS changes");
            var esocketEndpoints = _paymentConfig?.EndPoints;
            if (esocketEndpoints != null)
            {
                _currentEsocketEndPoints = esocketEndpoints.Select(x => new connGenericEndPoint(x.Item1, x.Item2));
                foreach (var opt in AllOpts.AllOpts)
                {
                    CheckOptConfig(opt);
                }
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        private void ReloadWorkers()
        {
            var workerController = GetWorker<IControllerWorker>();
            workerController.SendInformation("Restarting connections for new end points");

            workerController.ReloadListener();
            DoActionOnWorkers<IConnectableWorker>((cw) => cw.Restart(), LoggingReference);
        }

        protected override Result DoReloadValidateEndPoint(GenericEndPoint oldEndPoint, GenericEndPoint newEndPoint)
        {
            return Result.SuccessIf((OldEndPoints?.ValidChange(CurrentEndPoints, true, Logger) ?? true) || (OldEndPoints?.ValidChange(CurrentEndPoints, false, Logger) ?? true), "Invalid EndPoint");
        }

        protected override Result DoRestart()
        {
            var result = base.DoRestart();
            if (!result.IsSuccess)
            {
                return result;
            }

            ReloadWorkers();

            FetchGenericOptConfig(forceRefresh: true);

            FetchAvailableSoftware();
            FetchMediaFilesList();
            FetchPlaylistFilesList();
            AllPumps.AllocatePumps(_paymentConfig?.CheckGetAllPumpTids(LoggingReference));

            SetGradeNames();

            CheckEsocketChanges();

            foreach (var opt in AllOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                opt.WhitelistCheckRequired();
                opt.MediaUpdateCheckRequired();
                CheckOptConfig(opt);
            }

            return result;
        }

        public void SetPaymentTimeout(PaymentTimeoutType mode, int timeout)
        {
            DoAction(() =>
            {
                _paymentTimeoutDictionary[mode] = timeout;
                foreach (var opt in AllOpts.AllOpts)
                {
                    SetPaymentTimeout(opt);
                }

                CheckPaymentTimeouts();

            }, null);
        }

        public void SetPaymentTimeout(IOpt opt, OptModeType? modeOverride = null)
        {
            DoAction(() =>
            {
                if (opt == null)
                {
                    return;
                }

                var mode = modeOverride ?? opt.Mode;
                var podModeTimeOut = opt.IsInPodMode ? GetPaymentTimeout(PaymentTimeoutType.Pod) : 0;

                switch (mode)
                {
                    case OptModeType.OptModeKioskOnly:
                        opt.PayAtKioskPressedTimeoutInSeconds = GetPaymentTimeout(PaymentTimeoutType.Kiosk) + podModeTimeOut;
                        break;

                    case OptModeType.OptModeOpt:
                        opt.SetPaymentTimeoutInSeconds(GetPaymentTimeout(PaymentTimeoutType.Opt) + podModeTimeOut);
                        break;

                    case OptModeType.OptModeMixed:
                        opt.SetPaymentTimeoutInSeconds(GetPaymentTimeout(PaymentTimeoutType.Mixed) + podModeTimeOut);
                        break;
                }
            }, LoggingReference);
        }

        public void SetAutoAuth(bool isOn)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (AutoAuth != isOn)
            {
                var message = new MessageTracking();

                AutoAuth = isOn;
                foreach (IPump pump in AllPumps.AllPumps)
                {
                    _posWorker?.StatusResponse(pump.Number, message);
                }

                HydraDb.SetAutoAuth(MyIdString, isOn);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetMediaChannel(bool isOn)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (MediaChannel != isOn)
            {
                MediaChannel = isOn;

                HydraDb.SetMediaChannel(MyIdString, isOn);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetUnmannedPseudoPos(bool isOn)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (UnmannedPseudoPos != isOn)
            {
                var message = new MessageTracking();

                UnmannedPseudoPos = isOn;
                AllPumps.SetUnmannedPseudoPos(UnmannedPseudoPos);
                foreach (var pump in AllPumps.AllPumps)
                {
                    _posWorker?.StatusResponse(pump.Number, message);
                }

                foreach (var opt in AllOpts.AllOpts)
                {
                    CheckOptConfig(opt);
                }

                HydraDb.SetUnmannedPseudoPos(MyIdString, isOn);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetAsdaDayEndReport(bool isAsda)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (AsdaDayEndReport != isAsda)
            {
                AsdaDayEndReport = isAsda;

                HydraDb.SetAsdaDayEndReport(MyIdString, isAsda);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        [Obsolete("Use IGradeHelper direct!")]
        public void SetGradeName(byte number, string name, float vatRate)
        {
            _gradeHelper.SetGrade(number, name, vatRate, LoggingReference);
        }

        [Obsolete("Use IGradeHelper direct!")]
        public void SetGradeName(byte number, string name)
        {
            _gradeHelper.UpdateGrade(number, name, LoggingReference);
        }

        [Obsolete("Use IGradeHelper direct!")]
        public void SetGradeName(byte number, float vatRate)
        {
            _gradeHelper.UpdateGrade(number, vatRate, LoggingReference);
        }

        [Obsolete("Use IGradeHelper direct!")]
        public void SetGradeName(byte number)
        {
            _gradeHelper.RemoveGrade(number, LoggingReference);
        }

        public void Redirect(string hydraId)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            _redirectHydraId = hydraId;
            foreach (var opt in AllOpts.AllOpts)
            {
                SendNotificationToOpt(opt, new ConfigPendingNotificationRequest());
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void RestartAll()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            foreach (var opt in AllOpts.AllOpts)
            {
                SendNotificationToOpt(opt, new RestartNotificationRequest());
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            _isOptServiceDiverted = true;
            _divertedServiceAddress = address;
            _divertedFromOptPort = fromOptPort;
            _divertedToOptPort = toOptPort;
            _divertedHeartbeatPort = heartbeatPort;
            _divertedMediaChannelPort = mediaChannelPort;
            HydraDb.SetDivertOpt(address, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort);
            HydraDb.SetOptDiverted();
            foreach (var opt in AllOpts.AllOpts)
            {
                CheckOptConfig(opt);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CancelDivertOptService()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            _isOptServiceDiverted = false;
            HydraDb.SetOptNotDiverted();
            foreach (var opt in AllOpts.AllOpts)
            {
                CheckOptConfig(opt);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public bool IsOptServiceDiverted
            (out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort, out int mediaChannelPort)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            address = _divertedServiceAddress;
            fromOptPort = _divertedFromOptPort;
            toOptPort = _divertedToOptPort;
            heartbeatPort = _divertedHeartbeatPort;
            mediaChannelPort = _divertedMediaChannelPort;

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return _isOptServiceDiverted;
        }

        /// <inheritdoc/>
        public void SendDayEndNotification(IEnumerable<CardAmountSalesItem> cardAmountItems, int shiftNumber)
        {
            DoAction(() =>
            {
                foreach (var opt in AllOpts.AllOpts)
                {
                    SendNotificationToOpt(opt, new EndOfDayNotificationRequest(cardAmountItems.Select(x => new CardAmountItem(x.CardProductName, x.Amount)).ToList(), shiftNumber));
                }
            }, LoggingReference);
        }

        public bool RestartOpt(string optId)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            IOpt opt = AllOpts.GetOptForIdString(optId);
            if (opt == null)
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return false;
            }

            SendNotificationToOpt(opt, new RestartNotificationRequest());

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return true;
        }

        public void CheckConfigNeeded(IOpt opt = null)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            lock (_checkConfigs)
            {
                if (opt == null)
                {
                    foreach (IOpt theOpt in AllOpts.AllOpts)
                    {
                        _checkConfigs.Add(theOpt);
                    }
                }
                else
                {
                    _checkConfigs.Add(opt);
                }
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public IEnumerable<IPAddress> GetAllIpAddresses()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            var addresses = new List<IPAddress>();
            foreach (var opt in AllOpts.AllOptsAllConnected)
            {
                var address = GetOptIpAddress(opt);
                if (address != null)
                {
                    addresses.Add(address);
                }
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return addresses;
        }

        #endregion

        #region Local Calculations

        public void CheckWhitelistFiles()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            foreach (var opt in AllOpts.AllOpts)
            {
                opt.WhitelistCheckRequired();
                CheckOptConfig(opt);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CheckLayoutFiles()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            foreach (var opt in AllOpts.AllOpts)
            {
                opt.LayoutCheckRequired();
                CheckOptConfig(opt);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CheckContactlessProperties()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            FetchGenericOptConfig();
            foreach (var opt in AllOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                CheckOptConfig(opt);
            }

            NotificationWorker.PushChange(EventType.GenericOptConfigChanged);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CheckMediaFiles()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            FetchMediaFilesList();
            foreach (var opt in AllOpts.AllOpts)
            {
                opt.MediaUpdateCheckRequired();
                CheckOptConfig(opt);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void CheckSoftwareFiles()
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            FetchAvailableSoftware();
            NotificationWorker.PushChange(EventType.OPTChanged);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        private void CheckPaymentTimeouts()
        {
            DoAction(() =>
            {
                FetchGenericOptConfig();
                foreach (var opt in AllOpts.AllOpts)
                {
                    opt.ConfigCheckRequired();
                    CheckOptConfig(opt);
                }

                NotificationWorker.PushChange(EventType.GenericOptConfigChanged);
            }, LoggingReference);
        }

        public void SetWhitelistDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            WhitelistDirectory = directory;

            HydraDb.SetWhitelistDirectory(WhitelistDirectory);
            CheckWhitelistFiles();
            GetWorker<IConfigUpdateWorker>().SetWhitelistDirectory(WhitelistDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetLayoutDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            LayoutDirectory = directory;

            HydraDb.SetLayoutDirectory(LayoutDirectory);
            CheckLayoutFiles();
            GetWorker<IConfigUpdateWorker>().SetLayoutDirectory(LayoutDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetSoftwareDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            SoftwareDirectory = directory;

            HydraDb.SetSoftwareDirectory(SoftwareDirectory);
            GetWorker<IConfigUpdateWorker>().SetSoftwareDirectory(SoftwareDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetMediaDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            MediaDirectory = directory;

            HydraDb.SetMediaDirectory(MediaDirectory);
            CheckMediaFiles();
            GetWorker<IConfigUpdateWorker>().SetMediaDirectory(MediaDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetPlaylistDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            PlaylistDirectory = directory;

            HydraDb.SetPlaylistDirectory(PlaylistDirectory);
            GetWorker<IConfigUpdateWorker>().SetPlaylistDirectory(PlaylistDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        public void SetOptLogFileDirectory(string directory)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return;
            }

            OptLogFileDirectory = directory;

            HydraDb.SetOptLogFileDirectory(OptLogFileDirectory);
            GetWorker<IConfigUpdateWorker>().SetOptLogsDirectory(OptLogFileDirectory);
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
        }

        private string Whitelist(out WhitelistResponse whitelist)
        {
            var files = new List<FileDetails>();
            try
            {
                IList<string> fileNames = Directory.GetFiles(WhitelistDirectory)
                    .Select(x => x.Replace(WhitelistDirectory, string.Empty).Trim())
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                if (fileNames.Any())
                {
                    foreach (var fileName in fileNames)
                    {
                        DoDeferredLogging(LogLevel.Debug, "Filename", () => new[] { fileName });
                        files.Add(new FileDetails(fileName, Convert.ToBase64String(File.ReadAllBytes(WhitelistDirectory + fileName))));
                    }
                }

                whitelist = new WhitelistResponse(ResultSuccess, files);
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Folder: {LayoutDirectory}", ex.Message }, ex);
                whitelist = new WhitelistResponse(ResultFailure, new List<FileDetails>());
            }

            return whitelist.Result;
        }

        private string Layout(out LayoutResponse layout)
        {
            var files = new List<FileDetails>();
            try
            {
                var fileNames = Directory.GetFiles(LayoutDirectory)
                    .Select(x => x.Replace(LayoutDirectory, string.Empty).Trim())
                    .Where(x => !string.IsNullOrWhiteSpace(x)).ToArray();
                if (fileNames.Any())
                {
                    foreach (var fileName in fileNames)
                    {
                        DoDeferredLogging(LogLevel.Debug, "Filename", () => new[] { fileName });
                        files.Add(new FileDetails(fileName, Convert.ToBase64String(File.ReadAllBytes(LayoutDirectory + fileName))));
                    }
                }

                layout = new LayoutResponse(ResultSuccess, files);
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Folder: {LayoutDirectory}", ex.Message }, ex);
                layout = new LayoutResponse(ResultFailure, new List<FileDetails>());
            }

            return layout.Result;
        }

        private string Software(string previousSoftwareVersion, string newSoftwareVersion, out string fileName, out string softwareBase64)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            DoDeferredLogging(LogLevel.Info, $"Constructing Software Base64 for version {newSoftwareVersion}, previous version is {previousSoftwareVersion}");

            fileName = "OPTApp";
            if (newSoftwareVersion == null)
            {
                DoDeferredLogging(LogLevel.Warn, "Software version not specified");
                softwareBase64 = null;
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }

            if (newSoftwareVersion.Equals(previousSoftwareVersion))
            {
                DoDeferredLogging(LogLevel.Warn, $"Software version {newSoftwareVersion} already installed");
                softwareBase64 = null;
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }

            try
            {
                softwareBase64 =
                    Convert.ToBase64String(File.ReadAllBytes(SoftwareDirectory + OptAppPrefix + newSoftwareVersion + SoftwareSuffix));
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultSuccess;
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, "Exception raised reading software file", exception: ex);
                softwareBase64 = null;
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }
        }

        private string Asset(bool isSecureAssets, string version, out string fileName, out string softwareBase64)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            DoDeferredLogging(LogLevel.Info, $"Constructing Asset Base64 for version {version} of {(isSecureAssets ? "Secure" : "CPAT")} Assets");

            fileName = isSecureAssets ? "SecureAssets" : "CPATAssets";
            if (version == null)
            {
                DoDeferredLogging(LogLevel.Warn, "Software version not specified");
                softwareBase64 = null;
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }

            try
            {
                softwareBase64 =
                    Convert.ToBase64String(File.ReadAllBytes(SoftwareDirectory + (isSecureAssets ? SecureAssetsPrefix : CpatAssetsPrefix) +
                                                             version + SoftwareSuffix));
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultSuccess;
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, "Exception raised reading software file", exception: ex);
                softwareBase64 = null;
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }
        }

        private string Config(IOpt opt, bool updateTids, out optMessages.Xsd.configType config, bool forcedRefresh = false)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (updateTids)
            {
                _paymentConfig?.GetAllPumpTids(LoggingReference);
            }

            FetchGenericOptConfig(forceRefresh: forcedRefresh);
            // Use local variable as property could be reinitialized during configuration build
            var advancedConfig = HydraDb.AdvancedConfig;
            var genericOptConfig = GenericOptConfig;
            _hasCardClessAids = genericOptConfig.CardClessAids.Any();

            config = null;
            try
            {
                config = _optConfigurationBuilder.BuildConfig(opt, advancedConfig, genericOptConfig, AllPumps, FetchedTids, GetWorker<IPaymentConfigIntegrator>(), GetWorker<ILocalAccountWorker>(),
                    new DivertConfig
                    {
                        RedirectHydraId = _redirectHydraId,
                        IsOptServiceDiverted = _isOptServiceDiverted,
                        DivertedServiceAddress = _divertedServiceAddress,
                        DivertedFromOptPort = _divertedFromOptPort,
                        DivertedToOptPort = _divertedToOptPort,
                        DivertedHeartbeatPort = _divertedHeartbeatPort
                    });

                var cfg = config.pumps.Select(x => $"{x.tid}/{x.transactionNumber}").ToList();
                DoDeferredLogging(LogLevel.Info, $"OPT: {opt.IdString} ({opt.Id}); Contains [TID/Transaction Number]", () => new[] { !cfg.Any() ? ConfigConstants.None : string.Join("; ", cfg) });

                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultSuccess;
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Warn, "Invalid Config", exception: ex);
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return ResultFailure;
            }
        }

        /// <summary>Sign in or sign out.</summary>
        /// <param name="signIn">True if signing in, false if signing out.</param>
        /// <param name="id">Id of OPT.</param>
        /// <param name="optVersions">Details of the component versions in the OPT.</param>
        /// <param name="optIpDetails">Details of the ip attributes of the OPT.</param>
        /// <returns>Status to reply.</returns>
        private string SignInOut(IMessageTracking message, bool signIn, int id, optModels.Opt optVersions, optModels.IpDetails optIpDetails)
        {
            var logRefFull = message.FullId;
            var result = ResultFailure;
            DoAction(() =>
            {
                var workerController = NotificationWorker;

                if (AllOpts.TryGetOpt(id, out var opt))
                {
                    var pushChangeSent = false;

                    if (signIn && !(string.IsNullOrWhiteSpace(optVersions.SoftwareVersion) && string.IsNullOrWhiteSpace(optVersions.SecureAssetsVersion) &&
                                    string.IsNullOrWhiteSpace(optVersions.MultimediaAssetsVersion) && string.IsNullOrWhiteSpace(optVersions.CpatAssetsVersion) &&
                                    string.IsNullOrWhiteSpace(optVersions.OptFirmwareVersion) && string.IsNullOrWhiteSpace(optVersions.EMVKernelVersion) &&
                                    string.IsNullOrWhiteSpace(optVersions.PluginType) && string.IsNullOrWhiteSpace(optVersions.PluginVersion)))
                    {
                        if (!string.IsNullOrWhiteSpace(optVersions.SoftwareVersion))
                        {
                            opt.SetSoftwareVersion(optVersions.SoftwareVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.SecureAssetsVersion))
                        {
                            opt.SetSecureAssetsVersion(optVersions.SecureAssetsVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.MultimediaAssetsVersion))
                        {
                            opt.SetMultimediaAssetsVersion(optVersions.MultimediaAssetsVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.CpatAssetsVersion))
                        {
                            opt.SetCpatAssetsVersion(optVersions.CpatAssetsVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.OptFirmwareVersion))
                        {
                            opt.SetOptFirmwareVersion(optVersions.OptFirmwareVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.EMVKernelVersion))
                        {
                            opt.SetEmvKernelVersion(optVersions.EMVKernelVersion);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.PluginType))
                        {
                            opt.SetPluginType(optVersions.PluginType);
                        }

                        if (!string.IsNullOrWhiteSpace(optVersions.PluginVersion))
                        {
                            opt.SetPluginVersion(optVersions.PluginVersion);
                        }

                        if (optIpDetails != null)
                        {
                            if (!string.IsNullOrWhiteSpace(optIpDetails.SubnetMask))
                            {
                                opt.SetSubnet(optIpDetails.SubnetMask);
                            }

                            if (!string.IsNullOrWhiteSpace(optIpDetails.DefaultGateway))
                            {
                                opt.SetGateway(optIpDetails.DefaultGateway);
                            }

                            if (!string.IsNullOrWhiteSpace(optIpDetails.Dns1))
                            {
                                opt.SetDns1(optIpDetails.Dns1);
                            }

                            if (!string.IsNullOrWhiteSpace(optIpDetails.Dns2))
                            {
                                opt.SetDns2(optIpDetails.Dns2);
                            }
                        }

                        workerController.PushChange(EventType.OPTChanged, opt.IdString);
                        pushChangeSent = true;
                    }

                    if (signIn && opt.SignedIn)
                    {
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"Failed Sign In for OPT {opt.IdString}, already signed in", reference: logRefFull);
                        workerController.SendInformation($"Sign In Note OPT {opt.IdString} already signed in");
                    }
                    else if (!signIn && !opt.SignedIn)
                    {
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"Failed Sign Out for OPT {opt.IdString}, not signed in", reference: logRefFull);
                        workerController.SendInformation($"Sign Out Note OPT {opt.IdString} not signed in");
                    }
                    else
                    {
                        if (signIn)
                        {
                            opt.SignIn();
                            opt.SetSignInRequired(false);
                            GetWorker<IToOptWorker>().SendCurrentNotificationToOpt(opt, message);
                        }
                        else
                        {
                            opt.SignOut();
                        }

                        if (!pushChangeSent)
                        {
                            workerController.PushChange(EventType.OPTChanged, opt.IdString);
                        }
                        UpdateAllPumpsOnPos(opt, logRefFull);

                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"OPT {opt.IdString} Signed {(signIn ? "In" : "Out")}", reference: logRefFull);
                        workerController.SendInformation($"OPT {opt.IdString} Signed {(signIn ? "In" : "Out")}");
                    }

                    if (signIn)
                    {
                        CheckConfigNeeded(opt);
                    }

                    result = ResultSuccess;
                }
            }, logRefFull);

            return result;
        }

        private void SendNotificationToOpt(IOpt opt, OptRequest notification, IMessageTracking message = null)
        {
            var logRef = message?.FullId ?? LoggingReference;

            DoAction(() =>
            {

                if (opt == null || notification == null)
                {
                    DoDeferredLogging(LogLevel.Error, $"{HeaderException}.{GetShortNotificationName(notification)}.OPT", () => new[] { $"{opt?.IdString} ({opt?.Id})", "Unable to send notification" }, reference: logRef);
                }
                else
                {
                    var send = opt.AddNotification(notification);
                    if (send)
                    {
                        GetWorker<IToOptWorker>().SendCurrentNotificationToOpt(opt, message);
                    }
                    else
                    {
                        DoDeferredLogging(LogLevel.Info, $"Queue.{GetShortNotificationName(notification)}.OPT", () => new[] { $"{opt.IdString} ({opt.Id})" }, reference: logRef);
                    }
                }

            }, logRef);
        }

        private bool WhitelistChanged(IOpt opt)
        {
            var result1 = DoAction(() =>
            {
                if (opt?.SentWhitelist?.Whitelist?.Any(x => x.Name == null || x.Base64 == null) != false)
                {
                    return Result.Success(true);
                }

                if (opt.WhitelistCheckPending)
                {
                    opt.WhitelistCheckCleared();
                    var result = Whitelist(out var whitelist);
                    if (result.Equals(ResultSuccess) && whitelist?.Whitelist?.Any(x => x.Name == null || x.Base64 == null) == false &&
                        (whitelist.Whitelist.Any(x => !opt.SentWhitelist.Whitelist.Any(y => x.Name.Equals(y.Name) && x.Base64.Equals(y.Base64))) ||
                         opt.SentWhitelist.Whitelist.Any(x => !whitelist.Whitelist.Any(y => x.Name.Equals(y.Name) && x.Base64.Equals(y.Base64)))))
                    {
                        return Result.Success(true);
                    }
                }

                return Result.Success(false);
            }, LoggingReference);

            return result1.IsSuccess ? result1.Value : false;
        }

        private bool LayoutChanged(IOpt opt)
        {
            var result1 = DoAction(() =>
            {
                if (opt?.SentLayout?.Layout?.Any(x => x.Name == null || x.Base64 == null) != false)
                {
                    return Result.Success(true);
                }

                if (opt.LayoutCheckPending)
                {
                    opt.LayoutCheckCleared();
                    var result = Layout(out var layout);
                    if (result.Equals(ResultSuccess) && layout?.Layout?.Any(x => x.Name == null || x.Base64 == null) == false &&
                        (layout.Layout.Any(x => !opt.SentLayout.Layout.Any(y => x.Name.Equals(y.Name) && x.Base64.Equals(y.Base64))) ||
                         opt.SentLayout.Layout.Any(x => !layout.Layout.Any(y => x.Name.Equals(y.Name) && x.Base64.Equals(y.Base64)))))
                    {
                        return Result.Success(true);
                    }
                }

                return Result.Success(false);
            }, LoggingReference);

            return result1.IsSuccess ? result1.Value : false;
        }

        private bool MediaFilesListChanged(IOpt opt)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (opt?.SentMediaFilesList?.Any(x => x == null) != false)
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return true;
            }

            if (opt.MediaUpdateCheckPending)
            {
                opt.MediaUpdateCheckCleared();
                IList<string> files = MediaFilesPlusPlaylist(opt.PlaylistFileName);
                if (files.Any(x => x == null) == false && (files.Any(x => !opt.SentMediaFilesList.Any(x.Equals)) ||
                                                           opt.SentMediaFilesList.Any(x => !files.Any(x.Equals))))
                {
                    DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                    return true;
                }
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return false;
        }

        private IList<string> MediaFilesPlusPlaylist(string playlist)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            IList<string> list = new List<string>();
            foreach (string item in _mediaFilesList)
            {
                list.Add(item);
            }

            if (!string.IsNullOrWhiteSpace(playlist) && _playlistFilesList.Contains(playlist) && !_mediaFilesList.Contains(playlist))
            {
                list.Add(playlist);
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return list;
        }

        private bool ConfigChanged(IOpt opt)
        {
            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.EntryMessage);
            if (opt.SentConfig == null)
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return true;
            }

            // Use local variable as property could be reinitialized during configuration comparison
            var advancedConfig = HydraDb.AdvancedConfig;
            var genericOptConfig = GenericOptConfig;
            if (opt.ConfigCheckPending)
            {
                opt.ConfigCheckCleared();
                var result = Config(opt, false, out var config);
                if (result.Equals(ResultSuccess))
                {
                    DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                    return CompareConfig.HasConfigChanged(opt, config, advancedConfig, genericOptConfig);
                }
            }

            if (IsSentConfigStale(genericOptConfig, opt, _optConfigurationBuilder.GetConfigPumps(opt, AllPumps, FetchedTids, _paymentConfig)))
            {
                DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
                return true;
            }

            DoDeferredLogging(LogLevel.Debug, HtecLoggingConstants.ExitMessage);
            return false;
        }

        //TODO: Want to move this, but not right now.
        private bool IsSentConfigStale(GenericOptConfig genericOptConfig, IOpt opt, optMessages.Xsd.pump[] pumps)
        {
            return pumps.Any(x => !opt.SentConfig.pumps.Any(y =>
                       x.number == y.number && x.tid.Equals(y.tid) && x.maxFillOverrideForFuelCards == y.maxFillOverrideForFuelCards &&
                       x.maxFillOverrideForPaymentCards == y.maxFillOverrideForPaymentCards))
                   || opt.SentConfig.pumps.Any(x => !pumps.Any(y => x.number == y.number && x.tid.Equals(y.tid)))
                   || IsModeConfigMismatch(opt)
                   || HasContactlessChanged(genericOptConfig, opt)
                   || !string.Equals(opt.ReceiptHeader ?? ConcatLines(genericOptConfig.DefaultReceiptHeaderLines), ConcatLines(opt.SentConfig.opt.receiptHeaderLines))
                   || !string.Equals(opt.ReceiptFooter ?? ConcatLines(genericOptConfig.DefaultReceiptFooterLines), ConcatLines(opt.SentConfig.opt.receiptFooterLines))
                   || HasDivertConfigChanged(opt)
                   || HasEndpointConfigChanged(opt)
                   || (_paymentConfig?.EndPoints.Any(x => !opt.SentConfig.esocket.Any(y => x.Item1.Equals(y.ip) && x.Item2 == y.port)) ?? false)
                   || opt.SentConfig.esocket.Any(x => !(_paymentConfig?.EndPoints.Any(y => x.ip.Equals(y.Item1) && x.port == y.Item2) ?? false));
        }

        private static string ConcatLines(string[] receiptLines)
        {
            return receiptLines != null ? string.Join("\n", receiptLines) : null;
        }

        private bool HasContactlessChanged(GenericOptConfig genericOptConfig, IOpt opt)
        {
            return _hasCardClessAids && genericOptConfig.ContactlessConfiguration.HasContactless && opt.SentConfig.cards.cless_aids.Length == 0
                   || opt.SentConfig.cards.cless_aids.Length > 0 && !genericOptConfig.ContactlessConfiguration.HasContactless;
        }

        private static bool IsModeConfigMismatch(IOpt opt)
        {
            return opt.Mode == OptModeType.OptModeNotSet && (OptModeType)opt.SentConfig.opt.mode != OptModeType.OptModeNotSet
                   || opt.Mode != OptModeType.OptModeNotSet && (OptModeType)opt.SentConfig.opt.mode == OptModeType.OptModeNotSet;
        }

        private bool HasEndpointConfigChanged(IOpt opt)
        {
            return !_isOptServiceDiverted
                   && (!_currentEndPoints.FromOptEndPoint.Address.ToString().Equals(opt.SentConfig.hydraOpt.inbound.ip)
                       || _currentEndPoints.FromOptEndPoint.Port != opt.SentConfig.hydraOpt.outbound.port
                       || _currentEndPoints.ToOptEndPoint.Port != opt.SentConfig.hydraOpt.inbound.port
                       || _currentEndPoints.HeartbeatEndPoint.Port != opt.SentConfig.hydraOpt.heartbeat.port);
        }

        private bool HasDivertConfigChanged(IOpt opt)
        {
            return _isOptServiceDiverted
                   && (!_divertedServiceAddress.ToString().Equals(opt.SentConfig.hydraOpt.inbound.ip)
                       || _divertedFromOptPort != opt.SentConfig.hydraOpt.outbound.port
                       || _divertedToOptPort != opt.SentConfig.hydraOpt.inbound.port
                       || _divertedHeartbeatPort != opt.SentConfig.hydraOpt.heartbeat.port);
        }

        private string OptNotification(IMessageTracking message, OptRequest notificationType, int id)
        {
            var result = DoAction(() => Result.Success(DoOptNotification(message, notificationType, id)), message.FullId);
            return result.Value;
        }

        private string DoOptNotification(IMessageTracking message, OptRequest notificationType, int id)
        {
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;

            var workerController = NotificationWorker;

            IPump thePump;
            var printerStatusRequest = notificationType as PrinterStatusOptNotificationRequest;
            var deviceStateRequest = notificationType as DeviceStateOptNotificationRequest;

            if (AllOpts.TryGetOpt(id, out var opt))
            {
                if (!opt.SignedIn)
                {
                    workerController.SendInformation($"OPT Notification {opt.IdString} not signed in");
                    opt.SetSignInRequired(true);
                }

                if (printerStatusRequest != null)
                {
                    var printerStatus = printerStatusRequest.Notification.PrinterStatus.Status;
                    workerController.SendInformation($"Printer Status Notification from OPT {opt.IdString}, Status is {printerStatus}");
                    switch (printerStatus)
                    {
                        case PrinterStatus.PrinterOK:
                            opt.SetPrinterStatus();
                            break;
                        case PrinterStatus.PrinterPaperLow:
                            opt.SetPrinterStatus(paperLow: true);
                            break;
                        case PrinterStatus.PrinterError:
                            opt.SetPrinterStatus(true);
                            break;
                    }

                    UpdateAllPumpsOnPos(opt, logRef);

                    workerController.PushChange(EventType.OPTChanged, opt.IdString);
                }
                else if (deviceStateRequest != null)
                {
                    var deviceState = deviceStateRequest.Notification.DeviceState.Status;
                    workerController.SendInformation($"Device State Notification from OPT {opt.IdString}, Status is {deviceState}");
                    opt.SetDeviceState(deviceState);

                    UpdateAllPumpsOnPos(opt, logRef);

                    workerController.PushChange(EventType.OPTChanged, opt.IdString);
                }
                else if (notificationType is CardInsertedOptNotificationRequest ntCardInserted && AllPumps.TryGetPump(ntCardInserted.Notification.Pump ?? 0, out thePump) && thePump.Opt == opt)
                {
                    workerController.SendInformation($"Card Inserted Notification from OPT {opt.IdString}, Pump is {thePump.Number}");
                    if (!thePump.CanInsertCard)
                    {
                        var summary = thePump.TransactionSummary;
                        summary.CardInsertedCount++;

                        var maxCount = MaximumCardInsertedAttempts.GetValue();
                        if (summary.CardInsertedCount > maxCount && summary.IsDefaultState())
                        {
                            DoDeferredLogging(LogLevel.Warn, $"CardInserted.OutOfSequence.{Pump.HeaderPump}", () => new[] { $"{thePump.Number}; Count: {summary.CardInsertedCount}; Resetting pump" }, methodName: nameof(DoOnMessageReceived));
                            workerController.SendInformation($"Card Inserted out of sequence (max times: {maxCount}), resetting pump {thePump.Number}; ");
                            var prevState = thePump.SetPreviousPumpState();
                            if (thePump.ResetPump())
                            {
                                _posWorker?.StatusResponse(thePump.Number, message);
                                CheckPumpState(thePump, prevState);
                            }
                        }

                        if (!thePump.CanInsertCard)
                        {
                            DoDeferredLogging(LogLevel.Warn, $"CardInserted.OutOfSequence.{Pump.HeaderPump}", () => new[] { $"{thePump.Number}; Count: {summary.CardInsertedCount}" }, methodName: nameof(DoOnMessageReceived));
                            workerController.SendInformation($"Card Inserted out of sequence, pump {thePump.Number}; ");
                            return ResultFailure;
                        }
                    }

                    thePump.CardInserted(logRef);

                    if (thePump.InUseByOpt)
                    {
                        var result = _pumpWorker?.ReservePump(thePump.Number, HydraDb.AdvancedConfig.PumpReserveLimit, message) ?? Result.Success();

                        if (!result.IsSuccess)
                        {
                            DoDeferredLogging(LogLevel.Warn, Pump.HeaderPump, () => new[] { $"{thePump.Number}; Pump could not be reserved" }, methodName: nameof(DoOnMessageReceived));
                            CheckRemovePumpReserve((p) => p.InUseByOpt, thePump, message, "#5", true);
                            return ResultFailure;
                        }

                        if (result.IsSuccess && !thePump.IsSecAuthRequested)
                        {
                            GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.PreAuthRequest(new SecAuthRequest(message.ParentIdAsString, message.IdAsString, opt.IdString, thePump.Number), message);
                        }
                    }

                    _posWorker?.StatusResponse(thePump.Number, message);
                    PushChangePumpStateChanged(thePump, true, true);
                }
                else if (notificationType is KioskUseOptNotificationRequest ntKioskUse && AllPumps.TryGetPump(ntKioskUse.Notification.Pump ?? 0, out thePump) && thePump.Opt == opt)
                {
                    return DoProcessKioskUse(thePump, message, false);
                }
                else
                {
                    return ResultFailure;
                }
            }
            else
            {
                return ResultFailure;
            }

            return ResultSuccess;
        }

        private string DoProcessKioskUse(IPump thePump, IMessageTracking message, bool pumpInstigated = true, bool isMobile = false)
        {
            var workerController = NotificationWorker;
            var logRef = message.IdAsString;

            if (pumpInstigated && !HydraDb.AdvancedConfig.NozzleUpForKioskUse)
            {
                DoDeferredLogging(LogLevel.Warn, Pump.HeaderPump, () => new[] { $"{thePump.Number}; Mode: {thePump.PumpMode}; pumpInstigated: {pumpInstigated}; !NozzleUpForKioskUse: {!HydraDb.AdvancedConfig.NozzleUpForKioskUse}" }, reference: logRef);
                return ResultFailure;
            }

            var opt = thePump.Opt;
            if (!thePump.CanSetKioskUse)
            {
                workerController.SendInformation($"Set Kiosk Use out of sequence for pump {thePump.Number}");
                DoDeferredLogging(LogLevel.Warn, Pump.HeaderPump, () => new[] { $"{thePump.Number}; Mode: {thePump.PumpMode}; !CanSetKioskUse: {!thePump.CanSetKioskUse}" }, reference: logRef);
                return ResultFailure;
            }

            var prevState = thePump.SetPreviousPumpState();

            workerController.SendInformation($"Setting pump {thePump.Number} to KioskUse{(isMobile ? " (with Mobile)" : string.Empty)}");

            thePump.SetKioskUse(logRef, isMobile);

            if (!opt.IsNozzleUp(thePump.Number))
            {
                DoDeferredLogging(LogLevel.Info, Pump.HeaderPump, () => new[] { $"{thePump.Number}", "Starting pay at kiosk timer" }, reference: logRef);
                thePump.StartPayAtKioskPressedTimer(opt.PayAtKioskPressedTimeoutInSeconds);
            }

            CheckPumpState(thePump, prevState, message);
            CheckOptConfig(opt, opt.IsInPodMode);

            return ResultSuccess;
        }

        /// <summary>Payment message received.</summary>
        /// <param name="paymentResult">Approved or Cancelled.</param>
        /// <param name="discount">Discount for cleared payment.</param>
        /// <param name="id">Id of OPT.</param>
        /// <param name="message">Tracking for current message.</param>
        /// <param name="payment"></param>
        /// <returns>Status to reply.</returns>
        private string Payment(PaymentResult paymentResult, DiscountItem discount, int id, IMessageTracking message, optModels.Payment payment)
        {
            var resultAction = DoAction<string>(() =>
            {
                if (AllPumps.TryGetPump(payment.Pump, out IPump thePump) && thePump.Opt != null && id == thePump.Opt.Id)
                {
                    var workerController = GetWorker<IControllerWorker>();
                    var workerJournal = GetWorker<IJournalWorker>();
                    var optConfig = GenericOptConfig;

                    var opt = thePump.Opt;
                    if (!opt.SignedIn)
                    {
                        workerController.SendInformation($"Payment Note OPT {opt.IdString} not signed in");
                        opt.SetSignInRequired(true);
                    }

                    if (payment is PaymentApproved paymentApproved)
                    {
                        var result = workerJournal.PaymentApproved(paymentResult, thePump, opt, message, paymentApproved, optConfig, AutoAuth);
                        return Result.Success(result.IsSuccess ? ResultSuccess : ResultFailure);
                    }

                    if (payment is PaymentCancelled paymentCancelled)
                    {
                        var result = workerJournal.PaymentCancelled(paymentResult, thePump, opt, message, paymentCancelled);

                        if (result.IsSuccess && !thePump.InUse && !thePump.HasPayment && thePump.IsPaid && thePump.IsNozzleUp && opt.Mode == OptModeType.OptModeMixed)
                        {
                            DoProcessKioskUse(thePump, message);
                        }

                        return Result.Success(result.IsSuccess ? ResultSuccess : ResultFailure);
                    }

                    if (payment is PaymentCleared paymentCleared && thePump.CanClearPayment)
                    {
                        var result = workerJournal.PaymentCleared(paymentResult, discount, thePump, opt, message, paymentCleared, optConfig);
                        return Result.Success(result.IsSuccess ? ResultSuccess : ResultFailure);
                    }
                }

                return Result.Failure<string>(ResultFailure);
            }, message.FullId);

            return resultAction.IsSuccess ? resultAction.Value : resultAction.Error;
        }

        private string GetReceipts(IMessageTracking message, string cardNumber, int id, out List<ReceiptData> receipt)
        {
            var result = DoAction(() => GetWorker<IJournalWorker>().GetReceipts(id, cardNumber, message), message.FullId);

            receipt = result.IsSuccess ? result.Value.ToList() : null;
            return result.IsSuccess ? ResultSuccess : ResultFailure;
        }

        private string StoreReceipt(IMessageTracking message, int id, ReceiptTransaction receiptTransaction)
        {
            var result = DoAction(() => GetWorker<IJournalWorker>().StoreReceipt(id, receiptTransaction, message), message.FullId);

            return result.IsSuccess ? ResultSuccess : ResultFailure;
        }

        private string ReceiptPrinted(IMessageTracking message, int id, ReceiptPrintedDetails receiptDetails)
        {
            var result = DoAction(() => GetWorker<IJournalWorker>().ReceiptPrinted(id, receiptDetails, message), message.FullId);

            return result.IsSuccess ? ResultSuccess : ResultFailure;
        }

        private void CheckHeartbeat(IOpt opt)
        {
            if (!opt.Connected)
            {
                return;
            }

            var logRef = LoggingReference;

            var workerToOpt = GetWorker<IToOptWorker>();
            var workerHeartbeatOpt = GetWorker<IOptHeartbeatWorker>();

            if (!opt.ToOptConnected)
            {
                DoDeferredLogging(LogLevel.Info, "ConnectedStatus.OPT", () => new[]
                {
                    $"{opt.IdString} ({opt.Id})",
                    $"Heartbeat: {opt.HeartbeatConnected}; ToOpt: {opt.ToOptConnected}; FromOpt: {opt.FromOptConnected};",
                    $" ToOptReconnectionCount: {opt.ToOptReconnectionCount}/{ConfigValueMaximumReconnectionAttemptsOptWorkerToOpt}"
                });

                if (opt.ToOptReconnectionCount > ConfigValueMaximumReconnectionAttemptsOptWorkerToOpt)
                {
                    DoDeferredLogging(LogLevel.Info, $"Forced.{HeaderDisconnect}.OPT", () => new[] { $"{opt.IdString} ({opt.Id})" });
                    workerHeartbeatOpt.Disconnect(opt.Id);
                    opt.ToOptReconnectionCount = 1;
                }
                else
                {
                    DoDeferredLogging(LogLevel.Info, "Force.Heartbeat.OPT", () => new[] { $"{opt.IdString} ({opt.Id})" });
                    opt.ToOptReconnectionCount++;
                    workerToOpt.SendToOptHeartbeat(opt);
                }
            }
            else
            {
                opt.ToOptReconnectionCount = 1;
                workerToOpt.SendCurrentNotificationToOpt(opt);
            }

            if (opt.Connected && opt.HasTimeoutExpired())
            {
                workerToOpt.Disconnect(opt.Id);
                Disconnect(opt.Id);
                workerHeartbeatOpt.Disconnect(opt.Id);

                GetWorker<ITelemetryWorker>().MessageTimeoutFromOpt(opt, GetShortNotificationName(opt.GetCurrentNotification()?.GetType().Name ?? ConfigConstants.Unknown), logRef);
            }
        }

        private void CheckAuthorisedPumps(IMessageTracking message)
        {
            DoAction(() =>
            {
                foreach (var pump in AllPumps.AllPumps)
                {
                    if (IsOnPumpStateActive(pump.Number))
                    {
                        continue;
                    }

                    if (pump.HasSecAuthRequestTimedOut)
                    {
                        // TODO: DAW - extract the embedded timerable interval
                        var interval = DateTime.UtcNow.Ticks - pump.SecAuthExpiryTime.Ticks;
                        if (interval < TimeSpan.FromSeconds(5).Ticks)
                        {
                            if (interval < TimeSpan.FromSeconds(3).Ticks && pump.SecAuthState == SecAuthState.Requested)
                            {
                                AllPumps.UpdateParentId(pump.Number, message);
                                var response = new SecAuthResponse(message.ParentIdAsString, message.IdAsString, pump.Number, null, GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.TimedOutResponse ?? true);
                                GetWorker<ISecAuthIntegratorInTransient<IMessageTracking>>().RequestTimedOut(response, message);
                            }

                            NotificationWorker?.PushChange(EventType.PumpChanged, pump.Number.ToString());
                        }
                    }

                    if (pump.IsAuthorised(out var amount, message.IdAsString))
                    {
                        if (_pumpWorker.PaymentApproved(pump.Number, amount, message).IsSuccess)
                        {
                            HydraDb.SetOptPayment(pump.Number, message);
                        }
                    }
                    else if (pump.IsReserved)
                    {
                        var chk = CheckRemovePumpReserve((p) => !p.InUse && (!p.IsNozzleUp && p.LastPumpState == PumpState.Idle || p.IsNozzleUp && p.LastPumpState == PumpState.Request) && p.HasReserveTimedOut, pump, message, "#2");
                        if (!chk)
                        {
                            CheckRemovePumpReserve((p) => p.InUse && p.OptUse && (!p.IsNozzleUp && p.LastPumpState == PumpState.Idle || p.IsNozzleUp && p.LastPumpState == PumpState.Request) && !p.HasPayment && !p.IsDelivering && pump.IsPaid && p.HasReserveTimedOut, pump, message, "#4");
                        }
                    }
                }
            }, message.FullId);
        }

        private void CheckSignInRequired(IOpt opt)
        {
            if (opt.Connected && opt.SignInRequired)
            {
                SendNotificationToOpt(opt, new SignInRequiredNotificationRequest());
            }
        }

        private void CheckLogFileRequest(IOpt opt)
        {
            if (opt.Connected && opt.LogFileRequestPending)
            {
                opt.LogFileRequestSent = true;
                NotificationWorker.PushChange(EventType.OPTChanged, opt.IdString);

                SendNotificationToOpt(opt, new RequestLogFileNotificationRequest());
                var now = DateTime.Now;
                opt.SetLastLogTime(now);
                HydraDb.SetLastLogTime(opt.IdString, now);
            }
        }

        private void CheckPayAtKioskTimer(IOpt opt)
        {
            if (!opt.PumpList().Any(pump => AllPumps.TryGetPump(pump, out var x) && x.HasPayAtKioskTimeoutExpired()))
            {
                return;
            }

            var workerController = NotificationWorker;
            var message = new MessageTracking { IdAsString = LoggingReference };

            DoDeferredLogging(LogLevel.Info, "OPT", () => new[] { $"{opt.IdString} ({opt.Id})", "Pay at Kiosk timer expired" });
            var modeChangeWorker = GetWorker<IPosIntegratorOutMode<IMessageTracking>>();

            foreach (var pump in opt.PumpList())
            {
                if (AllPumps.TryGetPump(pump, out var thePump) && thePump.Opt == opt && thePump.KioskUse && thePump.HasPayAtKioskTimeoutExpired())
                {
                    DoDeferredLogging(LogLevel.Info, "OPT", () => new[] { $"{opt.IdString} ({opt.Id}); {Pump.HeaderPump}: {pump}", $"Setting pump to mixed" });
                    thePump.SetPreviousPumpState();
                    thePump.ClearPayAtKioskPressedTimer();
                    thePump.SetMixed(reference: LoggingReference);
                    CheckPumpState(thePump, optModels.PumpStateType.KioskOnly, message, true);
                    _posWorker?.StatusResponse(thePump.Number, message);

                    workerController.SendInformation($"Setting pump {thePump.Number} to Mixed");
                }
            }

            CheckOptConfig(opt, opt.IsInPodMode);
        }

        private void SetGradeNames()
        {
            _gradeHelper.Initialise(LoggingReference);
        }

        private void CheckForDelivery(IMessageTracking message)
        {
            DoAction(() =>
            {
                foreach (var pump in AllPumps.AllPumps)
                {
                    if (HydraDb.HasDelivered(pump.Number, out var grade, out var volume, out var amount, out var name, out var price,
                        out var netAmount, out var vatAmount, out var vatRate))
                    {
                        DoDeferredLogging(LogLevel.Warn, "Pump", () => new[] { $"{pump.Number}", "Forcing Delivered (from CheckForDelivery)" });
                        SendNotificationToOpt(pump.Opt, new DeliveredNotificationRequest(pump.Number, grade, volume, amount, name, price, netAmount, vatAmount, vatRate));
                    }
                    else if (HydraDb.HasOptPayment(pump.Number, message))
                    {
                        DoDeferredLogging(LogLevel.Warn, "Pump", () => new[] { $"{pump.Number}", "Forcing SetOptPayment (from CheckForDelivery)" });
                        pump.SetOptPayment(message.IdAsString);
                        pump.SetHasKioskPayment(false);
                    }
                }
            }, message.FullId);
        }

        #endregion

        /// <inheritdoc/>
        protected override void DoOnDisconnected(int? id = null)
        {
            RegisterDisconnect(SocketType.FromOpt, id);

            base.DoOnDisconnected(id);
        }

        /// <inheritdoc/>
        public Result ModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking message = null, bool suppressModeChange = false)
        {
            if (!AllPumps.TryGetPump(pump, out var thePump))
            {
                return Result.Failure($"Invalid pump: {pump}");
            }

            var prevState = previousState.ToPumpStateType();

            var opt = thePump.Opt;
            SetPaymentTimeout(opt, (!opt?.IsInPodMode ?? true) ? null : mode.ToOptModeType());
            CheckPumpState(thePump, prevState, message);
            CheckOptConfig(opt, suppressModeChange);

            return Result.Success();
        }

        /// <summary>
        /// Overridden and attempt to extract intended message, otherwise return defaul
        /// </summary>
        /// <param name="message">{ILogTracking} instance with .Context containing invalid buffer string</param>
        /// <param name="id">Id of OPT.</param>
        /// <returns>Result wrapped {XElement}</returns>
        protected override Result<XElement> DoGetInvalidMessageResponse(ILogTracking message, int id)
        {
            if (string.IsNullOrEmpty(message?.Context ?? string.Empty))
            {
                return Result.Failure<XElement>("Invalid Buffer");
            }

            if (!AllOpts.TryGetOpt(id, out var opt))
            {
                return Result.Failure<XElement>("Invalid Opt id");
            }

            var match = Regex.Match(message.Context, "(?<pre>.*)[ *\\t\\n\\r]+" +
                "<HydraOPT>[ *\\t\\n\\r]+" +
                "<Request Type=\"(?<request>.*)\">[ *\\t\\n\\r]+" +
                "<OPT Id=\"(?<opt>.*)\"\\/>[ *\\t\\n\\r]+" +
                "<Hydra Id=\"(?<hydra>.*)\"\\/>");

            if (match.Success)
            {
                var assembly = typeof(OptResponse).Assembly;
                var requestType = assembly.GetTypes().FirstOrDefault(x => x.FullName == $"{typeof(OptMessage).Namespace}.{match.Groups["request"].Value}{OptRequest.Request}");
                var responseType = assembly.GetTypes().FirstOrDefault(x => x.FullName == $"{typeof(OptMessage).Namespace}.{match.Groups["request"].Value}{OptResponse.Response}");

                var request = requestType == null ? null : Activator.CreateInstance(requestType) as OptRequest;
                if (request != null)
                {
                    request.Message = new() { Id = message.IdAsString };

                    var response = responseType == null ? null : Activator.CreateInstance(responseType) as OptResponse;
                    if (response != null)
                    {
                        response.Result = ResultFailure;
                        response.Hydra = new() { Id = match.Groups["hydra"].Value };
                        response.Opt.Id = match.Groups["opt"].Value;

                        return Result.Success(GetResponseMessage(opt, response, request));
                    }
                }
            }

            return Result.Success(GetResponseMessage(opt,
                new ReadMessageResponse(ResultFailure),
                new ReadMessageRequest() { Message = new() { Id = message.IdAsString } }));
        }

        /// <inheritdoc />
        public Result DayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() =>
            {
                SendDayEndNotification(cardAmountSales, dayEndDetails.ShiftReference);

                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc />
        public Result ShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result WriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }
    }
}
