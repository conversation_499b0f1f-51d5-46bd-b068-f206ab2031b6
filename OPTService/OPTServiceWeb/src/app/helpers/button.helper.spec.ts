import { ButtonHelper } from './button.helper';

describe('ButtonHelper', () => {

  it('should be created', () => {
    expect(ButtonHelper).toBeTruthy();
  });

  describe('clicked', () => {

    it('should handle undefined event', () => {
      expect(() => {
        ButtonHelper.clicked(undefined);
      }).not.toThrowError(TypeError);
    });

    it('should handle click event', () => {
      const event = new Event('click');

      expect(() => {
        ButtonHelper.clicked(event);
      }).not.toThrowError(TypeError);
    });
    
    it('should handle submit event', () => {
      const event = new Event('submit');

      expect(() => {
        ButtonHelper.clicked(event);
      }).not.toThrowError(TypeError);
    });
  });

  describe('reset', () => {

    it('should handle undefined event', () => {
      expect(() => {
        ButtonHelper.reset(undefined);
      }).not.toThrowError(TypeError);
    });

    it('should handle click event', () => {
      const event = new Event('click');

      expect(() => {
        ButtonHelper.reset(event);
      }).not.toThrowError(TypeError);
    });

    it('should handle submit event', () => {
      const event = new Event('submit');

      expect(() => {
        ButtonHelper.reset(event);
      }).not.toThrowError(TypeError);
    });
  });
});