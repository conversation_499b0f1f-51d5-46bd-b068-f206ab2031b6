using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Extensions;
using Forecourt.Pos.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Logger.Interfaces;
using System;
using StatusResponse = Htec.Hydra.Core.Pos.Messages.StatusResponse;

namespace Forecourt.Pos.Workers
{
    public abstract class PosXxxWorker: Core.Workers.HydraDbable<IHydraDb>
    {
        /// <summary>
        /// Failure 
        /// </summary>
        protected const string FailureReasonPrefix = "Invalid: ";

        /// <inheritdoc/>
        protected PosXxxWorker(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IPumpCollection allPumps, IConfigurationManager configurationManager = null) : base(hydraDb, logManager, loggerName, allPumps, configurationManager)
        {
            if (AllPumps == null)
            {
                throw new ArgumentNullException(nameof(allPumps));
            }
        }

        /// <summary>
        /// Provide a template for processing a pump action, with override action point
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="message">Current message tracking information</param>
        /// <param name="action">Action to execute, optional</param>
        /// <returns>Result</returns>
        protected Result DoActionPump(byte pump, IMessageTracking message, Action<IPump> action = null)
        {
            return DoAction(() =>
            {
                if (!AllPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure($"{FailureReasonPrefix} Pump");
                }

                action?.Invoke(thePump);

                return Result.Success();
            }, message);
        }

        /// <summary>
        /// Provide a template for processing a pump action, with override action point
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="message">Current message tracking information</param>
        /// <param name="action">Action to execute, optional</param>
        /// <returns>Result</returns>
        protected Result<T> DoActionPump<T>(byte pump, IMessageTracking message, Func<IPump, T> action = null)
        {
            return DoAction(() =>
            {
                if (!AllPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure<T>($"{FailureReasonPrefix} Pump");
                }

                var result = action == null ? default(T) : action.Invoke(thePump);

                return Result.Success(result);
            }, message);
        }

        /// <summary>
        /// Get the Status for the pump
        /// </summary>
        /// <param name="pump">The pump</param>
        /// <returns>Result wrapped status <see cref="StatusResponse"/></returns>
        protected Result<StatusResponse> GetStatus(IPump pump)
        {
            var endPoints = HydraDb.FetchEndPoints();
            return Result.Success(pump.ToStatusResponse(endPoints.AutoAuth));
        }
    }
}
