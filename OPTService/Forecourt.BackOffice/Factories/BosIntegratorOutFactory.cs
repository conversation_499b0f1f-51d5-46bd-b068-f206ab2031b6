using Forecourt.Bos.Factories.Interfaces;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Bos.Factories
{
    /// <summary>
    /// Box integrator out factory
    /// </summary>
    public class BosIntegratorOutFactory : Factory<string, IBosIntegratorOut<IMessageTracking>>, IBosIntegratorOutFactory
    {
        private readonly Func<string, IBosIntegratorOut<IMessageTracking>> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public BosIntegratorOutFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IBosIntegratorOut<IMessageTracking>> resolveTypeInstance) : 
            base(logManager, $"{nameof(BosIntegratorOutFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => null);
            AddItem($"{PosType.HydraPos}", "Htec Hydra/VBO.net", (key) => _resolveTypeInstance(key));
            AddItem($"{PosType.Retalix}", "Morrisons BOS", (key) => _resolveTypeInstance(key));
            AddItem($"{PosType.GenericSignalRApi}", "Generic SignalR and RestAPI", (key) => _resolveTypeInstance(key));
            AddItem($"{PosType.MadicApiSignalR}", "MADIC RestAPI and SignalR", (key) => _resolveTypeInstance(key));
        }
    }
}
