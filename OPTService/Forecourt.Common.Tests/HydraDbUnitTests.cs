using CSharpFunctionalExtensions;
using Dapper;
using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.HydraOPT;
using OPT.Common.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Xunit;
using PumpEndPoint = OPT.Common.HydraDb.Models.PumpEndPoint;
using ReceiptTransaction = OPT.Common.HydraDbClasses.ReceiptTransaction;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace OPT.Common.Tests
{
    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class HydraDbUnitTests
    {
        private const string DefaultCardNumber = "1234";
        private const string DefaultOpt = "1234";

        private const string HydraId = "Hydra 2";
        private const string OtherHydraId = "Hydra 3";
        private static readonly IPAddress IpAddress = IPAddress.Parse("***************");
        private static readonly string IpAddressString = IpAddress.ToString();
        private const string OtherIpAddress = "***************";
        private const int FromOptPort = 12341;
        private const int ToOptPort = 12342;
        private const int HeartbeatPort = 12343;
        private const int ControllerPort = 12344;
        private const int HydraPosPort = 12345;
        private const int RetalixPosPort = 12346;
        private const int ThirdPartyPosPort = 12347;
        private const int MediaChannelPort = 12348;
        private const int OtherFromOptPort = 22341;
        private const int OtherToOptPort = 22342;
        private const int OtherHeartbeatPort = 22343;
        private const int OtherControllerPort = 22344;
        private const int OtherHydraPosPort = 22345;
        private const int OtherRetalixPosPort = 22346;
        private const int OtherThirdPartyPosPort = 22347;
        private const int OtherPort = 31234;
        private const bool AutoAuth = false;
        private readonly IHydraDb _hydraDb;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IDbExecutor _dbExecutor;
        private readonly IHtecLogger _logger;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly ICacheHelper _cacheHelper;
        private const int MaxRetalixTransactionNumber = 100000;
        private readonly IConfigurationManager _configurationManager;
        private readonly IConfigurationRepository _configurationRepository;

        public HydraDbUnitTests()
        {
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _dbExecutor = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(_dbExecutor);
            _logger = Substitute.For<IHtecLogger>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _cacheHelper = Substitute.For<ICacheHelper>();
            _configurationRepository = Substitute.For<IConfigurationRepository>();
            _hydraDb = CreateDefaultInterface();
        }

        #region Local Comparisons

        private static void OptEndPointsMatch(OptEndPoints x, OptEndPoints y)
        {
            x.HydraId.Should().Be(y.HydraId);
            x.FromOptEndPoint.Should().Be(y.FromOptEndPoint);
            x.ToOptEndPoint.Should().Be(y.ToOptEndPoint);
            x.HeartbeatEndPoint.Should().Be(y.HeartbeatEndPoint);
            x.HydraPosEndPoint.Should().Be(y.HydraPosEndPoint);
            x.RetalixPosEndPoint.Should().Be(y.RetalixPosEndPoint);
            x.ThirdPartyPosEndPoint.Should().Be(y.ThirdPartyPosEndPoint);
            x.FromOptBindEndPoint.Should().Be(y.FromOptBindEndPoint);
            x.ToOptBindEndPoint.Should().Be(y.ToOptBindEndPoint);
            x.HeartbeatBindEndPoint.Should().Be(y.HeartbeatBindEndPoint);
            x.HydraPosBindEndPoint.Should().Be(y.HydraPosBindEndPoint);
            x.RetalixPosBindEndPoint.Should().Be(y.RetalixPosBindEndPoint);
            x.ThirdPartyPosBindEndPoint.Should().Be(y.ThirdPartyPosBindEndPoint);
            x.AutoAuth.Should().Be(y.AutoAuth);
        }

        #endregion

        #region Database Tests

        #region Fetch End Points

        public class OptEndPointsData
        {
            public string Case { get; set; }
            internal IEnumerable<OptEndPoints> EndPoints { get; set; }
            internal OptEndPoints Expected { get; set; }

            public OptEndPointsData(string testCase, IEnumerable<OptEndPoints> endpoints, OptEndPoints expected)
            {
                Case = testCase;
                EndPoints = endpoints;
                Expected = expected;
            }
        }

        private static OptEndPoints CreateOptEndPointsHydra2()
        {
            return new OptEndPoints(HydraId, IpAddressString, FromOptPort, ToOptPort, HeartbeatPort, HydraPosPort,
                RetalixPosPort, ThirdPartyPosPort, MediaChannelPort, AutoAuth);
        }

        private static OptEndPoints CreateOptEndPointsEmpty()
        {
            return new OptEndPoints("", IpAddressString, FromOptPort, ToOptPort, HeartbeatPort, HydraPosPort,
                RetalixPosPort, ThirdPartyPosPort, MediaChannelPort, AutoAuth);
        }

        private static OptEndPoints CreateOptEndPointsDefault()
        {
            return new OptEndPoints(OptEndPoints.DefaultHydraId, IpAddressString, FromOptPort, ToOptPort,
                HeartbeatPort, HydraPosPort, RetalixPosPort, ThirdPartyPosPort, MediaChannelPort, AutoAuth);
        }

        private static OptEndPoints CreateOptEndPointsOtherId()
        {
            return new OptEndPoints(OtherHydraId);
        }

        private static OptEndPoints CreateOptEndPointsIdOnly()
        {
            return new OptEndPoints(HydraId);
        }


        // TODO: Commented out test cases seem wrong and just fail, though the code under test hasn't changed for a while
        public static TheoryData<OptEndPointsData> OptEndPointsTestCases = new TheoryData<OptEndPointsData>
        {
            new OptEndPointsData("Hydra ID found", new List<OptEndPoints> {CreateOptEndPointsHydra2() }, CreateOptEndPointsHydra2()),
            new OptEndPointsData("Hydra ID not found use blank", new List<OptEndPoints> { CreateOptEndPointsEmpty() }, CreateOptEndPointsHydra2()),
            // new OptEndPointsData("Hydra ID not found no blank", new List<OptEndPoints> { CreateOptEndPointsEmpty() }, CreateOptEndPointsOtherId()), // original fails
            //new OptEndPointsData("No Hydra ID use blank", new List<OptEndPoints> { CreateOptEndPointsEmpty() }, CreateOptEndPointsDefault()), // original fails
            new OptEndPointsData("No end points", new List<OptEndPoints>(), CreateOptEndPointsIdOnly()),
            // new OptEndPointsData("No end points no Hydra ID", new List<OptEndPoints>(), CreateOptEndPointsOtherId()) // original fails
        };

        [Theory]
        [MemberData(nameof(OptEndPointsTestCases), MemberType = typeof(HydraDbUnitTests))]
        public void fetch_end_points_returns_expected_end_points(OptEndPointsData optEndPointsData)
        {
            // Arrange
            _dbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(optEndPointsData.EndPoints);

            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid)x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid)x[1]));

            var hydraDb = CreateDefaultInstance();

            // Act
            var result = hydraDb.DoFetchEndPoints(HydraId);

            // Assert
            OptEndPointsMatch(result, optEndPointsData.Expected);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        #endregion

        [Fact]
        public void test_fetch_pump_end_point()
        {
            // Arrange
            PumpEndPoint pumpEndPoint = new PumpEndPoint("***************", 12341);
            _dbExecutor.Query<Htec.Foundation.Connections.Models.GenericEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> {pumpEndPoint});
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetPumpEndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            var result = _hydraDb.FetchPumpEndPoint();

            // Assert
            result.EndPoint.ToString().Should().BeEquivalentTo(pumpEndPoint.EndPoint.ToString());
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetPumpEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        [SuppressMessage("ReSharper", "RedundantArgumentDefaultValue")]
        public void test_fetch_pump_end_point_default()
        {
            // Arrange
            PumpEndPoint pumpEndPoint = new PumpEndPoint("***************", 12341);
            _dbExecutor.Query<Htec.Foundation.Connections.Models.GenericEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> {pumpEndPoint, pumpEndPoint});
            const string defaultPumpEndPointString = "127.0.0.1:1259";

            // Act
            var result = _hydraDb.FetchPumpEndPoint();

            // Assert
            result.EndPoint.ToString().Should().BeEquivalentTo(defaultPumpEndPointString);
        }

        [Fact]
        public void test_fetch_esocket_end_points()
        {
            // Arrange
            ESocketEndPoint eSocketEndPoint1 = new ESocketEndPoint("***************", 12341);
            ESocketEndPoint eSocketEndPoint2 = new ESocketEndPoint("***************", 12342);
            _dbExecutor.Query<ESocketEndPoint>("GetESocketEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(new List<ESocketEndPoint> {eSocketEndPoint1, eSocketEndPoint2});
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetESocketEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));
            //_logger.IsDebugEnabled.Returns(true);

            var hydraDb = CreateDefaultInstance();

            // Act
            var result = hydraDb.DoFetchESocketEndPoints();

            // Assert
            result.Should().BeEquivalentTo(new List<ESocketEndPoint> {eSocketEndPoint1, eSocketEndPoint2});
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetESocketEndPoints", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_fetch_all_pumps()
        {
            // Arrange
            IList<TermId> allTids = new List<TermId> {new TermId("99979902", "345"), new TermId("99979905", "346")};
            IList<PumpTid> pumpTids = new List<PumpTid>
            {
                new PumpTid(1, "99979902", "1234", false, true, false, true, false, false, false),
                new PumpTid(2, "99979903", "5678", false, true, false, true, false, false, false),
                new PumpTid(3, "99979904", null, false, true, false, true, false, false, false),
                new PumpTid(4, "99979905", null, false, true, false, true, false, false, false)
            };
            IList<PumpTid> expectedPumpTids = new List<PumpTid>
            {
                new PumpTid(1, "99979902", "1234", false, true, false, true, false, false, false),
                new PumpTid(2, null, "5678", false, true, false, true, false, false, false),
                new PumpTid(4, "99979905", null, false, true, false, true, false, false, false)
            };
            _dbExecutor.Query<PumpTid>("GetPumpTids", commandType: CommandType.StoredProcedure).Returns(pumpTids);
            _paymentConfig.AllPumpTids.Returns(allTids);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetPumpTids", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            IList<PumpTid> result = _hydraDb.FetchAllPumps();

            // Assert
            result.Should().BeEquivalentTo(expectedPumpTids);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetPumpTids", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_fetch_opt_mode()
        {
            // Arrange
            const string optId1 = "5678";
            const string optId2 = "1234";
            OptMode optMode1 = new OptMode(optId1, true);
            OptMode optMode2 = new OptMode(optId2);
            IList<OptMode> optModes = new List<OptMode> {optMode2, optMode1};
            _dbExecutor.Query<OptMode>("GetOPTMode", commandType: CommandType.StoredProcedure).Returns(optModes);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetOPTMode", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            OptMode result = _hydraDb.FetchOptMode(optId1);

            // Assert
            result.Should().BeEquivalentTo(optMode1);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetOPTMode", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        [SuppressMessage("ReSharper", "RedundantArgumentDefaultValue")]
        public void test_fetch_opt_mode_default()
        {
            // Arrange
            const string optId1 = "5678";
            const string optId2 = "1234";
            const string optId3 = "4321";
            OptMode optMode1 = new OptMode(optId1, true);
            OptMode optMode2 = new OptMode(optId2);
            OptMode optMode3 = new OptMode(optId3, false);
            IList<OptMode> optModes = new List<OptMode> {optMode2, optMode1};
            _dbExecutor.Query<OptMode>("GetOPTMode", commandType: CommandType.StoredProcedure).Returns(optModes);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetOPTMode", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            OptMode result = _hydraDb.FetchOptMode(optId3);

            // Assert
            result.Should().BeEquivalentTo(optMode3);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetOPTMode", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_fetch_payment_timeout()
        {
            // Arrange
            const PaymentTimeoutType mode1 = PaymentTimeoutType.Pod;
            const PaymentTimeoutType mode2 = PaymentTimeoutType.Mixed;
            const int timeout1 = 10;
            const int timeout2 = 20;
            IList<PaymentTimeout> paymentTimeouts = new List<PaymentTimeout>
            {
                new PaymentTimeout(mode2, timeout2),
                new PaymentTimeout(mode1, timeout1)
            };
            _dbExecutor.Query<PaymentTimeout>("GetPaymentTimeout", commandType: CommandType.StoredProcedure).Returns(paymentTimeouts);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetPaymentTimeout", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            int result = _hydraDb.FetchPaymentTimeout(mode1);

            // Assert

            result.Should().Be(timeout1);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetPaymentTimeout", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_fetch_payment_timeout_default()
        {
            // Arrange
            const PaymentTimeoutType mode1 = PaymentTimeoutType.Opt;
            const PaymentTimeoutType mode2 = PaymentTimeoutType.Mixed;
            const int defaultTimeout = 30;
            const int timeout2 = 20;
            IList<PaymentTimeout> paymentTimeouts = new List<PaymentTimeout> {new PaymentTimeout(mode2, timeout2)};
            _dbExecutor.Query<PaymentTimeout>("GetPaymentTimeout", commandType: CommandType.StoredProcedure).Returns(paymentTimeouts);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetPaymentTimeout", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            int result = _hydraDb.FetchPaymentTimeout(mode1);

            // Assert
            result.Should().Be(defaultTimeout);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetPaymentTimeout", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_prune_transactions()
        {
            // Arrange
            const int pruneDays = 20;
            List<object> pruneParameters = new List<object>();
            _dbExecutor.Execute("PruneTransactions", Arg.Do<object>(x => pruneParameters.Add(x)), null, Arg.Any<int?>(),
                Arg.Any<CommandType?>());
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("PruneTransactions", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.PruneTransactions(pruneDays);

            // Assert
            pruneParameters.Should().HaveCount(1);
            DateTime? pruneDate = (DateTime?) pruneParameters[0]?.GetType().GetProperty("checkDate")?.GetValue(pruneParameters[0], null);
            pruneDate.Should().NotBeNull();
            pruneDate.Should().BeCloseTo(DateTime.Now.AddDays(pruneDays * -1), 50);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("PruneTransactions", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        #endregion

        #region Receipts Tests

        [Fact]
        public void test_fetch_receipts()
        {
            // Arrange
            const string cardNumber1 = "1234";
            const string content1 = "A Receipt";
            const string opt = "1234";
            const int transactionNumber = 34;
            const int timeout = 10;
            const int maxCount = 20;
            DateTime expiry1 = DateTime.Now.AddSeconds(10);
            Receipt expectedReceipt = new Receipt(content1);
            IList<ReceiptInfo> receipts = new List<ReceiptInfo> {new ReceiptInfo(cardNumber1, content1, expiry1, opt, transactionNumber, 500, DateTime.Now, 1)};
            IList<int> timeouts = new List<int> {timeout};
            IList<int> maxCounts = new List<int> {maxCount};
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(receipts);
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);
            _dbExecutor.Query<int>("GetReceiptMaxCount", commandType: CommandType.StoredProcedure).Returns(maxCounts);

            // Act
            _hydraDb.FetchReceipts();
            var result = _hydraDb.GetReceipt(cardNumber1);

            // Assert
            result.Should().BeEquivalentTo(expectedReceipt);
            _hydraDb.ReceiptTimeout.Should().Be(timeout);
            _hydraDb.ReceiptMaxCount.Should().Be(maxCount);
        }

        [Fact]
        public void test_fetch_receipts_for_opt()
        {
            // Arrange
            const string cardNumber1 = "1234";
            const string cardNumber2 = "5678";
            const int transactionNumber1 = 34;
            const int transactionNumber2 = 35;
            const string content1 = "A Receipt";
            const string content2 = "Another Receipt";
            const string opt = "1234";
            DateTime expiry1 = DateTime.Now.AddSeconds(10);
            IList<ReceiptInfo> receipts = new List<ReceiptInfo>
            {
                new ReceiptInfo(cardNumber1, content1, expiry1, opt, transactionNumber1, 500, DateTime.Now, 1),
                new ReceiptInfo(cardNumber2, content2, expiry1, opt, transactionNumber2, 500, DateTime.Now, 1)
            };
            IList<int> timeouts = new List<int> {10};
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(receipts);
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);

            // Act
            _hydraDb.FetchReceipts();
            var result = _hydraDb.GetReceiptsForOpt(opt);

            // Assert
            result.Should().HaveCount(2);
            foreach (var item in result)
            {
                item.ReceiptContent.Should().BeOneOf(content1, content2);
            }
        }

        [Fact]
        public void save_receipt_zero_transaction_number_returns_false()
        {
            // Arrange
            const string content1 = "A Receipt";
            
            // Act
            var result = _hydraDb.SaveReceipt(DefaultCardNumber, new ReceiptTransaction { Details = content1 }, DefaultOpt, 0);

            // Assert
            result.Should().BeFalse();
        }
        
        [Theory]
        [InlineData(null),
        InlineData(""),
        InlineData(" "),
        InlineData("A Receipt")]
        public void save_receipt_no_customer_copy_in_text_returns_false(string receiptText)
        {
            // Arrange
            const int transactionNumber1 = 34;

            // Act
            var result = _hydraDb.SaveReceipt(DefaultCardNumber, new ReceiptTransaction { Details = receiptText }, DefaultOpt, transactionNumber1);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void save_receipt_null_transaction_time_does_not_prevent_save()
        {
            // Arrange
            const int transactionNumber1 = 34;
            const string content1 = "A Receipt Text=\"Customer Copy\"";

            // Act
            var result = _hydraDb.SaveReceipt(DefaultCardNumber, new ReceiptTransaction { Details = content1 }, DefaultOpt, transactionNumber1);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public void test_save_receipt()
        {
            // Arrange
            const string cardNumber1 = "1234";
            const string cardNumber2 = "5678";
            const string content1 = "A Receipt" + Forecourt.Common.HydraDbClasses.HydraDb.CustomerCopy;
            const string content2 = "Another Receipt" + Forecourt.Common.HydraDbClasses.HydraDb.CustomerCopy;
            const string content3 = "Changed Receipt" + Forecourt.Common.HydraDbClasses.HydraDb.CustomerCopy;
            const int transactionNumber1 = 34;
            const int transactionNumber2 = 35;
            const string opt = "1234";

            var transactionTime = DateTime.Now;

            var receipt1 = new ReceiptInfo(cardNumber1, content1, transactionTime.AddSeconds(-10), opt, transactionNumber1, 500, transactionTime, 1);
            var receipt2 = new ReceiptInfo(cardNumber2, content2, transactionTime.AddSeconds(50), opt, transactionNumber2, 500, transactionTime, 1);
            Receipt expectedReceipt1 = new Receipt(content3);
            Receipt expectedReceipt2 = new Receipt(content2);
            IList<ReceiptInfo> receipts = new List<ReceiptInfo> {receipt1, receipt2};
            IList<int> timeouts = new List<int> {10};
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(receipts);
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);
            object dbAddParameters = null;
            _dbExecutor.Execute("AddReceipt", Arg.Do<object>(x => dbAddParameters = x), null, Arg.Any<int?>(), Arg.Any<CommandType?>());

            // Act
            _hydraDb.FetchReceipts();
            _hydraDb.SaveReceipt(cardNumber1, new ReceiptTransaction {Details = content3, CardNumber = cardNumber1}, opt, 36);
            var result1 = _hydraDb.GetReceipt(cardNumber1);
            var result2 = _hydraDb.GetReceipt(cardNumber2);

            // Assert
            dbAddParameters.Should().NotBeNull();
            result1.ReceiptContent.Should().BeEquivalentTo(expectedReceipt1.ReceiptContent);
            result2.ReceiptContent.Should().BeEquivalentTo(expectedReceipt2.ReceiptContent);
        }

        [Fact]
        public void test_set_receipt_timeout()
        {
            // Arrange
            const int timeout = 100;
            IList<object> objects = new List<object>();
            _dbExecutor.Execute("SetReceiptTimeout", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetReceiptTimeout(timeout);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {timeout});
            }
        }

        [Fact]
        public void test_set_receipt_max_count()
        {
            // Arrange
            const int maxCount = 100;
            IList<object> objects = new List<object>();
            _dbExecutor.Execute("SetReceiptMaxCount", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetReceiptMaxCount(maxCount);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {maxCount});
            }
        }

        // TODO: Seems to work sometimes, but might be getting tangled with reinitialisation of OPT collection
        [Theory(Skip = "Flaky test, not entirely sure why"),
        InlineData(1),
        InlineData(2),
        InlineData(3),
        InlineData(4)]
        public void get_receipts_other_calls_do_not_affect_returned_result(int seed) // Something of a wonky test, but need to hit it repeatedly to prove it's fine
        {
            // Arrange
            const string opt = "1234";
            var expiry = DateTime.Now.AddDays(-10);

            var rand = new Random(seed);

            var receipts = new List<ReceiptInfo>();

            for (var i = 0; i < 100000; i++)
            {
                receipts.Add(new ReceiptInfo(rand.Next(9999).ToString(), $"A Receipt{i}", expiry, opt, i, 500, DateTime.Now, 1));
            }

            IList<int> timeouts = new List<int> {10};
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(receipts);
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);

            _hydraDb.SetReceiptMaxCount(50);

            // Act
            _hydraDb.FetchReceipts();
            var resultTask = new Task<IList<ReceiptInfo>>(() => _hydraDb.GetReceiptsForOpt(opt));
            var pruneTask = new Task(() => _hydraDb.PruneReceipts(1));

            resultTask.Start();
            pruneTask.Start();

            // Assert
            resultTask.Result.Count.Should().Be(50);
        }

        [Theory,
         InlineData(1),
         InlineData(2),
         InlineData(3),
         InlineData(4)]
        public void get_receipt_other_calls_do_not_affect_returned_result(int seed) // Something of a wonky test, but need to hit it repeatedly to prove it's fine
        {
            // Arrange
            const string opt = "1234";
            var expiry = DateTime.Now.AddDays(-10);

            var rand = new Random(seed);

            var receipts = new List<ReceiptInfo>();

            for (var i = 0; i < 100000; i++)
            {
                receipts.Add(new ReceiptInfo(rand.Next(9999).ToString(), $"A Receipt{i}", expiry, opt, i, 500, DateTime.Now, 1));
            }

            var matchingTransaction = receipts.Last();

            IList<int> timeouts = new List<int> { 10 };
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(receipts);
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);

            _hydraDb.SetReceiptMaxCount(50);

            // Act
            _hydraDb.FetchReceipts();
            var resultTask = new Task<ReceiptInfo>(() => _hydraDb.GetReceipt(matchingTransaction.CardNumber));
            var pruneTask = new Task(() => _hydraDb.PruneReceipts(1));

            resultTask.Start();
            pruneTask.Start();

            // Assert
            resultTask.Result.Should().BeNull();
        }

        #endregion

        #region Database Store Tests

        // TODO: Needs rework as FetchEndPoints called by SetServiceAddress uses cache helper
        [Fact(Skip = "Internal use of cache helper")]
        public void test_set_service_address()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetOPTEndPoints", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            _dbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure).Returns(new List<OptEndPoints>
            {
                new OptEndPoints(HydraId, OtherIpAddress, FromOptPort, ToOptPort, HeartbeatPort, HydraPosPort, RetalixPosPort,
                    ThirdPartyPosPort, MediaChannelPort)
            });
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetOPTEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetServiceAddress(HydraId, IpAddress);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    hydraId = HydraId,
                    ipAddress = IpAddressString,
                    fromOPTPort = FromOptPort,
                    toOPTPort = ToOptPort,
                    heartbeatPort = HeartbeatPort,
                    controllerPort = ControllerPort,
                    hydraPosPort = HydraPosPort,
                    retalixPosPort = RetalixPosPort,
                    thirdPartyPosPort = ThirdPartyPosPort,
                    mediaChannelPort = MediaChannelPort
                });
            }

            _telemetryWorker.Received(2).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetOPTEndPoints", Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>());
            guids.Should().HaveCount(4);
            guids[0].Should().Be(guids[1]);
            guids[2].Should().Be(guids[3]);
        }

        // TODO: Needs rework as FetchEndPoints called by SetServiceAddress uses cache helper
        [Fact(Skip = "Internal use of cache helper")]
        public void test_set_service_ports()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetOPTEndPoints", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            _dbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure).Returns(new List<OptEndPoints>
            {
                new OptEndPoints(HydraId, IpAddressString, OtherFromOptPort, OtherToOptPort, OtherHeartbeatPort, OtherControllerPort,
                    OtherHydraPosPort, OtherRetalixPosPort, OtherThirdPartyPosPort)
            });
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetOPTEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetServicePorts(HydraId, FromOptPort, ToOptPort, HeartbeatPort, HydraPosPort, RetalixPosPort, ThirdPartyPosPort,
                MediaChannelPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    hydraId = HydraId,
                    ipAddress = IpAddressString,
                    fromOptPort = FromOptPort,
                    toOptPort = ToOptPort,
                    heartbeatPort = HeartbeatPort,
                    controllerPort = ControllerPort,
                    hydraPosPort = HydraPosPort,
                    retalixPosPort = RetalixPosPort,
                    thirdPartyPosPort = ThirdPartyPosPort,
                    mediaChannelPort = MediaChannelPort
                });
            }

            _telemetryWorker.Received(2).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetOPTEndPoints", Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetOPTEndPoints", Arg.Any<Guid>());
            guids.Should().HaveCount(4);
            guids[0].Should().Be(guids[1]);
            guids[2].Should().Be(guids[3]);
        }

        [Fact]
        public void test_set_pump_end_point()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpEndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetPumpEndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetPumpEndPoint(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetPumpEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_divert_opt()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetDivertOPT", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetDivertOPT", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetDivertOpt(IpAddress, FromOptPort, ToOptPort, HeartbeatPort, MediaChannelPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    fromOptPort = FromOptPort,
                    toOptPort = ToOptPort,
                    heartbeatPort = HeartbeatPort,
                    mediaChannelPort = MediaChannelPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetDivertOPT", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_fetch_divert_opt()
        {
            // Arrange
            DivertOpt divertOpt = new DivertOpt(true, IpAddressString, FromOptPort, ToOptPort, HeartbeatPort, MediaChannelPort);
            _dbExecutor.Query<DivertOpt>("GetDivertOpt", commandType: CommandType.StoredProcedure).Returns(new List<DivertOpt> {divertOpt});
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetDivertOpt", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            DivertOpt result = _hydraDb.FetchDivertOpt();

            // Assert
            result.IsDiverted.Should().Be(divertOpt.IsDiverted);
            result.IpAddress.Should().Be(divertOpt.IpAddress);
            result.FromOptPort.Should().Be(divertOpt.FromOptPort);
            result.ToOptPort.Should().Be(divertOpt.ToOptPort);
            result.HeartbeatPort.Should().Be(divertOpt.HeartbeatPort);
            result.MediaChannelPort.Should().Be(divertOpt.MediaChannelPort);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetDivertOpt", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_opt_diverted()
        {
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetOPTDiverted", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetOPTDiverted", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetOptDiverted();

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {isDiverted = true});
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetOPTDiverted", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_opt_not_diverted()
        {
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetOPTDiverted", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetOPTDiverted", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetOptNotDiverted();

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {isDiverted = false});
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetOPTDiverted", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_anpr_end_point()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetANPREndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetANPREndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetAnprEndPoint(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetANPREndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_car_wash_end_point()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetCarWashEndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetCarWashEndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetCarWashEndPoint(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetCarWashEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_tank_gauge_end_point()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetTankGaugeEndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetTankGaugeEndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetTankGaugeEndPoint(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetTankGaugeEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_add_esocket()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddESocketEndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("AddESocketEndPoint", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.AddEsocket(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("AddESocketEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_remove_esocket()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("RemoveESocketEndPoint", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("RemoveESocketEndPoint", Arg.Any<Guid>()))
                .Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.RemoveEsocket(IpAddress, OtherPort);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString,
                    port = OtherPort
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("RemoveESocketEndPoint", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_set_retalix_pos_primary_ip_address()
        {
            // Arrange
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetRetalixPrimary", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("SetRetalixPrimary", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            _hydraDb.SetRetalixPosPrimaryIpAddress(IpAddress);

            // Assert
            _dbExecutor.DidNotReceive().Execute("ClearRetalixPrimary", null, null, null, CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    ipAddress = IpAddressString
                });
            }

            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("SetRetalixPrimary", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_clear_retalix_pos_primary_ip_address()
        {
            // Arrange

            // Act
            _hydraDb.SetRetalixPosPrimaryIpAddress(null);

            // Assert
            _dbExecutor.Received(1).Execute("ClearRetalixPrimary", null, null, null, CommandType.StoredProcedure);
            _dbExecutor.DidNotReceive().Execute("SetRetalixPrimary", Arg.Any<object>(), null, null, CommandType.StoredProcedure);
        }

        [Fact]
        public void test_get_retalix_pos_primary_ip_address()
        {
            // Arrange
            _dbExecutor.Query<string>("GetRetalixPrimary", commandType: CommandType.StoredProcedure)
                .Returns(new List<string> {IpAddressString});
            IList<Guid> guids = new List<Guid>();
            _telemetryWorker.When(x => x.QuerySentToHydraDb(Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[0]));
            _telemetryWorker.When(x => x.QueryReturnedFromHydraDb("GetRetalixPrimary", Arg.Any<Guid>())).Do(x => guids.Add((Guid) x[1]));

            // Act
            IPAddress result = _hydraDb.GetRetalixPosPrimaryIpAddress();

            // Assert
            result.Should().Be(IpAddress);
            _telemetryWorker.Received(1).QuerySentToHydraDb(Arg.Any<Guid>());
            _telemetryWorker.Received(1).QueryReturnedFromHydraDb("GetRetalixPrimary", Arg.Any<Guid>());
            guids.Should().HaveCount(2);
            guids[0].Should().Be(guids[1]);
        }

        [Fact]
        public void test_map_opt()
        {
            // Arrange
            const byte pump = 2;
            const string opt = "OPT1";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddPumpOPT", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.MapOpt(pump, opt);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    number = pump,
                    opt
                });
            }
        }

        [Fact]
        public void test_unmap_opt()
        {
            // Arrange
            const byte pump = 2;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddPumpOPT", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.MapOpt(pump, null);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    number = pump
                });
            }
        }

        [Fact]
        public void test_map_tid()
        {
            // Arrange
            const byte pump = 2;
            const string tid = "TID1";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddPumpTID", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.MapTid(pump, tid);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    number = pump,
                    tid
                });
            }
        }

        [Fact]
        public void test_unmap_tid()
        {
            // Arrange
            const byte pump = 2;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddPumpTID", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.MapTid(pump, null);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    number = pump
                });
            }
        }

        [Theory]
        [InlineData(PaymentTimeoutType.Mixed, 2000),
         InlineData(PaymentTimeoutType.Opt, 1500),
         InlineData(PaymentTimeoutType.Kiosk, 3000),
         InlineData(PaymentTimeoutType.Pod, 60000),
         InlineData(PaymentTimeoutType.NozzleDown, 4000)]
        public void set_payment_timeout_sets_expected_value(PaymentTimeoutType timeoutType, int timeout)
        {
            // Arrange
            var objects = new List<object>();
            _dbExecutor.Execute("SetPaymentTimeout", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPaymentTimeout(timeoutType, timeout);

            // Assert
            objects.Should().HaveCount(1);
            foreach (var item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    mode = timeoutType,
                    timeout
                });
            }
        }

        [Fact]
        public void get_site_info_success_returns_expected_values()
        {
            // Arrange
            var siteInfo = new SiteInfo(1, "Site Name", "VAT Number", true, true, 100, true, 100, 200, 0);
            _configurationRepository.GetSiteInfo()
                .Returns(Result.Success(siteInfo));

            var hydraDb = CreateDefaultInstance();

            // Act
            var result = hydraDb.DoGetSiteInfo();

            // Assert
            result.Should().BeEquivalentTo(siteInfo);
        }


        [Fact]
        public void get_site_info_failure_returns_default_values()
        {
            // Arrange
            _configurationRepository.GetSiteInfo()
                .Returns(Result.Failure<SiteInfo>("Failure"));

            var hydraDb = CreateDefaultInstance();

            // Act
            var result = hydraDb.DoGetSiteInfo();

            // Assert
            var defaultSiteInfo = new SiteInfo(1, null, null, true, true, 826, false, 99, 99, 0);

            result.Should().BeEquivalentTo(defaultSiteInfo);
        }


        [Fact]
        public void test_set_receipt_layout_mode()
        {
            // Arrange
            const int mode = 2000;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetReceiptLayoutMode", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetReceiptLayoutMode(mode);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {mode});
            }
        }

        [Fact]
        public void test_set_site_name()
        {
            // Arrange
            const string siteName = "A Name";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetSiteName", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetSiteName(siteName);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {siteName});
            }
        }

        [Fact]
        public void test_set_vat_number()
        {
            // Arrange
            const string vatNumber = "1111";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetVATNumber", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetVatNumber(vatNumber);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {vatNumber});
            }
        }

        [Fact]
        public void test_set_nozzle_up_for_kiosk_use()
        {
            // Arrange
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetNozzleUpForKioskUse", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetNozzleUpForKioskUse(flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {flag});
            }
        }

        [Fact]
        public void test_set_currency_code()
        {
            // Arrange
            const int currencyCode = 100;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetCurrencyCode", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetCurrencyCode(currencyCode);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {currencyCode});
            }
        }

        [Fact]
        public void test_set_forward_fuel_price_update()
        {
            // Arrange
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetForwardFuelPriceUpdate", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetForwardFuelPriceUpdate(flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {flag});
            }
        }

        [Fact]
        public void test_set_playlist_filename()
        {
            // Arrange
            const string opt = "OPT1";
            const string playlistFileName = "File Name";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPlaylistFileName", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPlaylistFileName(opt, playlistFileName);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {opt, playlistFileName});
            }
        }

        [Fact]
        public void test_set_receipt_header()
        {
            // Arrange
            const string opt = "OPT1";
            const string receiptHeader = "A Header";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetReceiptHeader", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetReceiptHeader(opt, receiptHeader);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {opt, receiptHeader});
            }
        }

        [Fact]
        public void test_set_receipt_footer()
        {
            // Arrange
            const string opt = "OPT1";
            const string receiptFooter = "A Footer";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetReceiptFooter", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetReceiptFooter(opt, receiptFooter);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new { opt, receiptFooter });
            }
        }

        [Fact]
        public void test_set_kiosk_only()
        {
            // Arrange
            const byte pump = 2;
            const bool setDefault = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpKioskOnly", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetKioskOnly(pump, setDefault);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, setDefault});
            }
        }

        [Fact]
        public void test_set_mixed()
        {
            // Arrange
            const byte pump = 2;
            const bool setDefault = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpMixed", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetMixed(pump, setDefault);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, setDefault});
            }
        }

        [Fact]
        public void test_set_outside_only()
        {
            // Arrange
            const byte pump = 2;
            const bool setDefault = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpOutsideOnly", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetOutsideOnly(pump, setDefault);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, setDefault});
            }
        }

        [Fact]
        public void test_set_pump_closed()
        {
            // Arrange
            const byte pump = 2;
            const bool closed = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpClosed", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPumpClosed(pump, closed);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, closed});
            }
        }

        [Fact]
        public void test_set_pump_max_fill_override_for_fuel_cards()
        {
            // Arrange
            const byte pump = 2;
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpMaxFillOverrideForFuelCards", Arg.Do<object>(x => objects.Add(x)), null, null,
                CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPumpMaxFillOverrideForFuelCards(pump, flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, flag});
            }
        }

        [Fact]
        public void test_set_pump_max_fill_override_for_payment_cards()
        {
            // Arrange
            const byte pump = 2;
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetPumpMaxFillOverrideForPaymentCards", Arg.Do<object>(x => objects.Add(x)), null, null,
                CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPumpMaxFillOverrideForPaymentCards(pump, flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = pump, flag});
            }
        }

        // TODO: Might want to change this to just be a cache check as the saving code is in a repo class
        [Fact(Skip = "Code is using repository so test is anemic?")]
        public void test_set_contactless()
        {
            // Arrange
            const string opt = "OPT1";
            const bool contactless = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetOPTContactless", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetContactless(contactless);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {opt, contactless});
            }
        }

        [Fact]
        public void test_set_auto_auth()
        {
            // Arrange
            const bool autoAuth = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetAutoAuth", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetAutoAuth(HydraId, autoAuth);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {hydraId = HydraId, autoAuth});
            }
        }

        [Fact]
        public void test_set_media_channel()
        {
            // Arrange
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetMediaChannel", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetMediaChannel(HydraId, flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {hydraId = HydraId, mediaChannel = flag});
            }
        }

        [Fact]
        public void test_set_unmanned_pseudo_pos()
        {
            // Arrange
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetUnmannedPseudoPOS", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetUnmannedPseudoPos(HydraId, flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {hydraId = HydraId, unmanned = flag});
            }
        }

        [Fact]
        public void test_set_asda_day_end_report()
        {
            // Arrange
            const bool flag = true;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetAsdaDayEndReport", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetAsdaDayEndReport(HydraId, flag);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {hydraId = HydraId, isAsda = flag});
            }
        }

        [Fact]
        public void test_set_grade_name()
        {
            // Arrange
            const byte grade = 3;
            const string gradeName = "Unleaded";
            const float vatRate = 17.5F;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetGradeName", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetGradeName(grade, gradeName, vatRate);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {grade, gradeName, vatRate});
            }
        }

        [Fact]
        public void test_add_transaction()
        {
            // Arrange
            const string gradeName = "Unleaded";
            const string code = "11";
            const uint quantity = 3000;
            const uint amount = 4000;
            const string pumpDetails = "A Pump";
            const string cardNumber = "1234";
            DateTime transactionTime = DateTime.Now;
            const string category = "98";
            const string subCategory = "4";
            const string discountName = "A Discount";
            const string discountCode = "AA";
            const uint discountValue = 100;
            const string discountCardNumber = "1234";
            const uint localAccountMileage = 1234;
            const string localAccountRegistration = "ABCD";
            const string txnNumber = "765";
            const int transactionNumber = 100;
            List<DynamicParameters> objects = new List<DynamicParameters>();
            _dbExecutor.Execute("AddTransaction", Arg.Do<DynamicParameters>(x =>
            {
                x.Add("@transactionNumber", transactionNumber);
                objects.Add(x);
            }), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.AddTransaction(gradeName, code, quantity, amount, pumpDetails, cardNumber, transactionTime, category, subCategory,
                discountName, discountCode, discountValue, discountCardNumber, localAccountMileage, localAccountRegistration, txnNumber, MaxRetalixTransactionNumber,
                out int outputTransactionNumber);

            // Assert
            objects.Should().HaveCount(1);
            foreach (DynamicParameters item in objects)
            {
                item.Get<string>("@gradeName").Should().BeEquivalentTo(gradeName);
                item.Get<string>("@code").Should().BeEquivalentTo(code);
                item.Get<long>("@quantity").Should().Be(quantity);
                item.Get<long>("@amount").Should().Be(amount);
                item.Get<string>("@pumpDetails").Should().BeEquivalentTo(pumpDetails);
                item.Get<string>("@cardNumber").Should().BeEquivalentTo(cardNumber);
                item.Get<DateTime>("@transactionTime").Should().Be(transactionTime);
                item.Get<string>("@category").Should().BeEquivalentTo(category);
                item.Get<string>("@subcategory").Should().BeEquivalentTo(subCategory);
                item.Get<string>("@discountName").Should().BeEquivalentTo(discountName);
                item.Get<string>("@discountCode").Should().BeEquivalentTo(discountCode);
                item.Get<long>("@discountValue").Should().Be(discountValue);
                item.Get<string>("@discountCardNumber").Should().BeEquivalentTo(discountCardNumber);
                item.Get<long>("@localAccountMileage").Should().Be(localAccountMileage);
                item.Get<string>("@localAccountRegistration").Should().BeEquivalentTo(localAccountRegistration);
                item.Get<string>("@txnNumber").Should().BeEquivalentTo(txnNumber);
            }

            outputTransactionNumber.Should().Be(transactionNumber);
        }

        [Fact]
        public void test_add_to_transaction()
        {
            // Arrange
            const string gradeName = "Unleaded";
            const string code = "11";
            const uint quantity = 3000;
            const uint amount = 4000;
            const string pumpDetails = "A Pump";
            const string cardNumber = "1234";
            DateTime transactionTime = DateTime.Now;
            const string category = "98";
            const string subCategory = "4";
            const int transactionNumber = 100;
            List<DynamicParameters> objects = new List<DynamicParameters>();
            _dbExecutor.Execute("AddToTransaction", Arg.Do<DynamicParameters>(x => objects.Add(x)), null, null,
                CommandType.StoredProcedure);

            // Act
            _hydraDb.AddToTransaction(transactionNumber, gradeName, code, quantity, amount, pumpDetails, cardNumber, transactionTime,
                category, subCategory);

            // Assert
            objects.Should().HaveCount(1);
            foreach (DynamicParameters item in objects)
            {
                item.Get<int>("@transactionNumber").Should().Be(transactionNumber);
                item.Get<string>("@gradeName").Should().BeEquivalentTo(gradeName);
                item.Get<string>("@code").Should().BeEquivalentTo(code);
                item.Get<long>("@quantity").Should().Be(quantity);
                item.Get<long>("@amount").Should().Be(amount);
                item.Get<string>("@pumpDetails").Should().BeEquivalentTo(pumpDetails);
                item.Get<string>("@cardNumber").Should().BeEquivalentTo(cardNumber);
                item.Get<DateTime>("@transactionTime").Should().Be(transactionTime);
                item.Get<string>("@category").Should().BeEquivalentTo(category);
                item.Get<string>("@subcategory").Should().BeEquivalentTo(subCategory);
            }
        }

        [Fact]
        public void test_add_event()
        {
            // Arrange
            DateTime transactionTime = DateTime.Now;
            const int transactionNumber = 100;
            List<DynamicParameters> objects = new List<DynamicParameters>();
            _dbExecutor.Execute("AddEvent", Arg.Do<DynamicParameters>(x =>
            {
                x.Add("@transactionNumber", transactionNumber);
                objects.Add(x);
            }), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.AddEvent(transactionTime, out int outputTransactionNumber);

            // Assert
            objects.Should().HaveCount(1);
            foreach (DynamicParameters item in objects)
            {
                item.Get<DateTime>("@transactionTime").Should().Be(transactionTime);
            }

            outputTransactionNumber.Should().Be(transactionNumber);
        }

        [Fact]
        public void test_add_day_end()
        {
            // Arrange
            const uint fuelAmount = 3000;
            const uint dryAmount = 4000;
            const uint quantity = 5000;
            const uint discountValue = 100;
            const int transactionNumber = 100;
            const short category = 98;
            const short subcategory = 5;
            const string gradeCode = "1111";
            const string gradeName = "Unleaded";
            const string cardProductName = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddDayEnd", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.AddDayEnd(fuelAmount, dryAmount, quantity, transactionNumber, category, subcategory, gradeCode, gradeName,
                cardProductName, discountValue);

            // Assert
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    fuelCash = fuelAmount,
                    dryCash = dryAmount,
                    quantity,
                    transaction = transactionNumber,
                    category,
                    subcategory,
                    gradeCode,
                    gradeName,
                    cardProductName,
                    discount = discountValue
                });
            }
        }

        [Fact]
        public void test_take_day_end()
        {
            // Arrange
            const uint fuelAmount = 3000;
            const uint dryAmount = 4000;
            const uint discountValue = 100;
            DateTime endTime = DateTime.Now;
            DateTime startTime = endTime.AddDays(-1);
            const int firstTransactionNumber = 100;
            const int lastTransactionNumber = 200;
            const int shiftNumber = 300;
            _dbExecutor.Execute("TakeDayEnd", Arg.Do<DynamicParameters>(x =>
            {
                x.Add("@fuelCash", (long) fuelAmount);
                x.Add("@dryCash", (long) dryAmount);
                x.Add("@discount", (long) discountValue);
                x.Add("@startTime", startTime);
                x.Add("@endTime", endTime);
                x.Add("@firstTransaction", firstTransactionNumber);
                x.Add("@lastTransaction", lastTransactionNumber);
                x.Add("@shiftNumber", shiftNumber);
            }), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.TakeDayEnd(true, out uint returnedFuelAmount, out uint returnedDryAmount, out uint returnedDiscount,
                out DateTime returnedStartTime, out DateTime returnedEndTime, out int returnedFirstTransaction,
                out int returnedLastTransaction, out int returnedShiftNumber);

            // Assert
            _dbExecutor.Received(1).Execute("TakeDayEnd", Arg.Any<DynamicParameters>(), null, null, CommandType.StoredProcedure);
            _dbExecutor.DidNotReceive().Execute("TakeShiftSummary", Arg.Any<DynamicParameters>(), null, null, CommandType.StoredProcedure);

            returnedFuelAmount.Should().Be(fuelAmount);
            returnedDryAmount.Should().Be(dryAmount);
            returnedDiscount.Should().Be(discountValue);
            returnedStartTime.Should().Be(startTime);
            returnedEndTime.Should().Be(endTime);
            returnedFirstTransaction.Should().Be(firstTransactionNumber);
            returnedLastTransaction.Should().Be(lastTransactionNumber);
            returnedShiftNumber.Should().Be(shiftNumber);
        }

        [Fact]
        public void test_take_shift_end()
        {
            // Arrange
            const uint fuelAmount = 3000;
            const uint dryAmount = 4000;
            const uint discountValue = 100;
            DateTime endTime = DateTime.Now;
            DateTime startTime = endTime.AddDays(-1);
            const int firstTransactionNumber = 100;
            const int lastTransactionNumber = 200;
            const int shiftNumber = 300;
            _dbExecutor.Execute("TakeShiftSummary", Arg.Do<DynamicParameters>(x =>
            {
                x.Add("@fuelCash", (long) fuelAmount);
                x.Add("@dryCash", (long) dryAmount);
                x.Add("@discount", (long) discountValue);
                x.Add("@startTime", startTime);
                x.Add("@endTime", endTime);
                x.Add("@firstTransaction", firstTransactionNumber);
                x.Add("@lastTransaction", lastTransactionNumber);
                x.Add("@shiftNumber", shiftNumber);
            }), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.TakeDayEnd(false, out uint returnedFuelAmount, out uint returnedDryAmount, out uint returnedDiscount,
                out DateTime returnedStartTime, out DateTime returnedEndTime, out int returnedFirstTransaction,
                out int returnedLastTransaction, out int returnedShiftNumber);

            // Assert
            _dbExecutor.DidNotReceive().Execute("TakeDayEnd", Arg.Any<DynamicParameters>(), null, null, CommandType.StoredProcedure);
            _dbExecutor.Received(1).Execute("TakeShiftSummary", Arg.Any<DynamicParameters>(), null, null, CommandType.StoredProcedure);

            returnedFuelAmount.Should().Be(fuelAmount);
            returnedDryAmount.Should().Be(dryAmount);
            returnedDiscount.Should().Be(discountValue);
            returnedStartTime.Should().Be(startTime);
            returnedEndTime.Should().Be(endTime);
            returnedFirstTransaction.Should().Be(firstTransactionNumber);
            returnedLastTransaction.Should().Be(lastTransactionNumber);
            returnedShiftNumber.Should().Be(shiftNumber);
        }

        [Fact]
        public void test_take_item_sales_for_shift()
        {
            // Arrange
            const int category = 97;
            const int subcategory = 4;
            const string gradeCode = "44";
            const string gradeName = "Unleaded";
            const uint amount = 4000;
            const uint quantity = 3000;
            _dbExecutor.Query<ItemSales>("TakeItemSales", commandType: CommandType.StoredProcedure).Returns(new List<ItemSales>
                {new ItemSales(category, subcategory, gradeCode, gradeName, amount, quantity)});

            // Act
            IList<ItemSales> result = _hydraDb.TakeItemSales(false);

            // Assert
            _dbExecutor.DidNotReceive().Query<ItemSales>("TakeDayItemSales", commandType: CommandType.StoredProcedure);
            _dbExecutor.Received(1).Query<ItemSales>("TakeItemSales", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            foreach (ItemSales item in result)
            {
                item.Category.Should().Be(category);
                item.Subcategory.Should().Be(subcategory);
                item.GradeCode.Should().Be(gradeCode);
                item.GradeName.Should().Be(gradeName);
                item.Amount.Should().Be(amount);
                item.Quantity.Should().Be(quantity);
            }
        }

        [Fact]
        public void test_take_item_sales_for_day()
        {
            // Arrange
            const int category = 97;
            const int subcategory = 4;
            const string gradeCode = "44";
            const string gradeName = "Unleaded";
            const uint amount = 4000;
            const uint quantity = 3000;
            _dbExecutor.Query<ItemSales>("TakeDayItemSales", commandType: CommandType.StoredProcedure).Returns(new List<ItemSales>
                {new ItemSales(category, subcategory, gradeCode, gradeName, amount, quantity)});

            // Act
            IList<ItemSales> result = _hydraDb.TakeItemSales(true);

            // Assert
            _dbExecutor.Received(1).Query<ItemSales>("TakeDayItemSales", commandType: CommandType.StoredProcedure);
            _dbExecutor.DidNotReceive().Query<ItemSales>("TakeItemSales", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            result.Should().HaveCount(1);
            foreach (ItemSales item in result)
            {
                item.Category.Should().Be(category);
                item.Subcategory.Should().Be(subcategory);
                item.GradeCode.Should().Be(gradeCode);
                item.GradeName.Should().Be(gradeName);
                item.Amount.Should().Be(amount);
                item.Quantity.Should().Be(quantity);
            }
        }

        [Fact]
        public void test_take_card_sales_for_shift()
        {
            // Arrange
            const int cardRef = 5;
            const uint amount = 4000;
            const string productName = "XXXX";
            _dbExecutor.Query<CardSales>("TakeCardSales", commandType: CommandType.StoredProcedure)
                .Returns(new List<CardSales> {new CardSales(cardRef, amount, productName)});

            // Act
            IList<CardSales> result = _hydraDb.TakeCardSales(false);

            // Assert
            _dbExecutor.DidNotReceive().Query<CardSales>("TakeDayCardSales", commandType: CommandType.StoredProcedure);
            _dbExecutor.Received(1).Query<CardSales>("TakeCardSales", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            foreach (CardSales item in result)
            {
                item.CardRef.Should().Be(cardRef);
                item.Amount.Should().Be(amount);
                item.ProductName.Should().Be(productName);
            }
        }

        [Fact]
        public void test_take_card_sales_for_day()
        {
            // Arrange
            const int cardRef = 5;
            const uint amount = 4000;
            const string productName = "XXXX";
            _dbExecutor.Query<CardSales>("TakeDayCardSales", commandType: CommandType.StoredProcedure)
                .Returns(new List<CardSales> {new CardSales(cardRef, amount, productName)});

            // Act
            IList<CardSales> result = _hydraDb.TakeCardSales(true);

            // Assert
            _dbExecutor.Received(1).Query<CardSales>("TakeDayCardSales", commandType: CommandType.StoredProcedure);
            _dbExecutor.DidNotReceive().Query<CardSales>("TakeCardSales", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            foreach (CardSales item in result)
            {
                item.CardRef.Should().Be(cardRef);
                item.Amount.Should().Be(amount);
                item.ProductName.Should().Be(productName);
            }
        }

        [Fact]
        public void test_take_card_volume_sales()
        {
            // Arrange
            const int cardRef = 5;
            const int grade = 2;
            const uint volume = 5000;
            const string productName = "XXXX";
            _dbExecutor.Query<CardVolumeSales>("TakeCardVolumeSales", commandType: CommandType.StoredProcedure)
                .Returns(new List<CardVolumeSales> {new CardVolumeSales(cardRef, grade, volume, productName)});

            // Act
            IList<CardVolumeSales> result = _hydraDb.TakeCardVolumeSales();

            // Assert
            _dbExecutor.Received(1).Query<CardVolumeSales>("TakeCardVolumeSales", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            foreach (CardVolumeSales item in result)
            {
                item.CardRef.Should().Be(cardRef);
                item.Grade.Should().Be(grade);
                item.Volume.Should().Be(volume);
                item.ProductName.Should().Be(productName);
            }
        }

        [Fact]
        public void test_fetch_card_references()
        {
            // Arrange
            const int cardRef = 5;
            const string productName = "XXXX";
            const string acquirerName = "ZZZZZ";
            const string externalName = "YYYY";
            _dbExecutor.Query<CardReference>("FetchCardReferences", commandType: CommandType.StoredProcedure)
                .Returns(new List<CardReference> {new CardReference(cardRef, productName, true, acquirerName, true, externalName) });

            // Act
            IList<CardReference> result = _hydraDb.FetchCardReferences();

            // Assert
            _dbExecutor.Received(1).Query<CardReference>("FetchCardReferences", commandType: CommandType.StoredProcedure);
            result.Should().HaveCount(1);
            foreach (CardReference item in result)
            {
                item.CardRef.Should().Be(cardRef);
                item.CardProductName.Should().Be(productName);
                item.FuelCard.Should().BeTrue();
                item.AcquirerName.Should().Be(acquirerName);
                item.InUse.Should().BeTrue();
                item.CardExternalName.Should().Be(externalName);
            }
        }

        [Fact]
        public void test_set_card_reference()
        {
            // Arrange
            const int cardRef = 5;
            const string name = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetCardReference", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetCardReference(name, cardRef);

            // Assert
            _dbExecutor.Received(1).Execute("SetCardReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {name, reference = cardRef});
            }
        }

        [Fact]
        public void test_clear_card_reference()
        {
            // Arrange
            const string name = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("ClearCardReference", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.ClearCardReference(name);

            // Assert
            _dbExecutor.Received(1).Execute("ClearCardReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {name});
            }
        }

        [Fact]
        public void test_set_acquirer_reference()
        {
            // Arrange
            const string cardName = "XXXX";
            const string acquirerName = "ZZZZZ";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetAcquirerReference", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetAcquirerReference(cardName, acquirerName);

            // Assert
            _dbExecutor.Received(1).Execute("SetAcquirerReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {cardName, acquirerName});
            }
        }

        [Fact]
        public void test_clear_acquirer_reference()
        {
            // Arrange
            const string name = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("ClearAcquirerReference", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.ClearAcquirerReference(name);

            // Assert
            _dbExecutor.Received(1).Execute("ClearAcquirerReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {cardName = name});
            }
        }

        [Fact]
        public void test_set_fuel_card()
        {
            // Arrange
            const string name = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetCardReferenceFuelCard", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetFuelCard(name, true);

            // Assert
            _dbExecutor.Received(1).Execute("SetCardReferenceFuelCard", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {cardName = name, isFuelCard = true});
            }
        }

        [Fact]
        public void test_set_external_name()
        {
            // Arrange
            const string name = "XXXX";
            const string external = "YYYY";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetExternalName", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.SetExternalName(name, external);

            // Assert
            _dbExecutor.Received(1).Execute("SetExternalName", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new { cardName = name, externalCardName = external });
            }
        }

        [Fact]
        public void test_clear_external_name()
        {
            // Arrange
            const string name = "XXXX";
            List<object> objects = new List<object>();
            _dbExecutor.Execute("ClearExternalName", Arg.Do<object>(x => objects.Add(x)), null, null, CommandType.StoredProcedure);

            // Act
            _hydraDb.ClearExternalName(name);

            // Assert
            _dbExecutor.Received(1).Execute("ClearExternalName", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new { name });
            }
        }

        [Fact]
        public void test_fetch_fuel_transactions()
        {
            // Arrange
            const long transactionId = 123;
            const string gradeCode = "33";
            const string washCode = "44";
            const string gradeName = "Unleaded";
            const string washName = "A Wash";
            const string pumpDetails = "A Pump";
            const string cardNumber = "12345";
            const uint fuelQuantity = 3000;
            const int washQuantity = 1;
            const long amount = 5000;
            const string fuelCategory = "97";
            const string washCategory = "96";
            const string fuelSubcategory = "5";
            const string washSubcategory = "4";
            const string discountName = "A Discount";
            const string discountCode = "AAA";
            const long discountValue = 100;
            const string discountCardNumber = "12345";
            const long localAccountMileage = 12345;
            const string localAccountRegistration = "ABCD";
            const string txnNumber = "765";
            DateTime endTime = DateTime.Now;
            DateTime startTime = endTime.AddDays(-1);
            DateTime transactionTime = endTime.AddHours(-1);

            List<object> objects = new List<object>();
            _dbExecutor.Query<FuelTransaction>("GetFuelTransactions", Arg.Do<object>(x => objects.Add(x)),
                commandType: CommandType.StoredProcedure).Returns(new List<FuelTransaction>
            {
                new FuelTransaction(transactionId, transactionTime, gradeCode, washCode, gradeName, washName, pumpDetails, cardNumber,
                    fuelQuantity, washQuantity, amount, fuelCategory, washCategory, fuelSubcategory, washSubcategory, discountName,
                    discountCode, discountValue, discountCardNumber, localAccountMileage, localAccountRegistration, txnNumber)
            });

            // Act
            IList<FuelTransaction> result = _hydraDb.FetchFuelTransactions(startTime, endTime, 10000).ToList();

            // Assert
            _dbExecutor.Received(1)
                .Query<FuelTransaction>("GetFuelTransactions", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    startTime,
                    endTime
                });
            }

            result.Should().HaveCount(1);
            foreach (FuelTransaction item in result)
            {
                item.TransactionId.Should().Be(transactionId);
                item.TransactionTime.Should().Be(transactionTime);
                item.GradeCode.Should().Be(gradeCode);
                item.WashCode.Should().Be(washCode);
                item.GradeName.Should().Be(gradeName);
                item.WashName.Should().Be(washName);
                item.PumpDetails.Should().Be(pumpDetails);
                item.CardNumber.Should().Be(cardNumber);
                item.FuelQuantity.Should().Be(fuelQuantity);
                item.WashQuantity.Should().Be(washQuantity);
                item.Amount.Should().Be((uint) amount);
                item.FuelCategory.Should().Be(fuelCategory);
                item.WashCategory.Should().Be(washCategory);
                item.FuelSubcategory.Should().Be(fuelSubcategory);
                item.WashSubcategory.Should().Be(washSubcategory);
                item.DiscountName.Should().Be(discountName);
                item.DiscountCode.Should().Be(discountCode);
                item.DiscountValue.Should().Be((uint) discountValue);
                item.DiscountCardNumber.Should().Be(discountCardNumber);
                item.LocalAccountMileage.Should().Be((uint) localAccountMileage);
                item.LocalAccountRegistration.Should().Be(localAccountRegistration);
                item.TxnNumber.Should().Be(txnNumber);
            }
        }

        [Fact]
        public void test_fetch_other_events()
        {
            // Arrange
            const long transactionId = 123;
            DateTime endTime = DateTime.Now;
            DateTime startTime = endTime.AddDays(-1);
            DateTime transactionTime = endTime.AddHours(-1);

            List<object> objects = new List<object>();
            _dbExecutor.Query<OtherEvent>("GetOtherEvents", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure)
                .Returns(new List<OtherEvent>
                {
                    new OtherEvent(transactionId, transactionTime)
                });

            // Act
            IList<OtherEvent> result = _hydraDb.FetchOtherEvents(startTime, endTime).ToList();

            // Assert
            _dbExecutor.Received(1).Query<OtherEvent>("GetOtherEvents", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    startTime,
                    endTime
                });
            }

            result.Should().HaveCount(1);
            foreach (OtherEvent item in result)
            {
                item.TransactionId.Should().Be(transactionId);
                item.TransactionTime.Should().Be(transactionTime);
            }
        }

        [Fact]
        public void test_fetch_shift_start()
        {
            // Arrange
            DateTime startTime = DateTime.Now;

            _dbExecutor.Query<DateTime>("GetShiftStart", commandType: CommandType.StoredProcedure).Returns(new List<DateTime> {startTime});

            // Act
            DateTime result = _hydraDb.FetchShiftStart(false);

            // Assert
            _dbExecutor.Received(1).Query<DateTime>("GetShiftStart", commandType: CommandType.StoredProcedure);
            _dbExecutor.DidNotReceive().Query<DateTime>("GetDayStart", commandType: CommandType.StoredProcedure);

            result.Should().Be(startTime);
        }

        [Fact]
        public void test_fetch_day_start()
        {
            // Arrange
            DateTime startTime = DateTime.Now;

            _dbExecutor.Query<DateTime>("GetDayStart", commandType: CommandType.StoredProcedure).Returns(new List<DateTime> {startTime});

            // Act
            DateTime result = _hydraDb.FetchShiftStart(true);

            // Assert
            _dbExecutor.DidNotReceive().Query<DateTime>("GetShiftStart", commandType: CommandType.StoredProcedure);
            _dbExecutor.Received(1).Query<DateTime>("GetDayStart", commandType: CommandType.StoredProcedure);

            result.Should().Be(startTime);
        }

        [Fact]
        public void test_set_till_number()
        {
            // Arrange
            const short tillNumber = 97;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetTillNumber", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetTillNumber(tillNumber);

            // Assert
            _dbExecutor.Received(1).Execute("SetTillNumber", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {number = tillNumber});
            }
        }

        [Fact]
        public void test_set_fuel_category()
        {
            // Arrange
            const short fuelCategory = 96;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetFuelCategory", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetFuelCategory(fuelCategory);

            // Assert
            _dbExecutor.Received(1).Execute("SetFuelCategory", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {category = fuelCategory});
            }
        }

        [Fact]
        public void test_set_max_fill_override()
        {
            // Arrange
            const int maxFillOverride = 100;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetMaxFillOverride", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetMaxFillOverride(maxFillOverride);

            // Assert
            _dbExecutor.Received(1).Execute("SetMaxFillOverride", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {maxFillOverride});
            }
        }

        [Fact]
        public void test_next_day_end()
        {
            // Arrange
            DateTime? dayEnd = DateTime.Now;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetNextDayEnd", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetNextDayEnd(dayEnd);

            // Assert
            _dbExecutor.Received(1).Execute("SetNextDayEnd", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {dayEnd});
            }
        }

        [Fact]
        public void test_set_log_interval()
        {
            // Arrange
            const int interval = 100;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetLogInterval", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetLogInterval(interval);

            // Assert
            _dbExecutor.Received(1).Execute("SetLogInterval", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {interval});
            }
        }

        [Fact]
        public void test_add_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddLoyaltyReference", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddGenericLoyalty(loyaltyName);

            // Assert
            _dbExecutor.Received(1).Execute("AddLoyaltyReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName});
            }
        }

        [Fact]
        public void test_delete_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            List<object> objects = new List<object>();
            _dbExecutor.Execute("DeleteLoyaltyReference", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.DeleteGenericLoyalty(loyaltyName);

            // Assert
            _dbExecutor.Received(1).Execute("DeleteLoyaltyReference", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName});
            }
        }

        [Fact]
        public void test_set_generic_loyalty_present()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";
            const bool present = true;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("SetLoyaltyPresent", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetGenericLoyaltyPresent(loyaltyName, present);

            // Assert
            _dbExecutor.Received(1).Execute("SetLoyaltyPresent", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName, present});
            }
        }

        [Fact]
        public void test_is_generic_loyalty_available()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            var objects = new List<object>();
            _dbExecutor.Query<LoyaltyTerminal>("GetLoyaltyTerminal", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure)
                .Returns(new List<LoyaltyTerminal> {new LoyaltyTerminal("Site", "1", string.Empty, string.Empty, 30, string.Empty, string.Empty)});

            var hydraDb = CreateDefaultInstance();

            // Act
            var result = hydraDb.DoIsGenericLoyaltyAvailable(loyaltyName);

            // Assert
            _dbExecutor.Received(1).Query<LoyaltyTerminal>("GetLoyaltyTerminal", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            result.Should().BeTrue();
            objects.Should().HaveCount(1);
            foreach (var item in objects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName});
            }
        }

        [Fact]
        public void test_fetch_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "Loyalty Name";
            LoyaltyTerminal loyaltyTerminal =
                new LoyaltyTerminal("Site ID", "Terminal ID", "Footer 1", "Footer 2", 1000, "Api Key", "HTTP Header");
            GenericEndPoint host1 = new GenericEndPoint("***************", 100);
            GenericEndPoint host2 = new GenericEndPoint("***************", 200);
            LoyaltyIin iin1 = new LoyaltyIin("Low 1", "High 1");
            LoyaltyIin iin2 = new LoyaltyIin("Low 2", "High 2");
            LoyaltyMapping mapping1 = new LoyaltyMapping("Product 1", "Loyalty 1");
            LoyaltyMapping mapping2 = new LoyaltyMapping("Product 2", "Loyalty 2");
            List<object> nameObjects = new List<object>();
            _dbExecutor.Query<bool>("GetLoyaltyPresent", Arg.Do<object>(x => nameObjects.Add(x)), commandType: CommandType.StoredProcedure)
                .Returns(new List<bool> {true});
            _dbExecutor.Query<LoyaltyTerminal>("GetLoyaltyTerminal", Arg.Do<object>(x => nameObjects.Add(x)),
                commandType: CommandType.StoredProcedure).Returns(new List<LoyaltyTerminal> {loyaltyTerminal});
            _dbExecutor.Query<GenericEndPoint>("GetLoyaltyHosts", Arg.Do<object>(x => nameObjects.Add(x)),
                commandType: CommandType.StoredProcedure).Returns(new List<GenericEndPoint> {host1, host2});
            _dbExecutor.Query<LoyaltyIin>("GetLoyaltyIins", Arg.Do<object>(x => nameObjects.Add(x)),
                commandType: CommandType.StoredProcedure).Returns(new List<LoyaltyIin> {iin1, iin2});
            _dbExecutor.Query<LoyaltyMapping>("GetLoyaltyMappings", Arg.Do<object>(x => nameObjects.Add(x)),
                commandType: CommandType.StoredProcedure).Returns(new List<LoyaltyMapping> {mapping1, mapping2});

            // Act
            GenericLoyalty result = _hydraDb.FetchGenericLoyalty(loyaltyName);

            // Assert
            nameObjects.Should().HaveCount(5);
            foreach (object item in nameObjects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName});
            }

            result.Terminal.SiteId.Should().Be(loyaltyTerminal.SiteId);
            result.Terminal.TerminalId.Should().Be(loyaltyTerminal.TerminalId);
            result.Terminal.Footer1.Should().Be(loyaltyTerminal.Footer1);
            result.Terminal.Footer2.Should().Be(loyaltyTerminal.Footer2);
            result.Hosts.Should().HaveCount(2);
            foreach (GenericEndPoint item in result.Hosts)
            {
                item.IpAddress.Should().BeOneOf(host1.IpAddress, host2.IpAddress);
                item.Port.Should().BeOneOf(host1.Port, host2.Port);
            }

            result.Iins.Should().HaveCount(2);
            foreach (LoyaltyIin item in result.Iins)
            {
                item.Low.Should().BeOneOf(iin1.Low, iin2.Low);
                item.High.Should().BeOneOf(iin1.High, iin2.High);
            }

            result.TariffMappings.Should().HaveCount(2);
            foreach (LoyaltyMapping item in result.TariffMappings)
            {
                item.ProductCode.Should().BeOneOf(mapping1.ProductCode, mapping2.ProductCode);
                item.LoyaltyCode.Should().BeOneOf(mapping1.LoyaltyCode, mapping2.LoyaltyCode);
            }
        }

        [Fact]
        public void test_set_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "Loyalty Name";
            LoyaltyTerminal loyaltyTerminal =
                new LoyaltyTerminal("Site ID", "Terminal ID", "Footer 1", "Footer 2", 1000, "API Key", "HTTP Header");
            GenericEndPoint host1 = new GenericEndPoint("***************", 100);
            GenericEndPoint host2 = new GenericEndPoint("***************", 200);
            const string hostname1 = "Host 1";
            const string hostname2 = "Host 2";
            LoyaltyIin iin1 = new LoyaltyIin("Low 1", "High 1");
            LoyaltyIin iin2 = new LoyaltyIin("Low 2", "High 2");
            LoyaltyMapping mapping1 = new LoyaltyMapping("Product 1", "Loyalty 1");
            LoyaltyMapping mapping2 = new LoyaltyMapping("Product 2", "Loyalty 2");
            GenericLoyalty loyalty = new GenericLoyalty(loyaltyTerminal, new List<GenericEndPoint> {host1, host2},
                new List<string> {hostname1, hostname2}, new List<LoyaltyIin> {iin1, iin2}, new List<LoyaltyMapping> {mapping1, mapping2}, true);
            List<object> clearObjects = new List<object>();
            List<object> terminalObjects = new List<object>();
            List<object> hostObjects = new List<object>();
            List<object> iinObjects = new List<object>();
            List<object> mappingObjects = new List<object>();
            _dbExecutor.Execute("ClearLoyalty", Arg.Do<object>(x => clearObjects.Add(x)), commandType: CommandType.StoredProcedure);
            _dbExecutor.Execute("SetLoyaltyTerminal", Arg.Do<object>(x => terminalObjects.Add(x)),
                commandType: CommandType.StoredProcedure);
            _dbExecutor.Execute("AddLoyaltyHost", Arg.Do<object>(x => hostObjects.Add(x)), commandType: CommandType.StoredProcedure);
            _dbExecutor.Execute("AddLoyaltyIIN", Arg.Do<object>(x => iinObjects.Add(x)), commandType: CommandType.StoredProcedure);
            _dbExecutor.Execute("AddLoyaltyMapping", Arg.Do<object>(x => mappingObjects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetGenericLoyalty(loyaltyName, loyalty);

            // Assert
            clearObjects.Should().HaveCount(1);
            foreach (object item in clearObjects)
            {
                item.Should().BeEquivalentTo(new {name = loyaltyName});
            }

            terminalObjects.Should().HaveCount(1);
            foreach (object item in terminalObjects)
            {
                item.Should().BeEquivalentTo(new
                {
                    name = loyaltyName,
                    siteId = loyaltyTerminal.SiteId,
                    terminalId = loyaltyTerminal.TerminalId,
                    footer1 = loyaltyTerminal.Footer1,
                    footer2 = loyaltyTerminal.Footer2, timeout = loyaltyTerminal.Timeout, apiKey = loyaltyTerminal.ApiKey,
                    httpHeader = loyaltyTerminal.HttpHeader
                });
            }

            hostObjects.Should().HaveCount(2);
            hostObjects[0].Should().BeEquivalentTo(new {name = loyaltyName, ipAddress = host1.IpAddress, port = host1.Port});
            hostObjects[1].Should().BeEquivalentTo(new {name = loyaltyName, ipAddress = host2.IpAddress, port = host2.Port});
            iinObjects.Should().HaveCount(2);
            iinObjects[0].Should().BeEquivalentTo(new {name = loyaltyName, low = iin1.Low, high = iin1.High});
            iinObjects[1].Should().BeEquivalentTo(new {name = loyaltyName, low = iin2.Low, high = iin2.High});
            mappingObjects.Should().HaveCount(2);
            mappingObjects[0].Should().BeEquivalentTo(new
            {
                name = loyaltyName,
                productCode = mapping1.ProductCode,
                loyaltyCode = mapping1.LoyaltyCode
            });
            mappingObjects[1].Should().BeEquivalentTo(new
            {
                name = loyaltyName,
                productCode = mapping2.ProductCode,
                loyaltyCode = mapping2.LoyaltyCode
            });
        }

        [Fact]
        public void test_fetch_washes()
        {
            // Arrange
            const int programId1 = 1;
            const int programId2 = 2;
            const string productCode1 = "Product Code 1";
            const string productCode2 = "Product Code 2";
            const string description1 = "Description 1";
            const string description2 = "Description 2";
            const string price1 = "Price 1";
            const string price2 = "Price 2";
            const string vatRate1 = "VAT Rate 1";
            const string vatRate2 = "VAT Rate 2";
            const int category1 = 11;
            const int category2 = 22;
            const int subcategory1 = 111;
            const int subcategory2 = 222;
            Wash wash1 = new Wash(programId1, productCode1, description1, price1, vatRate1, category1, subcategory1);
            Wash wash2 = new Wash(programId2, productCode2, description2, price2, vatRate2, category2, subcategory2);
            _dbExecutor.Query<Wash>("GetWashes", commandType: CommandType.StoredProcedure).Returns(new List<Wash> {wash1, wash2});

            // Act
            IList<Wash> result = _hydraDb.FetchWashes();

            // Assert
            result.Should().HaveCount(2);
            foreach (Wash item in result)
            {
                item.ProgramId.Should().BeOneOf(programId1, programId2);
                if (item.ProgramId == programId1)
                {
                    item.ProductCode.Should().Be(productCode1);
                    item.Description.Should().Be(description1);
                    item.Price.Should().Be(price1);
                    item.VatRate.Should().Be(vatRate1);
                    item.Category.Should().Be(category1);
                    item.Subcategory.Should().Be(subcategory1);
                }
                else if (item.ProgramId == programId2)
                {
                    item.ProductCode.Should().Be(productCode2);
                    item.Description.Should().Be(description2);
                    item.Price.Should().Be(price2);
                    item.VatRate.Should().Be(vatRate2);
                    item.Category.Should().Be(category2);
                    item.Subcategory.Should().Be(subcategory2);
                }
            }
        }

        [Fact]
        public void test_add_wash()
        {
            // Arrange
            const int programId = 1;
            const string productCode = "Product Code 1";
            const string description = "Description 1";
            const string price = "Price 1";
            const string vatRate = "VAT Rate 1";
            const int category = 11;
            const int subcategory = 111;
            Wash wash = new Wash(programId, productCode, description, price, vatRate, category, subcategory);

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddWash", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddWash(wash);

            // Assert
            _dbExecutor.Received(1).Execute("AddWash", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {programId, productCode, description, price, vatRate, category, subcategory});
            }
        }

        [Fact]
        public void test_remove_wash_by_program_id()
        {
            // Arrange
            const int programId = 1;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("RemoveWash", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.RemoveWashByProgramId(programId);

            // Assert
            _dbExecutor.Received(1).Execute("RemoveWash", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {programId});
            }
        }

        [Fact]
        public void test_fetch_tariff_mappings()
        {
            // Arrange
            const int grade1 = 1;
            const int grade2 = 2;
            const string productCode1 = "Product Code 1";
            const string productCode2 = "Product Code 2";
            TariffMapping tariffMapping1 = new TariffMapping(grade1, productCode1, false);
            TariffMapping tariffMapping2 = new TariffMapping(grade2, productCode2, false);
            _dbExecutor.Query<TariffMapping>("GetTariffMappings", commandType: CommandType.StoredProcedure)
                .Returns(new List<TariffMapping> {tariffMapping1, tariffMapping2});

            // Act
            IList<TariffMapping> result = _hydraDb.FetchTariffMappings();

            // Assert
            result.Should().HaveCount(2);
            foreach (TariffMapping item in result)
            {
                item.Grade.Should().BeOneOf(grade1, grade2);
                if (item.Grade == grade1)
                {
                    item.ProductCode.Should().Be(productCode1);
                }
                else if (item.Grade == grade1)
                {
                    item.ProductCode.Should().Be(productCode2);
                }
            }
        }

        [Fact]
        public void test_set_tariff_mappings()
        {
            // Arrange
            const int grade1 = 1;
            const int grade2 = 2;
            const string productCode1 = "Product Code 1";
            const string productCode2 = "Product Code 2";
            TariffMapping tariffMapping1 = new TariffMapping(grade1, productCode1, false);
            TariffMapping tariffMapping2 = new TariffMapping(grade2, productCode2, false);
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddTariffMapping", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetTariffMappings(new List<TariffMapping> {tariffMapping1, tariffMapping2});

            // Assert
            _dbExecutor.Received(1).Execute("ClearTariffMappings", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            _dbExecutor.Received(2).Execute("AddTariffMapping", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(2);
            objects[0].Should().BeEquivalentTo(new {grade = grade1, productCode = productCode1});
            objects[1].Should().BeEquivalentTo(new {grade = grade2, productCode = productCode2});
        }

        [Fact]
        public void test_fetch_predefined_amounts()
        {
            // Arrange
            const int amount1 = 1;
            const int amount2 = 2;
            _dbExecutor.Query<int>("GetPredefinedAmounts", commandType: CommandType.StoredProcedure)
                .Returns(new List<int> {amount1, amount2});

            // Act
            IList<int> result = _hydraDb.FetchPredefinedAmounts();

            // Assert
            result.Should().HaveCount(2);
            foreach (int item in result)
            {
                item.Should().BeOneOf(amount1, amount2);
            }
        }

        [Fact]
        public void test_set_predefined_amounts()
        {
            // Arrange
            const int amount1 = 1;
            const int amount2 = 2;
            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddPredefinedAmount", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.SetPredefinedAmounts(new List<int> {amount1, amount2});

            // Assert
            _dbExecutor.Received(1).Execute("ClearPredefinedAmounts", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            _dbExecutor.Received(2).Execute("AddPredefinedAmount", Arg.Any<object>(), commandType: CommandType.StoredProcedure);
            objects.Should().HaveCount(2);
            objects[0].Should().BeEquivalentTo(new {amount = amount1});
            objects[1].Should().BeEquivalentTo(new {amount = amount2});
        }

        [Fact]
        public void test_fetch_discount_cards()
        {
            // Arrange
            const string iin1 = "IIN 1";
            const string iin2 = "IIN 2";
            const string name1 = "Name 1";
            const string name2 = "Name 2";
            const string type1 = "Type 1";
            const string type2 = "Type 2";
            const float value1 = 11.1F;
            const float value2 = 22.2F;
            const int grade1 = 1;
            const int grade2 = 2;
            const string pan1 = "PAN 1";
            const string pan2 = "PAN 2";
            const string pan3 = "PAN 3";
            const string pan4 = "PAN 4";
            DiscountCard card1 = new DiscountCard(iin1, name1, type1, value1, grade1);
            DiscountCard card2 = new DiscountCard(iin2, name2, type2, value2, grade2);
            _dbExecutor.Query<DiscountCard>("GetDiscountCards", commandType: CommandType.StoredProcedure)
                .Returns(new List<DiscountCard> {card1, card2});
            List<object> objects = new List<object>();
            _dbExecutor.Query<string>("GetDiscountWhitelist", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure)
                .Returns(new List<string> {pan1, pan2}, new List<string> {pan3, pan4});

            // Act
            IList<DiscountCard> result = _hydraDb.FetchDiscountCards();

            // Assert
            objects.Should().HaveCount(2);
            objects[0].Should().BeEquivalentTo(new {iin = iin1});
            objects[1].Should().BeEquivalentTo(new {iin = iin2});
            result.Should().HaveCount(2);
            foreach (DiscountCard item in result)
            {
                item.Iin.Should().BeOneOf(iin1, iin2);
                if (item.Iin.Equals(iin1))
                {
                    item.Name.Should().Be(name1);
                    item.Type.Should().Be(type1);
                    item.Value.Should().Be(value1);
                    item.Grade.Should().Be(grade1);
                    item.Whitelist.Should().HaveCount(2);
                    foreach (string pan in item.Whitelist)
                    {
                        pan.Should().BeOneOf(pan1, pan2);
                    }
                }
                else if (item.Iin.Equals(iin2))
                {
                    item.Name.Should().Be(name2);
                    item.Type.Should().Be(type2);
                    item.Value.Should().Be(value2);
                    item.Grade.Should().Be(grade2);
                    item.Whitelist.Should().HaveCount(2);
                    foreach (string pan in item.Whitelist)
                    {
                        pan.Should().BeOneOf(pan3, pan4);
                    }
                }
            }
        }

        [Fact]
        public void test_add_discount_card()
        {
            // Arrange
            const string iin = "IIN 1";
            const string name = "Name 1";
            const string type = "Type 1";
            const float value = 11.1F;
            const int grade = 1;

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddDiscountCard", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddDiscountCard(iin, name, type, value, grade);

            // Assert
            _dbExecutor.Received(1).Execute("AddDiscountCard", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {iin, name, type, value, grade});
            }
        }

        [Fact]
        public void test_remove_discount_card()
        {
            // Arrange
            const string iin = "IIN 1";

            List<object> objects = new List<object>();
            _dbExecutor.Execute("RemoveDiscountCard", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.RemoveDiscountCard(iin);

            // Assert
            _dbExecutor.Received(1).Execute("RemoveDiscountCard", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {iin});
            }
        }

        [Fact]
        public void test_add_discount_whitelist()
        {
            // Arrange
            const string iin = "IIN 1";
            const string pan = "PAN 1";

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddDiscountWhitelist", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddDiscountWhitelist(iin, pan);

            // Assert
            _dbExecutor.Received(1).Execute("AddDiscountWhitelist", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {iin, pan});
            }
        }

        [Fact]
        public void test_remove_discount_whitelist()
        {
            // Arrange
            const string iin = "IIN 1";
            const string pan = "PAN 1";

            List<object> objects = new List<object>();
            _dbExecutor.Execute("RemoveDiscountWhitelist", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.RemoveDiscountWhitelist(iin, pan);

            // Assert
            _dbExecutor.Received(1).Execute("RemoveDiscountWhitelist", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {iin, pan});
            }
        }

        [Fact]
        public void test_fetch_local_account_customers()
        {
            // Arrange
            const string reference1 = "Reference 1";
            const string reference2 = "Reference 2";
            const string name1 = "Name 1";
            const string name2 = "Name 2";
            const uint transLimit1 = 1000;
            const uint transLimit2 = 2000;
            const uint balance1 = 1010;
            const uint balance2 = 2020;
            const string pan1 = "PAN 1";
            const string pan2 = "PAN 2";
            const string pan3 = "PAN 3";
            const string pan4 = "PAN 4";
            const string description1 = "Description 1";
            const string description2 = "Description 2";
            const string description3 = "Description 3";
            const string description4 = "Description 4";
            const double discount1 = 10.5;
            const double discount2 = 20.5;
            const double discount3 = 30.5;
            const double discount4 = 40.5;
            LocalAccountCard card1 = new LocalAccountCard(pan1, description1, discount1, true, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false);
            LocalAccountCard card2 = new LocalAccountCard(pan2, description2, discount2, true, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, true);
            LocalAccountCard card3 = new LocalAccountCard(pan3, description3, discount3, false, true, false, true, false, false, false,
                false, false, false, false, false, false, false, true, false);
            LocalAccountCard card4 = new LocalAccountCard(pan4, description4, discount4, false, false, true, false, false, false, false,
                false, false, false, false, false, false, false, false, true);
            LocalAccountCustomer customer1 = new LocalAccountCustomer(reference1, name1, true, transLimit1, true, true, true, true, true,
                true, true, false, false, balance1, true);
            LocalAccountCustomer customer2 = new LocalAccountCustomer(reference2, name2, false, transLimit2, false, true, true, true, true,
                true, false, true, false, balance2, true);
            _dbExecutor.Query<LocalAccountCustomer>("GetLocalAccountCustomers", commandType: CommandType.StoredProcedure)
                .Returns(new List<LocalAccountCustomer> {customer1, customer2});
            List<object> objects = new List<object>();
            _dbExecutor
                .Query<LocalAccountCard>("GetLocalAccountCards", Arg.Do<object>(x => objects.Add(x)),
                    commandType: CommandType.StoredProcedure).Returns(new List<LocalAccountCard> {card1, card2},
                    new List<LocalAccountCard> {card3, card4});

            // Act
            IList<LocalAccountCustomer> result = _hydraDb.FetchLocalAccountCustomers();

            // Assert
            objects.Should().HaveCount(2);
            objects[0].Should().BeEquivalentTo(new {reference = reference1});
            objects[1].Should().BeEquivalentTo(new {reference = reference2});
            result.Should().HaveCount(2);
            foreach (LocalAccountCustomer item in result)
            {
                item.CustomerReference.Should().BeOneOf(customer1.CustomerReference, customer2.CustomerReference);
                if (item.CustomerReference.Equals(customer1.CustomerReference))
                {
                    item.Should().BeEquivalentTo(customer1);
                }
                else if (item.CustomerReference.Equals(customer2.CustomerReference))
                {
                    item.Should().BeEquivalentTo(customer2);
                }
            }
        }

        [Fact]
        public void test_add_local_account_customer()
        {
            // Arrange
            const string reference = "Reference 1";
            const string name = "Name 1";
            const bool transactionsAllowed = true;
            const uint transLimit = 1000;
            const bool pin = true;
            const bool printValue = true;
            const bool allowLoyalty = false;
            const bool fuelOnly = true;
            const bool registrationEntry = false;
            const bool mileageEntry = false;
            const bool prepayAccount = true;
            const bool lowCreditWarning = false;
            const bool maxCreditReached = false;
            const uint balance = 1010;
            const bool customerExists = true;
            LocalAccountCustomer customer = new LocalAccountCustomer(reference, name, transactionsAllowed, transLimit, pin, printValue,
                allowLoyalty, fuelOnly, registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, balance,
                customerExists);

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddLocalAccountCustomer", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddLocalAccountCustomer(customer);

            // Assert
            _dbExecutor.Received(1).Execute("AddLocalAccountCustomer", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    reference,
                    name,
                    transactionsAllowed,
                    transLimit,
                    pin,
                    printValue,
                    allowLoyalty,
                    fuelOnly,
                    registrationEntry,
                    mileageEntry,
                    prepayAccount,
                    lowCreditWarning,
                    maxCreditReached,
                    balance,
                    customerExists
                });
            }
        }

        [Fact]
        public void test_delete_local_account_customer()
        {
            // Arrange
            const string reference = "Reference 1";
            const string name = "Name 1";
            const bool transactionsAllowed = true;
            const uint transLimit = 1000;
            const bool pin = true;
            const bool printValue = true;
            const bool allowLoyalty = false;
            const bool fuelOnly = true;
            const bool registrationEntry = false;
            const bool mileageEntry = false;
            const bool prepayAccount = true;
            const bool lowCreditWarning = false;
            const bool maxCreditReached = false;
            const uint balance = 1010;
            const bool customerExists = true;
            LocalAccountCustomer customer = new LocalAccountCustomer(reference, name, transactionsAllowed, transLimit, pin, printValue,
                allowLoyalty, fuelOnly, registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, balance,
                customerExists);

            List<object> objects = new List<object>();
            _dbExecutor.Execute("DeleteLocalAccountCustomer", Arg.Do<object>(x => objects.Add(x)),
                commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.DeleteLocalAccountCustomer(customer);

            // Assert
            _dbExecutor.Received(1).Execute("DeleteLocalAccountCustomer", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {reference});
            }
        }

        [Fact]
        public void test_add_local_account_card()
        {
            // Arrange
            const string customerReference = "Reference 1";
            const string name = "Name 1";
            const bool transactionsAllowed = true;
            const uint transLimit = 1000;
            const bool pin = true;
            const bool printValue = true;
            const bool allowLoyalty = false;
            const bool fuelOnly = true;
            const bool registrationEntry = false;
            const bool mileageEntry = false;
            const bool prepayAccount = true;
            const bool lowCreditWarning = false;
            const bool maxCreditReached = false;
            const uint balance = 1010;
            const bool customerExists = true;
            const string pan = "PAN 1";
            const string description = "Description 1";
            const double discount = 10.5;
            const bool noRestrictions = true;
            const bool unleaded = false;
            const bool diesel = false;
            const bool lpg = false;
            const bool lrp = false;
            const bool gasOil = false;
            const bool adBlue = false;
            const bool kerosene = false;
            const bool oil = false;
            const bool avgas = false;
            const bool jet = false;
            const bool mogas = false;
            const bool valeting = false;
            const bool otherMotorRelatedGoods = false;
            const bool shopGoods = false;
            const bool hot = false;
            LocalAccountCustomer customer = new LocalAccountCustomer(customerReference, name, transactionsAllowed, transLimit, pin,
                printValue, allowLoyalty, fuelOnly, registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached,
                balance, customerExists);
            LocalAccountCard card = new LocalAccountCard(pan, description, discount, noRestrictions, unleaded, diesel, lpg, lrp, gasOil,
                adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods, hot);

            List<object> objects = new List<object>();
            _dbExecutor.Execute("AddLocalAccountCard", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.AddLocalAccountCard(customer, card);

            // Assert
            _dbExecutor.Received(1).Execute("AddLocalAccountCard", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new
                {
                    pan,
                    customerReference,
                    description,
                    discount,
                    noRestrictions,
                    unleaded,
                    diesel,
                    lpg,
                    lrp,
                    gasOil,
                    adBlue,
                    kerosene,
                    oil,
                    avgas,
                    jet,
                    mogas,
                    valeting,
                    otherMotorRelatedGoods,
                    shopGoods,
                    hot
                });
            }
        }

        [Fact]
        public void test_delete_local_account_card()
        {
            // Arrange
            const string pan = "PAN 1";
            const string description = "Description 1";
            const double discount = 10.5;
            const bool noRestrictions = true;
            const bool unleaded = false;
            const bool diesel = false;
            const bool lpg = false;
            const bool lrp = false;
            const bool gasOil = false;
            const bool adBlue = false;
            const bool kerosene = false;
            const bool oil = false;
            const bool avgas = false;
            const bool jet = false;
            const bool mogas = false;
            const bool valeting = false;
            const bool otherMotorRelatedGoods = false;
            const bool shopGoods = false;
            const bool hot = false;
            LocalAccountCard card = new LocalAccountCard(pan, description, discount, noRestrictions, unleaded, diesel, lpg, lrp, gasOil,
                adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods, hot);

            List<object> objects = new List<object>();
            _dbExecutor.Execute("DeleteLocalAccountCard", Arg.Do<object>(x => objects.Add(x)), commandType: CommandType.StoredProcedure);

            // Act
            _hydraDb.DeleteLocalAccountCard(card);

            // Assert
            _dbExecutor.Received(1).Execute("DeleteLocalAccountCard", Arg.Any<object>(), commandType: CommandType.StoredProcedure);

            objects.Should().HaveCount(1);
            foreach (object item in objects)
            {
                item.Should().BeEquivalentTo(new {pan});
            }
        }

        #endregion

        #region Helper Methods

        private IHydraDb CreateDefaultInterface()
        {
            return CreateDefaultInstance();
        }

        private Forecourt.Common.HydraDbClasses.HydraDb CreateDefaultInstance()
        {
            return new Forecourt.Common.HydraDbClasses.HydraDb(_dbExecutorFactory,  _logger, _telemetryWorker, _configurationManager, _configurationRepository, _cacheHelper);
        }

        #endregion
    }
}
