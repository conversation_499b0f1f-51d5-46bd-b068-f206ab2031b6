USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('SetPumpDelivered') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE SetPumpDelivered
END
GO

CREATE PROCEDURE [dbo].[SetPumpDelivered]
@pump int,
 @grade int,
 @volume bigint,
 @amount bigint,
 @name varchar(20),
 @price int,
 @netAmount bigint,
 @vatAmount bigint,
 @vatRate float,
 @transSeqNum int,
 @hose int
AS
BEGIN
  BEGIN TRY
   BEGIN TRANSACTION
   IF (NOT EXISTS (SELECT * FROM PumpDelivered WHERE Number = @pump))
    BEGIN
     insert PumpDelivered (Number) VALUES (@pump)
    END
   UPDATE PumpDelivered SET
    Delivered = 1,
    Grade = @grade,
    Volume = @volume,
    Amount = @amount,
    Name = @name,
    Price = @price,
    NetAmount = @netAmount,
    VatAmount = @vatAMount,
    VatRate = @vatRate,
    TransSeqNum = @transSeqNum,
    Hose = @hose
    WHERE Number = @pump
   COMMIT TRANSACTION
  END TRY
  BEGIN CATCH
   ROLLBACK TRANSACTION;
   THROW
  END CATCH
 END
GO

GRANT EXECUTE ON SetPumpDelivered TO UserRole
GO
