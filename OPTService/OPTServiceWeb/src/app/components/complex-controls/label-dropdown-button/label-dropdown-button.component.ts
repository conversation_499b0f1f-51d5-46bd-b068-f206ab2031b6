import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, ControlContainer } from '@angular/forms';

export class KeyValuePair {
  key: string;
  value: any;
}

@Component({
  selector: 'app-label-dropdown-button',
  templateUrl: './label-dropdown-button.component.html',
  styleUrls: ['./label-dropdown-button.component.css']
})
export class LabelDropdownButtonComponent implements OnInit {

  public form: FormGroup;
  public control : FormControl;

  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() buttonText: string = 'Save';
  @Input() validationErrorText: string = 'Please provide a valid value';
  @Input() errorInActionText: string = 'Field couldn\'t be updated';
  @Input() appendText: string = '';
  @Input() controlName: string = '';  
  @Input() errorInAction: boolean = false;
  @Input() labelColClass: string = 'col-2';
  @Input() dropdownColClass: string = 'col-2';
  @Input() buttonColClass: string = 'col-auto';
  @Input('id') private _id: string;
  @Input() options: Array<KeyValuePair>;
  
  @Output() action = new EventEmitter();

  public get id(): string {
    return this._id || this.controlName || '';
  }

  public get fieldFeedbackId(): string {
    return `${this.id}FieldFeedback`;
  }

  public get buttonId(): string {
    return `${this.id}Btn`;
  }

  public get errorId(): string {
    return `${this.id}Error`;
  }

  constructor(
    private controlContainer: ControlContainer,
  ) { }

  ngOnInit(): void {
    console.log('siteTypeOptions', this.options);
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
  }

  doAction(event?: Event): void {
    this.action.emit(event);
  }
}
