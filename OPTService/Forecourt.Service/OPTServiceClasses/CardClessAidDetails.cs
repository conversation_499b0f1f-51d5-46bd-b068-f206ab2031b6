namespace OPTService.OPTServiceClasses
{
    public class CardClessAidDetails
    {
        public string Aid { get; set; }
        public string AppVerTerm { get; set; }
        public string TransLimit { get; set; }
        public string FloorLimit { get; set; }
        public string CvmLimit { get; set; }
        public string OdcvmLimit { get; set; }
        public string TermAddCapabilities { get; set; }
        public string TermCapabilitiesCvm { get; set; }
        public string TermCapabilitiesNoCvm { get; set; }
        public string TermRiskData { get; set; }
        public string Udol { get; set; }
        public string TacDefault { get; set; }
        public string TacDenial { get; set; }
        public string TacOnline { get; set; }
        public string TerminalType { get; set; }
        public string MerchantCategoryCode { get; set; }
        public string MaxTorn { get; set; }
        public string MaxTornLife { get; set; }
        public string MchipCvmCapAboveLimit { get; set; }
        public string MchipCvmCapBelowLimit { get; set; }
        public string MstripeCvmCapAboveLimit { get; set; }
        public string MstripeCvmCapBelowLimit { get; set; }
        public string MagStripeAppVer { get; set; }
        public string ExpressPayReaderCapabilities { get; set; }
        public string ExpressPayEnhancedReaderCapabilities { get; set; }
        public string ExpressPayKernelVersion { get; set; }
    }
}