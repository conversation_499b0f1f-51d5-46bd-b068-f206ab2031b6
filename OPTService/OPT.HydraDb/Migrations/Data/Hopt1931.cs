using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    [Migration(20030004, "HOPT-1931 - Enable pre-defined limits per MOP, config needed"), Tags(Constants.MigrationTypeSchema)]
    public class Hopt1931: Migrationable
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_install.sql");
        }

        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_rollback.sql");
        }

    }
}
