using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;

namespace Forecourt.Core.Workers
{
    /// <summary>
    /// Adds common <see cref="IHydraDb"/> instance to the standard <see cref="Htec.Foundation.Connections.Workers.ClientWorker{TMessage}"/>
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="THydraDb"></typeparam>
    /// <typeparam name="TTelemetryWorker"></typeparam>
    [HasConfiguration]
    public abstract class ClientWorker<T, THydraDb, TTelemetryWorker> : Htec.Foundation.Connections.Workers.ClientWorker<T> 
        where THydraDb : IHydraDb
        where TTelemetryWorker: ITelemetryWorker
    {
        /// <summary>
        /// Current <see cref="IHydraDb"/> instance
        /// </summary>
        protected THydraDb HydraDb { get; }

        /// <summary>
        /// Worker used to send notifications
        /// </summary>
        protected INotificationWorker<EventType> NotificationWorker => GetWorker<INotificationWorker<EventType>>();

        /// <inheritdoc/>
        protected ClientWorker(IHtecLogger logger, TTelemetryWorker telemetryWorker,
            IClientConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null,
            string defaultTimerInterval = null) : base(logger, connectionThread, telemetryWorker as ITelemetryWorker, configurationManager, timerFactory, defaultTimerInterval)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        /// <inheritdoc/>
        protected ClientWorker(IHtecLogManager logMan, string loggerName, TTelemetryWorker telemetryWorker,
            IClientConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null,
            string defaultTimerInterval = null) : base(logMan, loggerName, connectionThread, telemetryWorker as ITelemetryWorker, configurationManager, timerFactory, defaultTimerInterval)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }
    }
}
