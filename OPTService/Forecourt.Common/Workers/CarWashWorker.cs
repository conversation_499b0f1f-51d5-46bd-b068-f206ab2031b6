using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Timers;

namespace OPT.Common.Workers
{
    public class CarWashWorker : ClientWorker<string, IHydraDb, ITelemetryWorker>, ICarWashWorker
    {
        private struct QueueItem
        {
            public IOpt Opt;
            public string Machine;
            public byte ProgramId;
            public DateTime Timeout;
        }

        private const int NoTicketSize = 8;
        private const int TicketSize = 6;
        private const int TicketNumSize = 6;
        private const int VersionSize = 6;
        private const int PinSize = 3;
        private const int TicketAndNumSize = TicketSize + TicketNumSize;
        private const int TicketAndVersionSize = TicketAndNumSize + 1 + VersionSize;
        private const int TicketAndPinSize = TicketAndVersionSize + 1 + PinSize;

        private const int SecondsToWaitForCarWash = 4;
        private readonly Queue<QueueItem> _queue = new Queue<QueueItem>();

        private IOpt _currentOpt;
        private DateTime _currentTimeout;

        private IFromOptWorker _optWorker => GetWorker<IFromOptWorker>();
        private ITelemetryWorker _telemetryWorker => GetWorker<ITelemetryWorker>();
        private IControllerWorker _controllerWorker => GetWorker<IControllerWorker>();

        /// <inheritdoc/>
        public CarWashWorker(ITelemetryWorker telemetryWorker, IHydraDb hydraDb, IHtecLogger logger, IClientConnectionThread<string> connectionThread, IConfigurationManager configurationManager, ITimerFactory timerFactory) :
            base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, Connectable.DefaultValueInterval)
        {
        }

        /// <inheritdoc/>
        protected override Htec.Foundation.Connections.Models.GenericEndPoint DoGetEndPoint()
        {
            return HydraDb.FetchCarWashEndPoint();
        }

        /// <inheritdoc/>
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs args)
        {
            DateTime now = DateTime.UtcNow;
            if (_currentOpt != null && _currentTimeout < now)
            {
                // TODO: Sending a request on the FromOpt channel!?!
                _optWorker?.CarWashNoTicket(_currentOpt);
                _telemetryWorker?.MessageTimeoutFromCarWash();
                _currentOpt = null;

                while (_queue.Any() && _currentOpt == null)
                {
                    var item = _queue.Dequeue();
                    if (item.Timeout < now)
                    {
                        _optWorker?.CarWashNoTicket(item.Opt);
                    }
                    else
                    {
                        SendRequest(item.Opt, item.Machine, item.ProgramId, item.Timeout);
                    }
                }
            }
        }

        protected override void DoOnDisconnected(int? id = null)
        {
            if (_currentOpt != null)
            {
                _optWorker?.CarWashNoTicket(_currentOpt);
                _currentOpt = null;
                while (_queue.Any())
                {
                    var item = _queue.Dequeue();
                    _optWorker?.CarWashNoTicket(item.Opt);
                }
            }

            base.DoOnDisconnected(id);
        }

        #region Actions

        private void SendRequest(IOpt opt, string machine, byte prog, DateTime timeOut, [CallerMemberName]string methodName = null)
        {
            DoAction(() =>
            {
                if (_currentOpt == null)
                {
                    DoDeferredLogging(LogLevel.Info, $"{OptListenerWorker.HeaderOpt}", () => new[] { $"{opt.IdString} ({opt.Id}); Machine: {machine}; Prog: {prog}" }, methodName: methodName);
                    _currentOpt = opt;
                    SendRequest($"Valet={machine}/Normal/{prog}/", null, () => { _currentTimeout = timeOut; });
                    _telemetryWorker.MessageSentToCarWash();
                }
                else
                {
                    _queue.Enqueue(new QueueItem
                    {
                        Opt = opt,
                        Machine = machine,
                        ProgramId = prog,
                        Timeout = DateTime.UtcNow.AddSeconds(SecondsToWaitForCarWash)
                    });
                }
            }, LoggingReference);
        }

        public void SendRequestToCarWash(IOpt opt, string machine, byte prog)
        {
            DoAction(() =>
            {
                if (IsConnected() && opt != null && !string.IsNullOrEmpty(machine))
                {
                    SendRequest(opt, machine, prog, DateTime.UtcNow.AddSeconds(SecondsToWaitForCarWash));
                }
                else
                {
                    _optWorker?.CarWashNoTicket(opt);
                }
            }, null);
        }

        private IList<string> GetMessagesFromMessage(string message)
        {
            List<string> messages = new List<string>();
            DoAction(() =>
            {
                while (message.Contains("TICKET"))
                {
                    int ticketStart = message.IndexOf("TICKET");
                    int noTicketStart = message.IndexOf("NOTICKET");
                    int start = noTicketStart < 0 || ticketStart < noTicketStart ? ticketStart : noTicketStart;
                    if (start > 0)
                    {
                        DoDeferredLogging(LogLevel.Warn, $"DiscardingCharacters", () => new[] { message });
                        message = message.Substring(start);
                    }

                    if (message.StartsWith("NOTICKET"))
                    {
                        string thisMessage = message.Substring(0, NoTicketSize);
                        messages.Add(thisMessage);
                        message = message.Substring(NoTicketSize);
                    }
                    else if (message.StartsWith("TICKET") && message.Length >= TicketAndPinSize && message[TicketAndVersionSize] == '/')
                    {
                        string thisMessage = message.Substring(0, TicketAndPinSize);
                        messages.Add(thisMessage);
                        message = message.Substring(TicketAndPinSize);
                    }
                    else if (message.StartsWith("TICKET") && message.Length >= TicketAndVersionSize)
                    {
                        string thisMessage = message.Substring(0, TicketAndVersionSize);
                        messages.Add(thisMessage);
                        message = message.Substring(TicketAndVersionSize);
                    }
                    else
                    {
                        DoDeferredLogging(LogLevel.Warn, $"MessageTooShort", () => new[] { message });
                        message = string.Empty;
                    }
                }

                if (message.Length > 0)
                {
                    DoDeferredLogging(LogLevel.Warn, $"DiscardingCharacters", () => new[] { message });
                }

            }, LoggingReference);
            return messages;
        }

        private void ProcessMessage(string message)
        {
            DoAction(() =>
            {
                string ticketNum = null;
                string version = null;
                string pin = null;
                if (message.Length >= TicketAndPinSize)
                {
                    pin = message.Substring(TicketAndVersionSize + 1, PinSize);
                }

                if (message.Length >= TicketAndVersionSize)
                {
                    ticketNum = message.Substring(TicketSize, TicketNumSize);
                    version = message.Substring(TicketAndNumSize + 1, VersionSize);
                }

                DoDeferredLogging(LogLevel.Debug, "Command: ", () => new[] { message });

                _controllerWorker?.SendInformation(
                    $"Car Wash {(ticketNum == null ? "No Ticket" : $"Ticket {ticketNum.Trim()}, Version {version.Trim()}")}" +
                    $"{(pin == null ? string.Empty : $", PIN {pin}")}");
                if (_currentOpt != null)
                {
                    if (ticketNum == null)
                    {
                        _optWorker?.CarWashNoTicket(_currentOpt);
                    }
                    else
                    {
                        _optWorker?.CarWashTicket(_currentOpt, ticketNum, version, pin);
                    }

                    _telemetryWorker.MessageReceivedFromCarWash();
                    _currentOpt = null;

                    if (_queue.Any())
                    {
                        var item = _queue.Dequeue();
                        SendRequest(item.Opt, item.Machine, item.ProgramId, item.Timeout);
                    }
                }
            }, LoggingReference);
        }

        /// <inheritdoc/>
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message)
        {
           var messages = GetMessagesFromMessage(message.Request);

            if (messages.Any())
            {
                foreach (var thisMessage in messages)
                {
                    ProcessMessage(thisMessage);
                }
            }

            return null;
        }

        #endregion
    }
}