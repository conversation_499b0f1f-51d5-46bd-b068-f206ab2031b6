using FluentAssertions;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System;
using System.Linq;
using Xunit;
using JavaProperties = Htec.Hydra.Core.PaymentConfiguration.JavaProperties.JavaProperties;

namespace OPT.Common.Tests.Java
{
    public class JavaPropertiesTests
    {
        private readonly IHtecLogger _logger;

        public JavaPropertiesTests()
        {
            _logger = Substitute.For<IHtecLogger>();
        }

        #region Read

        [Fact]
        public void read_duplicate_config_key_does_not_throw_exception()
        {
            // Arrange
            var configString = "postilion.esocketpos.TraceNrFiles=10" +
                    "\r\npostilion.esocketpos.TraceNrFiles=20";

            // Act
            Action act = () => JavaProperties.Read(configString, _logger);

            // Assert
            act.Should().NotThrow();
        }

        [Fact]
        public void read_duplicate_config_key_loads_first_entry()
        {
            // Arrange
            var configString = "postilion.esocketpos.TraceNrFiles=10" +
                               "\r\npostilion.esocketpos.TraceNrFiles=20";

            // Act
            var javaProperties = JavaProperties.Read(configString, _logger);

            // Assert
            javaProperties.Count.Should().Be(1);
            javaProperties.First().Value.Should().Be("10");
        }


        #endregion
    }
}
