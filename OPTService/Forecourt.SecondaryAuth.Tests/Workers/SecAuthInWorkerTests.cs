using FluentAssertions;
using Forecourt.Core.Configuration;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using NSubstitute;
using System.Collections.Specialized;
using Xunit;

namespace Forecourt.SecondaryAuth.Tests.Workers
{
    public class SecAuthInWorkerTests
    {
        private readonly SecAuthInWorker _worker;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pump;
        private readonly IPumpIntegratorInTransient<IMessageTracking> _pumpOutWorker;
        private readonly IMessageTracking _messageTracking;
        private readonly PumpTransaction _pumpTransaction = new PumpTransaction(1);
        private readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOutWorker;

        public SecAuthInWorkerTests()
        {
            _logManager = Substitute.For<IHtecLogManager>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            var appSettings = new NameValueCollection() { [Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "Message:IsStrict:"] = "False" };
            _configurationManager.AppSettings.Returns(appSettings);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();

            _pump = Substitute.For<IPump>();
            _pump.SecAuthState.Returns(SecAuthState.Requested);
            _pump.HasSecAuthRequestTimedOut.Returns(false);
            _pump.IsSecAuthRequested.Returns(true);

            _allPumps = Substitute.For<IPumpCollection>();
            _allPumps.TryGetPump(Arg.Is<byte>(1), out var p).Returns(true);
            _allPumps.When(x => x.TryGetPump(Arg.Is<byte>(1), out var p1)).Do(c =>
            {
                c[1] = _pump;
            });
            _pumpOutWorker = Substitute.For<IPumpIntegratorInTransient<IMessageTracking>>();
            _messageTracking = new MessageTracking() { ParentIdAsString = _pumpTransaction.LoggingReference, IdAsString = "LOG-REF" };

            _secAuthOutWorker = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();

            _worker = new SecAuthInWorker(_logManager, _configurationManager, _allPumps, _pumpOutWorker, _telemetryWorker, _secAuthOutWorker);
        }

        private void do_check_response_is_processed_as_expected(bool timedOut = true, bool isAuthorised = true,
            bool isOptInUse = false, bool isNozzleUp = false, bool isDelivering = false, string messageIdResponse = null, byte setSecAuthResponseReceivedCount = 1,
            bool expectedResult = true, SecAuthState expectedState = SecAuthState.Success, byte pump = 1)
        {
            // Arrange
            _pump.SecAuthState.Returns(SecAuthState.Requested);
            _pump.HasSecAuthRequestTimedOut.Returns(timedOut);
            _pump.IsSecAuthRequested.Returns(!timedOut);
            _pump.When(x => x.SetSecAuthResponse(Arg.Is(isAuthorised), Arg.Any<string>())).Do(c => { _pump.SecAuthState.Returns(expectedState); });

            _pump.InUseByOpt.Returns(isOptInUse);
            _pump.IsNozzleUp.Returns(isNozzleUp);
            _pump.IsDelivering.Returns(isDelivering);
            _pump.TransactionSummary.Returns(_pumpTransaction);

            _messageTracking.ParentIdAsString = messageIdResponse ?? _pumpTransaction.LoggingReference;
            var response = new SecAuthResponse(_messageTracking.ParentIdAsString, null, pump, null, isAuthorised);

            var appSettings = new NameValueCollection() { 
                [Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "Message:IsStrict:"] = "False",
                [Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "TimedOutResponse:"] = $"{isAuthorised}",
            };
            _configurationManager.AppSettings.Returns(appSettings);

            // Act
            var result = _worker.PostAuthResponse(response, _messageTracking);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().Be(expectedResult);
            _pump.Received(setSecAuthResponseReceivedCount).SetSecAuthResponse(Arg.Any<bool>(), Arg.Any<string>());
            _pump.SecAuthState.Should().Be(expectedState);
        }

        #region Non-TimedOut scenarios        

        [Fact]
        public void check_approved_response_is_processed_as_expected()
        {
            // Arrange

            // Act/Assert
            do_check_response_is_processed_as_expected(timedOut: false);
        }

        [Fact]
        public void check_rejected_response_is_processed_as_expected()
        {
            // Arrange

            // Act/Assert
            do_check_response_is_processed_as_expected(timedOut: false, isAuthorised: false, expectedState: SecAuthState.Failure);
        }

        #endregion

        #region TimedOut scenarios

        [Fact]
        public void check_approved_response_after_timeout_is_processed_as_expected()
        {
            // Arrange

            // Act/Assert
            do_check_response_is_processed_as_expected(timedOut: true, expectedResult: false, expectedState: SecAuthState.Failure);
        }

        [Fact]
        public void check_rejected_response_after_timeout_is_processed_as_expected()
        {
            do_check_response_is_processed_as_expected(isAuthorised: false, expectedResult: true, expectedState: SecAuthState.Failure);
        }

        [Fact]
        public void check_approved_response_after_timeout_does_not_update_stop_if_opt_isinuse_only()
        {
            // Arrange

            // Act
            do_check_response_is_processed_as_expected(isAuthorised: true, isOptInUse: true, expectedResult: false, expectedState: SecAuthState.Requested);

            // Assert
            _pumpOutWorker.Received(0).EmergencyStopUpdate(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
            _pumpOutWorker.Received(0).EmergencyStop(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
        }

        [Fact]
        public void check_approved_response_after_timeout_does_update_stop_if_opt_isinuse_and_delivering()
        {
            // Arrange

            // Act
            do_check_response_is_processed_as_expected(isAuthorised: true, isOptInUse: true, isNozzleUp: true, isDelivering: true, expectedResult: false, expectedState: SecAuthState.Requested);

            // Assert
            _pumpOutWorker.Received(1).EmergencyStopUpdate(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
        }

        [Fact]
        public void check_rejected_response_after_timeout_does_not_update_stop_if_opt_isinuse_only()
        {
            // Arrange

            // Act
            do_check_response_is_processed_as_expected(isAuthorised: false, isOptInUse: true, expectedResult: true, expectedState: SecAuthState.Failure);

            // Assert
            _pumpOutWorker.Received(0).EmergencyStopUpdate(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
            _pumpOutWorker.Received(0).EmergencyStop(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
        }

        [Fact]
        public void check_rejected_response_after_timeout_does_update_stop_if_opt_isinuse_and_delivering()
        {
            // Arrange

            // Act
            do_check_response_is_processed_as_expected(isAuthorised: false, isOptInUse: true, isNozzleUp:true, isDelivering: true, expectedResult: true, expectedState: SecAuthState.Failure);

            // Assert
            _pumpOutWorker.Received(1).EmergencyStop(Arg.Any<byte>(), Arg.Any<string>(), Arg.Any<IMessageTracking>());
        }

        #endregion

        #region IsStrictMessaging

        [Fact]
        public void check_approved_response_is_processed_as_expected_with_aligned_messageid()
        {
            // Arrange
            var appSettings = new NameValueCollection() { [Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "Message:IsStrict:"] = "True" };
            _configurationManager.AppSettings.Returns(appSettings);

            // Act
            do_check_response_is_processed_as_expected(timedOut: false);
        }

        [Fact]
        public void check_approved_response_is_processed_as_expected_with_mis_aligned_messageid()
        {
            // Arrange
            var appSettings = new NameValueCollection() { [Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "Message:IsStrict:"] = "True" };
            _configurationManager.AppSettings.Returns(appSettings);

            // Act
            do_check_response_is_processed_as_expected(timedOut: false, messageIdResponse: "ANYTHING", expectedResult: true, expectedState: SecAuthState.Requested);
        }

        #endregion
    }
}
