import { HttpErrorResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { Opt } from 'src/app/core/models/opt.model';
import { OPT_SERVICE_PROVIDER, OPT_SERVICE_SPY } from 'src/app/services/opt.service.spy';
import { NGX_LOGGER_PROVIDER, NGX_LOGGER_SPY } from 'src/app/testing/ngxlogger.spy';

import { OptReceiptComponent } from './opt-receipt.component';

describe('OptReceiptComponent', () => {
  let component: OptReceiptComponent;
  let fixture: ComponentFixture<OptReceiptComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ OptReceiptComponent ],
      imports: [
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        NGX_LOGGER_PROVIDER(),
        OPT_SERVICE_PROVIDER(),
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OptReceiptComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('isGlobal', () => {

    it('should return true when OPT is undefined', () => {
      component['opt'] = undefined;

      expect(component.isGlobal).toBeTrue();
    });

    it('should return false when OPT is defined', () => {
      component['opt'] = {} as Opt;

      expect(component.isGlobal).toBeFalse();
    });
  });

  describe('id', () => {

    it('should return empty string when OPT is undefined', () => {
      component['opt'] = undefined;

      expect(component.id).toBe('');
    });

    it('should return StringId when OPT is defined', () => {
      component['opt'] = { StringId: 'TestOpt' } as Opt;

      expect(component.id).toBe('TestOpt');
    });
  });

  describe('.setOptReceiptHeader()', () => {
    
    it('.setOptReceiptHeader() should handle success response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setReceiptHeader.and.returnValue(of({}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setOptReceiptHeader(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setReceiptHeader).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
      expect(component.headerErrors[index]).toBeFalsy();
    });
  
    it('.setOptReceiptHeader() should handle unsuccess response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setReceiptHeader.and.returnValue(throwError({status: 500}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setOptReceiptHeader(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setReceiptHeader).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
      expect(component.headerErrors[index]).toBeTruthy();
    });
  });

  describe('.setGlobalReceiptHeader()', () => {
    
    it('.setGlobalReceiptHeader() should handle success response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setGlobalReceiptHeader.and.returnValue(of({}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setGlobalReceiptHeader(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setGlobalReceiptHeader).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
      expect(component.headerErrors[index]).toBeFalsy();
    });
  
    it('.setGlobalReceiptHeader() should handle unsuccess response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setGlobalReceiptHeader.and.returnValue(throwError({status: 500}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setGlobalReceiptHeader(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setGlobalReceiptHeader).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
      expect(component.headerErrors[index]).toBeTruthy();
    });
  });

  describe('.setOptReceiptFooter()', () => {

    it('.setOptReceiptFooter() should handle success response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setReceiptFooter.and.returnValue(of({}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setOptReceiptFooter(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setReceiptFooter).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
      expect(component.footerErrors[index]).toBeFalsy();
    });
  
    it('.setOptReceiptFooter() should handle unsuccess response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setReceiptFooter.and.returnValue(throwError({status: 500}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setOptReceiptFooter(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setReceiptFooter).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
      expect(component.footerErrors[index]).toBeTruthy();
    });
  });

  describe('.setGlobalReceiptFooter()', () => {

    it('.setGlobalReceiptFooter() should handle success response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setGlobalReceiptFooter.and.returnValue(of({}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setGlobalReceiptFooter(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setGlobalReceiptFooter).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
      expect(component.footerErrors[index]).toBeFalsy();
    });
  
    it('.setGlobalReceiptFooter() should handle unsuccess response from service', () => {
      //Arrange
      OPT_SERVICE_SPY().setGlobalReceiptFooter.and.returnValue(throwError({status: 500}));
  
      const event = new Event('click');
      const index = 0;
      
      //Act
      component.setGlobalReceiptFooter(event, index);
  
      //Assert
      expect(OPT_SERVICE_SPY().setGlobalReceiptFooter).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
      expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
      expect(component.footerErrors[index]).toBeTruthy();
    });
  });

  describe('.getErrorMessage()', () => {

    it('should return Http Response error message', () => {
      let error = new HttpErrorResponse({ error: { Message: 'API Error Message' } });

      let result = component['getErrorMessage'](error);

      expect(result).toBe('API Error Message');
    });

    it('should return default error message when no Http Response error', () => {
      let result = component['getErrorMessage'](null);

      expect(result).toBe('Problem updating receipt');
    });
  });
});
