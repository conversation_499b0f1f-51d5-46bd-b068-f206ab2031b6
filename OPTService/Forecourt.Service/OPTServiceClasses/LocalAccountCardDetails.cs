namespace OPTService.OPTServiceClasses
{
    public class LocalAccountCardDetails
    {
        public string Pan { get; set; }
        public string Description { get; set; }
        public float Discount { get; set; }
        public bool NoRestrictions { get; set; }
        public bool Unleaded { get; set; }
        public bool Diesel { get; set; }
        public bool Lpg { get; set; }
        public bool Lrp { get; set; }
        public bool GasOil { get; set; }
        public bool AdBlue { get; set; }
        public bool Kerosene { get; set; }
        public bool Oil { get; set; }
        public bool Avgas { get; set; }
        public bool Jet { get; set; }
        public bool Mogas { get; set; }
        public bool Valeting { get; set; }
        public bool OtherMotorRelatedGoods { get; set; }
        public bool ShopGoods { get; set; }
        public bool Hot { get; set; }
        public string CustomerReference { get; set; }
        public int Restrictions1 { get; set; }
        public int Restrictions2 { get; set; }
    }
}
