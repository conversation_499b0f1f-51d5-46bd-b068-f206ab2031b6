import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { InfoMessagesService } from './info-messages.service';
import { environment } from 'src/environments/environment';
import { DOCUMENT } from '@angular/common';

describe('InfoMessagesService', () => {
  let infoMessagesService: InfoMessagesService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };
    
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        InfoMessagesService,
        {provide: NGXLogger, useValue: ngxLoggertSpy},
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    infoMessagesService = TestBed.inject(InfoMessagesService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(infoMessagesService).toBeTruthy();
  });

  it('.getInfoMessageDetails() should handle success response from API', () => {
    //Arrange
    let mockedResponse = [ {
      "Time": "2021-03-02T17:07:48.1350119+00:00",
      "Message": "Test message"
    }];
    
    //Act
    infoMessagesService.getInfoMessageDetails().subscribe((infoMessages) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(infoMessages).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.infoMessagesController.base + environment.infoMessagesController.getInfoMessageDetails);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getInfoMessageDetails() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    infoMessagesService.getInfoMessageDetails().subscribe(infoMessages =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.infoMessagesController.base + environment.infoMessagesController.getInfoMessageDetails);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
  
});
