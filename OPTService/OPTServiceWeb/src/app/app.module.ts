import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { OptsComponent } from './components/opts/opts.component';
import { PumpsComponent } from './components/pumps/pumps.component';
import { FuelPricesComponent } from './components/fuel-prices/fuel-prices.component';
import { ConnectionsComponent } from './components/connections/connections.component';
import { NgbActiveModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { GenericOptConfigComponent } from './components/generic-opt-config/generic-opt-config.component';
import { AdvancedConfigComponent } from './components/advanced-config/advanced-config.component';
import { FileLocationsComponent } from './components/file-locations/file-locations.component';
import { ShiftEndComponent } from './components/shift-end/shift-end.component';
import { TransactionsComponent } from './components/transactions/transactions.component';
import { LocalAccountsComponent } from './components/local-accounts/local-accounts.component';
import { DomsComponent } from './components/doms/doms.component';
import { InfoMessagesComponent } from './components/info-messages/info-messages.component';
import { AboutComponent } from './components/about/about.component';
import { HttpClientModule, HTTP_INTERCEPTORS }​​​​​​​​ from'@angular/common/http';
import { OptComponent } from './components/opt/opt.component';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { PumpComponent } from './components/pump/pump.component';
import { CardReferenceComponent } from './components/card-reference/card-reference.component';
import { LabelTextComponent } from './components/complex-controls/label-text/label-text.component';
import { LabelTextButtonComponent } from './components/complex-controls/label-text-button/label-text-button.component';
import { LabelTextButtonButtonComponent } from './components/complex-controls/label-text-button-button/label-text-button-button.component';
import { SwitchLabelComponent } from './components/complex-controls/switch-label/switch-label.component';
import { FuelPriceComponent } from './components/fuel-price/fuel-price.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LoyaltySchemeComponent } from './components/loyalty-scheme/loyalty-scheme.component';
import { CurrencyPipe, DecimalPipe } from '@angular/common';
import { PersistentDataService } from './services/persistent-data.service';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SettingComponent } from './components/setting/setting.component';
import { LocalAccountCardComponent } from './components/local-account-card/local-account-card.component';
import { RemoveCommaPipe } from './core/pipes/removeCommaPipe';
import { MiscSettingComponent } from './components/misc-setting/misc-setting.component';
import { MiscSettingDetailComponent } from './components/misc-setting/misc-setting-detail.component';
import { InputErrorMessagesComponent } from './components/complex-controls/input-error-messages/input-error-messages.component';
import { OptReceiptComponent } from './components/opt-receipt/opt-receipt.component';
import { LabelDropdownButtonComponent } from './components/complex-controls/label-dropdown-button/label-dropdown-button.component';
import { EsocketPosConfigComponent } from './components/esocket-pos-config/esocket-pos-config.component';
import { EsocketConnectedBadgeComponent } from './components/esocket-pos-config/esocket-connected-badge/esocket-connected-badge.component';
import { CardAidsComponent } from './components/generic-opt-config/card-aids/card-aids.component';
import { CardContactlessAidsComponent } from './components/generic-opt-config/card-contactless-aids/card-contactless-aids.component';
import { CardCapksComponent } from './components/generic-opt-config/card-capks/card-capks.component';
import { CardFuelComponent } from './components/generic-opt-config/card-fuel/card-fuel.component';
import { CardDivertOptComponent } from './components/generic-opt-config/card-divert-opt/card-divert-opt.component';
import { HeaderInterceptor } from './services/header-interceptor';

@NgModule({
  declarations: [
    AppComponent,
    OptsComponent,
    PumpsComponent,
    FuelPricesComponent,
    ConnectionsComponent,
    GenericOptConfigComponent,
    AdvancedConfigComponent,
    FileLocationsComponent,
    ShiftEndComponent,
    TransactionsComponent,
    LocalAccountsComponent,
    LocalAccountCardComponent,
    DomsComponent,
    InfoMessagesComponent,
    AboutComponent,
    OptComponent,
    PumpComponent,
    CardReferenceComponent,
    LabelTextComponent,
    LabelTextButtonComponent,
    LabelTextButtonButtonComponent,
    SwitchLabelComponent,
    FuelPriceComponent,
    LoyaltySchemeComponent,
    SettingComponent,
    MiscSettingComponent,
    MiscSettingDetailComponent,
    InputErrorMessagesComponent,
    OptReceiptComponent,
    LabelDropdownButtonComponent,
    EsocketPosConfigComponent,
    EsocketConnectedBadgeComponent,
    CardAidsComponent,
    CardContactlessAidsComponent,
    CardCapksComponent,
    CardFuelComponent,
    CardDivertOptComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    NgbModule,
    HttpClientModule,
    ReactiveFormsModule,
    FormsModule,
    LoggerModule.forRoot({level: NgxLoggerLevel.DEBUG}),
    BrowserAnimationsModule,
    FontAwesomeModule
  ],
  providers: [OptsComponent, FormBuilder, NgbActiveModal, CurrencyPipe, DecimalPipe, PersistentDataService, RemoveCommaPipe, {
    provide: HTTP_INTERCEPTORS, useClass: HeaderInterceptor, multi: true
  } ],
  bootstrap: [AppComponent]
})
export class AppModule { }
