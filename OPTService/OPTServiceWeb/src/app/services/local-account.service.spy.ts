import { TestBed } from "@angular/core/testing";
import { LocalAccountService } from "./local-account.service";

export function LOCAL_ACCOUNT_SERVICE_PROVIDER(): { provide: typeof LocalAccountService, useValue: any } {
  return {
    provide: LocalAccountService,
    useValue:  jasmine.createSpyObj('LocalAccountService', [
      'addLocalAccountCard',
      'setLocalAccountCardHot',
      'addLocalAccountCustomer',
      'getLocalAccounts', 
      'removeLocalAccountCustomer',
      'setLocalAccountCustomerBalance',
      'removeLocalAccountCard',
      'setLocalAccountsEnabled',
    ]),
  };
}

export function LOCAL_ACCOUNT_SERVICE_SPY(): jasmine.SpyObj<LocalAccountService> {
  return TestBed.inject(LocalAccountService) as jasmine.SpyObj<LocalAccountService>;
}