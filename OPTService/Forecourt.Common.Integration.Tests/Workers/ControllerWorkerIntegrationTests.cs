using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.UpdateFileClasses;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Threading;
using Xunit;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace OPT.Common.Integration.Tests.Workers
{
    public class ControllerWorkerIntegrationTests
    {
        private const string HydraId = "Hydra 1";
        private const string UpgradeFileDirectory = "Upgrade File Directory";
        private const string RollbackFileDirectory = "Rollback File Directory";
        private const string FuelDataUpdateFile = "ToSend\\FuelData.upd";
        private const string PlaylistFileName = "Play List File";
        private const string ContactlessPropertiesFile = "Contactless Properties File";
        private const string OptIpAddress = "***************";
        private const string PumpIpAddress = "***************";
        private const string AnprIpAddress = "***************";
        private const string CarWashIpAddress = "***************";
        private const string TankGaugeIpAddress = "***************";
        private const string HydraMobileIpAddress = "***************";
        private const string EsocketIpAddress = "***************";
        private const string ControllerName = "Controller";
        private const int FromOptPort = 123;
        private const int ToOptPort = 124;
        private const int HeartbeatPort = 125;
        private const int HydraPosPort = 126;
        private const int RetalixPosPort = 127;
        private const int ThirdPartyPosPort = 128;
        private const int MediaChannelPort = 129;
        private const int PumpPort = 129;
        private const int AnprPort = 120;
        private const int CarWashPort = 121;
        private const int TankGaugePort = 122;
        private const int HydraMobilePort = 123;
        private const int EsocketPort = 124;
        private const string Information = "Some Information";
        private const byte PumpOne = 1;
        private const byte PumpTwo = 2;
        private const string Tid = "12345678";
        private const string OptIdString = "Opt2";
        private readonly IHydraPosWorker _hydraPosWorker;
        private readonly IRetalixPosWorker _retalixPosWorker;
        private readonly IThirdPartyPosWorker _thirdPartyPosWorker;
        private readonly IMediaChannelWorker _mediaChannelWorker;
        private readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOutWorker;
        private readonly ISecAuthIntegratorInTransient<IMessageTracking> _secAuthInWorker;
        private readonly IInfoMessagesConfig _infoMessagesConfig;
        private readonly ICarWashWorker _carWashWorker;
        private readonly ITankGaugeWorker _tankGaugeWorker;
        private readonly IHydraMobileWorker _hydraMobileWorker;
        private readonly IPumpWorker _hscWorker;
        private readonly IJournalWorker _journalWorker;
        private readonly IUpdateWorker _updateWorker;
        private readonly IConfigUpdateWorker _configUpdateWorker;
        private readonly ILocalAccountWorker _localAccountWorker;
        private readonly IDomsWorker _domsWorker;
        private readonly IOptCollection _allOpts;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pumpOne;
        private readonly IPump _pumpTwo;
        private readonly IOpt _optOne;
        private readonly IOpt _optTwo;
        private readonly IHydraDb _hydraDb;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IHtecLogger _logger;
        private readonly IControllerWorker _controllerWorker;
        private readonly IFromOptWorker _optWorker;
        private readonly IHydraTransactionFile _transactionFile;
        private readonly IRetalixTransactionFile _retalixTransactionFile;
        private readonly PushChangeDelegate _pushChangeDelegate;
        private readonly ILoggingHelper _loggingHelper;
        private readonly ITimerFactory _timerFactory;
        private readonly ICore _core;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPosIntegratorInMode<IMessageTracking> _posInModeWorker;

        public ControllerWorkerIntegrationTests()
        {
            _hydraPosWorker = Substitute.For<IHydraPosWorker>();
            _retalixPosWorker = Substitute.For<IRetalixPosWorker>();
            _thirdPartyPosWorker = Substitute.For<IThirdPartyPosWorker>();
            _mediaChannelWorker = Substitute.For<IMediaChannelWorker>();
            _secAuthOutWorker = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();
            _carWashWorker = Substitute.For<ICarWashWorker>();
            _tankGaugeWorker = Substitute.For<ITankGaugeWorker>();
            _hydraMobileWorker = Substitute.For<IHydraMobileWorker>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _journalWorker = Substitute.For<IJournalWorker>();
            _updateWorker = Substitute.For<IUpdateWorker>();
            _configUpdateWorker = Substitute.For<IConfigUpdateWorker>();
            _localAccountWorker = Substitute.For<ILocalAccountWorker>();
            _domsWorker = Substitute.For<IDomsWorker>();
            _optOne = Substitute.For<IOpt>();
            _optTwo = Substitute.For<IOpt>();
            _allOpts = Substitute.For<IOptCollection>();
            _allOpts.GetOptForIdString(OptIdString).Returns(_optTwo);
            _allOpts.AllOpts.Returns(new List<IOpt> {_optOne, _optTwo});
            _optTwo.IdString.Returns(OptIdString);
            _optTwo.PlaylistFileName.Returns(PlaylistFileName);
            _pumpOne = Substitute.For<IPump>();
            _pumpOne.Opt.Returns(_optOne);
            _pumpTwo = Substitute.For<IPump>();
            _pumpTwo.Number.Returns(PumpTwo);
            _pumpTwo.Opt.Returns(_optTwo);
            _allPumps = Substitute.For<IPumpCollection>();
            _allPumps.TryGetPump(0, out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (byte) x[0] == PumpOne ? _pumpOne : null;
                return (byte) x[0] == PumpOne;
            });
            _allPumps.GetPumpForTid(Tid).Returns(_pumpTwo);
            _allPumps.GetPump(PumpOne).Returns(_pumpOne);
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.FetchEndPoints(HydraId).Returns(new OptEndPoints(HydraId, OptIpAddress, FromOptPort, ToOptPort, HeartbeatPort,
                HydraPosPort, RetalixPosPort, ThirdPartyPosPort, MediaChannelPort));
            _hydraDb.FetchPumpEndPoint().Returns(new connGenericEndPoint(PumpIpAddress, PumpPort));
            _hydraDb.FetchAnprEndPoint().Returns(new AnprEndPoint(AnprIpAddress, AnprPort));
            _hydraDb.FetchCarWashEndPoint().Returns(new CarWashEndPoint(CarWashIpAddress, CarWashPort));
            _hydraDb.FetchTankGaugeEndPoint().Returns(new TankGaugeEndPoint(TankGaugeIpAddress, TankGaugePort));
            _hydraDb.FetchHydraMobileEndPoint().Returns(new HydraMobileEndPoint(HydraMobileIpAddress, HydraMobilePort));
            _hydraDb.FetchESocketEndPoints().Returns(new List<connGenericEndPoint>
            {
                new ESocketEndPoint(EsocketIpAddress, EsocketPort)
            });
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", "", "", "", "", FuelDataUpdateFile, UpgradeFileDirectory,
                RollbackFileDirectory, "", "", "", "", "", "", "", "", "", true, "", "", "", false, false, false, false));
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _paymentConfig.CurrentContactlessFile.Returns(ContactlessPropertiesFile);

            _logger = Substitute.For<IHtecLogger>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _optWorker.RestartOpt(OptIdString).Returns(true);
            _transactionFile = Substitute.For<IHydraTransactionFile>();
            _retalixTransactionFile = Substitute.For<IRetalixTransactionFile>();
            _pushChangeDelegate = Substitute.For<PushChangeDelegate>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _core = Substitute.For<ICore>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _posInModeWorker = Substitute.For<IPosIntegratorInMode<IMessageTracking>>();
            _secAuthInWorker = Substitute.For<ISecAuthIntegratorIn<IMessageTracking>>();
            _infoMessagesConfig = Substitute.For<IInfoMessagesConfig>();

            _controllerWorker = new ControllerWorker((_hydraPosWorker, _hydraMobileWorker), _mediaChannelWorker, _secAuthInWorker,
                _secAuthOutWorker, _carWashWorker, _tankGaugeWorker, _posInModeWorker, _hydraMobileWorker, _hscWorker, _hscWorker, _journalWorker, _updateWorker,
                _configUpdateWorker, _localAccountWorker, _allOpts, _allPumps, _hydraDb, _paymentConfig, _transactionFile,
                _retalixTransactionFile, _logger, "Hydra", _loggingHelper, new ConfigurationManagerWrapper(), _telemetryWorker, _timerFactory,
                _infoMessagesConfig, Substitute.For<IIntegratorFactories>());
            _controllerWorker.RegisterWorker(_optWorker);
            _controllerWorker.PushChangeEvent += _pushChangeDelegate;
            _controllerWorker.Start(HydraId);
        }

        [Fact]
        public void test_initial_state()
        {
            // Arrange

            // Act
            Connections connections = _controllerWorker.GetConnections();
            Endpoints endpoints = _controllerWorker.GetEndpoints();
            IEnumerable<InfoMessage> info = _controllerWorker.GetInfo();

            // Assert
            _controllerWorker.UpgradeFileDirectory.Should().Be(UpgradeFileDirectory);
            _controllerWorker.RollbackFileDirectory.Should().Be(RollbackFileDirectory);
            _controllerWorker.IsConfigBatch.Should().BeFalse();
            _controllerWorker.UploadedFileNames.Should().BeEmpty();
            _controllerWorker.GradePriceToSet.Should().BeEmpty();

            connections.AllOpt.Should().Be(0);
            connections.FromOpt.Should().Be(0);
            connections.ToOpt.Should().Be(0);
            connections.Heartbeat.Should().Be(0);
            connections.HydraPos.Should().Be(0);
            connections.ThirdPartyPos.Should().Be(0);
            connections.SecAuth.Should().BeFalse();
            connections.CarWash.Should().BeFalse();
            connections.SiteController.Should().BeFalse();

            endpoints.FromOpt.Address.ToString().Should().Be(OptIpAddress);
            endpoints.FromOpt.Port.Should().Be(FromOptPort);
            endpoints.ToOpt.Address.ToString().Should().Be(OptIpAddress);
            endpoints.ToOpt.Port.Should().Be(ToOptPort);
            endpoints.Heartbeat.Address.ToString().Should().Be(OptIpAddress);
            endpoints.Heartbeat.Port.Should().Be(HeartbeatPort);
            endpoints.HydraPos.Address.ToString().Should().Be(OptIpAddress);
            endpoints.HydraPos.Port.Should().Be(HydraPosPort);
            endpoints.ThirdPartyPos.Address.ToString().Should().Be(OptIpAddress);
            endpoints.ThirdPartyPos.Port.Should().Be(ThirdPartyPosPort);
            endpoints.Pump.Address.ToString().Should().Be(PumpIpAddress);
            endpoints.Pump.Port.Should().Be(PumpPort);
            endpoints.Anpr.Address.ToString().Should().Be(AnprIpAddress);
            endpoints.Anpr.Port.Should().Be(AnprPort);
            endpoints.CarWash.Address.ToString().Should().Be(CarWashIpAddress);
            endpoints.CarWash.Port.Should().Be(CarWashPort);
            endpoints.TankGauge.Address.ToString().Should().Be(TankGaugeIpAddress);
            endpoints.TankGauge.Port.Should().Be(TankGaugePort);
            endpoints.HydraMobile.Address.ToString().Should().Be(HydraMobileIpAddress);
            endpoints.HydraMobile.Port.Should().Be(HydraMobilePort);
            endpoints.Esocket.Should().HaveCount(1);
            endpoints.Esocket[0].Address.ToString().Should().Be(EsocketIpAddress);
            endpoints.Esocket[0].Port.Should().Be(EsocketPort);

            info.Should().BeEmpty();

            _secAuthOutWorker.Received(1).RegisterWorker(_controllerWorker);
            _carWashWorker.Received(1).RegisterWorker(_controllerWorker);
            _hscWorker.Received(1).RegisterWorker(_controllerWorker);
            _hydraPosWorker.Received(1).RegisterWorker(_controllerWorker);
            _thirdPartyPosWorker.Received(1).RegisterWorker(_controllerWorker);

            _pushChangeDelegate.DidNotReceiveWithAnyArgs().Invoke(EventType.NewMessageChanged, "", "");
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.OPTChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.AdvancedConfigChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.FileLocationsChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.FuelPriceChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.ShiftEndChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.DivertDetailsChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.AboutChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.TransactionsChanged, null);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_connections_changed()
        {
            // Arrange

            // Act
            _controllerWorker.ConnectionsChanged();
            Thread.Sleep(2000);

            // Assert
            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged);
            _optWorker.Received(1).SendToOptHeartbeat();
            _optWorker.Received(1).CheckConfigNeeded();
        }


        [Fact]
        public void test_send_information()
        {
            // Arrange

            // Act
            _controllerWorker.SendInformation(Information);
            IList<InfoMessage> info = new List<InfoMessage>(_controllerWorker.GetInfo());

            // Assert
            info.Should().HaveCount(1);
            info[0].Message.Should().Be(Information);
            _pushChangeDelegate.Received(1).Invoke(EventType.InfoMessageChanged, null, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.NewMessageChanged, ControllerName, Information);
        }

        [Fact]
        public void test_open_pump()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(true);
            _pumpOne.PumpState.Returns(PumpStateType.Closed);

            // Act
            string result = _controllerWorker.OpenPump(PumpOne);

            // Assert
            result.Should().BeNull();
            _pumpOne.Received(1).OpenPump();
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckPumpState(_pumpOne, PumpStateType.Closed);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, "");
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "0");
        }

        [Fact]
        public void test_close_pump()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(false);
            _pumpOne.PumpState.Returns(PumpStateType.Open);

            // Act
            string result = _controllerWorker.ClosePump(PumpOne);

            // Assert
            result.Should().BeNull();
            _pumpOne.Received(1).ClosePump();
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckPumpState(_pumpOne, PumpStateType.Open);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, "");
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "0");
        }

        [Fact]
        public void test_open_pump_already_open()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(false);
            _pumpOne.PumpState.Returns(PumpStateType.Open);

            // Act
            string result = _controllerWorker.OpenPump(PumpOne);

            // Assert
            result.Should().Be("Unable to Open Pump");
            _pumpOne.DidNotReceive().OpenPump();
            _hydraPosWorker.DidNotReceive().StatusResponse(_pumpOne.Number);
            _optWorker.DidNotReceive().CheckPumpState(_pumpOne, PumpStateType.Closed);

            _pushChangeDelegate.DidNotReceive().Invoke(EventType.OPTChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.PumpChanged, null);
        }

        [Fact]
        public void test_close_pump_already_closed()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(true);
            _pumpOne.PumpState.Returns(PumpStateType.Closed);

            // Act
            string result = _controllerWorker.ClosePump(PumpOne);

            // Assert
            result.Should().Be("Unable to Close Pump");
            _pumpOne.DidNotReceive().ClosePump();
            _hydraPosWorker.DidNotReceive().StatusResponse(_pumpOne.Number);
            _optWorker.DidNotReceive().CheckPumpState(_pumpOne, PumpStateType.Open);

            _pushChangeDelegate.DidNotReceive().Invoke(EventType.OPTChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.PumpChanged, null);
        }

        [Fact]
        public void test_force_close_pump()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(false);
            _pumpOne.PumpState.Returns(PumpStateType.Open);

            // Act
            string result = _controllerWorker.ForceClosePump(PumpOne);

            // Assert
            result.Should().BeNull();
            _pumpOne.Received(1).ForceClosePump();
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckPumpState(_pumpOne, PumpStateType.Open);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, "");
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "0");
        }

        [Fact]
        public void test_force_close_pump_already_closed()
        {
            // Arrange
            _pumpOne.PumpIsClosed.Returns(true);
            _pumpOne.PumpState.Returns(PumpStateType.Closed);

            // Act
            string result = _controllerWorker.ForceClosePump(PumpOne);

            // Assert
            result.Should().Be("Unable to Force Close Pump");
            _pumpOne.DidNotReceive().ForceClosePump();
            _hydraPosWorker.DidNotReceive().StatusResponse(_pumpOne.Number);
            _optWorker.DidNotReceive().CheckPumpState(_pumpOne, PumpStateType.Open);

            _pushChangeDelegate.DidNotReceive().Invoke(EventType.OPTChanged);
            _pushChangeDelegate.DidNotReceive().Invoke(EventType.PumpChanged, null);
        }

        [Fact]
        public void test_map_to_tid()
        {
            // Arrange

            // Act
            _controllerWorker.MapToTid(PumpOne, Tid);

            // Assert
            _pumpTwo.Received(1).SetTid(null);
            _pumpOne.Received(1).SetTid(Tid);
            _optWorker.ReceivedWithAnyArgs(2).CheckOptConfig(null);
            _optWorker.Received(1).CheckOptConfig(_optOne);
            _optWorker.Received(1).CheckOptConfig(_optTwo);
            _hydraDb.Received(1).MapTid(PumpOne, Tid);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, "");
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "1");
        }

        [Fact]
        public void test_map_to_opt()
        {
            // Arrange

            // Act
            _controllerWorker.MapToOpt(PumpOne, OptIdString);

            // Assert
            _pumpOne.Received(1).SetOpt(_optTwo);
            _optOne.Received(1).RemovePump(_pumpOne);
            _optTwo.Received(1).AddPump(_pumpOne);
            _optWorker.ReceivedWithAnyArgs(2).CheckOptConfig(null);
            _optWorker.Received(1).CheckOptConfig(_optOne);
            _optWorker.Received(1).CheckOptConfig(_optTwo);
            _hydraDb.Received(1).MapOpt(PumpOne, OptIdString);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, "");
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, null);
        }

        [Fact]
        public void test_set_grade_name()
        {
            // Arrange
            const byte grade = 1;
            const string gradeName = "A Grade";

            // Act
            string result = _controllerWorker.SetGradeName(grade, gradeName);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetGradeName(grade, gradeName);
            _pushChangeDelegate.Received(1).Invoke(EventType.FuelPriceChanged, grade.ToString());
        }

        [Fact]
        public void test_set_grade_vat_rate()
        {
            // Arrange
            const byte grade = 1;
            const float vatRate = 20.0F;

            // Act
            string result = _controllerWorker.SetGradeVatRate(grade, vatRate);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetGradeName(grade, vatRate);
            _pushChangeDelegate.Received(1).Invoke(EventType.FuelPriceChanged, grade.ToString());
        }

        [Fact]
        public void test_set_default_mode_mixed()
        {
            // Arrange

            // Act
            var result = _controllerWorker.SetDefaultMode(PumpOne, false, false);

            // Assert
            result.IsSuccess.Should().BeTrue();
            _pumpOne.Received(1).SetMixed(true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckOptConfig(_optOne);
        }

        [Fact]
        public void test_set_default_mode_kiosk_only()
        {
            // Arrange

            // Act
            var result = _controllerWorker.SetDefaultMode(PumpOne, true, false);

            // Assert
            result.IsSuccess.Should().BeTrue();
            _pumpOne.Received(1).SetKioskOnly(true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckOptConfig(_optOne);
        }

        [Fact]
        public void test_set_default_mode_outside_only()
        {
            // Arrange

            // Act
            var result = _controllerWorker.SetDefaultMode(PumpOne, false, true);

            // Assert
            result.IsSuccess.Should().BeTrue();
            _pumpOne.Received(1).SetOutsideOnly(true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _optWorker.Received(1).CheckOptConfig(_optOne);
        }

        [Fact]
        public void test_set_auto_auth()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetAutoAuth(true);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetAutoAuth(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_contactless_allowed()
        {
            // Arrange
            _optTwo.HasContactless.Returns(true);

            // Act
            string result = _controllerWorker.SetContactlessAllowed(true);

            // Assert
            result.Should().BeNull();
            _optTwo.Received(1).SetContactless(true);
            _hydraDb.Received(1).SetContactless(true);
            _optWorker.Received(1).CheckOptConfig(_optTwo);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, OptIdString);
        }

        [Fact]
        public void test_set_receipt_header()
        {
            // Arrange
            const string receiptHeader = "A Receipt Header";
            _optTwo.ReceiptHeader.Returns(receiptHeader);

            // Act
            string result = _controllerWorker.SetReceiptHeader(OptIdString, receiptHeader);

            // Assert
            result.Should().BeNull();
            _optTwo.Received(1).SetReceiptHeader(receiptHeader);
            _hydraDb.Received(1).SetReceiptHeader(OptIdString, receiptHeader);
            _optWorker.Received(1).CheckOptConfig(_optTwo);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, OptIdString);
        }


        [Fact]
        public void test_set_payment_timeout_mixed()
        {
            // Arrange
            const string mixedModeString = "Mixed";
            const string optModeString = "OPT";
            const string podModeString = "POD";
            const string nozzleDownModeString = "Nozzle Down";
            const string unknownModeString = ConfigConstants.Unknown;
            const int mixedTimeout = 100;
            const int optTimeout = 200;
            const int podTimeout = 300;
            const int nozzleDownTimeout = 400;
            const int unknownTimeout = 500;

            // Act
            bool result1 = _controllerWorker.SetPaymentTimeout(mixedModeString, mixedTimeout);
            bool result2 = _controllerWorker.SetPaymentTimeout(optModeString, optTimeout);
            bool result3 = _controllerWorker.SetPaymentTimeout(podModeString, podTimeout);
            bool result4 = _controllerWorker.SetPaymentTimeout(nozzleDownModeString, nozzleDownTimeout);
            bool result5 = _controllerWorker.SetPaymentTimeout(unknownModeString, unknownTimeout);

            // Assert
            result1.Should().BeTrue();
            result2.Should().BeTrue();
            result3.Should().BeTrue();
            result4.Should().BeTrue();
            result5.Should().BeFalse();
            _optWorker.Received(1).SetPaymentTimeout(PaymentTimeoutType.Mixed, mixedTimeout);
            _optWorker.Received(1).SetPaymentTimeout(PaymentTimeoutType.Opt, optTimeout);
            _optWorker.Received(1).SetPaymentTimeout(PaymentTimeoutType.Pod, podTimeout);
            _optWorker.Received(1).SetPaymentTimeout(PaymentTimeoutType.NozzleDown, nozzleDownTimeout);
            _optWorker.DidNotReceive().SetPaymentTimeout(Arg.Any<PaymentTimeoutType>(), unknownTimeout);

            _pushChangeDelegate.Received(4).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_set_receipt_timeout()
        {
            // Arrange
            const int timeout = 100;

            // Act
            string result = _controllerWorker.SetReceiptTimeout(timeout);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetReceiptTimeout(timeout);

            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_set_receipt_max_count()
        {
            // Arrange
            const int count = 100;

            // Act
            string result = _controllerWorker.SetReceiptMaxCount(count);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetReceiptMaxCount(count);

            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_set_site_controller()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int port = 1259;

            // Act
            var result = _controllerWorker.SetPumpControllerAddress(address, port);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetPumpEndPoint(address, port);
            _hscWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_anpr()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int port = 10026;

            // Act
            string result = _controllerWorker.SetAnpr(address, port);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetAnprEndPoint(address, port);
            _secAuthOutWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_car_wash()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int port = 1255;

            // Act
            string result = _controllerWorker.SetCarWash(address, port);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetCarWashEndPoint(address, port);
            _carWashWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_service_ports()
        {
            // Arrange
            const int fromOptPort = 1262;
            const int toOptPort = 1263;
            const int heartbeatPort = 1264;
            const int hydraPosPort = 1261;
            const int retalixPosPort = 10029;
            const int thirdPartyPosPort = 10030;
            const int mediaChannelPort = 1267;

            // Act
            string result = _controllerWorker.SetServicePorts(fromOptPort, toOptPort, heartbeatPort, hydraPosPort, retalixPosPort,
                thirdPartyPosPort, mediaChannelPort);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetServicePorts(HydraId, fromOptPort, toOptPort, heartbeatPort, hydraPosPort, retalixPosPort,
                thirdPartyPosPort, mediaChannelPort);
            _optWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_service_address()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;

            // Act
            string result = _controllerWorker.SetServiceAddress(address);

            // Assert
            result.Should().BeNull();
            _hydraDb.SetServiceAddress(HydraId, address);
            _optWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_add_esocket()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int port = 10025;

            // Act
            string result = _controllerWorker.AddEsocket(address, port);

            // Assert
            result.Should().BeNull();
            _hydraDb.AddEsocket(address, port);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optWorker.Received(1).CheckEsocketChanges();

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_remove_esocket()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int port = 10025;

            // Act
            string result = _controllerWorker.RemoveEsocket(address, port);

            // Assert
            result.Should().BeNull();
            _hydraDb.RemoveEsocket(address, port);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optWorker.Received(1).CheckEsocketChanges();

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_reload_opt_configuration()
        {
            // Arrange

            // Act
            _controllerWorker.ReloadOptConfiguration();

            // Assert
            _optWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, null);
            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged);
        }

        [Fact]
        public void test_divert_opt_service()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;
            const int fromOptPort = 1262;
            const int toOptPort = 1263;
            const int heartbeatPort = 1264;
            const int mediaChannelPort = 1266;

            // Act
            _controllerWorker.DivertOptService(address, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort);

            // Assert
            _optWorker.Received(1).DivertOptService(address, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort);
            _pushChangeDelegate.Received(1).Invoke(EventType.DivertDetailsChanged, null);
        }

        [Fact]
        public void test_cancel_divert_opt_service()
        {
            // Arrange

            // Act
            _controllerWorker.CancelDivertOptService();

            // Assert
            _optWorker.Received(1).CancelDivertOptService();
            _pushChangeDelegate.Received(1).Invoke(EventType.DivertDetailsChanged, null);
        }

        [Fact]
        public void test_is_opt_service_diverted()
        {
            // Arrange
            IPAddress divertAddress = IPAddress.Loopback;
            const int divertFromOptPort = 1262;
            const int divertToOptPort = 1263;
            const int divertHeartbeatPort = 1264;
            const int divertMediaChannelPort = 1266;
            _optWorker.IsOptServiceDiverted(out _, out _, out _, out _, out _).ReturnsForAnyArgs(x =>
            {
                x[0] = divertAddress;
                x[1] = divertFromOptPort;
                x[2] = divertToOptPort;
                x[3] = divertHeartbeatPort;
                x[4] = divertMediaChannelPort;
                return true;
            });

            // Act
            bool result = _controllerWorker.IsOptServiceDiverted(out IPAddress address, out int fromOptPort, out int toOptPort,
                out int heartbeatPort, out int mediaChannelPort);

            // Assert
            result.Should().BeTrue();
            address.Should().Be(divertAddress);
            fromOptPort.Should().Be(divertFromOptPort);
            toOptPort.Should().Be(divertToOptPort);
            heartbeatPort.Should().Be(divertHeartbeatPort);
            mediaChannelPort.Should().Be(divertMediaChannelPort);
            _optWorker.Received(1).IsOptServiceDiverted(out _, out _, out _, out _, out _);
        }

        [Fact]
        public void test_set_config_batch_start()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetConfigBatch(true);

            // Assert
            result.Should().BeNull();
            _controllerWorker.IsConfigBatch.Should().BeTrue();
            _optWorker.DidNotReceive().CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.LocalAccountsChanged, null);
        }

        [Fact]
        public void test_set_config_batch_end()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetConfigBatch(false);

            // Assert
            result.Should().BeNull();
            _controllerWorker.IsConfigBatch.Should().BeFalse();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.LocalAccountsChanged, null);
        }

        [Fact]
        public void test_add_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            // Act
            string result = _controllerWorker.AddGenericLoyalty(loyaltyName);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).AddGenericLoyalty(loyaltyName);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_delete_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            // Act
            string result = _controllerWorker.DeleteGenericLoyalty(loyaltyName);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).DeleteGenericLoyalty(loyaltyName);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_set_generic_loyalty_present()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";

            // Act
            string result = _controllerWorker.SetGenericLoyaltyPresent(loyaltyName, true);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetGenericLoyaltyPresent(loyaltyName, true);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_generic_loyalty()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";
            GenericLoyalty loyalty = new GenericLoyalty(new LoyaltyTerminal("", "", "", "", 0, "", ""),
                new List<GenericEndPoint> {new GenericEndPoint(IPAddress.Loopback, 100)}, new List<string> {"LoyaltyHost"},
                new List<LoyaltyIin> {new LoyaltyIin("", "")}, new List<LoyaltyMapping> {new LoyaltyMapping("", "")}, true);

            // Act
            string result = _controllerWorker.SetGenericLoyalty(loyaltyName, loyalty);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetGenericLoyalty(loyaltyName, loyalty);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_generic_loyalty_with_duplicates()
        {
            // Arrange
            const string loyaltyName = "A Loyalty";
            GenericLoyalty loyalty1 = new GenericLoyalty(new LoyaltyTerminal("", "", "", "", 0, "", ""),
                new List<GenericEndPoint> {new GenericEndPoint(IPAddress.Loopback, 100)}, new List<string> {"LoyaltyHost"},
                new List<LoyaltyIin> {new LoyaltyIin("", "")},
                new List<LoyaltyMapping> {new LoyaltyMapping("1", "3"), new LoyaltyMapping("2", "3")}, true);

            GenericLoyalty loyalty2 = new GenericLoyalty(new LoyaltyTerminal("", "", "", "", 0, "", ""),
                new List<GenericEndPoint> {new GenericEndPoint(IPAddress.Loopback, 100)}, new List<string> {"LoyaltyHost"},
                new List<LoyaltyIin> {new LoyaltyIin("", "")},
                new List<LoyaltyMapping> {new LoyaltyMapping("1", "3"), new LoyaltyMapping("1", "4")}, true);

            GenericLoyalty loyalty3 = new GenericLoyalty(new LoyaltyTerminal("", "", "", "", 0, "", ""),
                new List<GenericEndPoint> {new GenericEndPoint(IPAddress.Loopback, 100)}, new List<string> {"LoyaltyHost"},
                new List<LoyaltyIin> {new LoyaltyIin("", "")},
                new List<LoyaltyMapping> {new LoyaltyMapping("1", "3"), new LoyaltyMapping("1", "4"), new LoyaltyMapping("2", "4")}, true);

            GenericLoyalty loyalty4 = new GenericLoyalty(new LoyaltyTerminal("", "", "", "", 0, "", ""),
                new List<GenericEndPoint> {new GenericEndPoint(IPAddress.Loopback, 100)}, new List<string> {"LoyaltyHost"},
                new List<LoyaltyIin> {new LoyaltyIin("", "")},
                new List<LoyaltyMapping> {new LoyaltyMapping("1", "3"), new LoyaltyMapping("1", "3")}, true);

            // Act
            string result1 = _controllerWorker.SetGenericLoyalty(loyaltyName, loyalty1);
            string result2 = _controllerWorker.SetGenericLoyalty(loyaltyName, loyalty2);
            string result3 = _controllerWorker.SetGenericLoyalty(loyaltyName, loyalty3);
            string result4 = _controllerWorker.SetGenericLoyalty(loyaltyName, loyalty4);

            // Assert
            result1.Should().Be("Duplicate loyalty code");
            result2.Should().Be("Duplicate product code");
            result3.Should().Be("Duplicate product code and duplicate loyalty code");
            result4.Should().Be("Duplicate mapping");
            _hydraDb.DidNotReceive().SetGenericLoyalty(loyaltyName, Arg.Any<GenericLoyalty>());
            _optWorker.DidNotReceive().FetchGenericOptConfig();
            _optOne.DidNotReceive().ConfigCheckRequired();
            _optWorker.DidNotReceive().CheckOptConfig(_optOne);
        }

        [Fact]
        public void test_set_predefined_amount()
        {
            // Arrange
            IList<int> amounts = new List<int> {1000, 2000};
            IList<int> dbAmounts = new List<int>();
            _hydraDb.SetPredefinedAmounts(Arg.Do<IList<int>>(x =>
            {
                dbAmounts.Clear();
                foreach (int i in x) dbAmounts.Add(i);
            }));

            // Act
            string result = _controllerWorker.SetPredefinedAmounts(amounts);

            // Assert
            result.Should().BeNull();
            dbAmounts.Should().BeEquivalentTo(amounts);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_add_wash()
        {
            // Arrange
            Wash wash = new Wash(1, "", "", "", "", 0, 0);

            // Act
            string result = _controllerWorker.AddWash(wash);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).AddWash(wash);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_remove_wash()
        {
            // Arrange
            const int programId = 1;

            // Act
            string result = _controllerWorker.RemoveWashByProgramId(programId);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).RemoveWashByProgramId(programId);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_retalix_pos_primary_ip_address()
        {
            // Arrange
            IPAddress address = IPAddress.Loopback;

            // Act
            string result = _controllerWorker.SetRetalixPosPrimaryIpAddress(address);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetRetalixPosPrimaryIpAddress(address);

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_retalix_transaction_file_directory()
        {
            // Arrange
            const string directory = "Retalix Directory";
            const string expectedDirectory = "Retalix Directory\\";

            // Act
            string result = _controllerWorker.SetRetalixTransactionFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _retalixTransactionFile.Received(1).SetFileDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.RetalixTransactionFileDirectory.ToString());
        }

        [Fact]
        public void test_set_transaction_file_directory()
        {
            // Arrange
            const string directory = "Transaction Directory";
            const string expectedDirectory = "Transaction Directory\\";

            // Act
            string result = _controllerWorker.SetTransactionFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _transactionFile.Received(1).SetFileDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.TransactionFileDirectory.ToString());
        }

        [Fact]
        public void test_set_whitelist_directory()
        {
            // Arrange
            const string directory = "Whitelist Directory";
            const string expectedDirectory = "Whitelist Directory\\";

            // Act
            string result = _controllerWorker.SetWhitelistDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetWhitelistDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.WhitelistDirectory.ToString());
        }

        [Fact]
        public void test_set_software_directory()
        {
            // Arrange
            const string directory = "Software Directory";
            const string expectedDirectory = "Software Directory\\";

            // Act
            string result = _controllerWorker.SetSoftwareDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetSoftwareDirectory(expectedDirectory);
            _optWorker.Received(1).FetchAvailableSoftware();

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.SoftwareDirectory.ToString());
        }

        [Fact]
        public void test_set_contactless_properties_file()
        {
            // Arrange
            const string fileName = "Contactless Properties File";

            // Act
            string result = _controllerWorker.SetContactlessPropertiesFile(fileName);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetContactlessFile(fileName);
            _optWorker.Received(1).FetchGenericOptConfig();

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.ContactlessPropertiesFile.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_fuel_data_update_file()
        {
            // Arrange
            const string fileName = "Fuel Data Update File";

            // Act
            string result = _controllerWorker.SetFuelDataUpdateFile(fileName);

            // Assert
            result.Should().BeNull();
            _controllerWorker.FuelDataUpdateFile.Should().BeEquivalentTo(fileName);
            _hydraDb.Received(1).SetFuelDataUpdateFile(fileName);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.FuelDataUpdateFile.ToString());
        }

        [Fact]
        public void test_set_upgrade_file_directory()
        {
            // Arrange
            const string directory = "Upgrade File Directory\\";
            const string expectedDirectory = "Upgrade File Directory";

            // Act
            string result = _controllerWorker.SetUpgradeFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.UpgradeFileDirectory.Should().BeEquivalentTo(expectedDirectory);
            _hydraDb.Received(1).SetUpgradeFileDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.UpgradeFileDirectory.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.AboutChanged);
        }

        [Fact]
        public void test_set_rollback_file_directory()
        {
            // Arrange
            const string directory = "Rollback File Directory\\";
            const string expectedDirectory = "Rollback File Directory";

            // Act
            string result = _controllerWorker.SetRollbackFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.RollbackFileDirectory.Should().BeEquivalentTo(expectedDirectory);
            _hydraDb.Received(1).SetRollbackFileDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.RollbackFileDirectory.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.AboutChanged);
        }

        [Fact]
        public void test_set_receipt_layout_mode()
        {
            // Arrange
            const int mode = 1;

            // Act
            string result = _controllerWorker.SetReceiptLayoutMode(mode);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetReceiptLayoutMode(mode);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_site_name()
        {
            // Arrange
            const string siteName = "A Site";

            // Act
            string result = _controllerWorker.SetSiteName(siteName);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetSiteName(siteName);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_vat_number()
        {
            // Arrange
            const string vatNumber = "VAT Number";

            // Act
            string result = _controllerWorker.SetVatNumber(vatNumber);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetVatNumber(vatNumber);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_nozzle_up_for_kiosk_use()
        {
            // Arrange
            const bool flag = true;

            // Act
            string result = _controllerWorker.SetNozzleUpForKioskUse(flag);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetNozzleUpForKioskUse(flag);
            _optWorker.Received(1).FetchGenericOptConfig();
            _optOne.Received(1).ConfigCheckRequired();
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_next_day_end_later_time()
        {
            // Arrange
            DateTime nextDayEnd = DateTime.Now.AddHours(1);

            // Act
            string result = _controllerWorker.SetNextDayEnd(nextDayEnd);

            // Assert
            result.Should().BeNull();
            _controllerWorker.NextDayEnd.Should().NotBeNull();
            _controllerWorker.NextDayEnd.Should().Be(nextDayEnd);
            _hydraDb.Received(1).SetNextDayEnd(nextDayEnd);

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
        }

        [Fact]
        public void test_set_next_day_end_earlier_time()
        {
            // Arrange
            DateTime nextDayEnd = DateTime.Now.AddHours(-1);

            // Act
            string result = _controllerWorker.SetNextDayEnd(nextDayEnd);

            // Assert
            result.Should().BeNull();
            _controllerWorker.NextDayEnd.Should().NotBeNull();
            _controllerWorker.NextDayEnd.Should().Be(nextDayEnd.AddDays(1));
            _hydraDb.Received(1).SetNextDayEnd(nextDayEnd.AddDays(1));

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
        }

        [Fact]
        public void test_set_next_day_end_to_null()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetNextDayEnd(null);

            // Assert
            result.Should().BeNull();
            _controllerWorker.NextDayEnd.Should().BeNull();
            _hydraDb.Received(1).SetNextDayEnd(null);

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
        }

        [Fact]
        public void test_perform_shift_end()
        {
            // Arrange

            // Act
            string result = _controllerWorker.PerformShiftEnd();

            // Assert
            result.Should().BeNull();
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).Received(1).RequestShiftEnd(Arg.Any<IMessageTracking>());

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.TransactionsChanged);
        }

        [Fact]
        public void test_perform_day_end()
        {
            // Arrange

            // Act
            string result = _controllerWorker.PerformDayEnd();

            // Assert
            result.Should().BeNull();
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).Received(1).RequestDayEnd(Arg.Any<IMessageTracking>());

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.TransactionsChanged);
        }

        [Fact]
        public void test_remove_file()
        {
            // Arrange
            const string fileName = "A File";

            // Act
            string result = _controllerWorker.RemoveFile(fileName);

            // Assert
            result.Should().BeNull();
            _controllerWorker.UploadedFileNames.Should().NotContain(fileName);

            _pushChangeDelegate.Received(1).Invoke(EventType.AboutChanged);
        }

        [Fact]
        public void test_set_max_fill_override_for_fuel_cards()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetMaxFillOverrideForFuelCards(PumpOne, true);

            // Assert
            result.Should().BeNull();
            _pumpOne.Received(1).SetMaxFillOverrideForFuelCards(true);
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "1");
        }

        [Fact]
        public void test_set_max_fill_override_for_payment_cards()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetMaxFillOverrideForPaymentCards(PumpOne, true);

            // Assert
            result.Should().BeNull();
            _pumpOne.Received(1).SetMaxFillOverrideForPaymentCards(true);
            _optWorker.Received(1).CheckOptConfig(_optOne);

            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, "1");
        }

        [Fact]
        public void test_set_grade_price()
        {
            // Arrange
            const byte grade = 1;
            const int price = 1000;

            // Act
            bool result = _controllerWorker.SetGradePrice(grade, price).IsSuccess;

            // Assert
            result.Should().BeTrue();
            File.Exists(FuelDataUpdateFile).Should().BeTrue();
            _controllerWorker.GradePriceToSet[grade].Should().Be(price);

            _pushChangeDelegate.Received(1).Invoke(EventType.FuelPriceChanged, grade.ToString());
        }

        [Fact]
        public void test_set_grade_prices()
        {
            // Arrange
            const byte grade1 = 1;
            const int price1 = 1000;
            const byte grade2 = 2;
            const int price2 = 2000;

            // Act
            bool result = _controllerWorker.SetGradePrices(new List<FuelPriceItem>
                {new FuelPriceItem {Fuel = grade1, Ppu = price1}, new FuelPriceItem {Fuel = grade2, Ppu = price2}}).IsSuccess;

            // Assert
            result.Should().BeTrue();
            File.Exists(FuelDataUpdateFile).Should().BeTrue();
            _controllerWorker.GradePriceToSet[grade1].Should().Be(price1);
            _controllerWorker.GradePriceToSet[grade2].Should().Be(price2);

            _pushChangeDelegate.Received(1).Invoke(EventType.FuelPriceChanged, null);
        }

        [Fact]
        public void test_set_forward_fuel_price_update()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetForwardFuelPriceUpdate(true);

            // Assert
            result.Should().BeNull();
            _updateWorker.Received(1).SetForwardFuelPriceUpdate(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
        }

        [Fact]
        public void test_set_media_channel()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetMediaChannel(true);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetMediaChannel(true);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.MediaChannel.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.DivertDetailsChanged, null);
            _pushChangeDelegate.Received(5).Invoke(Arg.Any<EventType>(), Arg.Any<string>());
        }

        [Fact]
        public void test_set_unmanned_pseudo_pos()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetUnmannedPseudoPos(true);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetUnmannedPseudoPos(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.OPTChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged);
            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged);
        }

        [Fact]
        public void test_set_asda_day_end_report()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetAsdaDayEndReport(true);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetAsdaDayEndReport(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.AdvancedConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
        }

        [Fact]
        public void test_set_playlist_filename()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetPlaylistFileName(OptIdString, PlaylistFileName);

            // Assert
            result.Should().BeNull();
            _optTwo.Received(1).SetPlaylistFileName(PlaylistFileName);
            _optTwo.Received(1).MediaUpdateCheckRequired();
            _hydraDb.Received(1).SetPlaylistFileName(OptIdString, PlaylistFileName);
            _optWorker.Received(1).CheckOptConfig(_optTwo);

            _pushChangeDelegate.Received().Invoke(EventType.OPTChanged, OptIdString);
        }

        [Fact]
        public void test_set_log_interval()
        {
            // Arrange
            const int interval = 100;

            // Act
            string result = _controllerWorker.SetLogInterval(interval);

            // Assert
            result.Should().BeNull();
            _allOpts.Received(1).SetLogInterval(interval);

            _pushChangeDelegate.Received(1).Invoke(EventType.ShiftEndChanged);
        }

        [Fact]
        public void test_restart_opt()
        {
            // Arrange

            // Act
            string result = _controllerWorker.RestartOpt(OptIdString);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).RestartOpt(OptIdString);
        }

        [Fact]
        public void test_set_tank_gauge()
        {
            // Arrange

            // Act
            var result = _controllerWorker.SetTankGaugeAddress(IPAddress.Parse(TankGaugeIpAddress), TankGaugePort);

            // Assert
            result.Should().BeNull();
            _hydraDb.SetTankGaugeEndPoint(IPAddress.Parse(TankGaugeIpAddress), TankGaugePort);
            _tankGaugeWorker.Received(1).Restart();

            _pushChangeDelegate.Received(1).Invoke(EventType.ConnectionChanged, null);
        }

        [Fact]
        public void test_set_tariff_mappings()
        {
            // Arrange
            const int grade1 = 1;
            const string productCode1 = "code one";
            const int grade2 = 2;
            const string productCode2 = "code two";

            // Act
            string result1 = _controllerWorker.SetTariffMappings(new List<TariffMapping>
                {new TariffMapping(grade1, productCode1, true), new TariffMapping(grade1, productCode1, true)});
            string result2 = _controllerWorker.SetTariffMappings(new List<TariffMapping>
                {new TariffMapping(grade1, productCode1, true), new TariffMapping(grade1, productCode2, true)});
            string result3 = _controllerWorker.SetTariffMappings(new List<TariffMapping>
                {new TariffMapping(grade1, productCode1, true), new TariffMapping(grade2, productCode1, true)});
            string result4 = _controllerWorker.SetTariffMappings(new List<TariffMapping>
                {new TariffMapping(grade1, productCode1, true), new TariffMapping(grade2, productCode2, true)});

            // Assert
            result1.Should().Be("Duplicate mapping");
            result2.Should().Be("Duplicate grade");
            result3.Should().Be("Duplicate product code");
            result4.Should().BeNull();
            _hydraDb.ReceivedWithAnyArgs(1).SetTariffMappings(null);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_tariff_mapping_fuel_cards_only()
        {
            // Arrange
            const int grade = 1;

            // Act
            string result = _controllerWorker.SetTariffMappingFuelCardsOnly(grade, true);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetTariffMappingFuelCardsOnly(grade, true);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_esocket_connection_string()
        {
            // Arrange
            const string connectionString = "Connection";

            // Act
            string result = _controllerWorker.SetEsocketConnectionString(connectionString);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetConnectionString(connectionString);
            _hydraDb.Received(1).SetEsocketConnectionString(connectionString);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_use_connection_string()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetEsocketUseConnectionString(true);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetUseConnectionString(true);
            _hydraDb.Received(1).SetEsocketUseConnectionString(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_config_file()
        {
            // Arrange
            const string configFile = "Config File";

            // Act
            string result = _controllerWorker.SetEsocketConfigFile(configFile);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetConfigFile(configFile);
            _hydraDb.Received(1).SetEsocketConfigFile(configFile);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_keystore_file()
        {
            // Arrange
            const string keystoreFile = "Keystore File";

            // Act
            string result = _controllerWorker.SetEsocketKeystoreFile(keystoreFile);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetKeystoreFile(keystoreFile);
            _hydraDb.Received(1).SetEsocketKeystoreFile(keystoreFile);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_db_url()
        {
            // Arrange
            const string dbUrl = "URL";

            // Act
            string result = _controllerWorker.SetEsocketDbUrl(dbUrl);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetDbUrl(dbUrl);
            _hydraDb.Received(1).SetEsocketDbUrl(dbUrl);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_override_properties()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetEsocketOverrideProperties(true);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetOverrideProperties(true);
            _hydraDb.Received(1).SetEsocketOverrideProperties(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_override_keystore()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetEsocketOverrideKeystore(true);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetOverrideKeystore(true);
            _hydraDb.Received(1).SetEsocketOverrideKeystore(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_override_url()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetEsocketOverrideUrl(true);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetOverrideUrl(true);
            _hydraDb.Received(1).SetEsocketOverrideUrl(true);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, null);
        }

        [Fact]
        public void test_set_esocket_override_contactless()
        {
            // Arrange

            // Act
            string result = _controllerWorker.SetEsocketOverrideContactless(true);

            // Assert
            result.Should().BeNull();
            _paymentConfig.Received(1).SetOverrideContactless(true);
            _hydraDb.Received(1).SetEsocketOverrideContactless(true);
            _configUpdateWorker.Received(1).SetContactlessPropertiesFile(ContactlessPropertiesFile);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.EsocketOverrideContactless.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_layout_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetLayoutDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetLayoutDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.LayoutDirectory.ToString());
        }

        [Fact]
        public void test_set_media_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetMediaDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetMediaDirectory(expectedDirectory);
            _optWorker.Received(1).FetchMediaFilesList();

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.MediaDirectory.ToString());
        }

        [Fact]
        public void test_set_playlist_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetPlaylistDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetPlaylistDirectory(expectedDirectory);
            _optWorker.Received(1).FetchPlaylistFilesList();

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.PlaylistDirectory.ToString());
        }

        [Fact]
        public void test_set_opt_log_file_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetOptLogFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _optWorker.Received(1).SetOptLogFileDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.OptLogFileDirectory.ToString());
        }

        [Fact]
        public void test_set_log_file_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetLogFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.LogFileDirectory.Should().Be(expectedDirectory);
            _hydraDb.Received(1).SetLogFileDirectory(expectedDirectory);
            _configUpdateWorker.Received(1).SetLogDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.LogFileDirectory.ToString());
        }

        [Fact]
        public void test_set_trace_file_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetTraceFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.TraceFileDirectory.Should().Be(expectedDirectory);
            _hydraDb.Received(1).SetTraceFileDirectory(expectedDirectory);
            _configUpdateWorker.Received(1).SetTracesDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.TraceFileDirectory.ToString());
        }

        [Fact]
        public void test_set_journal_file_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";
            const string expectedFile = expectedDirectory + "UPosJournal.txt";

            // Act
            string result = _controllerWorker.SetJournalFileDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.JournalFileDirectory.Should().Be(expectedDirectory);
            _hydraDb.Received(1).SetJournalFileDirectory(expectedDirectory);
            _configUpdateWorker.Received(1).SetJournalDirectory(expectedDirectory);
            _journalWorker.Received(1).SetUnmannedJournalFile(expectedFile);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.JournalFileDirectory.ToString());
        }

        [Fact]
        public void test_set_received_update_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetReceivedUpdateDirectory(directory);

            // Assert
            result.Should().BeNull();
            _updateWorker.Received(1).SetReceivedUpdateDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.ReceivedUpdateDirectory.ToString());
        }

        [Fact]
        public void test_set_database_backup_directory()
        {
            // Arrange
            const string directory = "Directory";
            const string expectedDirectory = directory + "\\";

            // Act
            string result = _controllerWorker.SetDatabaseBackupDirectory(directory);

            // Assert
            result.Should().BeNull();
            _controllerWorker.DatabaseBackupDirectory.Should().Be(expectedDirectory);
            _hydraDb.Received(1).SetDatabaseBackupDirectory(expectedDirectory);
            _configUpdateWorker.Received(1).SetDatabaseBackupDirectory(expectedDirectory);

            _pushChangeDelegate.Received(1).Invoke(EventType.FileLocationsChanged, EventItem.DatabaseBackupDirectory.ToString());
            _pushChangeDelegate.Received(1).Invoke(EventType.AboutChanged);
        }

        [Fact]
        public void test_set_currency_code()
        {
            // Arrange
            const int code = 333;

            // Act
            string result = _controllerWorker.SetCurrencyCode(code);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetCurrencyCode(code);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_max_fill_override()
        {
            // Arrange
            const uint maxFill = 222;

            // Act
            string result = _controllerWorker.SetMaxFillOverride(maxFill);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).SetMaxFillOverride(maxFill);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
            _pushChangeDelegate.Received(1).Invoke(EventType.PumpChanged, null);
        }

        [Fact]
        public void test_add_discount_card()
        {
            // Arrange
            const string iin = "IIN";
            const string name = "Name";
            const string type = "Type";
            const float value = 1.2F;
            const byte grade = 3;

            // Act
            string result = _controllerWorker.AddDiscountCard(iin, name, type, value, grade);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).AddDiscountCard(iin, name, type, value, grade);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_remove_discount_card()
        {
            // Arrange
            const string iin = "IIN";

            // Act
            string result = _controllerWorker.RemoveDiscountCard(iin);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).RemoveDiscountCard(iin);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_add_discount_whitelist()
        {
            // Arrange
            const string iin = "IIN";
            const string pan = "PAN";

            // Act
            string result = _controllerWorker.AddDiscountWhitelist(iin, pan);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).AddDiscountWhitelist(iin, pan);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_remove_discount_whitelist()
        {
            // Arrange
            const string iin = "IIN";
            const string pan = "PAN";

            // Act
            string result = _controllerWorker.RemoveDiscountWhitelist(iin, pan);

            // Assert
            result.Should().BeNull();
            _hydraDb.Received(1).RemoveDiscountWhitelist(iin, pan);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_remove_local_account_customer()
        {
            // Arrange
            const string reference = "ref";

            // Act
            string result = _controllerWorker.RemoveLocalAccountCustomer(reference);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).Delete(reference);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_add_local_account_customer()
        {
            // Arrange
            const string reference = "ref";
            const string name = "Name";
            const uint limit = 100;

            // Act
            string result = _controllerWorker.AddLocalAccountCustomer(reference, name, true, limit, true, true, true, true, true, true,
                true, true, true);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).Add(reference, name, true, limit, true, true, true, true, true, true);
            _localAccountWorker.Received(1).SetFlags(reference, true, true, true);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_local_account_customer_balance()
        {
            // Arrange
            const string reference = "ref";
            const uint balance = 100;

            // Act
            string result = _controllerWorker.SetLocalAccountCustomerBalance(reference, balance);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).Balance(reference, balance);
        }

        [Fact]
        public void test_add_local_account_card_without_restrictions()
        {
            // Arrange
            const string reference = "ref";
            const string pan = "PAN";
            const string description = "A description";
            const float discount = 2.4f;

            // Act
            string result = _controllerWorker.AddLocalAccountCardWithoutRestrictions(reference, pan, description, discount);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).AddCardWithoutRestrictions(pan, reference, description, discount);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_add_local_account_card_with_restrictions()
        {
            // Arrange
            const string reference = "ref";
            const string pan = "PAN";
            const string description = "A description";
            const float discount = 2.4f;

            // Act
            string result = _controllerWorker.AddLocalAccountCardWithRestrictions(reference, pan, description, discount, true, true, true,
                true, true, true, true, true, true, true, true, true, true, true);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).AddCardWithRestrictions(pan, reference, description, discount, true, true, true, true, true,
                true, true, true, true, true, true, true, true, true);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }

        [Fact]
        public void test_set_local_account_card_hot()
        {
            // Arrange
            const string pan = "PAN";

            // Act
            string result = _controllerWorker.SetLocalAccountCardHot(pan, true);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).HotCard(pan);
        }

        [Fact]
        public void test_set_local_account_card_not_hot()
        {
            // Arrange
            const string pan = "PAN";

            // Act
            string result = _controllerWorker.SetLocalAccountCardHot(pan, false);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).OkCard(pan);
        }

        [Fact]
        public void test_remove_local_account_card()
        {
            // Arrange
            const string pan = "PAN";

            // Act
            string result = _controllerWorker.RemoveLocalAccountCard(pan);

            // Assert
            result.Should().BeNull();
            _localAccountWorker.Received(1).DeleteCard(pan);

            _pushChangeDelegate.Received(1).Invoke(EventType.GenericOptConfigChanged, null);
        }
    }
}
