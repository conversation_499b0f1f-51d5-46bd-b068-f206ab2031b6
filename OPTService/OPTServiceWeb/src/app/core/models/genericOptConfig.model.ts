export class GenericOptConfig {
  ServiceAddress: string;
  ProcCategory: string;
  TermCategory: string;
  Timestamp: string;

  ContactlessDetails: any;
  ReceiptLayoutMode: any;
  
  CardAids: any[];
  CardClessAids: any[];
  CardClessDrls: any[];
  CardCapks: any[];
  CoockedCardCapks: any[];
  DiscountCards: any[];
  EsocketEndPoints: any[];
  FuelCards: any[];
  LocalAccounts: any[];
  Loyalty: any[];
  PredefinedAmounts: any[];
  TariffMappings: any[];
  Washes: any[];
}