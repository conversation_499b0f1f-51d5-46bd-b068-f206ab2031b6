namespace OPT.Common.HydraDbClasses
{
    public class DivertOpt
    {
        public bool IsDiverted { get; }
        public string IpAddress { get; }
        public int FromOptPort { get; }
        public int ToOptPort { get; }
        public int HeartbeatPort { get; }
        public int MediaChannelPort { get; }

        public DivertOpt
        (bool isDiverted = false, string ipAddress = "127.0.0.1", int fromOptPort = 1262, int toOptPort = 1263,
            int heartbeatPort = 1264, int mediaChannelPort = 1266)
        {
            IsDiverted = isDiverted;
            IpAddress = ipAddress;
            FromOptPort = fromOptPort;
            ToOptPort = toOptPort;
            HeartbeatPort = heartbeatPort;
            MediaChannelPort = mediaChannelPort;
        }
    }
}