using CSharpFunctionalExtensions;
using CsvHelper;
using Forecourt.Pump.Controllers.Interfaces;
using HSC;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Hydra.Core.Pump.Messages.HydraFdc;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.Workers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Abstractions;
using System.Linq;
using System.Net;
using CoreMeterReadings = Htec.Hydra.Core.Pump.Messages.MeterReadings;
using CorePumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using HscPumpCommand = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpCommand;

namespace Forecourt.Pump.Controllers
{
    /// <summary>
    /// Abstracts the pump commands issued to HydraFDC (via HSC) away from PumpWorker
    /// </summary>
    public class HydraPumpController : CoreHscWorker<HydraDb.Interfaces.IHydraDb>, IHydraPumpController
    {
        private readonly IFileSystem _fileSystem;

        /// <summary>Constructor for HSCWorker.</summary>
        /// <param name="siteController">Htec Site Controller to use.</param>
        /// <param name="fileSystem">IFileSystem to use</param>
        /// <param name="hydraDb">Hydra DB instance to use.</param>
        /// <param name="logMan">Htec log manager to use.</param>
        /// <param name="configurationManager">The configuration manager.</param>
        /// <param name="timerFactory">ITimerFactory instance</param>
        /// <param name="fileVersionInfoHelper">IFileVersionInfoHelper instance</param>
        public HydraPumpController(IHtecLogManager logMan, IConfigurationManager configurationManager, HydraDb.Interfaces.IHydraDb hydraDb, ISiteController siteController,
            IFileSystem fileSystem, ITimerFactory timerFactory, IFileVersionInfoHelper fileVersionInfoHelper)
            : base(siteController, hydraDb, logMan, fileVersionInfoHelper, configurationManager, nameof(HydraPumpController), 0, timerFactory)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));

            hydraDb.SetDomsEnabled(false);
            IsEnabled = true;
        }

        /// <inheritdoc/>
        string IPumpControllerConfiguration.LogHeader => "HSC";

        /// <inheritdoc/>
        bool IPumpControllerConfiguration.GotMaxPumps => GotMaxPumps;

        /// <inheritdoc/>
        byte IPumpControllerConfiguration.NumberOfPumps => (byte)NumberOfPumps;

        /// <inheritdoc/>
        GenericEndPoint IPumpControllerConfiguration.CurrentEndPoint => CurrentEndPoint;

        /// <inheritdoc/>
        string IPumpControllerConfiguration.LogonInfo=> string.Empty;
        
        /// <inheritdoc/>
        public Result AuthWithLimit(byte pump, uint limit, byte pos, string loggingReference)
        {
            var command = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.AuthWithLimit,
                Pump = pump,
                Cash = limit
            };
            SiteController.Command(command);
            SiteController.OptClaimPump(pump);

            return Result.Success();
        }

        /// <inheritdoc/>
        public Result Cashout(byte pump, byte pos, byte tran, string loggingReference)
        {
            var command = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = tran
            };
            SiteController.Command(command);
            SiteController.OptReleasePump(pump);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result Claim(byte pump, byte pos, byte tran, string loggingReference)
        {
            var command = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = tran
            };
            SiteController.Command(command);
            return Result.Success();
        }

        /// <inheritdoc/>
        public void EmergencyStop(byte pump, string reg, string loggingReference)
        {
            SiteController.ANPR_Stop(pump, reg);
        }

        /// <inheritdoc/>
        public void EmergencyStopUpdate(byte pump, string reg, string loggingReference)
        {
            SiteController.ANPR_Update(pump, reg);
        }

        /// <inheritdoc/>
        public void EmergencyStopCancel(byte pump, string loggingReference)
        {
            // "EmergencyStopCancel not applicable for HSC"
        }

        /// <inheritdoc/>
        public Result Release(byte pump, byte pos, byte tran, string loggingReference)
        {
            var command = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Release,
                Pump = pump,
                Pos = pos,
                TranNum = tran
            };
            SiteController.Command(command);
            SiteController.OptReleasePump(pump);

            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestMeters(DateTime? now = null, string loggingReference = null)
        {
            if (now == null)
            {
                FetchMeters(loggingReference);
            }
            else
            {
                base.RequestMeters(now.Value);
            }
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestMaxPumps(string loggingReference = null)
        {
            SiteController.RequestMaxPumps();
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestPrices(string loggingReference = null)
        {
            SiteController.Command(new HscPumpCommand { Command = (byte)PumpCommandType.NewPrices });
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result SetPrices(IEnumerable<Htec.Hydra.Core.Pump.Messages.Grade> gradePrices, string loggingReference = null)
        {
            return DoAction(() => {

                var fileName = HydraDb.GetFileLocations().FuelDataUpdateFile;
                _fileSystem.Directory.Exists(_fileSystem.Path.GetDirectoryName(fileName));

                try
                {
                    using (var writer = new StreamWriter(fileName, true))
                    {
                        using (var csv = new CsvWriter(writer, CultureInfo.CurrentCulture))
                        {
                            foreach (var item in gradePrices)
                            {                                
                                csv.WriteRecord(new FuelUpdateItem(item.Id, Convert.ToInt32(item.Price)));
                                csv.NextRecord();
                            }
                        }
                    }

                    //return RequestPrices(LoggingReference); // TODO: This is ADO #464908
                    return Result.Success();
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                    return Result.Failure(ex.Message);
                }
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RequestPumpData(byte pump, string loggingReference)
        {
            SiteController.RequestPumpData(pump);
            return Result.Success();
        }

        /// <inheritdoc/>
        public void Stop(byte pump, string loggingReference)
        {
            var command = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Stop,
                Pump = pump
            };

            SiteController.Command(command);
            SiteController.OptReleasePump(pump);
        }

        /// <inheritdoc/>
        protected override void DoOnPumpData(PumpData6 pumpData)
        {
            base.DoOnPumpData(pumpData);

            GetWorker<IPumpControllerCallbacks>()?.OnPumpData((CorePumpData)pumpData);
        }

        /// <inheritdoc/>
        protected override void DoOnMeterReadings(MeterReadings6 readings)
        {
            base.DoOnMeterReadings(readings);

            var stillPending = _pumpMeters.Values.Any(x => x.AreReadingsPending);
            if (stillPending)
            {
                return;
            }

            GetWorker<IPumpControllerCallbacks>()?.OnMeterReadings(_pumpMeters.Values.Select(x =>
            {
                var coreReadings = (CoreMeterReadings)x.Readings;
                return coreReadings;
            }).ToList(), LoggingReference);
        }

        /// <inheritdoc/>
        protected override void DoOnMaxPumpsResponse(int numberOfPumps, string loggingReference)
        {
            base.DoOnMaxPumpsResponse(numberOfPumps, loggingReference);

            GetWorker<IPumpControllerCallbacks>()?.OnMaxPumps(numberOfPumps, loggingReference);
        }

        /// <inheritdoc/>
        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            base.DoOnConnected(ipAddress, port);

            GetWorker<IPumpControllerCallbacks>()?.OnConnected(ipAddress, port);
        }

        /// <inheritdoc/>
        protected override void DoOnDisconnected(int? id = null)
        {
            base.DoOnDisconnected(id);

            GetWorker<IPumpControllerCallbacks>()?.OnDisconnected(id);
        }

        /// <inheritdoc/>
        public uint ToAmount(decimal amount) => amount.ToUInt();

        /// <inheritdoc/>
        public ushort ToPrice(decimal price) => price.ToUShort();

        /// <inheritdoc/>
        public uint ToVolume(decimal volume) => volume.ToUInt();

        /// <inheritdoc/>
        public void OpenPump(byte pump, string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void ClosePump(byte pump, string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForDisconnection(string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForLogin(byte pos, string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForMasterReset(string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForRequestPumpData(string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForRequestForecourtData(FetchFcRequestType fcRequestType, string loggingReference)
        {
        }

        /// <inheritdoc/>
        public void CheckForRequestPumpData(byte fpId, FetchFpRequestType fpRequestType, string loggingReference)
        {
        }

        /// <inheritdoc/>
        public Result SetIpAddress(IPAddress ipAddress, int port = 0, string loggingReference = null)
        {
            HydraDb.SetPumpEndPoint(ipAddress, port == 0 ? CurrentEndPoint.Port : port);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result SetLogonInfo(string logonInfo, string loggingReference = null)
        {
            return Result.Failure("n/a");
        }

        /// <inheritdoc/>
        public void Reconnect(string loggingReference = null)
        {
        }

        /// <inheritdoc/>
        public void Reset(string loggingReference = null)
        {
        }

        /// <inheritdoc/>
        public void MasterReset(string loggingReference = null)
        {
        }

        /// <inheritdoc/>
        public Result ReserveWithLimit(byte pump, uint limit, byte pos, string loggingReference)
        {
            SiteController.OptClaimPump(pump);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RemoveAuth(byte pump, byte pos, string loggingReference)
        {
            SiteController.OptReleasePump(pump);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RemoveReserve(byte pump, byte pos, string loggingReference)
        {
            SiteController.OptReleasePump(pump);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result CheckForStuckSales((bool, bool) checkLevels, byte pump, bool isDeliveredPending, int transSeqNum, uint amount, uint volume, byte grade, byte pos, string loggingReference)
        {
            // TODO:
            return Result.Failure("Not implemented");
        }

        /// <inheritdoc/>
        public Result StartCashOut(byte pump, byte tran, int tranSeqNum, uint amount, bool isZeroPaid, string loggingReference)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result PaymentClearedOrCancelledAcknowledged(byte pump, int transSeqNum, string loggingReference)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result SetPumpHoseGradePrice(byte pump, byte hose, byte grade, uint price, string name)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public bool ShouldSynchroniseGrades => false;
    }
}
