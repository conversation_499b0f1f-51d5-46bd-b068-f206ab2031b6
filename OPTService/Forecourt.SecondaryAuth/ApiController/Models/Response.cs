using Newtonsoft.Json;
using System.Xml.Serialization;

namespace Forecourt.SecondaryAuth.ApiController.Models
{
    /// <summary>
    /// Model (body) for all SecAuth responses
    /// </summary>
    [XmlRoot(ElementName = "Response")]
    public class Response
    {
        /// <summary>
        /// Unique Transaction id, in Guid format
        /// </summary>
        [JsonProperty("transactionId")]
        [XmlAttribute("TransactionId")]
        public string TransactionId { get; set; }

        /// <summary>
        /// Unique message id, in Guid format
        /// </summary>
        [JsonProperty("messageId")]
        [XmlAttribute("MessageId")]
        public string MessageId { get; set; }

        /// <summary>
        /// Any vehicle registration available
        /// </summary>
        [JsonProperty("vehicleRegistration")]
        [XmlAttribute("VehicleRegistration")]
        public string VehicleRegistration { get; set; }

        /// <summary>
        /// Has authorisation been given
        /// </summary>
        [JsonProperty("isAuthorised")]
        [XmlAttribute("IsAuthorised")]
        public bool IsAuthorised { get; set; }
    }
}
