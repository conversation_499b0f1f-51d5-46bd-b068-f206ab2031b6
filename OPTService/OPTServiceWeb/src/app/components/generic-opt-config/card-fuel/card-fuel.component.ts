import { Component, Input } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';

@Component({
  selector: 'app-card-fuel',
  templateUrl: './card-fuel.component.html',
  styleUrls: ['./card-fuel.component.css']
})
export class CardFuelComponent {

  @Input() form: FormGroup;
  @Input() formArrayName: string;

  /**
   * The getter for the card AIDs
   */
  get fuelCards(): FormArray {
    return this.form?.get(this.formArrayName) as FormArray;
  }
}
