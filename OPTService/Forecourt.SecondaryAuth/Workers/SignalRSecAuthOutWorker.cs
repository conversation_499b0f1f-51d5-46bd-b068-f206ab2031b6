using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <summary>
    /// Implements <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, by issuing SignalR notifications
    /// </summary>
    public abstract class SignalRSecAuthOutWorker: NoneSecAuthOutWorker
    {
        /// <summary>
        /// SignalR Hub
        /// </summary>
        protected readonly IHubbable<SecAuthMessage, IMessageTracking> HubContext;

        /// <inheritdoc/>
        protected SignalRSecAuthOutWorker(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker,
            IHubbable<SecAuthMessage, IMessageTracking> hubContext): base(logManager, loggerName, configurationManager, allPumps, telemetryWorker)
        {
            HubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
        }

        /// <summary>
        /// Template the SignalR SecAuth processing
        /// </summary>
        /// <param name="pump">The pump</param>
        /// <param name="tracking">Current message tracking instance</param>
        /// <param name="header">Logging header</param>
        /// <param name="context">SignalR notification</param>
        /// <param name="message">Message to send</param>
        /// <param name="action">Action to do the SignalR notification</param>
        protected Result<string> DoProcessingTemplate(IPump pump, IMessageTracking tracking, string header, string context, SecAuthMessage message, Action<dynamic, SecAuthMessage> action)
        {
            var logRef = tracking.IdAsString;
            message.MessageId = logRef;
            DoDeferredLogging(LogLevel.Info, $"{ConfigConstants.HeaderSignalR}.{header}Request", () => new[] { $"Pump: {pump.Number}; OptId: {pump.Opt?.IdString}" }, reference: tracking.FullId);
            HubContext.Publish(context, message, action, tracking);
            return Result.Success(header);
        }

        /// <inheritdoc/>
        protected override bool DoIsConnected() => HubContext.IsConnected();

        /// <inheritdoc/>
        protected override int DoGetConnectedCount() => HubContext.ConnectedCount;

        /// <inheritdoc/>
        protected override IEnumerable<IPAddress> DoGetAllIpAddresses() => HubContext.GetAllIpAddresses();

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            HubContext.RegisterWorker(GetWorker<Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<string>>());
            return base.DoStart(startParams);
        }

        /// <inheritdoc/>
        public override Result RequestTimedOutRequest(SecAuthRequest request, IMessageTracking message)
        {
            return DoProcessRequest(request, message, (pump) => DoProcessingTemplate(pump, message, "TimedOut", 
                "requestTimedOutRequest", request, (all, pl) => all.requestTimedOutRequest(pl)), false);
        }
    }

    /// <summary>
    /// Implements <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, by issuing SignalR notifications, for PreAuth
    /// </summary>
    public class SignalRPreSecAuthOutWorker : SignalRSecAuthOutWorker, ISignalRPreSecAuthOutWorker
    {
        /// <inheritdoc/>
        public SignalRPreSecAuthOutWorker(IHtecLogManager logManager,IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker, IHubbable<SecAuthMessage, IMessageTracking> hubContext)
            : base(logManager, nameof(SignalRPreSecAuthOutWorker), configurationManager, allPumps, telemetryWorker, hubContext)
        {
        }

        /// <inheritdoc/>
        public override Result PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Failure($"{FailureReasonPrefix}{HeaderPostAuth}");
        }

        /// <inheritdoc/>
        public override Result PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return DoProcessRequest(request, message, (pump) => DoProcessingTemplate(pump, message, HeaderPreAuth, "preAuthRequest", request, (all, pl) => all.preAuthRequest(pl)));
        }
    }

    /// <summary>
    /// Implements <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, by issuing SignalR notifications, for PostAuth
    /// </summary>
    public class SignalRPostSecAuthOutWorker : SignalRSecAuthOutWorker, ISignalRPostSecAuthOutWorker
    {
        /// <inheritdoc/>
        public SignalRPostSecAuthOutWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker, IHubbable<SecAuthMessage, IMessageTracking> hubContext)
            : base(logManager, nameof(SignalRPostSecAuthOutWorker), configurationManager, allPumps, telemetryWorker, hubContext)
        {
        }

        /// <inheritdoc/>
        public override Result PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return DoProcessRequest(request, message, (pump) => DoProcessingTemplate(pump, message, HeaderPostAuth, "postAuthRequest", request, (all, pl) => all.postAuthRequest(pl)));
        }

        /// <inheritdoc/>
        public override Result PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Failure($"{FailureReasonPrefix}{HeaderPreAuth}");
        }
    }
}
