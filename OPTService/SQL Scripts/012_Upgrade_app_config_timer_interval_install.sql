USE [Hydra]

PRINT N'HOPT-1628 - OPT Service - move Timer Interval config from GENERAL/CONNECTIVITY to TIMERS Install...Begin';

IF NOT EXISTS (SELECT * FROM [dbo].[ConfigurationCategory] WHERE [Category] = 'TIMERS')
BEGIN
	PRINT N'Updating Configuration Categories...';

	INSERT INTO [dbo].[ConfigurationCategory] (Category, Description)
	VALUES ('TIMERS', 'All timer related options');
END;

DECLARE @idCat INT = (SELECT ID FROM [dbo].[ConfigurationCategory] WHERE Category='CONNECTIVITY');
IF EXISTS (SELECT * FROM [dbo].[ConfigurationDetail] WHERE [Key] LIKE 'Timer:Interval:%' AND [CategoryId] = @idCat)
BEGIN
	PRINT N'Removing erroneous config item in CONNECTIVITY';
	DELETE [dbo].[ConfigurationDetail] WHERE [Key] LIKE 'Timer:Interval:%' AND [CategoryId] = @idCat;
END

DECLARE @idCatTimers INT = (SELECT TOP 1 ID FROM [dbo].[ConfigurationCategory] WHERE Category='TIMERS');

IF EXISTS (SELECT * FROM [dbo].[ConfigurationDetail] cd JOIN [dbo].[ConfigurationCategory] cc ON cc.Id = cd.CategoryId AND cc.Category = 'TIMERS' AND cc.Id <> @idCatTimers)
BEGIN
	PRINT N'Removing existing TIMERS Configuration Details...';

	DELETE cd 
	FROM [dbo].[ConfigurationDetail] cd 
		JOIN [dbo].[ConfigurationCategory] cc ON cc.Id = cd.CategoryId AND cc.Category = 'TIMERS' AND cc.Id <> @idCatTimers;
END

IF EXISTS (SELECT * FROM [dbo].[ConfigurationCategory] WHERE [Category] = 'TIMERS' AND [Id] <> @idCatTimers)
BEGIN
	PRINT N'Removing duplicate Configuration Categories...';
	DELETE [dbo].[ConfigurationCategory] WHERE [Category] = 'TIMERS' AND [Id] <> @idCatTimers;
END

IF EXISTS (SELECT * FROM [dbo].[ConfigurationDetail] WHERE [Key] LIKE 'Timer:Interval:%' AND CategoryId <> @idCatTimers)
BEGIN
	PRINT N'Re-locating existing CONNECTIVITY Configuration Details to TIMERS...';	
	UPDATE [dbo].[ConfigurationDetail] SET CategoryId = @idCatTimers WHERE [Key] LIKE 'Timer:Interval:%';
END

PRINT N'HOPT-1628 - OPT Service - move Timer Interval config from GENERAL/CONNECTIVITY to TIMERS Install...Begin';
PRINT N'';

GO