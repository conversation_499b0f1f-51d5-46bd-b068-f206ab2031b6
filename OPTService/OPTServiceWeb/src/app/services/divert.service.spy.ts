import { TestBed } from '@angular/core/testing';
import { DivertService } from './divert.service';

export function DIVERT_SERVICE_PROVIDER(): { provide: typeof DivertService, useValue: any } {
  return {
    provide: DivertService,
    useValue: jasmine.createSpyObj('DivertService', ['getDivertDetails']),
  };
}

export function DIVERT_SERVICE_SPY(): jasmine.SpyObj<DivertService> {
  return TestBed.inject(DivertService) as jasmine.SpyObj<DivertService>;
}