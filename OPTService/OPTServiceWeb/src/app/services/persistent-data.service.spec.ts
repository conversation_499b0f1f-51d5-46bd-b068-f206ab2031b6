import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { of } from 'rxjs';

import { PersistentDataService } from './persistent-data.service';
import { PumpService } from './pump.service';
import { SignalRService } from './signal-r.service';

describe('PersistentDataService', () => {
  let service: PersistentDataService;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let pumpRServiceSpy: jasmine.SpyObj<PumpService>;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['startConnection', 'getOptSignalRMessage', 'getPumpSignalRMessage', 'getFileLocationSignalRMessage', 'getAdvancedConfigSignalRMessage', 'getConnectionStatushChangeMessage', 'getGenericOptConfigSignalRMessage']);
    const pumpSpy = jasmine.createSpyObj('PumpService', ['getTids', 'getPumps', 'getMaxFillOverride']);


    TestBed.configureTestingModule({
      providers: [
        PersistentDataService,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: SignalRService, useValue: signalRSpy },
        { provide: PumpService, useValue: pumpSpy}
      ]
    });

    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    signalRServiceSpy.getOptSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getGenericOptConfigSignalRMessage.and.returnValue(of());

    pumpRServiceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    pumpRServiceSpy.getTids.and.returnValue(of());
    pumpRServiceSpy.getMaxFillOverride.and.returnValue(of());

    service = TestBed.inject(PersistentDataService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
