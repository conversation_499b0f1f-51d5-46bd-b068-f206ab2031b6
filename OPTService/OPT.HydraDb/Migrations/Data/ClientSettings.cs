using FluentMigrator;
using Htec.Common.Extensions;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Configuration;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to add client specific settings, based on config
    /// </summary>
    [Migration(4, "Updating HydraDb for initial client config"), Tags(Constants.MigrationTypeData)]
    public class ClientSettings: Migrationable
    {
        /// <summary>
        /// Default value for, Morrisons client
        /// </summary>
        public const string DefaultValueClient = "morrisons";

        /// <summary>
        /// Client name constant for, MADIC
        /// </summary>
        public const string ClientNameMADIC = "madic";

        /// <summary>
        /// Client name constant for, ORBIS
        /// </summary>
        public const string ClientNameORBIS = "orbis";

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">ILogger instance</param>
        public ClientSettings(ILogger<Migrationable> logger) : base(logger)
        {
        }

        public override void Up()
        {
            // This will be the ONLY config in the app.config (so I'm going direct!)
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultValueClient, null);

            Logger.LogInformation("Updating HydraDb for initial client config, for: {0}", client);

            switch (client.ToLower())
            {
                case DefaultValueClient:
                case ClientNameMADIC:
                case ClientNameORBIS:
                    Execute.EmbeddedScript($"{EmbeddedResourcePathData}.MorrisonsSettings.sql");
                    break;

                case "asda":
                    Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ASDASettings.sql");
                    break;

                default:
                    Execute.EmbeddedScript($"{EmbeddedResourcePathData}.IndiSettings.sql");
                    break;
            };
        }

        public override void Down()
        {
            
        }
    }
}
