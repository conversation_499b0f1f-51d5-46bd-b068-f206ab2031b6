using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System.Runtime.CompilerServices;

namespace OPTService
{
    internal class ServiceLoggable : Loggable
    {
        internal ServiceLoggable(IHtecLogger logger, IConfigurationManager configurationManager = null, string typeName = null)
            : base(logger, configurationManager, typeName)
        {
        }

        internal ServiceLoggable(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager = null, string typeName = null, bool useXmlConfigurator = false, ILogFormatter logFormatter = null)
            : base(logManager, loggerName, configurationManager, typeName, useXmlConfigurator, logFormatter)
        {
        }

        internal new IHtecLogger GetLogger([CallerMemberName] string methodName = null) => base.GetLogger(methodName);
    }
}
