<ng-container *ngIf="showContent">
    <div class="card form-group">
        <div class="card-header">
            Filter
        </div>
        <div class="card-body form-group">
            <form [formGroup]="datepickerForm">
                <div class="form-group">
                    <div class="form-inline">
                        <div class="input-group col-2 pl-0">
                            <label class="col-form-label" for="dp1">From</label>
                        </div>
                        <div class="input-group col-4">
                            <input id="dp1" class="form-control" placeholder="dd/mm/yyyy" name="dp1"
                                formControlName="startDate" ngbDatepicker #d1="ngbDatepicker">
                            <div class="input-group-append">
                                <button class="btn btn-outline-primary calendar" (click)="d1.toggle()" type="button">
                                    <i class="bi bi-calendar2-date"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <span id="startDateError" class="text-danger ml-2 pt-1" placement="right top" container="body"
                                [ngbTooltip]="startDateError" *ngIf="startDateError">
                                <i class="bi bi-exclamation-triangle"></i>
                            </span>
                        </div>
                    </div>
                    <div class="form row">
                        <div class="input-group col-2 pl-0"></div>
                        <div class="input-group col-4">
                            <ngb-timepicker class="timepicker" formControlName="startTime" id="startTime"
                                [seconds]="seconds">
                            </ngb-timepicker>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-inline">
                        <div class="input-group col-2 pl-0">
                            <label class="col-form-label" for="dp2">To</label>
                        </div>
                        <div class="input-group col-4">
                            <input id="dp2" class="form-control" placeholder="dd/mm/yyyy" name="dp2" 
                                formControlName="endDate" ngbDatepicker #d2="ngbDatepicker">
                            <div class="input-group-append">
                                <button class="btn btn-outline-primary calendar" (click)="d2.toggle()" type="button">
                                    <i class="bi bi-calendar2-date"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <span id="endDateError" class="text-danger ml-2 pt-1" placement="right top" container="body"
                                [ngbTooltip]="endDateError" *ngIf="endDateError">
                                <i class="bi bi-exclamation-triangle"></i>
                            </span>
                        </div>
                    </div>
                    <div class="form row">
                        <div class="input-group col-2 pl-0"></div>
                        <div class="input-group col-4">
                            <ngb-timepicker class="timepicker" formControlName="endTime" id="endTime"
                                [seconds]="seconds">
                            </ngb-timepicker>
                        </div>
                        <span id="endTimeError" class="text-danger ml-2 pt-1" placement="right top" container="body"
                            [ngbTooltip]="endTimeError" *ngIf="endTimeError">
                            <i class="bi bi-exclamation-triangle"></i>
                        </span>
                    </div>
                </div>
            </form>
            <div class="form-group">
                <div class="form-inline">
                    <div class="input-group col-2 pl-0">
                        <label class="col-form-label" for="filterDropdown">Filter</label>
                    </div>
                    <div class="input-group col-4">
                        <div ngbDropdown class="d-inline-block">
                            <button class="btn btn-outline-primary" id="filterDropdown"
                                ngbDropdownToggle>{{activeFilter}}</button>
                            <div ngbDropdownMenu aria-labelledby="filterDropdown" (ngModel)="activeFilter">
                                <button ngbDropdownItem *ngFor="let filter of filters;"
                                    (click)="setFilter(filter)">{{filter}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group text-center m-0">
                <button type="button" id="shiftSearchBtn" class="btn btn-primary" (click)="refreshData()"
                    [disabled]="startDateError || endDateError || endTimeError">
                    Search
                </button>
                <span id="shiftSearchError" class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="refreshError"
                    *ngIf="refreshError">
                    <i class="bi bi-exclamation-triangle"></i>
                </span>
            </div>
        </div>
    </div>
    <div id="card-fuel-transactions" class="card form-group">
        <div class="card-header form-group">
            Fuel transactions
        </div>
        <div class="card-body form-group">
            <span *ngIf="filteredFuelTransactionDetailsData.length === 0">No transactions</span>
            <ngb-accordion [closeOthers]="false" #accordion>
                <ngb-panel *ngFor="let fuelTransactionDetails of filteredFuelTransactionDetailsData; let i = index"
                    [id]="'item'+i">
                    <ng-template ngbPanelHeader>
                        <div class="container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <button ngbPanelToggle class="btn btn-link pl-0">
                                <span>Transaction {{fuelTransactionDetails.TransactionId}} at
                                    {{formatDatetime(fuelTransactionDetails.TransactionTime)}}</span>
                            </button>
                            <div>
                                <button type="button" class="btn btn-primary mr-1"
                                    (click)="getReceipt(fuelTransactionDetails)"
                                    *ngIf="fuelTransactionDetails.HasReceipt">Fetch receipt</button>
                                <button type="button" class="btn btn-primary mr-1"
                                    (click)="printReceipt(fuelTransactionDetails)"
                                    *ngIf="fuelTransactionDetails.HasReceipt && fuelTransactionDetails.PrinterEnabled">Print
                                    receipt</button>
                                <button type="button" class="btn btn-primary mr-1"
                                    (click)="saveReceipt(fuelTransactionDetails)"
                                    *ngIf="fuelTransactionDetails.HasReceipt">Save receipt</button>
                                <span class="text-danger ml-2 pt-1" placement="right top" container="body"
                                    [ngbTooltip]="fuelTransactionDetails.ReceiptErrorText"
                                    *ngIf="fuelTransactionDetails.HasReceipt && fuelTransactionDetails.ReceiptError"><i
                                        class="bi bi-exclamation-triangle"></i></span>
                            </div>
                        </div>
                    </ng-template>
                    <ng-template ngbPanelContent>
                        <table><tr><td><div class="col-10">
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.TxnNumber)">
                            <span class="col-4">OPT transaction number</span>
                            <span class="col-auto">{{fuelTransactionDetails.TxnNumber}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.GradeCode)">
                            <span class="col-4">Grade code</span>
                            <span class="col-auto">{{fuelTransactionDetails.GradeCode}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.GradeName)">
                            <span class="col-4">Grade name</span>
                            <span class="col-auto">{{fuelTransactionDetails.GradeName}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.PumpDetailsString)">
                            <span class="col-4">Pump details</span>
                            <span class="col-auto">{{fuelTransactionDetails.PumpDetailsString}}</span>
                        </div>
                        <div class="row">
                            <span class="col-4">Fuel quantity</span>
                            <span class="col-auto">{{fuelTransactionDetails.FuelQuantity}} litres</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.WashCode)">
                            <span class="col-4">Car wash code</span>
                            <span class="col-auto">{{fuelTransactionDetails.WashCode}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.WashName)">
                            <span class="col-4">Car wash name</span>
                            <span class="col-auto">{{fuelTransactionDetails.WashName}}</span>
                        </div>
                        <div class="row">
                            <span class="col-4">Car wash quantity</span>
                            <span class="col-auto">{{fuelTransactionDetails.WashQuantity}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.CardNumber)">
                            <span class="col-4">Card number</span>
                            <span class="col-auto">{{fuelTransactionDetails.CardNumber}}</span>
                        </div>
                        <div class="row">
                            <span class="col-4">Amount</span>
                            <span class="col-auto">&pound;{{fuelTransactionDetails.Amount.toFixed(2)}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.FuelCategory)">
                            <span class="col-4">Fuel category</span>
                            <span class="col-auto">{{fuelTransactionDetails.FuelCategory}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.FuelSubcategory)">
                            <span class="col-4">Fuel subcategory</span>
                            <span class="col-auto">{{fuelTransactionDetails.FuelSubcategory}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.WashCategory)">
                            <span class="col-4">Car wash category</span>
                            <span class="col-auto">{{fuelTransactionDetails.WashCategory}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.WashSubcategory)">
                            <span class="col-4">Car wash subcategory</span>
                            <span class="col-auto">{{fuelTransactionDetails.WashSubcategory}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.DiscountName)">
                            <span class="col-4">Discount name</span>
                            <span class="col-auto">{{fuelTransactionDetails.DiscountName}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.DiscountCode)">
                            <span class="col-4">Discount code</span>
                            <span class="col-auto">{{fuelTransactionDetails.DiscountCode}}</span>
                        </div>
                        <div class="row" *ngIf="fuelTransactionDetails.DiscountValue > 0">
                            <span class="col-4">Discount value</span>
                            <span class="col-auto">&pound;{{fuelTransactionDetails.DiscountValue}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.DiscountCardNumber)">
                            <span class="col-4">Discount card number</span>
                            <span class="col-auto">{{fuelTransactionDetails.DiscountCardNumber}}</span>
                        </div>
                        <div class="row" *ngIf="fuelTransactionDetails.LocalAccountMileage > 0">
                            <span class="col-4">Local account mileage</span>
                            <span class="col-auto">{{fuelTransactionDetails.LocalAccountMileage}}</span>
                        </div>
                        <div class="row" *ngIf="notEmptyField(fuelTransactionDetails.LocalAccountRegistration)">
                            <span class="col-4">Local account registration</span>
                            <span class="col-auto">{{fuelTransactionDetails.LocalAccountRegistration}}</span>
                        </div>
                    </div></td><td><div class="col-10">
                        <div class="col-auto">
                            <div class="row"><pre>{{fuelTransactionDetails.ReceiptContent}}</pre></div>
                        </div>
                    </div></td></tr></table>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>
    <!-- <div id="card-receipt" class="card form-group" *ngIf="showReceipt">
        <div class="card-header form-group d-flex align-items-center justify-content-between">
            Receipt
            <div id="hideReceipt">
                <button type="button" class="btn btn-primary" (click)="hideReceipt()">Hide</button>
            </div>
        </div>
        <div class="card-body form-group">
          <pre>{{receiptText}}</pre>
        </div>
    </div> -->
    <div id="card-other-events" class="card form-group" *ngIf="activeFilter === 'None'">
        <div class="card-header form-group">
            Other events
        </div>
        <div class="card-body form-group">
            <span *ngIf="otherEventsData.length === 0">No events</span>
            <ngb-accordion>
                <ngb-panel *ngFor="let otherEvent of otherEventsData; let i = index" [id]="'item'+i">
                    <ng-template ngbPanelHeader>
                        <span>Transaction {{otherEvent.TransactionId}} at
                            {{formatDatetime(otherEvent.TransactionTime)}}</span>
                    </ng-template>
                </ngb-panel>
            </ngb-accordion>
        </div>
    </div>
</ng-container>
