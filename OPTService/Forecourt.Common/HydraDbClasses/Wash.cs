namespace OPT.Common.HydraDbClasses
{
    public class Wash
    {
        public byte ProgramId { get; }
        public string ProductCode { get; }
        public string Description { get; }
        public string Price { get; }
        public string VatRate { get; }
        public short Category { get; }
        public short Subcategory { get; }

        public Wash(int programId, string productCode, string description, string price, string vatRate, int category, int subcategory)
        {
            ProgramId = (byte) programId;
            ProductCode = productCode;
            Description = description;
            Price = price;
            VatRate = vatRate;
            Category = (short) category;
            Subcategory = (short) subcategory;
        }
    }
}