using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Messages.Opt.Xsd;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace OPT.Common.Workers.Messaging
{
    /// <summary>
    /// Class to build XSD based configuration sent to the OPT.
    /// </summary>
    public class OptConfigurationBuilder : Loggable
    {
        private readonly IHydraDb _hydraDb;

        private DivertConfig _divertConfig;

        /// <summary>
        /// Creates a new instance of the <see cref="OptConfigurationBuilder"/> class.
        /// </summary>
        /// <param name="hydraDb">The abstracted database access layer.</param>
        /// <param name="logger">The logger.</param>
        public OptConfigurationBuilder(IHydraDb hydraDb, IHtecLogger logger)
            : base(logger)
        {
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        /// <summary>
        /// Build the configuration object for an OPT.
        /// </summary>
        /// <param name="opt">The target OPT.</param>
        /// <param name="advancedConfig">Advanced config for OPTs</param>
        /// <param name="genericOptConfig">Generic config options for all OPTs.</param>
        /// <param name="allPumps">All configured pumps.</param>
        /// <param name="fetchedTids">All TIDs retrieved from PaymentConfig</param>
        /// <param name="paymentConfig">Worker for Payment Configuration </param>
        /// <param name="localAccountWorker">Worker for Local Account functionality.</param>
        /// <param name="divertConfig">Configuration for diverted communications.</param>
        /// <returns>An XML serialisable class.</returns>
        public configType BuildConfig(IOpt opt, AdvancedConfig advancedConfig, GenericOptConfig genericOptConfig, IPumpCollection allPumps, IList<TermId> fetchedTids,
            IPaymentConfigIntegrator paymentConfig, ILocalAccountWorker localAccountWorker, DivertConfig divertConfig)
        {
            _divertConfig = divertConfig;

            return new configType
            {
                site = GetConfigSite(advancedConfig, genericOptConfig),
                opt = GetConfigOpts(advancedConfig, genericOptConfig, opt),
                pumps = GetConfigPumps(opt, allPumps, fetchedTids, paymentConfig),
                hydraOpt = GetConfigHydraOpt(opt.IdString, _hydraDb),
                esocket = GetConfigPaymentConfig(genericOptConfig, paymentConfig),
                contactless = GetConfigContactless(genericOptConfig.ContactlessConfiguration),
                cards = GetConfigCards(advancedConfig, genericOptConfig, localAccountWorker),
                loyalty = GetConfigLoyalty(genericOptConfig),
                carWash = GetConfigWashLink(genericOptConfig),
                localAccounts = GetLocalAccountConfig(advancedConfig),
                logging = GetLoggingConfig(genericOptConfig)
            };
        }

        private localAccountConfig GetLocalAccountConfig(AdvancedConfig advancedConfig)
        {
            return new localAccountConfig
            {
                enabled = advancedConfig.LocalAccountsEnabled
            };
        }

        private static host[] GetConfigPaymentConfig(GenericOptConfig genericOptConfig, IPaymentConfigIntegrator paymentConfig)
        {
            return paymentConfig?.EndPoints?.Select(x => new host { ip = x.Item1, port = x.Item2}).ToArray() ?? new host[] { };
        }

        private static carWash GetConfigWashLink(GenericOptConfig genericOptConfig)
        {
            return new carWash
            {
                wash = genericOptConfig.Washes?.Select(x => new wash
                {
                    programId = x.ProgramId,
                    productCode = x.ProductCode,
                    displayDescription = x.Description,
                    price = x.Price,
                    vatRate = x.VatRate
                }).ToArray()
            };
        }

        private static loyalty GetConfigLoyalty(GenericOptConfig genericOptConfig)
        {
            var morrisonsLoyalty = genericOptConfig.LoyaltyList[GenericOptConfig.MorrisonsName];

            if (morrisonsLoyalty == null || !morrisonsLoyalty.IsPresent)
            {
                return null;
            }

            return new loyalty
            {
                morrisons = new morrisons
                {
                    terminal = new terminal
                    {
                        siteId = morrisonsLoyalty.Terminal.SiteId,
                        terminalId = morrisonsLoyalty.Terminal.TerminalId,
                        footer1 = morrisonsLoyalty.Terminal.Footer1,
                        footer2 = morrisonsLoyalty.Terminal.Footer2,
                        timeout = morrisonsLoyalty.Terminal.Timeout,
                        apiKey = morrisonsLoyalty.Terminal.ApiKey,
                        httpHeader = morrisonsLoyalty.Terminal.HttpHeader
                    },
                    hosts = morrisonsLoyalty.Hosts
                        .Select(x => new host { ip = x.IpAddress, port = x.Port }).ToArray(),
                    hostnames = morrisonsLoyalty.Hostnames
                        .Select(x => new hostname { name = x }).ToArray(),
                    iins = morrisonsLoyalty.Iins
                        .Select(x => new iin { low = x.Low, high = x.High }).ToArray(),
                    tariffMapping = morrisonsLoyalty.TariffMappings
                        .Select(x => new mapping { productCode = x.ProductCode, loyaltyCode = x.LoyaltyCode }).ToArray()
                }
            };
        }

        private static cards GetConfigCards(AdvancedConfig advancedConfig, GenericOptConfig genericOptConfig, ILocalAccountWorker localAccountWorker)
        {
            return new cards
            {
                config = new cardsConfig
                {
                    termCapabilities = genericOptConfig.TermCapabilities ?? string.Empty,
                    termAddCapabilities = genericOptConfig.TermAddCapabilities ?? string.Empty
                },
                aids = genericOptConfig.CardAids.Select(x => new aid
                {
                    aid1 = x.Aid ?? string.Empty,
                    appVerTerm = x.AppVerTerm ?? string.Empty,
                    tacDefault = x.TacDefault ?? string.Empty,
                    tacDenial = x.TacDenial ?? string.Empty,
                    tacOnline = x.TacOnline ?? string.Empty,
                    partialMatch = x.PartialMatch ?? string.Empty,
                    tdol = x.Tdol ?? string.Empty,
                    ddol = x.Ddol ?? string.Empty,
                    floorLimit = x.FloorLimit ?? string.Empty,
                    emvTarget = x.EmvTarget ?? string.Empty,
                    emvMaxTarget = x.EmvMaxTarget ?? string.Empty,
                    emvThreshold = x.EmvThreshold ?? string.Empty
                }).ToArray(),
                cless_aids = genericOptConfig.ContactlessConfiguration.HasContactless
                    ? GetClessAids(genericOptConfig)
                    : new cless_aid[0],
                capks = genericOptConfig.CardCapks.Select(x => new capk
                {
                    rid = x.Rid ?? string.Empty,
                    index = x.TheIndex ?? string.Empty,
                    modulus = x.Modulus ?? string.Empty,
                    exponent = x.Exponent ?? string.Empty,
                    checksum = x.Checksum ?? string.Empty,
                    expiryDate = x.ExpiryDate ?? string.Empty
                }).ToArray(),
                fuelcards = genericOptConfig.FuelCards.Select(x => new fuelcard
                {
                    iinStart = x.IinStart ?? string.Empty,
                    iinEnd = x.IinEnd ?? string.Empty,
                    onlinePin = x.OnlinePin ?? string.Empty
                }).ToArray(),
                tariffMapping = genericOptConfig.TariffMappings
                    .Select(x => new gradeMapping { grade = x.Grade, productCode = x.ProductCode }).ToArray(),
                discountCards = CreateDiscountCards(genericOptConfig),
                localAccounts = advancedConfig.LocalAccountsEnabled
                    ? GetLocalAccounts(localAccountWorker)
                    : new localAccountCustomer[0]
            };
        }

        private static localAccountCustomer[] GetLocalAccounts(ILocalAccountWorker localAccountWorker)
        {
            return localAccountWorker.Customers.Where(x => x.CustomerExists).Select(x => new localAccountCustomer
            {
                name = x.Name,
                pin = x.Pin ? 1 : 0,
                printValue = x.PrintValue ? 1 : 0,
                allowLoyalty = x.AllowLoyalty ? 1 : 0,
                fuelOnly = x.FuelOnly ? 1 : 0,
                getMiles = x.MileageEntry ? 1 : 0,
                getReg = x.RegistrationEntry ? 1 : 0,
                isPrePay = x.PrePayAccount ? 1 : 0,
                transLimit = $"{x.TransactionLimit}",
                cards = x.Cards.Select(y => new card
                {
                    pan = y.Pan,
                    description = y.Description,
                    discount = $"{y.Discount}",
                    restrictions1 = y.Restrictions1,
                    restrictions2 = y.Restrictions2
                }).ToArray()

            }).ToArray();
        }

        private static discountCardList CreateDiscountCards(GenericOptConfig genericOptConfig)
        {
            return new discountCardList
            {
                // TODO: HOPT-1991 to add the other booleans in here
                discountRange = AreDiscountCardsEnabled(genericOptConfig)
                    ? genericOptConfig.DiscountCards.Select(x => new discountRange
                    {
                        iin = x.Iin,
                        name = x.Name,
                        type = x.Type,
                        value = $"{x.Value:F2}",
                        gradeSpecified = x.Grade > 0,
                        grade = x.Grade,
                        whitelist = x.Whitelist.Select(y => new whitelistCard { pan = y }).ToArray()
                    }).ToArray()
                    : null
            };
        }

        private static bool AreDiscountCardsEnabled(GenericOptConfig genericOptConfig)
        {
            // TODO: To be implemented in HOPT-1991, for now this should always return false;
            return false;
        }

        private static cless_aid[] GetClessAids(GenericOptConfig genericOptConfig)
        {
            // A contactless AID must have a CVM limit value, so we don't include it if not
            return genericOptConfig.CardClessAids.Where(x => !string.IsNullOrWhiteSpace(x.CvmLimit))
                .Select(x => new cless_aid
                {
                    aid = x.Aid ?? string.Empty,
                    appVerTerm = x.AppVerTerm ?? string.Empty,
                    transLimit = x.TransLimit ?? string.Empty,
                    floorLimit = x.FloorLimit ?? string.Empty,
                    cvmLimit = x.CvmLimit ?? string.Empty,
                    odcvmLimit = x.OdcvmLimit ?? string.Empty,
                    termAddCapabilities = x.TermAddCapabilities ?? string.Empty,
                    termCapabilitiesCvm = x.TermCapabilitiesCvm ?? string.Empty,
                    termCapabilitiesNoCvm = x.TermCapabilitiesNoCvm ?? string.Empty,
                    termRiskData = x.TermRiskData ?? string.Empty,
                    udol = x.Udol ?? string.Empty,
                    tacDefault = x.TacDefault ?? string.Empty,
                    tacDenial = x.TacDenial ?? string.Empty,
                    tacOnline = x.TacOnline ?? string.Empty,
                    terminalType = x.TerminalType ?? string.Empty,
                    mcc = x.MerchantCategoryCode ?? string.Empty,
                    maxTorn = x.MaxTorn ?? string.Empty,
                    maxTornLife = x.MaxTornLife ?? string.Empty,
                    mchipCvmCapAboveLimit = x.MchipCvmCapAboveLimit ?? string.Empty,
                    mchipCvmCapBelowLimit = x.MchipCvmCapBelowLimit ?? string.Empty,
                    mstripeCvmCapAboveLimit = x.MstripeCvmCapAboveLimit ?? string.Empty,
                    mstripeCvmCapBelowLimit = x.MstripeCvmCapBelowLimit ?? string.Empty,
                    magStripeAppVer = x.MagStripeAppVer ?? string.Empty,
                    expresspayReaderCapabilities = x.ExpressPayReaderCapabilities ?? string.Empty,
                    expresspayEnhancedReaderCapabilities = x.ExpressPayEnhancedReaderCapabilities ?? string.Empty,
                    expresspayKernelVersion = x.ExpressPayKernelVersion ?? string.Empty,
                    drl = GetClessDrls(genericOptConfig, x).ToArray()
                }).ToArray();
        }

        private static IEnumerable<drl> GetClessDrls(GenericOptConfig genericOptConfig, CardClessAid cardClessAid)
        {
            return genericOptConfig.CardClessDrls.Where(y => y.Aid.Equals(cardClessAid.Aid))
                .Select(z => new drl
                {
                    programId = z.ProgramId ?? string.Empty,
                    transLimit = z.TransLimit ?? string.Empty,
                    floorLimit = z.FloorLimit ?? string.Empty,
                    cvmLimit = z.CvmLimit ?? string.Empty
                });
        }

        private static contactless GetConfigContactless(ContactlessConfiguration configuration)
        {
            return new contactless
            {
                enabled = configuration.HasContactless,
                cardpreauthamount = (int)configuration.ContactlessCardPreAuthLimit,
                devicepreauthamount = (int)configuration.ContactlessDevicePreAuthLimit,
                ttq = configuration.Ttq,
                showSingleContactlessButton = configuration.SingleContactlessButton
            };
        }

        private hydraOpt GetConfigHydraOpt(string optIdString, IHydraDb hydraDb)
        {
            var optEndPoints = hydraDb.FetchEndPoints(_divertConfig.RedirectHydraId);

            return new hydraOpt
            {
                id = optIdString,
                inbound = new host
                {
                    ip = _divertConfig.IsOptServiceDiverted ? _divertConfig.DivertedServiceAddress.ToString() : optEndPoints.ToOptEndPoint.Address.ToString(),
                    port = _divertConfig.IsOptServiceDiverted ? _divertConfig.DivertedToOptPort : optEndPoints.ToOptEndPoint.Port
                },
                outbound = new host
                {
                    ip = _divertConfig.IsOptServiceDiverted
                        ? _divertConfig.DivertedServiceAddress.ToString()
                        : optEndPoints.FromOptEndPoint.Address.ToString(),
                    port = _divertConfig.IsOptServiceDiverted ? _divertConfig.DivertedFromOptPort : optEndPoints.FromOptEndPoint.Port
                },
                heartbeat = new host
                {
                    ip = _divertConfig.IsOptServiceDiverted
                        ? _divertConfig.DivertedServiceAddress.ToString()
                        : optEndPoints.HeartbeatEndPoint.Address.ToString(),
                    port = _divertConfig.IsOptServiceDiverted ? _divertConfig.DivertedHeartbeatPort : optEndPoints.HeartbeatEndPoint.Port
                },
                media = new host
                {
                    ip = _divertConfig.IsOptServiceDiverted
                        ? _divertConfig.DivertedServiceAddress.ToString()
                        : optEndPoints.MediaChannelEndPoint.Address.ToString(),
                    port = optEndPoints.MediaChannelEndPoint.Port
                }
            };
        }

        private static opt GetConfigOpts(AdvancedConfig advancedConfig, GenericOptConfig genericOptConfig, IOpt opt)
        {
            return new opt
            {
                NullableMode = (int)opt.Mode,
                NullableReceiptLayoutMode = genericOptConfig.ReceiptLayoutMode,
                predefinedAmounts = CreatePredefinedAmounts(genericOptConfig),
                receiptHeaderLines = opt.ReceiptHeader?.Split('\n') ?? genericOptConfig.DefaultReceiptHeaderLines,
                receiptFooterLines = opt.ReceiptFooter?.Split('\n') ?? genericOptConfig.DefaultReceiptFooterLines,
                NullableMixedModeKioskTriggerMode = (int)GetMixedModeKioskTriggerMode(advancedConfig),
                timeouts = new timeouts()
                {
                    opt = genericOptConfig.OptPaymentTimeout,
                    pod = genericOptConfig.PodPaymentTimeout,
                    mixed = genericOptConfig.MixedPaymentTimeout,
                    kiosk = genericOptConfig.KioskTimeout,
                    nozzleDown = genericOptConfig.NozzleDownTimeout,
                    secAuth = genericOptConfig.SecAuthTimeout
                },
                pciRestartTime = genericOptConfig.PciRestartTime,
                NullableGetConfigRetryAttempts = genericOptConfig.GetConfigRetryAttempts,
                NullableMinimumAuthorisedAmount = genericOptConfig.MinimumAuthorisedAmount,
                NullableCardRemovalTimeout = genericOptConfig.CardRemovalTimeout,
                NullableMainThreadHeartbeatTimeout = genericOptConfig.MainThreadHeartbeatTimeout,
                NullableSignInMsgTimeout = genericOptConfig.SignInMsgTimeout,
                NullableConfigMsgTimeout = genericOptConfig.ConfigMsgTimeout,
                NullablePaymentApprovedMsgTimeout = genericOptConfig.PaymentApprovedMsgTimeout,
                NullablePaymentClearedMsgTimeout = genericOptConfig.PaymentClearedMsgTimeout,
                NullableGeneralMessageAckTimeout = genericOptConfig.generalMessageAckTimeout
            };
        }

        private static MixedModeKioskTrigger GetMixedModeKioskTriggerMode(AdvancedConfig advancedConfig)
        {
            if (!advancedConfig.NozzleUpForKioskUse)
            {
                return MixedModeKioskTrigger.IgnoreNozzleUp;
            }

            return advancedConfig.UseReplaceNozzleScreen
                ? MixedModeKioskTrigger.NozzleUp
                : MixedModeKioskTrigger.NozzleUpUseReplaceScreen;
        }

        internal static predefinedAmount CreatePredefinedAmounts(GenericOptConfig genericOptConfig)
        {
            return ArePredefinedAmountsSet(genericOptConfig)
                ? new predefinedAmount
                {
                    fuelEnabled = genericOptConfig.PredefinedAmountFuel,
                    paymentCardEnabled = genericOptConfig.PredefinedAmountPayment,
                    localAccountEnabled = genericOptConfig.PredefinedAmountLocalAccount,
                    amount = genericOptConfig.PredefinedAmounts.Select(x => new amount { NullableValue = x }).ToArray()
                }
                : null;
        }

        private static bool ArePredefinedAmountsSet(GenericOptConfig genericOptConfig)
        {
            return (genericOptConfig.PredefinedAmountFuel
                   || genericOptConfig.PredefinedAmountPayment
                   || genericOptConfig.PredefinedAmountLocalAccount)
                   && genericOptConfig.PredefinedAmounts != null
                   && genericOptConfig.PredefinedAmounts.Any();
        }

        private static site GetConfigSite(AdvancedConfig advancedConfig, GenericOptConfig genericOptConfig)
        {
            return new site
            {
                vatNumber = advancedConfig.VatNumber,
                name = advancedConfig.SiteName,
                currencyCodeSpecified = advancedConfig.CurrencyCode != 0,
                currencyCode = advancedConfig.CurrencyCode,
                maxFillOverrideSpecified = advancedConfig.MaxFillOverride > 0,
                maxFillOverride = advancedConfig.MaxFillOverride,
                type = advancedConfig.SiteType.ToString(),
                ignoreMessageId = genericOptConfig.IgnoreMessageId,
                useReplaceNozzleScreen = advancedConfig.UseReplaceNozzleScreen,
                marina = advancedConfig.SiteType.Equals(SiteType.Marina)
                    ? new marina { dutySplit = genericOptConfig.DutySplit }
                    : null
            };
        }

        /// <summary>
        /// Builds the OPT configuration object for pumps.
        /// </summary>
        /// <param name="opt">The target OPT.</param>
        /// <param name="allPumps">All configured pumps.</param>
        /// <param name="fetchedTids">All TIDs retrieved from PaymentConfiguration</param>
        /// <param name="paymentConfig">Payment Configuration Worker</param>
        /// <returns>An XML serialisable class.</returns>
        public pump[] GetConfigPumps(IOpt opt, IPumpCollection allPumps, IList<TermId> fetchedTids, IPaymentConfigIntegrator paymentConfig)
        {
            return opt.PumpList().Select(p => allPumps.TryGetPump(p, out var thePump) ? thePump : null)
                .Where(p => p != null && (fetchedTids.Any(y => y.Tid.Equals(p.Tid)) || (!paymentConfig?.StrictPumpTidValidation ?? false)))
                .Select(z => new pump
                {
                    number = z.Number,
                    numberSpecified = true,
                    tid = z.Tid ?? string.Empty,
                    transactionNumberSpecified = true,
                    transactionNumber = fetchedTids.FirstOrDefault(a => a.Tid.Equals(z.Tid))?.TransactionNumber ?? 0,
                    maxFillOverrideForFuelCardsSpecified = z.MaxFillOverrideForFuelCards,
                    maxFillOverrideForFuelCards = z.MaxFillOverrideForFuelCards ? 1 : 0,
                    maxFillOverrideForPaymentCardsSpecified = z.MaxFillOverrideForPaymentCards,
                    maxFillOverrideForPaymentCards = z.MaxFillOverrideForPaymentCards ? 1 : 0
                }).ToArray();
        }

        private static logging GetLoggingConfig(GenericOptConfig genericOptConfig)
        {
            return new logging
            {
                enhancedLogging = genericOptConfig.EnhancedLogging
            };
        }
    }
}
