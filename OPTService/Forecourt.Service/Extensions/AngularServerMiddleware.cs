using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Owin.StaticFiles;

namespace OPTService.Extensions
{
    /// <summary>
    /// An Angular server middleware class
    /// </summary>
    public class AngularServerMiddleware
    {
        private readonly AngularServerOptions _options;
        private readonly StaticFileMiddleware _innerMiddleware;

        /// <summary>
        /// The Angular server middleware constructor
        /// </summary>
        /// <param name="next">The navigation interceptor function</param>
        /// <param name="options">The Angular server options</param>
        public AngularServerMiddleware(Func<IDictionary<string, object>, Task> next, AngularServerOptions options)
        {
            _options = options;
            _innerMiddleware = new StaticFileMiddleware(next, options.FileServerOptions.StaticFileOptions);
        }

        /// <summary>
        /// The middleware interceptor
        /// </summary>
        /// <param name="arg">The call arguments dictionary</param>
        /// <returns></returns>
        public async Task Invoke(IDictionary<string, object> arg)
        {
            await _innerMiddleware.Invoke(arg);

            // route to root path if the status code is 404
            if ((int)arg["owin.ResponseStatusCode"] == (int)HttpStatusCode.NotFound && _options.IsHtml5Mode)
            {
                arg["owin.RequestPath"] = _options.EntryPath.Value;
                await _innerMiddleware.Invoke(arg);
            }
        }
    }
}
