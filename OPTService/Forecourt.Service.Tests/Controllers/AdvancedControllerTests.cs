using FluentAssertions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Testing.Helpers.Core;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class AdvancedControllerTests
    {
        private readonly IHtecLogger _logger;
        private readonly ICore _core;
        private readonly IWeb _web;
        private readonly IConfigurationManager _configurationManager;

        public AdvancedControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _core = Substitute.For<ICore>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _web = Substitute.For<IWeb, FakeLoggable>(_logger, _configurationManager, null);
        }

        [Fact]
        public async Task test_GetAdvancedConfiguration_NoConfiguration_response()
        {
            // Arrange
            AdvancedConfigDetails advancedConfiguration = null;
            _web.GetAdvancedConfig().Returns(advancedConfiguration);
            var controller = new AdvancedController(_web, _logger, _configurationManager);

            // Act
            var result = await controller.GetAdvancedConfiguration().ConfigureAwait(false) as ResponseMessageResult;

            // Assert
            result.Should().BeOfType<ResponseMessageResult>();
            result.Response.Should().Be404NotFound();
        }

        [Fact]
        public async Task test_GetAdvancedConfiguration_Ok_AllData_response()
        {
            // Arrange
            var advancedConfiguration = new AdvancedConfigDetails
            {
                AutoAuth = true,
                MediaChannel = false,
                UnmannedPseudoPos = true,
                AsdaDayEndReport = true,
                LoyaltyAvailable = new List<string>(),
                PaymentTimeoutOpt = 1,
                PaymentTimeoutPod = 2,
                PaymentTimeoutMixed = 3,
                PaymentTimeoutNozzleDown = 4,
                TimeoutKiosk = 5,
                TimeoutSecAuth = 6,
                TillNumber = 7,
                FuelCategory = 8,
                ForwardFuelPriceUpdate = false,
                FuellingIndefiniteWait = false,
                FuellingWaitMinutes = 9,
                FuellingBackoffAuth = 10,
                FuellingBackoffPreAuth = 11,
                FuellingBackoffStopStart = 12,
                FuellingBackoffStopOnly = 13,
                PosClaimNumber = 14,
                FilePruneDays = 15,
                TransactionPruneDays = 16,
                ReceiptPruneDays = 17,
                CardReferences = new List<CardReferenceDetails>()
            };
            _web.GetAdvancedConfig().Returns(advancedConfiguration);
            var controller = new AdvancedController(_web, _logger, _configurationManager);

            // Act
            var result = await controller.GetAdvancedConfiguration().ConfigureAwait(false) as ResponseMessageResult;

            // Assert
            result.Should().BeOfType<ResponseMessageResult>();
            result.Response.Should().Be200Ok().And.BeAs(advancedConfiguration);
        }

        [Fact]
        public async Task test_GetAdvancedConfiguration_Ok_SingleProperty_response()
        {
            // Arrange
            var advancedConfiguration = new AdvancedConfigDetails
            {
                FuelCategory = 1
            };

            _web.GetAdvancedConfig().Returns(advancedConfiguration);
            var controller = new AdvancedController(_web, _logger, _configurationManager);

            // Act
            var result = await controller.GetAdvancedConfiguration().ConfigureAwait(false) as ResponseMessageResult;

            // Assert
            result.Should().BeOfType<ResponseMessageResult>();
            result.Response.Should().Be200Ok().And.BeAs(advancedConfiguration);
        }

        [Fact]
        public async Task test_GetMaxFillOverride_Ok_response()
        {
            // Arrange
            _web.GetMaxFillOverride().Returns((uint) 0);
            var controller = new AdvancedController(_web, _logger, _configurationManager);

            // Act
            var result = await controller.GetMaxFillOverride().ConfigureAwait(false) as ResponseMessageResult;

            // Assert
            result.Should().BeOfType<ResponseMessageResult>();
            result.Response.Should().Be200Ok().And.BeAs(0);
        }
    }
}
