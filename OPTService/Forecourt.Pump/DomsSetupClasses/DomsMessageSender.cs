using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Logger.Interfaces;
using PSS_Forecourt_Lib;
using PSS_TcpIp_Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using domsForecourt = PSS_Forecourt_Lib.Forecourt;

namespace Forecourt.Pump.DomsSetupClasses
{
    [HasConfiguration]
    public class DomsMessageSender : DomsMessage, IDomsMessageSender
    {
        /// <summary>
        /// Config key for, the message Acknowledge timeout.  Suffix for uniqueness.
        /// </summary>
        public const string ConfigKeyMessageAckTimeout = Constants.ConfigKeyCategoryConnectivity + "Message:Ack:Timeout:DOMS";

        /// <summary>
        /// Default value for, the message Acknowledge timeout
        /// </summary>
        public const string DefaultValueMessageAckTimeout = "00:00:02";

        /// <summary>
        /// Configuration value for, the message Acknowledge timeout
        /// </summary>
        protected ConfigurableTimeSpan MessageAckTimeout { get; private set; }

        /// <summary>
        /// Config key, for the Timer poll interval.  Suffix with the instance the poll runs on for uniqueness.
        /// </summary>
        public const string ConfigKeyPollInterval = Constants.ConfigKeyCategoryConnectivity + "Poll:Interval:Milliseconds:DOMS";

        /// <summary>
        /// Default value, for the Timer poll interval.
        /// </summary>
        public const string DefaultValuePollInterval = "00:00:00.050";

        /// <summary>
        /// Configuration value for, the Timer poll interval.
        /// </summary>
        protected ConfigurableTimeSpan PollInterval { get; private set; }

        private readonly byte _posId;
        private readonly IDomsPos _domsPos;
        private readonly IPSSTcpIpInterface _tcp;
        private readonly IDomsMessageReader _reader;
        private readonly byte _apc;

        /// <inheritdoc cref="IConnectionThread"/>
        public ConfigurableBool LogRxTx { get; private set; }

        public DomsMessageSender(IDomsPos domsPos, IDomsMessageReader reader, byte posId, IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(DomsMessageSender), configurationManager)
        {
            _posId = posId;
            _domsPos = domsPos;
            _tcp = null;
            _reader = reader;
            _apc = 1;

            MessageAckTimeout = new ConfigurableTimeSpan(this, ConfigKeyMessageAckTimeout, DefaultValueMessageAckTimeout);
            PollInterval = new ConfigurableTimeSpan(this, ConfigKeyPollInterval, DefaultValuePollInterval);
            LogRxTx = new ConfigurableBool(this, DomsSetup.ConfigKeyLogRxTx, DomsSetup.DefaultValueLogRxTx);
        }

        public DomsMessageSender(IPSSTcpIpInterface tcp, IDomsMessageReader reader, byte posId, byte apc, IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(DomsMessageSender), configurationManager)
        {
            _posId = posId;
            _tcp = tcp;
            _domsPos = null;
            _reader = reader;
            _apc = apc;

            MessageAckTimeout = new ConfigurableTimeSpan(this, ConfigKeyMessageAckTimeout, DefaultValueMessageAckTimeout);
            PollInterval = new ConfigurableTimeSpan(this, ConfigKeyPollInterval, DefaultValuePollInterval);
            LogRxTx = new ConfigurableBool(this, DomsSetup.ConfigKeyLogRxTx, DomsSetup.DefaultValueLogRxTx);
        }

        private static byte Bcd1(byte number) => Bcd(number, 1)[0];

        private static byte[] Bcd(uint number, int length)
        {
            byte[] bytes = new byte[length];
            uint current = number;
            for (int i = length - 1; i >= 0; i--)
            {
                bytes[i] = (byte)(16 * (current % 100 / 10) + current % 10);
                current /= 100;
            }

            return bytes;
        }

        private static byte[] AsciiBytes(string name, int length)
        {
            byte[] ascii = Encoding.ASCII.GetBytes(name);
            var bytes = new List<byte>();
            for (int i = 0; i < length; i++)
            {
                if (i < ascii.Length)
                {
                    bytes.Add(ascii[i]);
                }
                else
                {
                    bytes.Add(0);
                }
            }

            return bytes.ToArray();
        }

        private static byte[] ToTwoByte(int number)
        {
            return new byte[] { (byte)(number % 256), (byte)(number / 256) };
        }

        private static int FromTwoByte(byte[] bytes)
        {
            return bytes.Length == 2 ? bytes[0] + bytes[1] * 256 : 0;
        }

        private bool SendDomsMessage(byte[] message, string info)
        {
            var msgCode = (DomsMessageCode)FromTwoByte(message.Skip(1).Take(2).ToArray());
            var subC = message[3];
            var logMsg = _reader.LoggingEnabled(msgCode, subC);

            DoDeferredLogging(logMsg ? LogLevel.Info : LogLevel.None, $"TX Length: {message.Length}", () => new[] { $"APC: {_apc}; Code/SubCode: {msgCode}/{subC}; {info}; Message: {ToHexString(message)}" }, methodName: "DoSendMessage");

            if (_domsPos == null && _tcp == null)
            {
                DoDeferredLogging(logMsg ? LogLevel.Warn : LogLevel.None, HeaderException, () => new[] { "Cannot send message to DOMS Pos" });
                return false;
            }

            if (_domsPos != null)
            {
                try
                {
                    _domsPos.SendDomsPosMessage(message.Length, ref message, out byte[] answer);
                    _reader.ExtractResponse(answer);
                    return !_reader.MessageReject;
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, $"{HeaderException}.Via DOMS POS Protocol", () => new[] { ex.Message }, ex);

                    int rejectCode = ((domsForecourt)_domsPos).LastRejectCode;
                    byte rejectInfo = ((domsForecourt)_domsPos).LastRejectInfo;
                    int rejectMsgCode = ((domsForecourt)_domsPos).LastRejectMsgCode;
                    int rejectMsgSubCode = ((domsForecourt)_domsPos).LastRejectMsgSubCode;
                    DoDeferredLogging(LogLevel.Warn, $"{HeaderException}.LastRejectCode", () => new[] {
                        (rejectCode == 0x01 ? "Unknown Message Code" :
                            rejectCode == 0x02 ? "Syntax Error" :
                            rejectCode == 0x03 ? "Access Error" : $"0x{rejectCode:X2}; ") +
                            $"Info: 0x{rejectInfo:X2}; Message Code/SubCode: 0x{rejectMsgCode:X4}/0x{rejectMsgSubCode:X4}"
                    });
                    return false;
                }
            }
            else if (_tcp != null)
            {
                DateTime timeout = DateTime.Now.AddMilliseconds(MessageAckTimeout.GetValue().TotalMilliseconds);
                _reader.ClearReceived(_apc);
                try
                {
                    _tcp.SendMessage(_apc, message.Length, ref message);
                    while (!_reader.HasReceived(_apc) && DateTime.Now < timeout)
                    {
                        Thread.Sleep(PollInterval.GetValue().Milliseconds);
                    }

                    return _reader.HasReceived(_apc) && !_reader.HasRejected(_apc);
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, $"{HeaderException}.Via TCP/IP", () => new[] { ex.Message }, ex);
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        private static byte[] ConstructMessage(DomsMessageCode msgCode, byte subc, IEnumerable<byte> data)
        {
            var bytes = new List<byte> { 0xFF };
            bytes.AddRange(ToTwoByte((int)msgCode));
            bytes.Add(subc);
            bytes.AddRange(data);
            return bytes.ToArray();
        }

        public void FcPriceSetRequest(byte priceSetId, IList<byte> priceGroups, IList<DomsSetupGrade> grades)
        {
            var data = new List<byte>
            {
                Bcd1(priceSetId),
                Bcd1((byte) priceGroups.Count),
                Bcd1((byte) grades.Count)
            };
            foreach (byte priceGroup in priceGroups)
            {
                data.Add(priceGroup);
            }

            foreach (DomsSetupGrade grade in grades)
            {
                data.Add(grade.GradeId);
            }

            foreach (byte priceGroup in priceGroups)
            {
                foreach (DomsSetupGrade grade in grades)
                {
                    data.AddRange(Bcd(grade.Price(priceGroup), 3));
                }
            }

            data.AddRange(new byte[] { 0, 0, 0, 0, 0, 0, 0 });

            SendDomsMessage(ConstructMessage(DomsMessageCode.ChangeFcPriceSet, 0x02, data), "LoadFcPriceSet");
        }

        public void SetGradeTextRequest(byte gradeId, string name)
        {
            var data = new List<byte>
            {
                0x04, // FC Parameter Group ID is 04 (Grade Texts)
                0x01, // FC Parameter Set is 01 (Grade Text Group ID for 12 ch grade names)
                0x01, // Number of FC Parameters is 1
                Bcd1(gradeId)
            };
            data.AddRange(AsciiBytes(name, 12));
            SendDomsMessage(ConstructMessage(DomsMessageCode.ChangeFcParameters, 0x00, data), "SetGradeText");
        }

        public void SetGradeColourRequest(byte gradeId, string colour)
        {
            var data = new List<byte>
            {
                0x04, // FC Parameter Group ID is 04 (Grade Texts)
                0x04, // FC Parameter Set is 04 (Grade Text Group ID for 6 ch grade colours)
                0x01, // Number of FC Parameters is 1
                Bcd1(gradeId)
            };
            data.AddRange(AsciiBytes(colour, 6));
            SendDomsMessage(ConstructMessage(DomsMessageCode.ChangeFcParameters, 0x00, data), "SetGradeColour");
        }

        public void FpClearRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                0x10, // Install Message Code is 10H
                Bcd1(pump.FpId)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.ClearInstallData, 0x00, data), "FpClearRequest");
        }

        public void FpInstallRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                (byte) pump.AllInstallParIds.Count
            };
            foreach (byte installPar in pump.AllInstallParIds)
            {
                IList<byte> parBytes = pump.InstallPar(installPar);
                data.Add(Bcd1(installPar));
                data.AddRange(ToTwoByte(parBytes.Count));
                data.AddRange(parBytes);
            }

            SendDomsMessage(ConstructMessage(DomsMessageCode.InstallFp, 0x03, data), "FpInstallRequest");
        }

        public void FetchFcPriceSetRequest()
        {
            var data = new List<byte>
            {
                0x00 // Current Price Set
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.FcPriceSet, 0x02, data), "Fetch.FcPriceSet");
        }

        public void FetchFcGradeTextRequest()
        {
            var data = new List<byte>
            {
                0x04, // FC Parameter Group ID is 04 (Grade Texts)
                0x01, // FC Parameter Set is 01 (Grade Text Group ID for 12 ch grade names)
                0x00 // Number of FC Parameters is 0
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.FcParameterSet, 0x00, data), "Fetch.FcGradeText");
        }

        public void FetchFcGradeColourRequest()
        {
            var data = new List<byte>
            {
                0x04, // FC Parameter Group ID is 04 (Grade Texts)
                0x04, // FC Parameter Set is 01 (Grade Text Group ID for RGB value)
                0x00 // Number of FC Parameters is 0
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.FcParameterSet, 0x00, data), "Fetch.FcGradeColour");
        }

        public void FetchFpInstallDataRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                (byte) pump.AllInstallParIds.Count
            };
            foreach (byte installPar in pump.AllInstallParIds)
            {
                data.Add(Bcd1(installPar));
            }

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpInstallData, 0x00, data), "Fetch.FpInstallData");
        }

        public void FetchFpStatusRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpStatus, 0x03, data), "Fetch.FpStatus");
        }

        public void FetchFpSupTransBufferStatusRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpSupTransBufStatus, 0x03, data), "Fetch.FpSupervisedTransactionBufferStatus");
        }

        public void FetchFpUnSupTransBufferStatusRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpUnSupTransBufStatus, 0x03, data), "Fetch.FpUnSupervisedTransactionBufferStatus");
        }

        public void FetchFpInfoRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                0x01, // Number of FP Info Items
                Bcd1(0x02) // Grade Prices
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpInfo, 0x01, data), "Fetch.FpInfo"); // Grade Prices
        }

        public void FetchFpMeterStatusRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpPumpGradeTotals, 0x01, data), "Fetch.FpPumpGradeTotals"); // Meter status
        }

        public void FetchFcInstallStatusRequest()
        {
            SendDomsMessage(ConstructMessage(DomsMessageCode.FcInstallStatus, 0x00, new byte[0]), "Fetch.FcInstallStatus");
        }

        public void OpenFpRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(_posId),
                0x00 // Operation Mode zero
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.OpenFp, 0x00, data), "OpenFuellingPoint");
        }

        public void CloseFpRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.CloseFp, 0x00, data), "CloseFuellingPoint");
        }

        public bool LoginRequest(string accessCode, uint countryCode, string posVersionId, IList<DomsMessageId> unsolMsgs)
        {
            var data = new List<byte>
            {
                (byte) accessCode.Length
            };

            data.AddRange(AsciiBytes(accessCode, accessCode.Length));
            data.AddRange(Bcd(countryCode, 2));
            data.Add((byte)posVersionId.Length);
            data.AddRange(AsciiBytes(posVersionId, posVersionId.Length));
            if (unsolMsgs.Count > 0)
            {
                data.Add(1); // 1 Parameter
                data.Add(Bcd1(0x01)); // Unsolicited Message List
                data.Add((byte)(1 + 3 * unsolMsgs.Count));
                data.Add((byte)unsolMsgs.Count);
                foreach (DomsMessageId msg in unsolMsgs)
                {
                    data.AddRange(ToTwoByte((int)msg.MsgCode));
                    data.Add(msg.Subc);
                }
            }
            else
            {
                data.Add(0);
            }

            return SendDomsMessage(ConstructMessage(DomsMessageCode.FcLogon, 0x01, data), "LoginDOMS");
        }

        public void ReserveRequest(DomsSetupPump pump, uint limit, byte posId)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(posId),
                0x01, // One Auth Param
                Bcd1(13), // Start Limit e
                0x02 // Money preset limit
            };
            data.AddRange(Bcd(limit, 5));
            SendDomsMessage(ConstructMessage(DomsMessageCode.ReserveFp, 0x01, data), "Reserve");
        }

        public void AuthoriseRequest(DomsSetupPump pump, uint limit, byte posId, byte smId = 0)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(posId),
                0x02, // Two Auth Param
                Bcd1(13), // Start Limit e
                0x02 // Money preset limit
            };
            data.AddRange(Bcd(limit, 5));
            data.AddRange(new byte[] { 0x08, Bcd1(posId) }); // Auto-Lock Transaction, on Idle

            if (smId != 0)
            {
                data[2] = 0x03;  // Three Auth Param
                data.AddRange(new byte[] { 0x01, Bcd1(smId) });
            }
            SendDomsMessage(ConstructMessage(DomsMessageCode.AuthoriseFp, 0x02, data), "Authorise");
        }

        public void CancelAuthoriseRequest(DomsSetupPump pump, byte posId)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(posId)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.CancelFpAuth, 0x00, data), "CancelAuthorise");
        }

        public void ReadLockRequest(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId)
        {
            ReadLockRequestFpUnSup(pump, transaction, posId);
        }

        public void ReadLockRequestFpUnSup(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId)
        {
            ReadLockRequestXxx(pump, transaction, posId);
        }

        private void ReadLockRequestXxx(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId, DomsMessageCode msgCode = DomsMessageCode.FpUnSupTrans, byte subC = 0x00)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };
            data.AddRange(Bcd((uint)transaction.SequenceNumber, 2));
            data.Add(Bcd1(posId));
            data.Add(0x02); // Two Transaction Parameters
            data.Add(Bcd1(51)); // = FcGradeId
            data.Add(Bcd1(56)); // = FcGradeOptionNumber
            if (msgCode == DomsMessageCode.FpUnSupTrans)
            {
                data.Add(0x00); // Zero Payment Control Parameters
            }
            SendDomsMessage(ConstructMessage(msgCode, subC, data), "Lock/ReadTransaction");
        }

        public void ReadLockRequestFpSup(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId)
        {
            ReadLockRequestXxx(pump, transaction, 0, DomsMessageCode.FpSupTrans);
        }

        public void UnlockRequest(DomsSetupPump pump, uint seqNo, byte posId)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(posId)
            };
            data.AddRange(Bcd(seqNo, 2));
            SendDomsMessage(ConstructMessage(DomsMessageCode.UnlockFpUnSupTrans, 0x00, data), "UnlockTransaction");
        }

        public void UnlockRequest(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId)
        {
            UnlockRequest(pump, (uint)transaction.SequenceNumber, posId);
        }

        public void ClearTransactionRequest(DomsSetupPump pump, DomsSetupTransaction transaction, byte posId)
        {
            ClearTransactionRequest(pump.FpId, transaction.SequenceNumber, transaction.Volume, transaction.Amount, posId);
        }

        public void ClearTransactionRequest(byte fpId, int seqNum, uint volume, uint amount, byte posId)
        {
            var data = new List<byte>
            {
                Bcd1(fpId),
                Bcd1(posId)
            };
            data.AddRange(Bcd((uint)seqNum, 2));
            data.AddRange(Bcd(volume, 3));
            data.AddRange(Bcd(amount, 3));
            SendDomsMessage(ConstructMessage(DomsMessageCode.ClearFpUnSupTrans, 0x00, data), "ClearTransaction");
        }

        public void EmergencyStopRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(_posId)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.EstopFp, 0x00, data), "EmergencyStop");
        }

        public void CancelEmergencyStopRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(_posId)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.CancelFpEstop, 0x00, data), "CancelEmergencyStop");
        }

        public void FetchFpFuellingDataRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.FpFuellingData, 0x00, data), "FpFuellingData");
        }

        public void FetchFpStatusHeartbeat(byte fpId = 0)
        {
            var data = new List<byte>
            {
                Bcd1(fpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpStatus, 0x00, data), "Fetch.FpStatus.Heartbeat");
        }

        public void FetchFpErrorRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId)
            };

            SendDomsMessage(ConstructMessage(DomsMessageCode.FpError, 0x00, data), "FpError");
        }

        public void ClearFpErrorRequest(DomsSetupPump pump)
        {
            var data = new List<byte>
            {
                Bcd1(pump.FpId),
                Bcd1(pump.ErrorCode)
            };
            SendDomsMessage(ConstructMessage(DomsMessageCode.ClearFpError, 0x00, data), "ClearFpError");
        }

    }
}