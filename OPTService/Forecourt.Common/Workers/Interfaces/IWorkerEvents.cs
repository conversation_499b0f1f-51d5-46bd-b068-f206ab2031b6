using Forecourt.Core.Enums;

namespace OPT.Common.Workers.Interfaces
{
    public delegate void PushChangeDelegate(EventType eventType, string itemId = null, string additionalData = null);

    public interface IWorkerEvents
    {
        event PushChangeDelegate PushChangeEvent;

        void PushChange(EventType eventType, string itemId = null, string additionalData = null);
        void PushChange(params EventType[] eventType);
    }
}
