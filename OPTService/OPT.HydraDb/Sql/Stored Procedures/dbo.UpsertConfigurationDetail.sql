USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('UpsertConfigurationDetail') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE UpsertConfigurationDetail
END
GO

CREATE PROCEDURE [dbo].UpsertConfigurationDetail
  @key NVARCHAR(255),
  @value NVARCHAR(max),
  @header NVARCHAR(255) = 'Site Controller',
  @category NVARCHAR(255) = 'GENERAL'
AS
BEGIN
	BEGIN TRY
		BEGIN TRANSACTION
			DECLARE @idHeader INT = (SELECT [Id] FROM dbo.ConfigurationHeader WHERE [Description] = @header);
			DECLARE @idCategory INT = (SELECT [Id] FROM dbo.ConfigurationCategory WHERE [Category] = @category);

			UPDATE dbo.[ConfigurationDetail] WITH (UPDLOCK, SERIALIZABLE) 
			SET [Value] = @value,
				[ModifiedDateTime] = GETDATE()
			WHERE HeaderId = @idHeader
				AND CategoryId = @idCategory
				AND [KEY] = @key
 
			IF @@ROWCOUNT = 0
			BEGIN
				INSERT dbo.ConfigurationDetail ([HeaderId],[CategoryId],[Key],[Value],[ModifiedDateTime]) 
				VALUES (@idHeader, @idCategory, @key, @value,  GETDATE())
			END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW
	END CATCH
END
GO

GRANT EXECUTE ON UpsertConfigurationDetail TO UserRole
GO
