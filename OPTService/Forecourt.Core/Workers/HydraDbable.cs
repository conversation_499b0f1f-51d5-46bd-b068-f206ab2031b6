using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Runtime.CompilerServices;

namespace Forecourt.Core.Workers
{
    /// <inheritdoc/>
    public abstract class HydraDbable<THydraDb> : OPT.Common.Workers.HydraDbable<THydraDb> where THydraDb : HydraDb.Interfaces.IHydraDb
    {
        /// <summary>
        /// All pumps
        /// </summary>
        protected IPumpCollection AllPumps { get; }

        /// <summary>
        /// Worker used to send notifications
        /// </summary>
        protected INotificationWorker<EventType> NotificationWorker => GetWorker<INotificationWorker<EventType>>();


        /// <inheritdoc />
        protected HydraDbable(THydraDb hydraDb, IHtecLogger logger, IPumpCollection allPumps = null, IConfigurationManager configurationManager = null, string name = null, int? id = null, ITimerFactory timerFactory = null, string defaultTimerInterval = null)
        : base(hydraDb, logger, configurationManager, name, id, timerFactory, defaultTimerInterval)
        {
            AllPumps = allPumps;
        }

        /// <inheritdoc />
        protected HydraDbable(THydraDb hydraDb, IHtecLogManager logManager, string loggerName, IPumpCollection allPumps = null, IConfigurationManager configurationManager = null, string name = null, int? id = null, ITimerFactory timerFactory = null, string defaultTimerInterval = null, bool useXmlConfigurator = false, ILogFormatter logFormatter = null)
        : base(hydraDb, logManager, loggerName, configurationManager, name, id, timerFactory, defaultTimerInterval, useXmlConfigurator, logFormatter)
        {
            AllPumps = allPumps;
        }

        /// <summary>
        /// Wraps the <see cref="Loggable"/> counterpart methods, using <see cref="IMessageTracking"/> rather than simple string logging reference
        /// </summary>
        /// <param name="action">Action delegate to execute</param>
        /// <param name="message"><see cref="IMessageTracking"/> instance</param>
        /// <param name="methodName">Calling method name</param>
        protected void DoAction(Action action, IMessageTracking message = null, [CallerMemberName] string methodName = null)
        {
            message ??= new MessageTracking();
            DoAction(action, message.FullId, methodName: methodName);
        }

        /// <summary>
        /// Wraps the <see cref="Loggable"/> counterpart methods, using <see cref="IMessageTracking"/> rather than simple string logging reference
        /// </summary>
        /// <param name="action">Function delegate to execute, returning a <see cref="Result"/></param>
        /// <param name="message"><see cref="IMessageTracking"/> instance</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns><see cref="Result"/> instance</returns>
        protected Result DoAction(Func<Result> action, IMessageTracking message = null, [CallerMemberName] string methodName = null)
        {
            message ??= new MessageTracking();
            return DoAction(action, message.FullId, methodName: methodName);
        }

        /// <summary>
        /// Wraps the <see cref="Loggable"/> counterpart methods, using <see cref="IMessageTracking"/> rather than simple string logging reference
        /// </summary>
        /// <param name="action">Function delegate to execute, returning a <see cref="Result{T}"/></param>
        /// <param name="message"><see cref="IMessageTracking"/> instance</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns><see cref="Result{T}"/> instance</returns>
        protected Result<T> DoAction<T>(Func<Result<T>> action, IMessageTracking message = null, [CallerMemberName] string methodName = null)
        {
            message ??= new MessageTracking();
            return DoAction(action, message.FullId, methodName: methodName);
        }

        /// <summary>
        /// Add a trailing 's' depending on the supplied count 
        /// </summary>
        /// <param name="word">Word</param>
        /// <param name="count">Count of items</param>
        /// <returns>The word pluralised, or not</returns>
        public static string Pluralise(string word, int count) => $"{word}{(count <= 1 ? string.Empty : "s")}";
    }
}
