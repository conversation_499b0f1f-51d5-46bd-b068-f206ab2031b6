USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetReceipt') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetReceipt
END
GO

CREATE PROCEDURE [dbo].[GetReceipt](
 @cardNumber varchar(20),
 @TransactionNumber BIGINT = NULL,
 @includeExpired BIT = 0
) AS
BEGIN
	select CardNumber,
	CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, NULL) = ReceiptHash) THEN ReceiptContent
	WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, 0) = ReceiptHash) THEN ReceiptContent
	ELSE cast('Error with receipt data hash.' AS VARCHAR(MAX)) END AS ReceiptContent,
	Expiry,
	OPT,
	TransactionNumber,
	Amount,
	TransactionTime,
	PrintedCount
	from Receipts
	WHERE CardNumber = @cardNumber
	AND (@TransactionNumber = null OR TransactionNumber = @TransactionNumber)
	AND (@includeExpired = 1 OR Expiry > GETDATE());
END 
GO

GRANT EXECUTE ON GetReceipt TO UserRole
GO


