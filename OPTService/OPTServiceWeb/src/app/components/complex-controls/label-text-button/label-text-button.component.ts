import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-label-text-button',
  templateUrl: './label-text-button.component.html',
  styleUrls: ['./label-text-button.component.css']
})
export class LabelTextButtonComponent implements OnInit {

  public form: FormGroup;
  public control : FormControl;

  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() placeHolderText: string = '';
  @Input() buttonText: string = 'Save';
  @Input() validationErrorText: string = 'Please provide a valid value';
  @Input() errorInActionText: string = 'Field couldn\'t be updated';
  @Input() appendText: string = '';
  @Input() controlName: string = '';  
  @Input() errorInAction: boolean = false;
  @Input() labelColClass: string = 'col-2';
  @Input() textColClass: string = 'col-2';
  @Input() buttonColClass: string = 'col-auto';
  @Input() disabled: boolean = false;
  @Input('id') private _id: string;
  @Input() maxLength: number = 524288; //The default value for HTML input maxlength attribute
  
  @Output() action= new EventEmitter();

  public get id(): string {
    return this._id || this.controlName || '';
  }

  public get fieldFeedbackId(): string {
    return `${this.id}FieldFeedback`;
  }

  public get buttonId(): string {
    return `${this.id}Btn`;
  }

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
  }

  doAction(event?: Event): void {
    this.action.emit(event);
  }
}
