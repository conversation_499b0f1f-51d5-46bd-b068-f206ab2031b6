using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations
{
    /// <summary>
    /// Base class for stored procedure migrations.
    /// </summary>
    public abstract class MigrationableProgrammibility : Migrationable
    {
        private readonly IList<string> _functions;
        private readonly IList<string> _sprocs;

        /// <summary>
        /// Creates a new instance of the <see cref="MigrationableProgrammibility"/> class.
        /// </summary>
        /// <param name="functionScripts">List of files containing function scripts. File extension is not required.</param>
        /// <param name="sprocScripts">List of files containing stored procedure scripts. File extension is not required.</param>
        /// <param name="logger">The abstracted logger.</param>
        /// <param name="dbExecutorFactory">The factory for database connectivity.</param>
        /// <param name="fileSystem">The abstracted file system.</param>
        protected MigrationableProgrammibility(IList<string> functionScripts, IList<string> sprocScripts, ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) 
            : base(logger, dbExecutorFactory, fileSystem)
        {
            _functions = functionScripts ?? new List<string>();
            _sprocs = sprocScripts ?? new List<string>();
        }

        /// <inheritdoc />
        public override void Up()
        {
            BackupObjects(_functions, EmbeddedResourcePathFunctions);

            BackupObjects(_sprocs, EmbeddedResourcePathStoredProcedures);
        }

        private void BackupObjects(IList<string> dbObjects, string resourcePath)
        {
            var result = BackupDbObjects(GetPrefix(), dbObjects.ToArray());
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in dbObjects)
            {
                Execute.EmbeddedScript($"{resourcePath}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc />
        public override void Down()
        {
            RestoreDbObjects(GetPrefix(), _functions.Reverse().ToArray());

            RestoreDbObjects(GetPrefix(), _sprocs.Reverse().ToArray());
        }

        private string GetPrefix()
        {
            return GetType().Name;
        }
    }
}
