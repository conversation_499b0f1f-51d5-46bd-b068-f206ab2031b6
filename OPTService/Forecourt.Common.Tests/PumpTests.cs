using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces;
using NSubstitute;
using System.Collections.Generic;
using System.Collections.Specialized;
using Xunit;

namespace OPT.Common.Tests
{
    public class PumpTests
    {
        private const byte PumpNumber = 1;
        private const byte Grade = 2;
        private const byte OtherGrade = 1;
        private const byte Hose = 3;
        private const ushort Ppu = 1269;
        private const uint PaymentAmount = 10000;
        private const uint ClearedAmount = 5000;
        private const string Tid = "99979901";
        private const string CardNumber = "1234********6789";
        private const string CardProductName = "Visa";
        private const int PaymentTimeout = 1;
        private const string LoggerName = "Logger";
        private readonly IHydraDb _hydraDb;
        private readonly IOpt _opt;
        private readonly IPump _pump;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;
        private readonly NameValueCollection _appSetting;

        public PumpTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _opt = Substitute.For<IOpt>();
            _logManager = Substitute.For<IHtecLogManager>();
            _opt.PaymentTimeoutInSeconds.Returns(PaymentTimeout);
            _configurationManager = Substitute.For<IConfigurationManager>();
            _appSetting = new NameValueCollection();
            _configurationManager.AppSettings.Returns(_appSetting);
            
            _pump = CreateDefaultPump();
            _pump.OpenPump(false);
        }

        [Fact]
        public void test_initial_state()
        {
            // Arrange

            // Act

            // Assert
            _pump.Opt.Should().Be(_opt);
            _pump.Number.Should().Be(PumpNumber);
            _pump.Tid.Should().BeEquivalentTo(Tid);
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.IsSecAuthRequested.Should().BeFalse();
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsDelivering.Should().BeFalse();
            _pump.IsDelivered.Should().BeFalse();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.HasOptPayment.Should().BeFalse();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.PumpIsClosed.Should().BeFalse();
            _pump.OptUse.Should().BeTrue();
            _pump.CardNumber.Should().BeNull();
            _pump.CardProductName.Should().BeNull();
            _pump.ClearedAmount.Should().Be(0);
            _pump.ThirdPartyWait.Should().BeFalse();
            _pump.ClosePending.Should().BeFalse();
            _pump.DeliveredHose.Should().Be(0);
            _pump.DeliveredPpu.Should().Be(0);
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanCancelPayment.Should().BeTrue();
            _pump.CanClearPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.CanSetKioskUse.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(0);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        [Fact]
        public void test_initial_state_without_opt()
        {
            // Arrange
            IPump pump = new Pump(PumpNumber, _hydraDb, _logManager, LoggerName, null, null, _configurationManager);

            // Act

            // Assert
            pump.Opt.Should().BeNull();
            pump.Number.Should().Be(PumpNumber);
            pump.Tid.Should().BeNull();
        }

        [Fact]
        public void test_insert_card()
        {
            // Arrange

            // Act
            _pump.CardInserted();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_and_send_anpr()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_send_anpr_and_anpr_ok()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_send_anpr_and_anpr_not_ok()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(false);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_and_reset_pump()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.ResetPump();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.CanInsertCard.Should().BeTrue();
        }
        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_and_add_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }
        
        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_and_add_payment_with_third_party_wait()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount, true);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.ThirdPartyWait.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_with_third_party_wait_requesting_and_is_authorised()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount, true);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out _);

            // Assert
            result.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.ThirdPartyWait.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_with_third_party_wait_requesting_third_party_auth_and_is_authorised()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount, true);
            _pump.Requesting(new List<byte> { Grade });
            _pump.ThirdPartyAuth();
            bool result = _pump.IsAuthorised(out uint amount);

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.ThirdPartyWait.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_with_allowed_grades_requesting_other_grade_and_is_authorised()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount, false, new List<byte> {Grade});
            _pump.Requesting(new List<byte> { OtherGrade });
            bool result = _pump.IsAuthorised(out _);

            // Assert
            result.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_with_allowed_grades_requesting_grade_and_is_authorised()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount, false, new List<byte> {Grade});
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_and_cancel_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.CanInsertCard.Should().BeTrue();
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_and_cancel_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_and_requesting()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.Requesting(new List<byte> { Grade });

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_and_idle()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.Requesting(new List<byte> { Grade });
            _pump.Idle();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_and_is_authorised()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_and_idle()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);
            _pump.Idle();

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeFalse();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_and_delivering()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.AddPayment(PaymentAmount);
            _pump.SetSecAuthResponse(true);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);
            _pump.Delivering();

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.InDelivery);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsDelivering.Should().BeTrue();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_delivering_and_delivered()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.AddPayment(PaymentAmount);
            _pump.SetSecAuthResponse(true);
            _pump.Requesting(new List<byte> { Grade });
             bool result = _pump.IsAuthorised(out uint amount);
            _pump.Delivering();
            _pump.Delivered();

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.Complete);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsDelivering.Should().BeFalse();
            _pump.IsDelivered.Should().BeTrue();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_delivering_delivered_and_set_delivered_hose()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.AddPayment(PaymentAmount);
            _pump.SetSecAuthResponse(true);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);
            _pump.Delivering();
            _pump.Delivered();
            _pump.SetDeliveredHose(Hose, Ppu);

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.Complete);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsDelivering.Should().BeFalse();
            _pump.IsDelivered.Should().BeTrue();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeTrue();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.DeliveredHose.Should().Be(Hose);
            _pump.DeliveredPpu.Should().Be(Ppu);
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void
            test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_delivering_delivered_set_delivered_hose_and_idle()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.AddPayment(PaymentAmount);
            _pump.SetSecAuthResponse(true);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);
            _pump.Delivering();
            _pump.Delivered();
            _pump.SetDeliveredHose(Hose, Ppu);
            _pump.Idle();

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.Complete);
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.IsDelivering.Should().BeFalse();
            _pump.IsDelivered.Should().BeTrue();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeTrue();
            _pump.HasOptPayment.Should().BeFalse();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.DeliveredHose.Should().Be(Hose);
            _pump.DeliveredPpu.Should().Be(Ppu);
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void
            test_insert_card_send_anpr_anpr_ok_add_payment_requesting_is_authorised_delivering_delivered_set_delivered_hose_idle_and_clear_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.Requesting(new List<byte> { Grade });
            bool result = _pump.IsAuthorised(out uint amount);
            _pump.Delivering();
            _pump.Delivered();
            _pump.SetDeliveredHose(Hose, Ppu);
            _pump.Idle();
            _pump.ClearPayment(CardNumber, CardProductName, ClearedAmount);

            // Assert
            result.Should().BeTrue();
            amount.Should().Be(PaymentAmount);
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsSecAuthApproved.Should().BeFalse();
            _pump.IsDelivering.Should().BeFalse();
            _pump.IsDelivered.Should().BeFalse();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.HasOptPayment.Should().BeFalse();
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.CardNumber.Should().BeEquivalentTo(CardNumber);
            _pump.CardProductName.Should().BeEquivalentTo(CardProductName);
            _pump.ClearedAmount.Should().Be(ClearedAmount);
            _pump.DeliveredHose.Should().Be(Hose);
            _pump.DeliveredPpu.Should().Be(Ppu);
            _pump.CanInsertCard.Should().BeTrue();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_and_close_pump()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.ClosePump();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.InUse.Should().BeTrue();
            _pump.ClosePending.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_close_pump_and_cancel_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.ClosePump();
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Closed);
            _pump.PumpState.Should().Be(PumpStateType.Closed);
            _pump.InUse.Should().BeFalse();
            _pump.PumpIsClosed.Should().BeTrue();
            _pump.ClosePending.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_close_pump_open_pump_and_cancel_payment()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.ClosePump();
            _pump.OpenPump();
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.InUse.Should().BeFalse();
            _pump.PumpIsClosed.Should().BeFalse();
            _pump.ClosePending.Should().BeFalse();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
        }

        [Fact]
        public void test_set_kiosk_mode_current()
        {
            // Arrange

            // Act
            _pump.SetKioskOnly();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.KioskUse.Should().BeTrue();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_set_kiosk_mode_default()
        {
            // Arrange

            // Act
            _pump.SetKioskOnly(true);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeTrue();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
            _pump.DefaultOutsideOnly.Should().BeFalse();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_mode_current()
        {
            // Arrange

            // Act
            _pump.SetMixed();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_mode_default()
        {
            // Arrange

            // Act
            _pump.SetMixed(true);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
            _pump.DefaultOutsideOnly.Should().BeFalse();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_mode_in_pod_mode()
        {
            // Arrange
            _opt.IsInPodMode.Returns(true);

            // Act
            _pump.SetMixed();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.CanSetKioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_mode_set_kiosk_use_and_idle_in_pod_mode()
        {
            // Arrange
            _opt.IsInPodMode.Returns(true);

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Idle();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.KioskUse.Should().BeFalse();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.CanSetKioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_mode_and_set_kiosk_use_in_pod_mode()
        {
            // Arrange
            _appSetting[AdvancedConfig.ConfigKeyPosType] = "Retalix";
            _hydraDb.GetSiteInfo().Returns(new SiteInfo());

            _opt.IsInPodMode.Returns(true);

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.KioskUse.Should().BeTrue();
            _pump.DefaultKioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.DefaultOutsideOnly.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.CanSetKioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_kiosk_only()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.Kiosk);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.KioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_opt_use()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.OptUse);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.Mixed.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_close()
        {
            // Arrange
            _appSetting[AdvancedConfig.ConfigKeyPosType] = "Retalix";
            _hydraDb.GetSiteInfo().Returns(new SiteInfo());
            var advConfig = new AdvancedConfig(_hydraDb, _logManager.GetLogger(LoggerName), _configurationManager);
            _hydraDb.AdvancedConfig.Returns(advConfig);

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.Close);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Closed);
            _pump.PumpState.Should().Be(PumpStateType.Closed);
            _pump.PumpIsClosed.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_close_and_open()
        {
            // Arrange
            _appSetting[AdvancedConfig.ConfigKeyPosType] = "Retalix";
            _hydraDb.GetSiteInfo().Returns(new SiteInfo());
            var advConfig = new AdvancedConfig(_hydraDb, _logManager.GetLogger(LoggerName), _configurationManager);
            _hydraDb.AdvancedConfig.Returns(advConfig);

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.Close);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.Open);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.PumpIsClosed.Should().BeFalse();
        }

        [Fact]
        public void test_hydra_pos_command_mix_mode()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.MixMode);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.Mixed.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_mix_mode_and_out_side()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.MixMode);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.OutSide);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_mix_mode_and_night_mode()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.MixMode);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.NightMode);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_kiosk_and_night_mode()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.Kiosk);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.NightMode);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Closed);
            _pump.PumpState.Should().Be(PumpStateType.Closed);
            _pump.KioskUse.Should().BeTrue();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.PumpIsClosed.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_mix_mode_and_evening_mode()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.MixMode);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.EveningMode);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeTrue();
        }

        [Fact]
        public void test_hydra_pos_command_kiosk_and_evening_mode()
        {
            // Arrange

            // Act
            bool result = _pump.HydraPosCommand(HydraPosCommand.Kiosk);
            bool result2 = _pump.HydraPosCommand(HydraPosCommand.EveningMode);

            // Assert
            result.Should().BeTrue();
            result2.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Closed);
            _pump.PumpState.Should().Be(PumpStateType.Closed);
            _pump.KioskUse.Should().BeTrue();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.PumpIsClosed.Should().BeTrue();
        }

        [Fact]
        public void test_set_mixed_default_and_hydra_pos_command_day_mode()
        {
            // Arrange

            // Act
            _pump.SetMixed(true);
            bool result = _pump.HydraPosCommand(HydraPosCommand.DayMode);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.KioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
        }

        [Fact]
        public void test_set_kiosk_default_and_hydra_pos_command_day_mode()
        {
            // Arrange

            // Act
            _pump.SetKioskOnly(true);
            bool result = _pump.HydraPosCommand(HydraPosCommand.DayMode);

            // Assert
            result.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.KioskUse.Should().BeTrue();
            _pump.Mixed.Should().BeFalse();
            _pump.OutsideOnly.Should().BeFalse();
        }

        [Fact]
        public void test_card_inserted_and_kiosk_use()
        {
            // Arrange
            _pump.SetMixed();

            // Act
            _pump.CardInserted();
            _pump.SetKioskUse();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.InUse.Should().BeTrue();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.CanSetKioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_card_inserted_kiosk_use_and_cancel_payment()
        {
            // Arrange
            _pump.SetMixed();

            // Act
            _pump.CardInserted();
            _pump.SetKioskUse();
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.InUse.Should().BeFalse();
            _pump.KioskUse.Should().BeTrue();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.CanSetKioskUse.Should().BeTrue();
        }
        [Fact]
        public void test_card_inserted_kiosk_use_idle_and_cancel_payment()
        {
            // Arrange
            _pump.SetMixed();

            // Act
            _pump.CardInserted();
            _pump.SetKioskUse();
            _pump.Idle();
            _pump.CancelPayment();

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.InUse.Should().BeFalse();
            _pump.KioskUse.Should().BeFalse();
            _pump.Mixed.Should().BeTrue();
            _pump.OutsideOnly.Should().BeFalse();
            _pump.OptUse.Should().BeTrue();
            _pump.CanAddPayment.Should().BeTrue();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.CanSetKioskUse.Should().BeTrue();
        }

        [Fact]
        public void test_nozzle_up()
        {
            // Arrange

            // Act
            _pump.Requesting(new List<byte> { Grade });

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        [Fact]
        public void test_nozzle_up_and_nozzle_down()
        {
            // Arrange

            // Act
            _pump.Requesting(new List<byte> { Grade });
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        [Fact]
        public void test_nozzle_up_and_take_fuel()
        {
            // Arrange

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeTrue();
            _pump.KioskUse.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
        }
        [Fact]
        public void test_nozzle_up_take_fuel_and_nozzle_down()
        {
            // Arrange

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeTrue();
            _pump.KioskUse.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_nozzle_up_take_fuel_and_delivering()
        {
            // Arrange

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Delivering();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeTrue();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeTrue();
            _pump.KioskUse.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.IsDelivering.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
        }

        [Fact]
        public void test_nozzle_up_take_fuel_delivering_and_delivered()
        {
            // Arrange

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Delivering();
            _pump.Delivered();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeTrue();
            _pump.KioskUse.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.IsDelivering.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
        }

        [Fact]
        public void test_nozzle_up_take_fuel_delivering_delivered_and_nozzle_down()
        {
            // Arrange

            // Act
            _pump.SetMixed();
            _pump.SetKioskUse();
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Delivering();
            _pump.Delivered();
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeTrue();
            _pump.KioskUse.Should().BeTrue();
            _pump.OptUse.Should().BeFalse();
            _pump.CanAddPayment.Should().BeFalse();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.IsDelivering.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
        }

        [Fact]
        public void test_offer_kiosk()
        {
            // Arrange
            _opt.Mode.Returns(OptModeType.OptModeMixed);

            // Act
            _pump.SetMixed();
            _pump.Requesting(new List<byte> { Grade });
            _pump.SetPaid(false);
            _pump.Authorised();
            _pump.Delivering();
            _pump.Delivered();
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.OfferKiosk);
            _pump.IsPaid.Should().BeFalse();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        [Fact]
        public void test_printer_error_kiosk_mode_returns_state_printer_error()
        {
            // Arrange
            _pump.SetKioskOnly(false, true);
            _opt.PrinterError.Returns(true);
            _opt.Mode.Returns(OptModeType.OptModeKioskOnly);

            // Act
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Delivering();
            _pump.Delivered();
            _pump.SetPaid(true);
            _pump.Opt.SetPrinterStatus(true);
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.KioskOnly);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.KioskUse);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        [Fact]
        public void test_printer_error_mixed_mode_returns_state_mixed_mode_printer_error()
        {
            // Arrange
            _pump.SetMixed(false, true);
            _opt.PrinterError.Returns(true);
            _opt.Mode.Returns(OptModeType.OptModeMixed);

            // Act
            _pump.Requesting(new List<byte> { Grade });
            _pump.Authorised();
            _pump.Delivering();
            _pump.Delivered();
            _pump.Opt.SetPrinterStatus(true);
            _pump.SetPaid(true);
            _pump.Idle();

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.Mixed);
            _pump.IsNozzleUp.Should().BeFalse();
            _pump.State.Should().Be(HydraPosPumpState.MixModePrintErr);
            _pump.IsPaid.Should().BeTrue();
            _pump.HasKioskPayment.Should().BeFalse();
        }

        #region Helpers

        private Pump CreateDefaultPump(byte pumpNumber = PumpNumber)
        {
            return new Pump(pumpNumber, _hydraDb, _logManager, LoggerName, _opt, Tid, _configurationManager);
        }

        #endregion      
    }
}
