using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using Microsoft.Owin.Cors;
using Microsoft.Owin.FileSystems;
using Microsoft.Owin.StaticFiles;
using OPT.Common;
using OPTService.Authentication;
using OPTService.Extensions;
using OPTServiceWeb;
using Owin;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web.Http;

namespace OPTService
{
    /// <summary>
    /// Owin startup class
    /// </summary>
    public class Startup : Loggable
    {
        public Startup(IHtecLogger logger, IConfigurationManager configurationManager) 
            : base(logger, configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }
        }

        /// <summary>
        /// Owin startup configuration handler
        /// </summary>
        /// <param name="appBuilder">The Owin app builder object</param>
        public void Configuration(IAppBuilder appBuilder)
        {
            appBuilder.UseApiKeyAuthentication(new ApiKeyAuthenticationOptions()
            {
                Provider = new ApiKeyAuthenticationProvider()
                {
                    OnValidateIdentity = ValidateApiKey,
                    OnGenerateClaims = GenerateClaims
                }
            });

            appBuilder.UseCors(CorsOptions.AllowAll);
            var config = new HttpConfiguration();
            config.Routes.MapHttpRoute("Default", "api/{controller}/{action}");
            appBuilder.UseWebApi(config);

            const string rootFolder = ".";

            ConfigureFileServer(appBuilder);

            var fileSystem = new PhysicalFileSystem(rootFolder);
            var options = new FileServerOptions
            {
                EnableDefaultFiles = true,
                FileSystem = fileSystem
            };

            appBuilder.UseFileServer(options);
            appBuilder.MapSignalR();
            UnityConfig.RegisterDependencyResolver(config);
            SwaggerConfig.Register(config, ConfigurationManager);
        }
       
        private async Task<IEnumerable<Claim>> GenerateClaims(ApiKeyGenerateClaimsContext context)
        {
            var claims = new Collection<Claim>();
            switch (context.ApiKey)
            {
                case ConfigConstants.WebUiApiKey:
                    claims.Add(new Claim(ClaimTypes.Role, "WebUi"));
                    break;
                case ConfigConstants.SwaggerApiKey:
                    claims.Add(new Claim(ClaimTypes.Role, "Swagger"));
                    break;
                case ConfigConstants.ThirdPartyApiKey:
                    claims.Add(new Claim(ClaimTypes.Role, "ThirdParty"));
                    break;
                case ConfigConstants.OrbisApiKey:
                    claims.Add(new Claim(ClaimTypes.Role, "Orbis"));
                    break;
                default:
                    claims.Add(new Claim(ClaimTypes.Role, "None"));
                    break;
            }
            return claims;
        }

        private async Task ValidateApiKey(ApiKeyValidateIdentityContext context)
        {
            switch (context.ApiKey)
            {
                case ConfigConstants.WebUiApiKey:
                case ConfigConstants.SwaggerApiKey:
                case ConfigConstants.ThirdPartyApiKey:
                case ConfigConstants.OrbisApiKey:
                    context.Validate();
                    break;
                default:
                    // TBD
                    break;
            }
            return;
        }

        /// <summary>
        /// Sets up the embedded file server to serve static http content
        /// </summary>
        /// <param name="appBuilder">The Owin app builder object</param>
        private void ConfigureFileServer(IAppBuilder appBuilder)
        {
            const string rootFolder = @".dist.OPTWebAdmin";
            const string defaultFile = "index.html";
            const string rootPath = "/";
            var optServiceWebAssembly = typeof(Dummy).Assembly;

            var options = new FileServerOptions
            {
                EnableDefaultFiles = true,
                FileSystem = new EmbeddedResourceFileSystem(optServiceWebAssembly, optServiceWebAssembly.GetName().Name + rootFolder),
                StaticFileOptions =
                {
                    ServeUnknownFileTypes = false,
                    OnPrepareResponse = (staticFileResponseContext) => staticFileResponseContext.AddResponseHeaders()
                },
                DefaultFilesOptions =
                {
                    DefaultFileNames = new[] { defaultFile }
                }
            };

            appBuilder.UseAngularServer(rootPath, Path.Combine(rootPath, defaultFile));
            appBuilder.UseFileServer(options);
        }
    }
}