namespace Forecourt.Core.Pump.Enums
{
    ///
    /// Name: PumpCommandType
    /// Description: Enumeration for commands to the Pump Simulator.
    ///
    public enum PumpCommandType
    {
        // ReSharper disable once UnusedMember.Global
        Vacant = 0,
        Claim = 1,
        Release = 2,
        Auth = 3,
        AuthWithLimit = 4,
        Stop = 5,
        AllStop = 6,
        CashOut = 7,
        Return = 8,
        PrePay = 9,
        CancelPrePay = 10,
        NewPrices = 11,
        NoStore = 12,
        EnableStore = 13
    }
}