USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetReceiptForOPT') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetReceiptForOPT
END
GO

CREATE PROCEDURE [dbo].[GetReceiptForOPT](
 @OPT varchar(1024)
) AS
BEGIN
	select CardNumber,
	CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, NULL) = ReceiptHash) THEN ReceiptContent
	WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, 0) = ReceiptHash) THEN ReceiptContent
	ELSE cast('Error with receipt data hash.' AS VARCHAR(MAX)) END AS ReceiptContent,
	Expiry,
	OPT,
	TransactionN<PERSON>ber,
	Amount,
	TransactionTime,
	PrintedCount
	from Receipts
	WHERE opt = @opt;
END 
GO

GRANT EXECUTE ON GetReceiptForOPT TO UserRole
GO
