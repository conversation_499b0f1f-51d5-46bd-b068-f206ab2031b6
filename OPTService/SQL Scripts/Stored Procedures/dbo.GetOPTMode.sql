USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[GetOPTMode]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE [GetOPTMode]
END
GO

CREATE PROCEDURE [dbo].[GetOPTMode] AS
 BEGIN
  SELECT OPT, Contactless, ReceiptHeader, ReceiptFooter, PlaylistFileName, LastLogTime FROM OPTMode
 END
GO

GRANT EXECUTE ON GetOPTMode TO UserRole
GO
