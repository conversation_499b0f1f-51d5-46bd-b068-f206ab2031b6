using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.Pos.Enums;
using System;

namespace Forecourt.Core.HydraDb.Models
{
    public class SiteInfo
    {
        public const SiteType DefaultSiteType = SiteType.Retail;
        public const PosType DefaultPosType = PosType.Retalix;

        public int ReceiptLayoutMode { get; }
        public string SiteName { get; }
        public string VatNumber { get; }
        public bool NozzleUpForKioskUse { get; }
        public bool UseReplaceNozzleScreen { get; }
        public int CurrencyCode { get; }
        public bool ForwardFuelPriceUpdate { get; }
        public short TillNumber { get; }
        public short FuelCategory { get; }
        public uint MaxFillOverride { get; }

        /// <summary>
        /// Default constructor
        /// </summary>
        public SiteInfo() { }

        /// <summary>
        /// Main constructor
        /// </summary>
        public SiteInfo
        (int mode, string siteName, string vatNumber, bool nozzleUpForKioskUse, bool useReplaceNozzleScreen, int currencyCode,
            bool forwardFuelPriceUpdate, int tillNumber, int fuelCategory, long maxFillOverride)
        {
            ReceiptLayoutMode = mode;
            SiteName = siteName;
            VatNumber = vatNumber;
            NozzleUpForKioskUse = nozzleUpForKioskUse;
            UseReplaceNozzleScreen = useReplaceNozzleScreen;
            CurrencyCode = currencyCode;
            ForwardFuelPriceUpdate = forwardFuelPriceUpdate;
            TillNumber = (short)tillNumber;
            FuelCategory = (short)fuelCategory;
            MaxFillOverride = (uint)maxFillOverride;
        }

        /// <summary>
        /// Split the (single) SiteName property into component Code and Name, based on the given separator
        /// </summary>
        /// <param name="separator">What separates the code and and parts, default a space</param>
        /// <returns>Tuple (code, name)</returns>
        public Tuple<string, string> SplitSiteName(string separator = " ")
        {
            var siteName = SiteName ?? string.Empty;
            if (string.IsNullOrEmpty(siteName))
            {
                return new Tuple<string, string>(string.Empty, string.Empty);
            }

            var idx = siteName.IndexOf(separator, 0, StringComparison.InvariantCultureIgnoreCase);

            return idx <= 0 ? new Tuple<string, string>(siteName, siteName) : new Tuple<string, string>(siteName.Substring(idx + 1), siteName.Substring(0, idx));
        }
    }
}