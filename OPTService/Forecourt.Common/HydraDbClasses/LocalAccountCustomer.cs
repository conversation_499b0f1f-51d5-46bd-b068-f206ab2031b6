using System.Collections.Generic;

namespace OPT.Common.HydraDbClasses
{
    public class LocalAccountCustomer
    {
        public string CustomerReference { get; }
        public string Name { get; }
        public bool TransactionsAllowed { get; private set; }
        public uint TransactionLimit { get; }
        public bool Pin { get; private set; }
        public bool PrintValue { get; private set; }
        public bool AllowLoyalty { get; private set; }
        public bool FuelOnly { get; }
        public bool RegistrationEntry { get; }
        public bool MileageEntry { get; }
        public bool PrePayAccount { get; }
        public bool LowCreditWarning { get; }
        public bool MaxCreditReached { get; }
        public bool CustomerExists { get; private set; }
        public uint Balance { get; private set; }
        public IList<LocalAccountCard> Cards { get; } = new List<LocalAccountCard>();

        public LocalAccountCustomer
        (string reference, string name, bool transactionsAllowed, long transLimit, bool pin, bool printValue,
            bool allowLoyalty, bool fuelOnly, bool registrationEntry, bool mileageEntry, bool prepayAccount, bool lowCreditWarning,
            bool maxCreditReached, long balance, bool customerExists)
        {
            CustomerReference = reference;
            Name = name;
            TransactionsAllowed = transactionsAllowed;
            TransactionLimit = (uint) transLimit;
            Pin = pin;
            PrintValue = printValue;
            AllowLoyalty = allowLoyalty;
            FuelOnly = fuelOnly;
            RegistrationEntry = registrationEntry;
            MileageEntry = mileageEntry;
            PrePayAccount = prepayAccount;
            LowCreditWarning = lowCreditWarning;
            MaxCreditReached = maxCreditReached;
            Balance = (uint) balance;
            CustomerExists = customerExists;
        }

        public void SetCards(IList<LocalAccountCard> cards)
        {
            Cards.Clear();
            ((List<LocalAccountCard>) Cards).AddRange(cards);
        }

        public void SetNotExists()
        {
            CustomerExists = false;
        }

        public void SetTransactionsAllowed(bool allowed)
        {
            TransactionsAllowed = allowed;
        }

        public void SetBalance(uint balance)
        {
            Balance = balance;
        }

        public void SetFlags(bool pin, bool printValue, bool allowLoyalty)
        {
            Pin = pin;
            PrintValue = printValue;
            AllowLoyalty = allowLoyalty;
        }
    }
}