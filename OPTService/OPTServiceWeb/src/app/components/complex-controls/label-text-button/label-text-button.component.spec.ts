import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ControlContainer, FormGroup, FormGroupDirective } from '@angular/forms';

import { LabelTextButtonComponent } from './label-text-button.component';

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('LabelTextButtonComponent', () => {
  let component: LabelTextButtonComponent;
  let fixture: ComponentFixture<LabelTextButtonComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [LabelTextButtonComponent],
      providers: [
        { provide: ControlContainer, useValue: fgd }
      ]
    })
    fixture = TestBed.createComponent(LabelTextButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  describe('fieldFeedbackId', () => {

    it('should set fieldFeedbackId when no ID set', () => {
      expect(component.fieldFeedbackId).toEqual('FieldFeedback');
    });

    it('should set fieldFeedbackId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.fieldFeedbackId).toEqual('inputFieldFeedback');
    });
  });
  
  describe('buttonId', () => {

    it('should set buttonId when no ID set', () => {
      expect(component.buttonId).toEqual('Btn');
    });

    it('should set buttonId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.buttonId).toEqual('inputBtn');
    });
  });

  describe('doAction', () => {

    it('should emit action', () => {
      spyOn(component.action, 'emit');

      component.doAction();

      expect(component.action.emit).toHaveBeenCalled();
    });
  });
});
