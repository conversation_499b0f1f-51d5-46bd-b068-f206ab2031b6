using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class GenericOptConfigDetails
    {
        public string ServiceAddress { get; set; }
        public List<EndPointDetails> EsocketEndPoints { get; set; }
        public string Timestamp { get; set; }
        public string TermCategory { get; set; }
        public string ProcCategory { get; set; }
        public List<CardAidDetails> CardAids { get; set; }
        public List<CardClessAidDetails> CardClessAids { get; set; }
        public List<CardClessDrlDetails> CardClessDrls { get; set; }
        public List<CardCapkDetails> CardCapks { get; set; }
        public List<FuelCardDetails> FuelCards { get; set; }
        public ContactlessDetails ContactlessDetails { get; set; }
        public List<TariffMappingDetails> TariffMappings { get; set; }
        public List<WashDetails> Washes { get; set; }
        public List<PredefinedAmountDetails> PredefinedAmounts { get; set; }
        public List<LoyaltyDetails> Loyalty { get; set; }
        public ReceiptLayoutModeDetails ReceiptLayoutMode { get; set; }
        public List<DiscountCardDetails> DiscountCards { get; set; }
    }
}
