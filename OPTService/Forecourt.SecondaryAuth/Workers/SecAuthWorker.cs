using Forecourt.Core.Configuration;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Core;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <summary>
    /// Base worker for (most) SecAuth workers
    /// </summary>
    [HasConfiguration]
    public abstract class SecAuthWorker : Connectable
    {
        /// <summary>
        /// All pumps
        /// </summary>
        protected IPumpCollection AllPumps { get; }

        /// <summary>
        /// ControllerWorker
        /// </summary>
        protected INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        private static string TrimLoggerName(string name, string tag = LoggerNamePrefix) =>
            name.StartsWith(tag) ? name.Substring(tag.Length) :
            name.EndsWith(tag) ? name.Substring(0, name.Length - tag.Length) : name;

        /// <summary>
        /// Config key for, SecAuth Cancellation Prompt
        /// </summary>
        public const string ConfigKeyPrefixSecAuthCancellationPrompt = Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + ConfigConstants.NameOptUpper + ":Prompt:Auth:Cancellation";

        /// <summary>
        /// Default vaue for, SecAuth Cancellation Prompt
        /// </summary>
        public const string DefaultValueSecAuthCancellationPrompt = "Authorisation has been Cancelled";

        /// <summary>
        /// Configurable value for, SecAuth Cancellation Prompt
        /// </summary>
        protected ConfigurableString ConfigValueSecAuthCancellationPrompt { get; set; }

        /// <summary>
        /// SecAuth Cancellation Prompt
        /// </summary>
        public string CancellationPrompt => ConfigValueSecAuthCancellationPrompt.GetValue();

        /// <summary>
        /// Config key for, SecAuth Strict Messaging
        /// </summary>
        public const string ConfigKeyPrefixSecAuthStrictMessaging = Constants.CategoryNameSecondaryAuthorisation + Constants.CategorySeparator + "Message:IsStrict:";

        /// <summary>
        /// Default vaue for, SecAuth Strict Messaging
        /// </summary>
        public const bool DefaultValueSecAuthStrictMessaging = true;

        /// <summary>
        /// Configurable value for, SecAuth Strict Messaging
        /// </summary>
        protected ConfigurableBool ConfigValueSecAuthStrictMessaging { get; set; }

        /// <summary>
        /// SecAuth Strict Messaging
        /// </summary>
        public bool IsStrictMessaging => ConfigValueSecAuthStrictMessaging.GetValue();

        /// <summary>
        /// Config key for, SecAuth Timed Out Response
        /// </summary>
        public const string ConfigKeyPrefixSecAuthTimedOutResponse = Constants.ConfigKeyPrefixSecAuthTimedOutResponse;

        /// <summary>
        /// Default vaue for, SecAuth Timed Out Response
        /// </summary>
        public const bool DefaultValueSecAuthTimedOutResponse = Constants.DefaultValueSecAuthTimedOutResponse;

        /// <summary>
        /// Configurable value for, SecAuth Timed Out Response
        /// </summary>
        protected ConfigurableBool ConfigValueSecAuthTimedOutResponse { get; set; }

        /// <summary>
        /// SecAuth Timed Out Response
        /// </summary>
        public bool TimedOutResponse => ConfigValueSecAuthTimedOutResponse.GetValue();

        /// <inheritdoc/>
        public SecAuthWorker(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker)
            : base(logManager, TrimLoggerName(loggerName).ConvertToPrefixedDottedName(LoggerNamePrefix), configurationManager)
        {
            AllPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));

            RegisterWorker(telemetryWorker ?? throw new ArgumentNullException(nameof(telemetryWorker)));

            ConfigValueSecAuthCancellationPrompt = new ConfigurableString(this, ConfigKeyPrefixSecAuthCancellationPrompt, DefaultValueSecAuthCancellationPrompt);
            ConfigValueSecAuthStrictMessaging = new ConfigurableBool(this, ConfigKeyPrefixSecAuthStrictMessaging, DefaultValueSecAuthStrictMessaging);
            ConfigValueSecAuthTimedOutResponse = new ConfigurableBool(this, ConfigKeyPrefixSecAuthTimedOutResponse, DefaultValueSecAuthTimedOutResponse);
        }
    }
}
