using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.Repositories.Interfaces;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Xml.Linq;
using Xunit;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Integration.Tests.Workers
{
    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class OriginalOptWorkerTests : IDisposable
    {
        private const string HydraId = "Hydra 1";
        private const string VersionString = "Version";
        private readonly IFromOptWorker _optWorker;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IDbExecutor _dbExecutor;
        private readonly IToOptWorker _toOptProxy;
        private readonly IListenerConnectionThreadXml _fromOptProxy;
        private readonly IOptHeartbeatWorker _heartbeatProxy;
        private readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOutWorker;
        private readonly ISecAuthIntegratorInTransient<IMessageTracking> _secAuthInWorker;
        private readonly ICarWashWorker _carWashWorker;
        private readonly IPumpWorker _hscWorker;
        private readonly IDomsWorker _domsWorker;
        private readonly IHydraPosWorker _hydraPosWorker;
        private readonly IRetalixPosWorker _retalixPosWorker;
        private readonly IThirdPartyPosWorker _thirdPartyPosWorker;
        private readonly IMediaChannelWorker _mediaChannelWorker;
        private readonly IControllerWorker _controllerWorker;
        private readonly IRetalixTransactionFile _retalixTransactionFile;
        private readonly IJournalWorker _journalWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IConfigUpdateWorker _configUpdateWorker;
        private readonly ILocalAccountWorker _localAccountWorker;
        private readonly IConfigurationManager _configurationManager;
        private readonly ICacheHelper _cacheHelper;
        private readonly ITimerFactory _timerFactory;
        private readonly IPaymentConfigIntegrator _paymentConfig;

        public OriginalOptWorkerTests()
        {
            _toOptProxy = Substitute.For<IToOptWorker>();
            _fromOptProxy = Substitute.For<IListenerConnectionThreadXml>();
            _heartbeatProxy = Substitute.For<IOptHeartbeatWorker>();
            _controllerWorker = Substitute.For<IControllerWorker>();
            _secAuthOutWorker = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();
            _secAuthInWorker = Substitute.For<ISecAuthIntegratorInTransient<IMessageTracking>>();
            _carWashWorker = Substitute.For<ICarWashWorker>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _domsWorker = Substitute.For<IDomsWorker>();
            _hydraPosWorker = Substitute.For<IHydraPosWorker>();
            _retalixPosWorker = Substitute.For<IRetalixPosWorker>();
            _thirdPartyPosWorker = Substitute.For<IThirdPartyPosWorker>();
            _mediaChannelWorker = Substitute.For<IMediaChannelWorker>();
            _retalixTransactionFile = Substitute.For<IRetalixTransactionFile>();
            _journalWorker = Substitute.For<IJournalWorker>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _configUpdateWorker = Substitute.For<IConfigUpdateWorker>();
            _localAccountWorker = Substitute.For<ILocalAccountWorker>();
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _dbExecutor = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(_dbExecutor);
            _logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _cacheHelper = Substitute.For<ICacheHelper>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();

            var configurationRepository = Substitute.For<IConfigurationRepository>();
            IHydraDb hydraDb = new Forecourt.Common.HydraDbClasses.HydraDb(_dbExecutorFactory, _logger, _telemetryWorker, _configurationManager, configurationRepository, _cacheHelper);
            var allOpts = new OptCollection(_logManager, "Opt", hydraDb, _configurationManager, Substitute.For<IPrinterHelper<IMessageTracking>>(), Substitute.For<IReceiptHelper>());
            var allPumps = new PumpCollection(_logManager, "Pump", hydraDb, allOpts, _configurationManager);

            _optWorker = new FromOptWorker(_toOptProxy, _heartbeatProxy, _fromOptProxy, _controllerWorker, _hscWorker, _secAuthInWorker, _secAuthOutWorker,
                _carWashWorker, _hydraPosWorker, _hydraPosWorker, _mediaChannelWorker, _journalWorker,
                _telemetryWorker, _configUpdateWorker, _localAccountWorker, hydraDb, _paymentConfig, allPumps, allOpts,
                _logManager, VersionString, _configurationManager, _timerFactory, Substitute.For<IGradeHelper>(), Substitute.For<IVatCalculator>());
        }

        #region Start tests

        [Fact]
        public void test_opt_worker_starts_with_default_settings()
        {
            // Arrange
            IPEndPoint fromOptEndPoint = new IPEndPoint(IPAddress.Any, 1262);
            IPEndPoint toOptEndPoint = new IPEndPoint(IPAddress.Any, 1263);
            IPEndPoint heartbeatEndPoint = new IPEndPoint(IPAddress.Any, 1264);
            // Act
            _optWorker.Start(HydraId);

            // Assert
            _toOptProxy.Received(1).Start(toOptEndPoint);
            _fromOptProxy.Received(1).Start(fromOptEndPoint);
            _heartbeatProxy.Received(1).Start(heartbeatEndPoint);
        }

        [Fact]
        public void test_opt_worker_starts_with_database_settings()
        {
            // Arrange
            OptEndPoints optEndPoints = new OptEndPoints("", "***************", 12341, 12342, 12343, 12344);
            _dbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(new List<OptEndPoints> {optEndPoints});

            IPEndPoint fromOptEndPoint = new IPEndPoint(IPAddress.Any, 12341);
            IPEndPoint toOptEndPoint = new IPEndPoint(IPAddress.Any, 12342);
            IPEndPoint heartbeatEndPoint = new IPEndPoint(IPAddress.Any, 12343);
            // Act
            _optWorker.Start(HydraId);

            // Assert
            _toOptProxy.Received(1).Start(toOptEndPoint);
            _fromOptProxy.Received(1).Start(fromOptEndPoint);
            _heartbeatProxy.Received(1).Start(heartbeatEndPoint);
        }

        #endregion

        #region Message Tests

        [Fact]
        public void test_opt_worker_receives_heartbeat_from_from_opt()
        {
            // Arrange
            var heartbeatRequest = HeartbeatMessage(optId: "ID 1");
            var heartbeatResponse = HeartbeatMessage(false, "*", "ID 1", "Success");
            // Act
            var result = _optWorker.OnMessageReceived(heartbeatRequest, 1);

            // Assert
            result.Should().NotBeNull();
            result.ToString().Should().Match(heartbeatResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_heartbeat_from_from_opt_then_heartbeat()
        {
            // Arrange
            var heartbeatRequest = HeartbeatMessage(optId: "ID 1");
            var heartbeatResponse = HeartbeatMessage(false, "*", "ID 1", "Success");
            // Act
            var result1 = _optWorker.OnMessageReceived(heartbeatRequest, 1);
            var result2 = _optWorker.OnMessageReceived(heartbeatRequest, 1);

            // Assert
            result1.Should().NotBeNull();
            result1.ToString().Should().Match(heartbeatResponse.ToString());
            result2.Should().NotBeNull();
            result2.ToString().Should().Match(heartbeatResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_heartbeat_from_from_opt_then_other_heartbeat()
        {
            // Arrange
            var heartbeatRequest1 = HeartbeatMessage(optId: "ID 1");
            var heartbeatResponse1 = HeartbeatMessage(false, "*", "ID 1", "Success");
            var heartbeatRequest2 = HeartbeatMessage(optId: "ID 2");
            var heartbeatResponse2 = HeartbeatMessage(false, "*", "ID 2", "Success");
            // Act
            var result1 = _optWorker.OnMessageReceived(heartbeatRequest1, 1);
            var result2 = _optWorker.OnMessageReceived(heartbeatRequest2, 2);

            // Assert
            result1.Should().NotBeNull();
            result1.ToString().Should().Match(heartbeatResponse1.ToString());
            result2.Should().NotBeNull();
            result2.ToString().Should().Match(heartbeatResponse2.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_signin_from_from_opt()
        {
            // Arrange
            var signInRequest = SignInOutMessage(optId: "ID 1");
            var signInResponse = SignInOutMessage(false, hydraId: "*", optId: "ID 1", result: "Success", versionString: VersionString);
            // Act
            var result = _optWorker.OnMessageReceived(signInRequest, 1);

            // Assert
            result.Should().NotBeNull();
            XElement response = signInResponse.Response;
            response.Element("Response")?.SetAttributeValue("Timestamp", $"{DateTime.Now:yyyyMMddHHmmss}");
            result.ToString().Should().Match(response.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_second_signin_from_from_opt()
        {
            // Arrange
            var signInRequest = SignInOutMessage(optId: "ID 1");
            var signInResponse = SignInOutMessage(false, hydraId: "*", optId: "ID 1", result: "Success", versionString: VersionString);

            // Act
            var result1 = _optWorker.OnMessageReceived(signInRequest, 1);
            var result2 = _optWorker.OnMessageReceived(signInRequest, 1);

            // Assert
            result1.Should().NotBeNull();
            XElement response = signInResponse.Response;
            response.Element("Response")?.SetAttributeValue("Timestamp", $"{DateTime.Now:yyyyMMddHHmmss}");
            result1.ToString().Should().Match(response.ToString());
            result2.Should().NotBeNull();
            result2.ToString().Should().Match(response.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_signin_and_signout_from_from_opt()
        {
            // Arrange
            var signInRequest = SignInOutMessage(optId: "ID 1");
            var signOutRequest = SignInOutMessage(signIn: false, optId: "ID 1");
            var signInResponse = SignInOutMessage(false, hydraId: "*", optId: "ID 1", result: "Success", versionString: VersionString);
            var signOutResponse = SignInOutMessage(false, false, "*", "ID 1", "Success");
            // Act
            var result1 = _optWorker.OnMessageReceived(signInRequest, 1);
            var result2 = _optWorker.OnMessageReceived(signOutRequest, 1);

            // Assert
            result1.Should().NotBeNull();
            XElement response = signInResponse.Response;
            response.Element("Response")?.SetAttributeValue("Timestamp", $"{DateTime.Now:yyyyMMddHHmmss}");
            result1.ToString().Should().Match(response.ToString());
            result2.Should().NotBeNull();
            result2.ToString().Should().Match(signOutResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_signout_from_from_opt_without_signin()
        {
            // Arrange
            var signOutRequest = SignInOutMessage(signIn: false, optId: "ID 1");
            var signOutResponse = SignInOutMessage(false, false, "*", "ID 1", "Success");
            // Act
            var result = _optWorker.OnMessageReceived(signOutRequest, 1);

            // Assert
            result.Should().NotBeNull();
            result.ToString().Should().Match(signOutResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_second_signout_from_from_opt()
        {
            // Arrange
            var signInRequest = SignInOutMessage(optId: "ID 1");
            var signInResponse = SignInOutMessage(false, hydraId: "*", optId: "ID 1", result: "Success", versionString: VersionString);
            var signOutRequest = SignInOutMessage(signIn: false, optId: "ID 1");
            var signOutResponse = SignInOutMessage(false, false, "*", "ID 1", "Success");
            // Act
            var result1 = _optWorker.OnMessageReceived(signInRequest, 1);
            var result2 = _optWorker.OnMessageReceived(signOutRequest, 1);
            var result3 = _optWorker.OnMessageReceived(signOutRequest, 1);

            // Assert
            result1.Should().NotBeNull();
            XElement response = signInResponse.Response;
            response.Element("Response")?.SetAttributeValue("Timestamp", $"{DateTime.Now:yyyyMMddHHmmss}");
            result1.ToString().Should().Match(response.ToString());
            result2.Should().NotBeNull();
            result2.ToString().Should().Match(signOutResponse.ToString());
            result3.Should().NotBeNull();
            result3.ToString().Should().Match(signOutResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_config_request()
        {
            // Arrange
            var configRequest = GetConfigMessage(optId: "ID 1");
            var configResponse =
                GetConfigMessage(false, "*", "ID 1", "Success", "<config *><hydraOpt id=\"ID 1\">*</hydraOpt>*</config>");
            // Act
            var result = _optWorker.OnMessageReceived(configRequest, 1);

            // Assert
            result.Should().NotBeNull();
            result.ToString().Should().Match(configResponse.ToString()); ;
        }

        [Fact]
        public void test_opt_worker_receives_config_request_with_opt_endpoints_from_database()
        {
            // Arrange
            OptEndPoints optEndPoints = new OptEndPoints("", "***************", 12341, 12342, 12343, 12344);
            _dbExecutor.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure)
                .Returns(new List<OptEndPoints> {optEndPoints});
            var configRequest = GetConfigMessage(optId: "ID 1");
            var configResponse = GetConfigMessage(false, "*", "ID 1", "Success",
                "<config *><hydraOpt id=\"ID 1\"><inbound ip=\"***************\" port=\"12342\" /><outbound ip=\"***************\" port=\"12341\" />" +
                "<heartbeat ip=\"***************\" port=\"12343\" />*</hydraOpt>*</config>");
            // Act
            var result = _optWorker.OnMessageReceived(configRequest, 1);

            // Assert
            result.Should().NotBeNull();
            result.ToString().Should().Match(configResponse.ToString());
        }

        [Fact]
        public void test_opt_worker_receives_config_request_with_esocket_endpoints_from_database()
        {
            // Arrange
            List<ESocketEndPoint> eSocketEndPoints = new List<ESocketEndPoint>
            {
                new ESocketEndPoint("***************", 12345),
                new ESocketEndPoint("***************", 6789)
            };
            _dbExecutor.Query<ESocketEndPoint>("GetESocketEndPoints", commandType: CommandType.StoredProcedure).Returns(eSocketEndPoints);
            var configRequest = GetConfigMessage(optId: "ID 1");
            var configResponse = GetConfigMessage(false, "*", "ID 1", "Success",
                "<config *><esocket><host ip=\"***************\" port=\"12345\" /><host ip=\"***************\" port=\"6789\" /></esocket>*</config>");
            // Act
            var result = _optWorker.OnMessageReceived(configRequest, 1);

            // Assert
            result.Should().NotBeNull();
            result.ToString().Should().Match(configResponse.ToString());
        }

        #endregion

        #region Message Construction

        /*
        private static XElement ConnectionsMessage
        (int allOptCount, int fromOptCount, int toOptCount, int heartbeatCount, int controlCount, int hydraPosCount,
            int thirdPartyCount, bool anprConnected, bool siteControllerConnected)
        {
            return new XElement("Controller",
                new XElement("Response", new XAttribute("Type", "Connections"), new XAttribute("Result", "Success"),
                    new XElement("Connections", new XAttribute("AllOPT", allOptCount), new XAttribute("FromOPT", fromOptCount),
                        new XAttribute("ToOPT", toOptCount), new XAttribute("Heartbeat", heartbeatCount),
                        new XAttribute("Control", controlCount), new XAttribute("HydraPOS", hydraPosCount),
                        new XAttribute("ThirdPartyPOS", thirdPartyCount), new XAttribute("ANPR", anprConnected),
                        new XAttribute("SiteController", siteControllerConnected))));
        }

        private static XElement MappingsMessage(string optId = null)
        {
            return new XElement("Controller",
                new XElement("Response", new XAttribute("Type", "Mappings"), new XAttribute("Result", "Success"),
                    new XElement("Mappings",
                        new XElement("OPTs",
                            optId == null
                                ? null
                                : new XElement("OPT", new XAttribute("Id", optId), new XAttribute("Connected", true),
                                    new XAttribute("Mode", "OptModeNotSet"), new XAttribute("Contactless", false), new XElement("Pumps"))),
                        new XElement("Pumps"), new XElement("TIDs"))));
        }

        private static XElement EndpointsMessage
        (IPEndPoint fromOpt, IPEndPoint toOpt, IPEndPoint heartbeat, IPEndPoint controller, IPEndPoint hydraPos,
            IPEndPoint thirdPartyPos, IPEndPoint pump, IPEndPoint anpr, IEnumerable<IPEndPoint> esocket)
        {
            return new XElement("Controller",
                new XElement("Response", new XAttribute("Type", "Endpoints"), new XAttribute("Result", "Success"),
                    new XElement("Endpoints",
                        new XElement("FromOPT", new XAttribute("IP", fromOpt.Address), new XAttribute("Port", fromOpt.Port)),
                        new XElement("ToOPT", new XAttribute("IP", toOpt.Address), new XAttribute("Port", toOpt.Port)),
                        new XElement("Heartbeat", new XAttribute("IP", heartbeat.Address), new XAttribute("Port", heartbeat.Port)),
                        new XElement("Controller", new XAttribute("IP", controller.Address), new XAttribute("Port", controller.Port)),
                        new XElement("HydraPOS", new XAttribute("IP", hydraPos.Address), new XAttribute("Port", hydraPos.Port)),
                        new XElement("ThirdPartyPOS", new XAttribute("IP", thirdPartyPos.Address),
                            new XAttribute("Port", thirdPartyPos.Port)),
                        new XElement("Pump", new XAttribute("IP", pump.Address), new XAttribute("Port", pump.Port)),
                        new XElement("ANPR", new XAttribute("IP", anpr.Address), new XAttribute("Port", anpr.Port)),
                        new XElement("ESocket",
                            esocket.Select(x => new XElement("Host", new XAttribute("IP", x.Address), new XAttribute("Port", x.Port)))))));
        }
        
        private static XElement InformationMessage(string message)
        {
            return new XElement("Controller",
                new XElement("Response", new XAttribute("Type", "Information"), new XAttribute("Result", "Success"),
                    new XAttribute("Message", message)));
        }
        */
        private static IMessageTracking<XElement> HeartbeatMessage(bool isRequest = true, string hydraId = null, string optId = null, string result = null)
        {
            var messageTracking = Substitute.For<IMessageTracking<XElement>>();

            messageTracking.Request.Returns(new XElement("HydraOPT",
                new XElement(isRequest ? "Request" : "Response", new XAttribute("Type", "Heartbeat"),
                    result == null ? null : new XAttribute("Result", result),
                    optId == null ? null : new XElement("OPT", new XAttribute("Id", optId)),
                    hydraId == null ? null : new XElement("Hydra", new XAttribute("Id", hydraId)))));
            
            return messageTracking;
        }

        private static IMessageTracking<XElement> SignInOutMessage
        (bool isRequest = true, bool signIn = true, string hydraId = null, string optId = null, string result = null,
            string versionString = null)
        {
            var messageTracking = Substitute.For<IMessageTracking<XElement>>();

            messageTracking.Request.Returns(new XElement("HydraOPT",
                new XElement(isRequest ? "Request" : "Response", new XAttribute("Type", signIn ? "SignIn" : "SignOut"),
                    isRequest || !signIn ? null : new XAttribute("Timestamp", ""), result == null ? null : new XAttribute("Result", result),
                    optId == null
                        ? null
                        : new XElement("OPT", new XAttribute("Id", optId),
                            versionString == null ? null : new XAttribute("SoftwareVersion", versionString)),
                    hydraId == null ? null : new XElement("Hydra", new XAttribute("Id", hydraId)))));
            
            return messageTracking;
        }

        private static IMessageTracking<XElement> GetConfigMessage
            (bool isRequest = true, string hydraId = null, string optId = null, string result = null, string config = null)
        {
            var messageTracking = Substitute.For<IMessageTracking<XElement>>();

            messageTracking.Request.Returns(new XElement("HydraOPT",
                new XElement(isRequest ? "Request" : "Response", new XAttribute("Type", "GetConfig"),
                    result == null ? null : new XAttribute("Result", result),
                    optId == null ? null : new XElement("OPT", new XAttribute("Id", optId)),
                    hydraId == null ? null : new XElement("Hydra", new XAttribute("Id", hydraId)),
                    config == null ? null : new XElement("Config", config))));
            return messageTracking;
        }

        #endregion

        #region IDisposable Support

        private bool _disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    _optWorker.Dispose();
                }

                _disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
        }

        #endregion
    }
}
