using CSharpFunctionalExtensions;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Forecourt.PaymentConfiguration.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.IO.Abstractions;

namespace Forecourt.PaymentConfiguration.Workers
{
    /// <summary>
    /// Add specific eSocket capabilities
    /// </summary>
    public abstract class ESocketWorker: HydraDbable
    {        
        /// <inheritdoc/>
        protected ESocketWorker(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IFileSystem fileSystem, IEsocketDb esocketDb, IContactlessProperties contactlessProperties, IConfigurationManager configurationManager = null) :
            base(logManager, loggerName.ConvertToPrefixedDottedName(LoggerNamePrefix), hydraDb, fileSystem, esocketDb, contactlessProperties, configurationManager)
        {
            if (esocketDb == null)
            {
                throw new ArgumentException(nameof(esocketDb));
            }

            if (contactlessProperties == null)
            {
                throw new ArgumentNullException(nameof(contactlessProperties));
            }
        }

        /// <inheritdoc/>
        protected override Result DoInitialise()
        {
            var fileLocations = HydraDb.GetFileLocations();

            ESocketDb.RefreshCustomDataOverrides(LoggingReference);
            ESocketDb.SetConnectionString(fileLocations.EsocketConnectionString, false);
            ESocketDb.SetConfigFile(fileLocations.EsocketConfigFile, false);
            ESocketDb.SetKeystoreFile(fileLocations.EsocketKeystoreFile, false);
            ESocketDb.SetDbUrl(fileLocations.EsocketDbUrl, false);
            ESocketDb.SetContactlessFile(fileLocations.ContactlessPropertiesFile);
            ESocketDb.SetUseConnectionString(fileLocations.EsocketUseConnectionString, false);
            ESocketDb.SetOverrideProperties(fileLocations.EsocketOverrideProperties, false);
            ESocketDb.SetOverrideKeystore(fileLocations.EsocketOverrideKeystore, false);
            ESocketDb.SetOverrideUrl(fileLocations.EsocketOverrideUrl, false);
            ESocketDb.SetOverrideContactless(fileLocations.EsocketOverrideContactless);

            return base.DoInitialise();
        }

        /// <inheritdoc/>
        protected override bool DoIsConnected()
        {
            return ESocketDb.ConnectionMade;
        }
    }
}
