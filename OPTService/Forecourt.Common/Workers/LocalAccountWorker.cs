using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Enums;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers.Interfaces;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace OPT.Common.Workers
{
    public class LocalAccountWorker : Connectable, ILocalAccountWorker
    {
        private IControllerWorker ControllerWorker => GetWorker<IControllerWorker>();

        private readonly IDictionary<string, LocalAccountCustomer> _customers = new ConcurrentDictionary<string, LocalAccountCustomer>();

        public ICollection<LocalAccountCustomer> Customers => _customers.Values;

        public LocalAccountWorker(IHydraDb hydraDb, IHtecLogger logger) : base(hydraDb, logger)
        {
            var customers = HydraDb.FetchLocalAccountCustomers();
            foreach (var customer in customers)
            {
                _customers.Add(customer.CustomerReference, customer);
            }
        }

        public void Add
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Add, reference {customerReference}, name {name}, " +
                                 $"transactions {(transactionsAllowed ? string.Empty : "not ")}allowed," +
                                 $" transaction limit {transactionLimit}" + (fuelOnly ? ", fuel only" : string.Empty) +
                                 (registrationEntry ? ", registration entry" : string.Empty) + (mileageEntry ? ", mileage entry" : string.Empty) +
                                 (prepayAccount ? ", pre-pay account" : string.Empty) + (lowCreditWarning ? ", low credit warning" : string.Empty) +
                                 (maxCreditReached ? ", max credit reached" : string.Empty));
                var customer = new LocalAccountCustomer(customerReference, name, transactionsAllowed, transactionLimit, true, // PIN is enabled by default as there's no way to set this from the back office export
                    false, false, fuelOnly, registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, 0, true);
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    customer.SetFlags(existing.Pin, existing.PrintValue, existing.AllowLoyalty);
                    customer.SetBalance(existing.Balance);
                    customer.SetCards(existing.Cards);
                }

                _customers[customerReference] = customer;
                HydraDb.AddLocalAccountCustomer(customer);

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void SetFlags(string customerReference, bool pin, bool printValue, bool allowLoyalty, string loggingReference = null)
        {
            DoAction(() =>
            {
                GetLogger().Info(
                    $"Local Account Set Flags, reference {customerReference}, Pin {pin}, Print Value {printValue}, Allow Loyalty {allowLoyalty}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    existing.SetFlags(pin, printValue, allowLoyalty);
                    HydraDb.AddLocalAccountCustomer(existing);
                }

                ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
            }, loggingReference);
        }

        public void Delete(string customerReference, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Delete, reference {customerReference}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    if (existing.Cards.Any())
                    {
                        existing.SetNotExists();
                    }
                    else
                    {
                        _customers.Remove(customerReference);
                    }

                    HydraDb.DeleteLocalAccountCustomer(existing);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void Stop(string customerReference, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Stop, reference {customerReference}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    existing.SetTransactionsAllowed(false);
                    HydraDb.AddLocalAccountCustomer(existing);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void Go(string customerReference, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Go, reference {customerReference}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    existing.SetTransactionsAllowed(true);
                    HydraDb.AddLocalAccountCustomer(existing);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        private void AddCard
        (string pan, string customerReference, string description, float discount, bool noRestrictions, bool unleaded, bool diesel,
            bool lpg, bool lrp, bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting,
            bool otherMotorRelatedGoods, bool shopGoods, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Add Card With Restrictions, pan {pan}," +
                                 $" reference {customerReference}, description {description}, discount {discount}" + (noRestrictions
                                     ? "No Restrictions"
                                     : "Restrictions" + (unleaded ? ", Unleaded" : string.Empty) + (diesel ? ", Diesel" : string.Empty) +
                                       (lpg ? ", LPG" : string.Empty) + (lrp ? ", LRP" : string.Empty) + (gasOil ? ", Gas Oil" : string.Empty) +
                                       (adBlue ? ", ad Blue" : string.Empty) + (kerosene ? ", Kerosene" : string.Empty) +
                                       (oil ? ", Oil" : string.Empty) + (avgas ? ", Avgas" : string.Empty) + (jet ? ", Jet" : string.Empty) +
                                       (mogas ? ", Mogas" : string.Empty) + (valeting ? ", Valeting" : string.Empty) +
                                       (otherMotorRelatedGoods ? ", Other motor related goods" : string.Empty) +
                                       (shopGoods ? ", Shop goods" : string.Empty)));
                var existingCustomer =
                    _customers.Values.FirstOrDefault(x => x.Cards.FirstOrDefault(y => string.Equals(y.Pan, pan)) != null);
                var existingCard = existingCustomer?.Cards.FirstOrDefault(x => string.Equals(x.Pan, pan));
                var existingHot = existingCard?.Hot ?? false;
                var newCard = new LocalAccountCard(pan, description, discount, noRestrictions, unleaded, diesel, lpg, lrp, gasOil,
                    adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods, existingHot);

                existingCustomer?.Cards.Remove(existingCard);
                if (existingCustomer?.CustomerExists == false && !existingCustomer.Cards.Any())
                {
                    _customers.Remove(existingCustomer.CustomerReference);
                }

                if (!_customers.TryGetValue(customerReference, out var customer))
                {
                    customer = new LocalAccountCustomer(customerReference, string.Empty, false, 0, false, false, false, false, false, false,
                        false, false, false, 0, false);
                    _customers[customerReference] = customer;
                }

                customer.Cards.Add(newCard);
                HydraDb.AddLocalAccountCard(customer, newCard);
                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void AddCardWithoutRestrictions(string pan, string customerReference, string description, float discount, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Add Card Without Restrictions, pan {pan}," +
                                 $" reference {customerReference}, description {description}, discount {discount}");
                AddCard(pan, customerReference, description, discount, true, false, false, false, false, false, false, false, false, false,
                    false, false, false, false, false, loggingReference, sendPushChange);
            }, loggingReference);
        }

        public void AddCardWithRestrictions
        (string pan, string customerReference, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Add Card With Restrictions, pan {pan}," +
                                 $" reference {customerReference}, description {description}, discount {discount}" +
                                 (unleaded ? ", Unleaded" : string.Empty) + (diesel ? ", Diesel" : string.Empty) + (lpg ? ", LPG" : string.Empty) +
                                 (lrp ? ", LRP" : string.Empty) + (gasOil ? ", Gas Oil" : string.Empty) + (adBlue ? ", ad Blue" : string.Empty) +
                                 (kerosene ? ", Kerosene" : string.Empty) + (oil ? ", Oil" : string.Empty) + (avgas ? ", Avgas" : string.Empty) +
                                 (jet ? ", Jet" : string.Empty) + (mogas ? ", Mogas" : string.Empty) + (valeting ? ", Valeting" : string.Empty) +
                                 (otherMotorRelatedGoods ? ", Other motor related goods" : string.Empty) +
                                 (shopGoods ? ", Shop goods" : string.Empty));
                AddCard(pan, customerReference, description, discount, false, unleaded, diesel, lpg, lrp, gasOil, adBlue, kerosene, oil, avgas,
                    jet, mogas, valeting, otherMotorRelatedGoods, shopGoods, loggingReference, sendPushChange);
            }, loggingReference);
        }

        public void DeleteCard(string pan, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Delete Card, pan {pan}");
                var existingCustomer =
                    _customers.Values.FirstOrDefault(x => x.Cards.FirstOrDefault(y => string.Equals(y.Pan, pan)) != null);
                var existingCard = existingCustomer?.Cards.FirstOrDefault(x => string.Equals(x.Pan, pan));

                if (existingCustomer != null && existingCard != null)
                {
                    existingCustomer.Cards.Remove(existingCard);
                    if (!existingCustomer.CustomerExists && !existingCustomer.Cards.Any())
                    {
                        _customers.Remove(existingCustomer.CustomerReference);
                    }

                    HydraDb.DeleteLocalAccountCard(existingCard);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void HotCard(string pan, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                var existingCustomer =
                    _customers.Values.FirstOrDefault(x => x.Cards.FirstOrDefault(y => string.Equals(y.Pan, pan)) != null);
                var existingCard = existingCustomer?.Cards.FirstOrDefault(x => string.Equals(x.Pan, pan));
                if (existingCustomer != null && existingCard != null)
                {
                    existingCard.SetHot(true);
                    HydraDb.AddLocalAccountCard(existingCustomer, existingCard);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void OkCard(string pan, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account OK Card, pan {pan}");
                var existingCustomer =
                    _customers.Values.FirstOrDefault(x => x.Cards.FirstOrDefault(y => string.Equals(y.Pan, pan)) != null);
                var existingCard = existingCustomer?.Cards.FirstOrDefault(x => string.Equals(x.Pan, pan));
                if (existingCustomer != null && existingCard != null)
                {
                    existingCard.SetHot(false);
                    HydraDb.AddLocalAccountCard(existingCustomer, existingCard);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void Balance(string customerReference, uint value, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Balance, reference {customerReference}, new balance value {value}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    existing.SetBalance(value);
                    HydraDb.AddLocalAccountCustomer(existing);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public void TopUp(string customerReference, uint value, string loggingReference = null, bool sendPushChange = true)
        {
            DoAction(() =>
            {
                GetLogger().Info($"Local Account Top Up, reference {customerReference}, top up amount {value}");
                if (_customers.TryGetValue(customerReference, out var existing))
                {
                    existing.SetBalance(existing.Balance + value);
                    HydraDb.AddLocalAccountCustomer(existing);
                }

                if (sendPushChange)
                {
                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }
            }, loggingReference);
        }

        public Result<LocalAccountBalance> GetBalance(string cardNumber, string loggingReference = null)
        {
            return DoAction(() =>
            {
                var customer = Customers.FirstOrDefault(x => x.Cards.Any(y => string.Equals(y.Pan, cardNumber)));
                
                return customer != null 
                    ? Result.Success(new LocalAccountBalance((int)customer.Balance, customer.LowCreditWarning, customer.MaxCreditReached)) 
                    : Result.Failure<LocalAccountBalance>("Failed to get balance");
            }, loggingReference);
        }

        public void ReduceBalance(string cardNumber, uint amount, string loggingReference = null)
        {
            DoAction(() =>
            {
                var customer = Customers.FirstOrDefault(x => x.Cards.Any(y => string.Equals(y.Pan, cardNumber)));
                if (customer != null)
                {
                    if (customer.Balance > amount)
                    {
                        customer.SetBalance(customer.Balance - amount);
                        HydraDb.AddLocalAccountCustomer(customer);
                    }
                    else
                    {
                        customer.SetBalance(0);
                        HydraDb.AddLocalAccountCustomer(customer);
                    }

                    ControllerWorker?.PushChange(EventType.LocalAccountsChanged);
                }

            }, loggingReference);
        }
    }
}
