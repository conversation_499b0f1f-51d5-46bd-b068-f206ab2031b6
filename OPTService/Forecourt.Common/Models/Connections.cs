namespace OPT.Common.Models
{
    public class Connections
    {
        public int AllOpt { get; }
        public int FromOpt { get; }
        public int ToOpt { get; }
        public int Heartbeat { get; }
        public int HydraPos { get; }
        public int RetalixPos { get; }
        public int ThirdPartyPos { get; }
        public int MediaChannel { get; }
        public bool SecAuth { get; }
        public bool CarWash { get; }
        public bool SiteController { get; }
        public bool TankGauge { get; }
        public bool HydraMobile { get; }
        public bool PaymentConfiguration { get; }
        public int SignalRPos { get; }
        public int SignalRPosIn { get; }
        public int SignalRSecAuth { get; }
        public int SignalRBos { get; }

        public Connections
        (int allOpt = 0, int fromOpt = 0, int toOpt = 0, int heartbeat = 0, int hydraPos = 0, int retalixPos = 0, int thirdPartyPos = 0,
            int mediaChannel = 0, bool secAuth = false, bool carWash = false, bool siteController = false, bool tankGauge = false, bool hydraMobile = false, 
            bool paymentConfig = false, int signalRPos = 0, int signalRSecAuth = 0, int signalRPosIn = 0, int signalRBos = 0)
        {
            AllOpt = allOpt;
            FromOpt = fromOpt;
            ToOpt = toOpt;
            Heartbeat = heartbeat;
            HydraPos = hydraPos;
            RetalixPos = retalixPos;
            ThirdPartyPos = thirdPartyPos;
            MediaChannel = mediaChannel;
            SecAuth = secAuth;
            CarWash = carWash;
            SiteController = siteController;
            TankGauge = tankGauge;
            HydraMobile = hydraMobile;
            PaymentConfiguration = paymentConfig;
            SignalRPos = signalRPos;
            SignalRSecAuth = signalRSecAuth;
            SignalRPosIn = signalRPosIn;
            SignalRBos = signalRBos;
        }
    }
}