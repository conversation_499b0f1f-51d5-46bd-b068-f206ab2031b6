import { Component, ElementRef, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { VersionInfo } from 'src/app/core/models/versionInfo.model';
import { WebService } from 'src/app/services/web.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { FormBuilder } from '@angular/forms';
import { UploadFileDetails } from 'src/app/core/models/uploadFileDetails.model';
import { environment } from 'src/environments/environment';
import { LoadingService } from 'src/app/services/loading.service';
import { OptService } from 'src/app/services/opt.service';


// Class to store errors
class Errors {
  OPTService: string = null;
  Refresh: string = null;
  RemoveWhitelistFile: Map<string, string> = new Map<string, string>();
  RemoveLayoutFile: Map<string, string> = new Map<string, string>();
  RemoveUpgradeFile: Map<string, string> = new Map<string, string>();
  RemoveSoftwareFile: Map<string, string> = new Map<string, string>();
  RemoveMediaFile: Map<string, string> = new Map<string, string>();
  RemovePlaylistFile: Map<string, string> = new Map<string, string>();
  Database: Map<string, string> = new Map<string, string>();
  RemoveOptLogFile: Map<string, string> = new Map<string, string>();
}

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.css']
})
export class AboutComponent implements OnInit {

  // Section collapse vars
  versionInfoIsCollapsed: boolean = true;
  filesIsCollapsed: boolean = true;
  whitelistFilesIsCollapsed: boolean = true;
  layoutFilesIsCollapsed: boolean = true;
  upgradeFilesIsCollapsed: boolean = true;
  softwareFilesIsCollapsed: boolean = true;
  mediaFilesIsCollapsed: boolean = true;
  playlistFilesIsCollapsed: boolean = true;
  databaseBackupFilesIsCollapsed: boolean = true;
  optLogFilesIsCollapsed: boolean = true;

  showContent: boolean = false;
  versionInfoData: VersionInfo = new VersionInfo();
  fileType: Map<string, string> = new Map<string, string>();
  errors: Errors = new Errors();

  lastRefresh: any | undefined;
  signalRData: Subscription | undefined;

  OptLogFilesPartial: Array<string> = [];

  aboutForm = this.fb.group({
    CurrentVersion: [ '' ],
    UpgradeVersion: [ '' ],
    UpgradeCommonVersion: [ '' ],
    RollbackVersion: [ '' ],
    RollbackCommonVersion: [ '' ],
    CommonVersion: [ '' ],
    FdcVersion: [ '' ],
    FdcChecksum: [ '' ],
    HscDllVersion: [ '' ],
    HscDllChecksum: [ '' ],
    DomsSoftwareVersion: [ '' ],
    DomsSoftwareDate: [ '' ],
    DomsPssPosChecksum: [ '' ],
    DomsPssTcpIpChecksum: [ '' ],
    OfflineFileServiceVersion: [ '' ],
    UploadedFileNames: [ '' ],
    WhitelistFiles: [ '' ],
    LayoutFiles: [ '' ],
    UpgradeFiles: [ '' ],
    SoftwareFiles: [ '' ],
    MediaFiles: [ '' ],
    PlaylistFiles: [ '' ],
    DatabaseBackupFiles: [ '' ],
    OptLogFiles: [ '' ]
  });

  constructor(
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private webService: WebService,
    private optService: OptService,
    private logger: NGXLogger,
    private loadingService: LoadingService) {
      this.signalRData = this.signalRService.getAboutSignalRMessage().subscribe(() => {
      this.zone.run(() => {
        this.refreshData(false);
      }); 
    });
  }

  /**
   * Removes errors of files that are not present in a given versionInfoData file Array
   * @param fileArray received file array
   * @param currentFileArray current file array
   * @param errors map of errors to update
   */
  removeUnusedFileErrors(fileArray: Array<string>, currentFileArray: Array<string>, errors: Map<string, string>) {    
    let difference = fileArray.filter(el => {
      return currentFileArray.indexOf(el) < 0;
    });
    difference.forEach(el => {
      errors.delete(el);
    })
  }

  /**
   * Removes errors of files that are not present in the current versionInfoData
   * @param data data received from refreshData call
   */
  removeUnusedErrors(data: any) {
    this.removeUnusedFileErrors(data.DatabaseBackupFiles, this.versionInfoData.DatabaseBackupFiles, this.errors.Database);
    this.removeUnusedFileErrors(data.LayoutFiles, this.versionInfoData.LayoutFiles, this.errors.RemoveLayoutFile);
    this.removeUnusedFileErrors(data.MediaFiles, this.versionInfoData.MediaFiles, this.errors.RemoveMediaFile);
    this.removeUnusedFileErrors(data.OptLogFiles, this.versionInfoData.OptLogFiles, this.errors.RemoveOptLogFile);
    this.removeUnusedFileErrors(data.PlaylistFiles, this.versionInfoData.PlaylistFiles, this.errors.RemovePlaylistFile);
    this.removeUnusedFileErrors(data.SoftwareFiles, this.versionInfoData.SoftwareFiles, this.errors.RemoveSoftwareFile);
    this.removeUnusedFileErrors(data.UpgradeFiles, this.versionInfoData.UpgradeFiles, this.errors.RemoveUpgradeFile);
    this.removeUnusedFileErrors(data.WhitelistFiles, this.versionInfoData.WhitelistFiles, this.errors.RemoveWhitelistFile);
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param data The signalR pushed object. 
   */
  refreshData(showLoading: boolean = true): void {
    //Main content loading

    if(showLoading){
      //Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }

    this.webService.getVersionInfo().subscribe(data => {
      this.logger.debug('Limited Version Info details data', data);
      this.removeUnusedErrors(data);
      this.versionInfoData = data;
      this.versionInfoData.DisplayOptLogFiles = [];
      this.showMore();
      this.lastRefresh = new Date().toLocaleString();
      this.errors.Refresh = null;

      this.aboutForm.patchValue(data);
      Object.keys(this.aboutForm.controls).forEach(key => {
        this.aboutForm.controls[key].valueChanges.subscribe(val => {
          this.versionInfoData[key] = val;
        })
      })
    }, (error) => {
      this.logger.error('Problem getting limited version info details data');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Problem refreshing OPT Service version information";
      }
      this.errors.Refresh = message;
      this.versionInfoData = new VersionInfo();
      this.loadingService.errorDuringLoading();
      if (showLoading) {
        this.showContent = true;
      }
    },
    () => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });    
  }

  /**
   * Component initialization
   */
  ngOnInit(): void {
    setTimeout(()=>{this.refreshData();});
  }

  /**
   * Component destroy
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Gets the text to display for a version parameter
   */
  getVersionText(value: string): string {
    return value ? value : 'Not available';
  }

  /**
   * Calls OPT Service to upgrade
   */
  upgradeService() {
    this.webService.upgradeService().subscribe(data => {
      this.logger.info('OPT service upgraded');
      this.errors.OPTService = null;
    }, (error) => {
      this.logger.error('Problem upgrading the OPT service');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Problem upgrading OPT Service";
      }
      this.errors.OPTService = message;
    });
  }

  /**
   * Calls OPT Service to rollback to previous version
   */
  rollbackService() {
    this.webService.rollbackService().subscribe(data => {
      this.logger.info('OPT service rollback');
      this.errors.OPTService = null;
    }, (error) => {
      this.logger.error('Problem with OPT service rollback');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Problem with OPT Service rollback";
      }
      this.errors.OPTService = message;
    });
  }

  /**
   * Restarts OPT Service
   */
  restartService() {
    this.webService.restartService().subscribe(data => {
      this.logger.info('OPT service restarted');
      this.errors.OPTService = null;
    }, (error) => {
      this.logger.error('Problem restarting  OPT service');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Problem restarting OPT Service";
      }
      this.errors.OPTService = message;
    });
  }
  
    /**
   * Request logs for all OPT
   */
  requestAllOptLogs() {
    this.optService.requestOptLog("").subscribe(data => {
      this.logger.info('Request logs for all OPT');
      this.errors.OPTService = null;
    }, (error) => {
      this.logger.error('Problem Requesting logs for all OPT');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = 'Problem Requesting logs for all OPT';
      }
      this.errors.OPTService = message;
    });
  }

  /**
   * Checks if reinstall is enabled
   */
  isReinstall(): boolean {
    let upgrade = this.versionInfoData?.Upgrade.filter(x => x.Name != null && x.Name != "" && x.Version != null && x.Version !== "");
    if (upgrade == null || upgrade.length == 0) {
      return false;
    }

    let result = true;
    upgrade.forEach(upd => {
      let current = this.versionInfoData?.Current.filter(x => x.Name == upd.Name && x.Version == upd.Version && x.Checksum == upd.Checksum);
      if (current == null || current.length == 0) {
        result = false;
      }
    });

    return result;
  }

  /**
   * Executes a file action against OPT Service
   * @param service service path to execute
   * @param fileName name of the file
   * @param base64 base64 contents of the file
   * @param errors assigned error component for this service call
   * @param errorName name to identify the error
   * @param inputComponent if available, input component in the view side so component can be blanked
   */
  commonFileAction(service: string, fileName: string, base64: string, errors: Map<string, string>, errorName: string, inputComponent: ElementRef = null) {
    let data: UploadFileDetails = new UploadFileDetails();
    data.FileName = fileName;
    data.Base64String = base64;
    this.webService.commonFileAction(service, data).subscribe(data => {
      this.logger.info('File action executed');
      errors.delete(errorName);
      if (inputComponent) {
        inputComponent.nativeElement.value = "";
      }
    }, (error) => {
      this.logger.error('Problem executing file action');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Error calling " + service;
      }
      errors.set(errorName, message);
    });
  }

  /**
   * Executes a files action against OPT Service
   * @param service service name to call
   * @param fileName name of the file
   * @param errors assigned error component for this service call
   */
  filesAction(service: string, fileName: string, errors: Map<string, string>) {
    let data: UploadFileDetails = new UploadFileDetails();
    data.FileName = fileName;
    this.webService.commonFileAction(service, data).subscribe(data => {
      this.logger.info('Files action executed');
      errors.delete(fileName);
      this.fileType.delete(fileName);
    }, (error) => {
      this.logger.error('Problem executing files action');
      this.logger.error(error);
      let message = error.error;
      if (!message || typeof message !== 'string') {
        message = "Error calling " + service;
      }
      errors.set(fileName, message);
    });
  }

  /**
   * Removes a white list file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeWhitelistFile(filename: string) { return this.commonFileAction(environment.webController.RemoveWhitelistFile, filename, null, this.errors.RemoveWhitelistFile, filename); }

  /**
   * Removes a white layout file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeLayoutFile(filename: string) { return this.commonFileAction(environment.webController.RemoveLayoutFile, filename, null, this.errors.RemoveLayoutFile, filename); }

  /**
   * Removes an upgrade file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeUpgradeFile(filename: string) { return this.commonFileAction(environment.webController.RemoveUpgradeFile, filename, null, this.errors.RemoveUpgradeFile, filename); }
  
  /**
   * Removes a software file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeSoftwareFile(filename: string) { return this.commonFileAction(environment.webController.RemoveSoftwareFile, filename, null, this.errors.RemoveSoftwareFile, filename); }
  
  /**
   * Removes a media file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeMediaFile(filename: string) { return this.commonFileAction(environment.webController.RemoveMediaFile, filename, null, this.errors.RemoveMediaFile, filename); }
  
  /**
   * Removes a playlist file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removePlaylistFile(filename: string) { return this.commonFileAction(environment.webController.RemovePlaylistFile, filename, null, this.errors.RemovePlaylistFile, filename); }
  
  /**
   * Removes an OPTLog file that was previously uploaded into OPT Service
   * @param filename  name of the file
   */
  removeOptLogFile(filename: string) { return this.commonFileAction(environment.webController.RemoveOptLogFile, filename, null, this.errors.RemoveOptLogFile, filename); }
    
  /**
   * Restores a previous database backup
   * @param filename  name of the file
   */
  restoreDatabase(filename: string) { return this.commonFileAction(environment.webController.RestoreDatabase, filename, null, this.errors.Database, filename); }
  
  /**
   * Removes an existing database backup from OPT Service
   * @param filename  name of the file
   */
  removeDatabaseBackupFile(filename: string) { return this.commonFileAction(environment.webController.RemoveDatabaseBackupFile, filename, null, this.errors.Database, filename); }

  /**
   * Creates a backup of the database
   */
  backupDatabase() {
    this.webService.backupDatabase().subscribe(data => {
      this.logger.info('OPT service database backed up');
    }, () => {
      this.logger.error('Problem backing OPT service database service');
    });
  }

  /**
   * Increments the list of displayed OPT log files
   */
  showMore(){
    var count = 50;
    for(var i = 0; count > 0 && i<this.versionInfoData.OptLogFiles.length; i++){
      this.versionInfoData.DisplayOptLogFiles.push(this.versionInfoData.OptLogFiles[i]);
      this.versionInfoData.OptLogFiles.splice(i,1);
      i--;
      count--;
    }
  }

  /*
   * Refresh all OPT information
   */
  refreshOPT() {
    this.logger.info('Refreshing all OPT configuration');
    this.optService.refreshOpt(null).subscribe(() => {
      this.logger.debug(`All OPT refresh sent OK`);
    }, error => {
      this.logger.error(`Problem refreshing all OPT`, error);
    });
  }
}
