using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    [Migration(20030005, "HOPT-1693 - related schema changes"), Tags(Constants.MigrationTypeProgrammatic)]
    public class Hopt_1693: Migrationable
    {
        private const string TableNameReceipts = "Receipts";
        private const string FieldNamePrintedCount = "PrintedCount";
        private const string FieldNameAmount = "Amount";
        private const string FieldNameTransactionTime = "TransactionTime";

        public override void Up()
        {
            if (!Schema.Table(TableNameReceipts).Column(FieldNamePrintedCount).Exists())
            {
                Create.Column(FieldNamePrintedCount).OnTable(TableNameReceipts).AsInt32().Nullable().WithDefaultValue(null);
            }

            if (!Schema.Table(TableNameReceipts).Column(FieldNameAmount).Exists())
            {
                Create.Column(FieldNameAmount).OnTable(TableNameReceipts).AsInt64().Nullable().WithDefaultValue(null);
            }

            if (!Schema.Table(TableNameReceipts).Column(FieldNameTransactionTime).Exists())
            {
                Create.Column(FieldNameTransactionTime).OnTable(TableNameReceipts).AsDateTime2().Nullable().WithDefaultValue(null);
            }
        }

        public override void Down()
        {
            if (Schema.Table(TableNameReceipts).Column(FieldNameTransactionTime).Exists())
            {
                Delete.Column(FieldNameTransactionTime).FromTable(TableNameReceipts);
            }

            if (Schema.Table(TableNameReceipts).Column(FieldNameAmount).Exists())
            {
                Delete.Column(FieldNameAmount).FromTable(TableNameReceipts);
            }

            if (Schema.Table(TableNameReceipts).Column(FieldNamePrintedCount).Exists())
            {
                Delete.Column(FieldNamePrintedCount).FromTable(TableNameReceipts);
            }
        }
    }
}
