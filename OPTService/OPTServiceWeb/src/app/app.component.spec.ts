import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { AppComponent } from './app.component';
import { SignalRService } from './services/signal-r.service';
import { OptService } from './services/opt.service';
import { PumpService } from './services/pump.service';
import { SignalREventType } from './core/enums/signalREventType';
import { ADVANCED_SERVICE_PROVIDER, ADVANCED_SERVICE_SPY } from './services/advanced-config.service.spy';
import { NGX_LOGGER_PROVIDER } from './testing/ngxlogger.spy';
import { SIGNAL_R_SERVICE_PROVIDER } from './services/signal-r.service.spy';
import { OPT_SERVICE_PROVIDER } from './services/opt.service.spy';

describe('AppComponent', () => {
  let fixture: ComponentFixture<AppComponent>;
  let app: AppComponent;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let optServiceSpy: jasmine.SpyObj<OptService>;
  let pumpServiceSpy: jasmine.SpyObj<PumpService>;
  
  beforeEach(async () => {
    const pumpSpy = jasmine.createSpyObj('PumpService', ['getPumpControllerStatus']);

    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        NgbNavModule,
      ],
      declarations: [
        AppComponent
      ],
      providers: [
        NGX_LOGGER_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        OPT_SERVICE_PROVIDER(),
        { provide: PumpService, useValue: pumpSpy },
        ADVANCED_SERVICE_PROVIDER(),
      ]
    }).compileComponents();

    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    signalRServiceSpy.startConnection.and.stub();
    signalRServiceSpy.getOptSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getPumpSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getFileLocationSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getAdvancedConfigSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getConnectionStatushChangeMessage.and.returnValue(of());
    signalRServiceSpy.getGenericOptConfigSignalRMessage.and.returnValue(of());
    optServiceSpy = TestBed.inject(OptService) as jasmine.SpyObj<OptService>;
    optServiceSpy.getGenericOptConfig.and.returnValue(of());
    pumpServiceSpy  = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    pumpServiceSpy.getPumpControllerStatus.and.returnValue(of());
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of());

    fixture = TestBed.createComponent(AppComponent);
    app = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the app', () => {
    expect(app).toBeTruthy();
  });

  it(`should have as title 'OPTWebAdmin'`, () => {
    expect(app.title).toEqual('OPTWebAdmin');
  });

  it('should render title', () => {
    optServiceSpy.getGenericOptConfig.and.returnValue(of());
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.d-flex').textContent).toContain('Hydra OPT ControllerUnknownConnectingRefresh');
  });

  describe('.refreshData()', () => {

    it('should get site controller data', () => {
      app.refreshData();

      expect(pumpServiceSpy.getPumpControllerStatus).toHaveBeenCalled();
    });

    it('should get advanced config data', () => {
      app.refreshData();

      expect(ADVANCED_SERVICE_SPY().getAdvancedConfig).toHaveBeenCalled();
    });
  });

  describe('.refreshCurrentComponent()', () => {
    const tests = [
      { activeScreen: 'opts', eventType: SignalREventType.OPTChanged },
      { activeScreen: 'pumps', eventType: SignalREventType.PumpChanged },
      { activeScreen: 'fuel-prices', eventType: SignalREventType.FuelPriceChanged },
      { activeScreen: 'connections', eventType: SignalREventType.ConnectionChanged },
      { activeScreen: 'generic-opt-config', eventType: SignalREventType.GenericOptConfigChanged },
      { activeScreen: 'divert-opt-service', eventType: SignalREventType.DivertDetailsChanged },
      { activeScreen: 'advanced-config', eventType: SignalREventType.AdvancedConfigChanged },
      { activeScreen: 'file-locations', eventType: SignalREventType.FileLocationsChanged },
      { activeScreen: 'shift-end', eventType: SignalREventType.ShiftEndChanged },
      { activeScreen: 'transactions', eventType: SignalREventType.TransactionChanged },
      { activeScreen: 'local-accounts', eventType: SignalREventType.LocalAccountsChanged },
      { activeScreen: 'info-messages', eventType: SignalREventType.InfoMessageChanged },
      { activeScreen: 'about', eventType: SignalREventType.AboutChanged },
    ];

    tests.forEach((test: { activeScreen:string, eventType: SignalREventType }) => {
      it(`should push SignalR event type ${test.eventType} for screen ${test.activeScreen}`, () => {
        app.activeScreenId = test.activeScreen;

        app.refreshCurrentComponent();

        expect(signalRServiceSpy.processPushedEvent).toHaveBeenCalledWith(test.eventType);
      });
    });
  });
});
