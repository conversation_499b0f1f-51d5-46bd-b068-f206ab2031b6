using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to switch MediaChannel RxTx logging off (Magical Advanced Config keys)
    /// </summary>
    [Migration(20050003, "ADO#409110 - Switch MediaChannel LogRxTx value off (in advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO409110_SwitchMediaChannelLogRxTxOff : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO409110_upgrade_app_config_install.sql");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO409110_upgrade_app_config_rollback.sql");
        }
    }
}
