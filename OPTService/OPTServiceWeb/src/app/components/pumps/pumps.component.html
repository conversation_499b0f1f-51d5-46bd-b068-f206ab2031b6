<ng-container *ngIf="showContent">    
    <div class="row row-cols-1 row-cols-sm-1 row-cols-md-1 row-cols-lg-1"
        [ngClass]="parentOpt != '' && parentOpt != 'none' ? 'row-cols-xl-1' : 'row-cols-xl-2'">
        <div *ngFor="let item of pumpsData; let i = index" [id]="'item'+i" class="col mb-4">
            <div class="card card-pump">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-2 col-xs-3 text-left">
                            <span>Pump {{item.Number}}</span>
                        </div>
                        <div class="col-9 col-xs-8">
                            <span class="mr-3 badge badge-info badge-pump-mode">
                                {{item.KioskOnly ? 'Kiosk' : item.OutsideOnly ? 'OPT' : 'Mixed'}}
                            </span>
                            <span class="mr-3 badge badge-pump-closed"
                                [ngClass]="item.Closed ? 'badge-danger' : item.ClosePending ? 'badge-warning' : 'badge-success'">
                                {{item.Closed ? 'Closed' : item.ClosePending ? 'Closing' : 'Open'}}
                            </span>
                            <span *ngIf="secAuthType != 'NONE' && item.SecAuthState != secAuthState.Idle"
                                  class="mr-3 badge badge-pump-secauth"
                                  [ngClass]="item.SecAuthState == secAuthState.Success || item.HasSecAuthRequestTimedOut ? 'badge-success' : item.SecAuthState == secAuthState.Failure ? 'badge-warning' : 'badge-info'">
                              {{item.SecAuthState == secAuthState.Success ? 'SecAuth OK' : item.HasSecAuthRequestTimedOut ? 'SecAuth TimedOut' : item.SecAuthState == secAuthState.Failure ? 'SecAuth Rejected' : 'SecAuth Pending'}}
                            </span>
                            <span *ngIf="item.ThirdPartyPending"
                                class="mr-3 badge badge-info badge-pump-third-party">
                                Third party pending
                            </span>
                            <span *ngIf="item.HasPayment"
                                class="mr-3 badge badge-info badge-pump-has-payment">
                                Has payment
                            </span>
                            <span *ngIf="item.Delivering || item.NozzleUp || item.Delivered"
                                class="mr-3 badge badge-pump-delivering" 
                                [ngClass]="item.Delivering ? 'badge-warning' : item.NozzleUp ? 'badge-info' : 'badge-success'">
                                {{item.Delivering ? 'Delivering' : item.NozzleUp ? 'Nozzle up' : 'Delivered'}}
                            </span>
                            <span *ngIf="item.InUse"
                                class="mr-3 badge badge-info badge-pump-in-use">
                                In Use
                            </span>
                        </div>
                        <div class="col-1 text-right" [id]="'heading-pump-collapsible'+i+'-opt'+item.OptStringId">
                            <a data-toggle="collapse" [href]="'#collapse-pump-body'+item.Number"
                                aria-expanded="false" [attr.aria-controls]="'collapse-pump-body'+item.Number"
                                class="d-block">
                                <i class="bi-chevron-down"></i>
                                <i class="bi-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div [id]="'collapse-pump-body'+item.Number" class="p-3" [ngClass]="getPumpCardState('collapse-pump-body'+item.Number)"
                            [attr.aria-labelledby]="'heading-pump-collapsible'+i+'-opt'+item.OptStringId">
                            <app-pump [pump]="item" [tids]="tids" [opts]="opts" [maxFillOverride]="maxFillOverride">
                            </app-pump>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-center">
                        <a data-toggle="collapse" [href]="'#collapse-pump-body'+item.Number"
                            aria-expanded="false" [attr.aria-controls]="'collapse-pump-body'+item.Number"
                            class="d-block">
                            <i class="bi-chevron-up"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-3" *ngIf="!parentOpt">
        <div class="col-auto">
            <button name="pumpAddBtn" class="btn btn-success btn-labeled" (click)="openAddPumpModal(content)">
                <span class="btn-label"><i class="bi bi-plus"></i></span>Add pump
            </button>    
        </div>
    </div>
    <ng-template #content let-modal>
        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Add pump</h4>
            <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form [formGroup]="addPumpForm">
                <div class="form-group">
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="modalPump">Pump</label>
                            <div class="input-group pt-2">
                                <input id="modalPump" type="number" class="form-control" value="1" min="1" max="255"
                                    formControlName="Number" ngbAutofocus
                                    [ngClass]="{ 'is-invalid': submitted && addPumpForm.controls.Number.errors}"
                                    onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="modalOpt">OPT</label>
                            <select id="modalOpt" class="form-control" formControlName="OptStringId"
                                [ngClass]="{'is-invalid': submitted && addPumpForm.controls.OptStringId.errors}">
                                <option value="" enabled selected>No OPT</option>
                                <option *ngFor="let opt of opts" [ngValue]="opt.StringId">{{opt.StringId}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="modalTid">TID</label>
                            <select id="modalTid" class="form-control" formControlName="Tid"
                                [ngClass]="{'is-invalid': submitted && addPumpForm.controls.Tid.errors}">
                                <option value="" enabled selected>No TID</option>
                                <option *ngFor="let tid of tids" [ngValue]="tid">{{tid}}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" name="modalAddPumpBtn" class="btn btn btn-success" (click)="addPump()"
                [disabled]="addPumpForm.invalid">Save</button>
        </div>
    </ng-template>
</ng-container>

