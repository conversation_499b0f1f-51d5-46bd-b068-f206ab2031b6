using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Microsoft.Owin.Hosting;
using OPT.Common;
using OPT.Common.Helpers;
using System;

namespace OPTService
{
    /// <summary>
    /// Rigging for starting application components.
    /// </summary>
    public class ServiceApplication : Disposable
    {
        private const string DefaultHydra = "Hydra 1";
        private readonly ICore _core;
        
        private IDisposable _webapp;
        private string _hydraId;

        public ServiceApplication(ICore core, IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager = null, bool useXmlConfigurator = false,
            ILogFormatter logFormatter = null, string typeName = null) : base(logManager, loggerName, configurationManager, useXmlConfigurator, logFormatter, typeName)
        {
            _core = core ?? throw new ArgumentNullException(nameof(core));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }
        }

        /// <summary>
        /// Start the application components.
        /// </summary>
        public Result Start()
        {
            LoggingHelper.SetThreadName(GetType().Name);
            GetLogger().Info("OPT Service Starting");
            GetLogger().Info("Starting Core");

            GetLogger().Info("Setting up self hosting");
            if (!StartWebApp("WebAppURL"))
            {
                StartWebApp("WebAppURLAlternate");
            }

            GetLogger().Info("Self host set up finished");

            var result = _core.Start(_hydraId);
            if (!result.IsSuccess)
            {
                // prevent the service from starting
                GetLogger().Error(result.Error);
                return result;
            }

            return Result.Success();
        }

        private bool StartWebApp(string webUrl)
        {
            try
            {
                var appSetting = ConfigurationManager.AppSettings.GetAppSettingOrDefault($"{Constants.CategoryNameWeb}{Constants.CategorySeparator}{webUrl}", "http://*", LoggerIConfigurationManager);

                GetLogger().Info($"Trying URL {appSetting}");
                var startup = new Startup(Logger, ConfigurationManager);

                _webapp = WebApp.Start(new StartOptions(appSetting), startup.Configuration);
                GetLogger().Info($"Using URL {appSetting}");

                return true;
            }
            catch (Exception e)
            {
                GetLogger().Error("Error starting self hosting", e);
                return false;
            }
        }

        /// <summary>
        /// Stop the application components.
        /// </summary>
        public void Stop()
        {
            LoggingHelper.SetThreadName(GetType().Name);
            GetLogger().Info("OPT Service Stopping");

            Dispose();

            GetLogger().Info("OPT Service Stopped");
        }

        protected override void DoDisposeDisposing()
        {
            _core.Stop();
            _core.Dispose();
            _webapp?.Dispose();

            base.DoDisposeDisposing();
        }

        public void SetHydraId(string appSetting)
        {
            _hydraId = ConfigurationManager.AppSettings.GetAppSettingOrDefault(appSetting, DefaultHydra, null);
        }
    }
}
