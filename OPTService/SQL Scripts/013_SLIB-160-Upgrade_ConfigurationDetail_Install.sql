USE [Hydra]
GO

DECLARE @catIdWorkers INT, @catIdConnectivity INT;

SELECT @catIdWorkers = cc.Id FROM dbo.ConfigurationCategory cc WHERE cc.Category = 'WORKERS';
SELECT @catIdConnectivity = cc.Id FROM dbo.ConfigurationCategory cc WHERE cc.Category = 'CONNECTIVITY';

-- If WORKERS and CONNECTIVITY exist - update W.Value with C.Value, 
UPDATE t1
  SET t1.Value = t2.Value
  FROM [dbo].[ConfigurationDetail] t1
    JOIN [dbo].[ConfigurationDetail] t2 ON t1.[Key] = t2.[Key]
  WHERE 
    (t1.CategoryId = @catIdWorkers) AND 
    (t2.CategoryId = @catIdConnectivity) AND
    (t1.[Key] LIKE 'BackgroundTask:%');

-- If WORKERS do NOT exist but CONNECTIVITY do - update W.CategoryId with C.CategoryId
UPDATE t1
  SET t1.CategoryId = @catIdWorkers
  FROM [dbo].[ConfigurationDetail] t1
  WHERE 
    (t1.CategoryId = @catIdConnectivity) AND 
    (t1.[Key] LIKE 'BackgroundTask:%') AND
    NOT EXISTS (
        SELECT 1 FROM [dbo].[ConfigurationDetail] t2 
        WHERE (t1.[Key] = t2.[Key]) AND (t2.CategoryId = @catIdWorkers)
    );

-- and DELETE CONNECTIVITY 
DELETE [dbo].[ConfigurationDetail]
WHERE (CategoryId = @catIdConnectivity) AND ([Key] LIKE 'BackgroundTask:%');

SELECT * FROM [dbo].[ConfigurationDetail] t JOIN [dbo].[ConfigurationCategory] tc ON tc.Id = t.CategoryId WHERE t.CategoryId = @catIdConnectivity AND t.[Key] LIKE 'BackgroundTask:%';
SELECT * FROM [dbo].[ConfigurationDetail] t JOIN [dbo].[ConfigurationCategory] tc ON tc.Id = t.CategoryId WHERE t.CategoryId = @catIdWorkers AND t.[Key] LIKE 'BackgroundTask:%';

GO