import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class HeaderInterceptor implements HttpInterceptor {

  constructor() { }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    return next.handle(this.addAuthToken(request));
  }

  addAuthToken(request: HttpRequest<any>) {
    const webUiApiKey = '06AB47638CED484C80EBEB40100B0AA8'
    return request.clone({
      setHeaders: {
        ApiKey: webUiApiKey
      }
    })
  }
}
