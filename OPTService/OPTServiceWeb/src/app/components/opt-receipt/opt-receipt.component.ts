import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { Opt } from 'src/app/core/models/opt.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { OptService } from 'src/app/services/opt.service';

@Component({
  selector: 'app-opt-receipt',
  templateUrl: './opt-receipt.component.html',
  styleUrls: ['./opt-receipt.component.css']
})
export class OptReceiptComponent implements OnInit {

  @Input()
  private opt: Opt;

  @Input()
  public formGroup: FormGroup;

  @Input()
  public headerArrayName: string;

  @Input()
  public footerArrayName: string;

  public get headerArray(): FormArray {
    return this.formGroup?.get(this.headerArrayName) as FormArray || new FormArray([]);
  }

  public get footerArray(): FormArray {
    return this.formGroup?.get(this.footerArrayName) as FormArray || new FormArray([]);
  }

  public get isGlobal(): boolean {
    return this.opt === undefined || this.opt === null;
  }

  public get id(): string {
    return this.opt?.StringId || '';
  }

  public headerErrors: { [key: number]: string } = {};
  public footerErrors: { [key: number]: string } = {};

  public readonly ALL_INDEX = -1;
  private readonly MAX_LENGTH = 30;

  constructor(
    private optService: OptService,
    private logger: NGXLogger,
  ) { }

  ngOnInit(): void {
    if (this.opt) {
      this.headerArray.clear();
      this.opt?.ReceiptHeaders?.forEach((header: string) => {
        this.headerArray.push(new FormControl(header));
      });
      this.footerArray.clear();
      this.opt?.ReceiptFooters?.forEach((footer: string) => {
        this.footerArray.push(new FormControl(footer));
      });
    }

    this.headerArray.controls.forEach(control => {
      control.setValidators([Validators.maxLength(this.MAX_LENGTH)]);
    });
    this.footerArray.controls.forEach(control => {
      control.setValidators([Validators.maxLength(this.MAX_LENGTH)]);
    });
  }

  addReceiptHeader(event?: Event): void {
    ButtonHelper.clicked(event);
    this.opt?.ReceiptHeaders?.push('');
    this.headerArray.push(new FormControl('', [Validators.maxLength(this.MAX_LENGTH)]));
    ButtonHelper.reset(event);
  }

  removeReceiptHeader(index: number, event?: Event): void {
    this.opt?.ReceiptHeaders?.splice(index, 1);
    this.headerArray.removeAt(index);
    this.updateReceiptHeader(index, event);
  }

  updateReceiptHeader(index: number, event?: Event): void {
    if (this.isGlobal) {
      this.setGlobalReceiptHeader(event, index);
    } else {
      this.setOptReceiptHeader(event, index);
    }
  }

  setOptReceiptHeader(event: Event, index: number = this.ALL_INDEX): void {
    ButtonHelper.clicked(event);
    this.logger.info('updating receipt header', this.opt);
    this.optService.setReceiptHeader(this.opt).subscribe(() => {
      this.logger.debug('Receipt updated ok');
      this.headerErrors[index] = null;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating receipt');
      this.headerErrors[index] = this.getErrorMessage(error);
      ButtonHelper.reset(event);
    });
  }

  setGlobalReceiptHeader(event: Event, index: number = this.ALL_INDEX): void {
    ButtonHelper.clicked(event);
    var receiptHeaders = this.headerArray.value;
    this.logger.info('Updating receipt headers', receiptHeaders);
    this.optService.setGlobalReceiptHeader(receiptHeaders, this.opt?.StringId).subscribe(() => {
      this.logger.debug('Global receipt headers updated ok');
      this.headerErrors[index] = null;
      ButtonHelper.reset(event);
    }, (error) => {
      this.logger.error('Problem updating global receipt headers');
      this.headerErrors[index] = this.getErrorMessage(error);
      ButtonHelper.reset(event);
    });
  }

  addReceiptFooter(event?: Event): void {
    ButtonHelper.clicked(event);
    this.opt?.ReceiptFooters?.push('');
    this.footerArray.push(new FormControl('', [Validators.maxLength(this.MAX_LENGTH)]));
    ButtonHelper.reset(event);
  }

  removeReceiptFooter(index: number, event?: Event): void {
    this.opt?.ReceiptFooters?.splice(index, 1);
    this.footerArray.removeAt(index);
    this.updateReceiptFooter(index, event);
  }

  updateReceiptFooter(index: number, event?: Event): void {
    if (this.isGlobal) {
      this.setGlobalReceiptFooter(event, index);
    } else {
      this.setOptReceiptFooter(event, index);
    }
  }

  setOptReceiptFooter(event: Event, index: number = this.ALL_INDEX): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating receipt footer', this.opt);
    this.optService.setReceiptFooter(this.opt).subscribe(() => {
      this.logger.debug('Receipt footer updated ok');
      this.footerErrors[index] = null;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating receipt footer');
      this.footerErrors[index] = this.getErrorMessage(error);
      ButtonHelper.reset(event);
    });
  }

  setGlobalReceiptFooter(event: Event, index: number = this.ALL_INDEX): void {
    ButtonHelper.clicked(event);
    var receipt = this.footerArray.value;
    this.logger.info('Updating global receipt footer', receipt);
    this.optService.setGlobalReceiptFooter(receipt, this.opt?.StringId).subscribe(() => {
      this.logger.debug('Global receipt footer updated ok');
      this.footerErrors[index] = null;
      ButtonHelper.reset(event);
    }, (error) => {
      this.logger.error('Problem updating global receipt footer');
      this.footerErrors[index] = this.getErrorMessage(error);
      ButtonHelper.reset(event);
    });
  }

  private getErrorMessage(error: HttpErrorResponse): string {
    let message = error?.error?.Message;
    if (!message || typeof message !== 'string') {
      message = 'Problem updating receipt';
    }
    return message;
  }
}
