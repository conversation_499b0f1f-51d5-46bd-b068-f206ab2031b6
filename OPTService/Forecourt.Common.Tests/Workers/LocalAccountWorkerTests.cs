using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System.Collections.Generic;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    public class LocalAccountWorkerTests
    {
        private const string CustRef1 = "Reference 1";
        private const string CustRef2 = "Reference 2";
        private const string CustName1 = "Name 1";
        private const string CustName2 = "Name 2";
        private const uint CustTransLimit1 = 100;
        private const uint CustTransLimit2 = 200;
        private const uint CustBalance1 = 1000;
        private const uint CustBalance2 = 2000;
        private const string CardPan1 = "Pan 1";
        private const string CardDesc1 = "Description 1";
        private const float CardDiscount1 = 10;
        private const string CardPan2 = "Pan 2";
        private const string CardDesc2 = "Description 2";
        private const float CardDiscount2 = 20;

        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _logger;
        private readonly ILocalAccountWorker _localAccountWorker;

        public LocalAccountWorkerTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            LocalAccountCustomer customer1 = new LocalAccountCustomer(CustRef1, CustName1, true, CustTransLimit1, true, true, true, true,
                true, true, true, false, false, CustBalance1, true);
            LocalAccountCustomer customer2 = new LocalAccountCustomer(CustRef2, CustName2, false, CustTransLimit2, true, false, true, false,
                true, false, false, true, false, CustBalance2, true);
            LocalAccountCard card1 = new LocalAccountCard(CardPan1, CardDesc1, CardDiscount1, true, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false);
            LocalAccountCard card2 = new LocalAccountCard(CardPan2, CardDesc2, CardDiscount2, false, true, false, true, false, true, false,
                true, false, true, false, true, false, true, false, true);
            customer1.SetCards(new List<LocalAccountCard> {card1, card2});
            _hydraDb.FetchLocalAccountCustomers().Returns(new List<LocalAccountCustomer> {customer1, customer2});
            _logger = Substitute.For<IHtecLogger>();
            _localAccountWorker = new LocalAccountWorker(_hydraDb, _logger);
        }

        #region Constructor

        [Fact]
        public void constructor_null_hydra_db_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LocalAccountWorker(null, _logger);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "hydraDb");
        }

        [Fact] public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LocalAccountWorker(_hydraDb, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        #endregion

        [Fact]
        public void test_get_balance()
        {
            // Arrange

            // Act
            var success = _localAccountWorker.GetBalance(CardPan1);

            // Assert
            success.IsSuccess.Should().BeTrue();
            success.Value.Balance.Should().Be((int)CustBalance1);
        }

        [Fact]
        public void test_get_balance_not_found()
        {
            // Arrange
            const string cardpan = "Pan 3";

            // Act
            var success = _localAccountWorker.GetBalance(cardpan);

            // Assert
            success.IsSuccess.Should().BeFalse();
        }
    }
}
