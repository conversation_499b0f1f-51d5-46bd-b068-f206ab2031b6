namespace OPTService.OPTServiceClasses
{
    public class FileLocations
    {
        public string RetalixTransactionFileDirectory { get; set; }
        public string TransactionFileDirectory { get; set; }
        public string WhitelistDirectory { get; set; }
        public string LayoutDirectory { get; set; }
        public string SoftwareDirectory { get; set; }
        public string ContactlessPropertiesFile { get; set; }
        public string FuelDataUpdateFile { get; set; }
        public string UpgradeFileDirectory { get; set; }
        public string RollbackFileDirectory { get; set; }
        public string MediaDirectory { get; set; }
        public string PlaylistDirectory { get; set; }
        public string OptLogFileDirectory { get; set; }
        public string LogFileDirectory { get; set; }
        public string TraceFileDirectory { get; set; }
        public string JournalFileDirectory { get; set; }
        public string ReceivedUpdateDirectory { get; set; }
        public string DatabaseBackupDirectory { get; set; }
        public bool EsocketOverrideContactless { get; set; }
        public bool MediaChannel { get; set; }
    }
}