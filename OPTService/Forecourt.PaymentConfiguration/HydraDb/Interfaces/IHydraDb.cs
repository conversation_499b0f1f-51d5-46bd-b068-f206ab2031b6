using Htec.Foundation.Connections.Models;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.PaymentConfiguration.HydraDb.Interfaces
{
    /// <summary>
    /// Any and all defintions of IHydraDb needed by PaymentConfiguration, split down by area
    /// </summary>
    public interface IHydraDb :
        Core.HydraDb.Interfaces.IHydraDb,
        IHydraDbPaymentConfigurationEsocket
    {
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by PaymentConfiguration
    /// </summary>
    public interface IHydraDbPaymentConfigurationEsocket
    {
        void SetEsocketConnectionString(string newConnectionString);
        void SetEsocketUseConnectionString(bool useConnectionString);
        void SetEsocketConfigFile(string fileName);
        void SetEsocketKeystoreFile(string fileName);
        void SetEsocketDbUrl(string url);
        void SetEsocketOverrideProperties(bool flag);
        void SetEsocketOverrideKeystore(bool flag);
        void SetEsocketOverrideUrl(bool flag);
        void SetEsocketOverrideContactless(bool flag);

        /// <summary>Add an End Point for eSocket.POS in the database.</summary>
        /// <param name="ip">IP address to add.</param>
        /// <param name="port">Port number to add.</param>
        void AddEsocket(IPAddress ip, int port);

        /// <summary>Remove an End Point for eSocket.POS in the database.</summary>
        /// <param name="ip">IP address to remove.</param>
        /// <param name="port">Port number to remove.</param>
        void RemoveEsocket(IPAddress ip, int port);

        /// <summary>Fetch the set of eSocket.POS end points from the database.</summary>
        /// <returns>The end points.</returns>
        IList<GenericEndPoint> FetchESocketEndPoints();

    }
}
