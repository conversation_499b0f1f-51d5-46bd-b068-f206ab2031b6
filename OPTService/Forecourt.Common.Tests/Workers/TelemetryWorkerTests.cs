using Forecourt.Common.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    public class TelemetryWorkerTests
    {
        private const string Device = "Device";
        private const string Version = "1.0";
        private readonly IHtecLogger _telemetry;
        private readonly IHtecLogger _logger;
        private readonly IConfigurationManager _configurationManager;

        public TelemetryWorkerTests()
        {
            _telemetry = Substitute.For<IHtecLogger>();
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
        }

        #region Constructor
        
        [Fact]
        public void constructor_null_telemetry_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new TelemetryWorker(Device, Version, null, _logger, _configurationManager);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "telemetry");
        }

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new TelemetryWorker(Device, Version, _telemetry, null, _configurationManager);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        [Fact]
        public void constructor_null_configuration_manager_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new TelemetryWorker(Device, Version, _telemetry, _logger, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "configurationManager");
        }

        #endregion
    }
}
