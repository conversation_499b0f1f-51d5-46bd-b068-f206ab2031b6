using FluentMigrator;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed for DOMS/Pump workers
    /// </summary>
    [Migration(26200007, "ADO#712741 - Set DOMS/Logging/PumpWorker configuration - client neutral"), Tags(Constants.MigrationTypeData)]
    public class ADO712741_DOMSPumpConfigUpdate : Migrationable
    {
        private const string CategoryNameLoggingDeveloper = "LOGGING:DEVELOPER";
        
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Timers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:DomsSetupTcp+EmbeddedTimerable', @value = '00:00:00.5', @category = '{cfgConstants.CategoryNameTimers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:PumpWorker+EmbeddedTimerable', @value = '00:00:00.75', @category = '{cfgConstants.CategoryNameTimers}'");

            // Workers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:ConfigUpdateWorker', @value = '12:00:00', @category = '{cfgConstants.CategoryNameWorkers}'");

            // Debug Logging
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DomsSetup:Log:RxTx:DOMS', @value = 'True', @category = 'CONNECTIVITY'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsMessageReader', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsMessageSender', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsController', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsSetupTcp', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:BookTransactionOrchestration', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
