import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { FuelTransactionDetails } from '../core/models/fuelTransactionDetails.model';
import Utils from '../core/utils/utils';
import { ReceiptDetails } from '../core/models/receiptDetails';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {

  private baseUrl: string;

  /**
   * The TransactionService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.transactionController.base;
  }

  /**
   * Gets the fuel transactions details
   */
  public getFuelTransactions(startTime: Date, endTime: Date): Observable<any> {
    const url = this.baseUrl + environment.transactionController.getFuelTransactions;
    const options = {
      params: new HttpParams().set("startTime", startTime.toUTCString()).set("endTime", endTime.toUTCString())
    };

    this.logger.debug(`Getting Fuel Transactions Details via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Gets the other events
   */
  public getOtherEvents(startTime: Date, endTime: Date): Observable<any> {
    const url = this.baseUrl + environment.transactionController.getOtherEvents;
    const options = {
      params: new HttpParams().set("startTime", startTime.toUTCString()).set("endTime", endTime.toUTCString())
    };

    this.logger.debug(`Getting Other Events via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Gets the receipt URL to display in the receipt iframe
   * @param transactionId transaction number to find the receipt
   */
  public getReceiptURL(transactionId: number) {
    return this.baseUrl + environment.transactionController.getReceipt + '?transactionNumber=' + transactionId;
  }

  /**
   * Gets the receipt data for the given transaction number
   */
  public getReceipt(transactionId: number): Observable<ReceiptDetails> {
    const url = this.baseUrl + environment.transactionController.getReceipt + '?transactionNumber=' + transactionId;
    const options = {
      params: {}
    };

    this.logger.debug(`Getting Receipt via url: ${url}`);
    return this.http.get<ReceiptDetails>(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Prints a receipt
   */
  public printReceipt(fuelTransactionDetails: FuelTransactionDetails): Observable<any> {
    const url = this.baseUrl + environment.transactionController.printReceipt;

    this.logger.debug(`Printing receipt via url: ${url}`);
    return this.http.post(url, fuelTransactionDetails);
  }

  /**
   * Save a receipt
   */
  public saveReceipt(fuelTransactionDetails: FuelTransactionDetails): Observable<any> {
    const url = this.baseUrl + environment.transactionController.saveReceipt;

    this.logger.debug(`Saving receipt via url: ${url}`);
    return this.http.post(url, fuelTransactionDetails);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
