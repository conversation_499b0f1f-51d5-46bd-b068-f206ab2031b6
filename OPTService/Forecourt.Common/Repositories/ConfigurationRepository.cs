using CSharpFunctionalExtensions;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Extensions;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.HydraDb;
using OPT.Common.HydraDbClasses;
using OPT.Common.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.Common.Repositories
{
    /// <summary>
    /// Repository for handling configuration data in the database.
    /// </summary>
    public class ConfigurationRepository : CoreHydraDbRepository<ITelemetryWorker>, IConfigurationRepository
    {
        public class ConfigType
        {
            public int Id { get; set; }
            public string Type { get; set; }
        }

        public class ConfigCategory
        {
            public int Id { get; set; }
            public string Category { get; set; }
            public bool IsStandardEditable { get; set; }
        }

        public class ConfigHeader
        {
            public int Id { get; set; }
            public int TypeId { get; set; }
            public string Description { get; set; }
        }

        public class ConfigurationIds
        {
            public IEnumerable<ConfigType> ConfigTypes { get; set; }
            public IEnumerable<ConfigCategory> ConfigCategories { get; set; }
            public IEnumerable<ConfigHeader> ConfigHeaders { get; set; }
        }

        private const string SetConfigSproc = "SetConfigValue";
        private const string UpsertConfigDetailSproc = "dbo.UpsertConfigurationDetail";
        private const string GetSiteInfoSproc = "GetSiteInfo";
        private const string GetCategoryConfigurationSproc = "GetCategoryConfiguration";

        private int _siteControllerId;
        private int _contactlessId;
        private int _siteInfoId;

        /// <summary>
        /// Creates a new instance of the <see cref="ConfigurationRepository"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="dbExecutorFactory">Factory for database query execution.</param>
        /// <param name="telemetryWorker">Worker for telemetry information.</param>
        public ConfigurationRepository(IHtecLogger logger, IDbExecutorFactory dbExecutorFactory, ITelemetryWorker telemetryWorker): 
            base(logger, dbExecutorFactory, telemetryWorker)
        {
            RefreshStaticData();
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void RefreshStaticData()
        {
            var (isSuccess, _, configIds) = GetConfigIds();

            if (!isSuccess)
            {
                const string configError = "Failed to retrieve configuration categories from the database. Check the database connection string.";
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { configError });

                throw new InvalidOperationException(configError);
            }

            var contactless = configIds.ConfigCategories?.FirstOrDefault(x => x.Category.Equals(ConfigurationConstants.CategoryNameContactless, StringComparison.OrdinalIgnoreCase));
            if (contactless == null)
            {
                var configError = ConfigError(ConfigurationConstants.CategoryNameContactless);
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { configError });

                throw new InvalidOperationException(configError);
            }
            _contactlessId = contactless.Id;

            var siteInfo = configIds.ConfigCategories?.FirstOrDefault(x => x.Category.Equals(ConfigurationConstants.CategoryNameSiteInfo, StringComparison.OrdinalIgnoreCase));
            if (siteInfo == null)
            {
                var configError = ConfigError(ConfigurationConstants.CategoryNameSiteInfo);
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { configError });

                throw new InvalidOperationException(configError);
            }

            _siteInfoId = siteInfo.Id;

            var type = configIds.ConfigTypes?.FirstOrDefault(x => x.Type.Equals(TypeConsts.Service, StringComparison.Ordinal));

            if (type == null)
            {
                var configError = ConfigError(TypeConsts.Service);
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { configError });

                throw new InvalidOperationException(configError);
            }
            _siteControllerId = configIds.ConfigHeaders.First(x => x.TypeId == type.Id).Id;
        }

        private static string ConfigError(string configSection)
        {
            return $"Could not find config for {configSection}. Ensure database is up to date";
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public Result SetLocalAccountsEnabled(bool enabled)
        {
            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"{nameof(enabled)}:{enabled}" });

                var result = ExecuteSetConfig(_siteControllerId, _siteInfoId, DetailConsts.LocalAccountsEnabled, enabled.ToString(), LoggingReference, ConfigurationConstants.CategoryNameSiteInfo);
                return Result.SuccessIf(result.IsSuccess && result.Value > 0, "Failed to update value");
            }, string.Empty);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public Result SetIntegrationType(IntegrationType integrationType, string value, string loggingReference = null)
        {
            var key = integrationType.ToDescriptionValue();
            return ExecuteSetConfig(_siteControllerId, _siteInfoId, key, value, loggingReference, ConfigurationConstants.CategoryNameSiteInfo);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetContactless(bool isEnabled)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"{nameof(isEnabled)}:{isEnabled}" });
                ExecuteSetConfig(_siteControllerId, _contactlessId, DetailConsts.Enabled, $"{isEnabled}", LoggingReference, ConfigurationConstants.CategoryNameContactless);
            }, string.Empty);
        }

        private Result<int> ExecuteUpsertConfigurationDetail(object parameters)
        {
            return DbActionWithTelemetry(x => x.Execute(UpsertConfigDetailSproc, parameters, commandType: CommandType.StoredProcedure), UpsertConfigDetailSproc);
        }

        private Result<int> ExecuteSetConfig<T>(int headerId, int categoryId, string key, T value, string loggingReference, string category = null, string header = null)
        {
            DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"header[Id]/category[Id]/key/value: {(header.IsNullOrWhiteSpace() ? headerId : header)}/{(category.IsNullOrWhiteSpace() ? categoryId : category)}/{key}/{value}" });

            return DbActionWithTelemetry(db => db.Execute(SetConfigSproc, new { headerId, categoryId, key, value }, commandType: CommandType.StoredProcedure), SetConfigSproc, loggingReference);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetContactlessCardPreAuth(int limit)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"{nameof(limit)}:{limit}" });
                ExecuteSetConfig(_siteControllerId, _contactlessId, DetailConsts.CardPreAuth, limit, LoggingReference, ConfigurationConstants.CategoryNameContactless);
            }, string.Empty);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetContactlessDevicePreAuth(int limit)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"{nameof(limit)}:{limit}" });
                ExecuteSetConfig(_siteControllerId, _contactlessId, DetailConsts.DevicePreAuth, limit, LoggingReference, ConfigurationConstants.CategoryNameContactless);
            }, string.Empty);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetContactlessTtq(string ttq)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"{nameof(ttq)}:{ttq}" });
                ExecuteSetConfig(_siteControllerId, _contactlessId, DetailConsts.Ttq, ttq, LoggingReference, ConfigurationConstants.CategoryNameContactless);
            }, string.Empty);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetContactlessSingleButton(bool showSingleButton)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[]{$"{nameof(showSingleButton)}:{showSingleButton}"});
                ExecuteSetConfig(_siteControllerId, _contactlessId, DetailConsts.SingleButton, $"{showSingleButton}", LoggingReference, ConfigurationConstants.CategoryNameContactless);
            }, string.Empty);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public void SetCategories(IEnumerable<CategoryConfiguration> categories)
        {
            DoAction(() =>
            {
                foreach (var category in categories)
                {
                    foreach (var keyValue in category.Settings)
                    {
                        DoDeferredLogging(LogLevel.Info, "ConfigKey", () => new[] {$"{category.Category}{ConfigurationConstants.CategorySeparator}{keyValue.Key}; Value: {keyValue.Value.Value}"});

                        ExecuteUpsertConfigurationDetail(new
                        {
                            category = category.Category,
                            key = keyValue.Key,
                            value = keyValue.Value.Value
                        });
                    }
                }

                return Result.Success();
            }, LoggingReference);
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        public Result<SiteInfo> GetSiteInfo()
        {
            var result = DbActionWithTelemetry(x => x.Query<SiteInfo>(GetSiteInfoSproc, null, commandType: CommandType.StoredProcedure), GetSiteInfoSproc, LoggingReference.ToMessageTracking());
            return Result.SuccessIf(result.IsSuccess && result.Value.FirstOrDefault() != null, result.Value.FirstOrDefault(), "Failed to retrieve siteInfo");
        }

        /// <summary>
        /// Retrieves all category information from database
        /// </summary>
        /// <returns>List of categories</returns>
        public IList<CategoryConfiguration> GetCategoriesConfiguration()
        {
            var result = DoAction(() =>
            {
                var (isSuccess, _, configIds) = GetConfigIds();

                if (!isSuccess)
                {
                    const string configError = "Failed to retrieve configuration categories from the database. Check the database connection string.";
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { configError });

                    throw new InvalidOperationException(configError);
                }

                var categories = configIds.ConfigCategories.Select(x => new CategoryConfiguration()
                {
                    Id = x.Id,
                    Category = x.Category,
                    IsStandardEditable =  x.IsStandardEditable
                }).ToList();

                foreach (var category in categories)
                {
                    var categoryConfiguration = GetCategoryConfiguration(category.Id);

                    category.Settings = categoryConfiguration?.ToDictionary(x => x.Key, x =>
                        new ConfigKeyInfo(null, x.Value)) ?? new Dictionary<string, ConfigKeyInfo>();
                }

                var results = HasConfigurationAttribute.MergeConfigurableItems(Logger, categories);

                // Remove any Sumo items (as these are defined in SLIB)
                var categoryLogging = results.FirstOrDefault(x => x.Category == "LOGGING");
                if (categoryLogging != null)
                {
                    var settings = categoryLogging.Settings;
                    categoryLogging.Settings = settings.Where(x => !x.Key.Contains("Sumo", StringComparison.InvariantCultureIgnoreCase)).ToDictionary(x => x.Key, x => x.Value);
                }

                return Result.Success(results);
            }, LoggingReference);

            return result.Value;
        }

        /// <inheritdoc cref="IConfigurationRepository"/>
        private IEnumerable<CategoryConfigurationKeyValue> GetCategoryConfiguration(int categoryId)
        {
            var result = DbActionWithTelemetry(db => db.Query<CategoryConfigurationKeyValue>(GetCategoryConfigurationSproc, 
                new {headerId = _siteControllerId, categoryId = categoryId}, commandType: CommandType.StoredProcedure), GetCategoryConfigurationSproc);

            return result.IsSuccess ? result.Value : Enumerable.Empty<CategoryConfigurationKeyValue>();
        }

        private Result<ConfigurationIds> GetConfigIds()
        {
            var guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);

            // TODO: Use DapperWrapperRepositories magic in v2, as QueryMultiple - however, CoreRepository needs to take in IConfigurationManager!!
            var configIds = new ConfigurationIds();

            const string getConfigurationTypesSproc = "GetConfigurationTypes";

            try
            {
                using (var db = DbExecutorFactory.CreateExecutor())
                {
                    var configurationTypes = db.QueryMultiple(getConfigurationTypesSproc, commandType: CommandType.StoredProcedure);
                    configIds.ConfigTypes = configurationTypes.Read<ConfigType>();
                    configIds.ConfigCategories = configurationTypes.Read<ConfigCategory>();
                    configIds.ConfigHeaders = configurationTypes.Read<ConfigHeader>();
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { "Exception raised in database call" }, ex);
                return Result.Failure<ConfigurationIds>($"SQL exception: {ex.Message}");
            }

            TelemetryWorker.QueryReturnedFromHydraDb(getConfigurationTypesSproc, guid);

            return Result.Success(configIds);
        }
    }
}
