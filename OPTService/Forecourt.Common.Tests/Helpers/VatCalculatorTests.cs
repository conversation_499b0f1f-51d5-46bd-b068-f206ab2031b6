using FluentAssertions;
using Forecourt.Core.Helpers;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System;
using Xunit;

namespace Forecourt.Common.Tests.Helpers
{
    public class VatCalculatorTests
    {
        private readonly IVatCalculator _vatCalculator;
        private readonly IHtecLogger _logger;

        public VatCalculatorTests()
        {
            _logger = Substitute.For<IHtecLogger>();

            _vatCalculator = new VatCalculator(_logger);
        }

        [Theory,
            // 82.6 rounds up to 83
            InlineData(555, 17.5f, 83, 472),
            // 20.49 rounds down to 20
            InlineData(123, 19.99f, 20, 103),
            InlineData(1200, 20f, 200, 1000),
            InlineData(100, 5f, 5, 95),
            InlineData(1000, 5f, 48, 952),
            InlineData(1000, 5f, 48, 952, 1),
            InlineData(1000, 5.2f, 49, 951, 1),
            InlineData(1687, 20f, 281, 1406),
            InlineData(1948, 20f, 325, 1623),
            InlineData(1635, 20f, 273, 1362),
            InlineData(1148, 20f, 191, 957),
            InlineData(1148, 20.1f, 192, 956),

            InlineData(120, 20f, 20, 100),
            InlineData(121, 20f, 20, 101),
            InlineData(122, 20f, 20, 102),
            // 20.5 rounds up to 21
            InlineData(123, 20f, 21, 102),
            InlineData(124, 20f, 21, 103),
            InlineData(125, 20f, 21, 104),
            ]
        public void test_calculate_vat_uint(uint grossAmount, float vatRate, uint expectedVatAmount, uint expectedNetAmount, byte decPlaces = 0)
        {
            // Arrange

            // Act
            var (vatAmount, netAmount) = _vatCalculator.CalculateVat(grossAmount, vatRate, decPlaces);

            // Assert
            vatAmount.Should().Be(expectedVatAmount);
            netAmount.Should().Be(expectedNetAmount);
        }

        [Theory,
       InlineData(555, 17.5f, 82.66, 472.34),
       InlineData(123, 19.99f, 20.49, 102.51),
       InlineData(1200, 20f, 200, 1000),
       InlineData(100, 5f, 4.76, 95.24),
       InlineData(1000, 5f, 47.62, 952.38),
       InlineData(1687, 20f, 281.17, 1405.83),
       InlineData(1948, 20f, 324.67, 1623.33),
       InlineData(1635, 20f, 272.5, 1362.5),
       InlineData(1148, 20f, 191.33, 956.67),
       InlineData(1148, 20.1f, 192.13, 955.87),

       InlineData(120, 20f, 20, 100),
       InlineData(121, 20f, 20.17, 100.83),
       InlineData(122, 20f, 20.33, 101.67),
       InlineData(123, 20f, 20.5, 102.5),
       InlineData(124, 20f, 20.67, 103.33),
       InlineData(125, 20f, 20.83, 104.17),
       ]
        public void test_calculate_vat_double(double grossAmount, double vatRate, double expectedVatAmount, double expectedNetAmount, byte decPlaces = 2)
        {
            // Arrange

            // Act
            var (vatAmount, netAmount) = _vatCalculator.CalculateVat(grossAmount, vatRate, decPlaces);

            // Assert
            Math.Round(vatAmount, decPlaces).Should().Be(expectedVatAmount);
            Math.Round(netAmount, decPlaces).Should().Be(expectedNetAmount);
        }
    }
}
