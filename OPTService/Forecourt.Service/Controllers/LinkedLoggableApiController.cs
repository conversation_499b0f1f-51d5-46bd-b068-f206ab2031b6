using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using System.Net;
using System.Web.Http;

namespace Forecourt.Service.Controllers
{

    /// <inheritdoc/>

    public class LinkedLoggableApiController<TLoggable> : Htec.Foundation.Web.Http.LinkedLoggableApiController<TLoggable> where TLoggable : class
    {
        /// <inheritdoc/>
        public LinkedLoggableApiController(Loggable loggable, IConfigurationManager configurationManager = null) : base(loggable, configurationManager)
        {
        }

        protected virtual StatusCodeResult ResultMapper(Result r, HttpStatusCode failCode = HttpStatusCode.BadRequest) =>
            r.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(failCode, new System.Exception(r.Error));
    }
}
