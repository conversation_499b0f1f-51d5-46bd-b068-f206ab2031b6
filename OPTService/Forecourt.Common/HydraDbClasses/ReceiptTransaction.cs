using System;
using System.Globalization;
using optReceiptTransaction = Htec.Hydra.Messages.Opt.Models.ReceiptTransaction;

namespace OPT.Common.HydraDbClasses
{
    /// <summary>
    /// Receipt transaction model (inherited to add explicit casts)
    /// </summary>
    public class ReceiptTransaction : OPT.Common.HydraDb.Models.ReceiptTransaction
    {
        /// <inheritdoc cref="OPT.Common.HydraDb.Models.ReceiptTransaction"/>
        public ReceiptTransaction() { }

        /// <inheritdoc cref="OPT.Common.HydraDb.Models.ReceiptTransaction"/>
        public ReceiptTransaction(string cardNumber, string txnNumber, string details, long value, DateTime dateTime, int printedCount) : base(cardNumber, txnNumber, details, value, dateTime, printedCount)
        {
        }

        /// <summary>
        /// Convert from <see cref="ReceiptTransaction"/> to <see cref="optReceiptTransaction"/> by using a standard .net cast
        /// </summary>
        /// <param name="from"><see cref="ReceiptTransaction"/> instance</param>
        public static explicit operator optReceiptTransaction(ReceiptTransaction from)
        {
            return new optReceiptTransaction(from.Details, from.PrintedCount, from.TimeStamp, from.Value, from.CardNumber, from.TxnNumber);
        }

        /// <summary>
        /// Convert from <see cref="optReceiptTransaction"/> to <see cref="ReceiptTransaction"/> by using a standard .net cast
        /// </summary>
        /// <param name="from"><see cref="optReceiptTransaction"/> instance</param>
        public static explicit operator ReceiptTransaction(optReceiptTransaction from)
        {
            return new ReceiptTransaction(from.CardNumber, from.TxnNumber, from.Details, from.Value, GetTransactionTime(from), from.PrintedCount);
        }

        private static DateTime GetTransactionTime(optReceiptTransaction receipt)
        {
            return DateTime.TryParseExact(receipt.TimeStamp, "yyyyMMddHHmmss", CultureInfo.CurrentCulture, DateTimeStyles.None, out var dateTime) ? dateTime : DateTime.Now;
        }
    }
}
