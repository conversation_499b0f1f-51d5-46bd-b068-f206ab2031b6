using CsvHelper;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Abstractions;

namespace Forecourt.Bos.TransactionFiles
{
    public abstract class GenericTransactionFile : Loggable, IGenericTransactionFile
    {
        protected const string CsvExtension = ".csv";

        protected IFileSystem FileSystem { get; }
        protected IHydraDb HydraDb { get; }
        
        protected readonly object SyncObject = new object();

        public string FileDirectory { get; protected set; }

        public bool Available { get; private set; }

        internal Func<DateTime> GetNow { get; set; }

        /// <inheritdoc />
        protected AllFileLocations AllFileLocations => HydraDb.GetFileLocations();

        /// <inheritdoc />
        protected GenericTransactionFile(IHydraDb hydraDb, IHtecLogger logger, IFileSystem fileSystem, IConfigurationManager configurationManager = null): 
            base(logger, configurationManager)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            FileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));

            GetNow = () => DateTime.Now;
        }

        /// <inheritdoc />
        public void CheckFileDirectory(string directory = null)
        {
            try
            {
                // Does an internal check if it exists
                FileSystem.Directory.CreateDirectory(string.IsNullOrEmpty(directory) ? FileDirectory : directory);
            }
            catch (Exception exception)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"Error creating transaction file directory {directory}"}, exception);
            }
        }

        /// <inheritdoc />
        protected abstract void DoSetFileDirectoryAction(string directory);

        /// <inheritdoc />
        public void SetFileDirectory(string directory = null)
        {
            if (string.IsNullOrWhiteSpace(directory))
            {
                Available = false;
                DoSetFileDirectoryAction(null);
            }
            else
            {
                Available = true;
                FileDirectory = directory;

                DoSetFileDirectoryAction(directory);
                CheckFileDirectory(directory);
            }
        }

        protected void WriteItemsToFile<T>(IEnumerable<T> items, string fileName, string fileType)
        {
            CheckFileDirectory(FileDirectory);
            CheckFileDirectory(FileSystem.Path.GetDirectoryName(fileName));

            try
            {
                lock (SyncObject)
                {
                    using (var file = FileSystem.FileStream.Create(fileName, FileMode.Append, FileAccess.Write, FileShare.None))
                    {
                        using (var writer = new StreamWriter(file))
                        {
                            using (var csv = new CsvWriter(writer, CultureInfo.CurrentCulture))
                            {
                                foreach (var item in items)
                                {
                                    csv.WriteRecord(item);
                                    csv.NextRecord();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"Error creating {fileType} file {fileName}"}, exception);
            }

            VerifyFile(fileName);
        }

        protected void VerifyFile(string fileName)
        {
            try
            {
                var fileInfo = FileSystem.FileInfo.FromFileName(fileName);
                DoDeferredLogging(LogLevel.Info, "BytesWritten", () => new[] {$"{fileInfo.Length}; File: {fileName}"});
            }
            catch (Exception e)
            {
                DoDeferredLogging(LogLevel.Error, $"{HeaderException}.File", () => new[] {$"{fileName}"}, e);
            }
        }

        protected string CreateFileName(string name, string tillNumber, DateTime timeStamp, string flag = "")
        {
            return FileSystem.Path.Combine(FileDirectory, $"{name}{tillNumber}{timeStamp:yyMMddHHmmss}{flag}{CsvExtension}");
        }
    }
}
