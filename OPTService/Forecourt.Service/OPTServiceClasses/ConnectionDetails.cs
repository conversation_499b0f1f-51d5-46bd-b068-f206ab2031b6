using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class ConnectionDetails
    {
        public int ToOptPort { get; set; }
        public int FromOptPort { get; set; }
        public int HeartbeatPort { get; set; }
        public int HydraPosPort { get; set; }
        public int RetalixPosPort { get; set; }
        public int ThirdPartyPosPort { get; set; }
        public int MediaChannelPort { get; set; }
        public string AnprIpAddress { get; set; }
        public int AnprPort { get; set; }
        public string CarWashIpAddress { get; set; }
        public int CarWashPort { get; set; }
        public string PumpControllerIpAddress { get; set; }
        public string PumpControllerLogonInfo { get; set; }
        public int PumpControllerPort { get; set; }
        public string TankGaugeIpAddress { get; set; }
        public int TankGaugePort { get; set; }
        public string HydraMobileIpAddress { get; set; }
        public int HydraMobilePort { get; set; }
        public int OptConnectedCount { get; set; }
        public int HydraPosConnectedCount { get; set; }
        public int RetalixPosConnectedCount { get; set; }
        public int ThirdPartyPosConnectedCount { get; set; }
        public int MediaChannelConnectedCount { get; set; }
        public bool IsSecAuthConnected { get; set; }
        public bool CarWashConnected { get; set; }
        public bool TankGaugeConnected { get; set; }
        public bool HydraMobileConnected { get; set; }
        public bool PumpControllerConnected { get; set; }
        public bool PaymentConfigConnected { get; set; }
        public bool AutoAuth { get; set; }
        public bool MediaChannel { get; set; }
        public bool UnmannedPseudoPos { get; set; }
        public bool RetalixDefined { get; set; }
        public List<string> OptIpAddresses { get; set; }
        public List<string> HydraPosIpAddresses { get; set; }
        public List<string> RetalixPosIpAddresses { get; set; }
        public List<string> ThirdPartyPosIpAddresses { get; set; }
        public List<string> MediaChannelIpAddresses { get; set; }
        public string RetalixPosPrimaryIpAddress { get; set; }

        public int SignalRPosConnectedCount { get; set; }
        public List<string> SignalRPosIpAddresses { get; set; }
        public int SignalRPosInConnectedCount { get; set; }
        public List<string> SignalRPosInIpAddresses { get; set; }

        public int SignalRSecAuthConnectedCount { get; set; }
        public List<string> SignalRSecAuthIpAddresses { get; set; }

        public int SignalRBosConnectedCount { get; set; }
        public List<string> SignalRBosIpAddresses { get; set; }


        public bool IsHydraPosConnected => HydraPosConnectedCount > 0;
        public bool IsRetalixPosConnected => HydraPosConnectedCount > 0 && RetalixPosConnectedCount > 0;
        public bool IsThirdPartyPosConnected => ThirdPartyPosConnectedCount > 0;
        public bool IsSignalRPosConnected => SignalRPosConnectedCount > 0;
        public bool IsSignalRPosInConnected => SignalRPosInConnectedCount > 0;
        public bool IsPosConnected => IsHydraPosConnected || IsRetalixPosConnected || IsThirdPartyPosConnected || IsSignalRPosConnected || IsSignalRPosInConnected;

        public bool IsSignalRSecAuthConnected => SignalRSecAuthConnectedCount > 0;

        public bool IsSignalRBosConnected => SignalRBosConnectedCount > 0;
        public bool IsBosConnected => IsSignalRBosConnected;
    }
}
