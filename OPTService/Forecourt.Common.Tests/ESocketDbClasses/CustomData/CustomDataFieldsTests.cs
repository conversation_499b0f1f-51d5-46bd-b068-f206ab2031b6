using FluentAssertions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Tests.ESocketDbClasses.CustomData.TestData;
using System.Collections.Specialized;
using Xunit;

namespace OPT.Common.Tests.ESocketDbClasses.CustomData
{
    public class CustomDataFieldsTests
    {
        private readonly IHtecLogger _logger;
        private readonly IConfigurationManager _configurationManager;

        public CustomDataFieldsTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
        }

        #region Properties

        public static TheoryData<CustomDataFieldsTestData> GetData() =>
            new TheoryData<CustomDataFieldsTestData>
            {
                new CustomDataFieldsTestData(x => x.UdolIdent, CustomDataFields.DefaultUdolIdent),
                new CustomDataFieldsTestData(x => x.AdditionalIdent, CustomDataFields.DefaultAdditionalIdent),
                new CustomDataFieldsTestData(x => x.ContactlessIdent, CustomDataFields.DefaultContactlessIdent),
                new CustomDataFieldsTestData(x => x.TerminalRiskIdent, CustomDataFields.DefaultTerminalRiskIdent),
                new CustomDataFieldsTestData(x => x.TerminalIdent, CustomDataFields.DefaultTerminalIdent),
                new CustomDataFieldsTestData(x => x.MccIdent, CustomDataFields.DefaultMccIdent),
                new CustomDataFieldsTestData(x => x.TornIdent,CustomDataFields.DefaultTornIdent),
                new CustomDataFieldsTestData(x => x.TornLifeIdent, CustomDataFields.DefaultTornLifeIdent),
                new CustomDataFieldsTestData(x => x.CvmCapAboveIdent, CustomDataFields.DefaultCvmCapAboveIdent),
                new CustomDataFieldsTestData(x => x.CvmCapBelowIdent, CustomDataFields.DefaultCvmCapBelowIdent),
                new CustomDataFieldsTestData(x => x.CapAboveIdent, CustomDataFields.DefaultCapAboveIdent),
                new CustomDataFieldsTestData(x => x.CapBelowIdent, CustomDataFields.DefaultCapBelowIdent),
                new CustomDataFieldsTestData(x => x.MagstripeIdent, CustomDataFields.DefaultMagstripeIdent),
                new CustomDataFieldsTestData(x => x.ReaderIdent, CustomDataFields.DefaultReaderIdent),
                new CustomDataFieldsTestData(x => x.EnhancedReaderIdent, CustomDataFields.DefaultEnhancedReaderIdent),
                new CustomDataFieldsTestData(x => x.KernalIdent, CustomDataFields.DefaultKernalIdent),
                new CustomDataFieldsTestData(x => x.RiskParameterIdent, CustomDataFields.DefaultRiskParameterIdent)
            };

        [Theory]
        [MemberData(nameof(GetData), MemberType = typeof(CustomDataFieldsTests))]
        public void property_empty_config_returns_default_value(CustomDataFieldsTestData testData)
        {
            // Arrange
            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            var key = testData.GetMember(customDataFields);

            // Assert
            key.Should().Be(testData.Expected + "=");
        }

        #endregion

        #region Refresh Keys
        
        [Theory]
        [MemberData(nameof(GetData), MemberType = typeof(CustomDataFieldsTests))]
        public void refresh_keys_empty_config_returns_default_value(CustomDataFieldsTestData testData)
        {
            // Arrange
            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            customDataFields.RefreshKeys();

            var key = testData.GetMember(customDataFields);

            // Assert
            key.Should().Be(testData.Expected + "=");
        }

        [Theory]
        [MemberData(nameof(GetData), MemberType = typeof(CustomDataFieldsTests))]
        public void refresh_keys_valid_config_returns_overridden_value(CustomDataFieldsTestData testData)
        {
            // Arrange
            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { CustomDataFields.ConfigKeyCustomDataOverride, $"{testData.Expected}=CHANGED" }
            });

            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            customDataFields.RefreshKeys();

            var key = testData.GetMember(customDataFields);

            // Assert
            key.Should().Be("CHANGED=");
        }

        [Fact]
        public void refresh_keys_valid_config_with_multiple_values_returns_overridden_values()
        {
            // Arrange
            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { CustomDataFields.ConfigKeyCustomDataOverride, $"{CustomDataFields.DefaultUdolIdent}=CHANGED1,{CustomDataFields.DefaultAdditionalIdent}=CHANGED2" }
            });

            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            customDataFields.RefreshKeys();

            var key1 = customDataFields.UdolIdent;
            var key2 = customDataFields.AdditionalIdent;

            // Assert
            key1.Should().Be("CHANGED1=");
            key2.Should().Be("CHANGED2=");
        }

        [Fact]
        public void refresh_keys_valid_config_with_multiple_values_and_spaces_returns_overridden_values()
        {
            // Arrange
            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { CustomDataFields.ConfigKeyCustomDataOverride, $" {CustomDataFields.DefaultUdolIdent}=CHANGED1,   {CustomDataFields.DefaultAdditionalIdent}=CHANGED2  " }
            });

            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            customDataFields.RefreshKeys();

            var key1 = customDataFields.UdolIdent;
            var key2 = customDataFields.AdditionalIdent;

            // Assert
            key1.Should().Be("CHANGED1=");
            key2.Should().Be("CHANGED2=");
        }

        [Fact]
        public void refresh_keys_valid_config_with_duplicate_values_returns_first_value()
        {
            // Arrange
            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { CustomDataFields.ConfigKeyCustomDataOverride, $"{CustomDataFields.DefaultUdolIdent}=CHANGED1,{CustomDataFields.DefaultUdolIdent}=CHANGED2" }
            });

            var customDataFields = new CustomDataFields(_logger, _configurationManager);

            // Act
            customDataFields.RefreshKeys();

            var key1 = customDataFields.UdolIdent;

            // Assert
            key1.Should().Be("CHANGED1=");
        }

        #endregion
    }
}
