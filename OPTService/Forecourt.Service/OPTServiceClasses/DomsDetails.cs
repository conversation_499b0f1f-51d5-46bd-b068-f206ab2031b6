using System;
using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class DomsDetails
    {
        public bool IsFetchedSetup { get; set; }
        public bool IsPreparedSetup { get; set; }
        public bool IsTcpSetup { get; set; }
        public string IpAddress { get; set; }
        public string LoginString { get; set; }
        public bool Enabled { get; set; }
        public bool Connected { get; set; }
        public byte OperationMode { get; set; }
        public byte FcStatus1 { get; set; }
        public byte FcStatus2 { get; set; }
        public byte PosId { get; set; }
        public int PosCountryCode { get; set; }
        public string PosVersionId { get; set; }
        public byte MajorVersion { get; set; }
        public byte MinorVersion { get; set; }
        public float VatRate { get; set; }
        public byte ForecourtPriceSetId { get; set; }
        public DateTime ForecourtPriceSetTimestamp { get; set; }
        public DomsPriceSetDetails PriceSet { get; set; }
        public List<DomsFuellingPointDetails> FuellingPoints { get; set; }
        public List<DomsGradeDetails> Grades { get; set; }
        public List<DomsTankGaugeDetails> TankGauges { get; set; }
        public List<DomsPricePoleDetails> PricePoles { get; set; }
    }
}