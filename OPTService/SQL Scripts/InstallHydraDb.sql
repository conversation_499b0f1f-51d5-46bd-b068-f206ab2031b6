if not exists (select * from sys.databases where name = 'Hydra')
begin
  begin try
    create database Hydra on (filename = 'C:\HydraOPTService\DB\hydra.mdf') for attach
  end try 
  begin catch 
    create database Hydra on primary (name = Hydra, filename = 'C:\HydraOPTService\DB\hydra.mdf')
  end catch
end
go

use Hydra

-- Create Tables
if not exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u')
 begin
  create table OPTEndPoints
   (rowid int identity,
    HydraId varchar(50) not null unique default '',
    IPAddress varchar(15) not null default '127.0.0.1',
    FromOPTPort int not null default 1262,
    ToOPTPort int not null default 1263,
    HeartbeatPort int not null default 1264,
    HydraPOSPort int not null default 1261,
    RetalixPOSPort int not null default 10029,
    ThirdPartyPOSPort int not null default 10030,
    MediaChannelPort int not null default 1266,
    AutoAuth bit not null default 1,
    MediaChannel bit not null default 1,
    UnmannedPseudoPOS bit not null default 0,
    AsdaDayEndReport bit not null default 0,
    constraint pk_OPTEndPoints primary key (rowid))

  insert into OPTEndPoints default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u') and
   not exists (select * from sys.columns where name = 'RetalixPOSPort' and object_id = OBJECT_ID('OPTEndPoints'))
 begin
  alter table OPTEndPoints add RetalixPOSPort int not null default 10029
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u') and
   not exists (select * from sys.columns where name = 'MediaChannelPort' and object_id = OBJECT_ID('OPTEndPoints'))
 begin
  alter table OPTEndPoints add MediaChannelPort int not null default 1266
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u') and
   not exists (select * from sys.columns where name = 'MediaChannel' and object_id = OBJECT_ID('OPTEndPoints'))
 begin
  alter table OPTEndPoints add MediaChannel bit not null default 1
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u') and
   not exists (select * from sys.columns where name = 'UnmannedPseudoPOS' and object_id = OBJECT_ID('OPTEndPoints'))
 begin
  alter table OPTEndPoints add UnmannedPseudoPOS bit not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTEndPoints') and type = 'u') and
   not exists (select * from sys.columns where name = 'AsdaDayEndReport' and object_id = OBJECT_ID('OPTEndPoints'))
 begin
  alter table OPTEndPoints add AsdaDayEndReport bit not null default 0
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DivertOPT') and type = 'u')
 begin
  create table DivertOPT
   (rowid int default 0,
    IsDiverted bit not null default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    FromOPTPort int not null default 1262,
    ToOPTPort int not null default 1263,
    HeartbeatPort int not null default 1264,
    MediaChannelPort int not null default 1266,
    constraint pk_DivertOPT primary key (rowid),
    constraint ck_DivertOPT check (rowid = 0))

  insert into DivertOPT default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('DivertOPT') and type = 'u') and
   not exists (select * from sys.columns where name = 'MediaChannelPort' and object_id = OBJECT_ID('DivertOPT'))
 begin
  alter table DivertOPT add MediaChannelPort int not null default 1266
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('OPTMode') and type = 'u')
 begin
  create table OPTMode
   (rowid int identity,
    OPT varchar(1024) unique not null,
    Contactless bit not null default 0,
    ReceiptHeader varchar(max),
    PlaylistFileName varchar(max),
    LastLogTime DateTime2,
    constraint pk_OPTMode primary key (rowid))
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTMode') and type = 'u') and
   not exists (select * from sys.columns where name = 'ReceiptHeader' and object_id = OBJECT_ID('OPTMode'))
 begin
  alter table OPTMode add ReceiptHeader varchar(max)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTMode') and type = 'u') and
   not exists (select * from sys.columns where name = 'PlaylistFileName' and object_id = OBJECT_ID('OPTMode'))
 begin
  alter table OPTMode add PlaylistFileName varchar(max)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('OPTMode') and type = 'u') and
   not exists (select * from sys.columns where name = 'LastLogTime' and object_id = OBJECT_ID('OPTMode'))
 begin
  alter table OPTMode add LastLogTime DateTime2
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PumpEndPoint') and type = 'u')
 begin
  create table PumpEndPoint
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    Port int not null default 1259,
    constraint pk_PumpEndPoint primary key (rowid),
    constraint ck_PumpEndPoint check (rowid = 0))

  insert into PumpEndPoint default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ANPREndPoint') and type = 'u')
 begin
  create table ANPREndPoint
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
      Port int not null default 10026,
    constraint pk_ANPREndPoint primary key (rowid),
    constraint ck_ANPREndPoint check (rowid = 0))

  insert into ANPREndPoint default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CarWashEndPoint') and type = 'u')
 begin
  create table CarWashEndPoint
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    Port int not null default 1255,
    constraint pk_CarWashEndPoint primary key (rowid),
    constraint ck_CarWashEndPoint check (rowid = 0))

  insert into CarWashEndPoint default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('TankGaugeEndPoint') and type = 'u')
 begin
  create table TankGaugeEndPoint
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    Port int not null default 1257,
    constraint pk_TankGaugeEndPoint primary key (rowid),
    constraint ck_TankGaugeEndPoint check (rowid = 0))

  insert into TankGaugeEndPoint default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('HydraMobileEndPoint') and type = 'u')
 begin
  create table HydraMobileEndPoint
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    Port int not null default 10051,
    constraint pk_HydraMobileEndPoint primary key (rowid),
    constraint ck_HydraMobileEndPoint check (rowid = 0))

  insert into HydraMobileEndPoint default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PumpTIDs') and type = 'u')
 begin
  create table PumpTIDs
   (rowid int identity,
    Number int unique not null,
    TID varchar(1024),
    OPT varchar(1024),
    DefaultKioskOnly bit not null default 0,
    DefaultMixed bit not null default 0,
    CurrentKioskOnly bit not null default 0,
    CurrentMixed bit not null default 0,
    Closed bit not null default 0,
    MaxFillOverrideForFuelCards bit not null default 0,
    MaxFillOverrideForPaymentCards bit not null default 0,
    constraint pk_PumpTIDs primary key (rowid),
    constraint ck_PumpTIDs check
     ((DefaultKioskOnly = 0 or DefaultMixed = 0) and (CurrentKioskOnly = 0 or CurrentMixed = 0)))
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('PumpTIDs') and type = 'u') and
   not exists (select * from sys.columns where name = 'MaxFillOverrideForFuelCards' and object_id = OBJECT_ID('PumpTIDs'))
 begin
  alter table PumpTIDs add MaxFillOverrideForFuelCards bit not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('PumpTIDs') and type = 'u') and
   not exists (select * from sys.columns where name = 'MaxFillOverrideForPaymentCards' and object_id = OBJECT_ID('PumpTIDs'))
 begin
  alter table PumpTIDs add MaxFillOverrideForPaymentCards bit not null default 0
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('GradeName') and type = 'u')
 begin
  create table GradeName
   (rowid int identity,
    Grade int unique not null,
    GradeName varchar(20) not null,
    VatRate float not null,
    constraint pk_GradeName primary key (rowid))

  insert into GradeName (Grade, GradeName, VatRate) values (1, 'Unleaded', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (2, 'Diesel', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (3, 'LPG', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (4, 'LRP', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (5, 'Gas Oil Red Diesel', 5)
  insert into GradeName (Grade, GradeName, VatRate) values (6, 'Super Unleaded', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (7, 'Super Diesel', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (8, 'Adblue', 20)
  insert into GradeName (Grade, GradeName, VatRate) values (9, 'Kerosene', 5)
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ESocketEndPoints') and type = 'u')
 begin
  create table ESocketEndPoints
   (rowid int identity,
    IPAddress varchar(15) not null default '127.0.0.1',
    Port int not null default 40000,
    constraint pk_ESocketEndPoints primary key (rowid))

  insert into ESocketEndPoints (IPAddress, Port) values ('127.0.0.1', 40000)
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('TariffMappings') and type = 'u')
 begin
  create table TariffMappings
   (rowid int identity,
    Grade int not null,
    ProductCode varchar(20) not null,
    FuelCardsOnly bit not null default 0,
    constraint pk_TariffMappings primary key (rowid))
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('TariffMappings') and type = 'u') and
   not exists (select * from sys.columns where name = 'FuelCardsOnly' and object_id = OBJECT_ID('TariffMappings'))
 begin
  alter table TariffMappings add FuelCardsOnly bit not null default 0
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DiscountCards') and type = 'u')
 begin
  create table DiscountCards
   (rowid int identity,
    Iin varchar(20) not null,
    Name varchar(80),
    Type varchar(20) not null,
    Value float not null,
    Grade int not null,
    constraint pk_DiscountCards primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DiscountWhitelist') and type = 'u')
 begin
  create table DiscountWhitelist
   (rowid int identity,
    Iin varchar(20) not null,
    Pan varchar(20) not null,
    constraint pk_DiscountWhitelist primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LocalAccountCustomers') and type = 'u')
 begin
  create table LocalAccountCustomers
   (rowid int identity,
    Reference varchar(80) not null,
    Name varchar(80),
    TransactionsAllowed bit not null,
    TransLimit bigint not null,
    Pin bit not null,
    PrintValue bit not null,
    AllowLoyalty bit not null,
    FuelOnly bit not null,
    RegistrationEntry bit not null,
    MileageEntry bit not null,
    PrePayAccount bit not null,
    LowCreditWarning bit not null,
    MaxCreditReached bit not null,
    CustomerExists bit not null,
    Balance bigint not null,
    constraint pk_LocalAccountCustomers primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LocalAccountCards') and type = 'u')
 begin
  create table LocalAccountCards
   (rowid int identity,
    Pan varchar(20) not null,
    CustomerReference varchar(80) not null,
    Description varchar(80) not null,
    Discount float not null,
    NoRestrictions bit not null,
    Unleaded bit not null,
    Diesel bit not null,
    LPG bit not null,
    LRP bit not null,
    GasOil bit not null,
    AdBlue bit not null,
    Kerosene bit not null,
    Oil bit not null,
    Avgas bit not null,
    Jet bit not null,
    Mogas bit not null,
    Valeting bit not null,
    OtherMotorRelatedGoods bit not null,
    ShopGoods bit not null,
    Hot bit not null,
    constraint pk_LocalAccountCards primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PredefinedAmounts') and type = 'u')
 begin
  create table PredefinedAmounts
   (rowid int identity,
    Amount int not null,
    constraint pk_PredefinedAmounts primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyReference') and type = 'u')
 begin
  create table LoyaltyReference
   (rowid int identity,
    LoyaltyRef int not null,
    LoyaltyName varchar(max) not null,
    LoyaltyPresent bit not null,
    constraint pk_LoyaltyReference primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyTerminal') and type = 'u')
 begin
  create table LoyaltyTerminal
   (rowid int identity,
    LoyaltyRef int not null,
    SiteID varchar(20) not null default '',
    TerminalID varchar(20) not null default '',
    Footer1 varchar(80) not null default '',
    Footer2 varchar(80) not null default '',
    Timeout int not null default 0,
    ApiKey varchar(80) not null default '',
    HttpHeader varchar(80) not null default '',
    constraint pk_LoyaltyTerminal primary key (rowid))

 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyTerminal') and type = 'u') and
   not exists (select * from sys.columns where name = 'Timeout' and object_id = OBJECT_ID('LoyaltyTerminal'))
 begin
  alter table LoyaltyTerminal add Timeout int not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyTerminal') and type = 'u') and
   not exists (select * from sys.columns where name = 'ApiKey' and object_id = OBJECT_ID('LoyaltyTerminal'))
 begin
  alter table LoyaltyTerminal add ApiKey varchar(80) not null default ''
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyTerminal') and type = 'u') and
   not exists (select * from sys.columns where name = 'HttpHeader' and object_id = OBJECT_ID('LoyaltyTerminal'))
 begin
  alter table LoyaltyTerminal add HttpHeader varchar(80) not null default ''
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyHosts') and type = 'u')
 begin
  create table LoyaltyHosts
   (rowid int identity,
    LoyaltyRef int not null,
    IPAddress varchar(15) not null,
    Port int not null,
    constraint pk_LoyaltyHosts primary key (rowid))
  
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyHostnames') and type = 'u')
 begin
  create table LoyaltyHostnames
   (rowid int identity,
    LoyaltyRef int not null,
    Hostname varchar(255) not null,
    constraint pk_LoyaltyHostnames primary key (rowid))
  
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyIINs') and type = 'u')
 begin
  create table LoyaltyIINs
   (rowid int identity,
    LoyaltyRef int not null,
    Low varchar(20) not null,
    High varchar(20) not null,
    constraint pk_LoyaltyIINs primary key (rowid))

 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('LoyaltyMappings') and type = 'u')
 begin
  create table LoyaltyMappings
   (rowid int identity,
    LoyaltyRef int not null,
    ProductCode varchar(20) not null,
    LoyaltyCode varchar(20) not null,
    constraint pk_LoyaltyMappings primary key (rowid))

 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('Washes') and type = 'u')
 begin
  create table Washes
   (rowid int identity,
    ProgramId int not null unique,
    ProductCode varchar(20),
    Description varchar(80),
    Price varchar(20),
    VatRate varchar(20),
    Category int,
    Subcategory int,
    constraint pk_Washes primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CardClessAIDs') and type = 'u')
 begin
  create table CardClessAIDs
   (rowid int identity,
    AID varchar(32) not null,
    AppVerTerm char(4) not null,
    TransLimit varchar(12) not null,
    FloorLimit varchar(12) not null,
    CVMLimit varchar(12) not null,
    ODCVMLimit varchar(12) not null,
    TermAddCapabilities char(10) not null,
    TermCapabilitiesCVM char(10) not null,
    TermCapabilitiesNoCVM char(10) not null,
    TermRiskData varchar(16) not null,
    UDOL text not null,
    TACDefault char(10) not null,
    TACDenial char(10) not null,
    TACOnline char(10) not null
    constraint pk_CardClessAIDs primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CardClessDRLs') and type = 'u')
 begin
  create table CardClessDRLs
   (rowid int identity,
    AID varchar(32) not null,
    ProgramId varchar(1024),
    TransLimit varchar(12),
    CVMLimit varchar(12),
    FloorLimit varchar(12),
    constraint pk_CardClessDRLs primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PaymentTimeout') and type = 'u')
 begin
  create table PaymentTimeout
   (rowid int identity,
    Mode int not null default 0,
    Timeout int not null default 0,
    constraint pk_PaymentTimeout primary key (rowid))

 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ReceiptExpiry') and type = 'u')
 begin
  create table ReceiptExpiry
   (rowid int default 0,
    Timeout int not null default 3600,
    MaxCount int not null default 10,
    constraint pk_ReceiptExpiry primary key (rowid),
    constraint ck_ReceiptExpiry check (rowid = 0))

 end

go

  if exists (select * from sys.objects where object_id = OBJECT_ID('AddReceipt') and type in ('P', 'PC'))
   begin
    drop procedure AddReceipt
   end
  if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceipts') and type in ('P', 'PC'))
   begin
    drop procedure GetReceipts
   end
  if exists (select * from sys.objects where object_id = OBJECT_ID('PruneReceipts') and type in ('P', 'PC'))
   begin
    drop procedure PruneReceipts
   end
   
   if exists (select * from sys.objects where object_id = OBJECT_ID('Receipts') and type = 'u') 
    begin 
	 drop table Receipts
   end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('Receipts') and type = 'u')
 begin
  create table Receipts
   (rowid int identity,
    CardNumber varchar(20) not null,
    ReceiptContent varchar(max) not null,
    Expiry DateTime2 not null,
    OPT varchar(1024),
    TransactionNumber bigint not null,
    ReceiptHash VARBINARY(64) NOT NULL,
	PrintedCount int,
    Amount bigint,
    TransactionTime DateTime2 
    constraint pk_Receipts primary key (rowid))
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Receipts') and type = 'u') and
   not exists (select * from sys.columns where name = 'ReceiptHash' and object_id = OBJECT_ID('Receipts'))
 begin
  alter table Receipts add ReceiptHash VARBINARY(64) NOT NULL default 0;
 END
 
GO

if not exists (select * from sys.objects where object_id = OBJECT_ID('Fuelling') and type = 'u')
 begin
  create table Fuelling
   (rowid int default 0,
    IndefiniteWait bit not null default 1,
    WaitMinutes int not null default 0,
    BackoffAuth int not null default 0,
    BackoffPreAuth int not null default 0,
    BackoffStopStart int not null default 0,
    BackoffStopOnly int not null default 0,
    constraint pk_Fuelling primary key (rowid),
    constraint ck_Fuelling check (rowid = 0))

  insert into Fuelling default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Fuelling') and type = 'u') and
   not exists (select * from sys.columns where name = 'BackoffPreAuth' and object_id = OBJECT_ID('Fuelling'))
 begin
  alter table Fuelling add BackoffPreAuth int not null default 0
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u')
 begin
  create table Transactions
   (rowid bigint identity,
    GradeName varchar(20),
    WashName varchar(20),
    FuelCode varchar(13),
    WashCode varchar(13),
    FuelQuantity bigint not null default 0,
    WashQuantity int not null default 0,
    Amount bigint not null default 0,
    PumpDetails varchar(20),
    CardNumber varchar(20),
    TransactionTime DateTime2 not null,
    FuelCategory varchar(20),
    WashCategory varchar(20),
    FuelSubcategory varchar(20),
    WashSubCategory varchar(20),
    DiscountName varchar(20),
    DiscountCode varchar(20),
    DiscountValue bigint not null default 0,
    DiscountCardNumber varchar(20),
    LocalAccountMileage bigint not null default 0,
    LocalAccountRegistration varchar(20),
    TxnNumber varchar(20),
    constraint pk_Transactions primary key (rowid))
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'DiscountName' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add DiscountName varchar(20)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'DiscountCode' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add DiscountCode varchar(20)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'DiscountValue' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add DiscountValue bigint not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'DiscountCardNumber' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add DiscountCardNumber varchar(20)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'LocalAccountMileage' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add LocalAccountMileage bigint not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'LocalAccountRegistration' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add LocalAccountRegistration varchar(20)
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('Transactions') and type = 'u') and
   not exists (select * from sys.columns where name = 'TxnNumber' and object_id = OBJECT_ID('Transactions'))
 begin
  alter table Transactions add TxnNumber varchar(20)
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DayEnd') and type = 'u')
 begin
  create table DayEnd
   (rowid int default 0,
    StartTime DateTime2 default GETDATE(),
    FuelCash bigint default 0,
    DryCash bigint default 0,
    Discount bigint default 0,
    FirstTransaction int default 0,
    LastTransaction int default 0,
    constraint pk_DayEnd primary key (rowid),
    constraint ck_DayEnd check (rowid = 0))

  insert into DayEnd default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('DayEnd') and type = 'u') and
   not exists (select * from sys.columns where name = 'Discount' and object_id = OBJECT_ID('DayEnd'))
 begin
  alter table DayEnd add Discount bigint default 0
 end

go

update DayEnd set Discount = 0 where Discount is null

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ShiftSummary') and type = 'u')
 begin
  create table ShiftSummary
   (rowid int default 0,
    StartTime DateTime2 default GETDATE(),
    FuelCash bigint default 0,
    DryCash bigint default 0,
    Discount bigint default 0,
    FirstTransaction int default 0,
    LastTransaction int default 0,
    ShiftNumber int default 1,
    constraint pk_ShiftSummary primary key (rowid),
    constraint ck_ShiftSummary check (rowid = 0))

  insert into ShiftSummary default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('ShiftSummary') and type = 'u') and
   not exists (select * from sys.columns where name = 'Discount' and object_id = OBJECT_ID('ShiftSummary'))
 begin
  alter table ShiftSummary add Discount bigint default 0
 end

go

update ShiftSummary set Discount = 0 where Discount is null

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ItemSales') and type = 'u')
 begin
  create table ItemSales
   (rowid int identity,
    Category int,
    Subcategory int,
    GradeCode varchar(13),
    GradeName varchar(20),
    Amount bigint default 0,
    Quantity bigint default 0,
    constraint pk_ItemSales primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DayItemSales') and type = 'u')
 begin
  create table DayItemSales
   (rowid int identity,
    Category int,
    Subcategory int,
    GradeCode varchar(13),
    GradeName varchar(20),
    Amount bigint default 0,
    Quantity bigint default 0,
    constraint pk_DayItemSales primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CardSales') and type = 'u')
 begin
  create table CardSales
   (rowid int identity,
    CardRef int,
    Amount bigint default 0,
    constraint pk_CardSales primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DayCardSales') and type = 'u')
 begin
  create table DayCardSales
   (rowid int identity,
    CardRef int,
    Amount bigint default 0,
    constraint pk_DayCardSales primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CardVolumeSales') and type = 'u')
 begin
  create table CardVolumeSales
   (rowid int identity,
    CardRef int,
    Grade int default 0,
    Volume bigint default 0,
    constraint pk_CardVolumeSales primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('AcquirerReference') and type = 'u')
 begin
  create table AcquirerReference
   (rowid int identity,
    AcquirerRef int unique not null,
    AcquirerName varchar(max),
    constraint pk_AcquirerReference primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('CardReference') and type = 'u')
 begin
  create table CardReference
   (rowid int identity,
    CardRef int unique not null,
    CardProductName varchar(max),
    AcquirerRef int,
    FuelCard bit not null default 0,
    constraint pk_CardReference primary key (rowid))

  if not exists (select * from AcquirerReference)
    begin
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (1, 'ASDA Fuel Card')
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (2, 'All other fuel cards')
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (3, 'American Express')
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (4, 'Diners Club')
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (5, 'Switch & Solo')
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (6, 'Visa,Mastercard,Delta&Electron')
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (1, 'VISA', 6, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (2, 'MASTERCARD', 6, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (3, 'SWITCH', 5, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (4, 'DELTA', 6, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (5, 'DINERS CLUB', 4, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (6, 'FORTE GOLDCARD', 4, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (7, 'AMERICAN EXPRESS', 3, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (8, 'ALL STAR', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (9, 'SUPERCHARGE', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (10, 'FUELCHECK', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (11, 'TEXACO EIRE', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (12, 'KEYFUELS', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (13, 'OVERDRIVE BUNKER', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (14, 'HARPUR GROUP', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (15, 'DIALCARD', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (16, 'ESSO CHARGECARD', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (17, 'STYLE', 6, 0)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (18, 'STATOIL', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (19, 'BP AGENCY', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (20, 'BP BUNKER', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (21, 'BP INTERNATIONAL', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (22, 'SHELL AGENCY', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (23, 'FORESERVE', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (24, 'EUROSHELL', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (25, 'SHELL GOLD', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (26, 'MAXOL', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (27, 'ESSO EUROPE Mk 1', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (28, 'ESSO EUROPE Mk II', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (29, 'ASDA FUEL CARD', 1, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (30, 'ROUTEX', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (31, 'PHH FUEL & JET WASH', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (32, 'PHH LOCAL ACCOUNT', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (33, 'PHH DART', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (34, 'GULF NETWORK ACCOUNT', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (35, 'TOTAL 4-COURT', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (36, 'FINA BUSINESS CARD', 2, 1)
     insert into CardReference (CardRef, CardProductName, AcquirerRef, FuelCard) values (37, 'FINA PRIVATE CARD', 2, 1)
    end
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('CardReference') and type = 'u') and
   not exists (select * from sys.columns where name = 'AcquirerRef' and object_id = OBJECT_ID('CardReference'))
 begin
  alter table CardReference add AcquirerRef int
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('CardReference') and type = 'u') and
   not exists (select * from sys.columns where name = 'FuelCard' and object_id = OBJECT_ID('CardReference'))
 begin
  alter table CardReference add FuelCard bit not null default 0
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PumpDelivered') and type = 'u')
 begin
  create table PumpDelivered
   (rowid int identity,
    Number int unique not null,
    OPTPayment bit default 0,
    Delivered bit default 0,
    Grade int,
    Volume bigint,
    Amount bigint,
    Name varchar(20),
    Price int,
    NetAmount bigint,
    VatAmount bigint,
    VatRate float,
    constraint pk_PumpDelivered primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('RetalixPrimary') and type = 'u')
 begin
  create table RetalixPrimary
   (rowid int not null default 0,
    IPAddress varchar(15),
    constraint pk_RetalixPrimary primary key (rowid),
    constraint ck_RetalixPrimary check (rowid = 0))

 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u')
 begin
  create table FileLocations
   (rowid int not null default 0,
    RetalixTransactionFileDirectory varchar(max) default 'C:\HydraOPTService\RetalixTransactionFiles',
    TransactionFileDirectory varchar(max) default 'C:\HydraOPTService\TransactionFiles',
    WhitelistDirectory varchar(max) default 'C:\HydraOPTService\Whitelist',
    LayoutDirectory varchar(max) default 'C:\HydraOPTService\Layout',
    SoftwareDirectory varchar(max) default 'C:\HydraOPTService\Software',
    ContactlessPropertiesFile varchar(max) default 'C:\HydraOPTService\contactless.properties',
    FuelDataUpdateFile varchar(max) default 'C:\HydraOPTService\ToSend\FuelData.upd',
    UpgradeFileDirectory varchar(max) default 'C:\HydraOPTService\Upgrade',
    RollbackFileDirectory varchar(max) default 'C:\HydraOPTService\Archive',
    MediaDirectory varchar(max) default 'C:\HydraOPTService\Media',
    PlaylistDirectory varchar(max) default 'C:\HydraOPTService\Playlist',
    OPTLogFileDirectory varchar(max) default 'C:\HydraOPTService\OPT Logs',
    LogFileDirectory varchar(max) default 'C:\HydraOPTService\Logs',
    TraceFileDirectory varchar(max) default 'C:\HydraOPTService\Traces',
    JournalFileDirectory varchar(max) default 'C:\HydraOPTService\JournalFiles',
    ReceivedUpdateDirectory varchar(max) default 'C:\HydraOPTService\Received',
    DatabaseBackupDirectory varchar(max) default 'C:\HydraOPTService\Database Backups',
    ESocketConnectionString varchar(max) default 'Data Source=localhost\sqlesp;Initial Catalog=esocketpos;Integrated Security=True;',
    ESocketUseConnectionString bit not null default 1,
    ESocketConfigFile varchar(max) default 'C:\postilion\eSocket.POS\properties.txt',
    ESocketKeystoreFile varchar(max) default 'C:\postilion\eSocket.POS\keystore\esp.ks',
    ESocketDbUrl varchar(max) default '********************************',
    ESocketOverrideProperties bit not null default 0,
    ESocketOverrideKeystore bit not null default 0,
    ESocketOverrideUrl bit not null default 0,
    ESocketOverrideContactless bit not null default 0,
    constraint pk_FileLocations primary key (rowid),
    constraint ck_FileLocations check (rowid = 0))

  insert into FileLocations default values
  
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'LayoutDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add LayoutDirectory varchar(max) default 'C:\HydraOPTService\Layout'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'MediaDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add MediaDirectory varchar(max) default 'C:\HydraOPTService\Media'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'PlaylistDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add PlaylistDirectory varchar(max) default 'C:\HydraOPTService\Playlist'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'OPTLogFileDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add OPTLogFileDirectory varchar(max) default 'C:\HydraOPTService\OPT Logs'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'LogFileDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add LogFileDirectory varchar(max) default 'C:\HydraOPTService\Logs'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'TraceFileDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add TraceFileDirectory varchar(max) default 'C:\HydraOPTService\Traces'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'JournalFileDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add JournalFileDirectory varchar(max) default 'C:\HydraOPTService\JournalFiles'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ReceivedUpdateDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ReceivedUpdateDirectory varchar(max) default 'C:\HydraOPTService\Received'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'DatabaseBackupDirectory' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add DatabaseBackupDirectory varchar(max) default 'C:\HydraOPTService\Datbase Backups'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketConnectionString' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketConnectionString varchar(max) default '// TODO:'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketUseConnectionString' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketUseConnectionString bit not null default 1
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketConfigFile' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketConfigFile varchar(max) default 'C:\HydraOPTService\eSocket.POS\properties.txt'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketKeystoreFile' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketKeystoreFile varchar(max) default 'C:\HydraOPTService\eSocket.POS\keystore\esp.ks'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketDbUrl' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketDbUrl varchar(max) default '********************************'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketOverrideProperties' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketOverrideProperties bit not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketOverrideKeystore' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketOverrideKeystore bit not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketOverrideUrl' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketOverrideUrl bit not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('FileLocations') and type = 'u') and
   not exists (select * from sys.columns where name = 'ESocketOverrideContactless' and object_id = OBJECT_ID('FileLocations'))
 begin
  alter table FileLocations add ESocketOverrideContactless bit not null default 0
 end

go


update FileLocations set LayoutDirectory = 'C:\HydraOPTService\Layout' where LayoutDirectory is null
update FileLocations set OPTLogFileDirectory = 'C:\HydraOPTService\OPT Logs' where OPTLogFileDirectory is null
update FileLocations set LogFileDirectory = 'C:\HydraOPTService\Logs' where LogFileDirectory is null

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('SiteInfo') and type = 'u')
 begin
  create table SiteInfo
   (rowid int not null default 0,
    Mode int not null default 1,
    SiteName varchar(80),
    VATNumber varchar(40),
    NozzleUpForKioskUse bit not null default 1,
    UseReplaceNozzleScreen bit not null default 1,
    CurrencyCode int not null default 826,
    ForwardFuelPriceUpdate bit not null default 0,
    TillNumber int not null default 99,
    FuelCategory int not null default 99,
    MaxFillOverride bigint not null default 0,
    constraint pk_SiteInfo primary key (rowid),
    constraint ck_SiteInfo check (rowid = 0))

 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('SiteInfo') and type = 'u') and
   not exists (select * from sys.columns where name = 'MaxFillOverride' and object_id = OBJECT_ID('SiteInfo'))
 begin
  alter table SiteInfo add MaxFillOverride bigint not null default 0
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('SiteInfo') and type = 'u') and
   not exists (select * from sys.columns where name = 'UseReplaceNozzleScreen' and object_id = OBJECT_ID('SiteInfo'))
 begin
  alter table SiteInfo add UseReplaceNozzleScreen bit not null default 1
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PosClaim') and type = 'u')
 begin
  create table PosClaim
   (rowid int not null default 0,
    PosNumber int not null default 99,
    constraint pk_PosClaim primary key (rowid),
    constraint ck_PosClaim check (rowid = 0))

 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PruneDays') and type = 'u')
 begin
  create table PruneDays
   (rowid int not null default 0,
    FilePruneDays int not null default 30,
    TransactionPruneDays int not null default 50,
    ReceiptPruneDays int not null default 14,
    constraint pk_PruneDays primary key (rowid),
    constraint ck_PruneDays check (rowid = 0))

  insert into PruneDays default values
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('NextDayEnd') and type = 'u')
 begin
  create table NextDayEnd
   (rowid int not null default 0,
    DayEnd DateTime2,
    LogInterval int not null default 3600,
    constraint pk_NextDayEnd primary key (rowid),
    constraint ck_NextDayEnd check (rowid = 0))

 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('NextDayEnd') and type = 'u') and
   not exists (select * from sys.columns where name = 'LogInterval' and object_id = OBJECT_ID('NextDayEnd'))
 begin
  alter table NextDayEnd add LogInterval int not null default 3600
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PrinterConfig') and type = 'u')
 begin
  create table PrinterConfig
   (rowid int not null default 0,
    Enabled bit not null default 0,
    PortName varchar(max) not null default 'COM1',
    BaudRate int not null default 9600,
    Handshake varchar(20) not null default 'None',
    StopBits varchar(12) not null default 'One',
    DataBits int not null default 8,
    constraint pk_PrinterConfig primary key (rowid),
    constraint ck_PrinterConfig check (rowid = 0))

  insert into PrinterConfig default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('PrinterConfig') and type = 'u') and
   not exists (select * from sys.columns where name = 'Handshake' and object_id = OBJECT_ID('PrinterConfig'))
 begin
  alter table PrinterConfig add Handshake varchar(20) not null default 'None'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('PrinterConfig') and type = 'u') and
   not exists (select * from sys.columns where name = 'StopBits' and object_id = OBJECT_ID('PrinterConfig'))
 begin
  alter table PrinterConfig add StopBits varchar(12) not null default 'One'
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('PrinterConfig') and type = 'u') and
   not exists (select * from sys.columns where name = 'DataBits' and object_id = OBJECT_ID('PrinterConfig'))
 begin
  alter table PrinterConfig add DataBits int not null default 8
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('PreviousMeterReadings') and type = 'u')
 begin
  create table PreviousMeterReadings
   (rowid int identity,
    Pump int not null default 0,
    Volume1 bigint not null default 0,
    Cash1 bigint not null default 0,
    Volume2 bigint not null default 0,
    Cash2 bigint not null default 0,
    Volume3 bigint not null default 0,
    Cash3 bigint not null default 0,
    Volume4 bigint not null default 0,
    Cash4 bigint not null default 0,
    constraint pk_PreviousMeterReadings primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('ShiftList') and type = 'u')
 begin
  create table ShiftList
   (rowid int identity,
    ShiftNumber int not null default 1,
    StartTime DateTime2 not null default GETDATE(),
    EndTime DateTime2 not null default GETDATE(),
    constraint pk_ShiftList primary key (rowid))
 end

go

if not exists (select * from sys.objects where object_id = OBJECT_ID('DOMSInfo') and type = 'u')
 begin
  create table DOMSInfo
   (rowid int default 0,
    IPAddress varchar(15) not null default '127.0.0.1',
    LoginString varchar(80) not null default 'POS,APPL_ID=H1',
    Enabled bit not null default 0,
    Detect bit not null default 1,
    constraint pk_DOMSInfo primary key (rowid),
    constraint ck_DOMSInfo check (rowid = 0))

  insert into DOMSInfo default values
 end

go

if exists (select * from sys.objects where object_id = OBJECT_ID('DOMSInfo') and type = 'u') and
   not exists (select * from sys.columns where name = 'Detect' and object_id = OBJECT_ID('DOMSInfo'))
 begin
  alter table DOMSInfo add Detect bit not null default 1
 end

go

-- Drop Stored Procedures
if exists (select * from sys.objects where object_id = OBJECT_ID('GetOPTEndPoints') and type in ('P', 'PC'))
 begin
  drop procedure GetOPTEndPoints
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetOPTEndPoints') and type in ('P', 'PC'))
 begin
  drop procedure SetOPTEndPoints
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveOPTEndPoints') and type in ('P', 'PC'))
 begin
  drop procedure RemoveOPTEndPoints
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetAutoAuth') and type in ('P', 'PC'))
 begin
  drop procedure SetAutoAuth
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetMediaChannel') and type in ('P', 'PC'))
 begin
  drop procedure SetMediaChannel
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetUnmannedPseudoPOS') and type in ('P', 'PC'))
 begin
  drop procedure SetUnmannedPseudoPOS
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetAsdaDayEndReport') and type in ('P', 'PC'))
 begin
  drop procedure SetAsdaDayEndReport
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetDivertOPT') and type in ('P', 'PC'))
 begin
  drop procedure GetDivertOPT
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDivertOPT') and type in ('P', 'PC'))
 begin
  drop procedure SetDivertOPT
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetOPTDiverted') and type in ('P', 'PC'))
 begin
  drop procedure SetOPTDiverted
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetOPTMode') and type in ('P', 'PC'))
 begin
  drop procedure GetOPTMode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetOPTContactless') and type in ('P', 'PC'))
 begin
  drop procedure SetOPTContactless
 end
 
if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceiptHeader') and type in ('P', 'PC'))
 begin
  drop procedure SetReceiptHeader
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPlaylistFileName') and type in ('P', 'PC'))
 begin
  drop procedure SetPlaylistFileName
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLastLogTime') and type in ('P', 'PC'))
 begin
  drop procedure SetLastLogTime
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearOPTMode') and type in ('P', 'PC'))
 begin
  drop procedure ClearOPTMode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPumpEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure GetPumpEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetANPREndPoint') and type in ('P', 'PC'))
 begin
  drop procedure GetANPREndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetANPREndPoint') and type in ('P', 'PC'))
 begin
  drop procedure SetANPREndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetCarWashEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure GetCarWashEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetCarWashEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure SetCarWashEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetTankGaugeEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure GetTankGaugeEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetTankGaugeEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure SetTankGaugeEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetHydraMobileEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure GetHydraMobileEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetHydraMobileEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure SetHydraMobileEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPumpTIDs') and type in ('P', 'PC'))
 begin
  drop procedure GetPumpTIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddPumpTID') and type in ('P', 'PC'))
 begin
  drop procedure AddPumpTID
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddPumpOPT') and type in ('P', 'PC'))
 begin
  drop procedure AddPumpOPT
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpKioskOnly') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpKioskOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpOutsideOnly') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpOutsideOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpMixed') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpMixed
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpClosed') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpClosed
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpMaxFillOverrideForFuelCards') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpMaxFillOverrideForFuelCards
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpMaxFillOverrideForPaymentCards') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpMaxFillOverrideForPaymentCards
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPumpTIDs') and type in ('P', 'PC'))
 begin
  drop procedure ClearPumpTIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetGradeNames') and type in ('P', 'PC'))
 begin
  drop procedure GetGradeNames
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetGradeName') and type in ('P', 'PC'))
 begin
  drop procedure SetGradeName
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearGradeNames') and type in ('P', 'PC'))
 begin
  drop procedure ClearGradeNames
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetESocketEndPoints') and type in ('P', 'PC'))
 begin
  drop procedure GetESocketEndPoints
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddESocketEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure AddESocketEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveESocketEndPoint') and type in ('P', 'PC'))
 begin
  drop procedure RemoveESocketEndPoint
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearESocketEndPoints') and type in ('P', 'PC'))
 begin
  drop procedure ClearESocketEndPoints
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetTariffMappings') and type in ('P', 'PC'))
 begin
  drop procedure GetTariffMappings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddTariffMapping') and type in ('P', 'PC'))
 begin
  drop procedure AddTariffMapping
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearTariffMappings') and type in ('P', 'PC'))
 begin
  drop procedure ClearTariffMappings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuelCardsOnly') and type in ('P', 'PC'))
 begin
  drop procedure SetFuelCardsOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPredefinedAmounts') and type in ('P', 'PC'))
 begin
  drop procedure GetPredefinedAmounts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddPredefinedAmount') and type in ('P', 'PC'))
 begin
  drop procedure AddPredefinedAmount
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPredefinedAmounts') and type in ('P', 'PC'))
 begin
  drop procedure ClearPredefinedAmounts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetDiscountCards') and type in ('P', 'PC'))
 begin
  drop procedure GetDiscountCards
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddDiscountCard') and type in ('P', 'PC'))
 begin
  drop procedure AddDiscountCard
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveDiscountCard') and type in ('P', 'PC'))
 begin
  drop procedure RemoveDiscountCard
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetDiscountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure GetDiscountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddDiscountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure AddDiscountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveDiscountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure RemoveDiscountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLocalAccountCustomers') and type in ('P', 'PC'))
 begin
  drop procedure GetLocalAccountCustomers
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLocalAccountCards') and type in ('P', 'PC'))
 begin
  drop procedure GetLocalAccountCards
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLocalAccountCustomer') and type in ('P', 'PC'))
 begin
  drop procedure AddLocalAccountCustomer
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('DeleteLocalAccountCustomer') and type in ('P', 'PC'))
 begin
  drop procedure DeleteLocalAccountCustomer
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLocalAccountCard') and type in ('P', 'PC'))
 begin
  drop procedure AddLocalAccountCard
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('DeleteLocalAccountCard') and type in ('P', 'PC'))
 begin
  drop procedure DeleteLocalAccountCard
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLoyaltyReference') and type in ('P', 'PC'))
 begin
  drop procedure AddLoyaltyReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('DeleteLoyaltyReference') and type in ('P', 'PC'))
 begin
  drop procedure DeleteLoyaltyReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLoyaltyPresent') and type in ('P', 'PC'))
 begin
  drop procedure SetLoyaltyPresent
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyPresent') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyPresent
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyTerminal') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyTerminal
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyHosts') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyHosts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyHostnames') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyHostnames
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyIINs') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyIINs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLoyaltyMappings') and type in ('P', 'PC'))
 begin
  drop procedure GetLoyaltyMappings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLoyaltyTerminal') and type in ('P', 'PC'))
 begin
  drop procedure SetLoyaltyTerminal
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLoyaltyHost') and type in ('P', 'PC'))
 begin
  drop procedure AddLoyaltyHost
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLoyaltyHostname') and type in ('P', 'PC'))
 begin
  drop procedure AddLoyaltyHostname
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLoyaltyIIN') and type in ('P', 'PC'))
 begin
  drop procedure AddLoyaltyIIN
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLoyaltyMapping') and type in ('P', 'PC'))
 begin
  drop procedure AddLoyaltyMapping
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearLoyalty') and type in ('P', 'PC'))
 begin
  drop procedure ClearLoyalty
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetWashes') and type in ('P', 'PC'))
 begin
  drop procedure GetWashes
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddWash') and type in ('P', 'PC'))
 begin
  drop procedure AddWash
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveWash') and type in ('P', 'PC'))
 begin
  drop procedure RemoveWash
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetCardClessAIDs') and type in ('P', 'PC'))
 begin
  drop procedure GetCardClessAIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetCardClessDRLs') and type in ('P', 'PC'))
 begin
  drop procedure GetCardClessDRLs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddCardClessAID') and type in ('P', 'PC'))
 begin
  drop procedure AddCardClessAID
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddCardClessDRL') and type in ('P', 'PC'))
 begin
  drop procedure AddCardClessDRL
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveCardClessAID') and type in ('P', 'PC'))
 begin
  drop procedure RemoveCardClessAID
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveCardClessDRL') and type in ('P', 'PC'))
 begin
  drop procedure RemoveCardClessDRL
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearCardClessAIDs') and type in ('P', 'PC'))
 begin
  drop procedure ClearCardClessAIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearCardClessAIDsOnly') and type in ('P', 'PC'))
 begin
  drop procedure ClearCardClessAIDsOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPaymentTimeout') and type in ('P', 'PC'))
 begin
  drop procedure GetPaymentTimeout
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPaymentTimeout') and type in ('P', 'PC'))
 begin
  drop procedure SetPaymentTimeout
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPaymentTimeout') and type in ('P', 'PC'))
 begin
  drop procedure ClearPaymentTimeout
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetFuelling') and type in ('P', 'PC'))
 begin
  drop procedure GetFuelling
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingIndefiniteWait') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingIndefiniteWait
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingWaitMinutes') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingWaitMinutes
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingBackoffAuth') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingBackoffAuth
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingBackoffPreAuth') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingBackoffPreAuth
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingBackoffStopStart') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingBackoffStopStart
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuellingBackoffStopOnly') and type in ('P', 'PC'))
 begin
  drop procedure SetFuellingBackoffStopOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddReceipt') and type in ('P', 'PC'))
 begin
  drop procedure AddReceipt
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceiptTimeout') and type in ('P', 'PC'))
 begin
  drop procedure SetReceiptTimeout
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceiptMaxCount') and type in ('P', 'PC'))
 begin
  drop procedure SetReceiptMaxCount
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceiptTimeout') and type in ('P', 'PC'))
 begin
  drop procedure GetReceiptTimeout
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceiptMaxCount') and type in ('P', 'PC'))
 begin
  drop procedure GetReceiptMaxCount
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceipt') and type in ('P', 'PC'))
 begin
  drop procedure GetReceipt
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('PruneReceipts') and type in ('P', 'PC'))
 begin
  drop procedure PruneReceipts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetOtherEvents') and type in ('P', 'PC'))
 begin
  drop procedure GetOtherEvents
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceiptForOPT') and type in ('P', 'PC'))
 begin
  drop procedure GetReceiptForOPT
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('PruneTransactions') and type in ('P', 'PC'))
 begin
  drop procedure PruneTransactions
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddTransaction') and type in ('P', 'PC'))
 begin
  drop procedure AddTransaction
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddToTransaction') and type in ('P', 'PC'))
 begin
  drop procedure AddToTransaction
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddEvent') and type in ('P', 'PC'))
 begin
  drop procedure AddEvent
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetFuelTransactions') and type in ('P', 'PC'))
 begin
  drop procedure GetFuelTransactions
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetOtherEvents') and type in ('P', 'PC'))
 begin
  drop procedure GetOtherEvents
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddDayEnd') and type in ('P', 'PC'))
 begin
  drop procedure AddDayEnd
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeDayEnd') and type in ('P', 'PC'))
 begin
  drop procedure TakeDayEnd
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeShiftSummary') and type in ('P', 'PC'))
 begin
  drop procedure TakeShiftSummary
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeItemSales') and type in ('P', 'PC'))
 begin
  drop procedure TakeItemSales
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeDayItemSales') and type in ('P', 'PC'))
 begin
  drop procedure TakeDayItemSales
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeCardSales') and type in ('P', 'PC'))
 begin
  drop procedure TakeCardSales
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeDayCardSales') and type in ('P', 'PC'))
 begin
  drop procedure TakeDayCardSales
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeCardVolumeSales') and type in ('P', 'PC'))
 begin
  drop procedure TakeCardVolumeSales
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetDayStart') and type in ('P', 'PC'))
 begin
  drop procedure GetDayStart
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetShiftStart') and type in ('P', 'PC'))
 begin
  drop procedure GetShiftStart
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('FetchCardReferences') and type in ('P', 'PC'))
 begin
  drop procedure FetchCardReferences
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetCardReference') and type in ('P', 'PC'))
 begin
  drop procedure SetCardReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearCardReference') and type in ('P', 'PC'))
 begin
  drop procedure ClearCardReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetCardReferenceFuelCard') and type in ('P', 'PC'))
 begin
  drop procedure SetCardReferenceFuelCard
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetAcquirerReference') and type in ('P', 'PC'))
 begin
  drop procedure SetAcquirerReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearAcquirerReference') and type in ('P', 'PC'))
 begin
  drop procedure ClearAcquirerReference
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPumpDelivered') and type in ('P', 'PC'))
 begin
  drop procedure GetPumpDelivered
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpDelivered') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpDelivered
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPumpDelivered') and type in ('P', 'PC'))
 begin
  drop procedure ClearPumpDelivered
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPumpOPTPayment') and type in ('P', 'PC'))
 begin
  drop procedure SetPumpOPTPayment
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPumpOPTPayment') and type in ('P', 'PC'))
 begin
  drop procedure ClearPumpOPTPayment
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetRetalixPrimary') and type in ('P', 'PC'))
 begin
  drop procedure GetRetalixPrimary
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetRetalixPrimary') and type in ('P', 'PC'))
 begin
  drop procedure SetRetalixPrimary
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearRetalixPrimary') and type in ('P', 'PC'))
 begin
  drop procedure ClearRetalixPrimary
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetFileLocations') and type in ('P', 'PC'))
 begin
  drop procedure GetFileLocations
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetRetalixTransactionFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetRetalixTransactionFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearRetalixTransactionFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure ClearRetalixTransactionFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetTransactionFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetTransactionFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetWhitelistDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetWhitelistDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLayoutDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetLayoutDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetSoftwareDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetSoftwareDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetContactlessPropertiesFile') and type in ('P', 'PC'))
 begin
  drop procedure SetContactlessPropertiesFile
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuelDataUpdateFile') and type in ('P', 'PC'))
 begin
  drop procedure SetFuelDataUpdateFile
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetUpgradeFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetUpgradeFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetRollbackFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetRollbackFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetMediaDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetMediaDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPlaylistDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetPlaylistDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetOPTLogFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetOPTLogFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLogFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetLogFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetTraceFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetTraceFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetJournalFileDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetJournalFileDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceivedUpdateDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetReceivedUpdateDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDatabaseBackupDirectory') and type in ('P', 'PC'))
 begin
  drop procedure SetDatabaseBackupDirectory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketConnectionString') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketConnectionString
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketUseConnectionString') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketUseConnectionString
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketConfigFile') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketConfigFile
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketKeystoreFile') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketKeystoreFile
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketDbUrl') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketDbUrl
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketOverrideProperties') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketOverrideProperties
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketOverrideKeystore') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketOverrideKeystore
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketOverrideUrl') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketOverrideUrl
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetESocketOverrideContactless') and type in ('P', 'PC'))
 begin
  drop procedure SetESocketOverrideContactless
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetReceiptLayoutMode') and type in ('P', 'PC'))
 begin
  drop procedure GetReceiptLayoutMode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetSiteInfo') and type in ('P', 'PC'))
 begin
  drop procedure GetSiteInfo
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceiptLayoutMode') and type in ('P', 'PC'))
 begin
  drop procedure SetReceiptLayoutMode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetSiteName') and type in ('P', 'PC'))
 begin
  drop procedure SetSiteName
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetVATNumber') and type in ('P', 'PC'))
 begin
  drop procedure SetVATNumber
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetNozzleUpForKioskUse') and type in ('P', 'PC'))
 begin
  drop procedure SetNozzleUpForKioskUse
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetUseReplaceNozzleScreen') and type in ('P', 'PC'))
 begin
  drop procedure SetUseReplaceNozzleScreen
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetCurrencyCode') and type in ('P', 'PC'))
 begin
  drop procedure SetCurrencyCode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetForwardFuelPriceUpdate') and type in ('P', 'PC'))
 begin
  drop procedure SetForwardFuelPriceUpdate
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetTillNumber') and type in ('P', 'PC'))
 begin
  drop procedure SetTillNumber
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFuelCategory') and type in ('P', 'PC'))
 begin
  drop procedure SetFuelCategory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetMaxFillOverride') and type in ('P', 'PC'))
 begin
  drop procedure SetMaxFillOverride
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPosClaim') and type in ('P', 'PC'))
 begin
  drop procedure GetPosClaim
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPosClaimNumber') and type in ('P', 'PC'))
 begin
  drop procedure SetPosClaimNumber
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPruneDays') and type in ('P', 'PC'))
 begin
  drop procedure GetPruneDays
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetFilePruneDays') and type in ('P', 'PC'))
 begin
  drop procedure SetFilePruneDays
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetTransactionPruneDays') and type in ('P', 'PC'))
 begin
  drop procedure SetTransactionPruneDays
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetReceiptPruneDays') and type in ('P', 'PC'))
 begin
  drop procedure SetReceiptPruneDays
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetNextDayEnd') and type in ('P', 'PC'))
 begin
  drop procedure GetNextDayEnd
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLogInterval') and type in ('P', 'PC'))
 begin
  drop procedure GetLogInterval
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetNextDayEnd') and type in ('P', 'PC'))
 begin
  drop procedure SetNextDayEnd
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetLogInterval') and type in ('P', 'PC'))
 begin
  drop procedure SetLogInterval
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetPrinterConfig') and type in ('P', 'PC'))
 begin
  drop procedure GetPrinterConfig
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterEnabled') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterEnabled
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterPortName') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterPortName
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterBaudRate') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterBaudRate
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterHandshake') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterHandshake
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterStopBits') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterStopBits
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetPrinterDataBits') and type in ('P', 'PC'))
 begin
  drop procedure SetPrinterDataBits
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('FetchPreviousMeterReadings') and type in ('P', 'PC'))
 begin
  drop procedure FetchPreviousMeterReadings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearPreviousMeterReadings') and type in ('P', 'PC'))
 begin
  drop procedure ClearPreviousMeterReadings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddPreviousMeterReading') and type in ('P', 'PC'))
 begin
  drop procedure AddPreviousMeterReading
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddShiftToList') and type in ('P', 'PC'))
 begin
  drop procedure AddShiftToList
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TakeShiftList') and type in ('P', 'PC'))
 begin
  drop procedure TakeShiftList
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetDOMSInfo') and type in ('P', 'PC'))
 begin
  drop procedure GetDOMSInfo
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDOMSIpAddress') and type in ('P', 'PC'))
 begin
  drop procedure SetDOMSIpAddress
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDOMSLoginString') and type in ('P', 'PC'))
 begin
  drop procedure SetDOMSLoginString
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDOMSEnabled') and type in ('P', 'PC'))
 begin
  drop procedure SetDOMSEnabled
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetDOMSDetect') and type in ('P', 'PC'))
 begin
  drop procedure SetDOMSDetect
 END
 
if exists (select * from sys.objects where object_id = OBJECT_ID('getReceiptHash') and type = 'FN')
 begin
  drop FUNCTION getReceiptHash
 end

go

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransactionRecord') AND TYPE IN ('P', 'PC'))
   BEGIN
    DROP PROCEDURE AddTransactionRecord
   END
GO

-- Create Functions
CREATE FUNCTION getReceiptHash(
	@CardNumber varchar(20), @ReceiptContent varchar(MAX), @OPT varchar(1024), @TransactionNumber bigint)
	RETURNS varbinary(64)
	AS
	BEGIN
    
		DECLARE @salt CHAR(36) ='99AAECE0-0206-43F9-B43E-308A953ADEEE';
		DECLARE @pepper CHAR(36) ='BA23AEFB-05E5-4D5B-B30B-BB731C449C05';

		RETURN HASHBYTES('SHA2_256', CONCAT(@salt, @CardNumber, @ReceiptContent, @OPT, @TransactionNumber, @pepper));

	END

GO

-- Create Stored Procedures
create procedure GetOPTEndPoints as
 begin
  select HydraId, IPAddress, FromOPTPort, ToOPTPort, HeartbeatPort, HydraPOSPort, RetalixPOSPort, ThirdPartyPOSPort, MediaChannelPort, AutoAuth, MediaChannel, UnmannedPseudoPOS, AsdaDayEndReport from OPTEndPoints
 end

go

create procedure SetOPTEndPoints
 @hydraId varchar(50),
 @ipAddress varchar(15),
 @fromOPTPort int,
 @toOPTPort int,
 @heartbeatPort int,
 @hydraPOSPort int,
 @retalixPOSPort int,
 @thirdPartyPOSPort int,
 @mediaChannelPort int as
 begin
  if exists (select * from OPTEndPoints where HydraId = @HydraId)
   begin
    update OPTEndPoints
     set IPAddress = @ipAddress,
         FromOPTPort = @fromOPTPort,
         ToOPTPort = @toOPTPort,
         HeartbeatPort = @heartbeatPort,
         HydraPOSPort = @hydraPOSPort,
         RetalixPOSPort = @retalixPOSPort,
         ThirdPartyPOSPort = @thirdPartyPOSPort,
         MediaChannelPort = @mediaChannelPort
      where HydraId = @HydraId
   end
  else
   begin
    insert OPTEndPoints
     (HydraId, IPAddress, FromOPTPort, ToOPTPort, HeartbeatPort, HydraPOSPort, RetalixPOSPort, ThirdPartyPOSPort, MediaChannelPort)
     values
      (@hydraId, @ipAddress, @fromOPTPort, @toOPTPort, @heartbeatPort, @hydraPOSPort, @retalixPOSPort, @thirdPartyPOSPort, @mediaChannelPort)
   end
 end

go

create procedure RemoveOPTEndPoints
 @hydraId varchar(50) as
 begin
  delete from OPTEndPoints where HydraId = @hydraId
 end

go

create procedure SetAutoAuth
 @hydraId varchar(50),
 @autoAuth bit as
 begin
  if exists (select * from OPTEndPoints where HydraId = @HydraId)
   begin
    update OPTEndPoints set AutoAuth = @autoAuth where HydraId = @HydraId
   end
  else
   begin
    insert OPTEndPoints (HydraId, AutoAuth) values (@hydraId, @autoAuth)
   end
 end

go

create procedure SetMediaChannel
 @hydraId varchar(50),
 @mediaChannel bit as
 begin
  if exists (select * from OPTEndPoints where HydraId = @HydraId)
   begin
    update OPTEndPoints set MediaChannel = @mediaChannel where HydraId = @HydraId
   end
  else
   begin
    insert OPTEndPoints (HydraId, MediaChannel) values (@hydraId, @mediaChannel)
   end
 end

go

create procedure SetUnmannedPseudoPOS
 @hydraId varchar(50),
 @unmanned bit as
 begin
  if exists (select * from OPTEndPoints where HydraId = @HydraId)
   begin
    update OPTEndPoints set UnmannedPseudoPOS = @unmanned where HydraId = @HydraId
   end
  else
   begin
    insert OPTEndPoints (HydraId, UnmannedPseudoPOS) values (@hydraId, @unmanned)
   end
 end

go

create procedure SetAsdaDayEndReport
 @hydraId varchar(50),
 @isAsda bit as
 begin
  if exists (select * from OPTEndPoints where HydraId = @HydraId)
   begin
    update OPTEndPoints set AsdaDayEndReport = @isAsda where HydraId = @HydraId
   end
  else
   begin
    insert OPTEndPoints (HydraId, AsdaDayEndReport) values (@hydraId, @isAsda)
   end
 end

go

create procedure GetDivertOPT as
 begin
  select IsDiverted, IPAddress, FromOPTPort, ToOPTPort, HeartbeatPort, MediaChannelPort from DivertOPT
 end

go

create procedure SetDivertOPT
 @ipAddress varchar(15),
 @fromOPTPort int,
 @toOPTPort int,
 @heartbeatPort int,
 @mediaChannelPort int as
 begin
  update DivertOPT set IPAddress = @ipAddress, FromOPTPort = @fromOPTPort, ToOPTPort = @toOPTPort, HeartbeatPort = @heartbeatPort, MediaChannelPort = @mediaChannelPort
 end

go

create procedure SetOPTDiverted
 @isDiverted bit as
 begin
  update DivertOPT set IsDiverted = @isDiverted
 end

go

create procedure GetOPTMode as
 begin
  select OPT, Contactless, ReceiptHeader, PlaylistFileName, LastLogTime from OPTMode
 end

go

create procedure SetReceiptHeader
 @opt varchar(1024),
 @receiptHeader varchar(max) as
 begin
  if(@opt is not null and ltrim(@opt) != '')
   begin
    begin try
     begin transaction
     if(exists (select * from OPTMode where OPT = @opt))
      begin
       update OPTMode set ReceiptHeader = @receiptHeader where OPT = @opt
      end
     else
      begin
       insert OPTMode (OPT, ReceiptHeader) values (@opt, @receiptHeader)
      end
     delete from OPTMode where
      Contactless = 0 and
      (ReceiptHeader is null or ltrim(ReceiptHeader) = '') and
      (PlaylistFileName is null or ltrim(PlaylistFileName) = '') and
      (LastLogTime is null or dateadd(day, 1, LastLogTime) < sysdatetime())
     commit transaction
    end try
    begin catch
     rollback transaction;
     throw
    end catch
   end
 end

go

create procedure SetPlaylistFileName
 @opt varchar(1024),
 @playlistFileName varchar(max) as
 begin
  if(@opt is not null and ltrim(@opt) != '')
   begin
    begin try
     begin transaction
     if(exists (select * from OPTMode where OPT = @opt))
      begin
       update OPTMode set PlaylistFileName = @playlistFileName where OPT = @opt
      end
     else
      begin
       insert OPTMode (OPT, PlaylistFileName) values (@opt, @playlistFileName)
      end
     delete from OPTMode where
      Contactless = 0 and
      (ReceiptHeader is null or ltrim(ReceiptHeader) = '') and
      (PlaylistFileName is null or ltrim(PlaylistFileName) = '') and
      (LastLogTime is null or dateadd(day, 1, LastLogTime) < sysdatetime())
     commit transaction
    end try
    begin catch
     rollback transaction;
     throw
    end catch
   end
 end

go

create procedure SetLastLogTime
 @opt varchar(1024),
 @logTime DateTime2 as
 begin
  if(@opt is not null and ltrim(@opt) != '')
   begin
    begin try
     begin transaction
     if(exists (select * from OPTMode where OPT = @opt))
      begin
       update OPTMode set LastLogTime = @logTime where OPT = @opt
      end
     else
      begin
       insert OPTMode (OPT, LastLogTime) values (@opt, @logTime)
      end
     delete from OPTMode where
      Contactless = 0 and
      (ReceiptHeader is null or ltrim(ReceiptHeader) = '') and
      (PlaylistFileName is null or ltrim(PlaylistFileName) = '') and
      (LastLogTime is null or dateadd(day, 1, LastLogTime) < sysdatetime())
     commit transaction
    end try
    begin catch
     rollback transaction;
     throw
    end catch
   end
 end

go

create procedure ClearOPTMode as
 begin
    delete from OPTMode
 end

go

create procedure GetPumpEndPoint as
 begin
  select IPAddress, Port from PumpEndPoint
 end

go

create procedure SetPumpEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  update PumpEndPoint set IPAddress = @ipAddress, Port = @port
 end

go

create procedure GetANPREndPoint as
 begin
  select IPAddress, Port from ANPREndPoint
 end

go

create procedure SetANPREndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  update ANPREndPoint set IPAddress = @ipAddress, Port = @port
 end

go

create procedure GetCarWashEndPoint as
 begin
  select IPAddress, Port from CarWashEndPoint
 end

go

create procedure SetCarWashEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  update CarWashEndPoint set IPAddress = @ipAddress, Port = @port
 end

go

create procedure GetTankGaugeEndPoint as
 begin
  select IPAddress, Port from TankGaugeEndPoint
 end

go

create procedure SetTankGaugeEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  update TankGaugeEndPoint set IPAddress = @ipAddress, Port = @port
 end

go

create procedure GetHydraMobileEndPoint as
 begin
  select IPAddress, Port from HydraMobileEndPoint
 end

go

create procedure SetHydraMobileEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  update HydraMobileEndPoint set IPAddress = @ipAddress, Port = @port
 end

go

create procedure GetPumpTIDs as
 begin
  select Number, TID, OPT, DefaultKioskOnly, DefaultMixed, CurrentkioskOnly, CurrentMixed,
         Closed, MaxFillOverrideForFuelCards, MaxFillOverrideForPaymentCards from PumpTIDs
 end

go

create procedure AddPumpTID
 @number int,
 @tid varchar(max) = null as
 begin
  begin try
   begin transaction
   update PumpTIDs set TID = null where TID = @tid
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
     update PumpTIDs set CurrentMixed = 0 where Number = @number
     update PumpTIDs set DefaultMixed = 0 where Number = @number
    end 
   update PumpTIDs set TID = @tid where Number = @number
   delete from PumpTIDs where Tid is null and OPT is null
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure AddPumpOPT
 @number int,
 @opt varchar(max) = null as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
     update PumpTIDs set CurrentMixed = 0 where Number = @number
     update PumpTIDs set DefaultMixed = 0 where Number = @number
    end 
   update PumpTIDs set OPT = @opt where Number = @number
   delete from PumpTIDs where Tid is null and OPT is null
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpKioskOnly
 @number int,
 @setDefault bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   if(@setDefault = 1)
    begin
     update PumpTIDs set DefaultMixed = 0, DefaultKioskOnly = 1 where Number = @number
    end
   else
    begin
     update PumpTIDs set CurrentMixed = 0, CurrentKioskOnly = 1 where Number = @number
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpOutsideOnly
 @number int,
 @setDefault bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   if(@setDefault = 1)
    begin
     update PumpTIDs set DefaultMixed = 0, DefaultKioskOnly = 0 where Number = @number
    end
   else
    begin
     update PumpTIDs set CurrentMixed = 0, CurrentKioskOnly = 0 where Number = @number
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpMixed
 @number int,
 @setDefault bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   if(@setDefault = 1)
    begin
     update PumpTIDs set DefaultMixed = 1, DefaultKioskOnly = 0 where Number = @number
    end
   else
    begin
     update PumpTIDs set CurrentMixed = 1, CurrentKioskOnly = 0 where Number = @number
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpClosed
 @number int,
 @closed bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   update PumpTIDs set Closed = @closed where Number = @number
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpMaxFillOverrideForFuelCards
 @number int,
 @flag bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   update PumpTIDs set MaxFillOverrideForFuelCards = @flag where Number = @number
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpMaxFillOverrideForPaymentCards
 @number int,
 @flag bit as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpTIDs where Number = @number))
    begin
     insert PumpTIDs (Number) values (@number)
    end 
   update PumpTIDs set MaxFillOverrideForPaymentCards = @flag where Number = @number
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure ClearPumpTIDs as
 begin
  delete from PumpTIDs
 end

go

create procedure GetGradeNames as
 begin
  select Grade, GradeName, VatRate from GradeName
 end

go

create procedure SetGradeName
 @grade int,
 @gradeName varchar(20),
 @vatRate float as
 begin
  if(@grade is not null and @grade >= 0)
   begin
    begin try
     begin transaction
     if(@gradeName is null or @gradeName = '')
      begin
       delete from GradeName where Grade = @grade
      end
     else if(exists (select * from GradeName where Grade = @grade))
      begin
       update GradeName set GradeName = @gradeName, VatRate = @vatRate where Grade = @grade
      end
     else
      begin
       insert GradeName (Grade, GradeName, VatRate) values (@grade, @gradeName, @vatRate)
      end
     commit transaction
    end try
    begin catch
     rollback transaction;
     throw
    end catch
   end
 end

go

create procedure ClearGradeNames as
 begin
  delete from GradeName
 end

go

create procedure GetESocketEndPoints as
 begin
  select IPAddress, Port from ESocketEndPoints
 end

go

create procedure AddESocketEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  if(not exists (select * from ESocketEndPoints where IPAddress = @ipAddress and Port = @port))
   begin
    insert ESocketEndPoints (IPAddress, Port) values (@ipAddress, @port)
   end
 end

go

create procedure RemoveESocketEndPoint
 @ipAddress varchar(15),
 @port int as
 begin
  if(exists (select * from ESocketEndPoints where IPAddress = @ipAddress and Port = @port))
   begin
    delete from ESocketEndPoints where IPAddress = @ipAddress and Port = @port
   end
 end

go

create procedure ClearESocketEndPoints as
 begin
  delete from ESocketEndPoints
 end

go

create procedure GetTariffMappings as
 begin
  select Grade, ProductCode, FuelCardsOnly from TariffMappings
 end

go

create procedure AddTariffMapping
 @grade int,
 @productCode varchar(20) as
 begin
  if(not exists (select * from TariffMappings where ProductCode = @productCode and Grade = @grade))
   begin
    insert TariffMappings (Grade, ProductCode) values (@grade, @productCode)
   end
 end

go

create procedure ClearTariffMappings as
 begin
  delete from TariffMappings
 end

go

create procedure SetFuelCardsOnly
 @grade int,
 @flag bit as
 begin
  update TariffMappings set FuelCardsOnly = @flag where Grade = @grade
 end

go

create procedure GetPredefinedAmounts as
 begin
  select Amount from PredefinedAmounts
 end

go

create procedure AddPredefinedAmount
 @amount int as
 begin
  if(not exists (select * from PredefinedAmounts where Amount = @amount))
   begin
    insert PredefinedAmounts (Amount) values (@amount)
   end
 end

go

create procedure ClearPredefinedAmounts as
 begin
  delete from PredefinedAmounts
 end

go

create procedure GetDiscountCards as
 begin
  select Iin, Name, Type, Value, Grade from DiscountCards
 end

go

create procedure AddDiscountCard
 @iin varchar(20),
 @name varchar(80),
 @type varchar(20),
 @value float,
 @grade int as
 begin
  if(@iin is not null and @type is not null and @value is not null and @grade is not null)
   begin
    if(not exists (select * from DiscountCards where Iin = @iin))
     begin
      insert DiscountCards (Iin, Name, Type, Value, Grade) values (@iin, @name, @type, @value, @grade)
     end
    else
     begin
      update DiscountCards set Name = @name, Type = @type, Value = @value, Grade = @grade where Iin = @iin
     end
   end
 end

go

create procedure RemoveDiscountCard
 @iin varchar(20) as
 begin
  begin try
   begin transaction
    delete from DiscountWhitelist where Iin = @iin
    delete from DiscountCards where Iin = @iin
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetDiscountWhitelist
 @iin varchar(20) as
 begin
  select Pan from DiscountWhitelist where Iin = @iin
 end

go

create procedure AddDiscountWhitelist
 @iin varchar(20),
 @pan varchar(20) as
 begin
  if(exists (select * from DiscountCards where Iin = @iin) and @pan is not null and
     not exists (select * from DiscountWhitelist where Iin = @iin and Pan = @pan))
   begin
    insert DiscountWhitelist (Iin, Pan) values (@iin, @pan)
   end
 end

go

create procedure RemoveDiscountWhitelist
 @iin varchar(20),
 @pan varchar(20) as
 begin
  delete from DiscountWhitelist where Iin = @iin and Pan = @pan
 end

go

create procedure GetLocalAccountCustomers as
 begin
  select Reference, Name, TransactionsAllowed, TransLimit, Pin, PrintValue, AllowLoyalty, FuelOnly, RegistrationEntry, MileageEntry, PrePayAccount, LowCreditWarning, MaxCreditReached, Balance, CustomerExists from LocalAccountCustomers
 end

go

create procedure GetLocalAccountCards
 @reference varchar(80) as
 begin
  select Pan, Description, Discount, NoRestrictions, Unleaded, Diesel, LPG, LRP, GasOil, AdBlue, Kerosene, Oil, Avgas, Jet, Mogas, Valeting, OtherMotorRelatedGoods, ShopGoods, Hot from LocalAccountCards where CustomerReference = @reference
 end

go

create procedure AddLocalAccountCustomer
 @reference varchar(80),
 @name varchar(80),
 @transactionsAllowed bit,
 @transLimit bigint,
 @pin bit,
 @printValue bit,
 @allowLoyalty bit,
 @fuelOnly bit,
 @registrationEntry bit,
 @mileageEntry bit,
 @prepayAccount bit,
 @lowCreditWarning bit,
 @maxCreditReached bit,
 @balance bigint,
 @customerExists bit as
 begin
  if(exists(select * from LocalAccountCustomers where Reference = @reference))
   begin
    update LocalAccountCustomers
     set Name = @name, TransactionsAllowed = @transactionsAllowed, TransLimit = @transLimit,
         Pin = @pin, PrintValue = @printValue, AllowLoyalty = @allowLoyalty,
         FuelOnly = @fuelOnly, RegistrationEntry = @registrationEntry, MileageEntry = @mileageEntry,
         PrePayAccount = @prepayAccount, LowCreditWarning = @lowCreditWarning,
         MaxCreditReached = @maxCreditReached, Balance = @balance, CustomerExists = @customerExists
      where Reference = @reference
   end
  else
   begin
    insert into LocalAccountCustomers
     (Reference, Name, TransactionsAllowed, TransLimit, Pin, PrintValue, AllowLoyalty,
      FuelOnly, RegistrationEntry, MileageEntry, PrePayAccount, LowCreditWarning,
      MaxCreditReached, Balance, CustomerExists)
    values
     (@reference, @name, @transactionsAllowed, @transLimit, @pin, @printValue,
      @allowLoyalty, @fuelOnly, @registrationEntry, @mileageEntry,
      @prepayAccount, @lowCreditWarning, @maxCreditReached, @balance, @customerExists)
   end
 end

go

create procedure DeleteLocalAccountCustomer
 @reference varchar(80) as
 begin
  if(exists(select * from LocalAccountCards where CustomerReference = @reference))
   begin
    update LocalAccountCustomers set CustomerExists = 0 where Reference = @reference
   end
  else
   begin
    delete LocalAccountCustomers where Reference = @reference
   end
 end

go

create procedure AddLocalAccountCard
 @pan varchar(20),
 @customerReference varchar(80),
 @description varchar(80),
 @discount float,
 @noRestrictions bit,
 @unleaded bit,
 @diesel bit,
 @lpg bit,
 @lrp bit,
 @gasOil bit,
 @adBlue bit,
 @kerosene bit,
 @oil bit,
 @avgas bit,
 @jet bit,
 @mogas bit,
 @valeting bit,
 @otherMotorRelatedGoods bit,
 @shopGoods bit,
 @hot bit as
 begin
  begin try
   begin transaction
    if(not exists(select * from LocalAccountCustomers where Reference = @customerReference))
     begin
      insert into LocalAccountCustomers
       (Reference, Name, TransactionsAllowed, TransLimit, Pin, PrintValue, AllowLoyalty,
        FuelOnly, RegistrationEntry, MileageEntry, PrePayAccount, LowCreditWarning,
        MaxCreditReached, Balance, CustomerExists)
      values
       (@customerReference, '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
     end
    if(exists(select * from LocalAccountCards where Pan = @pan))
     begin
      update LocalAccountCards
       set CustomerReference = @customerReference, Description = @description,
           Discount = @discount, NoRestrictions = @noRestrictions, Unleaded = @unleaded,
           Diesel = @diesel, LPG = @lpg, LRP = @lrp, GasOil = @gasOil, AdBlue = @adBlue,
           Kerosene = @kerosene, Oil = @oil, Avgas = @avgas, Jet = @jet, Mogas = @mogas,
           Valeting = @valeting, OtherMotorRelatedGoods = @otherMotorRelatedGoods,
           ShopGoods = @shopGoods, Hot = @hot
       where Pan = @pan
     end
    else
     begin
      insert into LocalAccountCards
       (Pan, CustomerReference, Description, Discount, NoRestrictions, Unleaded, Diesel,
        LPG, LRP, GasOil, AdBlue, Kerosene, Oil, Avgas, Jet, Mogas, Valeting,
        OtherMotorRelatedGoods, ShopGoods, Hot)
      values
       (@pan, @customerReference, @description, @discount, @noRestrictions, @unleaded,
        @diesel, @lpg, @lrp, @gasOil, @adBlue, @kerosene, @oil, @avgas, @jet,
        @mogas, @valeting, @otherMotorRelatedGoods, @shopGoods, @hot)
     end
    delete from LocalAccountCustomers where CustomerExists = 0 and not exists(select * from LocalAccountCards where CustomerReference = Reference)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure DeleteLocalAccountCard
 @pan varchar(20) as
 begin
  begin try
   begin transaction
    delete from LocalAccountCards where Pan = @pan
    delete from LocalAccountCustomers where CustomerExists = 0 and not exists(select * from LocalAccountCards where CustomerReference = Reference)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure AddLoyaltyReference
 @name varchar(max) as
 begin
  begin try
   begin transaction
    declare @loyaltyRef int
    if not exists (select * from LoyaltyReference where LoyaltyName = @name)
     begin
      if exists (select * from LoyaltyReference)
       begin
        set @loyaltyRef = (select max(LoyaltyRef) from LoyaltyReference) + 1
       end
      else
       begin
        set @loyaltyRef = 1
       end
      insert into LoyaltyReference (LoyaltyRef, LoyaltyName, LoyaltyPresent) values (@loyaltyRef, @name, 1)
      if not exists (select * from LoyaltyTerminal where LoyaltyRef = @loyaltyRef)
       begin
        insert into LoyaltyTerminal (LoyaltyRef) values (@loyaltyRef)
       end
     end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure DeleteLoyaltyReference
 @name varchar(max) as
 begin
  begin try
   begin transaction
    delete from T
     from LoyaltyTerminal T
      where exists (select * from LoyaltyReference R where R.LoyaltyRef = T.LoyaltyRef and R.LoyaltyName = @name) 
    delete from H
     from LoyaltyHosts H
      where exists (select * from LoyaltyReference R where R.LoyaltyRef = H.LoyaltyRef and R.LoyaltyName = @name) 
    delete from H
     from LoyaltyHostnames H
      where exists (select * from LoyaltyReference R where R.LoyaltyRef = H.LoyaltyRef and R.LoyaltyName = @name) 
    delete from I from LoyaltyIins I where exists (select * from LoyaltyReference R where R.LoyaltyRef = I.LoyaltyRef and R.LoyaltyName = @name) 
    delete from M
     from LoyaltyMappings M
      where exists (select * from LoyaltyReference R where R.LoyaltyRef = M.LoyaltyRef and R.LoyaltyName = @name) 
    delete from LoyaltyReference where LoyaltyName = @name
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetLoyaltyPresent
 @name varchar(max),
 @present bit as
 begin
  update LoyaltyReference set LoyaltyPresent = @present where LoyaltyName = @name
 end

go

create procedure GetLoyaltyPresent
 @name varchar(max) as
 begin
  select LoyaltyPresent from LoyaltyReference where LoyaltyName = @name
 end

go

create procedure GetLoyaltyTerminal
 @name varchar(max) as
 begin
  select SiteID, TerminalID, Footer1, Footer2, Timeout, ApiKey, HttpHeader
   from LoyaltyTerminal T join LoyaltyReference R
    on T.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end

go

create procedure GetLoyaltyHosts
 @name varchar(max) as
 begin
  select IPAddress, Port
   from LoyaltyHosts H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end

go

create procedure GetLoyaltyHostnames
 @name varchar(max) as
 begin
  select Hostname
   from LoyaltyHostnames H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end

go

create procedure GetLoyaltyIINs
 @name varchar(max) as
 begin
  select Low, High
   from LoyaltyIINs I join LoyaltyReference R
    on I.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end

go

create procedure GetLoyaltyMappings
 @name varchar(max) as
 begin
  select ProductCode, LoyaltyCode
   from LoyaltyMappings M join LoyaltyReference R
    on M.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name and R.LoyaltyPresent = 1
 end

go

create procedure SetLoyaltyTerminal
 @name varchar(max),
 @siteID varchar(20),
 @terminalID varchar(20),
 @footer1 varchar(80),
 @footer2 varchar(80),
 @timeout int,
 @apiKey varchar(80),
 @httpHeader varchar(80) as
 begin
  update T
   set SiteID = @siteID, TerminalID = @terminalID, Footer1 = @footer1, Footer2 = @footer2,
       Timeout = @timeout, ApiKey = @apiKey, HttpHeader = @httpHeader
    from LoyaltyTerminal T join LoyaltyReference R
     on R.LoyaltyRef = T.LoyaltyRef and R.LoyaltyName = @name
 end

go

create procedure AddLoyaltyHost
 @name varchar(max),
 @ipAddress varchar(15),
 @port int as
 begin
  if(not exists
     (select * from LoyaltyHosts H join LoyaltyReference R
       on R.LoyaltyRef = H.LoyaltyRef and R.LoyaltyName = @name and IPAddress = @ipAddress and Port = @port))
   begin
    insert LoyaltyHosts (LoyaltyRef, IPAddress, Port)
     values ((select min(LoyaltyRef) from LoyaltyReference where LoyaltyName = @name), @ipAddress, @port)
   end
 end

go

create procedure AddLoyaltyHostname
 @name varchar(max),
 @hostname varchar(255) as
 begin
  if(not exists
     (select * from LoyaltyHostnames H join LoyaltyReference R
       on R.LoyaltyRef = H.LoyaltyRef and R.LoyaltyName = @name and Hostname = @hostname))
   begin
    insert LoyaltyHostnames (LoyaltyRef, Hostname)
     values ((select min(LoyaltyRef) from LoyaltyReference where LoyaltyName = @name), @hostname)
   end
 end

go

create procedure AddLoyaltyIIN
 @name varchar(max),
 @low varchar(20),
 @high varchar(20) as
 begin
  if(not exists
     (select * from LoyaltyIINs I join LoyaltyReference R
       on R.LoyaltyRef = I.LoyaltyRef and R.LoyaltyName = @name and Low = @low and High = @high))
   begin
    insert LoyaltyIINs (LoyaltyRef, Low, High)
     values ((select min(LoyaltyRef) from LoyaltyReference where LoyaltyName = @name), @low, @high)
   end
 end

go

create procedure AddLoyaltyMapping
 @name varchar(max),
 @productCode varchar(20),
 @loyaltyCode varchar(20) as
 begin
  if(not exists
     (select * from LoyaltyMappings M join LoyaltyReference R
       on R.LoyaltyRef = M.LoyaltyRef and R.LoyaltyName = @name and
          ProductCode = @productCode and LoyaltyCode = @loyaltyCode))
   begin
    insert LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode)
     values ((select min(LoyaltyRef) from LoyaltyReference where LoyaltyName = @name), @productCode, @loyaltyCode)
   end
 end

go

create procedure ClearLoyalty
 @name varchar(max) as
 begin
    update T
     set SiteID = '', TerminalID = '', Footer1 = '', Footer2 = '', Timeout = 0, ApiKey = '', HttpHeader = ''
      from LoyaltyTerminal T join LoyaltyReference R on T.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
    delete from H
     from LoyaltyHosts H join LoyaltyReference R
      on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
    delete from H
     from LoyaltyHostnames H join LoyaltyReference R
      on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
    delete from I
     from LoyaltyIINs I join LoyaltyReference R
      on I.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
    delete from M
     from LoyaltyMappings M join LoyaltyReference R
      on M.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
 end

go

create procedure GetWashes as
 begin
  select ProgramId, ProductCode, Description, Price, VatRate, Category, Subcategory from Washes
 end

go

create procedure AddWash
 @programId int,
 @productCode varchar(20),
 @description varchar(80),
 @price varchar(20),
 @vatRate varchar(20),
 @category int,
 @subcategory int as
 begin
  if(exists(select * from Washes where ProgramId = @programId))
   begin
    update Washes
     set ProductCode = @productCode,
         Description = @description,
         Price = @price,
         VatRate = @vatRate,
         Category = @category,
         Subcategory = @subcategory
     where ProgramId = @programId
   end
  else
   begin
    insert Washes (ProgramId, ProductCode, Description, Price, VatRate, Category, Subcategory)
     values (@programId, @productCode, @description, @price, @vatRate, @category, @subcategory)
   end
 end

go

create procedure RemoveWash
 @programId int as
 begin
  delete from Washes where ProgramId = @programId
 end

go

create procedure GetPaymentTimeout as
 begin
  select Mode, Timeout from PaymentTimeout
 end

go

create procedure SetPaymentTimeout
 @mode int,
 @timeout int as
 begin
  if(@mode is not null and @mode >= 0)
   begin
    begin try
     begin transaction
     if(exists (select * from PaymentTimeout where Mode = @mode))
      begin
       update PaymentTimeout set Timeout = @timeout where Mode = @mode
      end
     else
      begin
       insert PaymentTimeout (Mode, Timeout) values (@mode, @timeout)
      end
     delete from PaymentTimeout where Timeout = 0
     commit transaction
    end try
    begin catch
     rollback transaction;
     throw
    end catch
   end
 end

go

create procedure ClearPaymentTimeout as
 begin
  delete from PaymentTimeout
 end

go

create procedure AddReceipt
 @cardNumber varchar(20),
 @receiptContent varchar(max),
 @expiry DateTime2,
 @opt varchar(1024),
 @transactionNumber bigint as
 begin
  begin TRY
  
  DECLARE @receiptDataHash VARBINARY(64) = dbo.getReceiptHash(@cardNumber,  @ReceiptContent, @OPT, @TransactionNumber);

   begin transaction
   delete from Receipts where CardNumber = @cardNumber and TransactionNumber = @transactionNumber
   if (@receiptContent is not null and @expiry is not null)
    begin
     insert into Receipts (CardNumber, ReceiptContent, Expiry, OPT, TransactionNumber, ReceiptHash)
      values (@cardNumber, @receiptContent, @expiry, @opt, @transactionNumber, @receiptDataHash)
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetFuelling as
 begin
  select IndefiniteWait, WaitMinutes, BackoffAuth, BackoffPreAuth, BackoffStopStart, BackoffStopOnly from Fuelling
 end

go

create procedure SetFuellingIndefiniteWait
 @flag bit as
 begin
  update Fuelling set IndefiniteWait = @flag
 end

go

create procedure SetFuellingWaitMinutes
 @minutes int as
 begin
  update Fuelling set WaitMinutes = @minutes
 end

go

create procedure SetFuellingBackoffAuth
 @backoff int as
 begin
  update Fuelling set BackoffAuth = @backoff
 end

go

create procedure SetFuellingBackoffPreAuth
 @backoff int as
 begin
  update Fuelling set BackoffPreAuth = @backoff
 end

go

create procedure SetFuellingBackoffStopStart
 @backoff int as
 begin
  update Fuelling set BackoffStopStart = @backoff
 end

go

create procedure SetFuellingBackoffStopOnly
 @backoff int as
 begin
  update Fuelling set BackoffStopOnly = @backoff
 end

go

create procedure SetReceiptTimeout
 @timeout int as
 begin
  update ReceiptExpiry set Timeout = @timeout
 end

go

create procedure SetReceiptMaxCount
 @maxCount int as
 begin
  update ReceiptExpiry set MaxCount = @maxCount
 end

go

create procedure GetReceiptTimeout as
 begin
  select max(Timeout) from ReceiptExpiry
 end

go

create procedure GetReceiptMaxCount as
 begin
  select max(MaxCount) from ReceiptExpiry
 end

go

create procedure GetReceipts as
 begin
  select CardNumber, 
  CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber) = ReceiptHash) THEN ReceiptContent
  ELSE cast('Error with reciept data hash.' AS VARCHAR(MAX)) END AS ReceiptContent, Expiry, OPT, TransactionNumber from Receipts;
 END

GO

CREATE PROCEDURE GetReceipt(
 @cardNumber varchar(20),
 @TransactionNumber BIGINT = NULL,
 @includeExpired BIT = 0
) AS
BEGIN
	select CardNumber,
	CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber) = ReceiptHash) THEN ReceiptContent
	ELSE cast('Error with reciept data hash.' AS VARCHAR(MAX)) END AS ReceiptContent,
	Expiry, OPT, TransactionNumber from Receipts
	WHERE CardNumber = @cardNumber
	AND (@TransactionNumber = null OR TransactionNumber = @TransactionNumber)
	AND (@includeExpired = 1 OR Expiry > GETDATE());
END 

GO

CREATE PROCEDURE GetReceiptForOPT(
 @OPT varchar(1024)
) AS
BEGIN
	select CardNumber,
	CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber) = ReceiptHash) THEN ReceiptContent
	ELSE cast('Error with reciept data hash.' AS VARCHAR(MAX)) END AS ReceiptContent,
	Expiry, OPT, TransactionNumber from Receipts
	WHERE opt = @opt;
END 

GO

create procedure PruneReceipts
 @now DateTime2,
 @count int as
 begin
  declare @opts table (OPT varchar(1024))
  declare @rows table (id int)
  insert into @opts select  distinct OPT from PumpTIDs
  declare OPTCursor cursor local read_only for select OPT from @opts
  open OPTCursor
  declare @opt varchar(1024)
  fetch next from OPTCursor into @opt
  while @@FETCH_STATUS = 0
   begin
    insert into @rows select top (@count) rowid from Receipts where OPT = @opt order by rowid desc
    fetch next from OPTCursor into @opt
   end
  close OPTCursor
  deallocate OPTCursor
  insert into @rows select rowid from Receipts where not exists (select * from @rows where id = rowid) and Expiry > @now
  delete from Receipts where not exists (select * from @rows where id = rowid)
 end

go

create procedure PruneTransactions
 @checkDate DateTime2 as
 begin
  delete from Transactions where TransactionTime < @checkDate
 end

go

PRINT N'HOPT-1071 - Retalix - Unable to print duplicate receipt from OPT on POS...Begin';
PRINT N'Creating Procedure [dbo].[AddTransactionRecord]...';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransactionRecord') AND TYPE IN ('P', 'PC'))
   BEGIN
    DROP PROCEDURE AddTransactionRecord
   END
GO

CREATE procedure [dbo].[AddTransactionRecord]
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20),
 @discountName varchar(20),
 @discountCode varchar(20),
 @discountValue bigint,
 @discountCardNumber varchar(20),
 @localAccountMileage bigint,
 @localAccountRegistration varchar(20),
 @txnNumber varchar(20),
 @maxTransactionNumber bigint,
 @hasValue bit,
 @transactionNumber int out as
 begin
  begin try
   begin transaction
   declare @temp table (id bigint)
   if(@hasValue = 0)
   begin
	   insert into Transactions (TransactionTime)
	   output INSERTED.rowid into @temp values (@transactionTime)
	   set @transactionNumber = (select top 1 id from @temp)
   end
   else
	begin   
	   if(nullif(@pumpDetails,'') is not null)
		begin
		 insert into Transactions
		  (GradeName, FuelCode, FuelQuantity, Amount, PumpDetails, CardNumber,
		   TransactionTime, FuelCategory, FuelSubcategory, DiscountName, DiscountCode,
		   DiscountValue, DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration,
		   TxnNumber)
		  output INSERTED.rowid into @temp values
		   (@gradeName, @code, @quantity, @amount, @pumpDetails, @cardNumber,
			@transactionTime, @category, @subcategory, @discountName, @discountCode,
			@discountValue, @discountCardNumber, @localAccountMileage, @localAccountRegistration,
			@txnNumber)
		end
	   else
		begin
		 insert into Transactions
		  (WashName, WashCode, WashQuantity, Amount, CardNumber, TransactionTime,
		   WashCategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
		   DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration,
		   TxnNumber)
		  output INSERTED.rowid into @temp values
		   (@gradeName, @code, @quantity, @amount, @cardNumber, @transactionTime,
			@category, @subcategory, @discountName, @discountCode, @discountValue,
			@discountCardNumber, @localAccountMileage, @localAccountRegistration,
			@txnNumber)
		end
	end
   set @transactionNumber = (select top 1 id from @temp)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end
 
 GO
 
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransaction') AND TYPE IN ('P', 'PC'))
   BEGIN
    DROP PROCEDURE AddTransaction
   END
GO
PRINT N'Creating Procedure [dbo].[AddTransaction]...';
GO

CREATE procedure [dbo].[AddTransaction]
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20),
 @discountName varchar(20),
 @discountCode varchar(20),
 @discountValue bigint,
 @discountCardNumber varchar(20),
 @localAccountMileage bigint,
 @localAccountRegistration varchar(20),
 @txnNumber varchar(20),
 @maxTransactionNumber bigint,
 @hasValue bit,
 @transactionNumber int out as
 begin
	DECLARE @rawTransactionNumber BIGINT

	EXEC AddTransactionRecord 
	@gradeName, 
	@code, 
	@quantity, 
	@amount, 
	@pumpDetails, 
	@cardNumber, 
	@transactionTime, 
	@category, 
	@subcategory, 
	@discountName ,
	@discountCode,
	@discountValue,
	@discountCardNumber,
	@localAccountMileage,
	@localAccountRegistration,
	@txnNumber,
	@maxTransactionNumber,
	@hasValue,
	@rawTransactionNumber out
	
	 
	-- Check for transaction number recycling
	SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	IF(@transactionNumber = 0)
	BEGIN
		-- Remove 0 txn number row
		DELETE FROM [Transactions]
		WHERE rowid = @rawTransactionNumber
		
		-- Re-add so txn number != 0
		EXEC AddTransactionRecord 
			@gradeName, 
			@code, 
			@quantity, 
			@amount, 
			@pumpDetails, 
			@cardNumber, 
			@transactionTime, 
			@category, 
			@subcategory, 
			@discountName ,
			@discountCode,
			@discountValue,
			@discountCardNumber,
			@localAccountMileage,
			@localAccountRegistration,
			@txnNumber,
			@maxTransactionNumber,
			@hasValue,
			@rawTransactionNumber out

			SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	END
 END

 GO
 
PRINT N'HOPT-1071 - Retalix - Unable to print duplicate receipt from OPT on POS......End';
PRINT N'';
GO

create procedure AddToTransaction
 @transactionNumber int,
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20) as
 begin
  begin try
   begin transaction
   if(nullif(@pumpDetails,'') is not null)
    begin
     if(nullif((select GradeName from Transactions where rowid = @transactionNumber),'') is null)
      begin
       update Transactions set GradeName = @gradeName, FuelCode = @code, PumpDetails = @pumpDetails, FuelCategory = @category, FuelSubcategory = @subcategory where rowid = @transactionNumber
      end
     update Transactions set FuelQuantity = FuelQuantity + @quantity where rowid = @transactionNumber
    end
   else
    begin
     if(nullif((select WashName from Transactions where rowid = @transactionNumber),'') is null)
      begin
       update Transactions set WashName = @gradeName, WashCode = @code, WashCategory = @category, WashSubcategory = @subcategory where rowid = @transactionNumber
      end
     update Transactions set WashQuantity = WashQuantity + @quantity where rowid = @transactionNumber
    end
   update Transactions set Amount = Amount + @amount where rowid = @transactionNumber
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure AddEvent
 @transactionTime DateTime2,
 @transactionNumber int out as
 begin
  begin try
   begin transaction
   declare @temp table (id bigint)
   insert into Transactions (TransactionTime) output INSERTED.rowid into @temp values (@transactionTime)
   set @transactionNumber = (select top 1 id % 1000000 from @temp)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetFuelTransactions
 @startTime DateTime2,
 @endTime DateTime2 as
 begin
  select rowid as TransactionId, TransactionTime, FuelCode as GradeCode, WashCode, GradeName,
         WashName, PumpDetails, CardNumber, FuelQuantity, WashQuantity, Amount, FuelCategory,
         WashCategory, FuelSubcategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
         DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration, TxnNumber from Transactions
   where (@startTime is null or TransactionTime >= @startTime) and
         (@endTime is null or TransactionTime <= @endTime) and
         (FuelCode is not null or WashCode is not null)
 end

go

create procedure GetOtherEvents
 @startTime DateTime2,
 @endTime DateTime2 as
 begin
  select rowid as TransactionId, TransactionTime from Transactions
   where (@startTime is null or TransactionTime >= @startTime) and
         (@endTime is null or TransactionTime <= @endTime) and
         FuelCode is null and WashCode is null
 end

go

create procedure AddDayEnd
 @fuelCash bigint,
 @dryCash bigint,
 @quantity bigint,
 @transaction int,
 @category int,
 @subcategory int,
 @gradeCode varchar(13),
 @gradeName varchar(20),
 @cardProductName varchar(max),
 @discount bigint as
 begin
  begin try
   begin transaction
   declare @cardRef int
   if exists (select * from CardReference where CardProductName = @cardProductName)
    begin
     set @cardRef = (select min(CardRef) from CardReference where CardProductName = @cardProductName)
    end
   else
    begin
     if exists (select * from CardReference)
      begin
       set @cardRef = (select max(CardRef) from CardReference) + 1
      end
     else
      begin
       set @cardRef = 1
      end
     insert into CardReference (CardRef, CardProductName) values (@cardRef, @cardProductName)
    end
   if not exists (select * from CardSales where CardRef = @cardRef)
    begin
     insert into CardSales (CardRef) values (@cardRef)
    end
   if not exists (select * from DayCardSales where CardRef = @cardRef)
    begin
     insert into DayCardSales (CardRef) values (@cardRef)
    end
   if not exists (select * from ItemSales where Category = @category and Subcategory = @subcategory)
    begin
     insert into ItemSales (Category, Subcategory, GradeCode, GradeName) values (@category, @subcategory, @gradeCode, @gradeName)
    end
   if not exists (select * from DayItemSales where Category = @category and Subcategory = @subcategory)
    begin
     insert into DayItemSales (Category, Subcategory, GradeCode, GradeName) values (@category, @subcategory, @gradeCode, @gradeName)
    end
   if @fuelCash > 0 and not exists (select * from CardVolumeSales where CardRef = @cardRef and Grade = @subcategory)
    begin
     insert into CardVolumeSales (CardRef, Grade) values (@cardRef, @subcategory)
    end
   update CardSales set Amount = Amount + @fuelCash + @dryCash where CardRef = @cardRef
   update DayCardSales set Amount = Amount + @fuelCash + @dryCash where CardRef = @cardRef
   update ItemSales set Amount = Amount + @fuelCash + @dryCash, Quantity = Quantity + @quantity where Category = @category and Subcategory = @subcategory
   update DayItemSales set Amount = Amount + @fuelCash + @dryCash, Quantity = Quantity + @quantity where Category = @category and Subcategory = @subcategory
   update CardVolumeSales set Volume = Volume + @quantity where CardRef = @cardRef and Grade = @subcategory
   update DayEnd set FuelCash = FuelCash + @fuelCash, DryCash = DryCash + @dryCash, Discount = Discount + @discount
   update DayEnd set LastTransaction = @transaction
   update DayEnd set FirstTransaction = @transaction where FirstTransaction = 0
   update ShiftSummary set FuelCash = FuelCash + @fuelCash, DryCash = DryCash + @dryCash, Discount = Discount + @discount
   update ShiftSummary set LastTransaction = @transaction
   update ShiftSummary set FirstTransaction = @transaction where FirstTransaction = 0
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeDayEnd
 @fuelCash bigint out,
 @dryCash bigint out,
 @discount bigint out,
 @startTime DateTime2 out,
 @endTime DateTime2 out,
 @firstTransaction int out,
 @lastTransaction int out,
 @shiftNumber int out as
 begin
  begin try
   begin transaction
   set @endTime = GETDATE()
   set @fuelCash = (select FuelCash from DayEnd)
   set @dryCash = (select DryCash from DayEnd)
   set @discount = (select Discount from DayEnd)
   set @startTime = (select StartTime from DayEnd)
   set @firstTransaction = (select FirstTransaction from DayEnd)
   set @lastTransaction = (select LastTransaction from DayEnd)
   set @shiftNumber = 0
   update DayEnd set FuelCash = 0, DryCash = 0, Discount = 0, StartTime = @endTime, FirstTransaction = 0, LastTransaction = 0
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeShiftSummary
 @fuelCash bigint out,
 @dryCash bigint out,
 @discount bigint out,
 @startTime DateTime2 out,
 @endTime DateTime2 out,
 @firstTransaction int out,
 @lastTransaction int out,
 @shiftNumber int out as
 begin
  begin try
   begin transaction
   set @endTime = GETDATE()
   set @fuelCash = (select FuelCash from ShiftSummary)
   set @dryCash = (select DryCash from ShiftSummary)
   set @discount = (select Discount from ShiftSummary)
   set @startTime = (select StartTime from ShiftSummary)
   set @firstTransaction = (select FirstTransaction from ShiftSummary)
   set @lastTransaction = (select LastTransaction from ShiftSummary)
   set @shiftNumber = (select ShiftNumber from ShiftSummary)
   update ShiftSummary set FuelCash = 0, DryCash = 0, Discount = 0, StartTime = @endTime, FirstTransaction = 0, LastTransaction = 0, ShiftNumber = (ShiftNumber % 999999) + 1
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeItemSales as
 begin
  begin try
   begin transaction
   select Category, Subcategory, GradeCode, GradeName, Amount, Quantity from ItemSales
   delete from ItemSales
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeDayItemSales as
 begin
  begin try
   begin transaction
   select Category, Subcategory, GradeCode, GradeName, Amount, Quantity from DayItemSales
   delete from DayItemSales
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeCardSales as
 begin
  begin try
   begin transaction
   select s.CardRef, s.Amount, r.CardProductName as ProductName from CardSales s join CardReference r on r.CardRef = s.CardRef
   delete from CardSales
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeDayCardSales as
 begin
  begin try
   begin transaction
   select s.CardRef, s.Amount, r.CardProductName as ProductName from DayCardSales s join CardReference r on r.CardRef = s.CardRef
   delete from DayCardSales
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure TakeCardVolumeSales as
 begin
  begin try
   begin transaction
    select s.CardRef, s.Grade, s.Volume, r.CardProductName as ProductName from CardVolumeSales s join CardReference r on r.CardRef = s.CardRef
    delete from CardVolumeSales
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetDayStart as
 begin
  select StartTime from DayEnd
 end

go

create procedure GetShiftStart as
 begin
  select StartTime from ShiftSummary
 end

go

create procedure FetchCardReferences as
 begin
  select
   c.CardRef, c.CardProductName, c.FuelCard, a.AcquirerName,
   cast
    (case when
      exists (select * from CardSales s where s.CardRef = c.CardRef) or
      exists (select * from DayCardSales d where d.CardRef = c.CardRef) or
      exists (select * from CardVolumeSales v where v.CardRef = c.CardRef) then 1 else 0 end as bit) as InUse
   from CardReference c left join AcquirerReference a on c.AcquirerRef = a.AcquirerRef
 end

go

create procedure SetCardReference
 @name varchar(max),
 @reference int as
 begin
  begin try
   begin transaction
   declare @newCardRef int
   declare @prevCardRef int
   if exists (select * from CardReference where CardRef = @reference)
    begin
     set @newCardRef = (select max(CardRef) from CardReference) + 1
     update CardReference set CardRef = @newCardRef where CardRef = @reference
     update CardSales set CardRef = @newCardRef where CardRef = @reference
     update DayCardSales set CardRef = @newCardRef where CardRef = @reference
     update CardVolumeSales set CardRef = @newCardRef where CardRef = @reference
    end
   if exists (select * from CardReference where CardProductName = @name)
    begin
     set @prevCardRef = (select min(CardRef) from CardReference where CardProductName = @name)
     update CardReference set CardRef = @reference where CardRef = @prevCardRef
     update CardSales set CardRef = @reference where CardRef = @prevCardRef
     update DayCardSales set CardRef = @reference where CardRef = @prevCardRef
     update CardVolumeSales set CardRef = @reference where CardRef = @prevCardRef
    end
   else
    begin
     insert into CardReference (CardRef, CardProductName) values (@reference, @name)
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure ClearCardReference
 @name varchar(max) as
 begin
  begin try
   begin transaction
   if not exists (select * from CardSales s join CardReference c on s.CardRef = c.CardRef and c.CardProductName = @name) and
      not exists (select * from DayCardSales d join CardReference c on d.CardRef = c.CardRef and c.CardProductName = @name) or
      not exists (select * from CardVolumeSales v join CardReference c on v.CardRef = c.CardRef and c.CardProductName = @name)
    begin
     delete from CardReference where CardProductName = @name
     delete from a from AcquirerReference a where not exists (select * from CardReference c where c.AcquirerRef = a.AcquirerRef)
    end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetCardReferenceFuelCard
 @cardName varchar(max),
 @isFuelCard bit as
 begin
  if exists (select * from CardReference where CardProductName = @cardName)
   begin
    update CardReference set FuelCard = @isFuelCard where CardProductName = @cardName
   end
  else
   begin
    declare @cardRef int
    if exists (select * from CardReference)
     begin
      set @cardRef = (select max(CardRef) from CardReference) + 1
     end
    else
     begin
      set @cardRef = 1
     end
    insert into CardReference (CardRef, CardProductName, FuelCard) values (@cardRef, @cardName, @isFuelCard)
   end
 end

go

create procedure SetAcquirerReference
 @cardName varchar(max),
 @acquirerName varchar(max) as
 begin
  begin try
   begin transaction
   declare @cardRef int
   declare @acquirerRef int
   if not exists (select * from CardReference where CardProductName = @cardName)
    begin
     if exists (select * from CardReference)
      begin
       set @cardRef = (select max(CardRef) from CardReference) + 1
      end
     else
      begin
       set @cardRef = 1
      end
     insert into CardReference (CardRef, CardProductName) values (@cardRef, @cardName)
    end
   if exists (select * from AcquirerReference where AcquirerName = @acquirerName)
    begin
     set @acquirerRef = (select min(AcquirerRef) from AcquirerReference where AcquirerName = @acquirerName)
    end
   else
    begin
     if exists (select * from AcquirerReference)
      begin
       set @acquirerRef = (select max(AcquirerRef) from AcquirerReference) + 1
      end
     else
      begin
       set @acquirerRef = 1
      end
     insert into AcquirerReference (AcquirerRef, AcquirerName) values (@acquirerRef, @acquirerName)
    end
    update CardReference set AcquirerRef = @acquirerRef where CardProductName = @cardName
    delete from a from AcquirerReference a where not exists (select * from CardReference c where c.AcquirerRef = a.AcquirerRef)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure ClearAcquirerReference
 @cardName varchar(max) as
 begin
  begin try
   begin transaction
   update CardReference set AcquirerRef = null where CardProductName = @cardName
   delete from a from AcquirerReference a where not exists (select * from CardReference c where c.AcquirerRef = a.AcquirerRef)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetPumpDelivered
 @pump int
 as
 begin
  select Number, OPTPayment, Delivered, Grade, Volume, Amount, Name, Price, NetAmount, VatAmount, VatRate from PumpDelivered where Number = @pump
 end

go

create procedure SetPumpDelivered
 @pump int,
 @grade int,
 @volume bigint,
 @amount bigint,
 @name varchar(20),
 @price int,
 @netAmount bigint,
 @vatAmount bigint,
 @vatRate float as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpDelivered where Number = @pump))
    begin
     insert PumpDelivered (Number) values (@pump)
    end
   update PumpDelivered set
    Delivered = 1,
    Grade = @grade,
    Volume = @volume,
    Amount = @amount,
    Name = @name,
    Price = @price,
    NetAmount = @netAmount,
    VatAmount = @vatAMount,
    VatRate = @vatRate
    where Number = @pump
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure ClearPumpDelivered
 @pump int as
 begin
  begin try
   begin transaction
    update PumpDelivered set Delivered = 0 where Number = @pump
    commit transaction
   end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure SetPumpOPTPayment
 @pump int as
 begin
  begin try
   begin transaction
   if (not exists (select * from PumpDelivered where Number = @pump))
    begin
     insert PumpDelivered (Number) values (@pump)
    end
    update PumpDelivered set OPTPayment = 1 where Number = @pump
    commit transaction
   end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure ClearPumpOPTPayment
 @pump int as
 begin
  begin try
   begin transaction
    update PumpDelivered set OPTPayment = 0 where Number = @pump
    commit transaction
   end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

go

create procedure GetRetalixPrimary as
 begin
  select IPAddress from RetalixPrimary
 end

go

create procedure SetRetalixPrimary
 @ipAddress varchar(15) as
 begin
  update RetalixPrimary set IPAddress = @ipAddress
 end

go

create procedure ClearRetalixPrimary as
 begin
  update RetalixPrimary set IPAddress = null
 end

go

create procedure GetFileLocations as
 begin
  select RetalixTransactionFileDirectory, TransactionFileDirectory ,WhitelistDirectory,
         LayoutDirectory, SoftwareDirectory, ContactlessPropertiesFile, FuelDataUpdateFile,
         UpgradeFileDirectory, RollbackFileDirectory, MediaDirectory, PlaylistDirectory,
         OPTLogFileDirectory, LogFileDirectory, TraceFileDirectory, JournalFileDirectory,
         ReceivedUpdateDirectory, DatabaseBackupDirectory, ESocketConnectionString,
         ESocketUseConnectionString, ESocketConfigFile,
         ESocketKeystoreFile, ESocketDbUrl, ESocketOverrideProperties,
         ESocketOverrideKeystore, ESocketOverrideUrl,
         ESocketOverrideContactless from FileLocations
 end

go

create procedure SetRetalixTransactionFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set RetalixTransactionFileDirectory = @directory
 end

go

create procedure ClearRetalixTransactionFileDirectory as
 begin
  update FileLocations set RetalixTransactionFileDirectory = null
 end

go

create procedure SetTransactionFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set TransactionFileDirectory = @directory
 end

go

create procedure SetWhitelistDirectory
 @directory varchar(max) as
 begin
  update FileLocations set WhitelistDirectory = @directory
 end

go

create procedure SetLayoutDirectory
 @directory varchar(max) as
 begin
  update FileLocations set LayoutDirectory = @directory
 end

go

create procedure SetSoftwareDirectory
 @directory varchar(max) as
 begin
  update FileLocations set SoftwareDirectory = @directory
 end

go

create procedure SetContactlessPropertiesFile
 @fileName varchar(max) as
 begin
  update FileLocations set ContactlessPropertiesFile = @fileName
 end

go

create procedure SetFuelDataUpdateFile
 @fileName varchar(max) as
 begin
  update FileLocations set FuelDataUpdateFile = @fileName
 end

go

create procedure SetUpgradeFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set UpgradeFileDirectory = @directory
 end

go

create procedure SetRollbackFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set RollbackFileDirectory = @directory
 end

go

create procedure SetMediaDirectory
 @directory varchar(max) as
 begin
  update FileLocations set MediaDirectory = @directory
 end

go

create procedure SetPlaylistDirectory
 @directory varchar(max) as
 begin
  update FileLocations set PlaylistDirectory = @directory
 end

go

create procedure SetOPTLogFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set OPTLogFileDirectory = @directory
 end

go

create procedure SetLogFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set LogFileDirectory = @directory
 end

go

create procedure SetTraceFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set TraceFileDirectory = @directory
 end

go

create procedure SetJournalFileDirectory
 @directory varchar(max) as
 begin
  update FileLocations set JournalFileDirectory = @directory
 end

go

create procedure SetReceivedUpdateDirectory
 @directory varchar(max) as
 begin
  update FileLocations set ReceivedUpdateDirectory = @directory
 end

go

create procedure SetDatabaseBackupDirectory
 @directory varchar(max) as
 begin
  update FileLocations set DatabaseBackupDirectory = @directory
 end

go

create procedure SetESocketConnectionString
 @newConnectionString varchar(max) as
 begin
  update FileLocations set ESocketConnectionString = @newConnectionString
 end

go

create procedure SetESocketUseConnectionString
 @useConnectionString bit as
 begin
  update FileLocations set ESocketUseConnectionString = @useConnectionString
 end

go

create procedure SetESocketConfigFile
 @fileName varchar(max) as
 begin
  update FileLocations set ESocketConfigFile = @fileName
 end

go

create procedure SetESocketKeystoreFile
 @fileName varchar(max) as
 begin
  update FileLocations set ESocketKeystoreFile = @fileName
 end

go

create procedure SetESocketDbUrl
 @url varchar(max) as
 begin
  update FileLocations set ESocketDbUrl = @url
 end

go

create procedure SetESocketOverrideProperties
 @flag bit as
 begin
  update FileLocations set ESocketOverrideProperties = @flag
 end

go

create procedure SetESocketOverrideKeystore
 @flag bit as
 begin
  update FileLocations set ESocketOverrideKeystore = @flag
 end

go

create procedure SetESocketOverrideUrl
 @flag bit as
 begin
  update FileLocations set ESocketOverrideUrl = @flag
 end

go

create procedure SetESocketOverrideContactless
 @flag bit as
 begin
  update FileLocations set ESocketOverrideContactless = @flag
 end

go

create procedure GetSiteInfo as
 begin
  select Mode, SiteName, VATNumber, NozzleUpForKioskUse, UseReplaceNozzleScreen, CurrencyCode, ForwardFuelPriceUpdate, TillNumber, FuelCategory, MaxFillOverride from SiteInfo
 end

go

create procedure SetReceiptLayoutMode
 @mode int as
 begin
  update SiteInfo set Mode = @mode
 end

go

create procedure SetSiteName
 @siteName varchar(80) as
 begin
  update SiteInfo set SiteName = @siteName
 end

go

create procedure SetVATNumber
 @vatNumber varchar(40) as
 begin
  update SiteInfo set VATNumber = @vatNumber
 end

go

create procedure SetCurrencyCode
 @currencyCode int as
 begin
  update SiteInfo set CurrencyCode = @currencyCode
 end

go

create procedure SetNozzleUpForKioskUse
 @flag bit as
 begin
  update SiteInfo set NozzleUpForKioskUse = @flag
 end

go

create procedure SetUseReplaceNozzleScreen
 @flag bit as
 begin
  update SiteInfo set UseReplaceNozzleScreen = @flag
 end

go

create procedure SetForwardFuelPriceUpdate
 @flag bit as
 begin
  update SiteInfo set ForwardFuelPriceUpdate = @flag
 end

go

create procedure SetTillNumber
 @number int as
 begin
  update SiteInfo set TillNumber = @number
 end

go

create procedure SetFuelCategory
 @category int as
 begin
  update SiteInfo set FuelCategory = @category
 end

go

create procedure SetMaxFillOverride
 @maxFillOverride bigint as
 begin
  update SiteInfo set MaxFillOverride = @maxFillOverride
 end

go

create procedure GetPosClaim as
 begin
  select PosNumber from PosClaim
 end

go

create procedure SetPosClaimNumber
 @number int as
 begin
  update PosClaim set PosNumber = @number
 end

go

create procedure GetPruneDays as
 begin
  select FilePruneDays, TransactionPruneDays, ReceiptPruneDays from PruneDays
 end

go

create procedure SetFilePruneDays
 @days int as
 begin
  update PruneDays set FilePruneDays = @days
 end

go

create procedure SetTransactionPruneDays
 @days int as
 begin
  update PruneDays set TransactionPruneDays = @days
 end

go

create procedure SetReceiptPruneDays
 @days int as
 begin
  update PruneDays set ReceiptPruneDays = @days
 end

go

create procedure GetNextDayEnd as
 begin
  select DayEnd from NextDayEnd
 end

go

create procedure GetLogInterval as
 begin
  select LogInterval from NextDayEnd
 end

go

create procedure SetNextDayEnd
 @dayEnd DateTime2 as
 begin
  update NextDayEnd set DayEnd = @dayEnd
 end

go

create procedure SetLogInterval
 @interval int as
 begin
  update NextDayEnd set LogInterval = @interval
 end

go

create procedure GetPrinterConfig as
 begin
  select Enabled, PortName, BaudRate, Handshake, StopBits, DataBits from PrinterConfig
 end

go

create procedure SetPrinterEnabled
 @isEnabled bit as
 begin
  update PrinterConfig set Enabled = @isEnabled
 end

go

create procedure SetPrinterPortName
 @portName varchar(max) as
 begin
  update PrinterConfig set PortName = @portName
 end

go

create procedure SetPrinterBaudRate
 @baudRate int as
 begin
  update PrinterConfig set BaudRate = @baudRate
 end

go

create procedure SetPrinterHandshake
 @handshake varchar(max) as
 begin
  update PrinterConfig set Handshake = @handshake
 end

go

create procedure SetPrinterStopBits
 @stopBits varchar(max) as
 begin
  update PrinterConfig set StopBits = @stopBits
 end

go

create procedure SetPrinterDataBits
 @dataBits int as
 begin
  update PrinterConfig set DataBits = @dataBits
 end

go

create procedure ClearPreviousMeterReadings as
 begin
  delete from PreviousMeterReadings
 end

go

create procedure AddPreviousMeterReading
 @pump int,
 @volume1 bigint,
 @cash1 bigint,
 @volume2 bigint,
 @cash2 bigint,
 @volume3 bigint,
 @cash3 bigint,
 @volume4 bigint,
 @cash4 bigint as
 begin
  if exists (select * from PreviousMeterReadings where Pump = @pump)
   begin
    update PreviousMeterReadings
     set Volume1 = @volume1, Cash1 = @cash1, Volume2 = @volume2, Cash2 = @cash2,
         Volume3 = @volume3, Cash3 = @cash3, Volume4 = @volume4, Cash4 = @cash4
   end
  else
   begin
    insert into PreviousMeterReadings (Pump, Volume1, Cash1, Volume2, Cash2, Volume3, Cash3, Volume4, Cash4)
     values (@pump, @volume1, @cash1, @volume2, @cash2, @volume3, @cash3, @volume4, @cash4)
   end
 end

go

create procedure FetchPreviousMeterReadings as
 begin
  select Pump, Volume1, Cash1, Volume2, Cash2, Volume3, Cash3, Volume4, Cash4 from PreviousMeterReadings
 end

go

create procedure TakeShiftList as
 begin
  select ShiftNumber, StartTime, EndTime from ShiftList order by ShiftNumber
  delete from ShiftList
 end

go

create procedure AddShiftToList
 @shiftNumber int,
 @startTime DateTime2,
 @endTime DateTime2 as
 begin
  if exists (select * from ShiftList where ShiftNumber = @shiftNumber)
   begin
    update ShiftList set StartTime = @startTime, EndTime = @endTime where ShiftNumber = @shiftNumber
   end
  else
   begin
    insert ShiftList (ShiftNumber, StartTime, EndTime) values (@shiftNumber, @startTime, @endTime)
   end
 end

go

create procedure GetDOMSInfo as
 begin
  select IpAddress, LoginString, Enabled, Detect from DOMSInfo
 end

go

create procedure SetDOMSIpAddress
 @ipAddress varchar(15) as
 begin
  update DOMSInfo set IpAddress = @ipAddress
 end

go

create procedure SetDOMSLoginString
 @loginString varchar(80) as
 begin
  update DOMSInfo set LoginString = @loginString
 end

go

create procedure SetDOMSEnabled
 @enabled bit as
 begin
  update DOMSInfo set Enabled = @enabled
 end

go

create procedure SetDOMSDetect
 @detect bit as
 begin
  update DOMSInfo set Detect = @detect
 end

go

-- Create User
if not exists (select * from sys.database_principals where name='UserRole' and type='R')
 begin
  create role UserRole
 end

if not exists (select * from sys.database_principals where name='InstallerRole' and type='R')
 begin
  create role InstallerRole
 end

if exists (select * from master.sys.server_principals where type='U') and not exists (select * from master.sys.server_principals where name='NT AUTHORITY\LOCAL SERVICE' and type='U')
 begin
  create login [NT AUTHORITY\LOCAL SERVICE] from WINDOWS
 end

if not exists (select * from sys.database_principals where name='NT AUTHORITY\LOCAL SERVICE' and type='U')
 begin
  create user [NT AUTHORITY\LOCAL SERVICE] for login [NT AUTHORITY\LOCAL SERVICE]
 end

if not exists
 (select * from sys.database_role_members r
   join sys.database_principals p1 on r.role_principal_id = p1.principal_id and p1.name = 'UserRole'
   join sys.database_principals p2 on r.member_principal_id = p2.principal_id and p2.name = 'NT AUTHORITY\LOCAL SERVICE')
 begin
  alter role UserRole add member [NT AUTHORITY\LOCAL SERVICE]
 end

if not exists
 (select * from sys.database_role_members r
   join sys.database_principals p1 on r.role_principal_id = p1.principal_id and p1.name = 'InstallerRole'
   join sys.database_principals p2 on r.member_principal_id = p2.principal_id and p2.name = 'NT AUTHORITY\LOCAL SERVICE')
 begin
  alter role InstallerRole add member [NT AUTHORITY\LOCAL SERVICE]
 end

grant control to InstallerRole

alter server role dbcreator add member [NT AUTHORITY\LOCAL SERVICE]

go

-- Grant Permissions for Stored Procedures
grant execute on GetOPTEndPoints to UserRole
grant execute on SetOPTEndPoints to UserRole
grant execute on RemoveOPTEndPoints to UserRole
grant execute on SetAutoAuth to UserRole
grant execute on SetMediaChannel to UserRole
grant execute on SetUnmannedPseudoPOS to UserRole
grant execute on SetAsdaDayEndReport to UserRole
grant execute on GetDivertOPT to UserRole
grant execute on SetDivertOPT to UserRole
grant execute on SetOPTDiverted to UserRole
grant execute on GetOPTMode to UserRole
grant execute on SetReceiptHeader to UserRole
grant execute on SetPlaylistFileName to UserRole
grant execute on SetLastLogTime to UserRole
grant execute on GetPumpEndPoint to UserRole
grant execute on SetPumpEndPoint to UserRole
grant execute on GetANPREndPoint to UserRole
grant execute on SetANPREndPoint to UserRole
grant execute on GetCarWashEndPoint to UserRole
grant execute on SetCarWashEndPoint to UserRole
grant execute on GetTankGaugeEndPoint to UserRole
grant execute on SetTankGaugeEndPoint to UserRole
grant execute on GetHydraMobileEndPoint to UserRole
grant execute on SetHydraMobileEndPoint to UserRole
grant execute on GetPumpTIDs to UserRole
grant execute on AddPumpTID to UserRole
grant execute on AddPumpOPT to UserRole
grant execute on SetPumpKioskOnly to UserRole
grant execute on SetPumpOutsideOnly to UserRole
grant execute on SetPumpMixed to UserRole
grant execute on SetPumpClosed to UserRole
grant execute on SetPumpMaxFillOverrideForFuelCards to UserRole
grant execute on SetPumpMaxFillOverrideForPaymentCards to UserRole
grant execute on ClearPumpTIDs to UserRole
grant execute on GetGradeNames to UserRole
grant execute on SetGradeName to UserRole
grant execute on ClearGradeNames to UserRole
grant execute on GetESocketEndPoints to UserRole
grant execute on AddESocketEndPoint to UserRole
grant execute on RemoveESocketEndPoint to UserRole
grant execute on ClearESocketEndPoints to UserRole
grant execute on GetTariffMappings to UserRole
grant execute on AddTariffMapping to UserRole
grant execute on ClearTariffMappings to UserRole
grant execute on SetFuelCardsOnly to UserRole
grant execute on GetPredefinedAmounts to UserRole
grant execute on AddPredefinedAmount to UserRole
grant execute on ClearPredefinedAmounts to UserRole
grant execute on GetDiscountCards to UserRole
grant execute on AddDiscountCard to UserRole
grant execute on RemoveDiscountCard to UserRole
grant execute on GetDiscountWhitelist to UserRole
grant execute on AddDiscountWhitelist to UserRole
grant execute on RemoveDiscountWhitelist to UserRole
grant execute on GetLocalAccountCustomers to UserRole
grant execute on GetLocalAccountCards to UserRole
grant execute on AddLocalAccountCustomer to UserRole
grant execute on DeleteLocalAccountCustomer to UserRole
grant execute on AddLocalAccountCard to UserRole
grant execute on DeleteLocalAccountCard to UserRole
grant execute on AddLoyaltyReference to UserRole
grant execute on DeleteLoyaltyReference to UserRole
grant execute on SetLoyaltyPresent to UserRole
grant execute on GetLoyaltyPresent to UserRole
grant execute on GetLoyaltyTerminal to UserRole
grant execute on GetLoyaltyHosts to UserRole
grant execute on GetLoyaltyHostnames to UserRole
grant execute on GetLoyaltyIINs to UserRole
grant execute on GetLoyaltyMappings to UserRole
grant execute on SetLoyaltyTerminal to UserRole
grant execute on AddLoyaltyHost to UserRole
grant execute on AddLoyaltyHostname to UserRole
grant execute on AddLoyaltyIIN to UserRole
grant execute on AddLoyaltyMapping to UserRole
grant execute on ClearLoyalty to UserRole
grant execute on GetWashes to UserRole
grant execute on AddWash to UserRole
grant execute on RemoveWash to UserRole
grant execute on GetPaymentTimeout to UserRole
grant execute on SetPaymentTimeout to UserRole
grant execute on ClearPaymentTimeout to UserRole
grant execute on AddReceipt to UserRole
grant execute on SetReceiptTimeout to UserRole
grant execute on SetReceiptMaxCount to UserRole
grant execute on GetReceiptTimeout to UserRole
grant execute on GetReceiptMaxCount to UserRole
grant execute on GetFuelling to UserRole
grant execute on SetFuellingIndefiniteWait to UserRole
grant execute on SetFuellingWaitMinutes to UserRole
grant execute on SetFuellingBackoffAuth to UserRole
grant execute on SetFuellingBackoffPreAuth to UserRole
grant execute on SetFuellingBackoffStopStart to UserRole
grant execute on SetFuellingBackoffStopOnly to UserRole
grant execute on GetReceipts to UserRole
grant execute on PruneReceipts to UserRole
grant execute on PruneTransactions to UserRole
grant execute on AddTransaction to UserRole
grant execute on AddToTransaction to UserRole
grant execute on AddEvent to UserRole
grant execute on GetFuelTransactions to UserRole
grant execute on GetOtherEvents to UserRole
grant execute on AddDayEnd to UserRole
grant execute on TakeDayEnd to UserRole
grant execute on TakeShiftSummary to UserRole
grant execute on TakeItemSales to UserRole
grant execute on TakeDayItemSales to UserRole
grant execute on TakeCardSales to UserRole
grant execute on TakeDayCardSales to UserRole
grant execute on TakeCardVolumeSales to UserRole
grant execute on GetDayStart to UserRole
grant execute on GetShiftStart to UserRole
grant execute on FetchCardReferences to UserRole
grant execute on SetCardReference to UserRole
grant execute on ClearCardReference to UserRole
grant execute on SetCardReferenceFuelCard to UserRole
grant execute on SetAcquirerReference to UserRole
grant execute on ClearAcquirerReference to UserRole
grant execute on GetPumpDelivered to UserRole
grant execute on SetPumpDelivered to UserRole
grant execute on ClearPumpDelivered to UserRole
grant execute on SetPumpOPTPayment to UserRole
grant execute on ClearPumpOPTPayment to UserRole
grant execute on GetRetalixPrimary to UserRole
grant execute on SetRetalixPrimary to UserRole
grant execute on ClearRetalixPrimary to UserRole
grant execute on GetFileLocations to UserRole
grant execute on SetRetalixTransactionFileDirectory to UserRole
grant execute on ClearRetalixTransactionFileDirectory to UserRole
grant execute on SetTransactionFileDirectory to UserRole
grant execute on SetWhitelistDirectory to UserRole
grant execute on SetLayoutDirectory to UserRole
grant execute on SetSoftwareDirectory to UserRole
grant execute on SetContactlessPropertiesFile to UserRole
grant execute on SetFuelDataUpdateFile to UserRole
grant execute on SetUpgradeFileDirectory to UserRole
grant execute on SetRollbackFileDirectory to UserRole
grant execute on SetMediaDirectory to UserRole
grant execute on SetPlaylistDirectory to UserRole
grant execute on SetOPTLogFileDirectory to UserRole
grant execute on SetLogFileDirectory to UserRole
grant execute on SetTraceFileDirectory to UserRole
grant execute on SetJournalFileDirectory to UserRole
grant execute on SetReceivedUpdateDirectory to UserRole
grant execute on SetDatabaseBackupDirectory to UserRole
grant execute on SetESocketConnectionString to UserRole
grant execute on SetESocketUseConnectionString to UserRole
grant execute on SetESocketConfigFile to UserRole
grant execute on SetESocketKeystoreFile to UserRole
grant execute on SetESocketDbUrl to UserRole
grant execute on SetESocketOverrideProperties to UserRole
grant execute on SetESocketOverrideKeystore to UserRole
grant execute on SetESocketOverrideUrl to UserRole
grant execute on SetESocketOverrideContactless to UserRole
grant execute on GetSiteInfo to UserRole
grant execute on SetReceiptLayoutMode to UserRole
grant execute on SetSiteName to UserRole
grant execute on SetVATNumber to UserRole
grant execute on SetNozzleUpForKioskUse to UserRole
grant execute on SetUseReplaceNozzleScreen to UserRole
grant execute on SetCurrencyCode to UserRole
grant execute on SetForwardFuelPriceUpdate to UserRole
grant execute on SetTillNumber to UserRole
grant execute on SetFuelCategory to UserRole
grant execute on SetMaxFillOverride to UserRole
grant execute on GetPosClaim to UserRole
grant execute on SetPosClaimNumber to UserRole
grant execute on GetPruneDays to UserRole
grant execute on SetFilePruneDays to UserRole
grant execute on SetTransactionPruneDays to UserRole
grant execute on SetReceiptPruneDays to UserRole
grant execute on GetNextDayEnd to UserRole
grant execute on SetNextDayEnd to UserRole
grant execute on GetLogInterval to UserRole
grant execute on SetLogInterval to UserRole
grant execute on GetPrinterConfig to UserRole
grant execute on SetPrinterEnabled to UserRole
grant execute on SetPrinterPortName to UserRole
grant execute on SetPrinterBaudRate to UserRole
grant execute on SetPrinterHandshake to UserRole
grant execute on SetPrinterStopBits to UserRole
grant execute on SetPrinterDataBits to UserRole
grant execute on FetchPreviousMeterReadings to UserRole
grant execute on ClearPreviousMeterReadings to UserRole
grant execute on AddPreviousMeterReading to UserRole
grant execute on AddShiftToList to UserRole
grant execute on TakeShiftList to UserRole
grant execute on GetDOMSInfo to UserRole
grant execute on SetDOMSIpAddress to UserRole
grant execute on SetDOMSLoginString to UserRole
grant execute on SetDOMSEnabled to UserRole
grant execute on SetDOMSDetect to UserRole
grant execute on AddTransactionRecord to UserRole
grant execute on AddTransaction to UserRole

go

if exists (select * from sys.objects where object_id = OBJECT_ID('GetMorrisonsLoyaltyTerminal') and type in ('P', 'PC'))
 begin
  drop procedure GetMorrisonsLoyaltyTerminal
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetMorrisonsLoyaltyHosts') and type in ('P', 'PC'))
 begin
  drop procedure GetMorrisonsLoyaltyHosts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetMorrisonsLoyaltyIINs') and type in ('P', 'PC'))
 begin
  drop procedure GetMorrisonsLoyaltyIINs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetMorrisonsLoyaltyMappings') and type in ('P', 'PC'))
 begin
  drop procedure GetMorrisonsLoyaltyMappings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('SetMorrisonsLoyaltyTerminal') and type in ('P', 'PC'))
 begin
  drop procedure SetMorrisonsLoyaltyTerminal
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddMorrisonsLoyaltyHost') and type in ('P', 'PC'))
 begin
  drop procedure AddMorrisonsLoyaltyHost
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddMorrisonsLoyaltyIIN') and type in ('P', 'PC'))
 begin
  drop procedure AddMorrisonsLoyaltyIIN
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddMorrisonsLoyaltyMapping') and type in ('P', 'PC'))
 begin
  drop procedure AddMorrisonsLoyaltyMapping
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearMorrisonsLoyalty') and type in ('P', 'PC'))
 begin
  drop procedure ClearMorrisonsLoyalty
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetTillNumber') and type in ('P', 'PC'))
 begin
  drop procedure GetTillNumber
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetFuelCategory') and type in ('P', 'PC'))
 begin
  drop procedure GetFuelCategory
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLocalAccounts') and type in ('P', 'PC'))
 begin
  drop procedure GetLocalAccounts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLocalAccount') and type in ('P', 'PC'))
 begin
  drop procedure AddLocalAccount
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveLocalAccount') and type in ('P', 'PC'))
 begin
  drop procedure RemoveLocalAccount
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetLocalAccountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure GetLocalAccountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddLocalAccountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure AddLocalAccountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveLocalAccountWhitelist') and type in ('P', 'PC'))
 begin
  drop procedure RemoveLocalAccountWhitelist
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetSiteName') and type in ('P', 'PC'))
 begin
  drop procedure GetSiteName
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetVATNumber') and type in ('P', 'PC'))
 begin
  drop procedure GetVATNumber
 end
 
if exists (select * from sys.objects where object_id = OBJECT_ID('GetCardClessAIDs') and type in ('P', 'PC'))
 begin
  drop procedure GetCardClessAIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('GetCardClessDRLs') and type in ('P', 'PC'))
 begin
  drop procedure GetCardClessDRLs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddCardClessAID') and type in ('P', 'PC'))
 begin
  drop procedure AddCardClessAID
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('AddCardClessDRL') and type in ('P', 'PC'))
 begin
  drop procedure AddCardClessDRL
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveCardClessAID') and type in ('P', 'PC'))
 begin
  drop procedure RemoveCardClessAID
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('RemoveCardClessDRL') and type in ('P', 'PC'))
 begin
  drop procedure RemoveCardClessDRL
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearCardClessAIDs') and type in ('P', 'PC'))
 begin
  drop procedure ClearCardClessAIDs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ClearCardClessAIDsOnly') and type in ('P', 'PC'))
 begin
  drop procedure ClearCardClessAIDsOnly
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('MorrisonsLoyaltyTerminal') and type = 'u')
 begin
  drop table MorrisonsLoyaltyTerminal
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('MorrisonsLoyaltyHosts') and type = 'u')
 begin
  drop table MorrisonsLoyaltyHosts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('MorrisonsLoyaltyIINs') and type = 'u')
 begin
  drop table MorrisonsLoyaltyIINs
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('MorrisonsLoyaltyMappings') and type = 'u')
 begin
  drop table MorrisonsLoyaltyMappings
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('ReceiptLayoutMode') and type = 'u')
 begin
  drop table ReceiptLayoutMode
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('TillNumber') and type = 'u')
 begin
  drop table TillNumber
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('LocalAccounts') and type = 'u')
 begin
  drop table LocalAccounts
 end

if exists (select * from sys.objects where object_id = OBJECT_ID('LocalAccountWhitelist') and type = 'u')
 begin
  drop table LocalAccountWhitelist
 end

go

-- Contactless
PRINT N'HOPT-1172 - Add contactless configuration...Begin';
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationCategory') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationCategory]...';

	CREATE TABLE [dbo].[ConfigurationCategory](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[Category] [nvarchar](100) NOT NULL,
		[Description] [nvarchar](255) NULL,
	 CONSTRAINT [PK_ConfigurationCategory] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationType') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationType]...';
	
	CREATE TABLE [dbo].[ConfigurationType](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[Type] [nvarchar](100) NOT NULL,
		[Description] [nvarchar](255) NULL,
	 CONSTRAINT [PK_ConfigurationType] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationHeader') AND TYPE = 'u')
BEGIN
    PRINT N'Creating [dbo].[ConfigurationHeader]...';

	CREATE TABLE [dbo].[ConfigurationHeader](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[TypeId] [int] NOT NULL,
		[Description] [nvarchar](255) NULL,
		[ModifiedDateTime] [datetime] NOT NULL,
	 CONSTRAINT [PK_ConfigurationHeader] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
	ALTER TABLE [dbo].[ConfigurationHeader]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationHeader_ConfigurationType]
	FOREIGN KEY([TypeId]) REFERENCES [dbo].[ConfigurationType] ([id])

	ALTER TABLE [dbo].[ConfigurationHeader] CHECK CONSTRAINT [FK_ConfigurationHeader_ConfigurationType]
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationDetail') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationDetail]...';

	CREATE TABLE [dbo].[ConfigurationDetail](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[HeaderId] [int] NOT NULL,
		[CategoryId] [int] NOT NULL,
		[Key] [nvarchar](255) NOT NULL,
		[Value] [nvarchar](max) NOT NULL,
		[ModifiedDateTime] [datetime] NOT NULL,
	 CONSTRAINT [PK_Configuration] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 100) ON [PRIMARY],
	 CONSTRAINT [UQ_codes] UNIQUE NONCLUSTERED 
	(
		[HeaderId] ASC,
		[CategoryId] ASC,
		[Key] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 100) ON [PRIMARY]
	) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

	ALTER TABLE [dbo].[ConfigurationDetail]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationDetail_ConfigurationCategory]
	FOREIGN KEY([CategoryId]) REFERENCES [dbo].[ConfigurationCategory] ([Id])

	ALTER TABLE [dbo].[ConfigurationDetail] CHECK CONSTRAINT [FK_ConfigurationDetail_ConfigurationCategory]

	ALTER TABLE [dbo].[ConfigurationDetail]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationDetail_ConfigurationHeader]
	FOREIGN KEY([HeaderId]) REFERENCES [dbo].[ConfigurationHeader] ([id])

	ALTER TABLE [dbo].[ConfigurationDetail] CHECK CONSTRAINT [FK_ConfigurationDetail_ConfigurationHeader]
END
GO

PRINT N'Creating Procedure [dbo].[SetConfigValue]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('SetConfigValue') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE SetConfigValue
END

GO 

CREATE PROCEDURE [dbo].SetConfigValue
  @headerId int,
  @categoryId int,
  @key VARCHAR(255),
  @value VARCHAR(255) AS
	BEGIN TRY
		BEGIN TRANSACTION
			UPDATE dbo.[ConfigurationDetail] WITH (UPDLOCK, SERIALIZABLE) 
			SET [Value] = @value,
				[ModifiedDateTime] = GETDATE()
			WHERE HeaderId = @headerId
				AND CategoryId = @categoryId 
				AND [KEY] = @key
 
			IF @@ROWCOUNT = 0
			BEGIN
				INSERT [ConfigurationDetail] ([HeaderId],[CategoryId],[Key],[Value],[ModifiedDateTime]) 
				VALUES (@headerId, @categoryId, @key, @value,  GETDATE())
			END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW
	END CATCH
GO

PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
 
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'TTQ')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ 
	FROM SiteInfo
END
GO

PRINT N'Creating Procedure [dbo].[GetConfigurationTypes]...';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetConfigurationTypes') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetConfigurationTypes
END
GO

CREATE PROCEDURE GetConfigurationTypes AS
BEGIN 
	SELECT [Id], [Type]
	FROM ConfigurationType

	SELECT [Id], [Category]
	FROM ConfigurationCategory

	SELECT [Id], [TypeId], [Description]
	FROM ConfigurationHeader
END

GO

PRINT N'Creating GetConfigurationTypes Permission...';


GO
GRANT EXECUTE
    ON OBJECT::[dbo].[GetConfigurationTypes] TO [UserRole]
    AS [dbo];


GO

PRINT N'HOPT-1172 - Add contactless configuration...End';
PRINT N'';
GO


PRINT N'HOPT-1062 - Standard Configuration...Begin';
PRINT N'Creating Procedure [dbo].[GetCategoryConfiguration]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetCategoryConfiguration') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetCategoryConfiguration
END
GO

CREATE PROCEDURE [dbo].[GetCategoryConfiguration]
  @headerId INT,
  @categoryId INT AS
BEGIN
 
    SELECT [Key],[Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId

END
GO

PRINT N'HOPT-1062 - Standard Configuration...End';
PRINT N'';
GO


PRINT N'HOPT-750/1294 - Site Type...Begin';
PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
    DECLARE @siteInfoCategory int = (SELECT [Id] FROM ConfigurationCategory WHERE [Category] = 'SITEINFO')
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'TTQ')
	DECLARE @siteType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'SITETYPE')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ,
	@siteType AS SiteType
	FROM SiteInfo
END

GO

PRINT N'HOPT-750/1294 - Site Type...End';
PRINT N'';
GO


PRINT N'HOPT-1416 - POS Type...Begin';
PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
    DECLARE @siteInfoCategory int = (SELECT [Id] FROM ConfigurationCategory WHERE [Category] = 'SITEINFO')
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'TTQ')
	DECLARE @siteType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'SITETYPE')
	DECLARE @posType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'POSTYPE')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ,
	@siteType AS SiteType,
	@posType AS PosType
	FROM SiteInfo
END
GO

PRINT N'HOPT-1416 - POS Type...End';
PRINT N'';
GO

PRINT N'HOPT-1117 - Create Local Accounts Configuration...Begin';
PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
    DECLARE @siteInfoCategory int = (SELECT [Id] FROM ConfigurationCategory WHERE [Category] = 'SITEINFO')
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'TTQ')
	DECLARE @siteType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'SITETYPE')
	DECLARE @posType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'POSTYPE')
	DECLARE @localAccountsEnabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'LOCALACCOUNTSENABLED')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ,
	@siteType AS SiteType,
	@posType AS PosType,
	@localAccountsEnabled AS LocalAccountsEnabled
	FROM SiteInfo
END

GO
PRINT N'HOPT-1117 - Create Local Accounts Configuration...End';
PRINT N'';

GO


PRINT N'HOPT-1023 - Standardise Log file/folder convention...Begin';
PRINT N'';

UPDATE [FileLocations]
SET [LogFileDirectory] = 'C:\Logs',
	[OPTLogFileDirectory] = 'C:\Logs'
GO

PRINT N'HOPT-1023 - Standardise Log file/folder convention...End';
PRINT N'';
GO

PRINT N'HOPT-1450 - OPT Service does not retrieve ReceiptLayoutMode...Begin';
PRINT N'Updating Procedure [dbo].[GetSiteInfo]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
    DECLARE @siteInfoCategory int = (SELECT [Id] FROM ConfigurationCategory WHERE [Category] = 'SITEINFO')
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'TTQ')
	DECLARE @siteType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'SITETYPE')
	DECLARE @posType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'POSTYPE')
	DECLARE @localAccountsEnabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'LOCALACCOUNTSENABLED')

	SELECT 
	Mode AS ReceiptLayoutMode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ,
	@siteType AS SiteType,
	@posType AS PosType,
	@localAccountsEnabled AS LocalAccountsEnabled
	FROM SiteInfo
END

GO
PRINT N'HOPT-1450 - OPT Service does not retrieve ReceiptLayoutMode...End';
PRINT N'';

GO

PRINT N'HOPT-1491 - OPT Service - Web UI - Unable to update Loyalty settings if loyalty is not set to present...Begin';
PRINT N'Updating Procedure [dbo].[GetLoyaltyTerminal]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyTerminal') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyTerminal
END
GO

CREATE PROCEDURE GetLoyaltyTerminal
  @name VARCHAR(MAX) AS
BEGIN
	SELECT SiteID, TerminalID, Footer1, Footer2, Timeout, ApiKey, HttpHeader
	FROM LoyaltyTerminal T JOIN LoyaltyReference R ON T.LoyaltyRef = R.LoyaltyRef AND R.LoyaltyName = @name
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddLoyaltyReference') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE AddLoyaltyReference
END
GO

CREATE procedure AddLoyaltyReference
 @name varchar(max) as
 begin
  begin try
   begin transaction
    declare @loyaltyRef int
    if not exists (select * from LoyaltyReference where LoyaltyName = @name)
     begin
      if exists (select * from LoyaltyReference)
       begin
        set @loyaltyRef = (select max(LoyaltyRef) from LoyaltyReference) + 1
       end
      else
       begin
        set @loyaltyRef = 1
       end
      insert into LoyaltyReference (LoyaltyRef, LoyaltyName, LoyaltyPresent) values (@loyaltyRef, @name, 0)
      if not exists (select * from LoyaltyTerminal where LoyaltyRef = @loyaltyRef)
       begin
        insert into LoyaltyTerminal (LoyaltyRef) values (@loyaltyRef)
       end
     end
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end

GO


IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyHosts') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyHosts
END
GO

CREATE procedure [dbo].[GetLoyaltyHosts]
 @name varchar(max) as
 begin
  select IPAddress, Port
   from LoyaltyHosts H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyIINs') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyIINs
END
GO

CREATE procedure [dbo].[GetLoyaltyIINs]
 @name varchar(max) as
 begin
  select Low, High
   from LoyaltyIINs I join LoyaltyReference R
    on I.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyMappings') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyMappings
END
GO

CREATE procedure [dbo].[GetLoyaltyMappings]
 @name varchar(max) as
 begin
  select ProductCode, LoyaltyCode
   from LoyaltyMappings M join LoyaltyReference R
    on M.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
 end
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetLoyaltyHostnames') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetLoyaltyHostnames
END
GO

CREATE procedure [dbo].[GetLoyaltyHostnames]
 @name varchar(max) as
 begin
  select Hostname
   from LoyaltyHostnames H join LoyaltyReference R
    on H.LoyaltyRef = R.LoyaltyRef and R.LoyaltyName = @name
 end
GO
PRINT N'HOPT-1491 - OPT Service - Web UI - Unable to update Loyalty settings if loyalty is not set to present...End';
PRINT N'';

GO

PRINT N'HOPT-1554 - OPT Service - Retailix Transaction Number cycling, and Receipts...Begin';
PRINT N'Updating Procedure [dbo].[GetFuelTransactions]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetFuelTransactions') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetFuelTransactions
END
GO

CREATE procedure [dbo].[GetFuelTransactions]
 @startTime DateTime2,
 @endTime DateTime2,
 @maxTransactionNumber bigint as
 begin
  select rowid % @maxTransactionNumber as TransactionId, TransactionTime, FuelCode as GradeCode, WashCode, GradeName,
         WashName, PumpDetails, CardNumber, FuelQuantity, WashQuantity, Amount, FuelCategory,
         WashCategory, FuelSubcategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
         DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration, TxnNumber from Transactions
   where (@startTime is null or TransactionTime >= @startTime) and
         (@endTime is null or TransactionTime <= @endTime) and
         (FuelCode is not null or WashCode is not null)
 end
 GO

PRINT N'HOPT-1554 - OPT Service - Retailix Transaction Number cycling, and Receipts...End';
PRINT N'';

GO

USE [Hydra]

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable...Begin';

IF NOT EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'IsStandardEditable'
          AND Object_ID = Object_ID(N'dbo.ConfigurationCategory'))
BEGIN		 
	PRINT N'Update [dbo].[ConfigurationCategory]...';
	ALTER TABLE [dbo].[ConfigurationCategory] ADD IsStandardEditable BIT NOT NULL DEFAULT 1
END;

PRINT N'Updating Procedure [dbo].[GetConfigurationTypes]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetConfigurationTypes') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetConfigurationTypes
END
GO

CREATE PROCEDURE GetConfigurationTypes AS
BEGIN 
	SELECT [Id], [Type]
	FROM ConfigurationType

	SELECT [Id], [Category], [IsStandardEditable]
	FROM ConfigurationCategory

	SELECT [Id], [TypeId], [Description]
	FROM ConfigurationHeader
END

GO

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable...End';
PRINT N'';

GO