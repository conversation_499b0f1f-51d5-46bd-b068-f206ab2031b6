using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    [Migration(20030007, "HOPT-1993 - config key changes due to SLIB-160 (realign IWorkerable/IConnectable)"), Tags(Constants.MigrationTypeSchema)]
    public class Hopt1993: Migrationable
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.013_SLIB-160-Upgrade_ConfigurationDetail_Install.sql");
        }

        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.013_SLIB-160-Upgrade_ConfigurationDetail_Rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.050_upgrade_app_config_rollback.sql");
        }
    }
}
