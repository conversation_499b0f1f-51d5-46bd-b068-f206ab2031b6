using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System.Diagnostics.CodeAnalysis;
using Xunit;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Tests
{
    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class ThirdPartyPosTests
    {
        private const uint AuthAmount = 10000;

        private readonly IListenerConnectionThread<string> _connectionThread;
        private readonly IThirdPartyPosWorker _thirdPartyPosWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IPumpWorker _hscWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pumpOne;
        private readonly IPump _pumpTwo;
        private readonly IHtecLogger _logger;

        public ThirdPartyPosTests()
        {
            _connectionThread = Substitute.For<IListenerConnectionThread<string>>();
            _hydraDb = Substitute.For<IHydraDb>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _allPumps = Substitute.For<IPumpCollection>();
            _pumpOne = Substitute.For<IPump>();
            _pumpOne.Number.Returns((byte)1);
            _pumpOne.IsAuthorised(out _).ReturnsForAnyArgs(x =>
            {
                x[0] = AuthAmount;
                return true;
            });
            _pumpTwo = Substitute.For<IPump>();
            _pumpTwo.Number.Returns((byte)2);
            _pumpTwo.IsAuthorised(out _).ReturnsForAnyArgs(x =>
            {
                x[0] = AuthAmount;
                return true;
            });
            _allPumps.TryGetPump(Arg.Any<byte>(), out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (byte) x[0] == 1 ? _pumpOne : (byte) x[0] == 2 ? _pumpTwo : null;
                return (byte) x[0] == 1 || (byte) x[0] == 2;
            });
            _logger = Substitute.For<IHtecLogger>();
            _thirdPartyPosWorker = new ThirdPartyPosWorker(_hydraDb, _hscWorker, _allPumps, _logger, _telemetryWorker, _connectionThread);
        }

        [Fact]
        public void test_unknown_command()
        {
            // Arrange
            const int id = 1;
            const string message = ConfigConstants.Unknown;

            // Act
            string result = _thirdPartyPosWorker.OnMessageReceived(new MessageTracking<string>(message), id).Value;


            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public void test_authorise_command()
        {
            // Arrange
            const int id = 1;
            const string message = "Start,Pump01,AUTHORISE,End";

            // Act
            string result = _thirdPartyPosWorker.OnMessageReceived(new MessageTracking<string>(message), id).Value;

            // Assert
            result.Should().BeNull();
            _pumpOne.ReceivedWithAnyArgs(1).ThirdPartyAuth();
            _hscWorker.Received(1).PaymentApproved(_pumpOne.Number, AuthAmount, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_authorise_multiple_commands()
        {
            // Arrange
            const int id = 1;
            const string message = "Start,Pump01,AUTHORISE,End" + "Start,Pump02,AUTHORISE,End";

            // Act
            string result = _thirdPartyPosWorker.OnMessageReceived(new MessageTracking<string>(message), id).Value;

            // Assert
            result.Should().BeNull();
            _pumpOne.ReceivedWithAnyArgs(1).ThirdPartyAuth();
            _pumpTwo.ReceivedWithAnyArgs(1).ThirdPartyAuth();
            _hscWorker.Received(1).PaymentApproved(_pumpOne.Number, AuthAmount, Arg.Any<IMessageTracking<string>>());
            _hscWorker.Received(1).PaymentApproved(_pumpTwo.Number, AuthAmount, Arg.Any<IMessageTracking<string>>());
        }

        [Fact]
        public void test_send_authorise_request()
        {
            // Arrange
            const byte pump = 1;

            // Act
            _thirdPartyPosWorker.SendAuthRequestToPos(pump, AuthAmount);
            _thirdPartyPosWorker.SendAuthRequestToPos(pump, AuthAmount);
            _thirdPartyPosWorker.SendClearRequestToPos(pump);
            _thirdPartyPosWorker.SendClearRequestToPos(pump);
            _thirdPartyPosWorker.SendAuthRequestToPos(pump, AuthAmount);

            // Assert
            _telemetryWorker.Received(3).MessageSentToThirdPartyPos(pump);
        }
    }
}
