using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Pos.Factories
{
    /// <summary>
    /// POS integrator XXX factory, where {TInstance} defines the factory instance type
    /// </summary>
    public abstract class PosIntegratorFactory<TInstance> : Factory<string, TInstance>
    {
        /// <summary>
        /// Resolve type instance delegate
        /// </summary>
        protected readonly Func<string, TInstance> ResolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        protected  PosIntegratorFactory(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, Func<string, TInstance> resolveTypeInstance) :
            base(logManager, $"{loggerName.ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            if (string.IsNullOrWhiteSpace(loggerName))
            {
                throw new ArgumentNullException(nameof(Logger));
            }

            ResolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));
        }     
    }
}
