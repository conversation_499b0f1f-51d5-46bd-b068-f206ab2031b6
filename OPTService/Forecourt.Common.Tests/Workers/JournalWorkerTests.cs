using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO.Ports;
using System.Linq;
using System.Net;
using Xunit;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Tests.Workers
{
    public class JournalWorkerTests
    {
        private const short TillNumber = 99;
        private const short FuelCategory = 97;
        private readonly DateTime _prevDayEndTime = DateTime.Now.AddHours(-12);
        private readonly DateTime _prevShiftEndTime = DateTime.Now.AddHours(-1);
        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _journal;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IHydraTransactionFile _transactionFile;
        private readonly IJournalWorker _journalWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IFromOptWorker _optWorker;
        private readonly ITankGaugeWorker _tankGaugeWorker;
        private readonly IPumpWorker _hscWorker;
        private readonly IRetalixPosWorker _retalixPosWorker;
        
        private readonly IList<string> _journalInfos = new List<string>();
        private readonly ILoggingHelper _loggingHelper;

        public JournalWorkerTests()
        {
            ConfigurationManager.AppSettings["TransactionFileDirectory"] = "";
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.GetSiteInfo().Returns(new SiteInfo(0, string.Empty, string.Empty, false, false, 0, false, TillNumber, FuelCategory, 0));
            _hydraDb.GetPrinterConfig().Returns(new PrinterConfig(false, "COM1", 9200, "None", "One", 8));
            _transactionFile = Substitute.For<IHydraTransactionFile>();
            _loggingHelper = Substitute.For<ILoggingHelper>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _journal = Substitute.For<IHtecLogger>();
            _journal.Info(Arg.Do<string>(x => _journalInfos.Add(x)));
            _logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _logManager.GetLogger(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<bool>(), Arg.Any<ILogFormatter>()).Returns(_logger);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _tankGaugeWorker = Substitute.For<ITankGaugeWorker>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _retalixPosWorker = Substitute.For<IRetalixPosWorker>();

            _journalWorker = new JournalWorker(Substitute.For<IBosIntegratorOut<IMessageTracking>>(), _hydraDb, _telemetryWorker, _journal, _logManager, _loggingHelper,
                Substitute.For<IConfigurationManager>(), Substitute.For<ITimerFactory>(), _hscWorker, _hscWorker, _tankGaugeWorker,
                Substitute.For<IPrinterHelper<IMessageTracking>>(), Substitute.For<ILocalAccountWorker>(), Substitute.For<IOptCollection>(), 
                Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IPosIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IGradeHelper>(),
                Substitute.For<IReceiptHelper>(), Substitute.For<IPumpCollection>(), Substitute.For<IShiftDayEndConfig>(), Substitute.For<IPumpIntegratorConfiguration>());
            _journalWorker.RegisterWorker(_optWorker);
            _journalWorker.RegisterWorker(_tankGaugeWorker);
            _journalWorker.RegisterWorker(_hscWorker);
            _journalWorker.Start();
        }

        [Fact(Skip = "This is a stupid test, as it's not clear what the initial state is within the test")]
        public void test_initial_state()
        {
            // Arrange

            // Act

            // Assert
            _journalWorker.DayEndTime.Should().Be(_prevDayEndTime);
            _journalWorker.ShiftEndTime.Should().Be(_prevShiftEndTime);
            _journalWorker.TillNumber.Should().Be(TillNumber);
            _journalWorker.FuelCategory.Should().Be(FuelCategory);
            _hydraDb.Received(1).FetchShiftStart(false);
            _hydraDb.Received(1).FetchShiftStart(true);
            _hydraDb.Received(1).FetchCardReferences();
            _hydraDb.Received(1).GetSiteInfo();
            _hydraDb.Received(1).GetPrinterConfig();
        }

        [Fact]
        public void test_set_till_number()
        {
            // Arrange
            const short tillNumber = 10;

            // Act
            _journalWorker.SetTillNumber(tillNumber);

            // Assert
            _journalWorker.TillNumber.Should().Be(tillNumber);
            _hydraDb.Received(1).SetTillNumber(tillNumber);
        }

        [Fact]
        public void test_set_fuel_category()
        {
            // Arrange
            const short fuelCategory = 10;

            // Act
            _journalWorker.SetFuelCategory(fuelCategory);

            // Assert
            _journalWorker.FuelCategory.Should().Be(fuelCategory);
            _hydraDb.Received(1).SetFuelCategory(fuelCategory);
        }
        
        [Fact]
        public void test_format_receipt()
        {
            // Arrange
            const string receipt = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "<Receipt>" + "<Line Text=\"Some Text\" />" +
                                   " <Line Left=\"Left Text\" />" + " <Line Right=\"Right Text\" />" +
                                   " <Line Left=\"More Left\" Right=\"More Right\" />" + "</Receipt>";
            string formattedReceipt = $"Some Text{Environment.NewLine}" + $"Left Text{Environment.NewLine}" +
                                      $"Right Text{Environment.NewLine}" + $"More Left More Right{Environment.NewLine}";

            // Act
            string result = _journalWorker.FormatReceipt(receipt);

            // Assert
            result.Should().Be(formattedReceipt);
        }

        [Fact]
        public void test_format_receipt_non_xml()
        {
            // Arrange
            const string receipt = "A Receipt";
            string formattedReceipt = $"Receipt: {receipt}{Environment.NewLine}";

            // Act
            string result = _journalWorker.FormatReceipt(receipt);

            // Assert
            result.Should().Be(formattedReceipt);
        }

        [Fact]
        public void test_receipt_journal()
        {
            // Arrange
            const string receipt = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "<Receipt>" + "<Line Text=\"Some Text\" />" +
                                   " <Line Left=\"Left Text\" />" + " <Line Right=\"Right Text\" />" +
                                   " <Line Left=\"More Left\" Right=\"More Right\" />" + "</Receipt>";
            string formattedReceipt = $"{Environment.NewLine}Some Text{Environment.NewLine}" + $"Left Text{Environment.NewLine}" +
                                      $"Right Text{Environment.NewLine}" + $"More Left More Right{Environment.NewLine}";

            // Act
            _journalWorker.ReceiptJournal(receipt, null);

            // Assert
            _journalInfos.Should().HaveCount(1);
            _journalInfos.Should().Contain(formattedReceipt);
        }

        [Fact]
        public void test_set_card_reference()
        {
            // Arrange
            const string cardName = "Card One";
            const int cardReference = 10;

            // Act
            _journalWorker.SetCardReference(cardName, cardReference);

            // Assert
            _hydraDb.Received(1).SetCardReference(cardName, cardReference);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_clear_card_reference()
        {
            // Arrange
            const string cardName = "Card One";

            // Act
            _journalWorker.ClearCardReference(cardName);

            // Assert
            _hydraDb.Received(1).ClearCardReference(cardName);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_set_acquirer_reference()
        {
            // Arrange
            const string cardName = "Card One";
            const string acquirerName = "Acquirer One";

            // Act
            _journalWorker.SetAcquirerReference(cardName, acquirerName);

            // Assert
            _hydraDb.Received(1).SetAcquirerReference(cardName, acquirerName);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_clear_acquirer_reference()
        {
            // Arrange
            const string cardName = "Card One";

            // Act
            _journalWorker.ClearAcquirerReference(cardName);

            // Assert
            _hydraDb.Received(1).ClearAcquirerReference(cardName);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_set_fuel_card()
        {
            // Arrange
            const string cardName = "Card One";

            // Act
            _journalWorker.SetFuelCard(cardName, true);

            // Assert
            _hydraDb.Received(1).SetFuelCard(cardName, true);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_set_external_name()
        {
            // Arrange
            const string cardName = "Card One";
            const string cardExternalName = "External";

            // Act
            _journalWorker.SetExternalName(cardName, cardExternalName);

            // Assert
            _hydraDb.Received(1).SetExternalName(cardName, cardExternalName);
            _hydraDb.Received(2).FetchCardReferences();
        }

        [Fact]
        public void test_clear_external_name()
        {
            // Arrange
            const string cardName = "Card One";

            // Act
            _journalWorker.ClearExternalName(cardName);

            // Assert
            _hydraDb.Received(1).ClearExternalName(cardName);
            _hydraDb.Received(2).FetchCardReferences();
        }


        [Fact]
        public void test_set_printer_enabled()
        {
            // Arrange

            // Act
            _journalWorker.SetPrinterEnabled(true);

            // Assert
            _hydraDb.Received(1).SetPrinterEnabled(true);
            _journalWorker.PrinterConfig.Enabled.Should().BeTrue();
        }

        [Fact]
        public void test_set_printer_port_name()
        {
            // Arrange
            const string portName = "Port Name";

            // Act
            _journalWorker.SetPrinterPortName(portName);

            // Assert
            _hydraDb.Received(1).SetPrinterPortName(portName);
            _journalWorker.PrinterConfig.PortName.Should().Be(portName);
        }

        [Fact]
        public void test_set_printer_baud_rate()
        {
            // Arrange
            const int baudRate = 4800;

            // Act
            _journalWorker.SetPrinterBaudRate(baudRate);

            // Assert
            _hydraDb.Received(1).SetPrinterBaudRate(baudRate);
            _journalWorker.PrinterConfig.BaudRate.Should().Be(baudRate);
        }

        [Fact]
        public void test_set_printer_handshake()
        {
            // Arrange
            const Handshake handshake = Handshake.RequestToSend;

            // Act
            _journalWorker.SetPrinterHandshake(handshake);

            // Assert
            _hydraDb.Received(1).SetPrinterHandshake(handshake);
            _journalWorker.PrinterConfig.Handshake.Should().Be(handshake);
        }

        [Fact]
        public void test_set_printer_stop_bits()
        {
            // Arrange
            const StopBits stopBits = StopBits.Two;

            // Act
            _journalWorker.SetPrinterStopBits(stopBits);

            // Assert
            _hydraDb.Received(1).SetPrinterStopBits(stopBits);
            _journalWorker.PrinterConfig.StopBits.Should().Be(stopBits);
        }

        [Fact]
        public void test_set_printer_data_bits()
        {
            // Arrange
            const int dataBits = 7;

            // Act
            _journalWorker.SetPrinterDataBits(dataBits);

            // Assert
            _hydraDb.Received(1).SetPrinterDataBits(dataBits);
            _journalWorker.PrinterConfig.DataBits.Should().Be(dataBits);
        }

        #region Write Sales Item

        private TransactionFileItem[] ExecuteWriteSaleItemTest(JournalTotalSalesItem journalTotalSalesItem, JournalLocalAccountItem localAccountItem = null)
        {
            _hydraDb.FetchCardReferences(default)
                .ReturnsForAnyArgs(CreateDefaultCardReferences());

            var salesItems = Array.Empty<TransactionFileItem>();

            _transactionFile.WhenForAnyArgs(x => x.WriteTransactionFile(Arg.Any<TransactionFileItem[]>(), Arg.Any<DateTime>(), Arg.Any<IEnumerable<IPAddress>>(), Arg.Any<IMessageTracking>()))
                .Do(x => salesItems = x.Arg<TransactionFileItem[]>());

            var journalWorker = CreateDefaultJournalWorker();

            journalWorker.WriteSalesItems(journalTotalSalesItem,
                CreateDefaultFuelSales(),
                new List<JournalCarWashSalesItem>(),
                new List<JournalOtherSalesItem>(),
                null,
                localAccountItem,
                "123", out _);

            return salesItems;
        }

        [Theory]
        [InlineData("visa", "5"),
         InlineData("Visa", "5"),
         InlineData("VISA", "5"),
         InlineData("Master Card", "28"),
         InlineData("MASTER CARD", "28")
        ]
        public void write_sales_item_card_product_with_different_case_returns_expected_payment_item(string cardProductName, string expectedCode)
        {
            // Arrange
            var journalTotalSalesItem = new JournalTotalSalesItem { Amount = 500, CardNumber = "***550", CardProductName = cardProductName, Hose = 1, PumpNumber = 1 };

            // Act
            var salesItems = ExecuteWriteSaleItemTest(journalTotalSalesItem);
            
            // Assert
            salesItems.First(x => x.RecType == "16").Code.Should().BeEquivalentTo(expectedCode);
        }

        [Fact]
        public void write_sales_item_card_product_with_no_mileage_or_registration_returns_expected_payment_item()
        {
            var journalTotalSalesItem = CreateDefaultJournalTotalSalesItem();

            // Act
            var salesItems = ExecuteWriteSaleItemTest(journalTotalSalesItem);

            // Assert
            salesItems.Should().NotContain(x => x.RecType == "18");
        }

        public static TheoryData<string, uint?, string> RegistrationTestData => new TheoryData<string, uint?, string>
        {
            { "RT57 GTH", null, "RT57 GTH" },
            { "RT57 GTH", 10000, "RT57 GTH" },
            { null, 10000, string.Empty }
        };

        [Theory]
        [MemberData(nameof(RegistrationTestData), MemberType = typeof(JournalWorkerTests))]
        public void write_sales_item_card_product_with_registration_returns_expected_payment_item(string registration, uint? mileage, string expected)
        {
            var journalTotalSalesItem = CreateDefaultJournalTotalSalesItem();
            journalTotalSalesItem.Registration = registration;
            journalTotalSalesItem.Mileage = mileage;
            
            // Act
            var salesItems = ExecuteWriteSaleItemTest(journalTotalSalesItem);

            // Assert
            salesItems.First(x => x.RecType == "18").Code.Should().BeEquivalentTo(expected);
        }

        public static TheoryData<string, uint?, string> MileageTestData => new TheoryData<string, uint?, string>
        {
            { "RT57 GTH", null, "0" },
            { "RT57 GTH", 10000, "10000" },
            { null, 10000, "10000" }
        };

        [Theory]
        [MemberData(nameof(MileageTestData), MemberType = typeof(JournalWorkerTests))]
        public void write_sales_item_card_product_with_mileage_returns_expected_payment_item(string registration, uint? mileage, string expected)
        {
            var journalTotalSalesItem = CreateDefaultJournalTotalSalesItem();
            journalTotalSalesItem.Registration = registration;
            journalTotalSalesItem.Mileage = mileage;

            // Act
            var salesItems = ExecuteWriteSaleItemTest(journalTotalSalesItem);

            // Assert
            salesItems.First(x => x.RecType == "18").Qty.Should().BeEquivalentTo(expected);
        }

        public static TheoryData<string, uint?, string> MileageTestData1 => new TheoryData<string, uint?, string>
        {
            { "RT57 GTH", null, "0" },
            { "RT57 GTH", 10000, "10000" },
            { null, 10000, "10000" }
        };

        [Theory]
        [MemberData(nameof(MileageTestData1), MemberType = typeof(JournalWorkerTests))]
        public void write_sales_item_card_product_with_mileage_returns_expected_payment_item1(string registration, uint? mileage, string expected)
        {
            var journalTotalSalesItem = CreateDefaultJournalTotalSalesItem();
            var localAccountItem = new JournalLocalAccountItem
            {
                Registration = registration,
                Mileage = mileage ?? 0
            };

            // Act
            var salesItems = ExecuteWriteSaleItemTest(journalTotalSalesItem, localAccountItem);

            // Assert
            salesItems.First(x => x.RecType == "18").Qty.Should().BeEquivalentTo(expected);
        }

        #endregion

        #region Helper Methods
        private static List<CardReference> CreateDefaultCardReferences()
        {
            return new List<CardReference>
            {
                new CardReference(5, "Visa", false, "Visa etc", true, "external"),
                new CardReference(28, "master card", false, "MCard etc", true, "external2")
            };
        }

        private IJournalWorker CreateDefaultJournalWorker()
        {
            return new JournalWorker(Substitute.For<IBosIntegratorOut<IMessageTracking>>(), _hydraDb, _telemetryWorker, _journal, _logManager, _loggingHelper, 
                Substitute.For<IConfigurationManager>(), Substitute.For<ITimerFactory>(), _hscWorker, _hscWorker, _tankGaugeWorker, 
                Substitute.For<IPrinterHelper<IMessageTracking>>(), Substitute.For<ILocalAccountWorker>(), Substitute.For<IOptCollection>(), 
                Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IPosIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IGradeHelper>(), 
                Substitute.For<IReceiptHelper>(), Substitute.For<IPumpCollection>(), Substitute.For<IShiftDayEndConfig>(), Substitute.For<IPumpIntegratorConfiguration>());
        }

        private static JournalTotalSalesItem CreateDefaultJournalTotalSalesItem()
        {
            return new JournalTotalSalesItem
            {
                Amount = 500,
                CardNumber = "***550",
                CardProductName = "VISA",
                Hose = 1,
                PumpNumber = 1,
            };
        }

        private static List<JournalFuelSalesItem> CreateDefaultFuelSales()
        {
            return new List<JournalFuelSalesItem>
            {
                new JournalFuelSalesItem
                {
                    Amount = 500, 
                    Grade = 1, 
                    GradeName = "Unleaded", 
                    Quantity = 500
                }
            };
        }

        #endregion
    }
}
