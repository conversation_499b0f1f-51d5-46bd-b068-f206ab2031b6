using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <summary>
    /// Dummy implementation of the <see cref="ISecAuthIntegratorOut{IMessageTracking}"/>
    /// </summary>
    public class NoneSecAuthOutWorker : SecAuthOutWorker, INoneSecAuthOutWorker
    {
        /// <inheritdoc/>
        public NoneSecAuthOutWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker)
            : base(logManager, nameof(NoneSecAuthOutWorker), configurationManager, allPumps, telemetryWorker)
        {
        }

        /// <inheritdoc/>
        protected NoneSecAuthOutWorker(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IPumpCollection allPumps, ITelemetryWorker telemetryWorker)
            : base(logManager, loggerName, configurationManager, allPumps, telemetryWorker)
        {
        }

        /// <inheritdoc/>
        public override Result PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return DoProcessRequest(request, message, (pump) =>
            {
                var logRef = message.IdAsString;
                pump.SecAuthRequestSent(logRef);
                pump.SetSecAuthResponse(true, logRef);
                return Result.Failure<string>("No further processing needed");
            });
        }

        /// <inheritdoc/>
        public override Result PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Failure($"{FailureReasonPrefix}{HeaderPreAuth}");
        }
    }
}
