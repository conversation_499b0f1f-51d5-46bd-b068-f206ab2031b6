<ng-container *ngIf="showContent">
    <form [formGroup]="fileLocationsForm">
    
        <app-label-text-button id="retalixTransactionFileDirectory" labelText="Retalix transaction file directory"
            placeHolderText="NOT USED" controlName="RetalixTransactionFileDirectory"
            (action)="setRetalixTransactionFileDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3"
            textColClass="col-7" buttonColClass="col-auto" [errorInAction]="errors.RetalixTransactionFileDirectory">
        </app-label-text-button>
    
        <app-label-text-button id="transactionFileDirectory" labelText="Transaction file directory"
            controlName="TransactionFileDirectory" (action)="setTransactionFileDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.TransactionFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="whitelistDirectory" labelText="Whitelist directory" controlName="WhitelistDirectory"
            (action)="setWhitelistDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.WhitelistDirectory"></app-label-text-button>
    
        <app-label-text-button id="layoutDirectory" labelText="Layout directory" controlName="LayoutDirectory"
            (action)="setLayoutDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.LayoutDirectory"></app-label-text-button>
    
        <app-label-text-button id="softwareDirectory" labelText="OPT software directory" controlName="SoftwareDirectory"
            (action)="setSoftwareDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.SoftwareDirectory"></app-label-text-button>
    
        <app-label-text-button id="fuelDataUpdateFile" labelText="Fuel data update file" controlName="FuelDataUpdateFile"
            (action)="setFuelDataUpdateFile()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.FuelDataUpdateFile"></app-label-text-button>
    
        <app-label-text-button id="upgradeFileDirectory" labelText="Upgrade file directory"
            controlName="UpgradeFileDirectory" (action)="setUpgradeFileDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.UpgradeFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="rollbackFileDirectory" labelText="Rollback file directory"
            controlName="RollbackFileDirectory" (action)="setRollbackFileDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.RollbackFileDirectory"></app-label-text-button>
    
        <app-label-text-button *ngIf="fileLocationsData?.MediaChannel === 'true'"
            id="mediaDirectory" labelText="Media directory" controlName="MediaDirectory" (action)="setMediaDirectory()"
            [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.MediaDirectory"></app-label-text-button>
    
        <app-label-text-button *ngIf="fileLocationsData?.MediaChannel === 'true'"
            id="playlistDirectory" labelText="Playlist directory" controlName="PlaylistDirectory"
            (action)="setPlaylistDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.PlaylistDirectory"></app-label-text-button>
    
        <app-label-text-button *ngIf="fileLocationsData?.MediaChannel === 'true'" id="optLogFileDirectory"
            labelText="OPT log file directory" controlName="OptLogFileDirectory" (action)="setOptLogFileDirectory()"
            [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.OptLogFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="logFileDirectory" labelText="Log file directory" controlName="LogFileDirectory"
            (action)="setLogFileDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.LogFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="traceFileDirectory" labelText="Trace file directory" controlName="TraceFileDirectory"
            (action)="setTraceFileDirectory()" [formGroup]="fileLocationsForm" labelColClass="col-3" textColClass="col-7"
            buttonColClass="col-auto" [errorInAction]="errors.TraceFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="journalFileDirectory" labelText="Journal file directory"
            controlName="JournalFileDirectory" (action)="setJournalFileDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.JournalFileDirectory"></app-label-text-button>
    
        <app-label-text-button id="receivedUpdateDirectory" labelText="Received update directory"
            controlName="ReceivedUpdateDirectory" (action)="setReceivedUpdateDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.ReceivedUpdateDirectory"></app-label-text-button>
    
        <app-label-text-button id="databaseBackupDirectory" labelText="Database backup directory"
            controlName="DatabaseBackupDirectory" (action)="setDatabaseBackupDirectory()" [formGroup]="fileLocationsForm"
            labelColClass="col-3" textColClass="col-7" buttonColClass="col-auto"
            [errorInAction]="errors.DatabaseBackupDirectory"></app-label-text-button>
    
    </form>
</ng-container>