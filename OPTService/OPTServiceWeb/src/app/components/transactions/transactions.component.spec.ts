import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { TransactionService } from 'src/app/services/transaction.service';
import { PumpService } from 'src/app/services/pump.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { TransactionsComponent } from './transactions.component';
import { FuelTransactionDetails } from 'src/app/core/models/fuelTransactionDetails.model';
import { OtherEvents } from 'src/app/core/models/otherEvents.model';
import { Pump } from 'src/app/core/models/pump.model';
import { NgbAccordion } from '@ng-bootstrap/ng-bootstrap';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { SecAuthState } from 'src/app/core/enums/secAuthState.enum';

function createFuelTransactionDetails() {
  let fuelTransactionDetailsData = new Array<FuelTransactionDetails>();
  fuelTransactionDetailsData.push({
    Amount: 0,
    CardNumber: "374245*****1006",
    DiscountCardNumber: "",
    DiscountCode: "",
    DiscountName: "",
    DiscountValue: 0,
    FuelCategory: null,
    FuelQuantity: 0,
    FuelSubcategory: null,
    GradeCode: null,
    GradeName: null,
    HasReceipt: false,
    LocalAccountMileage: 0,
    LocalAccountRegistration: "",
    PrinterEnabled: false,
    PumpDetailsString: null,
    TransactionId: 42,
    TransactionTime: "2021-03-02T14:36:50.6",
    TxnNumber: "100283",
    WashCategory: null,
    WashCode: "",
    WashName: "",
    WashQuantity: 0,
    WashSubcategory: null,
    ReceiptError: false,
    ReceiptErrorText: '',
    ReceiptContent: ''
  });
  fuelTransactionDetailsData.push({
    Amount: 0,
    CardNumber: "374245*****1006",
    DiscountCardNumber: "",
    DiscountCode: "",
    DiscountName: "",
    DiscountValue: 0,
    FuelCategory: null,
    FuelQuantity: 0,
    FuelSubcategory: null,
    GradeCode: null,
    GradeName: null,
    HasReceipt: false,
    LocalAccountMileage: 0,
    LocalAccountRegistration: "",
    PrinterEnabled: false,
    PumpDetailsString: "PUMP 1:1",
    TransactionId: 42,
    TransactionTime: "2021-03-02T14:36:50.6",
    TxnNumber: "100283",
    WashCategory: null,
    WashCode: "",
    WashName: "",
    WashQuantity: 0,
    WashSubcategory: null,
    ReceiptError: false,
    ReceiptErrorText: '',
    ReceiptContent: ''
  });
  fuelTransactionDetailsData.push({
    Amount: 0,
    CardNumber: "374245*****1006",
    DiscountCardNumber: "",
    DiscountCode: "",
    DiscountName: "",
    DiscountValue: 0,
    FuelCategory: null,
    FuelQuantity: 0,
    FuelSubcategory: null,
    GradeCode: null,
    GradeName: null,
    HasReceipt: false,
    LocalAccountMileage: 0,
    LocalAccountRegistration: "",
    PrinterEnabled: false,
    PumpDetailsString: "PUMP 10:77777",
    TransactionId: 42,
    TransactionTime: "2021-03-02T14:36:50.6",
    TxnNumber: "100283",
    WashCategory: null,
    WashCode: "",
    WashName: "",
    WashQuantity: 0,
    WashSubcategory: null,
    ReceiptError: false,
    ReceiptErrorText: '',
    ReceiptContent: ''
  });
  fuelTransactionDetailsData.push({
    Amount: 0,
    CardNumber: "374245*****1006",
    DiscountCardNumber: "",
    DiscountCode: "",
    DiscountName: "",
    DiscountValue: 0,
    FuelCategory: null,
    FuelQuantity: 0,
    FuelSubcategory: null,
    GradeCode: null,
    GradeName: null,
    HasReceipt: false,
    LocalAccountMileage: 0,
    LocalAccountRegistration: "",
    PrinterEnabled: false,
    PumpDetailsString: "Pump 1:pppp",
    TransactionId: 42,
    TransactionTime: "2021-03-02T14:36:50.6",
    TxnNumber: "100283",
    WashCategory: null,
    WashCode: "",
    WashName: "",
    WashQuantity: 0,
    WashSubcategory: null,
    ReceiptError: false,
    ReceiptErrorText: '',
    ReceiptContent: ''
  });

  return fuelTransactionDetailsData;
}

describe('TransactionsComponent', () => {
  let component: TransactionsComponent;  
  let transactionServiceSpy: jasmine.SpyObj<TransactionService>;
  let pumpServiceSpy: jasmine.SpyObj<PumpService>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(() => {
    const transactionSpy = jasmine.createSpyObj('TransactionService', ['getFuelTransactions','getOtherEvents','getReceiptURL','printReceipt','saveReceipt']);
    const pumpSpy = jasmine.createSpyObj('PumpService', ['getPumps']);    
    const accordionSpy = jasmine.createSpyObj('NgbAccordion', ['collapseAll']);    
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getTransactionSignalRMessage','getPumpSignalRMessage']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ], 
      providers: [
        TransactionsComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: TransactionService, useValue: transactionSpy },
        { provide: PumpService, useValue: pumpSpy },
        { provide: SignalRService, useValue: signalRSpy },
        { provide: NgbAccordion, useValue: accordionSpy },
        LOADING_SERVICE_PROVIDER(),
      ]
    });
    
    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    transactionServiceSpy = TestBed.inject(TransactionService) as jasmine.SpyObj<TransactionService>;
    pumpServiceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;

    signalRServiceSpy.getTransactionSignalRMessage.and.returnValue(of());
    signalRServiceSpy.getPumpSignalRMessage.and.returnValue(of());

    component = TestBed.inject(TransactionsComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(component).toBeTruthy();
  });

  it('.refreshData() should handle success response from service', ()=>{
    //Arrange
    let fakefuelTransactionDetailsData = new Array<FuelTransactionDetails>();
    fakefuelTransactionDetailsData.push({
      Amount: 0,
      CardNumber: "374245*****1006",
      DiscountCardNumber: "",
      DiscountCode: "",
      DiscountName: "",
      DiscountValue: 0,
      FuelCategory: null,
      FuelQuantity: 0,
      FuelSubcategory: null,
      GradeCode: null,
      GradeName: null,
      HasReceipt: false,
      LocalAccountMileage: 0,
      LocalAccountRegistration: "",
      PrinterEnabled: false,
      PumpDetailsString: null,
      TransactionId: 42,
      TransactionTime: "2021-03-02T14:36:50.6",
      TxnNumber: "100283",
      WashCategory: null,
      WashCode: "",
      WashName: "",
      WashQuantity: 0,
      WashSubcategory: null,
      ReceiptError: false,
      ReceiptErrorText: '',
    ReceiptContent: ''
    });
    let fakeOtherEventsData = new Array<OtherEvents>();
    fakeOtherEventsData.push({TransactionId: 0, TransactionTime: ''});

    let date = new Date();
    let dateStruct = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    let timeStruct = { hour: 0, minute: 0, second: 0 };
    component.datepickerForm.setValue({
      "startDate": dateStruct,
      "endDate": dateStruct,
      "startTime": timeStruct,
      "endTime": timeStruct,
    });

    transactionServiceSpy.getFuelTransactions.and.returnValue(of(fakefuelTransactionDetailsData));
    transactionServiceSpy.getOtherEvents.and.returnValue(of(fakeOtherEventsData));

    //Act
    component.refreshData();

    //Assert
    expect(component.fuelTransactionDetailsData).toEqual(fakefuelTransactionDetailsData);
    expect(component.otherEventsData).toEqual(fakeOtherEventsData);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service call getFuelTransactions', ()=>{
    //Arrange
    let fakeOtherEventsData = [];
    let date = new Date();
    let dateStruct = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    let timeStruct = { hour: 0, minute: 0, second: 0 };
    component.datepickerForm.setValue({
      "startDate": dateStruct,
      "endDate": dateStruct,
      "startTime": timeStruct,
      "endTime": timeStruct,
    });

    transactionServiceSpy.getFuelTransactions.and.returnValue(throwError({status:500}));
    transactionServiceSpy.getOtherEvents.and.returnValue(of(fakeOtherEventsData));
    
    //Act
    component.refreshData();

    //Assert
    expect(component.fuelTransactionDetailsData).toBeDefined();
    expect(component.fuelTransactionDetailsData.length).toBe(0);
    expect(component.otherEventsData).toBeDefined();
    expect(component.otherEventsData.length).toBe(0);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service call getOtherEvents', ()=>{
    //Arrange
    let fakefuelTransactionDetailsData = [];
    let date = new Date();
    let dateStruct = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    let timeStruct = { hour: 0, minute: 0, second: 0 };
    component.datepickerForm.setValue({
      "startDate": dateStruct,
      "endDate": dateStruct,
      "startTime": timeStruct,
      "endTime": timeStruct,
    });

    transactionServiceSpy.getFuelTransactions.and.returnValue(of(fakefuelTransactionDetailsData));
    transactionServiceSpy.getOtherEvents.and.returnValue(throwError({status:500}));
    
    //Act
    component.refreshData();

    //Assert
    expect(component.fuelTransactionDetailsData).toBeDefined();
    expect(component.fuelTransactionDetailsData.length).toBe(0);
    expect(component.otherEventsData).toBeDefined();
    expect(component.otherEventsData.length).toBe(0);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.refreshFilter() should handle success response from service', ()=>{
    //Arrange
    let fakePumps = new Array<Pump>();
    fakePumps.push({
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 1,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    },{
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 10,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    });

    component.activeFilter = 'None';

    pumpServiceSpy.getPumps.and.returnValue(of(fakePumps));

    //Act
    component.refreshFilter();

    //Assert
    expect(component.maxPump).toEqual(10);
    expect(component.activeFilter).toEqual('None');
  });

  it('.refreshFilter() should handle success response from service and maintain activeFilter as Car Wash Only', ()=>{
    //Arrange
    let fakePumps = new Array<Pump>();
    fakePumps.push({
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 5,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    },{
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 1,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    });

    component.activeFilter = 'Car Wash Only';

    pumpServiceSpy.getPumps.and.returnValue(of(fakePumps));

    //Act
    component.refreshFilter();

    //Assert
    expect(component.maxPump).toEqual(5);
    expect(component.activeFilter).toEqual('Car Wash Only');
  });

  it('.refreshFilter() should handle success response from service and change activeFilter to None', ()=>{
    //Arrange
    let fakePumps = new Array<Pump>();
    fakePumps.push({
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 1,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    },{
      HasSecAuthRequestTimedOut: false,
      SecAuthState: SecAuthState.Idle,
      ClosePending: false,
      Closed: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: false,
      Delivered: false,
      Delivering: false,
      HasPayment: false,
      InUse: false,
      KioskOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false,
      NozzleUp: false,
      Number: 2,
      OptStringId: "KCIB005F",
      OutsideOnly: false,
      PodMode: false,
      ThirdPartyPending: false,
      Tid: "99979931"
    });

    component.activeFilter = 'Pump 10';

    pumpServiceSpy.getPumps.and.returnValue(of(fakePumps));

    //Act
    component.refreshFilter();

    //Assert
    expect(component.maxPump).toEqual(2);
    expect(component.activeFilter).toEqual('None');
  });

  it('.getDate() should have expected format', ()=>{
    //Arrange
    let nbDate = { day: 23, month: 3, year: 2021};
    let expectedDate = new Date('Tue Mar 23 2021 00:00:00 GMT+0000 (Greenwich Mean Time)');

    //Act
    let dateResult = component.getDate(nbDate);

    //Assert
    expect(dateResult).toEqual(expectedDate);
  });

  it('.notEmptyField() should check empty string', ()=>{
    //Arrange

    //Act
    let result = component.notEmptyField("");

    //Assert
    expect(result).toEqual(false);
  });

  it('.notEmptyField() should check empty string', ()=>{
    //Arrange

    //Act
    let result = component.notEmptyField(null);

    //Assert
    expect(result).toEqual(false);
  });

  it('.notEmptyField() should check string', ()=>{
    //Arrange

    //Act
    let result = component.notEmptyField("not empty");

    //Assert
    expect(result).toEqual(true);
  });

  it('.getFilteredTransactionData() should apply filter None', ()=>{
    //Arrange
    component.fuelTransactionDetailsData = createFuelTransactionDetails();
    component.activeFilter = 'None';

    //Act
    component.getFilteredTransactionData();

    //Assert
    expect(component.filteredFuelTransactionDetailsData).toEqual(component.fuelTransactionDetailsData);
  });

  it('.getFilteredTransactionData() should apply filter Car Wash Only', ()=>{
    //Arrange
    component.fuelTransactionDetailsData = createFuelTransactionDetails();
    component.activeFilter = 'Car Wash Only';
    let expectedTransactions = component.fuelTransactionDetailsData.filter((element) => {
      return element.PumpDetailsString === null;
    });    

    //Act
    component.getFilteredTransactionData();

    //Assert
    expect(component.filteredFuelTransactionDetailsData).toEqual(expectedTransactions);
  });

  it('.getFilteredTransactionData() should apply filter Pump 1', ()=>{
    //Arrange
    component.fuelTransactionDetailsData = createFuelTransactionDetails();
    component.activeFilter = 'Pump 1';
    let expectedTransactions = component.fuelTransactionDetailsData.filter((element) => {
      return element.PumpDetailsString === 'PUMP 1:1';
    });

    //Act
    component.getFilteredTransactionData();

    //Assert
    expect(component.filteredFuelTransactionDetailsData).toEqual(expectedTransactions);
  });

  it('.hideReceipt() should change showReceipt value to false', ()=>{
    //Arrange
    component.showReceipt = true;

    //Act
    component.hideReceipt();

    //Assert
    expect(component.showReceipt ).toEqual(false);
  });

  it('.hideReceipt() should not change showReceipt value', ()=>{
    //Arrange
    component.showReceipt = false;

    //Act
    component.hideReceipt();

    //Assert
    expect(component.showReceipt ).toEqual(false);
  });

  it('.formatDatetime() should format AM date correctly', ()=>{
    //Arrange
    let date = new Date('Tue Mar 23 2021 00:00:00 GMT+0000 (Greenwich Mean Time)');

    //Act
    let result = component.formatDatetime(date);

    //Assert
    expect(result).toEqual('23/03/2021, 00:00:00');
  });

  it('.formatDatetime() should format PM date correctly', ()=>{
    //Arrange
    let date = new Date('Tue Mar 23 2021 12:00:00 GMT+0000 (Greenwich Mean Time)');

    //Act
    let result = component.formatDatetime(date);

    //Assert
    expect(result).toEqual('23/03/2021, 12:00:00');
  });
  
  it('.ngOnInit() should initialize correctly', ()=>{
    //Arrange
    let fakefuelTransactionDetailsData = new Array<FuelTransactionDetails>();
    let fakeOtherEventsData = new Array<OtherEvents>();
    let fakePumps = new Array<Pump>();

    let date = new Date();
    let dateStruct = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    let timeStruct = { hour: 0, minute: 0, second: 0 };
    component.datepickerForm.setValue({
      "startDate": dateStruct,
      "endDate": dateStruct,
      "startTime": timeStruct,
      "endTime": timeStruct,
    });

    transactionServiceSpy.getFuelTransactions.and.returnValue(of(fakefuelTransactionDetailsData));
    transactionServiceSpy.getOtherEvents.and.returnValue(of(fakeOtherEventsData));
    pumpServiceSpy.getPumps.and.returnValue(of(fakePumps));

    //Act
    component.ngOnInit();
    component.refreshData();

    //Assert
    expect(transactionServiceSpy.getFuelTransactions).toHaveBeenCalledTimes(1);
    expect(transactionServiceSpy.getOtherEvents).toHaveBeenCalledTimes(1);
    expect(component.fuelTransactionDetailsData).toBeDefined();
    expect(component.fuelTransactionDetailsData.length).toBe(0);
    expect(component.otherEventsData).toBeDefined();
    expect(component.otherEventsData.length).toBe(0);
    expect(component.filteredFuelTransactionDetailsData).toBeDefined();
    expect(component.filteredFuelTransactionDetailsData.length).toBe(0);
  });

  it('.printReceipt() should handle success response', ()=>{
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    fuelTransactionDetails.ReceiptError = true;
    let fakefuelTransactionDetailsData = new Array<FuelTransactionDetails>();

    transactionServiceSpy.printReceipt.and.returnValue(of(fakefuelTransactionDetailsData));

    //Act
    component.printReceipt(fuelTransactionDetails);

    //Assert
    expect(transactionServiceSpy.printReceipt).toHaveBeenCalledTimes(1);
    expect(fuelTransactionDetails.ReceiptError).toEqual(false);
  });

  it('.printReceipt() should handle error response', ()=>{
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    fuelTransactionDetails.ReceiptError = false;

    transactionServiceSpy.printReceipt.and.returnValue(throwError({status:500}));

    //Act
    component.printReceipt(fuelTransactionDetails);

    //Assert
    expect(transactionServiceSpy.printReceipt).toHaveBeenCalledTimes(1);
    expect(fuelTransactionDetails.ReceiptError).toEqual(true);
    expect(fuelTransactionDetails.ReceiptErrorText).toEqual("Failed to print receipt");
  });

  it('.saveReceipt() should handle success response', ()=>{
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    fuelTransactionDetails.ReceiptError = true;
    let fakefuelTransactionDetailsData = new Array<FuelTransactionDetails>();

    transactionServiceSpy.saveReceipt.and.returnValue(of({}));

    //Act
    component.saveReceipt(fuelTransactionDetails);

    //Assert
    expect(transactionServiceSpy.saveReceipt).toHaveBeenCalledTimes(1);
    expect(fuelTransactionDetails.ReceiptError).toEqual(false);
  });

  it('.saveReceipt() should handle error response', ()=>{
    //Arrange
    let fuelTransactionDetails = new FuelTransactionDetails();
    fuelTransactionDetails.ReceiptError = false;

    transactionServiceSpy.saveReceipt.and.returnValue(throwError({status:500}));

    //Act
    component.saveReceipt(fuelTransactionDetails);

    //Assert
    expect(transactionServiceSpy.saveReceipt).toHaveBeenCalledTimes(1);
    expect(fuelTransactionDetails.ReceiptError).toEqual(true);
    expect(fuelTransactionDetails.ReceiptErrorText).toEqual("Failed to save receipt");
  });

  it('.setFilter() should change the active filter', ()=>{
    //Arrange
    component.fuelTransactionDetailsData = createFuelTransactionDetails();
    component.activeFilter = 'something';

    //Act
    component.setFilter("None");

    //Assert
    expect(component.activeFilter).toEqual("None");
    expect(component.filteredFuelTransactionDetailsData).toEqual(component.fuelTransactionDetailsData);
  });

  it('.configureFilter() should create the default filters', ()=>{
    //Arrange
    let expectedFilters = [ "None", "Pump 1", "Car Wash Only" ];

    //Act
    component.configureFilter();

    //Assert
    expect(component.filters).toEqual(expectedFilters);
  });

  it('.configureFilter() should create the additional pump filters based on maxPumps', ()=>{
    //Arrange
    let expectedFilters = [ "None", "Pump 1", "Pump 2", "Car Wash Only" ];
    component.maxPump = 2;

    //Act
    component.configureFilter();

    //Assert
    expect(component.filters).toEqual(expectedFilters);
  });

});

