using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;

namespace Forecourt.Core.Workers
{
    /// <summary>
    /// Extends <see cref="Htec.Foundation.Connections.Workers.ListenerWorker{T}"/>, by adding common support for:
    /// core <see cref="IHydraDb"/> related configuration, 
    /// majority of service endpoints <see cref="OptEndPoints"/>
    /// </summary>
    /// <typeparam name="T">Message type supported by this worker</typeparam>
    /// <typeparam name="THydraDb"></typeparam>
    /// <typeparam name="TTelemetryWorker"></typeparam>
    [HasConfiguration]
    public abstract class ListenerWorker<T, THydraDb, TTelemetryWorker> : Htec.Foundation.Connections.Workers.ListenerWorker<T>
        where THydraDb : IHydraDb
        where TTelemetryWorker : ITelemetryWorker
    {
        protected THydraDb HydraDb { get; }

        protected OptEndPoints OldEndPoints { get; private set; }

        protected OptEndPoints CurrentEndPoints { get; private set; }
        
        protected string MyIdString { get; set; } = OptEndPoints.DefaultHydraId;

        /// <summary>
        /// Worker used to send notifications
        /// </summary>
        protected INotificationWorker<EventType> NotificationWorker => GetWorker<INotificationWorker<EventType>>();

        protected ListenerWorker(IHtecLogger logger, TTelemetryWorker telemetryWorker,
            IListenerConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null,
            string defaultTimerInterval = null) : base(logger, connectionThread, telemetryWorker, configurationManager, timerFactory, defaultTimerInterval)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        protected ListenerWorker(IHtecLogManager logMan, string loggerName, TTelemetryWorker telemetryWorker,
            IListenerConnectionThread<T> connectionThread, THydraDb hydraDb, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null,
            string defaultTimerInterval = null) : base(logMan, loggerName, connectionThread, telemetryWorker, configurationManager, timerFactory, defaultTimerInterval)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        /// <summary>
        /// Extracts the OptEndPoints, and assigns
        /// </summary>
        /// <returns></returns>
        protected OptEndPoints GetEndPoints()
        {
            var optEndPoints = HydraDb.FetchEndPoints(Name);

            if (optEndPoints == null)
            {
                return null;
            }

            // Deal with Start
            if (CurrentEndPoints == null)
            {
                if (optEndPoints.ValidPorts)
                {
                    OldEndPoints = CurrentEndPoints;
                    CurrentEndPoints = optEndPoints;
                    MyIdString = optEndPoints.HydraId;
                    return optEndPoints;
                }

                return null;
            }

            // Deal with Reload
            if (optEndPoints.ValidChange(CurrentEndPoints, true, Logger))
            {
                OldEndPoints = CurrentEndPoints;
                CurrentEndPoints = optEndPoints;
                MyIdString = optEndPoints.HydraId;

                return optEndPoints;
            }

            if (optEndPoints.ValidChange(CurrentEndPoints, false, Logger))
            {
                OldEndPoints = CurrentEndPoints;
                CurrentEndPoints = optEndPoints;
                MyIdString = optEndPoints.HydraId;
            }

            return CurrentEndPoints;
        }
    }
}
