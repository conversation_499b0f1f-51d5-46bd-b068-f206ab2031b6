using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Logger.Interfaces;
using ConnectionsConstants = Htec.Foundation.Connections.Common.Constants;

namespace OPT.Common.ConnectionThreads
{
    [HasConfiguration]
    public class FromOptConnectionThread : XmlListenerConnectionThread
    {
        public FromOptConnectionThread(IHtecLogManager logMan, ISocketWrapperFactory socketWrapperFactory, IConfigurationManager configurationManager,
            byte lengthSize = ConnectionsConstants.DefaultLengthSize, bool logRxTx = true, MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.Both) : base(
            logMan, "FromOPT", socketWrapperFactory, configurationManager, lengthSize, logRxTx, messageLengthFlow)
        {
        }
    }
}
