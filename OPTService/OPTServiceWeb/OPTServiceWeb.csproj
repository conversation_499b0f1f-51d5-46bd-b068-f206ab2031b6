<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="node_modules\**" />
    <EmbeddedResource Remove="node_modules\**" />
    <None Remove="node_modules\**" />
  </ItemGroup>

  <ItemGroup Condition="Exists('dist\')">
    <EmbeddedResource Include="dist\OPTWebAdmin\assets\img\*.png" />
    <EmbeddedResource Include="dist\OPTWebAdmin\favicon.ico" />
    <EmbeddedResource Include="dist\OPTWebAdmin\index.html" />
    <EmbeddedResource Include="dist\OPTWebAdmin\*.js" />
    <EmbeddedResource Include="dist\OPTWebAdmin\*.map" />
    <EmbeddedResource Include="dist\OPTWebAdmin\*.css" />
    <EmbeddedResource Include="dist\OPTWebAdmin\*.woff" />
    <EmbeddedResource Include="dist\OPTWebAdmin\*.woff2" />
  </ItemGroup>
  <Target Name="PreBuild" BeforeTargets="ReBuild">
    <Exec Command="powershell.exe -ExecutionPolicy Unrestricted $(ProjectDir)build.ps1 $(ConfigurationName)" />
  </Target>

</Project>
