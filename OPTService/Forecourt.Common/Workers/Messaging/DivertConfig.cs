using System.Net;

namespace OPT.Common.Workers.Messaging
{
    /// <summary>
    /// Configuration object for diverted communications.
    /// </summary>
    public class DivertConfig
    {
        /// <summary>
        /// Id of redirected service instance.
        /// </summary>
        public string RedirectHydraId { get; set; }

        /// <summary>
        /// Is the OPT diverted to another service instance?
        /// </summary>
        public bool IsOptServiceDiverted { get; set; }

        /// <summary>
        /// Address of service if diverted.
        /// </summary>
        public IPAddress DivertedServiceAddress { get; set; }
        
        /// <summary>
        /// From OPT port if diverted.
        /// </summary>
        public int DivertedFromOptPort { get; set; }

        /// <summary>
        /// To OPT port if diverted.
        /// </summary>
        public int DivertedToOptPort { get; set; }

        /// <summary>
        /// Heartbeat port if diverted.
        /// </summary>
        public int DivertedHeartbeatPort { get; set; }
    }
}