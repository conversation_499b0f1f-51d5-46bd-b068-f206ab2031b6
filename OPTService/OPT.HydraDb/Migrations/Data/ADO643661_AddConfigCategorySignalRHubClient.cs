using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to add SIGNALR-HUBCLIENT Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20050013, "ADO#643661 - add SIGNALR-HUBCLIENT Configuraion Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO643661_AddConfigCategorySignalRHubClient : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{Htec.HubClient.CommonConstants.CategoryNameSignalRSignalRHubClient}', @description = 'All SIGNALR-HUBCLIENT related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{Htec.HubClient.CommonConstants.CategoryNameSignalRSignalRHubClient}'");
        }
    }
}
