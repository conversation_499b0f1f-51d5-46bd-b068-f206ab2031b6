using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Service.OPTServiceClasses;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using OPTService;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Description;
using Unity;

namespace Forecourt.Service.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all POS requests
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "WebUi,Swagger,ThirdParty,Orbis")]
    [RoutePrefix("api/pos")]
    public class PosController : LinkedLoggableApiController<IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>>
    {
        /// <inheritdoc/>
        public PosController([Dependency(UnityConfig.MessageBrokerName)] IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>> loggable, IConfigurationManager configurationManager = null) : base(loggable as Loggable, configurationManager)
        {
        }

        /// <summary>
        /// Request Receipt information
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="loggindReference">Logging reference</param>
        /// <param name="request">Additional receipt information</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("pumps/{pump:range(1,256)/receipts"), ResponseType(typeof(IEnumerable<ReceiptInfo>))]
        public async Task<IHttpActionResult> RequestReceipt([FromUri] byte pump, [FromBody] GetReceiptDetails request, [FromUri] string loggindReference = null)
        {
            return await DoHttpActionAsync<IEnumerable<ReceiptInfo>>(async () =>
                Loggable.RequestReceipt(new GetReceiptRequest { Pump = pump, CardNumber = request.CardNumber, TransactionNumber = request.TransactionId }, loggindReference.ToMessageTracking())).ConfigureAwait(false);
        }

        /// <summary>
        /// Request Status information
        /// </summary>
        /// <param name="pump">Pump number, or fuelling point id</param>
        /// <param name="loggindReference">Logging reference</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpGet, Route("pumps/{pump:range(1,256)/status"), ResponseType(typeof(StatusResponse))]
        public async Task<IHttpActionResult> RequestStatus([FromUri] byte pump, [FromUri] string loggindReference = null)
        {
            return await DoHttpActionAsync<StatusResponse>(async () => Loggable.RequestStatus(pump, loggindReference.ToMessageTracking())).ConfigureAwait(false);
        }
    }
}
