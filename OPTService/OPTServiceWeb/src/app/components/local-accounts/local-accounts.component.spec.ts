import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { LocalAccountService } from 'src/app/services/local-account.service';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { LocalAccountsComponent } from './local-accounts.component';

describe('LocalAccountsComponent', () => {
  let component: LocalAccountsComponent;
  let ngxLoggerSpy: jasmine.SpyObj<NGXLogger>;
  let localAccountyServiceSpy: jasmine.SpyObj<LocalAccountService>;

  let mockAccounts = {
    "LocalAccountEnabled": true,
    "LocalAccountCustomers": [
      {
          "CustomerReference": "101",
          "Name": "Mr <PERSON>",
          "Cards": [
              {
                  "Pan": "**********",
                  "Description": "P",
                  "Discount": 0,
                  "NoRestrictions": false,
                  "Unleaded": true,
                  "Diesel": true,
                  "Lpg": true,
                  "Lrp": false,
                  "GasOil": false,
                  "AdBlue": false,
                  "Kerosene": false,
                  "Oil": true,
                  "Avgas": false,
                  "Jet": false,
                  "Mogas": false,
                  "Valeting": false,
                  "OtherMotorRelatedGoods": false,
                  "ShopGoods": true,
                  "Hot": false,
                  "CustomerReference": "108",
                  "Restrictions1": 7,
                  "Restrictions2": 65
              }
          ],
          "TransactionsAllowed": true,
          "TransactionLimit": 10000,
          "Pin": false,
          "PrintValue": false,
          "AllowLoyalty": false,
          "FuelOnly": false,
          "RegistrationEntry": false,
          "MileageEntry": false,
          "PrePayAccount": false,
          "LowCreditWarning": false,
          "MaxCreditReached": false,
          "Balance": 50000
      }
    ]
  };

  beforeEach(() => {
    const ngxLoggerObjSpy = jasmine.createSpyObj('NGXLogger', ['info','debug','error']);
    const localAccountyServiceObjSpy = jasmine.createSpyObj('LocalAccountService', ['getLocalAccounts', 
      'addLocalAccountCustomer', 'removeLocalAccountCustomer', 'setLocalAccountCustomerBalance']);
    const formBuilder: FormBuilder = new FormBuilder();

    TestBed.configureTestingModule({
      imports: [
        NgbModule,
      ],
      providers: [
        LocalAccountsComponent,
        { provide: NGXLogger, useValue: ngxLoggerObjSpy },
        SIGNAL_R_SERVICE_PROVIDER(),
        { provide: LocalAccountService, useValue: localAccountyServiceObjSpy },
        { provide: FormBuilder, useValue: formBuilder },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    ngxLoggerSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    SIGNAL_R_SERVICE_SPY().getLocalAccountsSignalRMessage.and.returnValue(of());
    localAccountyServiceSpy = TestBed.inject(LocalAccountService) as jasmine.SpyObj<LocalAccountService>;
    localAccountyServiceSpy.getLocalAccounts.and.returnValue(of(mockAccounts));
    component = TestBed.inject(LocalAccountsComponent);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.getCreditStatusText() returns Pre pay account', ()=>{
 
    let result = component.getCreditStatusText(true, false, false);

    expect(result).toBe('Pre pay account');
  });

  it('.getCreditStatusText() returns Low credit warning', ()=>{

    let result = component.getCreditStatusText(false, true, false);

    expect(result).toBe('Low credit warning');
  });

  it('.getCreditStatusText() returns Max credit reached', ()=>{

    let result = component.getCreditStatusText(false, false, true);

    expect(result).toBe('Max credit reached');
  });

  it('.getCreditStatusText() returnes None', ()=>{

    let result = component.getCreditStatusText(false, false, false);

    expect(result).toBe('None');
  });

  it('.refreshData() should handle success response from service', ()=>{
    localAccountyServiceSpy.getLocalAccounts.and.returnValue(of(mockAccounts));

    component.refreshData();

    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(3);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(0);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle failure response from service', ()=>{
    localAccountyServiceSpy.getLocalAccounts.and.returnValue(throwError({status:500}));

    component.refreshData();

    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(0);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });
});

