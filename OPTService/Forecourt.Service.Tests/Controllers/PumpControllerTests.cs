using FluentAssertions;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using NSubstitute;
using OPT.Common.Workers.Interfaces;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http.Results;
using Xunit;

namespace OPTServiceUnitTests.Controllers
{
    public class PumpControllerTests
    {
        private readonly IWeb _web;
        private readonly IHtecLogger _logger;

        public PumpControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _web = Substitute.For<IWeb, Loggable>(_logger, null, null);
        }

        [Fact]
        public async Task test_GetPumpInfo_returns_BadRequest_result()
        {
            // Arrange
            var pumpController = CreateDefaultPumpController();
            var pumpNumber = -1;

            _web.GetPumps(Arg.Any<int>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<PumpDetails>>.Specific(HttpStatusCode.BadRequest, null, new ArgumentException("Invalid Pump number")));

            // Act
            var actual = await pumpController.GetPumpInfo(pumpNumber);

            // Assert
            var result = actual as ResponseMessageResult;
            result.Should().NotBeNull();
            result?.Response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task test_GetPumpInfo_returns_NotFound_result()
        {
            // Arrange
            var pumpController = CreateDefaultPumpController();
            var pumpNumber = 1;
            
            _web.GetPumps(Arg.Any<int>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<PumpDetails>>.Specific(HttpStatusCode.NotFound));

            // Act
            var actual = await pumpController.GetPumpInfo(pumpNumber);

            // Assert
            var result = actual as ResponseMessageResult;
            result.Should().NotBeNull();
            result?.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task test_GetPumpInfo_returns_OK_result_single_item()
        {
            // Arrange
            var pumpController = CreateDefaultPumpController();
            const int number = 1;
            const string optStringId = "33333";
            var webResult = new List<PumpDetails>
            {
                CreatePumpDetails(number, optStringId)
            };

            _web.GetPumps(Arg.Any<int>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<PumpDetails>>.Success(webResult));

            // Act
            var actual = await pumpController.GetPumpInfo(1);

            // Assert
            var result = actual as ResponseMessageResult;
            result.Should().NotBeNull();
            result?.Response.StatusCode.Should().Be(HttpStatusCode.OK);
            
        }

        [Fact]
        public async Task test_GetPumpInfo_returns_OK_result_multiple_items()
        {
            // Arrange
            var pumpController = CreateDefaultPumpController();
            var webResult = new List<PumpDetails>
            {
                CreatePumpDetails(1, "33333"),
                CreatePumpDetails(2, "44444"),
            };
            
            _web.GetPumps(Arg.Any<int>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<PumpDetails>>.Success(webResult));

            // Act
            var actual = await pumpController.GetPumpInfo();

            // Assert
            var result = actual as ResponseMessageResult;
            result.Should().NotBeNull();
            result?.Response.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        [Fact]
        public async Task test_GetPumpInfo_returns_correct_values_multiple_items()
        {
            // Arrange
            var pumpController = CreateDefaultPumpController();
            var webResult = new List<PumpDetails>
            {
                CreatePumpDetails(1, "33333"),
                CreatePumpDetails(2, "44444"),
            };

            _web.GetPumps(Arg.Any<int>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<PumpDetails>>.Success(webResult));

            // Act
            var actual = await pumpController.GetPumpInfo();

            // Assert
            var result = actual as ResponseMessageResult;
            var content = await result?.Response.Content.ReadAsStringAsync();
            content.Should().Be(JsonConvert.SerializeObject(webResult));
        }

        #region Helper Methods

        private PumpController CreateDefaultPumpController()
        {
            return new PumpController(_web, _logger, Substitute.For<IMessageBroker>(), Substitute.For<IPumpWorker>(),
                Substitute.For<Forecourt.Common.HydraDbClasses.IHydraDb>(), Substitute.For<IPumpCollection>(), Substitute.For<IFromOptWorker>());
        }

        private static PumpDetails CreatePumpDetails(byte number, string optStringId)
        {
            return new PumpDetails
            {
                Number = number,
                Tid = null,
                Closed = false,
                ClosePending = false,
                OptStringId = optStringId,
                InUse = false,
                NozzleUp = false,
                HasPayment = false,
                SecAuthState = $"{SecAuthState.Idle}",
                HasSecAuthRequestTimedOut = false,
                Delivering = false,
                Delivered = false,
                ThirdPartyPending = false,
                PodMode = false,
                KioskOnly = true,
                OutsideOnly = false,
                DefaultKioskOnly = true,
                DefaultOutsideOnly = false,
                MaxFillOverrideForFuelCards = false,
                MaxFillOverrideForPaymentCards = false
            };
        }

        #endregion
    }
}
