using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class LocalAccountCustomerDetails
    {
        public string CustomerReference { get; set; }
        public string Name { get; set; }
        public IList<LocalAccountCardDetails> Cards { get; set; }
        public bool TransactionsAllowed { get; set; }
        public uint TransactionLimit { get; set; }
        public bool Pin { get; set; }
        public bool PrintValue { get; set; }
        public bool AllowLoyalty { get; set; }
        public bool FuelOnly { get; set; }
        public bool RegistrationEntry { get; set; }
        public bool MileageEntry { get; set; }
        public bool PrePayAccount { get; set; }
        public bool LowCreditWarning { get; set; }
        public bool MaxCreditReached { get; set; }
        public decimal Balance { get; set; }
    }
}