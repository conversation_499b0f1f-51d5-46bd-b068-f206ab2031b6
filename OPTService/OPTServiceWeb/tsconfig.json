/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": false,
    "strict": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "noImplicitUseStrict": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "target": "es2015",
    "module": "es2020",
    "lib": [
      "es2018",
      "dom"
    ],
    "suppressImplicitAnyIndexErrors": true,
  },
  "angularCompilerOptions": {
    "strictInjectionParameters": false,
    "strictInputAccessModifiers": false,
    "strictPropertyInitialization": false,
    "strictTemplates": false
  }
}
