using System;

namespace Forecourt.Pump.Workers.Models
{
    /// <summary>
    /// Class to hold all Pump Meter related information
    /// </summary>
    public class PumpMeterInfo
    {
        /// <summary>
        /// Pump number
        /// </summary>
        public byte Pump { get; set; }     

        /// <summary>
        /// Are meter readings pending
        /// </summary>
        public bool AreReadingsPending { get; set; }

        /// <summary>
        /// Current attempt at retrieving Meters information
        /// </summary>
        public int RequestedAttempts { get; set; }

        /// <summary>
        /// Timeout for retrieving Meters information
        /// </summary>
        public DateTime RequestExpiry { get; set; }

        /// <summary>
        /// Main constructor
        /// </summary>
        public PumpMeterInfo(byte pump, bool areReadingsPending = true, int requestedAttempts = 0, DateTime? requestExpiry = null)
        {
            Pump = pump;
            AreReadingsPending = areReadingsPending;
            RequestedAttempts = requestedAttempts;
            RequestExpiry = requestExpiry ?? DateTime.MaxValue;
        }
    }
}
