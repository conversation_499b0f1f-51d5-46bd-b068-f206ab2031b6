assembly-file-versioning-scheme: MajorMinorPatchTag
assembly-versioning-format: '{Major}.{Minor}.{Patch}.{env:CI_PIPELINE_ID ?? 0}'
assembly-informational-format: '{NuGetVersionV2}+Build.{env:CI_PIPELINE_ID ?? 0}.Date.{CommitDate}.Branch.{env:CI_COMMIT_BRANCH ?? MR}.Sha.{Sha}'
assembly-file-versioning-format: '{MajorMinorPatch}.{CommitsSinceVersionSource}'
commit-date-format: yyyyMMddTHHmmss
continuous-delivery-fallback-tag: cicd
increment: Patch
mode: ContinuousDelivery
branches:
  master:
    regex: ^master$|^main$
    tag: ''
    prevent-increment-of-merged-branch-version: true
  release:
    regex: ^release?[/-]
    tag: ''
    prevent-increment-of-merged-branch-version: true
    is-release-branch: true
    pre-release-weight: 1000
  feature:
    regex: ^feature?[/-]
    tag: 'feat-{BranchName}'
    mode: ContinuousDeployment
  merge-request:
    regex: ^(merge|merge\-requests|mr)[/-]
    tag: mr
    increment: Inherit
    tag-number-pattern: '[/-](?<number>\d+)[-/]'
    is-mainline: false
    source-branches: ['feature','develop','release','hotfix','support']
  hotfix:
    regex: ^hotfix(es)?[/-]
    tag: ''
  support:
    regex: ^support[/-]
    tag: ''
    prevent-increment-of-merged-branch-version: true
  develop:
    regex: ^dev(elop)?(ment)?$
    mode: ContinuousDeployment
    tag: alpha
    increment: Minor
    track-merge-target: true
    tracks-release-branches: true
    prevent-increment-of-merged-branch-version: true
ignore:
  sha: []
merge-message-formats: {}
