USE [Hydra]

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable...Begin';

IF NOT EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'IsStandardEditable'
          AND Object_ID = Object_ID(N'dbo.ConfigurationCategory'))
BEGIN		 
	PRINT N'Update [dbo].[ConfigurationCategory]...';
	ALTER TABLE [dbo].[ConfigurationCategory] ADD IsStandardEditable BIT NOT NULL DEFAULT 1
END;

PRINT N'Updating Procedure [dbo].[GetConfigurationTypes]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetConfigurationTypes') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetConfigurationTypes
END
GO

CREATE PROCEDURE GetConfigurationTypes AS
BEGIN 
	SELECT [Id], [Type]
	FROM ConfigurationType

	SELECT [Id], [Category], [IsStandardEditable]
	FROM ConfigurationCategory

	SELECT [Id], [TypeId], [Description]
	FROM ConfigurationHeader
END

GO

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable...End';
PRINT N'';

GO