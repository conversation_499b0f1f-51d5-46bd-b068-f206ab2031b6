using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(26200005, "ADO#694428 - Update all related stored procedures, due to new dbo.PumpDelivered.[Hose]"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO694428UpdatePumpDelivered : Migrationable
    {
        /// <inheritdoc/>
        public ADO694428UpdatePumpDelivered(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetPumpDelivered", "dbo.SetPumpDelivered" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO657918_UpdatePumpDelivered), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }         
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO657918_UpdatePumpDelivered), DbObjects.Reverse().ToArray());
        }
    }
}
