using System;
using System.Collections.Generic;
using System.Linq;

namespace OPT.Common.MediaChannelClasses
{
    [Obsolete("Use Htec.Foundation.Connection.Models.MediaMessage versions now!")]
    public class MediaChannelMessageComponent
    {
        private const int TagSize = 2;
        private const int LengthSize = 4;
        
        public const byte TagByte = 0xFF;

        public const int TagAndLength = TagSize + LengthSize;

        public MediaChannelMessageComponentType Type { get; }

        public int Length { get; }

        public int FullLength => Length + TagAndLength;

        public byte[] Bytes { get; }

        public MediaChannelMessageComponent(MediaChannelMessageComponentType type, byte[] bytes)
        {
            Type = type;
            Bytes = bytes ?? new byte[0];
            Length = Bytes.Length;
        }

        public byte[] GetBytes()
        {
            var result = BitConverter.GetBytes(Length);

            return new [] {TagByte, (byte) Type}.Concat(BitConverter.IsLittleEndian ? result : result.Reverse()).Concat(Bytes).ToArray();
        }

        public static int GetLength(byte[] bytes, int count)
        {
            if (bytes.Length < count + TagAndLength)
            {
                throw new ArgumentException("Too short");
            }

            if (bytes[count] != TagByte)
            {
                throw new ArgumentException("Bad tag");
            }

            var bytes1 = bytes.Skip(count + TagSize).Take(LengthSize);
            var length = BitConverter.ToInt32((BitConverter.IsLittleEndian ? bytes1 : bytes1.Reverse()).ToArray(), 0);
            if (length + TagAndLength > bytes.Length)
            {
                throw new ArgumentException("Bad length");
            }

            return length;
        }

        public static IList<MediaChannelMessageComponent> ExtractComponents(byte[] bytes)
        {
            var components = new List<MediaChannelMessageComponent>();
            var count = 0;
            while (count < bytes.Length)
            {
                var length = GetLength(bytes, count);

                var type = (MediaChannelMessageComponentType) bytes[count + 1];
                if (!Enum.IsDefined(typeof(MediaChannelMessageComponentType), type))
                {
                    throw new ArgumentException("Bad component type");
                }

                components.Add(new MediaChannelMessageComponent(type, bytes.Skip(count + TagAndLength).Take(length).ToArray()));
                count += length + TagAndLength;
            }

            return components;
        }
    }
}