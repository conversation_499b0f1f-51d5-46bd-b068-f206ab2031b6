using CSharpFunctionalExtensions;
using CsvHelper;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Messages.Retalix;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Abstractions;
using System.Linq;
using System.Net;

namespace Forecourt.Bos.TransactionFiles
{
    /// <inheritdoc />
    public class RetalixTransactionFile : GenericTransactionFile, IRetalixTransactionFile
    {
        private const string ShiftEnd = "XSM";
        private const string Transaction = "XT";
        private const string SalesItem = "XSI";
        private const string CardVolume = "XCV";
        private const string CardAmount = "XCA";
        private const string UnconnectedDirectory = "unconnected";
        private readonly IListenerWorker<string> _retalixPosWorker;

        /// <inheritdoc />
        public RetalixTransactionFile(IHydraDb hydraDb, IHtecLogger logger, IFileSystem fileSystem, IListenerWorker<string> retalixPosWorker)
            : base(hydraDb, logger, fileSystem)
        {
            SetFileDirectory(AllFileLocations?.RetalixTransactionFileDirectory);
            _retalixPosWorker = retalixPosWorker ?? throw new ArgumentNullException(nameof(retalixPosWorker));
        }

        /// <inheritdoc />
        protected override void DoSetFileDirectoryAction(string directory)
        {
            HydraDb.SetRetalixTransactionFileDirectory(directory);
        }

        private static string CreateShiftEndFileName(RetalixShiftEndItem retalixShiftEndItems)
        {
            return $"{ShiftEnd}{retalixShiftEndItems.EndDate}{retalixShiftEndItems.EndTime}{CsvExtension}";
        }

        private string CreateItemFilename<T>(string prefix, IList<T> cardAmountItems) where T : RetalixItem
        {
            var time = cardAmountItems.Count > 0 ? cardAmountItems[0].Time() : GetNow();

            return $"{prefix}{time:yyMMddHHmmss}{CsvExtension}";
        }

        private string CreateTransactionFileName(RetalixTransactionFileItem transaction)
        {
            var now = GetNow();
            if (!int.TryParse(transaction.TransactionNumber, out var transactionNumber))
            {
                transactionNumber = 0;
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Error with transaction number {transaction.TransactionNumber}; {HeaderLoggingReference}: {LoggingReference}" });
            }
            
            return $"{Transaction}{transactionNumber:D6}_{now:yyMMddHHmmssfff}{CsvExtension}";
        }

        /// <inheritdoc />
        public void WriteTransactionFile(IEnumerable<RetalixTransactionFileItem> transactions, DateTime dateTime, IEnumerable<IPAddress> addresses, IMessageTracking message = null)
        {
            message ??= new MessageTracking();            
            DoAction(() => {
                foreach (var transaction in transactions)
                {
                    WriteItems(new[] { transaction }, addresses, CreateTransactionFileName(transaction), "Transaction");
                }
            }, message.FullId);
        }

        /// <inheritdoc />
        public void WriteShiftEnd(RetalixShiftEndItem shiftEnd, IEnumerable<IPAddress> addresses, string reference = null)
        {
            DoAction(() => { WriteItems(new[] { shiftEnd }, addresses, CreateShiftEndFileName(shiftEnd), "Shift End"); }, reference);
        }

        /// <inheritdoc />
        public void WriteSalesItems(RetalixItemSalesItem[] salesItems, IEnumerable<IPAddress> addresses, string reference = null)
        {
            DoAction(() => { WriteItems(salesItems, addresses, CreateItemFilename(SalesItem, salesItems), "Item Sale Items"); }, reference);
        }

        /// <inheritdoc />
        public void WriteCardVolumeItems(string tillNumber, RetalixCardVolumeItem[] cardVolumeItems, IEnumerable<IPAddress> addresses, string reference = null)
        {
            DoAction(() => { WriteItems(cardVolumeItems, addresses, CreateItemFilename(CardVolume, cardVolumeItems), "Card Volumes"); }, reference);
        }

        /// <inheritdoc />
        public void WriteCardAmountItems(RetalixCardAmountItem[] cardAmountItems, IEnumerable<IPAddress> addresses, string reference = null)
        {
            DoAction(() => { WriteItems(cardAmountItems, addresses, CreateItemFilename(CardAmount, cardAmountItems), "Card Amounts"); }, reference);
        }

        private void WriteItems<T>(IEnumerable<T> items, IEnumerable<IPAddress> addresses, string fileName, string itemName)
        {
            if (!Available)
            {
                return;
            }

            if (addresses != null && addresses.Any())
            {
                foreach (var address in addresses)
                {
                    WriteItemsToFile(items, FileSystem.Path.Combine(FileDirectory, $"{address.GetAddressBytes()[address.GetAddressBytes().Length - 1]}{Path.DirectorySeparatorChar}", fileName), itemName);
                }
            }
            else
            {
                DoDeferredLogging(LogLevel.Warn, HeaderInformation, () => new[] { $"No active connections, writing {itemName} to unconnected directory" });
                WriteItemsToFile(items, FileSystem.Path.Combine(FileDirectory, UnconnectedDirectory, fileName), itemName);
            }
        }

        /// <inheritdoc />
        public Result MoveOfflineTransactionFiles(IPAddress address, string logginReference = null)
        {
            return DoAction(() =>
            {
                GetLogger().Info("Attempting to move offline transaction files");
                List<string> unconnectedFiles;

                try
                {
                    FileSystem.Directory.CreateDirectory(FileDirectory);

                    unconnectedFiles = FileSystem.Directory.EnumerateFiles(FileSystem.Path.Combine(FileDirectory, UnconnectedDirectory)).ToList();

                    if (!unconnectedFiles.Any())
                    {
                        return Result.Failure("No un-connected files");
                    }

                    if (address == null)
                    {
                        DoDeferredLogging(LogLevel.Warn, HeaderInformation, () => new[] { "Unable to move unconnected files to new PoS directory as IP address has not been received" });
                        return Result.Failure("No IP address received");
                    }
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Failed to retrieve unconnected file list from folder {FileDirectory}" }, ex);
                    return Result.Failure(ex.Message);
                }

                var posTxnDirectory = FileSystem.Path.Combine(FileDirectory, address.GetAddressBytes()[address.GetAddressBytes().Length - 1].ToString());
                foreach (var unconnectedFile in unconnectedFiles)
                {
                    try
                    {
                        FileSystem.File.Move(unconnectedFile, FileSystem.Path.Combine(posTxnDirectory, FileSystem.Path.GetFileName(unconnectedFile)));
                    }
                    catch (Exception ex)
                    {
                        DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Failed to move unconnected file to {posTxnDirectory}" }, ex);
                        return Result.Failure(ex.Message);
                    }
                }

                return Result.Success();
            }, logginReference);
        }

        /// <inheritdoc />
        public void SendTransaction(RetalixTransactionFileItem transaction, out IEnumerable<IPAddress> addresses, IMessageTracking message = null)
        {
            IList<IPAddress> addresses2 = new List<IPAddress>();
            message ??= new MessageTracking();

            DoAction(() =>
            {                
                GetLogger().Info("ToRetalixPOS", () => null);
                try
                {
                    var ipAddress = HydraDb.GetRetalixPosPrimaryIpAddress();
                    using (var writer = new StringWriter())
                    {
                        using (var csv = new CsvWriter(writer, CultureInfo.CurrentCulture))
                        {
                            csv.WriteRecord(transaction);
                            csv.NextRecord();
                            _retalixPosWorker.ConnectionThread.Send(writer.ToString().Replace(Environment.NewLine, string.Empty), ipAddress, out addresses2, message.FullId);
                        }
                    }

                    GetLogger().Info("SentToRetalixPOS", () => new[] { $"{string.Join(", ", addresses2 ?? new List<IPAddress>())}" });
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                }

            }, message.FullId);

            addresses = addresses2;
        }
      
        /// <inheritdoc />
        public void WriteShiftEndFiles(RetalixShiftEndItem endItem, bool isDayEnd, IEnumerable<RetalixItemSalesItem> itemSales, IEnumerable<RetalixCardAmountItem> cardAmounts, IEnumerable<RetalixCardSalesItem> cardSales, IEnumerable<RetalixCategorySalesItem> categorySales, IEnumerable<RetalixCardVolumeItem> cardVolumeSales, IMessageTracking message = null)
        {
            if (!Available)            
            {
                return;
            }
            message ??=new MessageTracking();
            var logRef = message.FullId;

            var addresses = _retalixPosWorker.GetAllIpAddresses().ToList();

            WriteShiftEnd(endItem, addresses, logRef);
            WriteSalesItems(itemSales.ToArray(), addresses, logRef);
            WriteCardAmountItems(cardAmounts.ToArray(), addresses, logRef);
            WriteCardSalesItems(cardSales.ToArray(), addresses, logRef);
            WriteCardVolumeItems(string.Empty, cardVolumeSales.ToArray(), addresses, logRef);
            WriteCategorySalesItems(categorySales.ToArray(), addresses, logRef);            
        }

        /// <inheritdoc />
        public void WriteLocalAccountInvoiceFile(IEnumerable<RetalixLocalAccountItem> localAccountItems, short till, DateTime dateTime, IMessageTracking message = null)
        {
            // Do nothing
        }

        /// <inheritdoc />
        public void WriteCardSalesItems(RetalixCardSalesItem[] items, IEnumerable<IPAddress> options, string reference = null)
        {
            // Do nothing
        }

        /// <inheritdoc />
        public void WriteCategorySalesItems(RetalixCategorySalesItem[] items, IEnumerable<IPAddress> options, string reference = null)
        {
            // Do nothing
        }
    }
}
