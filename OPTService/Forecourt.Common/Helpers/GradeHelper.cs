using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Constants;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Forecourt.Common.Helpers
{
    public class GradeHelper : Loggable, IGradeHelper
    {
        private IDictionary<byte, GradeName> _dictionary = new ConcurrentDictionary<byte, GradeName>();
        private readonly IHydraDb _hydraDb;

        /// <summary>
        /// Default VAT Rate
        /// </summary>
        public const float DefaultVatRate = 20;

        /// <summary>
        /// Default grade name
        /// </summary>
        public const string DefaultGradeName = ConfigurationConstants.Unknown;

        public GradeHelper(IHtecLogger logger, IHydraDb hydraDb): base(logger)
        {
            _dictionary.Clear();
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        /// <inheritdoc/>
        public GradeName GetGradeInfo(byte id)
        {
            return _dictionary.TryGetValue(id, out GradeName info) ? info : Default;
        }

        /// <inheritdoc/>
        public string GetGradeName(byte id)
        {
            return _dictionary.TryGetValue(id, out GradeName info) ? info.Name : DefaultGradeName;
        }

        /// <inheritdoc/>
        public IEnumerable<GradeName> Grades => _dictionary.Values;

        /// <summary>
        /// Returns a default instance
        /// </summary>
        public static GradeName Default => new(0, DefaultGradeName, DefaultVatRate);

        /// <inheritdoc/>
        public void SetGrade(byte number, string name, float vatRate, string reference = null)
        {
            DoXxxGradeInfo(number, name, vatRate, reference);
        }

        /// <inheritdoc/>
        public void UpdateGrade(byte number, string name, string reference = null)
        {
            DoXxxGradeInfo(number, name, GetGradeInfo(number).VatRate, reference);
        }

        /// <inheritdoc/>
        public void UpdateGrade(byte number, float vatRate, string reference = null)
        {
            DoXxxGradeInfo(number, GetGradeInfo(number).Name, vatRate, reference);
        }

        /// <inheritdoc/>
        public void RemoveGrade(byte number, string reference = null)
        {
            DoXxxGradeInfo(number, null, DefaultVatRate, reference);
        }

        private void DoXxxGradeInfo(byte number, string name, float vatRate, string reference)
        {
            DoAction(() =>
            {
                _hydraDb.SetGradeName(number, name, vatRate);

                Initialise(reference);
            }, reference);
        }

        /// <inheritdoc/>
        public void Initialise(string reference = null)
        {
            DoAction(() =>
            {
                var names = _hydraDb.FetchGradeNames();
                _dictionary = names.ToDictionary(x => x.Grade, x => x);
            }, reference);
        }
    }
}
