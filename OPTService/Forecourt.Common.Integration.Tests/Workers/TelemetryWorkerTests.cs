using FluentAssertions;
using Forecourt.Common.Workers;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System;
using System.Collections.Generic;
using Xunit;

namespace OPT.Common.Integration.Tests.Workers
{
    public class TelemetryWorkerTests
    {
        private const string Device = "Device";
        private const string Version = "1.0";
        private const string OptIdString = "OPT ID";
        private const byte Pump = 1;
        private readonly IHtecLogger _telemetry;
        private readonly IHtecLogger _logger;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IOpt _opt;
        private readonly IPump _pump;
        private readonly IList<string> _infoMessages = new List<string>();
        private readonly IConfigurationManager _configurationManager;

        public TelemetryWorkerTests()
        {
            _opt = Substitute.For<IOpt>();
            _opt.IdString.Returns(OptIdString);
            _opt.ToString().Returns(OptIdString);
            _pump = Substitute.For<IPump>();
            _telemetry = Substitute.For<IHtecLogger>();
            _telemetry.When(x => x.Info(Arg.Any<string>())).Do(x => _infoMessages.Add((string) x[0]));
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _telemetryWorker = new TelemetryWorker(Device, Version, _telemetry, _logger, _configurationManager);
        }

        [Fact]
        public void test_message_sent_to_opt()
        {
            // Arrange
            const string message = "A Message";

            // Act
            _telemetryWorker.MessageSentToOpt(_opt);
            _telemetryWorker.MessageReceivedFromOpt(_opt, message);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} ({message}) response time,Milliseconds,*");
        }

        [Fact]
        public void test_config_pending_sent_to_opt()
        {
            // Arrange
            const string message = "ConfigPending";

            // Act
            _telemetryWorker.MessageSentToOpt(_opt);
            _telemetryWorker.MessageReceivedFromOpt(_opt, message);
            _telemetryWorker.ConfigRequestReceivedFromOpt(_opt);
            _telemetryWorker.SignInReceivedFromOpt(OptIdString);

            //Assert
            _telemetry.Received(3).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(3);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} ({message}) response time,Milliseconds,*");
            _infoMessages[1].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} Config Request time,Milliseconds,*");
            _infoMessages[2].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} Config Reload time,Milliseconds,*");
        }

        [Fact]
        public void test_whitelist_pending_sent_to_opt()
        {
            // Arrange
            const string message = "WhitelistPending";

            // Act
            _telemetryWorker.MessageSentToOpt(_opt);
            _telemetryWorker.MessageReceivedFromOpt(_opt, message);
            _telemetryWorker.WhitelistRequestReceivedFromOpt(_opt);

            //Assert
            _telemetry.Received(2).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(2);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} ({message}) response time,Milliseconds,*");
            _infoMessages[1].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} Whitelist Request time,Milliseconds,*");
        }

        [Fact]
        public void test_software_pending_sent_to_opt()
        {
            // Arrange
            const string message = "SoftwarePending";

            // Act
            _telemetryWorker.MessageSentToOpt(_opt);
            _telemetryWorker.MessageReceivedFromOpt(_opt, message);
            _telemetryWorker.SoftwareRequestReceivedFromOpt(_opt);

            //Assert
            _telemetry.Received(2).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(2);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} ({message}) response time,Milliseconds,*");
            _infoMessages[1].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} Software Request time,Milliseconds,*");
        }

        [Fact]
        public void test_delivered_sent_to_opt()
        {
            // Arrange

            // Act
            _telemetryWorker.DeliveredSentToOpt(Pump, string.Empty);
            _telemetryWorker.PaymentClearedReceivedFromOpt(Pump, string.Empty);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Pump {Pump} (Payment Cleared) response time,Milliseconds,*");
        }

        [Fact]
        public void test_opt_timeout()
        {
            // Arrange
            const string message = "A Message";

            // Act
            _telemetryWorker.MessageSentToOpt(_opt);
            _telemetryWorker.MessageTimeoutFromOpt(_opt, message, string.Empty);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},OPT {OptIdString} ({message}) timeout time,Milliseconds,*");
        }

        [Fact]
        public void test_message_sent_to_car_wash()
        {
            // Arrange

            // Act
            _telemetryWorker.MessageSentToCarWash();
            _telemetryWorker.MessageReceivedFromCarWash();

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Car Wash response time,Milliseconds,*");
        }

        [Fact]
        public void test_car_wash_timeout()
        {
            // Arrange

            // Act
            _telemetryWorker.MessageSentToCarWash();
            _telemetryWorker.MessageTimeoutFromCarWash();

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Car Wash timeout time,Milliseconds,*");
        }

        [Fact]
        public void test_message_sent_to_anpr()
        {
            // Arrange

            // Act
            _telemetryWorker.MessageSentToSecAuthHost(_pump.Number, "LOG-REF");
            _telemetryWorker.MessageReceivedFromSecAuthHost(_pump.Number, "LOG-REF");

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Pump (ANPR PumpLift) response time,Milliseconds,*");
        }

        [Fact]
        public void test_anpr_timeout()
        {
            // Arrange

            // Act
            _telemetryWorker.MessageSentToSecAuthHost(_pump.Number, "LOG-REF");
            _telemetryWorker.MessageTimeoutFromSecAuthHost(_pump.Number, string.Empty);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Pump (ANPR PumpLift) timeout time,Milliseconds,*");
        }

        [Fact]
        public void test_message_sent_to_third_party_pos()
        {
            // Arrange

            // Act
            _telemetryWorker.MessageSentToThirdPartyPos(Pump);
            _telemetryWorker.MessageReceivedFromThirdPartyPos(Pump);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Pump 1 (Third Party POS) response time,Milliseconds,*");
        }

        [Fact]
        public void test_query_sent_to_hydra_db()
        {
            // Arrange
            Guid guid = Guid.NewGuid();
            const string query = "A Query";

            // Act
            _telemetryWorker.QuerySentToHydraDb(guid);
            _telemetryWorker.QueryReturnedFromHydraDb(query, guid);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Hydra DB ({query}) response time,Milliseconds,*");
        }

        [Fact]
        public void test_query_sent_to_esocket_db()
        {
            // Arrange
            Guid guid = Guid.NewGuid();
            const string query = "A Query";

            // Act
            _telemetryWorker.QuerySentToEsocketDb(guid);
            _telemetryWorker.QueryReturnedFromEsocketDb(query, guid);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},eSocket.POS DB ({query}) response time,Milliseconds,*");
        }

        [Fact]
        public void test_staged_message()
        {
            // Arrange
            Guid guid = Guid.NewGuid();
            const string filename = "File Name";
            const int length = 100;

            // Act
            _telemetryWorker.StagedMessageStarted(guid);
            _telemetryWorker.StagedMessageFinished(guid, filename, length);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should()
                .Match($"*,{Device},HydraOPT,{Version},Staged message ({filename}, {length} bytes) response time,Milliseconds,*");
        }

        [Fact]
        public void test_channel_message()
        {
            // Arrange
            Guid guid = Guid.NewGuid();
            const string channelType = "Channel Type";

            // Act
            _telemetryWorker.ChannelRead(guid);
            _telemetryWorker.ChannelProcessed(guid, channelType);

            //Assert
            _telemetry.Received(1).Info(Arg.Any<string>());
            _infoMessages.Should().HaveCount(1);
            _infoMessages[0].Should().Match($"*,{Device},HydraOPT,{Version},Channel message (Channel Type) response time,Milliseconds,*");
        }
    }
}
