import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormControl, FormGroup } from '@angular/forms';
import { MiscConfigLevel } from 'src/app/core/enums/miscConfigLevel.enum';
import { Setting } from 'src/app/core/models/setting.model';

@Component({
  selector: 'app-misc-setting-detail',
  templateUrl: './misc-setting-detail.component.html',
  styleUrls: ['./misc-setting-detail.component.css']
})
export class MiscSettingDetailComponent implements OnInit {

  public form: FormGroup;
  public control : FormControl;

  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() placeHolderText: string = '';
  @Input() buttonText: string = 'Save';
  @Input() validationErrorText: string = 'Please provide a valid value';
  @Input() errorInActionText: string = 'Field couldn\'t be updated';
  @Input() appendText: string = '';
  @Input() controlName: string = '';
  @Input() setting: Setting;
  @Input() errorInAction: boolean = false;
  @Input() labelColClass: string = 'col-2';
  @Input() textColClass: string = 'col-2';
  @Input() buttonColClass: string = 'col-auto';
  @Input() disabled: boolean = false;
  @Input() id: string = '';
  @Input() maxLength: number = 524288; //The default value for HTML input maxlength attribute
  
  @Output() action = new EventEmitter();
   
  @Output() revertAction = new EventEmitter();

  MiscConfigLevel = MiscConfigLevel;

  public get fieldFeedbackId(): string {
    return `${this.id}FieldFeedback`;
  }

  public get formGroupId(): string {
    return `${this.id}FormGroup`;
  }

  public get actionButtonId(): string {
    return `${this.id}ActionBtn`;
  }

  public get revertDefaultButtonId(): string {
    return `${this.id}RevertDefaultBtn`;
  }

  public get revertGlobalButtonId(): string {
    return `${this.id}RevertGlobalBtn`;
  }

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
  }

  doAction(event?: Event): void {
    this.action.emit(event);
  }

  doRevertAction(event?: Event): void {
    this.revertAction.emit(event);
  }
}
