using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add SECAUTH Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20050008, "ADO#634495 - add SHIFT|DAY-END Configuration Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO634495_AddConfigurationCategory_ShiftDayEnd : Migrationable
    {
        private const string _categoryName = "SHIFT|DAY-END";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{_categoryName}', @description = 'All Shift and Day End related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{_categoryName}'");
        }
    }
}
