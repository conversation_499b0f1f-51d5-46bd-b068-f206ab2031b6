import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { DivertDetails } from 'src/app/core/models/divertDetails.model';
import { DIVERT_SERVICE_PROVIDER, DIVERT_SERVICE_SPY } from 'src/app/services/divert.service.spy';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { NGX_LOGGER_PROVIDER, NGX_LOGGER_SPY } from 'src/app/testing/ngxlogger.spy';

import { CardDivertOptComponent } from './card-divert-opt.component';

describe('CardDivertOptComponent', () => {
  let component: CardDivertOptComponent;
  let fixture: ComponentFixture<CardDivertOptComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CardDivertOptComponent ],
      imports: [
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        FormBuilder,
        NGX_LOGGER_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        DIVERT_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    SIGNAL_R_SERVICE_SPY().getDivertDetailsSignalRMessage.and.returnValue(of());
    DIVERT_SERVICE_SPY().getDivertDetails.and.returnValue(of());

    fixture = TestBed.createComponent(CardDivertOptComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('.refreshDivertData()', () => {

    it('.refreshDivertData() should handle success response from service', ()=>{
      //Arrange
      const fakeDivertData = {} as DivertDetails;
      DIVERT_SERVICE_SPY().getDivertDetails.and.returnValue(of(fakeDivertData));

      //Act
      component.refreshDivertData();

      //Assert
      expect(component.divertDetailsData).toEqual(fakeDivertData);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
    });

    it('.refreshDivertData() should handle unsuccess response from service', ()=>{
      //Arrange
      DIVERT_SERVICE_SPY().getDivertDetails.and.returnValue(throwError({status:500}));

      //Act
      component.refreshDivertData();

      //Assert
      expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
    });
  });
});
