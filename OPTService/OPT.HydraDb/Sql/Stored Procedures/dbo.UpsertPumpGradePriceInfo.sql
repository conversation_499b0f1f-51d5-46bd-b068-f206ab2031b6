USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('UpsertPumpGradePriceInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE UpsertPumpGradePriceInfo
END
GO

CREATE PROCEDURE [dbo].UpsertPumpGradePriceInfo
  @pump INT,
  @hose INT,
  @grade INT,
  @price INT
AS
BEGIN
	BEGIN TRY
		BEGIN TRANSACTION
			UPDATE dbo.[PumpGradePriceInfo] WITH (UPDLOCK, SERIALIZABLE) 
			SET [Price] = @price,
				[ModifiedDate] = GETDATE()
			WHERE [Pump] = @pump
			    AND [Hose] = @hose
				AND [Grade] = @grade
 
			IF @@ROWCOUNT = 0
			BEGIN
				INSERT dbo.[PumpGradePriceInfo] ([Pump],[Hose],[Grade],[Price],[CreatedDate],[ModifiedDate]) 
				VALUES (@pump, @hose, @grade, @price, GETDATE(),  GETDATE())
			END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW
	END CATCH
END
GO

GRANT EXECUTE ON UpsertPumpGradePriceInfo TO UserRole
GO
