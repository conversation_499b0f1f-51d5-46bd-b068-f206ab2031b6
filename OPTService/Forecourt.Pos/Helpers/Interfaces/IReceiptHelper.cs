using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using System.Collections.Generic;
using posReceiptInfo = Htec.Hydra.Core.Pos.Messages.ReceiptInfo;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;

namespace Forecourt.Pos.Helpers.Interfaces
{
    /// <summary>
    /// Any and all capabilities related to receipts
    /// </summary>
    public interface IReceiptHelper
    {
        /// <summary>
        /// Extract the pump number from the receipt
        /// </summary>
        /// <param name="receipt">Full receipt details <see cref="ReceiptInfo"/></param>
        /// <returns>Pump number,</returns>
        byte GetPumpFromReceiptInfo(ReceiptInfo receipt);

        /// <summary>
        /// Extract the pump number from the receipt
        /// </summary>
        /// <param name="receipt">Full receipt details <see cref="posReceiptInfo"/></param>
        /// <returns>Pump number</returns>
        byte GetPumpFromReceiptInfo(posReceiptInfo receipt);

        /// <summary>
        /// Get the Receipts, linked to a pump, cardnumber or transaction 
        /// </summary>
        /// <param name="pump"></param>
        /// <param name="request">Receipt request details</param>
        /// <param name="includeExpiredReceipts">Include all receipts, or just recent ones, defaults to all</param>
        /// <returns>Result wrapped list of <see cref="ReceiptInfo"/></returns>
        Result<IEnumerable<ReceiptInfo>> GetReceipts(IPump pump, GetReceiptRequest request, bool includeExpiredReceipts = true);

        /// <summary>
        /// Get the Receipts for the OPT linked to the Pump
        /// </summary>
        /// <param name="pump">The pump</param>
        /// <returns>Result wrapped list of <see cref="ReceiptInfo"/></returns>
        Result<IEnumerable<ReceiptInfo>> GetReceipts(IPump pump);

        /// <summary>
        /// Get just the receipt text from the receipt contents
        /// </summary>
        /// <param name="receipt">Full receipt contents</param>
        /// <param name="centreAlign">Centre align the text content</param>
        /// <returns></returns>
        Result<IEnumerable<string>> GetRawReceiptText(ReceiptInfo receipt, bool centreAlign = false);
    }
}
