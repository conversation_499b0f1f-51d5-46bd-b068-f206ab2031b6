using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using System.Threading.Tasks;

namespace Forecourt.Bos.Orchestrations.Interfaces
{
    /// <summary>
    /// Any and all capabilities of an data mapping orchestration
    /// </summary>
    /// <typeparam name="TMessageTracking">Type implementing <see cref="ILogTracking"/></typeparam>
    public interface IMapConfigurationOrchestration<TMessageTracking>
    {
        /// <summary>
        /// Execute the whole map configuration data process
        /// </summary>
        /// <param name="message">Current message instance</param>
        /// <returns><see cref="StatusCodeResult"/> instance</returns>
        StatusCodeResult RunOrchestration(TMessageTracking message = default);
    }

    /// <summary>
    /// Any and all capabilities of an data mapping orchestration, asynchronously
    /// </summary>
    /// <typeparam name="TMessageTracking">Type implementing <see cref="ILogTracking"/></typeparam>
    public interface IMapConfigurationOrchestrationAsync<TMessageTracking>
    {
        /// <summary>
        /// Execute the whole map configuration data process
        /// </summary>
        /// <param name="message">Current message instance</param>
        /// <returns><see cref="StatusCodeResult"/> instance</returns>
        Task<StatusCodeResult> RunOrchestrationAsync(TMessageTracking message = default);
    }
}
