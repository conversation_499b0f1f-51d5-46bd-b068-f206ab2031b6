namespace Forecourt.Core.HydraDb.Models
{
    public class AllFileLocations
    {
        public string RetalixTransactionFileDirectory { get; }
        public string TransactionFileDirectory { get; }
        public string WhitelistDirectory { get; }
        public string LayoutDirectory { get; }
        public string SoftwareDirectory { get; }
        public string ContactlessPropertiesFile { get; }
        public string FuelDataUpdateFile { get; }
        public string UpgradeFileDirectory { get; }
        public string RollbackFileDirectory { get; }
        public string MediaDirectory { get; }
        public string PlaylistDirectory { get; }
        public string OptLogFileDirectory { get; }
        public string LogFileDirectory { get; }
        public string TraceFileDirectory { get; }
        public string JournalFileDirectory { get; }
        public string ReceivedUpdateDirectory { get; }
        public string DatabaseBackupDirectory { get; }
        public string EsocketConnectionString { get; }
        public bool EsocketUseConnectionString { get; }
        public string EsocketConfigFile { get; }
        public string EsocketKeystoreFile { get; }
        public string EsocketDbUrl { get; }
        public bool EsocketOverrideProperties { get; }
        public bool EsocketOverrideKeystore { get; }
        public bool EsocketOverrideUrl { get; }
        public bool EsocketOverrideContactless { get; }

        // ReSharper disable once UnusedMember.Global
        public AllFileLocations
        (string retalixTransactionFileDirectory, string transactionFileDirectory, string whitelistDirectory, string layoutDirectory,
            string softwareDirectory, string contactlessPropertiesFile, string fuelDataUpdateFile, string upgradeFileDirectory,
            string rollbackFileDirectory, string mediaDirectory, string playlistDirectory, string optLogFileDirectory,
            string logFileDirectory, string traceFileDirectory, string journalFileDirectory, string receivedUpdateDirectory,
            string databaseBackupDirectory, string esocketConnectionString, bool esocketUseConnectionString, string esocketConfigFile,
            string esocketKeystoreFile, string esocketDbUrl, bool esocketOverrideProperties, bool esocketOverrideKeystore,
            bool esocketOverrideUrl, bool esocketOverrideContactless)
        {
            RetalixTransactionFileDirectory = retalixTransactionFileDirectory;
            TransactionFileDirectory = transactionFileDirectory;
            WhitelistDirectory = whitelistDirectory;
            LayoutDirectory = layoutDirectory;
            SoftwareDirectory = softwareDirectory;
            ContactlessPropertiesFile = contactlessPropertiesFile;
            FuelDataUpdateFile = fuelDataUpdateFile;
            UpgradeFileDirectory = upgradeFileDirectory;
            RollbackFileDirectory = rollbackFileDirectory;
            MediaDirectory = mediaDirectory;
            PlaylistDirectory = playlistDirectory;
            OptLogFileDirectory = optLogFileDirectory;
            LogFileDirectory = logFileDirectory;
            TraceFileDirectory = traceFileDirectory;
            JournalFileDirectory = journalFileDirectory;
            ReceivedUpdateDirectory = receivedUpdateDirectory;
            DatabaseBackupDirectory = databaseBackupDirectory;
            EsocketConnectionString = esocketConnectionString;
            EsocketUseConnectionString = esocketUseConnectionString;
            EsocketConfigFile = esocketConfigFile;
            EsocketKeystoreFile = esocketKeystoreFile;
            EsocketDbUrl = esocketDbUrl;
            EsocketOverrideProperties = esocketOverrideProperties;
            EsocketOverrideKeystore = esocketOverrideKeystore;
            EsocketOverrideUrl = esocketOverrideUrl;
            EsocketOverrideContactless = esocketOverrideContactless;
        }
    }
}