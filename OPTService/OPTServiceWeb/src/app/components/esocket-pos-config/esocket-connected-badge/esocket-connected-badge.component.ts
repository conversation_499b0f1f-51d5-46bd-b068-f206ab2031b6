import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-esocket-connected-badge',
  templateUrl: './esocket-connected-badge.component.html',
  styleUrls: ['./esocket-connected-badge.component.css']
})
export class EsocketConnectedBadgeComponent {

  @Input('eSocketConnected')
  private _eSocketConnected: boolean | string;

  public get eSocketConnected(): boolean {
    return this._eSocketConnected === true || this._eSocketConnected === 'true';
  }

}
