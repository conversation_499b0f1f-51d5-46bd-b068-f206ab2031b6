USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('SetReceiptPrintedCount') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE SetReceiptPrintedCount
END
GO

CREATE PROCEDURE [dbo].[SetReceiptPrintedCount]
 @cardNumber VARCHAR(20),
 @opt VARCHAR(1024),
 @transactionNumber BIGINT
AS
BEGIN
	BEGIN TRY
		BEGIN TRANSACTION
		
		DECLARE @count INT = 0;
		SELECT @count = t.PrintedCount FROM Receipts t WHERE t.CardNumber = @cardNumber AND t.OPT = @opt AND t.TransactionNumber = @transactionNumber;

		UPDATE t
		SET t.PrintedCount = @count + 1
		FROM Receipts t
		WHERE t.CardNumber = @cardNumber AND t.OPT = @opt AND t.TransactionNumber = @transactionNumber;

		IF @@ROWCOUNT = 0
			THROW 50000, 'Receipt NotFound', 1

		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION;
		THROW
	END CATCH
END
GO

GRANT EXECUTE ON SetReceiptPrintedCount TO UserRole
GO
