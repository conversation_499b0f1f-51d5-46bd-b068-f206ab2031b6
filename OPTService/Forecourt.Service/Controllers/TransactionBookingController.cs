using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.HydraDb.Models;
using Forecourt.Service.OPTServiceClasses;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Core;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Description;

namespace Forecourt.Service.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all TransactionBooking requests
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "WebUi,Swagger,ThirdParty,Orbis")]
    [RoutePrefix("api/transactionBooking")]
    public class TransactionBookingController: LinkedLoggableApiController<IHydraDb>
    {
        /// <inheritdoc/>
        public TransactionBookingController(IHydraDb loggable): base(loggable as Loggable)
        {            
        }

        /// <summary>
        /// Get a list of all pending transaction bookings
        /// </summary>
        /// <param name="loggindReference">Current logging reference</param>
        /// <returns><see cref="IHttpActionResult"/></returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpGet, Route("pendingBookings"), ResponseType(typeof(IEnumerable<TransactionBookingState>))]
        public async Task<IHttpActionResult> RequestPendingTransactionBookings([FromUri] string loggindReference = null)
        {
            // TODO: Http version
            var result = await DoActionAsync(async () => Loggable.GetPendingTransactionBookings(loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Get a specific transaction booking
        /// </summary>
        /// <param name="loggindReference">Current logging reference</param>
        /// <param name="request">Request details</param>
        /// <returns><see cref="IHttpActionResult"/></returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("booking"), ResponseType(typeof(TransactionBookingState))]
        public async Task<IHttpActionResult> RequestTransactionBooking([FromBody] RequestBooking request, [FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync<TransactionBookingState>(async () => 
                Loggable.GetTransactionBookingHttp(request.TransId, request.TxnNumber, request.ExternalTransId, 
                loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Updates a specific transaction booking, with external booking details, e.g. transaction number
        /// </summary>
        /// <param name="loggindReference">Current logging reference</param>
        /// <param name="request">Request details</param>
        /// <returns><see cref="IHttpActionResult"/></returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("updateExternal")]
        public async Task<IHttpActionResult> UpdateTransactionBookingExternal([FromBody] UpdateBookingExternal request, [FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync(async () => Loggable.UpdateTransactionBookingHttp(request.Id, request.TransId, request.ExternalTransId, request.ShiftId, request.PeriodId, request.BusinessDate,
                    loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Updates a specific transaction booking, with failure details
        /// </summary>
        /// <param name="loggindReference">Current logging reference</param>
        /// <param name="request">Request details</param>
        /// <returns><see cref="IHttpActionResult"/></returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("updateFail")]
        public async Task<IHttpActionResult> UpdateTransactionBookingFail([FromBody] UpdateBookingFail request, [FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync(async () =>
                Loggable.UpdateTransactionBookingHttp(request.Id, request.TransId, request.StatusCode, request.StatusResponse, request.RetryCount, request.NextRetryDate,
                loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Updates a specific transaction booking, with completion details
        /// </summary>
        /// <param name="loggindReference">Current logging reference</param>
        /// <param name="request">Request details</param>
        /// <returns><see cref="IHttpActionResult"/></returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("complete")]
        public async Task<IHttpActionResult> CompleteTransactionBooking([FromBody] CompleteBooking request, [FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync(async () =>
                Loggable.CompleteTransactionBookingHttp(request.Id, request.TransId, request.BookedDate,
                loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }
    }
}
