using Microsoft.Owin.StaticFiles;
using System.IO;

namespace OPTService.Extensions
{
    public static class HeadersHelper
    {
        /// <summary>
        /// Adds OWASP recommended headers
        /// </summary>
        /// <param name="context">StaticFileResponseContext</param>
        public static void AddResponseHeaders(this StaticFileResponseContext context)
        {
            // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options
            context.OwinContext.Response.Headers.Add("X-Frame-Options", new[] { "deny" });

            // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-XSS-Protection
            context.OwinContext.Response.Headers.Add("X-XSS-Protection", new[] { "1; mode=block" });

            // The X-Content-Type-Options header prevents ICO files from being loaded
            if (Path.GetExtension(context.File.Name) != ".ico")
            {
                // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options
                context.OwinContext.Response.Headers.Add("X-Content-Type-Options", new[] { "nosniff" });
            }
        }
    }
}
