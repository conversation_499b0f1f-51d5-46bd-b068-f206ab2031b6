using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using signalR = Htec.Hydra.Core.Pos.Messages.SignalR;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc/>
    public class SignalRPosOutWorker : PosReceiptXxxWorker, ISignalRPosOutWorker
    {
        /// <summary>
        /// SignalR Hub
        /// </summary>
        protected readonly IHubbable<PosMessage, IMessageTracking> HubContext;

        private readonly IPrinterHelper<IMessageTracking> _printerHelper;

        /// <inheritdoc/>
        public SignalRPosOutWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IHydraDb hydraDb, IPumpCollection allPumps, IPrinterHelper<IMessageTracking> printerHelper,
            IReceiptHelper receiptHelper, IHubbable<PosMessage, IMessageTracking> hubContext) : base(logManager, nameof(SignalRPosOutWorker), hydraDb, allPumps, receiptHelper, configurationManager)
        {
            HubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));

            _printerHelper = printerHelper ?? throw new ArgumentNullException(nameof(printerHelper));
        }

        /// <inheritdoc/>
        public Result ModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking tracking = null, bool suppressModeChange = false)
        {
            return DoActionPump(pump, tracking, (p) =>
                HubContext.Publish("modeChangedResponse", new signalR.ModeChangeResponse { OptId = p.Opt?.IdString, Pump = p.Number, Mode = $"{mode}", PreviousOptMode = $"{previousState}", MessageId = tracking.IdAsString },
                    (all, pl) => all.modeChangedResponse(pl), tracking));
        }

        /// <inheritdoc/>
        public Result ReceiptResponse(ReceiptInfo receipt, IMessageTracking tracking = null)
        {
            return DoActionPump(ReceiptHelper.GetPumpFromReceiptInfo(receipt), tracking, (p) =>
                HubContext.Publish("receiptResponse", new signalR.ReceiptResponse { OptId = p.Opt?.IdString, Pump = p.Number, Receipt = receipt, MessageId = tracking.IdAsString },
                    (all, pl) => all.receiptResponse(pl), tracking));
        }

        /// <inheritdoc/>
        public Result ReceiptResponse(byte pump, IMessageTracking tracking = null)
        {
            return DoActionPump(pump, tracking, (p) =>
            {
                var result = ReceiptHelper.GetReceipts(p);
                if (!result.IsSuccess)
                {
                    return;
                }

                var receipts = result.Value.Select(x => _printerHelper.FormatReceipt(x.ReceiptContent, true, false));

                HubContext.Publish("receiptsResponse", new signalR.ReceiptsResponse { OptId = p.Opt?.IdString, Pump = p.Number, Receipts = receipts, MessageId = tracking.IdAsString },
                    (all, pl) => all.receiptsResponse(pl), tracking);
            });
        }

        /// <inheritdoc/>
        public Result StatusResponse(StatusResponse status, IMessageTracking tracking = null)
        {
            return DoActionPump(status.Pump, tracking, (p) =>
            {
                var sigR = (signalR.StatusResponse)status;
                sigR.OptId = p.Opt?.IdString;
                sigR.MessageId = tracking.IdAsString;
                HubContext.Publish("statusResponse", sigR, (all, pl) => all.statusResponse(pl), tracking);
            });
        }

        /// <inheritdoc/>
        public Result StatusResponse(byte pump, IMessageTracking tracking = null)
        {
            return DoActionPump(pump, tracking, (p) =>
            {
                var result = GetStatus(p);
                if (!result.IsSuccess)
                {
                    return;
                }

                var sigR = (signalR.StatusResponse)result.Value;
                sigR.OptId = p.Opt?.IdString;
                sigR.MessageId = tracking.IdAsString;
                HubContext.Publish("statusResponse", sigR, (all, pl) => all.statusResponse(pl), tracking);
            });
        }

        /// <inheritdoc/>
        protected override bool DoIsConnected() => HubContext.IsConnected();        

        /// <inheritdoc/>
        protected override int DoGetConnectedCount() => HubContext.ConnectedCount;

        /// <inheritdoc/>
        protected override IEnumerable<IPAddress> DoGetAllIpAddresses() => HubContext.GetAllIpAddresses();

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            HubContext.RegisterWorker(GetWorker<Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<string>>());
            return base.DoStart(startParams);
        }
    }
}
