using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Extensions;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Helpers;
using Htec.Hydra.Core.Pos.Messages.HydraMobile;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Timers;
using System.Xml.Linq;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;
using posIntf = Htec.Hydra.Core.Pos.Interfaces;
using PumpState = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpState;
using Status = Htec.Hydra.Core.Pos.Messages.HydraMobile.Status;
using StatusResponse = Htec.Hydra.Core.Pos.Messages.StatusResponse;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Represents a worker responsible for handling full Hydra Mobile operations within the forecourt POS system.
    /// </summary>
    /// <inheritdoc cref="IHydraMobileFullWorker" />
    public class HydraMobileFullWorker : BaseHydraMobileWorker, IHydraMobileFullWorker
    {
        public HydraMobileFullWorker(IHtecLogManager logMan, IHydraDb hydraDb, IConfigurationManager configurationManager, ITelemetryWorker telemetryWorker, IClientConnectionThread<XElement> connectionThread,
            posIntf.IPosIntegratorOutTransient<IMessageTracking> posOut, IJournalWorkerReceipt journalWorker, IBosIntegratorOut<IMessageTracking> bosOut, IOptCollection allOpt, IPumpCollection allPumps,
            IGradeHelper gradeHelper, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOut, ITimerFactory timerFactory) :
            base(logMan, hydraDb, configurationManager, telemetryWorker, connectionThread, posOut, journalWorker, bosOut, allOpt, allPumps, gradeHelper, secAuthOut, timerFactory)
        {            
        }

        private static (string, DateTime, long) GetReceiptInfo(IEnumerable<string> receiptLines)
        {
            var matches = Regex.Matches(string.Join(string.Empty, receiptLines),
                "( TRANSACTION (?<transaction>\\w{1,9}) DATE: (?<date>\\d{1,2} \\w{1,3} \\d{1,4}) TIME: (?<time>\\d{1,2}:\\d{1,2})" +
                "(?<misc>[.| |\\n|\\r|\\w|\\W]*) TOTAL \\?(?<amount>[\\d|\\.|]{1,}))");

            if (matches.Count > 0)
            {
                var txn = matches[0].Groups["transaction"].Value;
                var date = matches[0].Groups["date"].Value;
                var time = matches[0].Groups["time"].Value;
                var amount = matches[0].Groups["amount"].Value;

                return ($"{HeaderHydraMobile}-{txn}", DateTime.Parse($"{date} {time}"), Convert.ToInt64(Convert.ToDouble(amount) * 100));
            }

            return (HeaderHydraMobile, DateTime.MinValue, 0);
        }

        /// <inheritdoc />
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            var message = new MessageTracking();
            SendRequest(MessageGenerator.ConstructSendTransactionsCommandMessage(), message);
        }

        /// <summary>
        /// Processes a receipt message received from the Hydra Mobile system.
        /// This method overrides the base implementation to handle receipt-specific logic,
        /// such as extracting transaction details, updating receipt lines, and storing the receipt
        /// transaction in the associated OPT system.
        /// </summary>
        protected override Result<XElement> DoOnMessageReceived_Receipt(IMessageTracking<XElement> message, ReceiptData responseReceipt)
        {
            var (txn, dtm, value) = GetReceiptInfo(responseReceipt.Receipt.Lines);
            responseReceipt.Receipt.Lines.Insert(0, OPT.Common.ConfigConstants.CustomerCopy); // Indicator to store and allocate TransId

            var receipt = new Htec.Hydra.Messages.Opt.Models.ReceiptTransaction()
            {
                CardNumber = HeaderHydraMobile,
                Details = string.Join(Environment.NewLine, responseReceipt.Receipt.Lines),
                PrintedCount = 0,
                TimeStamp = $"{dtm:g}",
                TxnNumber = txn,
                Value = value
            };

            var theOpt = AllOpts.GetOptForIdString<IOptCore>(HeaderHydraMobile);
            theOpt.StoreTxnReceipt(txn, receipt);
            PumpTxnNumbers[responseReceipt.Pump] = txn;

            return base.DoOnMessageReceived_Receipt(message, responseReceipt);
        }

        /// <summary>
        /// Processes a transaction message received from Hydra Mobile.
        /// </summary>
        protected override Result<XElement> DoOnMessageReceived_Transaction(IMessageTracking<XElement> message, Transaction responseTransaction)
        {
            var theOpt = AllOpts.GetOptForIdString<IOptCore>(HeaderHydraMobile);
            if (!AllPumps.TryGetPump((byte)responseTransaction.Pump, out var thePump))
            {
                return Result.Failure<XElement>($"Pump {(byte)responseTransaction.Pump} is not defined!");
            }

            var hose = (byte)responseTransaction.Hose;
            byte grade = 0;
            ushort price = 0;

            // No receipt, no HydraMobile TxnNumber
            if (!PumpTxnNumbers.TryGetValue(thePump.Number, out var txn))
            {
                // Therefore, allocate one
                txn = $"{HeaderHydraMobile}-{responseTransaction.Id}";
            }

            // Map ItemId = GradeName to Product, prior to posting
            var mappings = HydraDb.FetchTariffMappings();
            foreach (var salesItem in responseTransaction.SaleItems)
            {
                grade = byte.TryParse(salesItem.ItemId.Trim(), out var gradeId) ? gradeId :
                    GradeHelper.Grades.FirstOrDefault(x => x.Name.Equals(salesItem.ItemId, StringComparison.InvariantCultureIgnoreCase))?.Grade ?? 0;

                price = (ushort)(salesItem.UnitPrice * 10);

                var code = mappings.FirstOrDefault(x => x.Grade == grade)?.ProductCode ?? "1";

                salesItem.ItemId = code;
            }

            var payment = responseTransaction.ConvertTo(thePump.Number, txn, HeaderHydraMobile);

            if ((grade == 0 || hose == 0 || price == 0)
                && CurrentInfo.TryGetValue(thePump.Number, out var info) && info.Hose != null)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "FpId", () => new[] { $"{thePump.Number}; CurrentInfo: {JsonConvert.SerializeObject(info)}" }, methodName: "SetDelivered");

                hose = info.Hose.Number;
                grade = info.Hose.GradeNumber;
                price = info.Hose.Grade.PriceAsShort;
            }

            if (grade == 0 || hose == 0 || price == 0)
            {
                DoDeferredLogging(LogLevel.Error, "Transaction.Failure.Zero.FpId", () => new[] { $"{thePump.Number}; TxnNumber: {payment.TxnNumber}; Grade: {grade}; Hose: {hose}; Price {price}" }, reference: message.FullId);
                return Result.Failure<XElement>("Zero Grade/Hose/Price");
            }

            // Have we already got the transaction
            var resultTransaction = HydraDb.FetchFuelTransactions(responseTransaction.TimeStamp.AddDays(-1), DateTime.Now, ConfigValueMaxRetalixTransactionNumber.GetValue());
            if (resultTransaction == null)
            {
                return Result.Failure<XElement>("Error Fetching transactions");
            }

            if (resultTransaction.Any(x => x.TxnNumber == payment.TxnNumber))
            {
                DoDeferredLogging(LogLevel.Error, "Transaction.Duplicate.FpId", () => new[] { $"{thePump.Number}; TxnNumber: {payment.TxnNumber}" }, reference: message.FullId);
            }
            else
            {
                HydraDb.SetDelivered(message, thePump.Number, grade, 0, 0, string.Empty, price, 0, 0, 0, 0, hose);
                try
                {
                    JournalWorker.PaymentCleared(PaymentResult.Cleared, null, thePump, theOpt, message, payment);
                }
                finally
                {
                    HydraDb.ClearDelivered(thePump.Number, message);
                }
            }
            
            return Result.Success(XElement.Parse(XmlSerializerHelper.Serialise(BookTransactionResponse.Create(responseTransaction.Id))));
        }

        /// <inheritdoc cref="IMessageTracking" />
        protected override Result<(XElement, StatusResponse)> DoOnMessageReceived_Status(IMessageTracking<XElement> message, Status status, string sendInfo)
        {
            return (null, new StatusResponse()
            {
                Pump = (byte)status.Pump,
                IsOffline = status.IsOffline,
                IsKioskUse = false,
                IsInUse = false,
                IsOutsideOnly = false,
                IsOptLinked = false,
                IsAutoAuth = false,
                PumpState = (ushort)(Enum.TryParse<PumpState>(status.PumpState, true, out var state) ? state : PumpState.Idle)
            });
        }
    }
}
