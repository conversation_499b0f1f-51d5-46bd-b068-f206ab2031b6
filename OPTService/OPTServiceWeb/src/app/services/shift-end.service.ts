import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { LogIntervalDetails } from '../core/models/logIntervalDetails';
import { PrinterDetails } from '../core/models/printerDetails';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class ShiftEndService {

  private baseUrl: string;

  /**
   * The ShiftEndService constructor
   * @param http The http client used for requests
   */
  constructor(private http: HttpClient,
    private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.shiftEndController.base;
  }

  /**
   * Gets the ShiftEnd configuration 
   */
  public getShiftEndInfo(): Observable<any> {
    const url = this.baseUrl + environment.shiftEndController.getShiftEndInfo;

    this.logger.debug(`Getting ShiftEnd configuration via url: ${url}`);
    return this.http.get(url, {})
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Performs ShiftEnd operation 
   * @param route The route name
   * @param logMessage The log message to print
   */
  public setInfoAsSimpleGet(route: string, logMessage: string): Observable<any> {
    const url = this.baseUrl + route;
    this.logger.debug(`${logMessage} operation via url: ${url}`);
    return this.http.get(url, {});
  }

  /**
   * Sets log interval
   * @param route The route name
   * @param logMessage The log message to print
   * @param printerDetails The printer details object
   */
  public setPrinterDetails(route: string, logMessage: string, printerDetails: PrinterDetails) {
    const url = this.baseUrl + route;
    const options = { params: {} };
    this.logger.debug(`Setting ${logMessage} via ${url}`);
    return this.http.post(url, printerDetails, options);
  }

  /**
   * Performs ShiftEnd operation 
   */
  public performShiftEnd() {
    return this.setInfoAsSimpleGet(environment.shiftEndController.performShiftEnd, 'Performing ShiftEnd');
  }

  /**
   * Performing DayEnd operation 
   */
  public performDayEnd() {
    return this.setInfoAsSimpleGet(environment.shiftEndController.performDayEnd, 'Performing DayEnd');
  }

  /**
   * Performs ClearAutoDayEnd operation 
   */
  public clearAutoDayEnd() {
    return this.setInfoAsSimpleGet(environment.shiftEndController.clearAutoDayEnd, 'Performing ClearAutoDayEnd');
  }

  /**
   * Sets Asda Day End Report On 
   */
  public setAsdaDayEndReportOn() {
    return this.setInfoAsSimpleGet(environment.shiftEndController.setAsdaDayEndReportOn, 'Setting Asda Day End Report On');
  }

  /**
   * Setting Asda Day End Report Off 
   */
  public setAsdaDayEndReportOff() {
    return this.setInfoAsSimpleGet(environment.shiftEndController.setAsdaDayEndReportOff, 'Setting Asda Day End Report Off');
  }

  /**
   * Sets Auto Day End time
   * @param dayEnd The day end time to set
   */
  public setNextDayEnd(dayEnd: string): Observable<any> {
    const url = this.baseUrl + environment.shiftEndController.setNextDayEnd;
    const options = {
      params: {
        dayEnd: dayEnd
      }
    };
    this.logger.debug(`Setting Auto Day End time operation via url: ${url}`);
    return this.http.get(url, options);
  }

  /**
   * Sets log interval
   * @param loginterval Interval to set
   */
  public setLogInterval(loginterval: LogIntervalDetails) {
    const url = this.baseUrl + environment.shiftEndController.setLogInterval;
    const options = { params: {} };
    this.logger.debug(`Setting log interval: ${loginterval.Hours}, ${loginterval.Minutes}, ${loginterval.Seconds} via ${url}`);
    return this.http.post(url, loginterval, options);
  }

  /**
   * Set printer enabled
   * @param printerDetails The printer details object
   */
   public setPrinterEnabled(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterEnabled, 'Setting printer enabled', printerDetails);
  }

  /**
   * Set printer port name
   * @param printerDetails The printer details object
   */
   public setPrinterPortName(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterPortName, 'Setting printer port name', printerDetails);
  }

  /**
   * Set printer baud rate
   * @param printerDetails The printer details object
   */
   public setPrinterBaudRate(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterBaudRate, 'Setting printer baud rate', printerDetails);
  }

  /**
   * Set printer handshake
   * @param printerDetails The printer details object
   */
   public setPrinterHandshake(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterHandshake, 'Setting printer handshake', printerDetails);
  }

  /**
   * Set printer stop bits
   * @param printerDetails The printer details object
   */
   public setPrinterStopBits(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterStopBits, 'Setting printer stop bits', printerDetails);
  }

  /**
   * Set printer data bits
   * @param printerDetails The printer details object
   */
   public setPrinterDataBits(printerDetails: PrinterDetails) {
    return this.setPrinterDetails(environment.shiftEndController.setPrinterDataBits, 'Setting printer data bits', printerDetails);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
