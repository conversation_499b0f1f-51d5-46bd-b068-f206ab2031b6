using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.IO;
using System.IO.Abstractions;
using System.Threading;
using Xunit;

namespace OPT.Common.Integration.Tests.Workers
{
    public class ConfigUpdateWorkerIntegrationTests
    {
        private const string WhitelistDirectory = "Whitelist";
        private const string ContactlessDirectory = "ContactlessProperties";
        private const string MediaDirectory = "Media";
        private const string SoftwareDirectory = "Software";
        private const string UpgradeDirectory = "Upgrade";
        private const string RollbackDirectory = "Rollback";
        private readonly string _whitelistFile = WhitelistDirectory + Path.DirectorySeparatorChar + "whitelist.txt";
        private readonly string _contactlessFile = ContactlessDirectory + Path.DirectorySeparatorChar + "contactless.properties";
        private readonly string _mediaFile = MediaDirectory + Path.DirectorySeparatorChar + "media.txt";
        private readonly string _softwareFile = SoftwareDirectory + Path.DirectorySeparatorChar + "OPTApp_1.2.pkg";
        private readonly string _upgradeFile = UpgradeDirectory + Path.DirectorySeparatorChar + $"{ConfigConstants.ServiceName}.exe";
        private readonly string _rollbackFile = RollbackDirectory + Path.DirectorySeparatorChar + $"{ConfigConstants.ServiceName}.exe";

        private readonly IHydraDb _hydraDb;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IHtecLogger _logger;
        private readonly IFromOptWorker _optWorker;
        private readonly IControllerWorker _controllerWorker;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITimerFactory _timerFactory;
        private readonly IFileSystem _fileSystem;

        public ConfigUpdateWorkerIntegrationTests()
        {
            _optWorker = Substitute.For<IFromOptWorker>();
            _controllerWorker = Substitute.For<IControllerWorker>();
            _hydraDb = Substitute.For<IHydraDb>();
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _fileSystem = Substitute.For<IFileSystem>();
        }

        [Fact]
        public void test_check_whitelist_files()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", WhitelistDirectory, "", "", "", "", "", "", "", "", "", "", "",
                "", "", "", "", true, "", "", "", false, false, false, false));
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_whitelistFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _optWorker.Received(1).CheckWhitelistFiles();
            _controllerWorker.Received(4).PushChange(EventType.AboutChanged);
        }

        [Fact]
        public void test_check_contactless_properties_file()
        {
            // Arrange
            _paymentConfig.CurrentContactlessFile.Returns(_contactlessFile);
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_contactlessFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _optWorker.Received(1).CheckContactlessProperties();
        }

        [Fact]
        public void test_check_media_files()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", "", "", "", "", "", "", "", MediaDirectory, "", "", "", "", "",
                "", "", "", true, "", "", "", false, false, false, false));
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_mediaFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _optWorker.Received(1).CheckMediaFiles();
            _controllerWorker.Received(4).PushChange(EventType.AboutChanged);
        }

        [Fact]
        public void test_check_software_files()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", "", "", SoftwareDirectory, "", "", "", "", "", "", "", "", "",
                "", "", "", "", true, "", "", "", false, false, false, false));
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_softwareFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _optWorker.Received(2).CheckSoftwareFiles();
            _controllerWorker.Received(4).PushChange(EventType.AboutChanged);
        }

        [Fact]
        public void test_check_upgrade_files()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", "", "", "", "", "", UpgradeDirectory, "", "", "", "", "", "",
                "", "", "", "", true, "", "", "", false, false, false, false));
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_upgradeFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _controllerWorker.Received(4).PushChange(EventType.AboutChanged);
        }

        [Fact]
        public void test_check_rollback_files()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", "", "", "", "", "", "", RollbackDirectory, "", "", "", "", "",
                "", "", "", "", true, "", "", "", false, false, false, false));
            var configUpdateWorker = CreateDefaultConfigUpdateWorker();
            configUpdateWorker.RegisterWorker(_optWorker);
            configUpdateWorker.RegisterWorker(_controllerWorker);
            configUpdateWorker.Start();

            // Act
            Thread.Sleep(200);
            // ReSharper disable once UnusedVariable
            FileInfo info = new FileInfo(_rollbackFile)
            {
                LastWriteTime = DateTime.Now
            };
            Thread.Sleep(200);

            // Assert
            _optWorker.Received(2).CheckRollbackFiles();
        }

        #region Helper Methods

        private IConfigUpdateWorker CreateDefaultConfigUpdateWorker()
        {
            return new ConfigUpdateWorker(_hydraDb, _paymentConfig, _logger, _configurationManager, _timerFactory, _fileSystem);
        }

        #endregion
    }
}
