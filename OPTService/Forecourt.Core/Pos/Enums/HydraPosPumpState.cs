// ReSharper disable UnusedMember.Global

namespace Forecourt.Core.Pos.Enums
{
    public enum HydraPosPumpState
    {
        Closed = 0,
        Idle = 2,
        PrintErr = 3,
        CardCheck = 4,
        TakeFuel = 10,
        InDelivery = 12,
        Complete = 13,
        Printing = 16,
        KioskUse = 19,
        OfferKiosk = 20,
        MixModePrintErr = 35,
    }

    public enum HydraPosEmvPumpState
    {
        Closed = 0,
        Initialise = 1,
        Idle = 2,
        PrintErr = 3,
        CardCheck = 4,
        HotCard = 5,
        BadCard = 6,
        AskReg = 7,
        AskMiles = 8,
        ReturnCard = 9,
        TakeFuel = 10,
        Cancel = 11,
        InDelivery = 12,
        Complete = 13,
        SelectLimit = 14,
        OfferRcptAfter = 15,
        Printing = 16,
        PumpOff = 17,
        NoReg = 18,
        KioskUse = 19,
        OfferKiosk = 20,
        WashOffer = 21,
        WashNumber = 22,
        FetchWash = 23,
        WashFailed = 24,
        WashPrint = 25,
        AppSelect = 26,
        LoyaltyOffer = 27,
        LoyaltyCardRead = 28,
        LoyaltyFetch = 29,
        LoyaltyBadRead = 30,
        ValidateCashCard = 31,
        ReadOnRemoval = 32,
        ReinsertRcpt = 33,
        UpdateOasis = 34,
        MixModePrintErr = 35,
        CashAccepted = 36,
        IdleBna = 37,
        PrintErrBna = 38,
        OfferKioskBna = 39,
        MixModePrintErrBna = 40,
        Unavailable = 41,
        WashReady = 42,
        WashInUse = 43,
        WashComplete = 44,
        DiscountAccepted = 45,
        DiscountRefused = 46,
        LoyReadNoPrint = 47,
        ReceiptOption = 48,
        WrongNozzle = 49,
        LoyaltyRemoveCard = 50,
        Tampered = 51,
        StoringTrans = 52,
        MaxState = 53
    }

    public enum HydraPosPodPumpState
    {
        Closed = 0,
        Initialise = 1,
        Idle = 2,
        PrintErr = 3,
        SelectPump = 4,
        NoPumpAvail = 5,
        UpdateOasis = 6,
        Printing = 7,
        ReadOnRemoval = 8,
        CardCheck = 9,
        AppSelect = 10,
        HotCard = 11,
        BadCard = 12,
        AskReg = 13,
        AskMiles = 14,
        ReturnCard = 15,
        WashOffer = 16,
        WashNumber = 17,
        FetchWash = 18,
        WashFailed = 19,
        WashPrint = 20,
        TakeFuel = 21,
        Cancel = 22,
        SelectLimit = 23,
        ValidateCashCard = 24,
        DiscountAccepted = 25,
        DiscountRefused = 26,
        ReceiptOption = 27,
        WrongNozzle = 28,
        PumpOrWash = 29,
        SelectWash = 30,
        NoWashAvail = 31,
        UseWash = 32,
        SupervisorAccepted = 33,
        SupervisorRequired = 34,
        SelectGrade = 35,
        CockpitCheck = 36,
        AccountOrPayment = 37,
        PaymentAfterLAcard = 38,
        Tampered = 39,
        MaxState = 40
    }
}