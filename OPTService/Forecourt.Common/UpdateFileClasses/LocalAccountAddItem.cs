using Forecourt.Core.UpdateFileClasses.Interfaces;

namespace OPT.Common.UpdateFileClasses
{
    public class LocalAccountAddItem : IUpdateItem
    {
        public string RecordType { get; set; }
        public string Action { get; set; }
        public string Reference { get; set; }
        public string Name { get; set; }
        public string Active { get; set; }
        public uint TransactionLimit { get; set; }
        public string FuelOnly { get; set; }
        public string Registration { get; set; }
        public string Miles { get; set; }
        public string CreditStatus { get; set; }

        public bool IsTransactionsAllowed()
        {
            return Active.Equals("Y");
        }

        public bool IsFuelOnly()
        {
            return FuelOnly.Equals("Y");
        }
        public bool IsRegistrationEntry()
        {
            return Registration.Equals("Y");
        }
        public bool IsMileageEntry()
        {
            return Miles.Equals("Y");
        }

        public bool IsPrePayAccount()
        {
            return CreditStatus.Equals("P");
        }
        public bool IsLowCreditWarning()
        {
            return CreditStatus.Equals("L");
        }
        public bool IsMaxCreditReached()
        {
            return CreditStatus.Equals("M");
        }

        public string CreditStatusString()
        {
            switch (CreditStatus)
            {
                case "P":
                    return "Pre-Pay Account";
                case "L":
                    return "Low Credit Warning";
                case "M":
                    return "Max Credit Reached";
                default:
                    return string.Empty;
            }
        }
    }
}
