namespace OPT.Common.HydraDbClasses
{
    public class LoyaltyTerminal
    {
        public string SiteId { get; }
        public string TerminalId { get; }
        public string Footer1 { get; }
        public string Footer2 { get; }
        public int Timeout { get; }
        public string ApiKey { get; }
        public string HttpHeader { get; }

        // ReSharper disable once UnusedMember.Global
        public LoyaltyTerminal(string siteId, string terminalId, string footer1, string footer2, int timeout, string apiKey, string httpHeader)
        {
            SiteId = siteId;
            TerminalId = terminalId;
            Footer1 = footer1;
            Footer2 = footer2;
            Timeout = timeout;
            ApiKey = apiKey;
            HttpHeader = httpHeader;
        }
    }
}