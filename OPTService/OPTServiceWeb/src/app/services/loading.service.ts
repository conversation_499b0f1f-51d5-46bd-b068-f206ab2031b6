import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {

  /**
   * Timestamp of the moment the web was connected to OPT Service
   */
  private _lastRefresh: Date = undefined;

  /**
   * Flag to show or hide the loading screen
   */
  private _showLoading: boolean = false;

  /**
   * Indicates the number of loading screens displayed simultaneously
   */
  private _activeLoadings: number = 0;

  /**
   * Flag to show whether an error occurred during loading
   */
  private _showError: boolean = false;

  constructor() { }

  /**
   * showLoading getter
   */
  get showLoading(): boolean {
    return this._showLoading;
  }

  /**
   * showError getter
   */
  get showError(): boolean {
    return this._showError;
  }

  /**
   * lastRefresh getter
   */
  get lastRefresh(): Date {
    return this._lastRefresh;
  }

  /**
   * Displays the loading screen. lastrefresh value is unset
   */
  showLoadingScreen(): void {
    this._activeLoadings ++;
    this._showLoading = true;
    this._showError = false;
    this._lastRefresh = undefined;
  }

  /**
   * Hides the loading screen. lastRefresh is set to the current datetime
   */
  hideLoadingScreen(): void {
    if(this._activeLoadings > 0){
      this._activeLoadings --;
    }    
    if(this._activeLoadings <= 0){
      this._showLoading = false;
      this._lastRefresh = new Date();
    }    
  }

  /**
   * Hides the loading screen. showError is set to true
   */
  errorDuringLoading(): void {
    if (this._showLoading) {
      if (this._activeLoadings > 0) {
        this._activeLoadings --;
      }    
      if (this._activeLoadings <= 0) {
        this._showLoading = false;
      } 
    }
    this._showError = true;
  }

  /**
   * lastRefresh is set to the current datetime
   */
  updateLastRefresh(): void {
    this._lastRefresh = new Date();
  }
}
