using CSharpFunctionalExtensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Web.Http;
using Htec.Hydra.Core.Bos.Interfaces;
using OPTService.OPTServiceClasses;
using System;
using System.IO.Ports;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]
    public class ShiftController : LinkedLoggableApiController<IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>>
    {
        /// <summary>
        /// The web name
        /// </summary>
        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="loggable">The <see cref="IBosIntegratorInJournal{TMessageTracking, TResult}"/> instance</param>
        public ShiftController([Dependency(UnityConfig.MessageBrokerName)] IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>> loggable, IWeb web) : base(loggable as Loggable)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        /// <summary>
        /// Perform Day End.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public async Task<IHttpActionResult> PerformDayEnd()
        {
            _web.SendChatMessage(WebName, "Day End");
            return await DoHttpActionAsync(async () => Loggable.RequestDayEnd()).ConfigureAwait(false);
        }

        /// <summary>
        /// Perform Shift End.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public async Task<IHttpActionResult> PerformShiftEnd()
        {
            _web.SendChatMessage(WebName, "Shift End");
            return await DoHttpActionAsync(async () => Loggable.RequestShiftEnd()).ConfigureAwait(false);
        }

        /// <summary>
        /// Clears Auto Day End.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage ClearAutoDayEnd()
        {
            _web.SendChatMessage(WebName, "Clear Auto Day End");
            string result = _web.SetNextDayEnd(null);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Auto Day End time.
        /// </summary>
        /// <param name="dayEnd">Time to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage SetNextDayEnd(DateTime dayEnd)
        {
            _web.SendChatMessage(WebName, $"Set Auto Day End {dayEnd}");
            string result = _web.SetNextDayEnd(dayEnd);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set log interval.
        /// </summary>
        /// <param name="details">Interval to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLogInterval(LogIntervalDetails details)
        {
            int hours = details.Hours;
            int minutes = details.Minutes;
            int seconds = details.Seconds;
            _web.SendChatMessage(WebName, $"Set Log Interval {hours} hours, {minutes} minutes, {seconds} seconds");
            string result = _web.SetLogInterval(hours, minutes, seconds);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Asda Day End Report On.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage SetAsdaDayEndReportOn()
        {
            _web.SendChatMessage(WebName, "Asda Day End Report On");
            string result = _web.SetAsdaDayEndReport(true);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Asda Day End Report Off.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage SetAsdaDayEndReportOff()
        {
            _web.SendChatMessage(WebName, "Asda Day End Report Off");
            string result = _web.SetAsdaDayEndReport(false);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set printer enabled.
        /// </summary>
        /// <param name="details">Includes enabled flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterEnabled(PrinterDetails details)
        {
            bool isEnabled = details.Enabled;
            _web.SendChatMessage(WebName, $"Set Printer Enabled {OnOff(isEnabled)}");
            string result = _web.SetPrinterEnabled(isEnabled);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set printer port name.
        /// </summary>
        /// <param name="details">Includes port name.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterPortName(PrinterDetails details)
        {
            string portName = details.PortName?.Trim();
            if (string.IsNullOrEmpty(portName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Port name is empty");
            }

            _web.SendChatMessage(WebName, $"Set Printer Port Name {portName}");
            string result = _web.SetPrinterPortName(portName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set printer baud rate.
        /// </summary>
        /// <param name="details">Includes baud rate.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterBaudRate(PrinterDetails details)
        {
            int baudRate = details.BaudRate;
            _web.SendChatMessage(WebName, $"Set Printer Baud Rate {baudRate}");
            string result = _web.SetPrinterBaudRate(baudRate);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set printer handshake.
        /// </summary>
        /// <param name="details">Includes handshake.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterHandshake(PrinterDetails details)
        {
            string handshakeString = details.Handshake?.Trim()??string.Empty;
            if (Enum.TryParse(handshakeString, out Handshake handshake))
            {
                _web.SendChatMessage(WebName, $"Set Printer Handshake {handshake}");
                string result = _web.SetPrinterHandshake(handshake);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, $"Unable to parse {handshakeString}");
            }
        }

        /// <summary>
        /// Set printer stop bits.
        /// </summary>
        /// <param name="details">Includes stop bits.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterStopBits(PrinterDetails details)
        {
            string stopBitsString = details.StopBits?.Trim()??string.Empty;
            if (Enum.TryParse(stopBitsString, out StopBits stopBits))
            {
                _web.SendChatMessage(WebName, $"Set Printer Stop Bits {stopBits}");
                string result = _web.SetPrinterStopBits(stopBits);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, $"Unable to parse {stopBitsString}");
            }
        }

        /// <summary>
        /// Set printer data bits.
        /// </summary>
        /// <param name="details">Includes data bits.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPrinterDataBits(PrinterDetails details)
        {
            int dataBits = details.DataBits;
            if (dataBits < 5 || dataBits > 8)
            {
                return Request.CreateResponse( HttpStatusCode.BadRequest, "Invalid value for Data Bits");
            }
            _web.SendChatMessage(WebName, $"Set Printer Data Bits {dataBits}");
            string result = _web.SetPrinterDataBits(dataBits);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        private static string OnOff(bool isOn)
        {
            return isOn ? "On" : "Off";
        }
 
        /// <summary>
        /// Gets the ShiftEnd details.
        /// </summary>
        /// <param name="reference">The Logging reference</param>
        /// <response code="200">Success.</response>
        /// <response code="404">No ShiftEnd configuration found.</response>
        /// <returns>ShiftEnd detail object.</returns>
        [HttpGet]
        public IHttpActionResult GetShiftEndInfo(string reference = null)
        {
            var result = _web.GetShiftEndDetails();

            return (result is null) ? NotFound() : Ok(result);
        }
    }
}