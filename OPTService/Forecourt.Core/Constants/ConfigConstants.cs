using OPT.Common.Constants;
using ConnectionsConstants = Htec.Foundation.Connections.Common.Constants;

namespace OPT.Common
{
    /// <summary>
    /// Configuration Constants.
    /// </summary>
    public static class ConfigConstants
    {
        /// <summary>
        /// Default value, for client name
        /// </summary>
        public const string DefaultValueClientName = "Morrisons";

        /// <summary>
        /// Global name for the current service
        /// </summary>
        public const string ServiceName = "Forecourt.Service";
        
        /// <summary>
        /// Service file name
        /// </summary>
        public const string ServiceFileName = "OPTService.exe";

        /// <summary>
        /// Configuration Category for all things Connectivity-wise.
        /// </summary>
        public const string ConfigKeyCategoryConnectivity = ConnectionsConstants.ConfigKeyCategoryConnectivity;

        /// <summary>
        /// Config key prefix, for the Timer poll interval in milliseconds.  Suffix with the instance the timer runs in for uniqueness.
        /// </summary>
        public const string ConfigKeyPrefixTimerIntervalMilliseconds = ConfigKeyCategoryConnectivity + "Timer:Interval:Milliseconds:";

        /// <summary>
        /// Config key prefix, for the poll interval in milliseconds.  Suffix with the instance the timer runs in for uniqueness.
        /// </summary>
        public const string ConfigKeyPrefixPollIntervalMilliseconds = ConfigKeyCategoryConnectivity + "Poll:Interval:Milliseconds:";

        /// <summary>
        /// Standard marker/header constant for OnPumpState logs
        /// </summary>
        public const string HeaderOnPumpState = "OnPumpState";

        /// <inheritdoc cref="ConfigurationConstants"/>
        public const string Unknown = ConfigurationConstants.Unknown;

        /// <inheritdoc cref="ConfigurationConstants"/>
        public const string ConfigKeyDebugStartDelayInterval = ConfigurationConstants.ConfigKeyDebugStartDelayInterval;

        /// <inheritdoc cref="ConfigurationConstants"/>
        public const string DefaultValueDebugStartDelayInterval = ConfigurationConstants.DefaultValueDebugStartDelayInterval;

        /// <inheritdoc cref="LoggingConstants"/>
        public const string UnityDependencyWebLogger = LoggingConstants.UnityDependencyWebLogger;

        /// <summary>
        /// Configuration Category for all things logging.
        /// </summary>
        public const string ConfigCategoryLogging = Forecourt.Core.Configuration.Constants.ConfigCategoryLogging;

        /// <summary>
        /// Config key for the date format for log folders.
        /// </summary>
        public const string ConfigKeyLogFolderDateFormat = ConfigCategoryLogging + "LogFolderDateFormat";

        /// <summary>
        /// Default value for date format for log folders.
        /// </summary>
        public const string DefaultValueLogFolderDateFormat = "yyyy-MM-dd";

        /// <summary>
        /// Logger name prefix for general service logs.
        /// </summary>
        public const string LoggerPrefixOptService = "OptService.";

        /// <summary>
        /// Logger name prefix for workers.
        /// </summary>
        public const string LoggerPrefixWorker = "Worker.";

        /// <summary>
        /// Logger name prefix for connection threads.
        /// </summary>
        public const string LoggerPrefixConnection = "Connection.";

        /// <summary>
        /// Logger name prefix for database interactions.
        /// </summary>
        public const string LoggerPrefixDatabase = "Database.";

        public const string ConfigKeyCustomDataOverride = "CustomDataOverride";

        public const string ConfigCategoryOpt = Forecourt.Core.Configuration.Constants.ConfigCategoryOpt;

        /// <summary>
        /// Constant for the simple name of the HydraDb
        /// </summary>
        public const string HydraDbName = "Hydra";

        /// <summary>
        /// Default value for, the payment timeout (secs)
        /// </summary>
        public const int DefaultPaymentTimeoutInSeconds = 30;

        /// <summary>
        /// Default value, None
        /// </summary>
        public const string None = "None";

        /// <summary>
        /// Default value, NONE
        /// </summary>
        public const string NoneUpper = "NONE";

        /// <summary>
        /// SignalR marker/header constant
        /// </summary>
        public const string HeaderSignalR = "SignalR";

        /// <summary>
        /// Name value, OPT (Normal)
        /// </summary>
        public const string NameOpt= "Opt";

        /// <summary>
        /// Name value, OPT (upper)
        /// </summary>
        public const string NameOptUpper = "OPT";

        /// <summary>
        /// Name value, Pump (normal)
        /// </summary>
        public const string NamePump = "Pump";

        /// <summary>
        /// Name value, Pump (upper)
        /// </summary>
        public const string NamePumpUpper = "PUMP";

        /// <summary>
        /// Default value, when something is not connected
        /// </summary>
        public const string NotConnected = "Not connected";

        /// <summary>
        /// Standard text to search for within a receipt, to indicate this is the customer copy
        /// </summary>
        public const string CustomerCopy = "Text=\"Customer Copy\"";

        /// <summary>
        /// Constant for interval
        /// </summary>
        public const string Interval = "Interval";

        /// <summary>
        /// Constant for uppercase interval
        /// </summary>
        public const string IntervalUpper = "INTERVAL";

        /// <summary>
        /// API key for WebUI usage
        /// </summary>
        public const string WebUiApiKey = "06AB47638CED484C80EBEB40100B0AA8";

        /// <summary>
        /// API key for Swagger usage
        /// </summary>
        public const string SwaggerApiKey = "0C14BCC8424A4B70A39D56687AF09DC2";

        /// <summary>
        /// API key for Third party usage
        /// </summary>
        public const string ThirdPartyApiKey = "3C84F274D0054A71B598B923C5F5C850";

        /// <summary>
        /// API key for Orbis
        /// </summary>
        public const string OrbisApiKey = "57EA95ABE5994760AAD00389B71BD5AE";

        /// <summary>
        /// Constant for EndPoint 
        /// </summary>
        public const string EndPoint = "EndPoint";

        /// <summary>
        /// Constant for uppercase EndPoint 
        /// </summary>
        public const string EndPointUpper = "ENDPOINT";
    }
}
