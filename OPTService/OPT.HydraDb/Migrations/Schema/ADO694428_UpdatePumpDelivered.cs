using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(26200004, "ADO#694428 - Add dbo.PumpDelivered.[Hose]"), Tags(Constants.MigrationTypeSchema)]
    public class ADO694428_UpdatePumpDelivered : Migrationable
    {
        private const string TableName = "PumpDelivered";
        private const string ColumnName = "Hose";

        /// <inheritdoc/>
        public ADO694428_UpdatePumpDelivered(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {            
        }

        /// <inheritdoc/>
        public override void Up()
        {
            if (!Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Alter.Table(TableName).AddColumn(ColumnName).AsByte().Nullable();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            if (Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Delete.Column(ColumnName).FromTable(TableName);
            }
        }
    }
}
