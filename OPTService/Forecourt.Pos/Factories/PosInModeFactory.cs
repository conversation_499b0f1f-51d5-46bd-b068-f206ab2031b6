using Forecourt.Core.Pos.Enums;
using Forecourt.Pos.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Pos.Factories
{
    /// <summary>
    /// POS Integrator In Mode factory
    /// </summary>
    public class PosInModeFactory : PosIntegratorFactory<IPosIntegratorInMode<IMessageTracking>>, IPosInModeFactory
    {
        /// <inheritdoc/>
        public PosInModeFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IPosIntegratorInMode<IMessageTracking>> resolveTypeInstance) : 
            base(logManager, nameof(PosInModeFactory), configurationManager, resolveTypeInstance)
        {
            AddItem($"{PosType.None}", ConfigConstants.None, (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.HydraPos}", "Htec HydraPOS", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.Retalix}", "NCR Retalix", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.GenericSignalRApi}", "Generic SignalR and RestAPI", (key) => ResolveTypeInstance(key));
            AddItem($"{PosType.MadicApiSignalR}", "MADIC RestAPI and SignalR", (key) => ResolveTypeInstance(key));
        }
    }
}
