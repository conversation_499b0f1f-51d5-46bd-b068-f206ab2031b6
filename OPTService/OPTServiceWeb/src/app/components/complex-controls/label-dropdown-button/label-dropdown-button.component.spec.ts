import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormGroup, FormGroupDirective, ControlContainer } from '@angular/forms';

import { LabelDropdownButtonComponent } from './label-dropdown-button.component';

describe('LabelDropdownButtonComponent', () => {
  let component: LabelDropdownButtonComponent;
  let fixture: ComponentFixture<LabelDropdownButtonComponent>;

  const fg: FormGroup = new FormGroup({});
  const fgd: FormGroupDirective = new FormGroupDirective([], []);
  fgd.form = fg;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ LabelDropdownButtonComponent ],
      providers: [
        { provide: ControlContainer, useValue: fgd },
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LabelDropdownButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('fieldFeedbackId', () => {

    it('should set fieldFeedbackId when no ID set', () => {
      expect(component.fieldFeedbackId).toEqual('FieldFeedback');
    });

    it('should set fieldFeedbackId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.fieldFeedbackId).toEqual('inputFieldFeedback');
    });
  });
  
  describe('buttonId', () => {

    it('should set buttonId when no ID set', () => {
      expect(component.buttonId).toEqual('Btn');
    });

    it('should set buttonId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.buttonId).toEqual('inputBtn');
    });
  });
    
  describe('errorId', () => {

    it('should set errorId when no ID set', () => {
      expect(component.errorId).toEqual('Error');
    });

    it('should set errorId using ID when set', () => {
      component['_id'] = 'input';

      expect(component.errorId).toEqual('inputError');
    });
  });
});
