using System;

namespace OPTService.OPTServiceClasses
{
    public class FuelTransactionDetails
    {
        public long TransactionId { get; set; }
        public DateTime TransactionTime { get; set; }
        public string GradeCode { get; set; }
        public string WashCode { get; set; }
        public string GradeName { get; set; }
        public string WashName { get; set; }
        public string PumpDetailsString { get; set; }
        public string CardNumber { get; set; }
        public float FuelQuantity { get; set; }
        public uint WashQuantity { get; set; }
        public float Amount { get; set; }
        public string FuelCategory { get; set; }
        public string WashCategory { get; set; }
        public string FuelSubcategory { get; set; }
        public string WashSubcategory { get; set; }
        public string DiscountName { get; set; }
        public string DiscountCode { get; set; }
        public float DiscountValue { get; set; }
        public string DiscountCardNumber { get; set; }
        public uint LocalAccountMileage { get; set; }
        public string LocalAccountRegistration { get; set; }
        public string TxnNumber { get; set; }
        public bool HasReceipt { get; set; }
        public bool PrinterEnabled { get; set; }
    }
}