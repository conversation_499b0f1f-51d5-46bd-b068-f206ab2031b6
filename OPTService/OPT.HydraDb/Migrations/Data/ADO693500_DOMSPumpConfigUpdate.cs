using FluentMigrator;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed for DOMS/Pump workers
    /// </summary>
    [Migration(26200003, "ADO#693500 - Set further configuration needed for revised DOMS/Pump workers"), Tags(Constants.MigrationTypeData)]
    public class ADO693500_DOMSPumpConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);

            if (client.ToLower() == ClientSettings.ClientNameMADIC)
            {

            }

            // Timers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:DomsSetupTcp+EmbeddedTimerable', @value = '00:00:00.5', @category = '{cfgConstants.CategoryNameTimers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:PumpWorker+EmbeddedTimerable', @value = '00:00:00.75', @category = '{cfgConstants.CategoryNameTimers}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
