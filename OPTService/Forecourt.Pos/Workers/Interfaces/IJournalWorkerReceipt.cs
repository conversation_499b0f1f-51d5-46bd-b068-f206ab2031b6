using CSharpFunctionalExtensions;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using DiscountItem = Htec.Hydra.Core.Bos.Messages.DiscountItem;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;

namespace Forecourt.Pos.Workers.Interfaces
{
    /// <summary>
    /// 
    /// </summary>
    public interface IJournalWorkerReceipt : IWorkerable
    {
        /// <summary>
        /// Store Receipt details.
        /// </summary>
        /// <param name="id">Id of OPT.</param>
        /// <param name="receiptTransaction">Receipt details</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result StoreReceipt(int id, ReceiptTransaction receiptTransaction, IMessageTracking message);

        /// <summary>
        /// Store Receipt details.
        /// </summary>
        /// <param name="opt">Internal Opt instance</param>
        /// <param name="receiptTransaction">Receipt details</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result StoreReceipt(IOptCore opt, ReceiptTransaction receiptTransaction, IMessageTracking message);

        /// <summary>
        /// Respond to the payment being cleared
        /// </summary>
        /// <param name="result">The <see cref="PaymentResult"/> instance</param>
        /// <param name="discount">The <see cref="DiscountItem"/> instance for the payment</param>
        /// <param name="thePump">The <see cref="IPump"/> instance</param>
        /// <param name="opt">The <see cref="IOpt"/> instance</param>
        /// <param name="message">Current message</param>
        /// <param name="payment">The <see cref="Htec.Hydra.Messages.Opt.Models.PaymentCancelled"/> instance</param>
        /// <returns>Result</returns>
        Result PaymentCleared(PaymentResult result, DiscountItem discount, IPump thePump, IOptCore opt, IMessageTracking message, PaymentCleared payment);
    }
}
