using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    /// <inheritdoc/>
    [Migration(20050004, "ADO#646362 - Add dbo.TransactionBookingState table"), Tags(Constants.MigrationTypeSchema)]
    public class ADO646362_Add_TransactionBookingState : Migrationable
    {
        private const string TableName = "TransactionBookingState";

        /// <inheritdoc/>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.dbo.{TableName}.sql");
        }

        /// <inheritdoc/>
        public override void Down()
        {
            if (Schema.Table(TableName).Exists())
            {
                Delete.Table(TableName);
            }
        }
    }
}
