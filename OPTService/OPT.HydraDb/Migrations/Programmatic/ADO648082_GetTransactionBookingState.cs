using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(2005015, "ADO#648082 - Update dbo.GetTransactionBookingState stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO648082_GetTransactionBookingState : Migrationable
    {
        /// <inheritdoc/>
        public ADO648082_GetTransactionBookingState(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetTransactionBooking" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO648082_GetTransactionBookingState), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO646362_GetTransactionBookingState), DbObjects.Reverse().ToArray());
        }
    }
}
