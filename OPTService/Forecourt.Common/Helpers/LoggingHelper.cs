using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using Htec.Logger.log4net.Helpers;
using Htec.Logger.log4net.Helpers.Interfaces;
using log4net;
using log4net.Appender;
using log4net.Config;
using log4net.Core;
using log4net.Filter;
using log4net.Layout;
using log4net.Repository;
using System;
using System.IO.Abstractions;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Xml;

namespace OPT.Common.Helpers
{
    /// <summary>
    /// Helper for handling common logging methods.
    /// </summary>
    [HasConfiguration]
    public class LoggingHelper : Loggable, ILoggingHelper
    {
        public const string LogConfigFile = "Forecourt.Service.log4net.xml";
        private const string UnmannedReposName = "UnmannedRepos";
        private const string UnmannedJournalName = "UnmannedJournal";

        private readonly string _exceptionMessage = $"Logging config file '{LogConfigFile}' failed to load. File could be corrupt";

        private readonly IXmlConfigHelper _xmlConfigHelper;

        /// <summary>
        /// Creates a new instance of the <see cref="LoggingHelper"/> class.
        /// </summary>
        /// <param name="fileSystem">The abstracted file system.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="configurationManager">The abstracted configuration manager.</param>
        /// <param name="fileSavingHelper">Helper for saving files safely.</param>
        public LoggingHelper(IFileSystem fileSystem, IHtecLogger logger, IConfigurationManager configurationManager, IFileSavingHelper fileSavingHelper)
            : base(logger, configurationManager)
        {
            if (fileSystem == null)
            {
                throw new ArgumentNullException(nameof(fileSystem));
            }

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            if (fileSavingHelper == null)
            {
                throw new ArgumentNullException(nameof(fileSavingHelper));
            }

            _xmlConfigHelper = new XmlConfigHelper(Logger, ConfigurationManager, fileSystem.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, LogConfigFile), fileSavingHelper);
        }

        #region IXmlConfigHelper passthroughs
        
        /// <inheritdoc />
        public bool SumoLogicEnabled => false;

        /// <inheritdoc />
        public string SumoLogicInfoLoggers => _xmlConfigHelper.SumoLogicInfoLoggers;

        /// <inheritdoc />
        public string SumoLogicExternalLoggers => _xmlConfigHelper.SumoLogicExternalLoggers;

        /// <inheritdoc />
        public string SumoLogicAppenderName => _xmlConfigHelper.SumoLogicAppenderName;

        /// <inheritdoc />
        public string SumoLogicInfoAppenderName => _xmlConfigHelper.SumoLogicInfoAppenderName;

        /// <inheritdoc />
        public Result SetLogFileLocation(string appenderName, string fileName)
        {
            try
            {
                return _xmlConfigHelper.SetLogFileLocation(appenderName, fileName);
            }
            catch (XmlException e)
            {
                //Not logging this as it means logging config is broken
                throw new XmlException(_exceptionMessage, e);
            }
        }

        /// <inheritdoc />
        public Result SetSumoLogicLogging(bool? enabled = null, string infoLoggers = null, string externalLoggers = null)
        {
            try
            {
                return _xmlConfigHelper.SetSumoLogicLogging(false, infoLoggers, externalLoggers);
            }
            catch (XmlException e)
            {
                //Not logging this as it means logging config is broken
                throw new XmlException(_exceptionMessage, e);
            }
        }

        #endregion

        /// <inheritdoc cref="MiscHelper"/>
        public static void SetThreadName(string type, [CallerMemberName] string method = null) => MiscHelper.SetThreadName(type, method);

        /// <inheritdoc />
        public void WriteDayEndJournal(StringBuilder entry)
        {
            DoAction(() =>
            {
                ILog unmannedJournal = log4net.LogManager.GetLogger(UnmannedReposName, UnmannedJournalName);
                unmannedJournal.Info(entry.ToString());
            }, string.Empty);
        }

        /// <inheritdoc />
        public void CheckUnmannedJournal(string unmannedJournalFile)
        {
            DoAction(() =>
            {
                if (log4net.LogManager.GetAllRepositories().FirstOrDefault(x => x.Name.Equals(UnmannedReposName)) == null)
                {
                    ILoggerRepository repos = log4net.LogManager.CreateRepository(UnmannedReposName);
                    RollingFileAppender appender =
                        (RollingFileAppender)repos.GetAppenders()
                            .FirstOrDefault(x => x is RollingFileAppender && x.Name.Equals(UnmannedJournalName)) ?? new RollingFileAppender
                            {
                                Name = UnmannedJournalName,
                                File = unmannedJournalFile,
                                ImmediateFlush = true,
                                AppendToFile = true,
                                DatePattern = "'.'yyMMdd",
                                RollingStyle = RollingFileAppender.RollingMode.Composite,
                                LockingModel = new FileAppender.MinimalLock(),
                                Layout = new PatternLayout("%message %newline"),
                                StaticLogFileName = false,
                                PreserveLogFileNameExtension = true,
                                MaximumFileSize = "10MB"
                            };
                    LevelMatchFilter filter = new LevelMatchFilter { LevelToMatch = Level.All };
                    filter.ActivateOptions();
                    appender.ClearFilters();
                    appender.AddFilter(filter);
                    appender.ActivateOptions();
                    BasicConfigurator.Configure(repos, appender);
                }
            }, string.Empty);
        }
    }
}
