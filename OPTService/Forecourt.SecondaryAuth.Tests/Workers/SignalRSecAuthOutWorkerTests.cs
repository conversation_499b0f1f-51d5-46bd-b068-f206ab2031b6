using CSharpFunctionalExtensions;
using FluentAssertions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using NSubstitute;
using System;
using Xunit;

namespace Forecourt.SecondaryAuth.Tests.Workers
{
    public class SignalRSecAuthOutWorkerTests
    {
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pump;
        private readonly ISecAuthIntegratorOut<IMessageTracking> _workerPre;
        private readonly ISecAuthIntegratorOut<IMessageTracking> _workerPost;
        private readonly IHubbable<SecAuthMessage, IMessageTracking> _preHub;
        private readonly IHubbable<SecAuthMessage, IMessageTracking> _postHub;

        public SignalRSecAuthOutWorkerTests()
        {
            _logManager = Substitute.For<IHtecLogManager>();
            _configurationManager = Substitute.For<IConfigurationManager>();

            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _allPumps = Substitute.For<IPumpCollection>();
            _pump = Substitute.For<IPump>();
            _allPumps.TryGetPump(Arg.Is<byte>(1), out var p).Returns(true);
            _allPumps.When(x => x.TryGetPump(Arg.Is<byte>(1), out var p1)).Do(c =>
            {
                c[1] = _pump;
            });

            _preHub = Substitute.For<IHubbable<SecAuthMessage, IMessageTracking>>();
            _postHub = Substitute.For<IHubbable<SecAuthMessage, IMessageTracking>>();

            _workerPre = new SignalRPreSecAuthOutWorker(_logManager, _configurationManager, _allPumps, _telemetryWorker, _preHub);
            _workerPost = new SignalRPostSecAuthOutWorker(_logManager, _configurationManager, _allPumps, _telemetryWorker, _postHub);
        }

        [Fact]
        public void for_postauth_check_that_preauth_does_not_action_secauth()
        {
            // Act
            check_that_secauth_action_does_not_action_secauth(_workerPost, (w, r, m) => w.PreAuthRequest(r, m));
        }

        [Fact]
        public void for_postauth_check_that_postauth_does_action_secauth()
        {
            // Act
            check_that_secauth_action_does_action_secauth(_workerPost, (w, r, m) => w.PostAuthRequest(r, m));
        }

        [Fact]
        public void for_preauth_check_that_preauth_does_action_secauth()
        {
            // Act
            check_that_secauth_action_does_action_secauth(_workerPre, (w, r, m) => w.PreAuthRequest(r, m));
        }

        [Fact]
        public void for_preauth_check_that_postauth_does_not_action_secauth()
        {
            // Act
            check_that_secauth_action_does_not_action_secauth(_workerPre, (w, r, m) => w.PostAuthRequest(r, m));
        }

        private void check_that_secauth_action_does_not_action_secauth(ISecAuthIntegratorOut<IMessageTracking> worker, Func<ISecAuthIntegratorOut<IMessageTracking>, SecAuthRequest, IMessageTracking, Result> action)
        { 
            // Arrange
            var request = new SecAuthRequest { Pump = 1 };
            var message = new MessageTracking() { IdAsString = "LOG-REF" };

            // Assert
            var result = action(worker, request, message);

            // Act
            result.IsSuccess.Should().BeFalse();
            _pump.Received(0).SecAuthRequestSent(Arg.Any<string>());
            //_pump.Received(0).SetSecAuthResponse(Arg.Any<bool>(), Arg.Any<string>());
        }

        private void check_that_secauth_action_does_action_secauth(ISecAuthIntegratorOut<IMessageTracking> worker, Func<ISecAuthIntegratorOut<IMessageTracking>, SecAuthRequest, IMessageTracking, Result> action)
        {
            // Arrange
            var request = new SecAuthRequest { Pump = 1 };
            var message = new MessageTracking() { IdAsString = "LOG-REF" };            

            // Assert
            var result = action(worker, request, message);

            // Act
            result.IsSuccess.Should().BeTrue();
            _pump.Received(1).SecAuthRequestSent(Arg.Any<string>());
            //_pump.Received(1).SetSecAuthResponse(Arg.Any<bool>(), Arg.Any<string>());
        }
    }
}
