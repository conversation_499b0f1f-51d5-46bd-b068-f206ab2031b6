import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-label-text',
  templateUrl: './label-text.component.html',
  styleUrls: ['./label-text.component.css']
})
export class LabelTextComponent implements OnInit {

  public form: FormGroup | undefined;
  public control : FormControl | undefined;

  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() placeHolderText: string = '';
  @Input() validationErrorText: string = 'Please provide a valid value';
  @Input() appendText: string = '';
  @Input() controlName: string = '';  
  @Input() errorInAction: boolean = false;
  @Input() labelColClass: string = 'col-2';
  @Input() textColClass: string = 'col-2';
  @Input() disabled: boolean = false;
  @Input() id: string | undefined;
  @Input() maxLength: number = 524288; //The default value for HTML input maxlength attribute
  
  @Output() action= new EventEmitter();

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
  }
}
