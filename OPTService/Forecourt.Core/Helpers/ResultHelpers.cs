using CSharpFunctionalExtensions;
using Htec.Common.Extensions;
using Htec.Foundation.Models;
using System;
using System.Net;

namespace Forecourt.Core.Helpers
{
    /// <summary>
    /// Any and all helper methods relevant to <see cref="Result"/> and <see cref="StatusCodeResult"/>
    /// </summary>
    public static class ResultHelpers
    {
        /// <summary>
        /// Maps a <see cref="Result"/> to a standard <see cref="Result{StatusCodeResult}"/>
        /// </summary>
        /// <param name="result">Source instance</param>
        /// <param name="statusCode">Default <see cref="HttpStatusCode"/> to return, if failure</param>
        /// <returns>Target instance</returns>
        public static Result<StatusCodeResult> MapResult(Result result, HttpStatusCode statusCode = HttpStatusCode.BadRequest) => 
            Result.Success(result.IsSuccess ? 
                StatusCodeResult.Success :
                StatusCodeResult.Specific(statusCode, result.Error.IsNullOrWhiteSpace() ? null : new Exception(result.Error)));
    }
}
