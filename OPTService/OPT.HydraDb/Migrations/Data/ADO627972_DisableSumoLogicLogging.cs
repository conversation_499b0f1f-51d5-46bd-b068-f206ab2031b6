using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set LOGGING::SumoLogicEnabled to false
    /// </summary>
    [Migration(20050010, "ADO#627972 - Disable Sumo Logic logging"), Tags(Constants.MigrationTypeData)]
    public class ADO627972_DisableSumoLogicLogging : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"UPDATE dbo.ConfigurationDetail SET [Value] = 'False' WHERE [Key] = 'SumoLogicEnabled'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // No restore
        }
    }
}
