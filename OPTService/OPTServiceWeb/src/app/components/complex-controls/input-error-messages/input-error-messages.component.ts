import { Component, Input } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Component({
  selector: 'app-input-error-messages',
  templateUrl: './input-error-messages.component.html',
  styleUrls: ['./input-error-messages.component.css']
})
export class InputErrorMessagesComponent {

  @Input() control: AbstractControl;

  public getErrorMessages(): string[] {
    const errorMessages = new Array<string>();
    const errors = this.control?.errors;
    if (errors) {
      Object.keys(errors).forEach((key) => {
        const error = errors[key];
        switch (key) {
          case 'pattern':
            errorMessages.push(`Invalid character entered`);
            break;
          case 'min':
            errorMessages.push(`Min value of ${error.min}`);
            break;
          case 'max':
            errorMessages.push(`Max value of ${error.max}`);
            break;
          case 'minlength':
            errorMessages.push(`Min length of ${error.requiredLength}`);
            break;
          case 'maxlength':
            errorMessages.push(`Max length of ${error.requiredLength}`);
            break;
          default:
            errorMessages.push(`Please provide a valid value`);
            break;
        }
      });
    }
    return errorMessages;
  }

}
