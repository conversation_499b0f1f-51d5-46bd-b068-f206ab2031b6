using Microsoft.Owin.Security.Infrastructure;
using Microsoft.Owin.Security;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace OPTService.Authentication
{
    internal class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationOptions>
    {
        protected override async Task<AuthenticationTicket> AuthenticateCoreAsync()
        {

            string apiKey = this.Request.Headers.Get(this.Options.HeaderKey);

            if (!String.IsNullOrWhiteSpace(apiKey))
            {
                var context = new ApiKeyValidateIdentityContext(this.Context, this.Options, apiKey);

                await this.Options.Provider.ValidateIdentity(context);

                if (context.IsValidated)
                {
                    var claims = await this.Options.Provider.GenerateClaims(new ApiKeyGenerateClaimsContext(this.Context, this.Options, apiKey));

                    var identity = new ClaimsIdentity(claims, this.Options.AuthenticationType);

                    return new AuthenticationTicket(identity, new AuthenticationProperties()
                    {
                        IssuedUtc = DateTime.UtcNow
                    });
                }
            }

            return null;
        }
    }
}
