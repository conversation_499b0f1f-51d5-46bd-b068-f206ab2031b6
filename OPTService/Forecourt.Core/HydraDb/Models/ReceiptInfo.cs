using Htec.Hydra.Messages.Opt.Models;
using System;
using lrmReceiptInfo = OPT.Common.HydraDb.Models.ReceiptInfo;
using posReceiptInfo = Htec.Hydra.Core.Pos.Messages.ReceiptInfo;

namespace Forecourt.Core.HydraDb.Models
{
    /// <inheritdoc cref="lrmReceiptInfo"/>
    public class ReceiptInfo: lrmReceiptInfo
    {
        /// <inheritdoc cref="lrmReceiptInfo"/>
        public ReceiptInfo() { }

        /// <inheritdoc cref="lrmReceiptInfo"/>
        public ReceiptInfo(string cardNumber, string receiptContent, DateTime expiry, string opt, long transactionNumber, long amount, DateTime transactionTime, int printedCount) :
            base(cardNumber, receiptContent, expiry, opt, transactionNumber, amount, transactionTime, printedCount)
        { }

        /// <summary>
        /// Convert from <see cref="ReceiptInfo"/> to <see cref="ReceiptDetails"/> by using a standard .net cast
        /// </summary>
        /// <param name="from"><see cref="ReceiptInfo"/> instance</param>
        public static explicit operator ReceiptDetails(ReceiptInfo from)
        {
            return new ReceiptDetails(from.ReceiptContent, from.PrintedCount, from.TransactionTime, from.Amount, $"{from.TransactionNumber}");
        }

        /// <summary>
        /// Convert from <see cref="ReceiptInfo"/> to <see cref="posReceiptInfo"/> by using a standard .net cast
        /// </summary>
        /// <param name="from"><see cref="ReceiptInfo"/> instance</param>
        public static explicit operator posReceiptInfo(ReceiptInfo from)
        {
            return new posReceiptInfo()
            {
                CardNumber = from.CardNumber,
                Amount = from.Amount,
                Expiry = from.Expiry,
                Opt = from.Opt,
                PrintedCount = from.PrintedCount,
                ReceiptContent = from.ReceiptContent,
                TransactionNumber = from.TransactionNumber,
                TransactionTime = from.TransactionTime
            };
        }

        /// <summary>
        /// Convert from <see cref="posReceiptInfo"/> to <see cref="ReceiptInfo"/> by using a standard .net cast
        /// </summary>
        /// <param name="from"><see cref="posReceiptInfo"/> instance</param>
        public static explicit operator ReceiptInfo(posReceiptInfo from)
        {
            return new ReceiptInfo()
            {
                CardNumber = from.CardNumber,
                Amount = from.Amount,
                Expiry = from.Expiry,
                Opt = from.Opt,
                PrintedCount = from.PrintedCount,
                ReceiptContent = from.ReceiptContent,
                TransactionNumber = from.TransactionNumber,
                TransactionTime = from.TransactionTime
            };
        }
    }
}