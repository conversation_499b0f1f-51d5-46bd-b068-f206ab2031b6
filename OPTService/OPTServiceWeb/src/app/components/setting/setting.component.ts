import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { ConfigurationCategory } from 'src/app/core/models/configurationCategory.model';
import { SettingDetails } from 'src/app/core/models/settingDetails.model';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';

@Component({
  selector: 'app-setting',
  templateUrl: './setting.component.html',
  styleUrls: ['./setting.component.css']
})
export class SettingComponent implements OnInit {

  @HostBinding('attr.id') externalId = null;
  @Input() id: number;
  @Input() category: string;
  @Input() categoryId: number;
  @Input() key: any;
  @Input() value: any;

  valueError: boolean = false;

  form = this.fb.group({
    Key: [''],
    Value: ['']
  });

  constructor(private advancedConfigService: AdvancedConfigService, private fb: FormBuilder,private logger: NGXLogger) { }

  /**
   * The getter method for the form
   */
  get formControl() {
    return this.form.controls;
  }

  /**
   * Updates the setting value
   */
   updateValue(): void {
    this.logger.info('Updating ' + this.category + '.' + this.key + ' = ' + this.value);

    // Compose request body
    let configurationCategories : Array<ConfigurationCategory> = [<ConfigurationCategory>({
      Category : this.category, 
      ID: this.categoryId,
      Settings : { [this.key]: this.value }
    })];
    
    this.advancedConfigService.setCategories(configurationCategories).subscribe(() => {
      this.logger.debug(this.category + '.' + this.key + ' updated ok');
      this.valueError = false;
    }, () => {
      this.logger.error('Problem updating ' + this.category + '.' +this.key);
      this.valueError = true;
    });
  }

  ngOnInit(): void {
    console.log(this.key);
    console.log(this.value);

    this.form.patchValue({
      Key: this.key,
      Value: this.value
    });
    Object.keys(this.form.controls).forEach(key => {
      this.form.controls[key].valueChanges.subscribe(value => {
        this.value = value;
      });
    });
  }

}
