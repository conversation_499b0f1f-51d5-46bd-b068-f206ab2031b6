namespace OPT.Common.HydraDbClasses
{
    public class PruneDays
    {
        public int FilePruneDays { get; }
        public int TransactionPruneDays { get; }
        public int ReceiptPruneDays { get; }

        // ReSharper disable once UnusedMember.Global
        public PruneDays(int filePruneDays, int transactionPruneDays, int receiptPruneDays)
        {
            FilePruneDays = filePruneDays;
            TransactionPruneDays = transactionPruneDays;
            ReceiptPruneDays = receiptPruneDays;
        }
    }
}