import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON>uilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { SignalREventType } from 'src/app/core/enums/signalREventType';
import { Opt } from 'src/app/core/models/opt.model';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { OptService } from 'src/app/services/opt.service';
import { PersistentDataService } from 'src/app/services/persistent-data.service';
import { PumpService } from 'src/app/services/pump.service';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { OptsComponent } from './opts.component';

describe('OptsComponent', () => {
  let component: OptsComponent;
  let serviceSpy: jasmine.SpyObj<OptService>;
  let pumpServiceSpy: jasmine.SpyObj<PumpService>;
  let persistentDataServiceSpy: jasmine.SpyObj<PersistentDataService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('OptService', ['getOpts', 'restartOpt', 'requestOptLog']);
    const pumpServiceObjSpy = jasmine.createSpyObj('PumpService', ['getPumps', 'getTids', 'setOpt', 'setTid']);
    const persistentDataServiceObjSpy = jasmine.createSpyObj('PersistentDataService', ['refreshTids', 'refreshMaxFillOverride', 'getTids', 'getMaxFillOverride']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        OptsComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: OptService, useValue: serviceObjSpy },
        { provide: PumpService, useValue: pumpServiceObjSpy },
        { provide: PersistentDataService, useValue: persistentDataServiceObjSpy },
        SIGNAL_R_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    serviceSpy = TestBed.inject(OptService) as jasmine.SpyObj<OptService>;
    pumpServiceSpy = TestBed.inject(PumpService) as jasmine.SpyObj<PumpService>;
    pumpServiceSpy.setTid.and.returnValue(of());
    persistentDataServiceSpy = TestBed.inject(PersistentDataService) as jasmine.SpyObj<PersistentDataService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    SIGNAL_R_SERVICE_SPY().getOptSignalRMessage.and.returnValue(of());
    component = TestBed.inject(OptsComponent);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    jasmine.clock().install();
    //Arrange
    spyOn(component, 'refreshData').and.stub();

    //Act
    component.ngOnInit();
    jasmine.clock().tick(150);

    //Assert
    expect(component.refreshData).toHaveBeenCalledTimes(1);

    jasmine.clock().uninstall();
  });

  it('.ngOnDestroy() success', () => {
    //Arrange
    spyOn(component.signalRData, 'unsubscribe').and.stub();

    //Act
    component.ngOnDestroy();

    //Assert
    expect(component.signalRData.unsubscribe).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle success response from service', () => {
    //Arrange
    let fakeConfig = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '' }, { OptStringId: null }, { OptStringId: undefined }];
    }
    serviceSpy.getOpts.and.returnValue(of(fakeConfig));
    pumpServiceSpy.getPumps.and.returnValue(of(fakeConfig));

    //Act
    component.refreshData();

    //Assert
    expect(component.optsData).toEqual(fakeConfig);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() for single OPT should handle success response from service', () => {
    //Arrange
    let fakeConfig = new Array()
    {
      [{ OptStringId: '1' }];
    }
    component.optsData = new Array()
    {
      [{ OptStringId: '1' }, { OptStringId: '2' }, { OptStringId: '3' }, { OptStringId: '4' }];
    }
    serviceSpy.getOpts.and.returnValue(of(fakeConfig));
    pumpServiceSpy.getPumps.and.returnValue(of(fakeConfig));

    //Act
    component.refreshData('2');

    //Assert
    expect(component.optsData).toEqual(fakeConfig);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', () => {
    //Arrange
    let fakeConfig = [];
    serviceSpy.getOpts.and.returnValue(throwError({ status: 500 }));
    pumpServiceSpy.getPumps.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshData();

    //Assert
    expect(component.optsData).toEqual(fakeConfig);
    expect(component.orphanPumps).toBeUndefined();
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshTids() should handle success response from service', () => {
    //Arrange
    let fakeConfig = ['99979901', '99979902', '99979903', '99979904', '99979905', '99979906'];
    persistentDataServiceSpy.getTids.and.returnValue(fakeConfig);

    //Act
    component.refreshTids();

    //Assert
    expect(component.tids).toEqual(fakeConfig);
  });

  it('.refreshTids() should handle unsuccess response from service', () => {
    //Arrange
    pumpServiceSpy.getTids.and.returnValue(throwError({ status: 500 }));

    //Act
    component.refreshTids();

    //Assert
    expect(component.tids).toBeUndefined();
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(0);
  });

  it('.restartOpt() should handle success response from service', () => {
    //Arrange
    serviceSpy.restartOpt.and.returnValue(of({}));

    //Act
    component.restartOpt(new Opt());

    //Assert
    expect(serviceSpy.restartOpt).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.restartOpt() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.restartOpt.and.returnValue(throwError({ status: 500 }))

    //Act
    component.restartOpt(new Opt());

    //Assert
    expect(serviceSpy.restartOpt).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });
  
  it('.requestOptLogs() should handle success response from service', () => {
    //Arrange
    serviceSpy.requestOptLog.and.returnValue(of({}));

    //Act
    component.requestOptLog(new Opt());

    //Assert
    expect(serviceSpy.requestOptLog).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.requestOptLogs() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.requestOptLog.and.returnValue(throwError({ status: 500 }))

    //Act
    component.requestOptLog(new Opt());

    //Assert
    expect(serviceSpy.requestOptLog).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

});
