USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('UpdateTransactionBooking') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE UpdateTransactionBooking
END
GO

CREATE PROCEDURE [dbo].[UpdateTransactionBooking]
 @id BIGINT,
 @transId bigint,
 @externalTransId NVARCHAR(30) = null,
 @businessDate DateTime2 = null,
 @shiftId int = 0,
 @periodId int = 0,
 @bookedDate DateTime2 = null,
 @httpStatusCode int = 200,
 @httpResponse NVARCHAR(2048) = null,
 @retryCount int = 0,
 @nextRetryDate DateTime2 = null,
 @sendTransactionItem NVARCHAR(2048) = null
AS
BEGIN
	BEGIN TRY
		BEGIN TRANSACTION

		IF (ISNULL(@sendTransactionItem, '') != '') 
			BEGIN
				UPDATE t
				SET 
					t.SendTransactionItem = @sendTransactionItem,
					t.ModifiedDate = GETDATE()
				FROM TransactionBookingState t
				WHERE t.id = @id AND t.TransactionId = @transId AND ISNULL(t.ExternalTransactionId, '') = '';
			END
		ELSE
		IF (ISNULL(@externalTransId, '') != '') 
			BEGIN
				UPDATE t
				SET 
					t.ExternalTransactionId = @externalTransId,
					t.ShiftId = @shiftId,
					t.PeriodId = @periodId,
					t.BusinessDate = @businessDate,
					t.ModifiedDate = GETDATE()
				FROM TransactionBookingState t
				WHERE t.id = @id AND t.TransactionId = @transId AND ISNULL(t.ExternalTransactionId, '') = '';
			END
		ELSE IF (@bookedDate IS NOT NULL)
			BEGIN
				UPDATE t
				SET 
					t.BookedDate = @bookedDate,
					t.HttpStatusCode = @httpStatusCode,
					t.ModifiedDate = GETDATE()
				FROM TransactionBookingState t
				WHERE t.id = @id AND t.TransactionId = @transId AND t.BookedDate IS NULL;
			END
		ELSE IF (@httpStatusCode != 0)
			BEGIN
				UPDATE t
				SET 
					t.HttpStatusCode = @httpStatusCode,
					t.HttpResponse = @httpResponse,
					t.RetryCount = @retryCount,
					t.NextRetryDate = @nextRetryDate,
					t.ModifiedDate = GETDATE()
				FROM TransactionBookingState t
				WHERE t.id = @id AND t.TransactionId = @transId;
			END;	

		IF @@ROWCOUNT = 0
			THROW 50000, 'TransactionBookingState NotFound', 1

		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION;
		THROW
	END CATCH
END
GO

GRANT EXECUTE ON UpdateTransactionBooking TO UserRole
GO
