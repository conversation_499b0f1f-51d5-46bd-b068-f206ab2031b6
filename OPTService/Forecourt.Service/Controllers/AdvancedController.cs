using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Configuration;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces.Tracing;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;
using Constants = Htec.Foundation.Configuration.Constants;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class AdvancedController : LinkedLoggableApiController<IWeb>
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private IWeb _web => Loggable;

        /// <summary>
        /// Config key for the GenericOptConfig MaxFillOverride Limit (in pence).
        /// </summary>
        public const string ConfigKeyGenericOptConfigMaxFillOverrideLimit = Constants.CategoryNameGeneral + Constants.CategorySeparator + "GenericOptConfig:MaxFillOverride:Limit:pence";

        /// <summary>
        /// Default value for GenericOptConfig MaxFillOverride Limit (in pence).
        /// </summary>
        public const int DefaultValueGenericOptConfigMaxFillOverrideLimit = 99999;

        private ConfigurableInt ConfigValueMaxFillOverrideLimit { get; set; }

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        public AdvancedController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger, IConfigurationManager configurationManager)
            : base(web as Loggable, configurationManager)
        {         
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            ConfigValueMaxFillOverrideLimit = new ConfigurableInt(AsLoggable, ConfigKeyGenericOptConfigMaxFillOverrideLimit, DefaultValueGenericOptConfigMaxFillOverrideLimit);
        }

        #endregion Constructor

        #region Update Data

        /// <summary>
        /// Set Automatic Authentication on or off.
        /// </summary>
        /// <param name="details">Includes flag to set Automatic Authentication on or off.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetAutoAuth(AdvancedConfigDetails details)
        {
            bool isOn = details.AutoAuth;
            _web.SendChatMessage(WebName, $"Set Auto Auth {OnOff(isOn)}");
            string result = _web.SetAutoAuth(isOn);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Media Channel on or off.
        /// </summary>
        /// <param name="details">Includes flag to set Media Channel on or off.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetMediaChannel(AdvancedConfigDetails details)
        {
            bool isOn = details.MediaChannel;
            _web.SendChatMessage(WebName, $"Set Media Channel {OnOff(isOn)}");
            string result = _web.SetMediaChannel(isOn);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Unamanned Pseudo POS on or off.
        /// </summary>
        /// <param name="details">Includes flag to set Unamanned Pseudo POS on or off.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetUnmannedPseudoPos(AdvancedConfigDetails details)
        {
            bool isOn = details.UnmannedPseudoPos;
            _web.SendChatMessage(WebName, $"Set Unamanned Pseudo POS {OnOff(isOn)}");
            string result = _web.SetUnmannedPseudoPos(isOn);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public async Task<IHttpActionResult> SetPaymentTimeout(PaymentTimeoutType type, int value)
        {
            return await DoActionAsync(async () => Loggable.SetPaymentTimeout(type, value), (r) => ResultMapper(r), null).ConfigureAwait(false);
        }

        /// <summary>
        /// Set the payment timeout for a given OPT mode.
        /// Mode should be "POD", "OPT", "Mixed" or "Nozzle Down".
        /// </summary>
        /// <param name="details">Includes timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("Use the SetTimeout version instead")]
        public HttpResponseMessage SetPaymentTimeoutOpt(AdvancedConfigDetails.Timeouts details)
        {
            int timeout = details.PaymentTimeoutOpt;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            _web.SendChatMessage(WebName, $"Set Payment Timeout, Mode OPT, Timeout {timeout}");
            string result = _web.SetPaymentTimeout("OPT", timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the payment timeout for a given OPT mode.
        /// Mode should be "POD", "OPT", "Mixed" or "Nozzle Down".
        /// </summary>
        /// <param name="details">Includes timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("Use the SetTimeout version instead")]
        public HttpResponseMessage SetPaymentTimeoutPod(AdvancedConfigDetails.Timeouts details)
        {
            int timeout = details.PaymentTimeoutPod;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            _web.SendChatMessage(WebName, $"Set Payment Timeout, Mode POD, Timeout {timeout}");
            string result = _web.SetPaymentTimeout("POD", timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the payment timeout for a given OPT mode.
        /// Mode should be "POD", "OPT", "Mixed" or "Nozzle Down".
        /// </summary>
        /// <param name="details">Includes timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("Use the SetTimeout version instead")]
        public HttpResponseMessage SetPaymentTimeoutMixed(AdvancedConfigDetails.Timeouts details)
        {
            int timeout = details.PaymentTimeoutMixed;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            _web.SendChatMessage(WebName, $"Set Payment Timeout, Mode Mixed, Timeout {timeout}");
            string result = _web.SetPaymentTimeout("Mixed", timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the payment timeout for a given OPT mode.
        /// Mode should be "POD", "OPT", "Mixed" or "Nozzle Down".
        /// </summary>
        /// <param name="details">Includes timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("Use the SetTimeout version instead")]
        public HttpResponseMessage SetPaymentTimeoutNozzleDown(AdvancedConfigDetails.Timeouts details)
        {
            int timeout = details.PaymentTimeoutNozzleDown;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            _web.SendChatMessage(WebName, $"Set Payment Timeout, Mode Nozzle Down, Timeout {timeout}");
            string result = _web.SetPaymentTimeout("Nozzle Down", timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Site Name for the OPT Config.
        /// </summary>
        /// <param name="details">Includes site name to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetSiteName(AdvancedConfigDetails details)
        {
            string name = details.SiteName?.Trim() ?? string.Empty;
            _web.SendChatMessage(WebName, $"Set Site Name {name}");
            string result = _web.SetSiteName(name);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the VAT Number for the OPT Config.
        /// </summary>
        /// <param name="details">Includes VAT Number to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetVatNumber(AdvancedConfigDetails details)
        {
            string number = details.VatNumber?.Trim() ?? string.Empty;
            _web.SendChatMessage(WebName, $"Set VAT Number {number}");
            string result = _web.SetVatNumber(number);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Currency Code for the OPT Config.
        /// </summary>
        /// <param name="details">Includes Currency Code to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetCurrencyCode(AdvancedConfigDetails details)
        {
            int number = details.CurrencyCode;
            _web.SendChatMessage(WebName, $"Set Currency Code {number}");
            string result = _web.SetCurrencyCode(number);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Till Number for Transactions.
        /// </summary>
        /// <param name="details">Includes till number to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetTillNumber(AdvancedConfigDetails details)
        {
            short number = (short) details.TillNumber;
            if (number < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Till Number");
            }

            _web.SendChatMessage(WebName, $"Set Till Number {number}");
            string result = _web.SetTillNumber(number);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Fuel Category for Transactions.
        /// </summary>
        /// <param name="details">Includes fuel category to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuelCategory(AdvancedConfigDetails details)
        {
            short number = (short) details.FuelCategory;
            if (number < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Fuel Category");
            }

            _web.SendChatMessage(WebName, $"Set Fuel Category {number}");
            string result = _web.SetFuelCategory(number);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Reference Number for a card type.
        /// </summary>
        /// <param name="details">Includes card name and reference to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetCardReference(CardReferenceDetails details)
        {
            string name = details.Name?.Trim();
            int reference = details.Reference;
            if (reference < 1)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Card Reference");
            }
            else if (string.IsNullOrEmpty(name))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }

            _web.SendChatMessage(WebName, $"Set Card Reference for {name} to {reference}");
            string result = _web.SetCardReference(name, reference);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Clear a card type.
        /// </summary>
        /// <param name="details">Includes card name to clear.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ClearCardReference(CardReferenceDetails details)
        {
            string name = details.Name?.Trim();
            if (string.IsNullOrEmpty(name))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }

            _web.SendChatMessage(WebName, $"Clear Card Reference for {name}");
            string result = _web.ClearCardReference(name);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Acquirer Reference for a card type.
        /// </summary>
        /// <param name="details">Includes card name and acquirer name to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetAcquirerReference(CardReferenceDetails details)
        {
            string cardName = details.Name?.Trim();
            string acquirerName = details.Acquirer?.Trim();
            if (string.IsNullOrEmpty(cardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }
            else if (string.IsNullOrEmpty(acquirerName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Acquirer is empty");
            }

            _web.SendChatMessage(WebName, $"Set Acquirer Reference for {cardName} to {acquirerName}");
            string result = _web.SetAcquirerReference(cardName, acquirerName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Clear the Acquirer Reference for a card type.
        /// </summary>
        /// <param name="details">Includes card name to clear.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ClearAcquirerReference(CardReferenceDetails details)
        {
            string cardName = details.Name?.Trim();
            if (string.IsNullOrEmpty(cardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }

            _web.SendChatMessage(WebName, $"Clear Acquirer Reference for {cardName}");
            string result = _web.ClearAcquirerReference(cardName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Fuel Card flag for a card type.
        /// </summary>
        /// <param name="details">Includes card name and fuel card flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuelCard(CardReferenceDetails details)
        {
            string cardName = details.Name?.Trim();
            bool isFuelCard = details.FuelCard;
            if (string.IsNullOrEmpty(cardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }

            _web.SendChatMessage(WebName, $"Set Fuel Card for {cardName} to {OnOff(isFuelCard)}");
            string result = _web.SetFuelCard(cardName, isFuelCard);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Fuel Card flag for a card type.
        /// </summary>
        /// <param name="details">Includes card name and fuel card flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetExternalName(CardReferenceDetails details)
        {
            string cardName = details.Name?.Trim();
            string externalCardName = details.External?.Trim();
            if (string.IsNullOrEmpty(cardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }
            else if (string.IsNullOrEmpty(externalCardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "External card name is empty");
            }

            _web.SendChatMessage(WebName, $"Set External Name for {cardName} to {externalCardName}");
            string result = _web.SetExternalName(cardName, externalCardName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Clear the External Name for a card type.
        /// </summary>
        /// <param name="details">Includes card name to clear.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ClearExternalName(CardReferenceDetails details)
        {
            string cardName = details.Name?.Trim();
            if (string.IsNullOrEmpty(cardName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Card type is empty");
            }

            _web.SendChatMessage(WebName, $"Clear External Name for {cardName}");
            string result = _web.ClearExternalName(cardName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Forward Fuel Price Update on or off.
        /// </summary>
        /// <param name="details">Includes flag to set Forward Fuel Price Update on or off.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetForwardFuelPriceUpdate(AdvancedConfigDetails details)
        {
            bool isOn = details.ForwardFuelPriceUpdate;
            _web.SendChatMessage(WebName, $"Set Forward Fuel Price Update {OnOff(isOn)}");
            string result = _web.SetForwardFuelPriceUpdate(isOn);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Indefinite Wait on or off.
        /// </summary>
        /// <param name="details">Includes flag to set Fuelling Indefinite Wait on or off.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingIndefiniteWait(AdvancedConfigDetails details)
        {
            bool isOn = details.FuellingIndefiniteWait;
            _web.SendChatMessage(WebName, $"Set Fuelling Indefinite Wait {OnOff(isOn)}");
            string result = _web.SetFuellingIndefiniteWait(isOn);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Wait Minutes.
        /// </summary>
        /// <param name="details">Includes wait minutes.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingWaitMinutes(AdvancedConfigDetails details)
        {
            int minutes = details.FuellingWaitMinutes;
            if (minutes < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid wait minutes");
            }

            _web.SendChatMessage(WebName, $"Set Fuelling Wait Minutes {minutes}");
            string result = _web.SetFuellingWaitMinutes(minutes);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Backoff Auth.
        /// </summary>
        /// <param name="details">Includes backoff auth.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingBackoffAuth(AdvancedConfigDetails details)
        {
            int backoff = details.FuellingBackoffAuth;
            if (backoff < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid backoff");
            }

            _web.SendChatMessage(WebName, $"Set Fuelling Backoff Auth {backoff}");
            string result = _web.SetFuellingBackoffAuth(backoff);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Backoff Pre Auth.
        /// </summary>
        /// <param name="details">Includes backoff pre auth.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingBackoffPreAuth(AdvancedConfigDetails details)
        {
            int backoff = details.FuellingBackoffPreAuth;
            if (backoff < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid backoff");
            }

            _web.SendChatMessage(WebName, $"Set Fuelling Backoff Pre Auth {backoff}");
            string result = _web.SetFuellingBackoffPreAuth(backoff);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Backoff Stop Start.
        /// </summary>
        /// <param name="details">Includes backoff stop start.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingBackoffStopStart(AdvancedConfigDetails details)
        {
            int backoff = details.FuellingBackoffStopStart;
            if (backoff < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid backoff");
            }

            _web.SendChatMessage(WebName, $"Set Fuelling Backoff Stop Start {backoff}");
            string result = _web.SetFuellingBackoffStopStart(backoff);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Fuelling Backoff Stop Only.
        /// </summary>
        /// <param name="details">Includes backoff stop only.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFuellingBackoffStopOnly(AdvancedConfigDetails details)
        {
            int backoff = details.FuellingBackoffStopOnly;
            if (backoff < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid backoff");
            }

            _web.SendChatMessage(WebName, $"Set Fuelling Backoff Stop Only {backoff}");
            string result = _web.SetFuellingBackoffStopOnly(backoff);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set POS Claim Number.
        /// </summary>
        /// <param name="details">Includes POS Claim Number.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPosClaimNumber(AdvancedConfigDetails details)
        {
            byte number = (byte)details.PosClaimNumber;
            if (number <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid POS");
            }

            _web.SendChatMessage(WebName, $"Set POS Claim Number {number}");
            string result = _web.SetPosClaimNumber(number);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set File Prune Days.
        /// </summary>
        /// <param name="details">Includes File Prune Days.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetFilePruneDays(AdvancedConfigDetails details)
        {
            int days = details.FilePruneDays;
            if (days <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Prune Days");
            }

            _web.SendChatMessage(WebName, $"Set File Prune Days {days}");
            string result = _web.SetFilePruneDays(days);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Transaction Prune Days.
        /// </summary>
        /// <param name="details">Includes Transaction Prune Days.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetTransactionPruneDays(AdvancedConfigDetails details)
        {
            int days = details.TransactionPruneDays;
            if (days <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Prune Days");
            }

            _web.SendChatMessage(WebName, $"Set Transaction Prune Days {days}");
            string result = _web.SetTransactionPruneDays(days);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Receipt Prune Days.
        /// </summary>
        /// <param name="details">Includes Receipt Prune Days.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetReceiptPruneDays(AdvancedConfigDetails details)
        {
            int days = details.ReceiptPruneDays;
            if (days <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Prune Days");
            }

            _web.SendChatMessage(WebName, $"Set Receipt Prune Days {days}");
            string result = _web.SetReceiptPruneDays(days);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set categories information
        /// </summary>
        /// <param name="categories">Information of categories to update</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetCategories(IEnumerable<CategoryConfiguration> categories)
        {
            if (categories == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid categories");
            }

            if (categories.Any(x => x.Settings == null))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Settings can not be null");
            }

            var valid = categories.IsValidForUpdate();
            if (!valid.IsSuccess)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, valid.Error);
            }

            _web.SendChatMessage(WebName, $"Set Categories {categories}");
            var result = _web.SetCategories(categories);
            return Request.CreateResponse(result.IsSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the SITE type
        /// </summary>
        /// <param name="siteType">The SITE type</param>
        /// <response code="200">Success</response>
        /// <response code="400">BadRequest - parameter invalid details</response>
        [HttpPost]
        public async Task<IHttpActionResult> SetSiteType(string siteType)
        {
            return await DoActionAsync(async () => Loggable.SetIntegrationType(IntegrationType.Site, siteType), (r) => ResultMapper(r), null).ConfigureAwait(false);
        }

        /// <summary>
        /// Set the POS type
        /// </summary>
        /// <param name="posType">The POS type</param>
        /// <response code="200">Success</response>
        /// <response code="400">BadRequest - parameter invalid details</response>
        [HttpPost]
        public async Task<IHttpActionResult> SetPosType(string posType)
        {
            return await DoActionAsync(async () => Loggable.SetIntegrationType(IntegrationType.Pos, posType), (r) => ResultMapper(r), null).ConfigureAwait(false);
        }

        /// <summary>
        /// Set an Integration type
        /// </summary>
        /// <param name="integrationType">The Integration type</param>
        /// <param name="value">The integration type value</param>
        /// <response code="200">Success</response>
        /// <response code="400">BadRequest - parameter invalid details</response>
        [HttpPost]
        public async Task<IHttpActionResult> SetIntegrationType(IntegrationType integrationType, string value)
        {
            return await DoActionAsync(async () => Loggable.SetIntegrationType(integrationType, value), (r) => ResultMapper(r), null).ConfigureAwait(false);
        }

        private StatusCodeResult ResultMapper(Result r, HttpStatusCode failCode = HttpStatusCode.BadRequest) => r.IsSuccess ? StatusCodeResult.Success :
             (string.IsNullOrWhiteSpace(r.Error) ? StatusCodeResult.Specific(failCode) : StatusCodeResult.Specific(failCode, new Exception(r.Error)));

        private StatusCodeResult<T> ResultMapper<T>(Result<T> r, HttpStatusCode failCode = HttpStatusCode.BadRequest) => r.IsSuccess ? StatusCodeResult<T>.Success(r.Value) :
            (StatusCodeResult<T>)(string.IsNullOrWhiteSpace(r.Error) ? StatusCodeResult<T>.Specific(failCode) : StatusCodeResult<T>.Specific(failCode, new Exception(r.Error)));

        /// <summary>
        /// Set Nozzle Up For Kiosk Use for the OPT Config.
        /// </summary>
        /// <param name="details">Includes Nozzle Up For Kiosk Use to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetNozzleUpForKioskUse(AdvancedConfigDetails details)
        {
            bool flag = details.NozzleUpForKioskUse;
            _web.SendChatMessage(WebName, $"Set Nozzle Up For Kiosk Use {flag}");
            string result = _web.SetNozzleUpForKioskUse(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Use Replace Nozzle Screen for the OPT Config.
        /// </summary>
        /// <param name="details">Includes Use Replace Nozzle Screen to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetUseReplaceNozzleScreen(AdvancedConfigDetails details)
        {
            bool flag = details.UseReplaceNozzleScreen;
            _web.SendChatMessage(WebName, $"Set Use Replace Nozzle Screen {flag}");
            string result = _web.SetUseReplaceNozzleScreen(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set Max Fill Override for the OPT Config.
        /// </summary>
        /// <param name="details">Includes Max Fill Override to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetMaxFillOverride(AdvancedConfigDetails details)
        {
            var maxFillOverride = details.MaxFillOverride;
            var limit = ConfigValueMaxFillOverrideLimit.GetValue();
            if (maxFillOverride > limit)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, $"Override Value above limit £{(Convert.ToDecimal(limit) / 100):F2}");
            }

            if (maxFillOverride > 0)
            {
                _web.SendChatMessage(WebName, $"Set Max Fill Override {maxFillOverride}");
                var result = _web.SetMaxFillOverride(maxFillOverride);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }

            return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Override Value");
        }

        /// <summary>
        /// Clear Max Fill Override for the OPT Config.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage ClearMaxFillOverride()
        {
            _web.SendChatMessage(WebName, "Clear Max Fill Override");
            string result = _web.SetMaxFillOverride(0);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #region eSocket POS Config

        /// <summary>
        /// Set connection string for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes connection string to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketConnectionString(ESocketPosConfigDetails details)
        {
            string connectionString = details.EsocketConnectionString?.Trim();
            if (string.IsNullOrEmpty(connectionString))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Connection String is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set eSocket.POS Connection String to {connectionString}");
                string result = _web.SetEsocketConnectionString(connectionString);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Set use connection string flag for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes use connection string flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        [Obsolete("This option is now part of Integrations\\Payment Configuration type")]
        public HttpResponseMessage SetEsocketUseConnectionString(ESocketPosConfigDetails details)
        {
            bool useConnectionString = details.EsocketUseConnectionString;
            _web.SendChatMessage(WebName, $"Set eSocket.POS use {(useConnectionString ? "Connection String" : "Config File")}");
            string result = _web.SetEsocketUseConnectionString(useConnectionString);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set config file for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes config file to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketConfigFile(ESocketPosConfigDetails details)
        {
            string fileName = details.EsocketConfigFile?.Trim();
            if (string.IsNullOrEmpty(fileName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Config File Name is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set eSocket.POS Config File to {fileName}");
                string result = _web.SetEsocketConfigFile(fileName);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Set keystore file for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes keystore file to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketKeystoreFile(ESocketPosConfigDetails details)
        {
            string fileName = details.EsocketKeystoreFile?.Trim();
            if (string.IsNullOrEmpty(fileName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Keystore File Name is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set eSocket.POS Keystore File to {fileName}");
                string result = _web.SetEsocketKeystoreFile(fileName);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Set DB URL for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes DB URL to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketDbUrl(ESocketPosConfigDetails details)
        {
            string url = details.EsocketDbUrl?.Trim();
            if (string.IsNullOrEmpty(url))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Database URL is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set eSocket.POS Database URL to {url}");
                string result = _web.SetEsocketDbUrl(url);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Set override properties flag for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes override properties flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketOverrideProperties(ESocketPosConfigDetails details)
        {
            bool flag = details.EsocketOverrideProperties;
            _web.SendChatMessage(WebName, $"Set eSocket.POS {(flag ? "" : "do not ")}override properties");
            string result = _web.SetEsocketOverrideProperties(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set override keystore flag for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes override keystore flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketOverrideKeystore(ESocketPosConfigDetails details)
        {
            bool flag = details.EsocketOverrideKeystore;
            _web.SendChatMessage(WebName, $"Set eSocket.POS {(flag ? "" : "do not ")}override keystore");
            string result = _web.SetEsocketOverrideKeystore(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set override URL flag for the eSocket POS Config.
        /// </summary>
        /// <param name="details">Includes override URL flag to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetEsocketOverrideUrl(ESocketPosConfigDetails details)
        {
            bool flag = details.EsocketOverrideUrl;
            _web.SendChatMessage(WebName, $"Set eSocket.POS {(flag ? "" : "do not ")}override database URL");
            string result = _web.SetEsocketOverrideUrl(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        private static string OnOff(bool isOn)
        {
            return isOn ? "On" : "Off";
        }

        #endregion

        #region Get Data

        /// <summary>
        /// Retrieves all the advanced configuration
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <returns>The advanced configuration</returns>
        /// <response code="200">Success</response>
        /// <response code="404">Advanced configuration not found</response>
        [HttpGet]
        [ResponseType(typeof(AdvancedConfigDetails))]
        public async Task<IHttpActionResult> GetAdvancedConfiguration(string reference = null)
        {
            return await DoActionAsync(async () =>
            {
                var advancedConfiguration = Loggable.GetAdvancedConfig();
                return (advancedConfiguration is null) ? Result.Failure<AdvancedConfigDetails>("No configuration found!") : Result.Success(advancedConfiguration);
            },
            (Result<AdvancedConfigDetails> r) => ResultMapper(r, HttpStatusCode.NotFound),
            reference);
        }

        /// <summary>
        /// Retrieves the OPT MaxFillOverride
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <response code="200">Success</response>
        /// <returns>The OPT MaxFillOverride</returns>
        [HttpGet]
        [ResponseType(typeof(uint))]
        public async Task<IHttpActionResult> GetMaxFillOverride(string reference = null)
        {
            return await DoActionAsync(async () => Result.Success(Loggable.GetMaxFillOverride()), null, reference);
        }

        /// <summary>
        /// Retrieves information about config batch
        /// </summary>
        /// <returns>An object indicating if config batch is currently active or not</returns>
        [HttpGet]
        [ResponseType(typeof(ConfigBatch))]
        public IHttpActionResult GetIsConfigBatch()
        {
            return Ok(new ConfigBatch() {
                IsConfigBatch = _web.IsConfigBatch
            });
        }

        #endregion Get Data
    }
}