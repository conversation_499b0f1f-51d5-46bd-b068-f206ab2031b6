using CSharpFunctionalExtensions;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Models.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using JetBrains.Annotations;
using System;
using System.Runtime.CompilerServices;

namespace Htec.Foundation.Web.Hubs.Interfaces
{
    /// <summary>
    /// Defines the outbound actions available on an <see cref="Hubbable{TMessage, TMessageTracking}"/>
    /// </summary>
    /// <typeparam name="TMessage">Base type of messages passing through a SignalR Hub</typeparam>
    /// <typeparam name="TMessageTracking">Current message tracking instance</typeparam>
    public interface IHubbable<TMessage, TMessageTracking>: IConnectable
        where TMessage : class
        where TMessageTracking : ILogTracking
    {
        /// <summary>
        /// Send a message to a single Client connection
        /// </summary>
        /// <param name="connectionId">Connection id</param>
        /// <param name="context">Notiication context to send on</param>
        /// <param name="message">Message to send, based on {TMessage}</param>
        /// <param name="action">Action delegate</param>
        /// <param name="tracking">Current message tracking instance</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns>Result</returns>
        Result Send(string connectionId, string context, TMessage message, Action<dynamic, TMessage> action, [NotNull] TMessageTracking tracking, [CallerMemberName] string methodName = null);

        /// <summary>
        /// Send a message to all Client connections
        /// </summary>
        /// <param name="context">Notiication context to send on</param>
        /// <param name="message">Message to send, based on {TMessage}</param>
        /// <param name="action">Action delegate</param>
        /// <param name="tracking">Current message tracking instance</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns>Result</returns>
        Result Publish(string context, TMessage message, Action<dynamic, TMessage> action, [NotNull] TMessageTracking tracking, [CallerMemberName] string methodName = null);
    }

    /// <summary>
    /// Defines the ACK (inbound) actions available on an <see cref="Hubbable{TMessage, TMessageTracking}"/>
    /// </summary>
    /// <typeparam name="TMessage">Base type of messages passing through a SignalR Hub</typeparam>
    /// <typeparam name="TMessageTracking">Current message tracking instance</typeparam>
    public interface IHubbableIn<TMessage, TMessageTracking>
        where TMessage : class
        where TMessageTracking : ILogTracking
    {
        /// <summary>
        /// Process the inbound ACK response message, validating messageId and remove from outbound message queue 
        /// </summary>
        /// <param name="messageId">Message id</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns></returns>
        Result ProcessAckResponse(string messageId, [CallerMemberName] string methodName = null);

        /// <summary>
        /// Process the inbound ACK response message, validating messageId, execute the action and remove from outbound message queue 
        /// </summary>
        /// <typeparam name="TTarget">Type of the queued message</typeparam>
        /// <param name="messageId">Message id</param>
        /// <param name="action">Action delegate</param>
        /// <param name="methodName">Calling method name</param>
        /// <returns></returns>
        Result ProcessAckResponse<TTarget>(string messageId, Func<TMessageTracking, TTarget, Result> action, [CallerMemberName] string methodName = null) where TTarget: TMessage;

        Result ProcessRequest<TTarget>(string connectionId, string messageId, TTarget request, Func<TMessageTracking, TTarget, IHtecLogger, INotificationWorker<string>, Result> action, [CallerMemberName] string methodName = null) where TTarget : TMessage;
    }
}
