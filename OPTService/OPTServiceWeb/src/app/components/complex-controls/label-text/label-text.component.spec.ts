import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ControlContainer, FormControl, FormGroup, FormGroupDirective, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LabelTextComponent } from './label-text.component';
import { NO_ERRORS_SCHEMA } from "@angular/core";

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('LabelTextComponent', () => {
  let fixture: ComponentFixture<LabelTextComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [LabelTextComponent],
      imports: [FormsModule, ReactiveFormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: ControlContainer, useValue: fgd }
      ]
    });
    fixture = TestBed.createComponent(LabelTextComponent);
  });

  it('should create', () => {
    expect(fixture.componentInstance).toBeTruthy();
  });
});
