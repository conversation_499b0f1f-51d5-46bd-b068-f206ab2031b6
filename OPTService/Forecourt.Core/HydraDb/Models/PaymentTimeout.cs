using Forecourt.Core.HydraDb.Enums;

namespace Forecourt.Core.HydraDb.Models
{
    public class PaymentTimeout
    {
        public PaymentTimeoutType Mode { get; }
        public int Timeout { get; }

        // ReSharper disable once UnusedMember.Global
        public PaymentTimeout(PaymentTimeoutType mode, int timeout)
        {
            Mode = mode;
            Timeout = timeout;
        }
    }
}