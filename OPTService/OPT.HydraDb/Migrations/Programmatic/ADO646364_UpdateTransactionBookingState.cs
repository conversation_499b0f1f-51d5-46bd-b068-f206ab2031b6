using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(20050021, "ADO#646364 - Add dbo.TransactionBookingState.[SendTransactionItem] and update all related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO646364_UpdateTransactionBookingState : Migrationable
    {
        private const string TableName = "TransactionBookingState";
        private const string ColumnName = "SendTransactionItem";

        /// <inheritdoc/>
        public ADO646364_UpdateTransactionBookingState(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetTransactionBooking", "dbo.GetPendingTransactionBookings", "dbo.UpdateTransactionBooking" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO646364_UpdateTransactionBookingState), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            if (!Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Alter.Table(TableName).AddColumn(ColumnName).AsString(2 * 1024).Nullable();
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }         
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO646364_UpdateTransactionBookingState), DbObjects.Reverse().ToArray());

            if (Schema.Table(TableName).Column(ColumnName).Exists())
            {
                Delete.Column(ColumnName).FromTable(TableName);
            }
        }
    }
}
