using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.Pos.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using posReceiptInfo = Htec.Hydra.Core.Pos.Messages.ReceiptInfo;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;

namespace Forecourt.Pos.Helpers
{
    /// <inheritdoc/>
    public class ReceiptHelper : HydraDbable<HydraDb.Interfaces.IHydraDb>, IReceiptHelper
    {
        public const int PosReceiptWidth = PrinterHelper.PosReceiptWidth;

        /// <inheritdoc/>
        public ReceiptHelper(IHtecLogManager logManager, HydraDb.Interfaces.IHydraDb hydraDb, IPumpCollection allPumps, IConfigurationManager configurationManager = null) : base(hydraDb, logManager, nameof(ReceiptHelper), allPumps, configurationManager)
        {
        }

        /// <inheritdoc/>
        public byte GetPumpFromReceiptInfo(ReceiptInfo receipt)
        {
            var result = GetRawReceiptText(receipt);
            var raw = result.IsSuccess ? result.Value : Enumerable.Empty<string>();

            var pumpText = raw.FirstOrDefault(x => x.StartsWith("PUMP "));
            if (pumpText.IsNullOrWhiteSpace())
            {
                return 0;
            }

            var ss = pumpText.Split(' ');
            return ss.Length == 0 ? (byte)0 : Convert.ToByte(ss[0]);

            //var match = Regex.Match(receipt.ReceiptContent, "(?<pre>.*)[ *\\t\\n\\r]+" +
            //  "** SALE **[ *\\t\\n\\r]+" +
            //  "(?<pump>PUMP *)[ *\\t\\n\\r]+" +
            //  "Terminal No.(?<after>.*)[ *\\t\\n\\r]+");

            //return match.Success ? Convert.ToByte(match.Groups["pump"].Value) : (byte)0;
        }

        /// <inheritdoc/>
        public byte GetPumpFromReceiptInfo(posReceiptInfo receipt)
        {
            return GetPumpFromReceiptInfo((ReceiptInfo)receipt);
        }

        /// <inheritdoc/>
        public Result<IEnumerable<ReceiptInfo>> GetReceipts(IPump pump, GetReceiptRequest request, bool includeExpiredReceipts = true)
        {
            IEnumerable<ReceiptInfo> GetTransactionReceipt()
            {
                var result = HydraDb.GetReceipt(request.CardNumber, request.TransactionNumber, includeExpiredReceipts);

                return result == null ? Enumerable.Empty<ReceiptInfo>() : new List<ReceiptInfo>() { result };
            }

            var results = !request.CardNumber.IsNullOrWhiteSpace()
                ? request.TransactionNumber > 0 ? GetTransactionReceipt()
                : HydraDb.GetReceipts(request.CardNumber, includeExpiredReceipts)
                : GetReceipts(pump).Value;

            return Result.Success(results);
        }

        /// <inheritdoc/>
        public Result<IEnumerable<ReceiptInfo>> GetReceipts(IPump pump) => pump.Opt == null ?
            Result.Failure<IEnumerable<ReceiptInfo>>("No OPT") :
            Result.Success(HydraDb.GetReceiptsForOpt(pump.Opt.IdString).AsEnumerable());

        protected string CentreJustifyReceiptLine(string text)
        {
            text = text.Trim();
            return text.IsNullOrWhiteSpace() ?
                string.Empty :
                text.PadLeft((PosReceiptWidth - text.Length) / 2);
        }

        public Result<IEnumerable<string>> GetRawReceiptText(ReceiptInfo info, bool centreAlign = false)
        {
            var receipt = info.ReceiptContent;

            var results = new List<string>();
            try
            {
                var receiptElement = XElement.Parse(receipt);
                foreach (var element in receiptElement.Elements())
                {
                    if (element.Attribute("Text") != null)
                    {
                        if (element.Attribute("IsSingleChar")?.Value == "1")
                        {
                            var character = element.Attribute("Text").Value[0];
                            results.Add(new string(character, PosReceiptWidth));
                        }                    
                        else
                        {
                            var text = element.Attribute("Text").Value;
                            results.Add(centreAlign ? CentreJustifyReceiptLine(text) : text);
                        }
                    }
                    else if (element.Attribute("Left") != null && element.Attribute("Right") != null)
                    {
                        results.Add($"{element.Attribute("Left")?.Value} {element.Attribute("Right")?.Value}");
                    }
                    else if (element.Attribute("Left") != null)
                    {
                        results.Add($"{element.Attribute("Left")?.Value}");
                    }
                    else if (element.Attribute("Right") != null)
                    {
                        results.Add($"{element.Attribute("Right")?.Value}");
                    }
                }              
            }
            catch (Exception ex)
            {
                var msg = receipt.Replace(Environment.NewLine, "\n").Replace("\n", Environment.NewLine);
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { $"Exception thrown for receipt: {msg}" }, ex);
                results.Add($"Receipt: {msg}");

                return Result.Failure<IEnumerable<string>>(msg);
            }

            return Result.Success(results.AsEnumerable());
        }
    }
}
