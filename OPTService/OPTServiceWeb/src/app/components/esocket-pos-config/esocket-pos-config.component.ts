import { Component, EventEmitter, NgZone, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { NGXLogger } from 'ngx-logger';
import { LoadingService } from 'src/app/services/loading.service';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';
import { Constants } from 'src/app/helpers/constants';
import { ESocketPosConfigDetails } from 'src/app/core/models/eSocketPosConfigDetails.model';

class Errors {
  EsocketUseConnectionString: boolean;
  EsocketOverrideProperties: boolean;
  EsocketConfigFile: boolean;
  EsocketOverrideKeystore: boolean;
  EsocketKeystoreFile: boolean;
  EsocketOverrideUrl: boolean;
  EsocketDbUrl: boolean;
  EsocketConnectionString: boolean;
}

@Component({
  selector: 'app-esocket-pos-config',
  templateUrl: './esocket-pos-config.component.html',
  styleUrls: ['./esocket-pos-config.component.css']
})
export class EsocketPosConfigComponent implements OnInit, OnDestroy {

  @Output()
  public eSocketConnected = new EventEmitter<boolean>();

  public fileLocationsData: ESocketPosConfigDetails;

  public configForm = this.fb.group({
    EsocketUseConnectionString: [false],
    EsocketOverrideProperties: [false],
    EsocketConfigFile: ['', [ Validators.required ]],
    EsocketOverrideKeystore: [false],
    EsocketKeystoreFile: ['', [ Validators.required ]],
    EsocketOverrideUrl: [false],
    EsocketDbUrl: ['', [ Validators.required ]],
    EsocketConnectionString: ['', [ Validators.required ]],
  });
  public errors: Errors = new Errors;
  public showContent: boolean = false;
  public configurationIsCollapsed: boolean = true;
  paymentConfigType: string = "";

  public readonly labelColClass: string = 'col-12 col-md-6 col-xl-2';
  public readonly radioColClass: string = 'col-12 col-md-6 col-xl-2';
  public readonly inputColClass: string = 'col-12 col-md-12 col-xl-8';

  private signalRData: Subscription | undefined;

  constructor(
    private fb: FormBuilder,
    private logger: NGXLogger,
    private zone: NgZone,
    private loadingService: LoadingService,
    private advancedConfigService: AdvancedConfigService,
  ) { 
    this.signalRData = this.advancedConfigService.getConfigDataChanged().subscribe((data) => {
      this.zone.run(() => {
        const showLoading = data === Constants.SignalRRefreshItemId;
        this.refreshData(showLoading);
      });
    });
  }

  ngOnInit(): void {
    setTimeout(() => {
      this.refreshData();
    });
  }

  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }


  refreshData(showLoading: boolean = true): void {
    this.logger.debug('eSocket POS Config data refresh');
    if(showLoading){
      //Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }
    
    this.advancedConfigService.getAdvancedConfig()
    .subscribe(data => {
      this.logger.debug('eSocket POS Config data', data.ESocketPosConfig);
      this.fileLocationsData = data.ESocketPosConfig || {} as ESocketPosConfigDetails;
      this.configForm.patchValue(this.fileLocationsData);
      Object.keys(this.configForm.controls).forEach(key => {
        this.configForm.controls[key].valueChanges.subscribe(val => {
          this.fileLocationsData[key] = val;
        });
      });
      this.paymentConfigType = data.PaymentConfigType;
      this.eSocketConnected.emit(this.fileLocationsData?.EsocketConnectionMade);
      this.showContent = this.paymentConfigType != "NONE";
    }, () => {
      this.logger.error('Problem getting eSocket POS Config data');
      this.loadingService.errorDuringLoading();
    }, () => {
      if (showLoading) {
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = this.paymentConfigType != "NONE";
      }
    });
  }

  /**
   * Updates EsocketUseConnectionString property
   */
  setEsocketUseConnectionString(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketUseConnectionString');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketUseConnectionString).subscribe(res => {
      this.logger.debug('EsocketUseConnectionString updated ok');
      this.errors.EsocketUseConnectionString = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketUseConnectionString');
      this.errors.EsocketUseConnectionString = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketOverrideProperties property
   */
  setEsocketOverrideProperties(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketOverrideProperties');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketOverrideProperties).subscribe(res => {
      this.logger.debug('EsocketOverrideProperties updated ok');
      this.errors.EsocketOverrideProperties = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketOverrideProperties');
      this.errors.EsocketOverrideProperties = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketConfigFile property
   */
  setEsocketConfigFile(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketConfigFile');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketConfigFile).subscribe(res => {
      this.logger.debug('EsocketConfigFile updated ok');
      this.errors.EsocketConfigFile = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketConfigFile');
      this.errors.EsocketConfigFile = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketOverrideKeystore property
   */
  setEsocketOverrideKeystore(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketOverrideKeystore');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketOverrideKeystore).subscribe(res => {
      this.logger.debug('EsocketOverrideKeystore updated ok');
      this.errors.EsocketOverrideKeystore = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketOverrideKeystore');
      this.errors.EsocketOverrideKeystore = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketKeystoreFile property
   */
  setEsocketKeystoreFile(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketKeystoreFile');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketKeystoreFile).subscribe(res => {
      this.logger.debug('EsocketKeystoreFile updated ok');
      this.errors.EsocketKeystoreFile = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketKeystoreFile');
      this.errors.EsocketKeystoreFile = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketOverrideUrl property
   */
  setEsocketOverrideUrl(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketOverrideUrl');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketOverrideUrl).subscribe(res => {
      this.logger.debug('EsocketOverrideUrl updated ok');
      this.errors.EsocketOverrideUrl = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketOverrideUrl');
      this.errors.EsocketOverrideUrl = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketDbUrl property
   */
  setEsocketDbUrl(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketDbUrl');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketDbUrl).subscribe(res => {
      this.logger.debug('EsocketDbUrl updated ok');
      this.errors.EsocketDbUrl = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketDbUrl');
      this.errors.EsocketDbUrl = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates EsocketConnectionString property
   */
  setEsocketConnectionString(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating EsocketConnectionString');
    this.advancedConfigService.setESocketProperty(this.fileLocationsData, environment.advancedController.setEsocketConnectionString).subscribe(res => {
      this.logger.debug('EsocketConnectionString updated ok');
      this.errors.EsocketConnectionString = false;
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem updating EsocketConnectionString');
      this.errors.EsocketConnectionString = true;
      ButtonHelper.reset(event);
    });
  }
}
