using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;
using OPT.Common.Helpers;
using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.ServiceProcess;
using Unity;

namespace OPTService
{
    ///
    ///  Name: OptService
    ///  Description: The Hydra OPT Service.
    public partial class OptService : ServiceBase
    {
        private static ServiceLoggable _loggable;
        private readonly ServiceApplication _serviceApplication;

        private static IHtecLogger GetLogger([CallerMemberName] string methodName = null) => _loggable.GetLogger(methodName);

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        private static void Main(string[] args)
        {
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            var container = UnityConfig.RegisterComponents();
            _loggable = new ServiceLoggable(container.Resolve<IHtecLogManager>(), ConfigConstants.ServiceName, container.Resolve<IConfigurationManager>());
            
            LoggingHelper.SetThreadName("Main");
            GetLogger().Info("OPT Service Initialising");

            try
            {
                var core = container.Resolve<ICore>();
                if (core.AllOpts == null)
                {
                    GetLogger().Error("Service was not created, probably invalid database connection string");
                    return;
                }

                var appRigger = new ServiceApplication(core, container.Resolve<IHtecLogManager>(), "OptService", container.Resolve<IConfigurationManager>());
                var optService = new OptService(appRigger);

                GetLogger().Info("OPT Service Initialised");

                if (Environment.UserInteractive)
                {
                    GetLogger().Debug("Running in UserInteractive mode.");
                    appRigger.SetHydraId("HydraIdInteractive");
                    optService.OnStart(args);
                    Console.WriteLine("Press the ENTER key to stop the OPT Service.");
                    Console.Read();

                    optService.OnStop();
                }
                else
                {
                    appRigger.SetHydraId("HydraId");
                    GetLogger().Debug("Starting OPTService");
                    Run(optService);
                }
            }
            catch (Exception exception)
            {
                GetLogger().Fatal(exception.Message, exception);
                throw;
            }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            const string logName = "OptService";

            if (!EventLog.SourceExists(logName))
            {
                EventLog.CreateEventSource(logName, logName);
            }
            
            var exceptionObject = e.ExceptionObject as Exception;
            
            EventLog.WriteEntry(logName, $"Unhandled exception in service:{Environment.NewLine}{Environment.NewLine}{exceptionObject?.Message}", EventLogEntryType.Error);
            GetLogger()?.Fatal("Unhandled exception occurred", exceptionObject);
        }

        /// <summary>
        /// Constructor for OPTService.
        /// </summary>
        public OptService(ServiceApplication serviceApplication)
        {
            InitializeComponent();
            _serviceApplication = serviceApplication;
        }

        /// <summary>
        /// Called when the service starts.
        /// Fetches end point details from the database to pass to the Site Controller
        /// to connect to the pump (simulator).
        /// Also starts the timer running.
        /// </summary>
        /// <param name="args">Any arguments passed in.</param>
        protected override void OnStart(string[] args)
        {
            var result = _serviceApplication.Start();
            if (!result.IsSuccess)
            {
                Stop();
            }
        }

        /// <summary>
        /// Called when the service stops.
        /// Stops the Site Controller.
        /// </summary>
        protected override void OnStop()
        {
            _serviceApplication.Stop();
        }
    }
}
