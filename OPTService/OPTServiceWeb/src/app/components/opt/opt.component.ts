import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { OptService } from '../../services/opt.service';
import { NGXLogger } from 'ngx-logger';
import { <PERSON><PERSON><PERSON>elper } from 'src/app/helpers/button.helper';
import { Opt } from 'src/app/core/models/opt.model';
import { AppComponent } from 'src/app/app.component';
import { IntegrationType } from '../../core/enums/integrationType.enum';

@Component({
  selector: 'app-opt',
  templateUrl: './opt.component.html',
  styleUrls: ['./opt.component.css']
})

/**
 * The OPT component class
 */
export class OptComponent implements OnInit {

  @Input()
  opt: Opt;

  @Input()
  tids: any;

  @Input()
  opts: any;

  @Input()
  maxFillOverride: any;  

  @Input()
  pumpCardStates: Map<string,string>;

  @Output()
  pumpCardStatesChange = new EventEmitter<Map<string, string>>();

  optForm = this.fb.group({
    StringId: [''],
    Mode: [''],
    ReceiptHeaders: this.fb.array([]),
    ReceiptFooters: this.fb.array([]),
    Status: [''],
    Connected: [''],
    PlaylistFileName: ['']
  });

  setPlaylistFileNameError: boolean;
  setContactlessError: boolean;

  receiptIsCollapsed: boolean;

  integratorType: any;
  secAuthType: any;

  /**
   * The OPT component constructor
   * @param fb The angular reactive forms Form Builder
   * @param optService The OPT service
   * @param logger The logger
   */
  constructor(private fb: FormBuilder,
    private optService: OptService,
    private logger: NGXLogger,
    private appComp: AppComponent) {

    this.integratorType = IntegrationType;
}

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    // Formulary assignments
    this.optForm.patchValue(this.opt);
    Object.keys(this.optForm.controls).forEach(key => {
      this.optForm.controls[key].valueChanges.subscribe(val => {
        this.opt[key] = val;
      });
    });

    this.secAuthType = this.appComp.integrators[IntegrationType.SecAuth];
  }

   /**
   * Sets the playlist filename
   */
  setPlaylistFileName(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('updating playlist filename', this.opt);
    this.setPlaylistFileNameError = false;
    this.optService.setPlaylistFileName(this.opt).subscribe(res => {
      this.logger.debug('playlist filename udpated ok');
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error('Problem playlist filename', error);
      this.setPlaylistFileNameError = true;
      ButtonHelper.reset(event);
    });
  }

  updateCardStates(data: Map<string,string>){
    this.pumpCardStatesChange.emit(data);
  }
}
