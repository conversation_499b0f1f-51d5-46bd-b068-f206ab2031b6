using FluentAssertions;
using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Xunit;

namespace OPT.HydraDb.Tests.Migrations
{
    public class MigrationableTests
    {
        /// <summary>
        /// Gets loadable classes from an assembly
        /// </summary>
        /// <param name="assembly">assembly object</param>
        /// <param name="predicate">Func delegate to run, to specify inclusion in results</param>
        /// <returns>Array of class types, empty if the class it's not loadable</returns>
        private static IEnumerable<Type> GetLoadableTypes(Assembly assembly, Func<Type, bool> predicate)
        {
            try
            {
                return assembly.GetTypes().Where(x => predicate(x));
            }
            catch (ReflectionTypeLoadException)
            {
                return Enumerable.Empty<Type>();
            }
        }

        private IEnumerable<Assembly> GetMigrationAssembly => new[] { typeof(HydraDb.Migrations.Migrationable).Assembly };

        private IEnumerable<Type> GetMigrationTypes(Func<Type, bool> predicate) => GetMigrationAssembly.SelectMany(s => GetLoadableTypes(s, predicate)).ToList();

        [Fact]
        public void test_we_have_some_migrations()
        {
            // Arrange
            var types = GetMigrationTypes((x) => x.GetCustomAttribute<MigrationAttribute>() != null).ToList();

            // Act
            var count = types?.Count ?? 0;

            // Assert
            count.Should().BeGreaterThan(0);
        }

        [Fact]
        public void test_all_migrations_have_unique_version()
        {
            // Arrange
            var types = GetMigrationTypes((x) => x.GetCustomAttribute<MigrationAttribute>() != null);

            var versionInfo = types.Select(x => x.GetCustomAttribute<MigrationAttribute>()).ToList();

            // Act
            var uniqueInfo = versionInfo.Select(x => x?.Version).Distinct().ToList();

            // Assert
            uniqueInfo.Count.Should().Be(versionInfo.Count);
        }
    }
}