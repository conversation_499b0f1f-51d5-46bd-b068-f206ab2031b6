using Forecourt.Core.HydraDb.Models;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Configuration.Hydra;

namespace Forecourt.Bos.Configuration
{
    /// <summary>
    /// Generic class for mapping Hydra data to an external system
    /// </summary>
    /// <typeparam name="TExternalGrade">External grade model type</typeparam>
    /// <typeparam name="TExternalProduct">External product model type</typeparam>
    /// <typeparam name="TExternalCurrency">External currency model type</typeparam>
    /// <typeparam name="TExternalVat">External VAT Rate model type</typeparam>
    /// <typeparam name="TExternalCardReference">External card referer model type</typeparam>
    public abstract class HydraToExternalMapping<TExternalGrade, TExternalProduct, TExternalCurrency, TExternalVat, TExternalCardReference> : IOrchestratedCommand
    {
        /// <summary>
        /// All Hydra reference data
        /// </summary>
        public Hydra.ReferenceData HydraData { get; } = new Hydra.ReferenceData();

        /// <summary>
        /// All External reference data
        /// </summary>
        public ReferenceData<TExternalGrade, TExternalProduct, TExternalCurrency, TExternalVat, TExternalCardReference> ExternalData { get; } = new ReferenceData<TExternalGrade, TExternalProduct, TExternalCurrency, TExternalVat, TExternalCardReference>();

        /// <summary>
        /// Mapping of Grades from the Hydra end
        /// </summary>
        public MappingData<byte, Grade, TExternalGrade> HydraGrades{ get; } = new MappingData<byte, Grade, TExternalGrade>();

        /// <summary>
        /// Mapping of Grades from the External end
        /// </summary>
        public MappingData<int, TExternalGrade, Grade> ExternalGrades { get; } = new MappingData<int, TExternalGrade, Grade>();

        /// <summary>
        /// Mapping of Products from the Hydra end
        /// </summary>
        public MappingData<string, TariffMapping, TExternalProduct> HydraProducts { get; } = new MappingData<string, TariffMapping, TExternalProduct>();

        /// <summary>
        /// Mapping of Products from the External end
        /// </summary>
        public MappingData<int, TExternalProduct, TariffMapping> ExternalProducts { get; } = new MappingData<int, TExternalProduct, TariffMapping>();

        /// <summary>
        /// Mapping of Currencies from the Hydra end
        /// </summary>
        public MappingData<string, Currency, TExternalCurrency> HydraCurrencies { get; } = new MappingData<string, Currency, TExternalCurrency>();

        /// <summary>
        /// Mapping of Currencies from the External end
        /// </summary>
        public MappingData<int, TExternalCurrency, Currency> ExternalCurrencies { get; } = new MappingData<int, TExternalCurrency, Currency>();

        /// <summary>
        /// Mapping of VatRate from the Hydra end
        /// </summary>
        public MappingData<string, VatRate, TExternalVat> HydraVatRates { get; } = new MappingData<string, VatRate, TExternalVat>();

        /// <summary>
        /// Mapping of VatRate from the External end
        /// </summary>
        public MappingData<string, TExternalVat, VatRate> ExternalVatRates { get; } = new MappingData<string, TExternalVat, VatRate>();

        //   CardReference>
        /// <summary>
        /// Mapping of CardReferences from the Hydra end
        /// </summary>
        public MappingData<int, CardReference, TExternalCardReference> HydraCardReferences { get; } = new MappingData<int, CardReference, TExternalCardReference>();

        /// <summary>
        /// Mapping of VatRate from the External end
        /// </summary>
        public MappingData<string, TExternalCardReference, CardReference> ExternalCardReferences { get; } = new MappingData<string, TExternalCardReference, CardReference>();

        /// <summary>
        /// Logging reference 
        /// </summary>
        public string LoggingReference { get; set; }

        string IOrchestratedCommand.CorrelationId { get => LoggingReference; set => LoggingReference = value; }
    }
}
