using Forecourt.Core.Enums;
using Forecourt.Core.UpdateFileClasses;
using Forecourt.Pump.HydraDb.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Workers;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using PSS_Forecourt_Lib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Timers;
using domsForecourt = PSS_Forecourt_Lib.Forecourt;
using HscMeterReadings = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.MeterReadings;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;

namespace OPT.Common.Workers
{
    // TODO: Update IDomsWorker for Pump, TankGuage and then Factories

    /// <summary>
    /// Abstracts the pump commands issued to DOMS (via PSS) away from PumpWorker
    /// </summary>
    public class DomsWorker : Workerable, IDomsWorker
    {
        private const string PosVersionId = "1.2.3.4";
        private const byte DefaultPosIdForFetched = 22;
        private const byte DefaultPosIdForState = 23;
        private const byte DefaultPosIdForTcp1 = 24;
        private const byte DefaultPosIdForTcp2 = 25;
        private const byte DefaultPosIdForTcp5 = 26;
        private const byte DefaultPosIdForTcpPrepared = 27;
        private const int ClearedTimeoutInSeconds = 10;
        private const int MaxFetchTries = 5;
        private const uint CountryCode = 44;

        private const string LightGreen = "33CC00";
        private const string Black = "000000";
        private const string Yellow = "FFFF00";
        private const string Red = "FF0000";
        private const string Pink = "FF99CC";
        private const string DarkGreen = "006600";
        private const string Cyan = "00FFFF";
        private const string Purple = "9999FF";

        public bool StateEnabled { get; private set; } = false;

        public bool StateConnected { get; private set; } = false;

        public IDomsSetup FetchedSetup { get; }
        public IDomsSetup TcpSetup { get; }
        public IDomsSetup TcpPreparedSetup { get; }

        public DomsState State { get; private set; } = new DomsState();

        private bool _stateReload = false;


        public string SoftwareVersion { get; private set; }
        public string SoftwareDate { get; private set; }

        //// TODO: GetVersionInfo
        //public string PssPosChecksum => _pssPosDll.IsAvailable ? _pssPosDll.ChecksumString : null;
        //public string PssTcpIpChecksum => _pssTcpIpDll.IsAvailable ? _pssTcpIpDll.ChecksumString : null;

        private INotificationWorker<EventType> _controllerWorker;
        //private IFromOptWorker _optWorker;
        private IPumpIntegratorOutJournal<IMessageTracking> _journalWorker => GetWorker<IPumpIntegratorOutJournal<IMessageTracking>>();
        private readonly IHydraDb _hydraDb;
        private volatile bool _masterReset;

        public bool Enabled =>
            StateEnabled || FetchedSetup.Enabled || TcpSetup.Enabled || TcpPreparedSetup.Enabled;

        private bool Reload => _stateReload || FetchedSetup.Reload || TcpSetup.Reload || TcpPreparedSetup.Reload;

        private bool AllConnected => (StateConnected || !StateEnabled) && (FetchedSetup.Connected || !FetchedSetup.Enabled) &&
                                     (TcpSetup.Connected || !TcpSetup.Enabled) && (TcpPreparedSetup.Connected || !TcpPreparedSetup.Enabled);

        public bool Connected => StateConnected || FetchedSetup.Connected || TcpSetup.Connected || TcpPreparedSetup.Connected;

        private volatile bool _detect;

        public IPAddress IpAddress { get; private set; }
        public string LoginString { get; private set; }

        private bool _loginRunning = false;
        private readonly object _lockObject = new object();
        private DateTime _loginWait = DateTime.Now;
        private int _loginFails = 0;

        private readonly ForecourtClass _fc;
        private domsForecourt Forecourt => _fc;
        private IFCConfig Config => _fc;
        private bool _fetchingState = false;
        private readonly object _stateLockObject = new object();
        private bool _fetchStateNeeded = true;

        private int _fetchTries = 0;

        private readonly IDictionary<byte, IList<uint>> _clearedDictionary = new ConcurrentDictionary<byte, IList<uint>>();
        private readonly IDictionary<byte, uint> _pumpAuth = new ConcurrentDictionary<byte, uint>();
        private readonly IDictionary<byte, bool> _pumpStopped = new ConcurrentDictionary<byte, bool>();
        private DateTime _clearedTimeout = DateTime.MaxValue;

        public int BackoffStopStart { get; set; }
        public int BackoffStopOnly { get; set; }

        //private readonly DllCheck _pssPosDll;
        //private readonly DllCheck _pssTcpIpDll;

        private bool _pssPosFetched = false;
        private bool _pssTcpIpFetched = false;

        private int _checksumAttempts = 0;

        private bool _pendingMeters = false;
        private bool _pendingDips = false;
        private readonly IList<HscMeterReadings> _meters = new List<HscMeterReadings>();
        private readonly IList<Dip> _dips = new List<Dip>();
        private readonly IList<HscPumpData> _pumps = new List<HscPumpData>();

        #region Initialisation

        public DomsWorker(IHydraDb hydraDb, IHtecLogger logger, IPumpIntegratorOutJournal<IMessageTracking> pumpOutJournal, IConfigurationManager configurationManager, ITimerFactory timeFactory)
            :base(logger, configurationManager, nameof(DomsWorker), timeFactory)
        {
            //RegisterWorker(pumpOutJournal ?? throw new ArgumentNullException(nameof(pumpOutJournal)));

            ////_pssPosDll = new DllCheck("PSSPOS.dll", GetLogger());
            ////_pssTcpIpDll = new DllCheck("PSSTcpIp.dll", GetLogger());
            //_hydraDb = hydraDb;
            //DomsInfo info = _hydraDb.GetDomsInfo();
            //_detect = info?.Detect ?? false;
            //bool enabled = _detect || (info?.Enabled ?? false);
            //string address = info?.IpAddress ?? string.Empty;
            //if (IPAddress.TryParse(address, out IPAddress ipAddress))
            //{
            //    GetLogger().Debug($"Results of query, Address is {ipAddress}");
            //    IpAddress = ipAddress;
            //}
            //else
            //{
            //    GetLogger().Error($"Unable to parse {address}");
            //    IpAddress = null;
            //}

            //LoginString = info?.LoginString ?? string.Empty;
            //try
            //{
            //    _fc = new ForecourtClass();
            //}
            //// ReSharper disable once EmptyGeneralCatchClause
            //catch
            //{
            //}

            //FetchedSetup = new DomsSetupPosProtocol(null, DefaultPosIdForFetched, GetLogger(), true);
            //TcpSetup = new DomsSetupTcp(null, DefaultPosIdForTcp1, DefaultPosIdForTcp2, DefaultPosIdForTcp5, GetLogger(), true);
            //TcpPreparedSetup = new DomsSetupTcp(null, DefaultPosIdForTcpPrepared, DefaultPosIdForTcpPrepared, DefaultPosIdForTcpPrepared, GetLogger(), false);
            //SetEnabled(enabled);
            //InitialisePreparedSetup();
        }

        public void RegisterWorker(INotificationWorker<EventType> worker)
        {
            //_controllerWorker = worker;
            //FetchedSetup.RegisterWorker(worker);
            //TcpSetup.RegisterWorker(worker);
            //TcpPreparedSetup.RegisterWorker(worker);
        }

        //public void RegisterWorker(IFromOptWorker worker)
        //{
        //    _optWorker = worker;
        //    FetchedSetup.RegisterWorker(worker);
        //    TcpSetup.RegisterWorker(worker);
        //    TcpPreparedSetup.RegisterWorker(worker);
        //}

        public void SetIpAddress(IPAddress ipAddress)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_hydraDb.SetDomsIpAddress(ipAddress);
            //IpAddress = ipAddress;
            //SetReload();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetLoginString(string loginString)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_hydraDb.SetDomsLoginString(loginString);
            //LoginString = loginString;
            //SetReload();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void SetEnabled(bool isEnabled)
        {
            //FetchedSetup.SetEnabled(false);
            //TcpSetup.SetEnabled(isEnabled);
            //TcpPreparedSetup.SetEnabled(isEnabled);
            //StateEnabled = false;
        }

        private void SetReload()
        {
            //FetchedSetup.SetReload();
            //TcpSetup.SetReload();
            //TcpPreparedSetup.SetReload();
            //_stateReload = StateEnabled;
        }

        public void Enable()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_hydraDb.SetDomsEnabled(true);
            //_hydraDb.SetDomsDetect(false);
            //SetEnabled(true);
            //_detect = false;
            //_fetchStateNeeded = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void MasterReset()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_masterReset = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void Detect()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_hydraDb.SetDomsDetect(true);
            //SetEnabled(true);
            //if (!Connected)
            //{
            //    _detect = true;
            //}

            //_fetchStateNeeded = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void Disable()
        {
            //// TODO: This should not be happening, as they should be separate classes!!!
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_hydraDb.SetDomsEnabled(false);
            //_hydraDb.SetDomsDetect(false);
            //SetEnabled(false);
            //_detect = false;
            //_fetchStateNeeded = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void CheckState()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_fetchStateNeeded = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void ReconnectDoms()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //SetReload();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void InitialisePreparedSetup()
        {
            //TcpPreparedSetup.SetPriceSetId(2);
            //TcpPreparedSetup.SetGradePrice(1, 1, 1259);
            //TcpPreparedSetup.SetGradePrice(2, 1, 1359);
            //TcpPreparedSetup.SetGradePrice(3, 1, 1459);
            //TcpPreparedSetup.SetGradePrice(4, 1, 1559);
            //TcpPreparedSetup.SetGradePrice(5, 1, 1659);
            //TcpPreparedSetup.SetGradePrice(6, 1, 1759);
            //TcpPreparedSetup.SetGradePrice(7, 1, 2159);
            //TcpPreparedSetup.SetGradePrice(8, 1, 3159);
            //TcpPreparedSetup.SetGradePrice(9, 1, 4159);
            //TcpPreparedSetup.SetGradePrice(1, 3, 1258);
            //TcpPreparedSetup.SetGradePrice(2, 3, 1358);
            //TcpPreparedSetup.SetGradePrice(3, 3, 1458);
            //TcpPreparedSetup.SetGradePrice(4, 3, 1558);
            //TcpPreparedSetup.SetGradePrice(5, 3, 1658);
            //TcpPreparedSetup.SetGradePrice(6, 3, 1758);
            //TcpPreparedSetup.SetGradePrice(7, 3, 2158);
            //TcpPreparedSetup.SetGradePrice(8, 3, 3158);
            //TcpPreparedSetup.SetGradePrice(9, 3, 4158);
            //TcpPreparedSetup.SetGradeName(1, "Unleaded");
            //TcpPreparedSetup.SetGradeName(2, "Diesel");
            //TcpPreparedSetup.SetGradeName(3, "LPG");
            //TcpPreparedSetup.SetGradeName(4, "LRP");
            //TcpPreparedSetup.SetGradeName(5, "Gas Oil");
            //TcpPreparedSetup.SetGradeName(6, "SUL");
            //TcpPreparedSetup.SetGradeName(7, "Diesel 2");
            //TcpPreparedSetup.SetGradeName(8, "Adblue");
            //TcpPreparedSetup.SetGradeName(9, "Kerosene");
            //TcpPreparedSetup.SetGradeColour(1, LightGreen);
            //TcpPreparedSetup.SetGradeColour(2, Black);
            //TcpPreparedSetup.SetGradeColour(3, Yellow);
            //TcpPreparedSetup.SetGradeColour(4, Red);
            //TcpPreparedSetup.SetGradeColour(5, Pink);
            //TcpPreparedSetup.SetGradeColour(6, DarkGreen);
            //TcpPreparedSetup.SetGradeColour(7, Black);
            //TcpPreparedSetup.SetGradeColour(8, Cyan);
            //TcpPreparedSetup.SetGradeColour(9, Purple);
            //TcpPreparedSetup.SetGradeOption(4, 1, 1);
            //TcpPreparedSetup.SetGradeOption(4, 2, 2);
            //TcpPreparedSetup.SetGradeOption(4, 3, 6);
            //TcpPreparedSetup.SetGradeOption(5, 1, 1);
            //TcpPreparedSetup.SetGradeOption(5, 2, 3);
            //TcpPreparedSetup.SetGradeOption(5, 3, 4);
            //TcpPreparedSetup.SetPumpIpAddressAndPort(4, "***************", 10000);
            //TcpPreparedSetup.SetPumpPhysicalAddress(5);
            //TcpPreparedSetup.SetPumpDecimalPositionInMoney(4, 2);
            //TcpPreparedSetup.ClearPumpDecimalPositionInMoney(5);
        }

        public void ResetDoms()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //TcpPreparedSetup.SendToDoms(true);
            //_fetchStateNeeded = true;
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }
       
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            //if (Connected && (Reload || !Enabled))
            //{
            //    DisconnectDoms();
            //}

            //if (Enabled && !AllConnected)
            //{
            //    LoginDoms();
            //}

            //if (Enabled && TcpPreparedSetup.Connected && _masterReset)
            //{
            //    MasterResetDoms();
            //}

            //if (_clearedDictionary.Count > 0)
            //{
            //    ClearPayments();
            //}

            //if (_fetchStateNeeded)
            //{
            //    FetchState();
            //}

        //    if (_pssPosDll.IsAvailable)
        //    {
        //        if (!_pssPosFetched)
        //        {
        //            _controllerWorker?.SendInformation($"DOMS - PSS POS File Name is {_pssPosDll.FileName}");
        //            _controllerWorker?.SendInformation($"DOMS - PSS POS CRC32 Checksum is {_pssPosDll.ChecksumString}");
        //            _pssPosFetched = true;
        //            _controllerWorker?.PushChange(EventType.AboutChanged, EventType.DomsChanged);
        //        }
        //    }
        //    else if (Enabled && _checksumAttempts < MaxFetchTries)
        //    {
        //        try
        //        {
        //            // ReSharper disable once UnusedVariable
        //            ForecourtClass fc = new ForecourtClass();
        //        }
        //        catch (Exception)
        //        {
        //            GetLogger().Warn("Error loading DOMS DLL");
        //        }

        //        _pssPosDll.Process();
        //    }

        //    if (_pssTcpIpDll.IsAvailable)
        //    {
        //        if (!_pssTcpIpFetched)
        //        {
        //            _controllerWorker?.SendInformation($"DOMS - PSS TcpIp File Name is {_pssTcpIpDll.FileName}");
        //            _controllerWorker?.SendInformation($"DOMS - PSS TcpIp CRC32 Checksum is {_pssTcpIpDll.ChecksumString}");
        //            _pssTcpIpFetched = true;
        //            _controllerWorker?.PushChange(EventType.AboutChanged, EventType.DomsChanged);
        //        }
        //    }
        //    else if (Enabled && _checksumAttempts < MaxFetchTries)
        //    {
        //        try
        //        {
        //            // ReSharper disable once UnusedVariable
        //            ForecourtClass fc = new ForecourtClass();
        //        }
        //        catch (Exception)
        //        {
        //            GetLogger().Warn("Error loading DOMS DLL");
        //        }

        //        _pssTcpIpDll.Process();
        //    }

        //    _checksumAttempts++;
        }

        private void LoginDoms()
        {
            //bool run = false;
            //lock (_lockObject)
            //{
            //    if (_loginRunning)
            //    {
            //        GetLogger().Warn("DOMS login already running");
            //    }
            //    else if (DateTime.Now > _loginWait)
            //    {
            //        _loginRunning = true;
            //        run = true;
            //    }
            //}

            //if (run)
            //{
            //    string fetchedLoginString = LoginString.Replace("APPL_ID=", "APPL_ID=F");
            //    string stateLoginString = LoginString.Replace("APPL_ID=", "APPL_ID=S");
            //    string tcp1LoginString = LoginString.Replace("APPL_ID=", "APPL_ID=T1");
            //    string tcp2LoginString = LoginString.Replace("APPL_ID=", "APPL_ID=T2");
            //    string tcp5LoginString = LoginString.Replace("APPL_ID=", "APPL_ID=T5");
            //    string tcpPreparedLoginString = LoginString.Replace("APPL_ID=", "APPL_ID=TP");
            //    GetLogger().Info($"Attempting DOMS connection, IP Address is {IpAddress}," + $" Fetched Login String is {fetchedLoginString}," +
            //                 $" Prepared Login String is {tcpPreparedLoginString}," + $" TCP 1 Login String is {tcp1LoginString}," +
            //                 $" TCP 2 Login String is {tcp2LoginString}," + $" TCP 5 Login String is {tcp5LoginString}," +
            //                 $" State Login String is {stateLoginString}");
            //    try
            //    {
            //        if (FetchedSetup.Enabled && !FetchedSetup.Connected)
            //        {
            //            _controllerWorker?.SendInformation("DOMS - Calling DOMS Login (Fetched)");
            //            bool success = FetchedSetup.LoginDoms(IpAddress.ToString(), fetchedLoginString, string.Empty, string.Empty,
            //                CountryCode, PosVersionId, new List<DomsMessageId>(), new List<DomsMessageId>(), new List<DomsMessageId>());
            //            _controllerWorker?.SendInformation($"DOMS - DOMS Login {(success ? "Done" : "Failed")} (Fetched)");
            //        }

            //        if (TcpSetup.Enabled && !TcpSetup.Connected)
            //        {
            //            _controllerWorker?.SendInformation("DOMS - Calling DOMS Login (TCP)");
            //            bool success = TcpSetup.LoginDoms(IpAddress.ToString(), tcp1LoginString, tcp2LoginString, tcp5LoginString,
            //                CountryCode, PosVersionId, new List<DomsMessageId>(), new List<DomsMessageId>
            //                {
            //                    new DomsMessageId(DomsMessageCode.PosConnectionStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.PssPeripheralsStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.FcStatus, 0x02),
            //                    new DomsMessageId(DomsMessageCode.FpStatus, 0x03),
            //                    new DomsMessageId(DomsMessageCode.FpSupTransBufStatus, 0x03),
            //                    new DomsMessageId(DomsMessageCode.EptStatus, 0x01),
            //                    new DomsMessageId(DomsMessageCode.TgStatus, 0x01),
            //                    new DomsMessageId(DomsMessageCode.PpStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.FcPriceSetStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.OperationModeStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.SiteDeliveryStatus, 0x01),
            //                    new DomsMessageId(DomsMessageCode.WpStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.DiopStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.EptDeviceStatus, 0x00),
            //                    new DomsMessageId(DomsMessageCode.FcInstallStatus, 0x01)
            //                }, new List<DomsMessageId>
            //                {
            //                    new DomsMessageId(DomsMessageCode.FpUnSupTransBufStatus, 0x03),
            //                    new DomsMessageId(DomsMessageCode.BnaExchangeRateStatus, 0x00)
            //                });
            //            _controllerWorker?.SendInformation($"DOMS - DOMS Login {(success ? "Done" : "Failed")} (TCP)");
            //        }

            //        if (TcpPreparedSetup.Enabled && !TcpPreparedSetup.Connected)
            //        {
            //            _controllerWorker?.SendInformation("DOMS - Calling DOMS Login (TCP Prepared)");
            //            bool success = TcpPreparedSetup.LoginDoms(IpAddress.ToString(), tcpPreparedLoginString, string.Empty, string.Empty,
            //                CountryCode, PosVersionId, new List<DomsMessageId>(), new List<DomsMessageId>(), new List<DomsMessageId>());
            //            _controllerWorker?.SendInformation($"DOMS - DOMS Login {(success ? "Done" : "Failed")} (TCP Prepared)");
            //        }

            //        if (StateEnabled && !StateConnected && Forecourt != null)
            //        {
            //            try
            //            {
            //                Forecourt.HostName = IpAddress.ToString();
            //                Forecourt.PosId = DefaultPosIdForState;
            //                Forecourt.Initialize();

            //                Forecourt.FcOperationModeChanged += OpModeChanged;
            //                Forecourt.FcStatusChanged += StatusChanged;
            //                Forecourt.TerminalStatusChanged += TerminalStatusChanged;
            //                Forecourt.FuellingPointStatusChanged += FuellingPointStatusChanged;
            //                Forecourt.DIOStatusChanged += DioStatusChanged;
            //                Forecourt.PricePoleStatusChanged += PricePoleStatusChanged;
            //                Forecourt.TankGaugeStatusChanged += TankGaugeStatusChanged;
            //                Forecourt.SiteDeliveryStatusChanged += SiteDeliveryStatusChanged;
            //                _controllerWorker?.SendInformation("DOMS - Calling DOMS Login (State)");
            //                FcLogonParms parameters = new FcLogonParms();
            //                parameters.EnableFcEvent(FcEvents.FcOperationModeChanged);
            //                parameters.EnableFcEvent(FcEvents.FcStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.TerminalStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.FuellingPointStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.DIOStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.PricePoleStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.TankGaugeStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.SiteDeliveryStatusChanged);
            //                parameters.EnableFcEvent(FcEvents.xxxxCfgChanged);
            //                Forecourt.FcLogon2(stateLoginString, parameters);
            //                StateConnected = true;
            //                _controllerWorker?.SendInformation("DOMS - DOMS Login Done (State)");
            //            }
            //            catch (Exception e)
            //            {
            //                GetLogger().Error("Exception logging in to DOMS", e);
            //            }
            //        }

            //        _fetchStateNeeded = true;

            //        _controllerWorker?.PushChange(EventType.AboutChanged);
            //        if (AllConnected)
            //        {
            //            _loginFails = 0;
            //        }
            //        else
            //        {
            //            _loginFails++;
            //            if (_loginFails > 20)
            //            {
            //                _loginWait = DateTime.Now.AddSeconds(60);
            //            }
            //            else if (_loginFails > 10)
            //            {
            //                _loginWait = DateTime.Now.AddSeconds(10);
            //            }
            //        }
            //    }
            //    catch (Exception e)
            //    {
            //        GetLogger().Error("Exception connecting DOMS", e);
            //        GetLogger().Info($"DOMS error HResult is {e.HResult:x8}");
            //        GetLogger().Info($"DOMS error Text is {Forecourt?.HResult2Text(e.HResult) ?? "Unavailable"}");
            //    }

            //    lock (_lockObject)
            //    {
            //        _loginRunning = false;
            //    }
            //}
        }

        private void DisconnectDoms()
        {
            //bool run = false;
            //lock (_lockObject)
            //{
            //    if (_loginRunning)
            //    {
            //        GetLogger().Warn("DOMS login already running");
            //    }
            //    else
            //    {
            //        _loginRunning = true;
            //        run = true;
            //    }
            //}

            //if (run)
            //{
            //    _controllerWorker?.SendInformation("DOMS - Disconnecting DOMS");
            //    if (FetchedSetup.Connected)
            //    {
            //        FetchedSetup.Disconnect();
            //    }

            //    if (StateConnected)
            //    {
            //        try
            //        {
            //            Forecourt.Disconnect();
            //            StateConnected = false;
            //            _stateReload = false;
            //            _controllerWorker?.SendInformation("DOMS - DOMS Disconnected (State)");
            //        }
            //        catch (Exception e)
            //        {
            //            GetLogger().Error("Exception disconnecting DOMS", e);
            //            GetLogger().Info($"DOMS error HResult is {e.HResult:x8}");
            //            GetLogger().Info($"DOMS error Text is {Forecourt?.HResult2Text(e.HResult) ?? "Unavailable"}");
            //            _controllerWorker?.SendInformation("DOMS - Disconnection Failed (State)");
            //        }
            //    }

            //    if (TcpSetup.Connected)
            //    {
            //        TcpSetup.Disconnect();
            //    }

            //    if (TcpPreparedSetup.Connected)
            //    {
            //        TcpPreparedSetup.Disconnect();
            //    }

            //    _fetchStateNeeded = true;

            //    lock (_lockObject)
            //    {
            //        _loginRunning = false;
            //    }
            //}
        }

        private void MasterResetDoms()
        {
            //bool run = false;
            //lock (_lockObject)
            //{
            //    if (_loginRunning)
            //    {
            //        GetLogger().Warn("DOMS login already running");
            //    }
            //    else
            //    {
            //        _loginRunning = true;
            //        run = true;
            //    }
            //}

            //if (run)
            //{
            //    _controllerWorker?.SendInformation("DOMS - Master Resetting DOMS");
            //    try
            //    {
            //        if (TcpPreparedSetup.Enabled && TcpPreparedSetup.Connected)
            //        {
            //            string tcpPreparedLoginString = LoginString.Replace("APPL_ID=", "APPL_ID=MR") + ",MASTER-RESET";
            //            GetLogger().Info($"DOMS - Master Reset using access code {tcpPreparedLoginString}");
            //            bool success = TcpPreparedSetup.SendOneLogin(tcpPreparedLoginString, CountryCode, PosVersionId,
            //                new List<DomsMessageId>());
            //            _controllerWorker?.SendInformation($"DOMS - Master Reset {(success ? "Done" : "Failed")} (TCP Prepared)");
            //            _masterReset = false;
            //            _fetchStateNeeded = true;
            //        }
            //    }
            //    catch (Exception e)
            //    {
            //        GetLogger().Error("Exception master resetting DOMS", e);
            //        GetLogger().Info($"DOMS error HResult is {e.HResult:x8}");
            //        GetLogger().Info($"DOMS error Text is {Forecourt?.HResult2Text(e.HResult) ?? "Unavailable"}");
            //        _controllerWorker?.SendInformation("DOMS - Master Reset Failed");
            //    }

            //    lock (_lockObject)
            //    {
            //        _loginRunning = false;
            //    }
            //}
        }

        private void FetchState()
        {
            //bool run = false;
            //lock (_stateLockObject)
            //{
            //    if (!_fetchingState)
            //    {
            //        _fetchingState = true;
            //        run = true;
            //    }
            //}

            //if (run)
            //{
            //    if (StateEnabled && StateConnected)
            //    {
            //        try
            //        {
            //            if (StateEnabled && StateConnected)
            //            {
            //                State = new DomsState(StateEnabled, StateConnected, Forecourt, Config);
            //            }
            //        }
            //        catch (Exception e)
            //        {
            //            GetLogger().Error("Exception raised fetching state", e);
            //        }
            //    }
            //    else
            //    {
            //        State = new DomsState(StateEnabled, StateConnected);
            //    }

            //    if (Enabled && AllConnected)
            //    {
            //        FetchedSetup.FetchFromDoms();
            //        IDomsSetup previousSetup = TcpSetup.Replicate();
            //        TcpSetup.FetchFromDoms();
            //        if (!(string.Equals(SoftwareVersion, TcpSetup.SoftwareVersion) && string.Equals(SoftwareDate, TcpSetup.SoftwareDate)))
            //        {
            //            SoftwareVersion = TcpSetup.SoftwareVersion;
            //            SoftwareDate = TcpSetup.SoftwareDate;
            //            _controllerWorker?.PushChange(EventType.AboutChanged);
            //        }

            //        _fetchStateNeeded = TcpSetup.AllPumps().Count == 0 && _fetchTries < MaxFetchTries;
            //        _fetchTries = _fetchStateNeeded ? _fetchTries + 1 : 0;
            //        if (_pendingMeters)
            //        {
            //            _meters.Clear();
            //        }

            //        if (_pendingDips)
            //        {
            //            _pumps.Clear();
            //            _dips.Clear();
            //        }

            //        TcpSetup.FetchStatePump(previousSetup);

            //        foreach (DomsSetupPump pump in TcpSetup.AllPumps())
            //        {
            //            bool sendState = false;
            //            DomsSetupPump prevPump = previousSetup.AllPumps().FirstOrDefault(x => x.FpId == pump.FpId) ??
            //                                     new DomsSetupPump(pump.FpId);
            //            // ReSharper disable once ArrangeRedundantParentheses
            //            if (pump.MainState != prevPump.MainState ||
            //                (pump.AllTransactions.Count == 0) != (prevPump.AllTransactions.Count == 0))
            //            {
            //                sendState = true;
            //                _controllerWorker?.SendInformation(
            //                    $"DOMS - Fuelling Point {pump.FpId} state changed from {prevPump.MainState} to {pump.MainState}");
            //            }

            //            if (sendState)
            //            {
            //                IList<byte> grades = new List<byte>();
            //                foreach (byte gradeId in pump.AllGradeOptions())
            //                {
            //                    grades.Add(gradeId);
            //                }

            //                byte theHose = 0;
            //                byte theGrade = pump.GradeId;
            //                uint theAmount = 0;
            //                uint theVolume = 0;
            //                ushort thePrice = 0;
            //                foreach (DomsSetupTransaction trans in pump.AllTransactions)
            //                {
            //                    if (theAmount == 0 || theAmount == trans.CashAmount)
            //                    {
            //                        theAmount = trans.CashAmount;
            //                        theVolume = trans.ActualVolume;
            //                        theGrade = trans.GradeId;
            //                        theHose = 0;
            //                        thePrice = 0;
            //                    }
            //                }

            //                PumpState pumpState = PumpState.Closed;
            //                switch (pump.MainState)
            //                {
            //                    case DomsSetupMainState.Idle:
            //                        pumpState = PumpState.Idle;
            //                        break;
            //                    case DomsSetupMainState.Preauthorised:
            //                        pumpState = PumpState.Idle;
            //                        break;
            //                    case DomsSetupMainState.Fuelling:
            //                        pumpState = PumpState.Delivering;
            //                        break;
            //                    case DomsSetupMainState.Calling:
            //                        pumpState = PumpState.Request;
            //                        break;
            //                    case DomsSetupMainState.Starting:
            //                        pumpState = PumpState.Authorise;
            //                        break;
            //                    case DomsSetupMainState.FuellingTerminated:
            //                        pumpState = PumpState.Finished;
            //                        break;
            //                }

            //                //_optWorker?.OnPumpState(null, pumpState, pump.FpId, theHose, theGrade, theVolume, theAmount, thePrice,
            //                //    pump.AllTransactions.Count == 0, grades);
            //                if (_pendingMeters)
            //                {
            //                    HscMeterReadings meter = new HscMeterReadings
            //                    {
            //                        Pump = pump.FpId
            //                    };
            //                    _meters.Add(meter);
            //                }

            //                if (_pendingDips)
            //                {
            //                    HscPumpData pumpData = new HscPumpData {PumpNumber = pump.FpId, Dispenser = new HscDispenser()};
            //                    _pumps.Add(pumpData);
            //                }

            //                if (pump.MainState == DomsSetupMainState.Fuelling)
            //                {
            //                    _fetchStateNeeded = true;
            //                }

            //                if (_pumpAuth.ContainsKey(pump.FpId) && pump.MainState == DomsSetupMainState.Fuelling)
            //                {
            //                    const uint cashAmount = 0;
            //                    if (BackoffStopOnly > 0 && cashAmount > _pumpAuth[pump.FpId] - BackoffStopOnly)
            //                    {
            //                        EmergencyStop(pump.FpId);
            //                        _pumpStopped[pump.FpId] = true;
            //                    }
            //                    else if (BackoffStopStart > 0 && cashAmount > _pumpAuth[pump.FpId] - BackoffStopStart)
            //                    {
            //                        EmergencyStop(pump.FpId);
            //                        CancelEmergencyStop(pump.FpId);
            //                    }
            //                }
            //            }

            //            if (_pendingMeters)
            //            {
            //                _journalWorker?.OnMeters(_meters.Select(x =>
            //                {
            //                    var coreReadings = (CoreMeterReadings)x;
            //                    return coreReadings;
            //                }).ToList());
            //                _pendingMeters = false;
            //            }

            //            //if (_pendingDips)
            //            //{
            //            //    // TODO: PriceChange????
            //            //    foreach (HscPumpData pumpData in _pumps)
            //            //    {
            //            //        _journalWorker?.PumpDataReceived(pumpData);
            //            //    }

            //            //    // TODO: TankGauge not Pump Journal
            //            //    _journalWorker?.DipsReceived(_dips);
            //            //    _pendingDips = false;
            //            //}
            //        }
            //    }

            //    lock (_stateLockObject)
            //    {
            //        _fetchingState = false;
            //    }

            //    _controllerWorker?.PushChange(EventType.DomsChanged);
            //}
        }

        #endregion

        #region Actions

        // TODO: Refactor

        public void OpenPump(byte fpId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    TcpSetup.OpenPump(fpId);               
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void ClosePump(byte fpId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Close Pump, enabled {TcpSetup.Enabled}, connected {TcpSetup.Connected}");
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    TcpSetup.ClosePump(fpId);               
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void Authorise(byte fpId, uint limit)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    bool success = TcpSetup.Authorise(fpId, limit);
            //    if (success)
            //    {
            //        _controllerWorker?.SendInformation("DOMS - Authorisation sent");
            //        _fetchStateNeeded = true;
            //    }
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void ClearTransaction(byte fpId, int seqNo)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    TcpSetup.ClearTransaction(fpId, seqNo);                
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public bool PaymentApproved(byte fpId, uint limit)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    bool success = TcpSetup.Authorise(fpId, limit);
            //    if (success)
            //    {
            //        _controllerWorker?.SendInformation("DOMS - Authorisation sent");
            //        _fetchStateNeeded = true;
            //        _pumpAuth[fpId] = limit;
            //        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            //        return true;
            //    }              
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return false;
        }

        // TODO: For HSC where is this called from !?!?!?!?!
        public bool PaymentCancelled(byte fpId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    bool success = TcpSetup.CancelAuthorise(fpId);
            //    if (success)
            //    {
            //        _controllerWorker?.SendInformation("DOMS - Cancellation sent");
            //        _fetchStateNeeded = true;
            //        _pumpAuth.Remove(fpId);
            //        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            //        return true;
            //    }              
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return false;
        }

        private void ClearPayments()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (TcpSetup.Enabled && TcpSetup.Connected)
            {
                DateTime now = DateTime.Now;
                IList<byte> fpDone = new List<byte>();
                foreach (byte fpId in _clearedDictionary.Keys)
                {
                    IList<uint> tbd = new List<uint>();
                    foreach (uint amount in _clearedDictionary[fpId])
                    {
                        int seqNo = TcpSetup.AllPumps().FirstOrDefault(x => x.FpId == fpId)?.AllTransactions
                                        .FirstOrDefault(y => y.ActualAmount == amount)?.SequenceNumber ?? 0;
                        _controllerWorker?.SendInformation($"DOMS - Cleared List FP ID is {fpId}, amount is {amount}, seq no is {seqNo}");
                        if (seqNo == 0)
                        {
                            _controllerWorker?.SendInformation("DOMS - Transaction not found");
                            tbd.Add(amount);
                        }

                        {
                            _controllerWorker?.SendInformation($"DOMS - Clearing, FP is {fpId}, limit is {amount}");
                            TcpSetup.ClearTransaction(fpId, 0, seqNo);
                        }
                    }

                    if (tbd.Count > 0 && _clearedTimeout > now)
                    {
                        _clearedDictionary[fpId] = tbd;
                    }
                    else
                    {
                        fpDone.Add(fpId);
                    }
                }

                foreach (byte fpId in fpDone)
                {
                    _clearedDictionary.Remove(fpId);
                }
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public bool PaymentCleared(byte fpId, uint limit)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (!_clearedDictionary.ContainsKey(fpId))
            {
                _clearedDictionary[fpId] = new List<uint>();
            }

            _clearedDictionary[fpId].Add(limit);
            _clearedTimeout = DateTime.Now.AddSeconds(ClearedTimeoutInSeconds);
            _pumpAuth.Remove(fpId);
            if (_pumpStopped.ContainsKey(fpId) && _pumpStopped[fpId])
            {
                CancelEmergencyStop(fpId);
                _pumpStopped.Remove(fpId);
            }

            ClearPayments();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return true;
        }

        public bool EmergencyStop(byte fpId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    TcpSetup.EmergencyStop(fpId);                
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return false;
        }

        public bool CancelEmergencyStop(byte fpId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    TcpSetup.CancelEmergencyStop(fpId);            
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return false;
        }

        public void FetchMeters()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    _fetchStateNeeded = true;
            //    _pendingMeters = true;
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RequestDips()
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (TcpSetup.Enabled && TcpSetup.Connected)
            //{
            //    _fetchStateNeeded = true;
            //    _pendingDips = true;
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public bool SetGradePrices(IList<FuelPriceItem> updates)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (!StateEnabled || !StateConnected || Config == null)
            //{
            //    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            //    return false;
            //}

            //try
            //{
            //    Config.GetPrices2(PriceSetTypes.PST_ACTUAL_PRICE_SET, out byte priceSetId, out PriceGroupCollection prices, out _);
            //    foreach (FuelPriceItem update in updates)
            //    {
            //        foreach (PriceGroup pg in prices)
            //        {
            //            foreach (GradePrice gp in pg.GradePrices)
            //            {
            //                if (gp.Id == update.Fuel)
            //                {
            //                    gp.Price = update.Ppu / (float) 1000.0;
            //                }
            //            }
            //        }
            //    }

            //    try
            //    {
            //        Config.SetPrices(priceSetId, prices);
            //        _fetchStateNeeded = true;
            //    }
            //    catch (Exception e)
            //    {
            //        GetLogger().Error("DOMS error setting prices", e);
            //        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            //        return false;
            //    }
            //}
            //catch (Exception e)
            //{
            //    GetLogger().Error("Other error setting prices", e);
            //    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            //    return false;
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return true;
        }

        public void SetGradeName(byte grade, string text)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //if (StateEnabled && StateConnected && Config != null)
            //{
            //    foreach (Grade gradeName in Config.Grades)
            //    {
            //        if (gradeName.Id == grade)
            //        {
            //            _controllerWorker?.SendInformation($"DOMS - Should be changing name for grade {gradeName.Id}" +
            //                                               $" from {gradeName.Text}" + $" to {text}");
            //        }
            //    }
            //}

            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Callbacks

        private void OpModeChanged(byte modeNumber, ITimeStamp timestamp)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Op Mode Changed, mode number is {modeNumber}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void StatusChanged(FcStatus status)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation(
            //    $"DOMS - Status Changed, status 1 is {status.FcStatus1Flags}, status 2 is {status.FcStatus2Flags}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void TerminalStatusChanged(Terminal terminal, TermMainStates mainState, byte subState, byte errorState)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Terminal Status Changed, terminal is {terminal.Id}, main state is {mainState}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void FuellingPointStatusChanged(FuellingPoint fp, byte actSmId, FpMainStates mainState, byte subState, byte lockId)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation(
            //    $"DOMS - Fuelling Point Status Changed, id is {fp.Id}, main state is {mainState}, Act SM Id {actSmId}, sub state {subState}, Lock ID {lockId}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void DioStatusChanged(DIO dio, DIOStatusParmCollection dioStatusParmCollection)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - DIO Status Changed, id is {dio.Id}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void PricePoleStatusChanged(PricePole pp, PpMainStates mainState, byte subState)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Price Pole Status Changed, Id is {pp.Id}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void TankGaugeStatusChanged(TankGauge tg, TgMainStates mainState, byte status, int alarmStatus)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Tank Gauge Status Changed, Id is {tg.Id}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void SiteDeliveryStatusChanged(byte statusFlags, byte deliveryReportSeqNo, TankGaugeCollection tankGauges)
        {
            //GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            //_controllerWorker?.SendInformation($"DOMS - Site Delivery Status Changed, Status Flags {statusFlags}");
            //FetchState();
            //GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion
    }
}
