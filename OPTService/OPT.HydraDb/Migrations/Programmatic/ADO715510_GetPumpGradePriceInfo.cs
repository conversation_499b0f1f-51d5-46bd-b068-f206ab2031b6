using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(28000002, "ADO#715510 - Add all dbo.PumpGradePriceInfo related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO715510_GetPumpGradePriceInfo : Migrationable
    {
        /// <inheritdoc/>
        public ADO715510_GetPumpGradePriceInfo(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetPumpGradePriceInfo", "dbo.UpsertPumpGradePriceInfo" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO646362_GetTransactionBookingState), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO646362_GetTransactionBookingState), DbObjects.Reverse().ToArray());
        }
    }
}
