import { DOCUMENT } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { environment } from 'src/environments/environment';

import { FuelPriceService } from './fuel-price.service';

describe('FuelPriceService', () => {
  let service: FuelPriceService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        FuelPriceService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    service = TestBed.inject(FuelPriceService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('.getFuelPrices() should handle success response from API', () => {
    //Arrange
    let mockedData = [
      {
        Grade: 1,
        GradeName: "Unleaded",
        VatRate: 20,
        PriceToSet: 136.9,
        Prices: [
          {
            Pump: 1,
            Price: 136.9
          },
          {
            Pump: 2,
            Price: 136.9
          }
        ]
      },
      {
        Grade: 2,
        GradeName: "Diesel",
        VatRate: 20,
        PriceToSet: 145.9,
        Prices: [
          {
            Pump: 1,
            Price: 145.9
          },
          {
            Pump: 2,
            Price: 145.9
          }
        ]
      }
    ];

    //Act
    service.getFuelPrices().subscribe((data) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(mockedData);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.getFuelPrices);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedData);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getFuelPrices() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    service.getFuelPrices().subscribe(response => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.getFuelPrices);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrice() should handle success response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded",
      VatRate: 20,
      PriceToSet: 130.9,
    };

    //Act
    service.setPrice(fuelPrice).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setPrice);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrice() should handle unsuccess response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded",
      VatRate: 20,
      PriceToSet: 130.9,
    };

    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrice(fuelPrice).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setPrice);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGradeName() should handle success response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded2",
      VatRate: 20,
      PriceToSet: 130.9,
    };

    //Act
    service.setGradeName(fuelPrice).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setGradeName);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGradeName() should handle unsuccess response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded2",
      VatRate: 20,
      PriceToSet: 130.9,
    };

    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setGradeName(fuelPrice).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setGradeName);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGradeVatRate() should handle success response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded",
      VatRate: 23,
      PriceToSet: 130.9,
    };

    //Act
    service.setGradeVatRate(fuelPrice).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setGradeVatRate);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGradeVatRate() should handle unsuccess response from API', () => {
    //Arrange
    let fuelPrice: any = {
      Grade: 2,
      GradeName: "Unleaded",
      VatRate: 23,
      PriceToSet: 130.9,
    };

    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setGradeVatRate(fuelPrice).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.fuelPriceController.base + environment.fuelPriceController.setGradeVatRate);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
});
