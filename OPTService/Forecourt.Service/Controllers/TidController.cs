using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Linq;
using System.Web.Http;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class TidController : LoggableApiController
    {
        private readonly IWeb _web;

        #region Constructor
        /// <summary>
        /// TID Controller constructor
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="web"></param>
        public TidController([Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger, IWeb web) : base(logger)
        {
            _web = web ?? throw new ArgumentNullException();
        }
        #endregion


        #region Get Data

        /// <summary>
        /// Gets all TIDs.
        /// </summary>
        /// <param name="reference">The Logging reference</param>
        /// <response code="200">Success.</response>
        /// <response code="404">No TIDs found.</response>
        /// <returns>An array containg the results.</returns>
        [HttpGet]
        public IHttpActionResult GetTidList(string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));
            GetLogger().Debug(FormatMessage($"Parameters -> reference: {reference}"));

            // Getting the TIDs
            var tids = _web.Tids();

            // Return 404 is no TID is found
            if (!tids.Any())
            {
                GetLogger().Debug(FormatMessage($"No TID(s) found. Returning Not Found. Reference: {reference}"));
                return NotFound();
            }

            // Returning 200 and the result
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));

            return Ok(tids);
        }

        #endregion
    }
}
