using CSharpFunctionalExtensions;
using Forecourt.Core.Payment.Enums;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace Forecourt.Core.Pump.Models
{
    /// <summary>
    /// Model for transaction states.
    /// </summary>
    public class PumpTransaction
    {
        private readonly Stopwatch _stopWatch;
        private readonly DateTime _localStartTime;

        /// <summary>
        /// Creates a new instance of the <see cref="PumpTransaction"/>
        /// </summary>
        /// <param name="pump">The parent pump for the transaction</param>
        public PumpTransaction(byte pump)
        {
            Pump = pump;
            TransactionStates = new ConcurrentDictionary<PumpState, PumpTransactionState>();
            PaymentStates = new ConcurrentDictionary<PaymentResult, PumpTransactionPaymentState>();
            LoggingReference = $"{Guid.NewGuid()}";
            HasBeenActioned = false;
            _stopWatch = Stopwatch.StartNew();
            _localStartTime = DateTime.UtcNow;
        }

        /// <summary>
        /// UTC time based upon UTC time of class instantiation.
        /// </summary>
        public DateTime CurrentUtcTime => _localStartTime.AddMilliseconds(_stopWatch.ElapsedMilliseconds);

        /// <summary>
        /// UTC time for the end of the transaction.
        /// </summary>
        /// <remarks>Used to capture time changes during a transaction.</remarks>
        public DateTime LocalEndTime { get; set; }

        /// <summary>
        /// The parent pump for the transaction.
        /// </summary>
        public byte Pump { get; }

        /// <summary>
        /// All stages/states of the Transaction.
        /// </summary>
        public IDictionary<PumpState, PumpTransactionState> TransactionStates { get; }

        /// <summary>
        /// All payments stages/states of the Transaction
        /// </summary>
        public IDictionary<PaymentResult, PumpTransactionPaymentState> PaymentStates { get; }

        /// <summary>
        /// Helps correlate the logging entries across thr transaction.
        /// </summary>
        public string LoggingReference { get; }

        /// <summary>
        /// Latest PumpState equivalent for this transaction.
        /// </summary>
        public PumpState LastPumpState { get; set; }

        /// <summary>
        /// The Validation Result.
        /// </summary>
        public Result ValidationResult { get; set; }

        /// <summary>
        /// Have any Actions being taken for this transaction?
        /// </summary>
        public bool HasBeenActioned { get; protected set; }

        /// <summary>
        /// Does the pump have an error?
        /// </summary>
        public bool HasPumpError { get; set; }

        /// <summary>
        /// Indicate that all Actions have been taken.
        /// </summary>
        public void SetActionsComplete(bool value = true)
        {
            HasBeenActioned = value;
        }

        /// <summary>
        /// Is the transaction in a state where it can continue?
        /// </summary>
        /// <returns>Whether continue state is valid.</returns>
        public bool IsContinuationState()
        {
            return HasPumpError
                   || IsMinimumPumpStates()
                   && PaymentStates.Any(x => x.Key == PaymentResult.ApprovedAndAuthorised || x.Key == PaymentResult.Approved);
        }

        /// <summary>
        /// Is the transaction in a default state, for an OPT transaction?
        /// </summary>
        /// <returns>Whether the state is default.</returns>
        public bool IsDefaultState()
        {
            return IsMinimumPumpStates()
                   && PaymentStates.Count <= 1
                   && PaymentStates.All(x => x.Key == PaymentResult.Cleared);
        }

        private bool IsMinimumPumpStates(PumpState state = PumpState.Idle)
        {
            return TransactionStates.Count <= 2 + (state == PumpState.Idle ? 0 : 1)
                   && TransactionStates.All(x => x.Key == PumpState.Idle || x.Key == PumpState.Request || x.Key == state);
        }

        /// <summary>
        /// Is the transaction in a default state, for a Kiosk transaction?
        /// </summary>
        /// <returns>Whether the state is default.</returns>
        public bool IsDefaultKioskState()
        {
            return IsMinimumPumpStates(PumpState.Authorise)
                   && PaymentStates.Count <= 1
                   && PaymentStates.All(x => x.Key == PaymentResult.Cleared);
        }

        /// <summary>
        /// Resets a transaction, retaining the logging reference and any payment approved state.
        /// </summary>
        public void ResetRetainingPaymentApproved()
        {
            CardInsertedCount = 0;
            TransactionStates.Clear();
            PaymentStates.Remove(PaymentResult.Cleared);
            PaymentStates.Remove(PaymentResult.Cancelled);
        }

        /// <summary>
        /// Joins the current stage reference, with the overall Transaction reference
        /// </summary>
        /// <param name="reference">Reference for the current stage of the transaction</param>
        /// <returns>Full Transaction Id</returns>
        public string GetFullId(string reference)
        {
            return $"{reference}/{LoggingReference}";
        }

        /// <summary>
        /// Has a Payment Authorise been registered within the given time period, in ms
        /// </summary>
        /// <param name="intervalInMs">Interval to check, in ms</param>
        /// <param name="utcNow">DateTime to take interval from </param>
        /// <returns></returns>
        public bool WasPaymentAuthorisedWithin(int intervalInMs, DateTime? utcNow = null)
        {
            if (!IsAuthorised(true, false))
            {
                return false;
            }

            return !DoWasAuthorisedPriorTo((utcNow == null || utcNow == DateTime.MinValue ? DateTime.UtcNow : utcNow.Value) - TimeSpan.FromMilliseconds(intervalInMs), false);
        }

        private bool DoWasAuthorisedPriorTo(DateTime dtm, bool checkTransactionState = true)
        {
            var authState = TransactionStates.FirstOrDefault(x => x.Key == PumpState.Authorise);
            var authPayment = PaymentStates.FirstOrDefault(x => x.Key == PaymentResult.ApprovedAndAuthorised);

            return ((authPayment.Value?.WhenProcessed ?? DateTime.MaxValue) <= dtm) && (!checkTransactionState || (authState.Value?.LastEntered ?? DateTime.MaxValue) <= dtm);
        }

        /// <summary>
        /// Has an Authorise been registered prior to a datetime, checks PaymentStates and optionally TransactionStates
        /// </summary>
        /// <param name="dtm">Datetime to check</param>
        /// <param name="checkTransactionState"></param>
        /// <returns></returns>
        public bool WasAuthorisedPriorTo(DateTime dtm, bool checkTransactionState = true)
        {
            if (!IsAuthorised(true, checkTransactionState))
            {
                return false;
            }

            return DoWasAuthorisedPriorTo(dtm, checkTransactionState);
        }

        /// <summary>
        /// Has a ApprovedAndAuthorise payment state, and/or a Authorise transaction/pump state been registered
        /// </summary>
        /// <param name="checkPaymentState">Check payment states, default to true</param>
        /// <param name="checkTransactionState">Check transaction/pump states, default to true</param>
        /// <returns></returns>
        public bool IsAuthorised(bool checkPaymentState = true, bool checkTransactionState = true)
        {
            return (!checkPaymentState || PaymentStates.Any(x => x.Key == PaymentResult.ApprovedAndAuthorised) &&
                (!checkTransactionState || TransactionStates.Any(x => x.Key == PumpState.Authorise)));
        }

        /// <summary>
        /// Has a transaction received the in progress states, i.e. Delivery or Finished
        /// </summary>
        /// <returns></returns>
        public bool HasIntermediateStates()
        {
            return TransactionStates.Any(x => x.Key == PumpState.Delivering || x.Key == PumpState.Finished);
        }

        /// <summary>
        /// Number of times the CardInserted message has been received, for this transaction
        /// </summary>
        public int CardInsertedCount { get; set; }
    }
}
