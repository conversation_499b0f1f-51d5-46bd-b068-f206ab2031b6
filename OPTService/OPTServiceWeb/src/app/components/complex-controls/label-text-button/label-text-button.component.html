<div class="row form-group" [formGroup]="form">
    <div class="input-group" [ngClass]="labelColClass">
        <label class="col-form-label text-truncate" [for]="id" [title]="labelText">{{labelText}}</label>
    </div>
    <div class="input-group" [ngClass]="textColClass">
        <input type="text" class="form-control" [id]="id" [formControl]="control"
            [ngClass]="{'is-invalid':control?.invalid && (control?.dirty || control?.touched)}"
            [placeholder]="placeHolderText" [attr.aria-describedby]="fieldFeedbackId" [maxlength]="maxLength"
            [readonly]="disabled">
        <div class="input-group-append d-inline" *ngIf="appendText">
            <span class="input-group-text">{{appendText}}</span>
        </div>
        <div [id]="fieldFeedbackId" class="invalid-feedback">
            {{validationErrorText}}
        </div>
    </div>
    <div [ngClass]="buttonColClass">
        <button type="button" [id]="buttonId" class="btn btn-primary" (click)="doAction($event)"
            [disabled]="control?.invalid || disabled">{{buttonText}}</button>
        <span class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="errorInActionText"
            *ngIf="errorInAction"><i class="bi bi-exclamation-triangle"></i></span>
    </div>
</div>