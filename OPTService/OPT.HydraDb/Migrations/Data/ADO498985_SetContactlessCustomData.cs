using FluentMigrator;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set Contactless Custom Data overrides (Magical Advanced Config keys)
    /// </summary>
    [Migration(20040002, "ADO#498985 - Set Contactless Custom Data overrides (in advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO498985_SetContactlessCustomData : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            const string DefaultClient = ClientSettings.DefaultValueClient;

            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            switch (client.ToLower())
            {
                case DefaultClient:
                    Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO498985_upgrade_app_config_install.sql");
                    break;

                default:
                    Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO498985_upgrade_app_config_rollback.sql");
                    break;

            };
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.ADO498985_upgrade_app_config_rollback.sql");
        }
    }
}
