import { <PERSON><PERSON><PERSON>cy<PERSON>ipe, DecimalPipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { GenericOptConfig } from 'src/app/core/models/genericOptConfig.model';
import { ADVANCED_SERVICE_PROVIDER, ADVANCED_SERVICE_SPY } from 'src/app/services/advanced-config.service.spy';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { LOCAL_ACCOUNT_SERVICE_PROVIDER, LOCAL_ACCOUNT_SERVICE_SPY } from 'src/app/services/local-account.service.spy';
import { LOYALTY_SERVICE_PROVIDER, LOYALTY_SERVICE_SPY } from 'src/app/services/loyalty.service.spy';
import { OPT_SERVICE_PROVIDER, OPT_SERVICE_SPY } from 'src/app/services/opt.service.spy';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { NGX_LOGGER_PROVIDER, NGX_LOGGER_SPY } from 'src/app/testing/ngxlogger.spy';

import { GenericOptConfigComponent } from './generic-opt-config.component';

describe('GenericOptConfigComponent', () => {
  let fixture: ComponentFixture<GenericOptConfigComponent>;
  let component: GenericOptConfigComponent;

  beforeEach(async () => {
    const formBuilder: FormBuilder = new FormBuilder();

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        FormsModule,
        ReactiveFormsModule,
        NgbModule
      ],
      providers: [
        CurrencyPipe,
        DecimalPipe,
        GenericOptConfigComponent,
        FormBuilder,
        NGX_LOGGER_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        LOCAL_ACCOUNT_SERVICE_PROVIDER(),
        LOYALTY_SERVICE_PROVIDER(),
        OPT_SERVICE_PROVIDER(),
        { provide: FormBuilder, useValue: formBuilder },
        ADVANCED_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    SIGNAL_R_SERVICE_SPY().getLocalAccountsSignalRMessage.and.returnValue(of({}));
    SIGNAL_R_SERVICE_SPY().getGenericOptConfigSignalRMessage.and.returnValue(of({}));
    LOCAL_ACCOUNT_SERVICE_SPY().getLocalAccounts.and.returnValue(of({}));
    OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(of({} as GenericOptConfig));
    OPT_SERVICE_SPY().reloadOptConfiguration.and.returnValue(of({}));
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of());
    ADVANCED_SERVICE_SPY().getIsConfigBatch.and.returnValue(of(undefined));
    fixture = TestBed.createComponent(GenericOptConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('.refreshData()', () => {

    it('.refreshData() should handle success responses from services', () => {
      OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(of({} as GenericOptConfig));
      ADVANCED_SERVICE_SPY().getIsConfigBatch.and.returnValue(of({}));

      component.refreshData();

      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalled();
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalled();
      expect(component.genericOptConfigFormError).toBeFalse();
    });

    it('.refreshData() should handle unsuccess responses from services', () => {
      OPT_SERVICE_SPY().getGenericOptConfig.and.returnValue(throwError({status:500}));
      ADVANCED_SERVICE_SPY().getIsConfigBatch.and.returnValue(throwError({status:500}));

      component.refreshData();

      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalled();
      expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalled();
      expect(component.genericOptConfigFormError).toBeTrue();
    });
  });

  it('.updateServiceAddress() should handle success response from service', () =>{
    component.genericOptConfigForm.controls['ServiceAddress'].setValue('');
    OPT_SERVICE_SPY().setGenericServiceAdress.and.returnValue(of({}));
    
    component.updateServiceAddress();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(2);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(component.updateServiceAddressError).toBe(false);
  });

  it('.updateServiceAddress() should handle failure response from service', () =>{
    component.genericOptConfigForm.controls['ServiceAddress'].setValue('');
    OPT_SERVICE_SPY().setGenericServiceAdress.and.returnValue(throwError({status:500}));
    
    component.updateServiceAddress();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(component.updateServiceAddressError).toBe(true);
  });

  it('.updateLoyalty() should handle success response from service', () =>{
    component.genericOptConfigForm.controls['MorrisonLoyaltyAvailable'].setValue('');
    LOYALTY_SERVICE_SPY().addLoyalty.and.returnValue(of({}));
    LOYALTY_SERVICE_SPY().deleteLoyalty.and.returnValue(of({}));
    
    component.updateLoyalty('');

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(2);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(component.morrisonsLoyaltyError).toBe(false);
  });

  it('.updateReceiptTimeout() should handle success response from service', () => {
    //Arrange
    OPT_SERVICE_SPY().setReceiptReprintAvailability.and.returnValue(of({}));
    
    //Act
    component.updateReceiptReprintAvailability();

    //Assert
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalled();
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalled();
    expect(component.receiptReprintAvailabilityError).toBe(false);
  });

  it('.updateReceiptTimeout() should handle unsuccess response from service', () => {
    //Arrange
    OPT_SERVICE_SPY().setReceiptReprintAvailability.and.returnValue(throwError({status: 500}));

    //Act
    component.updateReceiptReprintAvailability();

    //Assert
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalled();
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalled();
    expect(component.receiptReprintAvailabilityError).toBe(true);
  });

  it('.updateReceiptMaxCount() should handle success response from service', () => {
    //Arrange
    OPT_SERVICE_SPY().setReceiptMaxCount.and.returnValue(of({}));
    
    //Act
    component.updateReceiptMaxCount();

    //Assert
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalled();
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalled();
    expect(component.receiptMaxCountError).toBe(false);
  });

  it('.updateReceiptMaxCount() should handle unsuccess response from service', () => {
    //Arrange
    OPT_SERVICE_SPY().setReceiptMaxCount.and.returnValue(throwError({status: 500}))
    
    //Act
    component.updateReceiptMaxCount();

    //Assert
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalled();
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalled();
    expect(component.receiptMaxCountError).toBe(true);
  });

  it('.reloadOptConfiguration() should handle success response from service', () =>{
    OPT_SERVICE_SPY().reloadOptConfiguration.and.returnValue(of({}));

    component.reloadOptConfiguration();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(2);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(component.reloadOptConfigurationError).toBe(false);
  });

  it('.reloadOptConfiguration() should handle failure response from service', () =>{
    OPT_SERVICE_SPY().reloadOptConfiguration.and.returnValue(throwError({status:500}));

    component.reloadOptConfiguration();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(component.reloadOptConfigurationError).toBe(true);
  });

  it('.toggleIsConfigBatch() stopConfigBatch should handle success response from service', () =>{
    component.isConfigBatch = true;
    OPT_SERVICE_SPY().startConfigBatch.and.returnValue(of({}));
    OPT_SERVICE_SPY().stopConfigBatch.and.returnValue(of({}));
    
    component.toggleIsConfigBatch();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(2);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(component.batchError).toBe(false);
  });

  it('.toggleIsConfigBatch() stopConfigBatch should handle failure response from service', () =>{
    component.isConfigBatch = true;
    OPT_SERVICE_SPY().startConfigBatch.and.returnValue(of({}));
    OPT_SERVICE_SPY().stopConfigBatch.and.returnValue(throwError({status:500}));

    component.toggleIsConfigBatch();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(component.batchError).toBe(true);
  });

});
