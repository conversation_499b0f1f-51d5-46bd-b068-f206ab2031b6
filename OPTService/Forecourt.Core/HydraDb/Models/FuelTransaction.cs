using Htec.Common.Extensions;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using System;
using lrmFuelTransaction = OPT.Common.HydraDb.Models.FuelTransaction;

namespace Forecourt.Core.HydraDb.Models
{
    /// <inheritdoc cref="lrmFuelTransaction"/>
    public class FuelTransaction : lrmFuelTransaction
    {
        /// <inheritdoc cref="lrmFuelTransaction"/>
        public FuelTransaction() : base() { }

        /// <inheritdoc cref="lrmFuelTransaction"/>
        public FuelTransaction(long transactionId, DateTime transactionTime, string gradeCode, string washCode, string gradeName, string washName,
            string pumpDetails, string cardNumber, long fuelQuantity, int washQuantity, long amount, string fuelCategory,
            string washCategory, string fuelSubcategory, string washSubcategory, string discountName, string discountCode,
            long discountValue, string discountCardNumber, long localAccountMileage, string localAccountRegistration, string txnNumber) : 
            base(transactionId, transactionTime, gradeCode, washCode, gradeName, washName,
            pumpDetails, cardNumber, fuelQuantity, washQuantity, amount, fuelCategory, 
            washCategory, fuelSubcategory, washSubcategory, discountName, discountCode, 
            discountValue, discountCardNumber, localAccountMileage, localAccountRegistration, txnNumber)
        { }

        /// <summary>
        /// Convert from <see cref="FuelTransaction"/> to <see cref="TransactionItem"/> by using a standard .net cast
        /// </summary>
        /// <param name="ft"><see cref="FuelTransaction"/> instance</param>
        public static explicit operator TransactionItem(FuelTransaction ft)
        {
            if (ft == null)
            {
                return null;
            }

            var grade = Convert.ToByte(ft.GradeCode?.Replace("FUEL", string.Empty));
            var txnNumber = int.TryParse(ft.TxnNumber, out var num) ? num : 0;

            var result = new TransactionItem()
            {
                RecordType = TransactionFileItem.RecTypeCode.SalesItem,
                Id = ft.TransactionId,
                Number = ft.TxnNumber,
                TransactionNumber = txnNumber,
                CardNumber = ft.CardNumber,
                PumpDetails = ft.PumpDetails,
                Grade = grade,
                GradeName = ft.GradeName,
                Code = ft.GradeCode,
                Name = ft.GradeName,
                DateTime = ft.TransactionTime,
                Category = ft.FuelCategory,
                SubCategory = ft.FuelSubcategory,
                Quantity = ft.FuelQuantity,
                Value = ft.Amount,
                IsLocalAccount = ft.LocalAccountMileage > 0 || !ft.LocalAccountRegistration.IsNullOrWhiteSpace(),
                DiscountCard = ft.DiscountCardNumber,
                DiscountCode = ft.DiscountCode,
                DiscountName = ft.DiscountName,
                DiscountValue = ft.DiscountValue,
            };

            return result;
        }
    }
}