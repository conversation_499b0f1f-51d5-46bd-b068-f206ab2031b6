namespace Forecourt.SecondaryAuth.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the ITelemetryWorker, related to Pump/TankGauge
    /// </summary>
    public interface ITelemetryWorker : Htec.Foundation.Connections.Workers.Interfaces.ITelemetryWorker
    {
        /// <summary>
        /// Register message sent to Secondary Authorisation Host
        /// </summary>
        /// <param name="pump">Pump for which message has been sent.</param>
        /// <param name="loggingReference">Logging reference</param>
        void MessageSentToSecAuthHost(byte pump, string loggingReference);

        /// <summary>
        /// Register message received from Secondary Authorisation Host
        /// </summary>
        /// <param name="pump">Pump for which message has been received.</param>
        /// <param name="loggingReference">Logging reference</param>
        void MessageReceivedFromSecAuthHost(byte pump, string loggingReference);

        /// <summary>
        /// Register timeout waiting for response from Secondary Authorisation Host
        /// </summary>
        /// <param name="pump">Pump for which message was sent.</param>
        /// <param name="loggingReference">Logging reference</param>
        void MessageTimeoutFromSecAuthHost(byte pump, string loggingReference = null);
    }
}
