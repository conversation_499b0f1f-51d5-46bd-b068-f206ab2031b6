using System;

namespace OPTService.OPTServiceClasses
{
    public class ShiftEndDetails
    {
        /// <summary>
        /// The ShiftEnd time
        /// </summary>
        public DateTime ShiftEndTime;

        /// <summary>
        /// The DayEnd time
        /// </summary>
        public DateTime DayEndTime;

        /// <summary>
        /// The optional NextDayEnd
        /// </summary>
        public DateTime? NextDayEnd;

        /// <summary>
        /// The log interval details
        /// </summary>
        public LogIntervalDetails LogInterval;

        /// <summary>
        /// The unmanned flag
        /// </summary>
        public bool Unmanned;

        /// <summary>
        /// The AsdaDayEndReport flag
        /// </summary>
        public bool AsdaDayEndReport;

        /// <summary>
        /// The printer details
        /// </summary>
        public PrinterDetails PrinterDetails;
    }
}
