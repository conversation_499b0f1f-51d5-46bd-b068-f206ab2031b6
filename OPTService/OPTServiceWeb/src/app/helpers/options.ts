import { Constants } from "./constants";

export abstract class Options {

  static readonly SiteTypes = [
    { key: 'Retail', value: Constants.RetailSiteType },
    { key: 'RDC', value:  Constants.RdcSiteType },
    { key: 'Airfield', value:  Constants.AirfieldSiteType },
    { key: 'Marina', value:  Constants.MarinaSiteType },
  ];

  static readonly PosTypes = [
    { key: Constants.POS_TYPE_NONE, value: Constants.POS_TYPE_NONE },
    { key: Constants.POS_TYPE_RETALIX, value: Constants.POS_TYPE_RETALIX },
    { key: Constants.POS_TYPE_HYDRAPOS, value: Constants.POS_TYPE_HYDRAPOS },
    { key: Constants.POS_TYPE_3RDPARTY, value: Constants.POS_TYPE_3RDPARTY },
    { key: Constants.POS_TYPE_SIGNALR, value: Constants.POS_TYPE_SIGNALR },
];

  static readonly DiscountCardTypes = [
    { key: 'P1', value: 'P1' },
    { key: 'F1', value: 'F1' },
    { key: 'F2', value: 'F2' },
  ];

  static readonly ReceiptLayouts = [
    { key: 'Asda', value: 0 },
    { key: 'Morrisons', value: 1 },
    { key: 'MFG', value: 2 },
    { key: 'Independent', value: 3 },
    { key: 'Other', value: 4 },
  ];
}
