using OPTService.OPTServiceClasses;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class LoyaltyController : ApiController
    {
        private const string WebName = "Web";
        private readonly IWeb _web;

        public IWeb Web => _web;

        public LoyaltyController(IWeb web)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        #region Update Data

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be available.
        /// </summary>
        /// <param name="details">Incudes Loyalty Name.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddLoyalty(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Add {name} Loyalty");
            string result = Web.AddLoyalty(name);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be available.
        /// </summary>
        /// <param name="details">Incudes Loyalty Name.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage DeleteLoyalty(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Delete {name} Loyalty");
            string result = Web.DeleteLoyalty(name);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be present or absent.
        /// </summary>
        /// <param name="details">Incudes Loyalty Name and Present flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyPresent(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            bool present = details.Present;
            Web.SendChatMessage(WebName, $"Set {name} Loyalty {(present ? "Present" : "Absent")}");
            string result = Web.SetLoyaltyPresent(name, present);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Site ID for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Incudes Site ID to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalSiteId(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string siteId = details.SiteId?.Trim() ?? string.Empty;
            if (siteId.All(char.IsDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal Site ID {siteId}");
                string result = Web.SetLoyaltyTerminalSiteId(name, siteId);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Site ID");
            }
        }

        /// <summary>
        /// Set the Terminal ID for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Terminal ID to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalTerminalId(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string terminalId = details.TerminalId?.Trim() ?? string.Empty;
            if (terminalId.All(char.IsDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal Terminal ID {terminalId}");
                string result = Web.SetLoyaltyTerminalTerminalId(name, terminalId);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Terminal ID");
            }
        }

        /// <summary>
        /// Set the Footer 1 for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Footer 1 to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalFooter1(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string footer1 = details.Footer1 ?? string.Empty;
            Web.SendChatMessage(WebName, $"Set Loyalty {name} Terminal Footer 1 {footer1}");
            string result = Web.SetLoyaltyTerminalFooter1(name, footer1);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Footer 2 for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Footer 2 to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalFooter2(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string footer2 = details.Footer2 ?? string.Empty;
            Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal Footer 2 {footer2}");
            string result = Web.SetLoyaltyTerminalFooter2(name, footer2);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Timeout for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Timeout to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalTimeout(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            int timeout = details.Timeout;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal Timeout {timeout}");
            string result = Web.SetLoyaltyTerminalTimeout(name, timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the API Key for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes API Key to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalApiKey(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string apiKey = details.ApiKey ?? string.Empty;
            Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal API Key {apiKey}");
            string result = Web.SetLoyaltyTerminalApiKey(name, apiKey);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the HTTP Header for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes HTTP Header to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyTerminalHttpHeader(LoyaltyDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string httpHeader = details.HttpHeader ?? string.Empty;
            Web.SendChatMessage(WebName, $"Set {name} Loyalty Terminal HTTP Header {httpHeader}");
            string result = Web.SetLoyaltyTerminalHttpHeader(name, httpHeader);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add a host to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes IP Address and Port of host to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyHostsAddHost(LoyaltyEndPointDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string address = details.IpAddress?.Trim() ?? string.Empty;
            int port = details.Port;
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Port Number");
            }

            if (IPAddress.TryParse(address, out IPAddress ip))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Hosts Add Host {ip}:{port}");
                string result = Web.SetLoyaltyHostsAddHost(name, ip, port);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid IP Address");
            }
        }

        /// <summary>
        /// Remove a host from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes IP Address and Port of host to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyHostsRemoveHost(LoyaltyEndPointDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string address = details.IpAddress?.Trim() ?? string.Empty;
            int port = details.Port;
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Port Number");
            }

            if (IPAddress.TryParse(address, out IPAddress ip))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Hosts Remove Host {ip}:{port}");
                string result = Web.SetLoyaltyHostsRemoveHost(name, ip, port);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid IP Address");
            }
        }

        /// <summary>
        /// Add a hostname to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Hostname of host to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyAddHostname(LoyaltyEndPointDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string hostname = details.Hostname?.Trim() ?? string.Empty;
            Web.SendChatMessage(WebName, $"Set {name} Loyalty Hostnames Add Hostname {hostname}");
            string result = Web.SetLoyaltyHostnamesAddHostname(name, hostname);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Remove a hostname from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Hostname of host to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyRemoveHostname(LoyaltyEndPointDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string hostname = details.Hostname?.Trim() ?? string.Empty;

            Web.SendChatMessage(WebName, $"Set {name} Loyalty Hostnames Remove Hostname {hostname}");
            string result = Web.SetLoyaltyHostnamesRemoveHostname(name, hostname);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add an IIN to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Bottom and Top  of IIN range to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyIinsAddIin(LoyaltyIinDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string low = details.Low?.Trim() ?? string.Empty;
            string high = details.High?.Trim() ?? string.Empty;
            if (low.All(char.IsDigit) && high.All(char.IsDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Iins Add Iin {low}{(low == high ? string.Empty : $" to {high}")}");
                string result = Web.SetLoyaltyIinsAddIin(name, low, high);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in IIN");
            }
        }

        /// <summary>
        /// Remove an IIN from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Bottom and Top of IIN range to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyIinsRemoveIin(LoyaltyIinDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string low = details.Low?.Trim() ?? string.Empty;
            string high = details.High?.Trim() ?? string.Empty;
            if (low.All(char.IsDigit) && high.All(char.IsDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Iins Remove Iin {low}{(low == high ? string.Empty : $" to {high}")}");
                string result = Web.SetLoyaltyIinsRemoveIin(name, low, high);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in IIN");
            }
        }

        /// <summary>
        /// Add a Tariff Mapping to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Product Code and Loyalty Code of the mapping to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyMappingsAddMapping(LoyaltyMappingDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string productCode = details.ProductCode?.Trim() ?? string.Empty;
            string loyaltyCode = details.LoyaltyCode?.Trim() ?? string.Empty;
            if (productCode.All(char.IsLetterOrDigit) && loyaltyCode.All(char.IsLetterOrDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Mappings Add Mapping {productCode} => {loyaltyCode}");
                string result = Web.SetLoyaltyMappingsAddMapping(name, productCode, loyaltyCode);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Loyalty Mapping");
            }
        }

        /// <summary>
        /// Remove a Tariff Mapping from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="details">Product Code and Loyalty Code of the mapping to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetLoyaltyMappingsRemoveMapping(LoyaltyMappingDetails details)
        {
            string name = details.Name?.Trim() ?? string.Empty;
            string productCode = details.ProductCode?.Trim() ?? string.Empty;
            string loyaltyCode = details.LoyaltyCode?.Trim() ?? string.Empty;
            if (productCode.All(char.IsLetterOrDigit) && loyaltyCode.All(char.IsLetterOrDigit))
            {
                Web.SendChatMessage(WebName, $"Set {name} Loyalty Mappings Remove Mapping {productCode} => {loyaltyCode}");
                string result = Web.SetLoyaltyMappingsRemoveMapping(name, productCode, loyaltyCode);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Loyalty Mapping");
            }
        }

        #endregion
    }
}