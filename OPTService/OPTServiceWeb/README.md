# OPTWebAdmin

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 11.0.6.

## Prerequisites

From an Admin Command Prompt

- Install node v14 - use 'choco install nodejs --version 14.21.3'
- npm install -g @angular/cli@11.0.6
- 
- Re-start Visual Studio and Admin Cmd 
- Change to ..\OPTService\OPTServiceWeb
- run `npm install` to install project dependencies.
- Rebuild OPTServiceWeb.csproj

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

You will also need to add a .vscode\launch.json file, with this content:-
```json
{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "pwa-chrome",
            "request": "launch",
            "name": "Launch Chrome against localhost",
            "url": "http://localhost:4200",
            "webRoot": "${workspaceFolder}"
        }
    ]
}
```

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

- Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).
- Run `ng test --code-coverage` to execute the unit tests via [Karma](https://karma-runner.github.io) and run covarge analysis via [istanbul](https://istanbul.js.org/). Coverage results are generated in ./coverage/
- Run `ng test --include="RELATIVE_PATH"` to execute the unit tests found in `RELATIVE_PATH` via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
