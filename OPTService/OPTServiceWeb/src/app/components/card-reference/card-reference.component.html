<form [formGroup]="cardForm">
  <app-label-text-button [id]="id + 'Reference'" labelColClass="col-12 col-md-2 col-xl-2"
                         textColClass="col-12 col-md-6 col-xl-3" labelText="Reference" controlName="Reference"
                         [errorInAction]="referenceError" (action)="updateCardReference($event)" [formGroup]="cardForm" maxLength="10">
  </app-label-text-button>
  <app-label-text-button-button [id]="id + 'Acquirer'" labelColClass="col-12 col-md-2 col-xl-2"
                                textColClass="col-12 col-md-6 col-xl-4" labelText="Acquirer" controlName="Acquirer"
                                [errorInAction]="acquirerError" (primaryAction)="updateAcquirerReference($event)"
                                (secondaryAction)="removeAcquirerReference($event)" [formGroup]="cardForm"></app-label-text-button-button>
  <app-label-text-button-button [id]="id + 'External'" labelColClass="col-12 col-md-2 col-xl-2"
                                textColClass="col-12 col-md-6 col-xl-5" labelText="External" controlName="External"
                                [errorInAction]="externalError" (primaryAction)="setExternalName($event)"
                                (secondaryAction)="removeExternalName($event)" [formGroup]="cardForm"></app-label-text-button-button>
  <div class="row form-group">
    <div class="col-auto">
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" id="cardTypePayment" [value]=false name="FuelCard"
               formControlName="FuelCard" (change)="setFuelCard($event)">
        <label class="form-check-label" for="cardTypePayment">Payment</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" id="cardTypeFuel" [value]=true name="FuelCard"
               formControlName="FuelCard" (change)="setFuelCard($event)">
        <label class="form-check-label" for="cardTypeFuel">Fuel</label>
      </div>
    </div>
    <span [id]="id + 'CardTypeError'" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated" *ngIf="cardTypeError">
      <i class="bi bi-exclamation-triangle"></i>
    </span>
  </div>
  <div class="row form-group d-flex justify-content-center">
    <button type="button" [id]="id + 'RemoveCardBtn'" class="btn btn-danger btn-labeled" (click)="removeCardReference($event)">
      <span class="btn-label"><i class="bi bi-trash"></i></span>Remove card
    </button>
    <span [id]="id + 'RemoveCardError'" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated" *ngIf="removeError">
      <i class="bi bi-exclamation-triangle"></i>
    </span>
  </div>
</form>
