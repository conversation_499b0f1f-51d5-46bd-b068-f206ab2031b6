using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Logger.Interfaces.Tracing;

namespace OPT.Common.ConnectionThreads
{
    [HasConfiguration]
    public class ThirdPartyPosConnectionThread : PlainTextListenerConnectionThread
    {
        public ThirdPartyPosConnectionThread(IHtecLogger logger, ISocketWrapperFactory socketWrapperFactory, IConfigurationManager configurationManager,
            MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.None, bool logRxTx = true) : base(logger, "ThirdPartyPOS", socketWrapperFactory,
            configurationManager, messageLengthFlow, logRxTx)
        {
        }
    }
}
