using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Models;
using Htec.Logger.Interfaces.Tracing;
using System.Runtime.CompilerServices;

namespace OPT.TransactionValidator.Interfaces
{
    /// <summary>
    /// Interface for validating transaction states.
    /// </summary>
    public interface IPumpTransactionValidator
    {
        /// <summary>
        /// Validate the specified transaction against rules.
        /// </summary>
        /// <param name="transaction">Transaction to validate.</param>
        /// <param name="reference">Logging reference</param>
        /// <returns>Result of validation.</returns>
        Result ValidateTransaction(PumpTransaction transaction, string reference = null);

        IHtecLogger GetLogger([CallerMemberName] string methodName = null);
    }
}
