import { HttpClientModule } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { AppRoutingModule } from 'src/app/app-routing.module';

import { LocalAccountCardComponent } from './local-account-card.component';

describe('LocalAccountCardComponent', () => {
  let localAccountCardComponent: LocalAccountCardComponent;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        HttpClientModule
      ],
      providers: [
        LocalAccountCardComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
      ]
    });

    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    localAccountCardComponent = TestBed.inject(LocalAccountCardComponent);
  });

  it('should create', () => {
    expect(localAccountCardComponent).toBeTruthy();
  });

});

