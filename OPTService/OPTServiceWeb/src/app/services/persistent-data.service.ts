import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { PumpService } from './pump.service';
import { SignalRService } from './signal-r.service';

@Injectable({
  providedIn: 'root'
})
export class PersistentDataService {

  private readonly className = PersistentDataService.name;
  private _tids: any;
  private _maxFillOverride: any;
  private signalRData: Subscription | undefined;

  constructor(
    private logger: NGXLogger,
    private pumpService: PumpService,
    private signalRService: SignalRService) {
    const logPreffix = this.className + '.' + this.constructor.name + ' - ';

    // Subscription to refresh data when all OPT changes
    this.signalRData = this.signalRService.getOptSignalRMessage().subscribe((optStringId) => {
      if (!optStringId){
        this.logger.debug(logPreffix + 'OPT signalR event received. Refreshing persistent data', optStringId);
        this.refreshTids();
        this.refreshMaxFillOverride();
      }      
    });

    // Subscription to refresh data when all Generic OPT Config changes
    this.signalRData = this.signalRService.getGenericOptConfigSignalRMessage().subscribe(() => {
      this.logger.debug(logPreffix + 'GenericOptConfig signalR event received. Refreshing persistent data');
      this.refreshTids();
      this.refreshMaxFillOverride();
    });

    // Initial refresh on first load.
    this.logger.debug(logPreffix + 'Refreshing persistent data on first load.');
    this.refreshTids();
    this.refreshMaxFillOverride();
  }

  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Get the TID list
   * @returns The TID list
   */
  public getTids() {
    return this._tids;
  }

  /**
   * Get the Max Fill Override setting
   * @returns The Max Fill Override setting
   */
  public getMaxFillOverride() {
    return this._maxFillOverride;
  }

  /**
   * Resfreshes the tid list via API call.
   */
  private refreshTids() {
    const logPreffix = this.className + '.' + this.refreshTids.name + ' - ';
    this.pumpService.getTids().subscribe(data => {
      this.logger.debug(logPreffix + 'All TIDs data', data);
      this._tids = data;
    }, error => {
      this.logger.error(logPreffix + 'Error getting TIDs: ', error)
    });
  }

  /**
   * Resfreshes the MaxFillOverride information via API call.
   */
  private refreshMaxFillOverride() {
    const logPreffix = this.className + '.' + this.refreshMaxFillOverride.name + ' - ';
    this.pumpService.getMaxFillOverride().subscribe(data => {
      this.logger.debug(logPreffix + 'MaxFillOverride data', data);
      this._maxFillOverride = data;
    }, error => {
      this.logger.error(logPreffix + 'Error getting MaxFillOverride: ', error)
    });
  }
}
