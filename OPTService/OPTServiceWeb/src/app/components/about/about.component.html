<ng-container *ngIf="showContent">
    <form [formGroup]="aboutForm">

        <div class="card form-group">
    
            <div class="card-header d-flex justify-content-between">
                <label class="col-form-label">OPT service</label>
                <div id="optServiceButtons">
                    <button class="btn btn-primary" (click)="refreshOPT()">Refresh all OPT</button>
                    <span>&nbsp;</span>
                    <button class="btn btn-primary" (click)="requestAllOptLogs()">Request all OPT Logs</button>
                    <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.OPTService" *ngIf="errors.OPTService"><i class="bi bi-exclamation-triangle"></i></span>
                </div>
            </div>
    
        </div>
    
        <div class="card form-group">    
            <div class="card-header d-flex justify-content-between">
                <button type="button" (click)="versionInfoCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Version information</span>
                    <div id="refreshVersion">
                        <button class="btn btn-primary" (click)="setReleaseVersion()" *ngIf="fullVersion">Release version</button>
                        <button class="btn btn-primary" (click)="setFullVersion()" *ngIf="fullVersion === false">Full version</button>
                        <button class="btn btn-primary ml-2 mr-4" (click)="refreshData()">Refresh versions</button>
                        <span class="text-danger ml-2 mr-4 pt-1" placement="right top" container="body" [ngbTooltip]="errors.Refresh" *ngIf="errors.Refresh"><i class="bi bi-exclamation-triangle"></i></span>
                        <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':versionInfoIsCollapsed,'bi-chevron-up':versionInfoIsCollapsed===false }"></i>
                    </div>
                </button>
            </div>    
            <div #versionInfoCollapse="ngbCollapse" [(ngbCollapse)]="versionInfoIsCollapsed" class="card-body">
              <div class="row">
                <span class="col-2 font-weight-bold">Current</span><span class="col-2 font-weight-bold">Version</span><span class="col-1 font-weight-bold">Checksum</span><span class="col-auto font-weight-bold">Date</span>
              </div>
              <div *ngFor="let currentInfo of versionInfoData?.Current;">
                <div class="row">
                  <span class="col-2">{{currentInfo.Name}}</span>
                  <span class="col-2">{{currentInfo.Version}}</span>
                  <span class="col-1">{{currentInfo.Checksum}}</span>
                  <span class="col-auto">{{currentInfo.DateModified | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </div>
              </div>
              <div class="row">
                <span class="col-2 font-weight-bold">Pump Integrator</span><span class="col-2 font-weight-bold">Version</span><span class="col-1 font-weight-bold">Checksum</span><span class="col-auto font-weight-bold">Date</span>
              </div>
              <div *ngFor="let info of versionInfoData?.PumpIntegrator;">
                <div class="row">
                  <span class="col-2">{{info.Name}}</span>
                  <span class="col-2">{{info.Version}}</span>
                  <span class="col-1">{{info.Checksum}}</span>
                  <span class="col-auto">{{info.DateModified | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </div>
              </div>
              <div class="row">
                <span class="col-2 font-weight-bold">Upgrade</span><span class="col-2 font-weight-bold">Version</span><span class="col-1 font-weight-bold">Checksum</span><span class="col-auto font-weight-bold">Date</span>
              </div>
              <div class="row" *ngIf="versionInfoData?.Upgrade.length === 0">
                <span class="col-2">n/a</span>
              </div>
              <div *ngFor="let currentInfo of versionInfoData?.Upgrade;">
                <div class="row">
                  <span class="col-2">{{currentInfo.Name}}</span>
                  <span class="col-2">{{currentInfo.Version}}</span>
                  <span class="col-1">{{currentInfo.Checksum}}</span>
                  <span class="col-auto">{{currentInfo.DateModified | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </div>
              </div>
              <div class="row">
                <span class="col-2 font-weight-bold">Rollback</span><span class="col-2 font-weight-bold">Version</span><span class="col-1 font-weight-bold">Checksum</span><span class="col-auto font-weight-bold">Date</span>
              </div>
              <div class="row" *ngIf="versionInfoData?.Rollback.length === 0">
                <span class="col-2">n/a</span>
              </div>
              <div *ngFor="let currentInfo of versionInfoData?.Rollback;">
                <div class="row">
                  <span class="col-2">{{currentInfo.Name}}</span>
                  <span class="col-2">{{currentInfo.Version}}</span>
                  <span class="col-1">{{currentInfo.Checksum}}</span>
                  <span class="col-auto">{{currentInfo.DateModified | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </div>
              </div>
              <div class="row">
                <span class="col-2 font-weight-bold">Offline Transaction File Service</span><span class="col-2 font-weight-bold">Version</span><span class="col-1 font-weight-bold">Checksum</span><span class="col-auto font-weight-bold">Date</span>
              </div>
              <div class="row" *ngIf="versionInfoData?.OfflineFileService.length === 0">
                <span class="col-2">n/a</span>
              </div>
              <div *ngFor="let info of versionInfoData?.OfflineFileService;">
                <div class="row">
                  <span class="col-2">{{info.Name}}</span>
                  <span class="col-2">{{info.Version}}</span>
                  <span class="col-1">{{info.Checksum}}</span>
                  <span class="col-auto">{{info.DateModified | date: 'dd/MM/yyyy HH:mm:ss'}}</span>
                </div>
              </div>
            </div>
            </div>       
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="whitelistFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Whitelist files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':whitelistFilesIsCollapsed,'bi-chevron-up':whitelistFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #whitelistFilesCollapse="ngbCollapse" [(ngbCollapse)]="whitelistFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.WhitelistFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.WhitelistFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeWhitelistFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveWhitelistFile.get(fileName)" *ngIf="errors.RemoveWhitelistFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="layoutFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Layout files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':layoutFilesIsCollapsed,'bi-chevron-up':layoutFilesIsCollapsed===false }"></i>
                </button>                
            </div>
            <div #layoutFilesCollapse="ngbCollapse" [(ngbCollapse)]="layoutFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.LayoutFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.LayoutFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeLayoutFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveLayoutFile.get(fileName)" *ngIf="errors.RemoveLayoutFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="upgradeFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Upgrade files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':upgradeFilesIsCollapsed,'bi-chevron-up':upgradeFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #upgradeFilesCollapse="ngbCollapse" [(ngbCollapse)]="upgradeFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.UpgradeFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.UpgradeFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeUpgradeFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveUpgradeFile.get(fileName)" *ngIf="errors.RemoveUpgradeFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="softwareFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Software files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':softwareFilesIsCollapsed,'bi-chevron-up':softwareFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #softwareFilesCollapse="ngbCollapse" [(ngbCollapse)]="softwareFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.SoftwareFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.SoftwareFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeSoftwareFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveSoftwareFile.get(fileName)" *ngIf="errors.RemoveSoftwareFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="mediaFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Media files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':mediaFilesIsCollapsed,'bi-chevron-up':mediaFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #mediaFilesCollapse="ngbCollapse" [(ngbCollapse)]="mediaFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.MediaFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.MediaFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeMediaFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveMediaFile.get(fileName)" *ngIf="errors.RemoveMediaFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="playlistFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Playlist files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':playlistFilesIsCollapsed,'bi-chevron-up':playlistFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #playlistFilesCollapse="ngbCollapse" [(ngbCollapse)]="playlistFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.PlaylistFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.PlaylistFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removePlaylistFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemovePlaylistFile.get(fileName)" *ngIf="errors.RemovePlaylistFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="card form-group">
            <div class="card-header d-flex justify-content-between">
                <button type="button" (click)="databaseBackupFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Database backup files</span>
                    <div id="backupDatabase">
                        <button class="btn btn-primary mr-4" (click)="backupDatabase()">Backup database</button>
                        <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':databaseBackupFilesIsCollapsed,'bi-chevron-up':databaseBackupFilesIsCollapsed===false }"></i>
                    </div>
                </button>                
            </div>
            <div #databaseBackupFilesCollapse="ngbCollapse" [(ngbCollapse)]="databaseBackupFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.DatabaseBackupFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.DatabaseBackupFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="restoreDatabase(fileName)">Restore</button>
                        <button class="btn btn-primary ml-2" (click)="removeDatabaseBackupFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.Database.get(fileName)" *ngIf="errors.Database.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="card form-group">
            <div class="card-header">
                <button type="button" (click)="optLogFilesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>OPT log files</span>
                    <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':optLogFilesIsCollapsed,'bi-chevron-up':optLogFilesIsCollapsed===false }"></i>
                </button>
            </div>
            <div #optLogFilesCollapse="ngbCollapse" [(ngbCollapse)]="optLogFilesIsCollapsed" class="card-body">
                <div class="form-group" *ngIf="versionInfoData?.OptLogFiles.length === 0 && versionInfoData?.DisplayOptLogFiles.length === 0">
                    No files
                </div>
                <div *ngFor="let fileName of versionInfoData?.DisplayOptLogFiles;">
                    <div class="form-group">
                        <label class="col-form-label col-7 pl-0">{{fileName}}</label>
                        <button class="btn btn-primary" (click)="removeOptLogFile(fileName)">Remove</button>
                        <span class="text-danger ml-2 pt-1" placement="right top" container="body" [ngbTooltip]="errors.RemoveOptLogFile.get(fileName)" *ngIf="errors.RemoveOptLogFile.has(fileName)"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
                <div class="text-center">
                    <button class="btn btn-success text-center" (click)="showMore()" *ngIf="versionInfoData?.OptLogFiles.length > 0">Show more</button>
                </div>
            </div>
        </div>
    
    </form>
</ng-container>

