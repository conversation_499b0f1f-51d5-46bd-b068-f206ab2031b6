using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add ENDPOINTS Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20050012, "ADO#657914 - add BOS Configuraion Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO657914_AddConfigCategoryBos : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameBOS}', @description = 'All BOS Api related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameBOS}'");
        }
    }
}
