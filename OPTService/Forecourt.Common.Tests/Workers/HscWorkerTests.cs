using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Workers;
using Forecourt.Pump.Workers.Interfaces;
using HSC;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.HydraDb.Models;
using OPT.Common.Repositories.Interfaces;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO.Abstractions;
using Xunit;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using CorePumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using HscDispenser = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Dispenser6;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using HscTransaction = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Transaction;
using HscVehicleRegistrationData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.VehicleRegistrationData;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Tests.Workers
{
    public class HscWorkerTests : IDisposable
    {
        private readonly ISiteController _siteController;
        private readonly IFromOptWorker _optWorker;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IPumpWorker _hscWorker;
        private readonly IDbExecutor _dbExecutor;
        private readonly IHydraDb _hydraDb;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IJournalWorker _journalWorker;
        private readonly IConfigurationManager _configurationManager;
        private readonly ICacheHelper _cacheHelper;
        private readonly IPump _pump;
        private readonly ITimerFactory _timerFactory;
        private readonly IMessageTracking _messageTracking;
        private readonly IPumpCollection _pumpCollection;
        private readonly IFileVersionInfoHelper _fileVersionInfoHelper;
        private readonly ITankGaugeWorker _tankGaugeWorker;
        private readonly IPumpControllerFactory _pumpControllerFactory;
        private readonly IPumpController _pumpController;
        private readonly IFileSystem _fileSystem;

        public HscWorkerTests()
        {
            _siteController = Substitute.For<ISiteController, ISiteControllerEvents>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _dbExecutor = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(_dbExecutor);
            _logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _logManager.GetLogger(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<bool>(), Arg.Any<ILogFormatter>()).Returns(_logger);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _journalWorker = Substitute.For<IJournalWorker>();
            _cacheHelper = Substitute.For<ICacheHelper>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _pump = Substitute.For<IPump>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _fileVersionInfoHelper = Substitute.For<IFileVersionInfoHelper>();
            _tankGaugeWorker = Substitute.For<ITankGaugeWorker>();

            _journalWorker.TillNumber.Returns((short) 99);
            var configurationRepository = Substitute.For<IConfigurationRepository>();
            _hydraDb = new Forecourt.Common.HydraDbClasses.HydraDb(_dbExecutorFactory, _logger, _telemetryWorker, _configurationManager, configurationRepository, _cacheHelper);
            _dbExecutor.Query<FuellingInfo>("GetFuelling", commandType: CommandType.StoredProcedure)
                .Returns(new List<FuellingInfo> {new FuellingInfo(true, 0, 0, 0, 0, 0)});

            _messageTracking = Substitute.For<IMessageTracking>();
            _pumpCollection = Substitute.For<IPumpCollection>();
            _pumpControllerFactory = Substitute.For<IPumpControllerFactory>();
            _fileSystem = Substitute.For<IFileSystem>();
            
            _hscWorker = new PumpWorker(_pumpControllerFactory, _journalWorker, _optWorker, _hydraDb, _logManager, _configurationManager, _timerFactory, _pumpCollection, Substitute.For<IGradeHelper>(), _fileVersionInfoHelper);

            _pumpController = new HydraPumpController(_logManager, _configurationManager, _hydraDb, _siteController, _fileSystem, _timerFactory, _fileVersionInfoHelper);

        }

        #region Start tests

        [Fact]
        public void test_site_controller_starts_with_default_settings()
        {
            // Arrange

            // Act
            _pumpController.Start();

            // Assert
            _siteController.Received(1).Start("127.0.0.1", 1259, _pumpController as ISiteControllerEvents);
        }

        [Fact]
        public void test_site_controller_starts_with_database_settings()
        {
            // Arrange
            var pumpEndPoint = new PumpEndPoint("***************", 12345);
            var db = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(db);
            db.Query<connGenericEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { pumpEndPoint });

            // Act
            _pumpController.Start();

            // Assert
            _siteController.Received(1).Start(pumpEndPoint.IpAddress, pumpEndPoint.Port, _pumpController as ISiteControllerEvents);
        }

        [Fact]
        public void test_site_controller_uses_the_default_settings_if_more_than_1_pump_end_point_exists()
        {
            // Arrange
            var pumpEndPoint = new PumpEndPoint("***************", 12345);
            var db = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(db);
            db.Query<connGenericEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { pumpEndPoint, pumpEndPoint });

            // Act
            _pumpController.Start();

            // Assert
            _siteController.Received(1).Start("127.0.0.1", 1259, _pumpController as ISiteControllerEvents);
        }

        #endregion

        #region Message Tests

        [Fact]
        public void test_receive_max_pumps()
        {
            // Arrange
            const byte numberOfPumps = 3;
            const byte numberOfHoses = 4;
            const string fdcVersion = "FDC Version";
            const string fdcChecksum = "FDC Checksum";
            IList<int> pumps = new List<int>();

            // Mock up what HSC.dll is now doing!!
            var siteControllerEvents = _siteController as ISiteControllerEvents;
            _siteController.When(x => x.RequestMaxPumps()).Do(x=> siteControllerEvents.OnMaxPumpsResponse(numberOfPumps, numberOfHoses, fdcVersion, fdcChecksum));
            siteControllerEvents.When(x => x.OnMaxPumpsResponse(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string>()))
                .Do(x =>
                {
                    for (int i = 1; i <= (byte.TryParse($"{x[0]}", out var b) ? b : (byte)0); i++)
                    {
                        _siteController.RequestPumpData(i);
                    }
                });
            _siteController
                .When(x => x.RequestPumpData(Arg.Any<int>()))
                .Do(x => siteControllerEvents.OnPumpData(new HscPumpData { PumpNumber = byte.TryParse($"{x[0]}", out var b) ? b : (byte)0 }));
            siteControllerEvents.OnPumpData(Arg.Do<HscPumpData>(x => pumps.Add(x.PumpNumber)));

            // Act
            _siteController.RequestMaxPumps();

            // Assert
            pumps.Should().ContainInOrder(1, 2, 3);
            _siteController.Received(3).RequestPumpData(Arg.Any<int>());
        }

        // TODO: Sort out when DOMs work splits out pump interface
        [Fact(Skip = "Method under test calls Task.Run")]
        public void test_receive_pump_data_in_request_state()
        {
            // Arrange
            const byte pump = 3;
            const byte number = 4;
            const byte pumpMake = 5;
            const byte pumpModel = 5;
            const byte pumpHoses = 4;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte tank4 = 4;
            const byte grade1 = 1;
            const byte grade2 = 2;
            const byte grade3 = 3;
            const byte grade4 = 4;
            const ushort price1 = 1200;
            const ushort price2 = 1300;
            const ushort price3 = 1400;
            const ushort price4 = 1500;
            const bool optAvailable = true;
            const bool optInControl = false;
            const PumpState state = PumpState.Request;
            const CommunicationState commErr = CommunicationState.Ok;
            const byte pos = 1;
            const byte currentHose = 1;
            // ReSharper disable once InconsistentNaming
            const ushort currentPPU = 1200;
            const uint currentVolume = 1000;
            const uint currentCash = 2000;
            const bool transaction1Paid = true;
            const byte transaction1Hose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort transaction1PPU = 1200;
            const uint transaction1Volume = 2000;
            const uint transaction1Cash = 3000;
            const uint transaction1PrePaid = 4000;
            const bool transaction2Paid = false;
            const byte transaction2Hose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort transaction2PPU = 1300;
            const uint transaction2Volume = 4000;
            const uint transaction2Cash = 5000;
            const uint transaction2PrePaid = 6000;
            const PrePayState prePayState = PrePayState.Active;
            const uint prePayment = 100;
            const bool vehicleRegistrationBlocked = false;
            const string vehicleRegistrationCurrent = "GEN 11";
            const string vehicleRegistrationTransaction1 = "772 YUJ";
            const string vehicleRegistrationTransaction2 = "TGK 681 M";
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    Number = number,
                    PumpMake = pumpMake,
                    PumpModel = pumpModel,
                    PumpHoses = pumpHoses,
                    Tank1 = tank1,
                    Tank2 = tank2,
                    Tank3 = tank3,
                    Tank4 = tank4,
                    Grade1 = grade1,
                    Grade2 = grade2,
                    Grade3 = grade3,
                    Grade4 = grade4,
                    Price1 = price1,
                    Price2 = price2,
                    Price3 = price3,
                    Price4 = price4,
                    OptAvailable = optAvailable,
                    OptInControl = optInControl,
                    State = (byte) state,
                    CommErr = (byte) commErr,
                    Pos = pos,
                    CurrentHose = currentHose,
                    CurrentPpu = currentPPU,
                    CurrentVolume = currentVolume,
                    CurrentCash = currentCash,
                    Transaction1 = new HscTransaction
                    {
                        Paid = transaction1Paid,
                        Hose = transaction1Hose,
                        Ppu = transaction1PPU,
                        Volume = transaction1Volume,
                        Cash = transaction1Cash,
                        PrePaid = transaction1PrePaid
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = transaction2Paid,
                        Hose = transaction2Hose,
                        Ppu = transaction2PPU,
                        Volume = transaction2Volume,
                        Cash = transaction2Cash,
                        PrePaid = transaction2PrePaid
                    },
                    PrePayState = (byte) prePayState,
                    PrePayment = prePayment
                },
                RegistrationData = new HscVehicleRegistrationData
                {
                    Blocked = vehicleRegistrationBlocked,
                    Current = vehicleRegistrationCurrent,
                    TransactionRegistration1 = vehicleRegistrationTransaction1,
                    TransactionRegistration2 = vehicleRegistrationTransaction2
                }
            };
            // Act
            _hscWorker.RegisterWorker(_optWorker);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            // ReSharper disable once RedundantLogicalConditionalExpressionOperand
            _optWorker.Received(1).OnPumpState(_messageTracking, state, pump, currentHose, grade1, currentVolume, currentCash, price1,
                transaction1Paid && transaction2Paid, Arg.Any<IList<byte>>());
        }

        // TODO: Sort out when DOMs work splits out pump interface
        [Fact(Skip = "Method under test calls Task.Run")]
        public void test_receive_pump_data_in_idle_state()
        {
            // Arrange
            const byte pump = 3;
            const byte number = 4;
            const byte pumpMake = 5;
            const byte pumpModel = 5;
            const byte pumpHoses = 4;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte tank4 = 4;
            const byte grade1 = 1;
            const byte grade2 = 2;
            const byte grade3 = 3;
            const byte grade4 = 4;
            const ushort price1 = 1200;
            const ushort price2 = 1300;
            const ushort price3 = 1400;
            const ushort price4 = 1500;
            const bool optAvailable = true;
            const bool optInControl = false;
            const PumpState state = PumpState.Idle;
            const CommunicationState commErr = CommunicationState.Ok;
            const byte pos = 1;
            const byte currentHose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort currentPPU = 1200;
            const uint currentVolume = 1000;
            const uint currentCash = 2000;
            const bool transaction1Paid = true;
            const byte transaction1Hose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort transaction1PPU = 1200;
            const uint transaction1Volume = 2000;
            const uint transaction1Cash = 3000;
            const uint transaction1PrePaid = 4000;
            const bool transaction2Paid = false;
            const byte transaction2Hose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort transaction2PPU = 1300;
            const uint transaction2Volume = 4000;
            const uint transaction2Cash = 5000;
            const uint transaction2PrePaid = 6000;
            const PrePayState prePayState = PrePayState.Active;
            const uint prePayment = 100;
            const bool vehicleRegistrationBlocked = false;
            const string vehicleRegistrationCurrent = "GEN 11";
            const string vehicleRegistrationTransaction1 = "772 YUJ";
            const string vehicleRegistrationTransaction2 = "TGK 681 M";
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    Number = number,
                    PumpMake = pumpMake,
                    PumpModel = pumpModel,
                    PumpHoses = pumpHoses,
                    Tank1 = tank1,
                    Tank2 = tank2,
                    Tank3 = tank3,
                    Tank4 = tank4,
                    Grade1 = grade1,
                    Grade2 = grade2,
                    Grade3 = grade3,
                    Grade4 = grade4,
                    Price1 = price1,
                    Price2 = price2,
                    Price3 = price3,
                    Price4 = price4,
                    OptAvailable = optAvailable,
                    OptInControl = optInControl,
                    State = (byte) state,
                    CommErr = (byte) commErr,
                    Pos = pos,
                    CurrentHose = currentHose,
                    CurrentPpu = currentPPU,
                    CurrentVolume = currentVolume,
                    CurrentCash = currentCash,
                    Transaction1 = new HscTransaction
                    {
                        Paid = transaction1Paid,
                        Hose = transaction1Hose,
                        Ppu = transaction1PPU,
                        Volume = transaction1Volume,
                        Cash = transaction1Cash,
                        PrePaid = transaction1PrePaid
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = transaction2Paid,
                        Hose = transaction2Hose,
                        Ppu = transaction2PPU,
                        Volume = transaction2Volume,
                        Cash = transaction2Cash,
                        PrePaid = transaction2PrePaid
                    },
                    PrePayState = (byte) prePayState,
                    PrePayment = prePayment
                },
                RegistrationData = new HscVehicleRegistrationData
                {
                    Blocked = vehicleRegistrationBlocked,
                    Current = vehicleRegistrationCurrent,
                    TransactionRegistration1 = vehicleRegistrationTransaction1,
                    TransactionRegistration2 = vehicleRegistrationTransaction2
                }
            };
            // Act
            _hscWorker.RegisterWorker(_optWorker);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            // ReSharper disable once RedundantLogicalConditionalExpressionOperand
            _optWorker.ReceivedWithAnyArgs(1)
                .OnPumpState(_messageTracking, state, 0, 0, 0, 0, 0, 0, transaction1Paid && transaction2Paid, Arg.Any<IList<byte>>());
        }

        // TODO: Sort out when DOMs work splits out pump interface
        [Fact(Skip="Method under test calls Task.Run")]
        public void test_receive_pump_data_in_delivering_state()
        {
            // Arrange
            const byte pump = 3;
            const byte number = 4;
            const byte pumpMake = 5;
            const byte pumpModel = 5;
            const byte pumpHoses = 4;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte tank4 = 4;
            const byte grade1 = 1;
            const byte grade2 = 2;
            const byte grade3 = 3;
            const byte grade4 = 4;
            const ushort price1 = 1200;
            const ushort price2 = 1300;
            const ushort price3 = 1400;
            const ushort price4 = 1500;
            const bool optAvailable = true;
            const bool optInControl = false;
            const PumpState state = PumpState.Delivering;
            const CommunicationState commErr = CommunicationState.Ok;
            const byte pos = 1;
            const byte currentHose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort currentPPU = price2;
            const uint currentVolume = 1000;
            const uint currentCash = 2000;
            const bool transaction1Paid = true;
            const byte transaction1Hose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort transaction1PPU = 1200;
            const uint transaction1Volume = 2000;
            const uint transaction1Cash = 3000;
            const uint transaction1PrePaid = 4000;
            const bool transaction2Paid = true;
            const byte transaction2Hose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort transaction2PPU = 1300;
            const uint transaction2Volume = 4000;
            const uint transaction2Cash = 5000;
            const uint transaction2PrePaid = 6000;
            const PrePayState prePayState = PrePayState.Active;
            const uint prePayment = 100;
            const bool vehicleRegistrationBlocked = false;
            const string vehicleRegistrationCurrent = "GEN 11";
            const string vehicleRegistrationTransaction1 = "772 YUJ";
            const string vehicleRegistrationTransaction2 = "TGK 681 M";
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    Number = number,
                    PumpMake = pumpMake,
                    PumpModel = pumpModel,
                    PumpHoses = pumpHoses,
                    Tank1 = tank1,
                    Tank2 = tank2,
                    Tank3 = tank3,
                    Tank4 = tank4,
                    Grade1 = grade1,
                    Grade2 = grade2,
                    Grade3 = grade3,
                    Grade4 = grade4,
                    Price1 = price1,
                    Price2 = price2,
                    Price3 = price3,
                    Price4 = price4,
                    OptAvailable = optAvailable,
                    OptInControl = optInControl,
                    State = (byte) state,
                    CommErr = (byte) commErr,
                    Pos = pos,
                    CurrentHose = currentHose,
                    CurrentPpu = currentPPU,
                    CurrentVolume = currentVolume,
                    CurrentCash = currentCash,
                    Transaction1 = new HscTransaction
                    {
                        Paid = transaction1Paid,
                        Hose = transaction1Hose,
                        Ppu = transaction1PPU,
                        Volume = transaction1Volume,
                        Cash = transaction1Cash,
                        PrePaid = transaction1PrePaid
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = transaction2Paid,
                        Hose = transaction2Hose,
                        Ppu = transaction2PPU,
                        Volume = transaction2Volume,
                        Cash = transaction2Cash,
                        PrePaid = transaction2PrePaid
                    },
                    PrePayState = (byte) prePayState,
                    PrePayment = prePayment
                },
                RegistrationData = new HscVehicleRegistrationData
                {
                    Blocked = vehicleRegistrationBlocked,
                    Current = vehicleRegistrationCurrent,
                    TransactionRegistration1 = vehicleRegistrationTransaction1,
                    TransactionRegistration2 = vehicleRegistrationTransaction2
                }
            };

            // Act
            _hscWorker.RegisterWorker(_optWorker);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            // ReSharper disable once RedundantLogicalConditionalExpressionOperand
            _optWorker.Received(1).OnPumpState(_messageTracking, state, pump, currentHose, grade2, currentVolume, currentCash, price2,
                transaction1Paid && transaction2Paid, Arg.Any<IList<byte>>());
        }

        // TODO: Sort out when DOMs work splits out pump interface
        [Fact(Skip = "Method under test calls Task.Run")]
        public void test_receive_pump_data_in_finished_state()
        {
            // Arrange
            const byte pump = 3;
            const byte number = 4;
            const byte pumpMake = 5;
            const byte pumpModel = 5;
            const byte pumpHoses = 4;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte tank4 = 4;
            const byte grade1 = 1;
            const byte grade2 = 2;
            const byte grade3 = 3;
            const byte grade4 = 4;
            const ushort price1 = 1200;
            const ushort price2 = 1300;
            const ushort price3 = 1400;
            const ushort price4 = 1500;
            const bool optAvailable = true;
            const bool optInControl = false;
            const PumpState state = PumpState.Finished;
            const CommunicationState commErr = CommunicationState.Ok;
            const byte pos = 1;
            const byte currentHose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort currentPPU = price3;
            const uint currentVolume = 1000;
            const uint currentCash = 2000;
            const bool transaction1Paid = true;
            const byte transaction1Hose = 2;
            // ReSharper disable once InconsistentNaming
            const ushort transaction1PPU = 1200;
            const uint transaction1Volume = 2000;
            const uint transaction1Cash = 3000;
            const uint transaction1PrePaid = 4000;
            const bool transaction2Paid = true;
            const byte transaction2Hose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort transaction2PPU = 1300;
            const uint transaction2Volume = 4000;
            const uint transaction2Cash = 5000;
            const uint transaction2PrePaid = 6000;
            const PrePayState prePayState = PrePayState.Active;
            const uint prePayment = 100;
            const bool vehicleRegistrationBlocked = false;
            const string vehicleRegistrationCurrent = "GEN 11";
            const string vehicleRegistrationTransaction1 = "772 YUJ";
            const string vehicleRegistrationTransaction2 = "TGK 681 M";
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    Number = number,
                    PumpMake = pumpMake,
                    PumpModel = pumpModel,
                    PumpHoses = pumpHoses,
                    Tank1 = tank1,
                    Tank2 = tank2,
                    Tank3 = tank3,
                    Tank4 = tank4,
                    Grade1 = grade1,
                    Grade2 = grade2,
                    Grade3 = grade3,
                    Grade4 = grade4,
                    Price1 = price1,
                    Price2 = price2,
                    Price3 = price3,
                    Price4 = price4,
                    OptAvailable = optAvailable,
                    OptInControl = optInControl,
                    State = (byte) state,
                    CommErr = (byte) commErr,
                    Pos = pos,
                    CurrentHose = currentHose,
                    CurrentPpu = currentPPU,
                    CurrentVolume = currentVolume,
                    CurrentCash = currentCash,
                    Transaction1 = new HscTransaction
                    {
                        Paid = transaction1Paid,
                        Hose = transaction1Hose,
                        Ppu = transaction1PPU,
                        Volume = transaction1Volume,
                        Cash = transaction1Cash,
                        PrePaid = transaction1PrePaid
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = transaction2Paid,
                        Hose = transaction2Hose,
                        Ppu = transaction2PPU,
                        Volume = transaction2Volume,
                        Cash = transaction2Cash,
                        PrePaid = transaction2PrePaid
                    },
                    PrePayState = (byte) prePayState,
                    PrePayment = prePayment
                },
                RegistrationData = new HscVehicleRegistrationData
                {
                    Blocked = vehicleRegistrationBlocked,
                    Current = vehicleRegistrationCurrent,
                    TransactionRegistration1 = vehicleRegistrationTransaction1,
                    TransactionRegistration2 = vehicleRegistrationTransaction2
                }
            };

            // Act
            _hscWorker.RegisterWorker(_optWorker);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            // ReSharper disable once RedundantLogicalConditionalExpressionOperand
            _optWorker.Received(1).OnPumpState(_messageTracking, state, pump, currentHose, grade3, currentVolume, currentCash, price3,
                transaction1Paid && transaction2Paid, Arg.Any<IList<byte>>());
        }

        #endregion

        

        #region IDisposable Support

        private bool _disposedValue;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    _hscWorker.Dispose();
                }

                _disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
        }

        ~HscWorkerTests()
        {
            Dispose(false);
        }

        #endregion
    }
}
