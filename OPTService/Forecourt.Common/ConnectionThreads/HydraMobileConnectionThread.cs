using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Logger.Interfaces;

namespace OPT.Common.ConnectionThreads
{
    /// <summary>
    /// HydraMobile specific Connection Thread
    /// </summary>
    [HasConfiguration]
    public class HydraMobileConnectionThread : XmlClientConnectionThread
    {
        /// <inheritdoc/>
        public HydraMobileConnectionThread(IHtecLogManager logMan, ISocketWrapperFactory socketWrapperFactory, IConfigurationManager configurationManager,
            MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.Both, bool logRxTx = true) : 
            base(logMan, "HydraMobile", socketWrapperFactory, configurationManager, sizeof(short), logRxTx, messageLengthFlow)
        {
            MessageAdapter.ReverseBytesOverride = ReverseBytesType.Length;
        }
    }
}
