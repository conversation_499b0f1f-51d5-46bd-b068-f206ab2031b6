<div class="row form-group" [formGroup]="form">
    <div class="input-group" [ngClass]="labelColClass">
        <label class="col-form-label text-truncate" [for]="id" [title]="labelText">{{labelText}}</label>
    </div>
    <div class="input-group" [ngClass]="dropdownColClass">
        <select [id]="id" [formControl]="control" [attr.aria-describedby]="fieldFeedbackId"
            class="custom-select" [ngClass]="{'is-invalid':control?.invalid && (control?.dirty || control?.touched)}"
        >
            <option value="" disabled hidden>Unknown</option>
            <option *ngFor="let option of options" [value]="option.value" [attr.selected]="control?.value == option.value ? true : null">{{option.key}}</option>
        </select>
        <div [id]="fieldFeedbackId" class="invalid-feedback">
            {{validationErrorText}}
        </div>
    </div>
    <div [ngClass]="buttonColClass">
        <button [id]="buttonId" type="button" class="btn btn-primary" (click)="doAction($event)" [disabled]="control?.invalid">{{buttonText}}</button>
        <span [id]="errorId" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="errorInActionText" *ngIf="errorInAction">
            <i class="bi bi-exclamation-triangle"></i>
        </span>
    </div>
</div>