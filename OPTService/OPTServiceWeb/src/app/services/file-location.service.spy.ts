import { TestBed } from '@angular/core/testing';
import { FileLocationService } from './file-location.service';

export function FILE_LOCATION_SERVICE_PROVIDER(): { provide: typeof FileLocationService, useValue: any } {
  return {
    provide: FileLocationService,
    useValue: jasmine.createSpyObj('FileLocationService', [
      'getFileLocations', 
      'setProperty',
    ]),
  };
}

export function FILE_LOCATION_SERVICE_SPY(): jasmine.SpyObj<FileLocationService> {
  return TestBed.inject(FileLocationService) as jasmine.SpyObj<FileLocationService>;
}