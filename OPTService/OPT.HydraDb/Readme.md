# OPT.HydraDb

This project is (now) used to manage the HydraDb Sql Server (localDb) installation and upgrades, using the DbUp concept of migrations, 
i.e. any upgrades are applied to a database by performing Up() logic, whereas rollbacks are affected by applying Down() logic.

It (currently) deals with the following installation scenarios, so the Sql within the OptInstaller solution is now obsolete.

* Upgrade to v2.3, from v2.2
* Upgrade to v2.3, from v1.4 
* Clean install/Create Db, v2.3 


### Build With

* [Htec.Common] (https://pdidev.visualstudio.com/DefaultCollection/HTEC-Shared-Libraries/_git/Htec.Common)
* [Htec.DapperWrapper] (https://pdidev.visualstudio.com/DefaultCollection/HTEC-Shared-Libraries/_git/HtecDapperWrapper)
* [FluentMigrator] (https://github.com/fluentmigrator/fluentmigrator)

### Usage

Any and all HydraDb changes needed (through versioned development) should be managed via a migration class, which will be run as part of the overall OptService start-up!

The class can either inherit the core FluentMigrator.Migration, or Migrationable depending on the nature of the change - there is an ever expanding array of examples within this project.

As the based class is FluentMigrator.Migration, there is an abundance of helper properties/methods that will facilate schema changes, rather than hand-crafting the sql.

```csharp

[Migration(number, description, tag]
public class CreateDbFile: Migration
{
    public override void Up()
    { ... }

    public override void Down()
    { ... }
}
   
```


### Migration Version Format
The id, as defined in the [Migration(id, description, tag)] must be unique across all migrations, and from versions >= v2.2 uses the following format:-


```csharp
long id = <major><minor:3><seq:4>

// Migrations for v2.3.x, etc
[Migration(20030001, "HOPT-1680 - Add dbo.OptMode.ReceiptFooter"), Tags(Constants.MigrationTypeSchema)]
[Migration(20030002, "HOPT-1680 - related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
[Migration(20030003, "HOPT-1770 - clear down obsolete config keys"), Tags(Constants.MigrationTypeData)]
  
```



#### MigrationHelper
Simple helper class that orchestrates the FluentMigrator migrations that are contained with this assembly, and that need applying.

The default direction of the migrations is Up, but this can be overriden in config to Down, if needed - see below.

Migrations are applied by Tag, in the following order: Schema, Code, Data

```csharp

public static Result<bool, Exception> RunMigrations(string connectionString, string tag, string logFileName, IFileSystem fileSystem = null, IDbExecutorFactory dbExecutorFactory = null)

```


#### Migrationable

This inherits the core FluentMigrator.Migration (and uses the same naming convention as Loggable), and adds common constants, 
and core functionality to Backup and Restore a set of DbObjects, so that additional Sql Rollback scripts are not needed.

It exposes a number of common protected properties too, and the ctor has optional parameters so migrations can as simple/complex
as they need to be.   Logging is available via MS ILogger, although longer term support for IHtecLogger should be possible.

```csharp

protected Migrationable(ILogger<Migrationable> logger = null, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null)

protected ILogger<Migrationable> Logger { get; }

protected IList<string> DbObjects { get; set; }

protected Result<bool, Exception> BackupDbObjects(string prefix, params string[] dbObjects)

protected Result<bool, Exception> RestoreDbObjects(string prefix, params string[] dbObjects)

```


### Config Overrides

```csharp
<appSettings >
  <add key="HydraDb:Client:Settings" value="MORRISONS | ASDA | INDIE"/>
  <add key="HydraDb:Migration:Type" value="Up | Down"/>
</appSettings>
```

The default values for the above are MORRISONS and Up.


### Sql Objects
All objects should be included as Embedded Resources, and under the relevant folder - Data, Schema, Stored Procedures, Funcions, etc.

Those objects <= OptService v2.2 are included in this project (by link, to ..\OPTServce\Sql Scripts), without change, and referenced by some form of migration.

Those objects >= OptService v2.3 need to be included in this project (directly, under Sql\\), any code changes (sp, f) should be made in the relevant named sql file, extracted from InstallHydraDb.sql where necessary.

