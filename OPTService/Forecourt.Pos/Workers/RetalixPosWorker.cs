using CSharpFunctionalExtensions;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.Net;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc />
    [HasConfiguration()]
    public class RetalixPosWorker : PosListenerWorker<string, IHydraDb, ITelemetryWorker>, IRetalixPosWorker
    {
        private readonly IBosIntegratorOutOfflineTransient _bosIn;

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();

            var endPoint = endPoints.RetalixPosBindEndPoint;

            return new GenericEndPoint(endPoint.Address, endPoint.Port);
        }
   
        /// <inheritdoc />
        public RetalixPosWorker(IHydraDb hydraDb, IHtecLogManager logMan, ITelemetryWorker telemetryWorker, IListenerConnectionThread<string> connectionThread,
            IConfigurationManager configurationManager, IHydraPosWorker hydraPosWorker, IBosIntegratorOutOfflineTransient bosIn) : base(logMan, "RetalixPOS", telemetryWorker, connectionThread, hydraDb, configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            PrimaryIpAddress = hydraDb.GetRetalixPosPrimaryIpAddress();

            RegisterWorker(hydraPosWorker ?? throw new ArgumentNullException(nameof(hydraPosWorker)));
            _bosIn = bosIn ?? throw new ArgumentNullException(nameof(bosIn));
        }

        /// <inheritdoc />
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message, int id)
        {
            var msg = "Text for Retalix POS not allowed";
            DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { msg });
            return Result.Failure<string>(msg);
        }

        /// <inheritdoc />
        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            _bosIn.MoveOfflineTransactionFiles(ipAddress, LoggingReference);
        }

        /// <inheritdoc />
        protected override Result DoStart(params object[] startParams)
        {
            GetWorker<IHydraPosWorker>().RegisterWorker(NotificationWorker);

            return base.DoStart(startParams);
        }

        /// <inheritdoc />
        protected override Result DoStop()
        {
            var result = base.DoStop();
            GetWorker<IHydraPosWorker>().DeregisterWorker(NotificationWorker);
            return result;
        }
    }
}
