using CSharpFunctionalExtensions;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Concurrent;
using System.Threading;

namespace Forecourt.Pump.Models
{
    /// <summary>
    /// General purpose manager for locked enties
    /// </summary>
    /// <typeparam name="TId">Type of Id</typeparam>
    /// <typeparam name="TData">Type of Data</typeparam>
    public class SemaphoreLockManager<TId, TData>: Disposable where TData: LockData, new()
    {
        private readonly ConcurrentDictionary<TId, TData> _dictionary = new();
        private readonly CancellationToken _cancellationToken;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">IHtecLogger instance</param>
        /// <param name="cancellationToken">Global CancellationToken</param>
        /// <exception cref="ArgumentNullException">Throw if CancellationToken is null</exception>
        public SemaphoreLockManager(IHtecLogger logger, CancellationToken cancellationToken): base(logger)
        {
            if (cancellationToken == null) 
            {
                throw new ArgumentNullException(nameof(cancellationToken)); 
            }

            _cancellationToken = cancellationToken;
        }

        /// <summary>
        /// Can the lock on Id be locked/entered
        /// </summary>
        /// <param name="id">Id to lock</param>
        /// <param name="autoLock">If can lock the Id, should Id be locked/entered automatically</param>
        /// <returns>Result wrapped {TData} instance, or Failure</returns>
        public Result<TData> CanLock(TId id, bool autoLock = true)
        {
            var data = GetLockData(id);

            var canLock = false;
            data.Semaphore.Wait(_cancellationToken);
            try
            {
                canLock = data.CanLock;
                if (canLock && autoLock)
                {
                    data.Lock();
                }
            }
            finally
            {
                data.Semaphore.Release();
            }

            return Result.SuccessIf(canLock, data, "Already Locked");
        }

        /// <summary>
        /// Gets the lock {TData} for the Id
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns>{TData} instance</returns>
        public TData GetLockData(TId id)
        {
            if (!_dictionary.TryGetValue(id, out var data))
            {
                data = new TData();
                _dictionary.AddOrUpdate(id, data, (key, oldValue) => data);
            }

            return data;
        }

        /// <summary>
        /// Unlock the Id
        /// </summary>
        /// <param name="id">Id</param>
        /// <returns>Result</returns>
        public Result Unlock(TId id)
        {
            var data = GetLockData(id);

            data.Semaphore.Wait(_cancellationToken);
            try
            {
                data.Unlock();
            }
            finally
            {
                data.Semaphore.Release();
            }

            return Result.SuccessIf(!data.IsLocked, "Still Locked");
        }

        protected override void DoDisposeDisposing()
        {
            foreach(var key in _dictionary.Keys)
            {
                var data = GetLockData(key);
                data.Semaphore.Dispose();
                data.Semaphore = null;
            }
            _dictionary.Clear();

            base.DoDisposeDisposing();
        }
    }

    public class LockData
    {
        private SemaphoreSlim _semaphore;

        public LockData(): this(1)
        { 
        }

        public LockData(int maxCount)
        {
            LockedAt = DateTime.MinValue;
            UnlockedAt = DateTime.MaxValue;
            _semaphore = new(1, maxCount);
        }

        public SemaphoreSlim Semaphore
        {
            get { return _semaphore; }
            protected internal set { _semaphore = value; }
        } 

        public virtual bool CanLock => LockedAt == DateTime.MinValue || UnlockedAt >= LockedAt;

        protected internal void Lock()
        {
            LockedAt = DateTime.UtcNow.AddMilliseconds(-1);
            IsLocked = true;
        }

        protected internal void Unlock() 
        {
            UnlockedAt = DateTime.UtcNow;
            IsLocked = false;
        }

        public bool IsLocked { get; private set; }

        public DateTime LockedAt { get; private set; }

        public DateTime UnlockedAt { get; private set; }
    }
}
