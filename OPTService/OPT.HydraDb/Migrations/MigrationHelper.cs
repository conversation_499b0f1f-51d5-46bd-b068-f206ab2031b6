using CSharpFunctionalExtensions;
using FluentMigrator.Runner;
using FluentMigrator.Runner.Initialization;
using FluentMigrator.Runner.Logging;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System;
using System.IO;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations
{
    /// <summary>
    /// Helper class to manage the HydraDb update process
    /// </summary>
    public static class MigrationHelper
    {
        /// <summary>
        /// Configure the dependency injection services
        /// </summary>
        private static IServiceProvider CreateServices(string connectionString, string tag, string logFileName, IFileSystem fileSystem = null, IDbExecutorFactory dbExecutorFactory = null)
        {
            var services = new ServiceCollection()
                .AddFluentMigratorCore()
                .ConfigureRunner(rb => rb
                    .AddSqlServer2014()
                    .WithGlobalConnectionString(connectionString)
                    .ScanIn(typeof(MigrationHelper).Assembly).For.Migrations().For.EmbeddedResources()
                )
                .Configure<FluentMigratorLoggerOptions>(opt =>
                {
                    opt.ShowSql = false;
                    opt.ShowElapsedTime = true;
                })
                .AddLogging(lb => lb.AddFluentMigratorConsole())
                .Configure<RunnerOptions>(opt => { opt.Tags = new[] { tag }; })
                .AddSingleton<TextWriter>(new StreamWriter(new FileStream(logFileName, FileMode.Append, FileAccess.Write, FileShare.ReadWrite)))
                .AddSingleton<ILoggerProvider, SqlScriptFluentMigratorLoggerProvider>()
                .Configure<SqlScriptFluentMigratorLoggerOptions>(opt =>
                {
                    opt.ShowSql = true;
                    opt.ShowElapsedTime = true;
                });

            if (dbExecutorFactory != null)
            {
                services.AddSingleton(dbExecutorFactory);
            }

            if (fileSystem != null)
            {
                services.AddSingleton(fileSystem);
            }

            return services.BuildServiceProvider(false);
        }

        /// <summary>
        /// Run any migrations for the given tag
        /// </summary>
        /// <param name="connectionString">Connection string to Db</param>
        /// <param name="tag">Migration Tag to filter on</param>
        /// <param name="logFileName">Log file name</param>
        /// <param name="migrateDown">Is the migration a downwards or rollback</param>
        /// <param name="fileSystem">IFileSystem instance, if applicable</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance, if applicable</param>
        /// <returns>Result{bool, Exception} instance</returns>
        public static Result<bool, Exception> RunMigrations(string connectionString, string tag, string logFileName, bool migrateDown = false, IFileSystem fileSystem = null, IDbExecutorFactory dbExecutorFactory = null)
        {
            IServiceProvider serviceProvider = null;
            try
            {
                serviceProvider = CreateServices(connectionString, tag, logFileName, fileSystem, dbExecutorFactory);

                // Make the Migrations atomic
                using (var scope = serviceProvider.CreateScope())
                {
                    var runner = scope.ServiceProvider.GetRequiredService<IMigrationRunner>();

                    if (migrateDown)
                    {
                        runner.MigrateDown(Constants.DefaultValueHydraDbMigrateDownToVersion);
                    }
                    else
                    {
                        runner.MigrateUp();
                    }
                }
            }
            catch (FluentMigrator.Exceptions.FluentMigratorException fmEx)
            {
                if (fmEx.Message.Contains("No migrations found"))
                {
                    return Result.Success<bool, Exception>(true);
                }

                var logger = (serviceProvider?.GetService(typeof(ILoggerProvider)) as ILoggerProvider)?.CreateLogger(typeof(Migrationable).FullName);
                logger?.LogError(fmEx, $"{nameof(RunMigrations)} failed!");

                return Result.Failure<bool, Exception>(fmEx);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool, Exception>(ex);
            }

            return Result.Success<bool, Exception>(true);
        }
    }
}
