using Newtonsoft.Json;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class CardDetail
    {
        [JsonProperty("acquirerID")]
        public string AcquirerId { get; set; }
        [JsonProperty("cardPan")]
        public string CardPan { get; set; }
        [JsonProperty("approvalMethodCode")]
        public string ApprovalMethodCode { get; set; }
        [JsonProperty("approvalCode")]
        public string ApprovalCode { get; set; }
        [JsonProperty("cardType")]
        public string CardType { get; set; }
        [JsonProperty("referenceNumber")]
        public string ReferenceNumber { get; set; }
        [JsonProperty("terminalId")]
        public string TerminalId { get; set; }
        [JsonProperty("approvalTime")]
        public string ApprovalTime { get; set; }
        [JsonProperty("stan")]
        public string Stan { get; set; }
        [JsonProperty("miscellaneousData")]
        public string MiscellaneousData { get; set; }
        [JsonProperty("receiptLines")]
        public List<string> ReceiptLines { get; set; }
        [JsonProperty("journalLines")]
        public List<string> JournalLines { get; set; }
        [JsonProperty("merchantId")]
        public string MerchantId { get; set; }
        [JsonProperty("terminalBatch")]
        public string TerminalBatch { get; set; }
        [JsonProperty("volumetricCard")]
        public bool VolumetricCard { get; set; }
        [JsonProperty("useDeposit")]
        public bool UseDeposit { get; set; }
        [JsonProperty("registration")]
        public string Registration { get; set; }
        [JsonProperty("reallocationFlag")]
        public bool ReallocationFlag { get; set; }
        [JsonProperty("customerID")]
        public string CustomerId { get; set; }
    }
}
