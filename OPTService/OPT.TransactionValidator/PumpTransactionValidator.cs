using CSharpFunctionalExtensions;
using Forecourt.Core.Payment.Enums;
using Forecourt.Core.Pump.Models;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using OPT.TransactionValidator.Extensions;
using OPT.TransactionValidator.Interfaces;
using OPT.TransactionValidator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

namespace OPT.TransactionValidator
{
    /// <summary>
    /// Validator for transaction state.
    /// </summary>
    public class PumpTransactionValidator : Loggable, IPumpTransactionValidator
    {
        private readonly ValidationConfiguration _validationConfiguration;

        private class TransactionMetrics
        {
            public DateTime LastStart { get; set; }
            public DateTime FirstComplete { get; set; }
            public DateTime AuthTime { get; set; }
            public uint TransactionVolume { get; set; }
        }
        
        /// <summary>
        /// Creates a new instance of the <see cref="PumpTransactionValidator"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="validationConfiguration">Threshold configuration for validation.</param>
        public PumpTransactionValidator(IHtecLogger logger, ValidationConfiguration validationConfiguration)
            : base(logger)
        {
            _validationConfiguration = validationConfiguration ?? throw new ArgumentNullException(nameof(validationConfiguration));
        }

        /// <inheritdoc cref="IPumpTransactionValidator.ValidateTransaction"/>
        public Result ValidateTransaction(PumpTransaction transaction, string reference = null)
        {
            return DoAction(() =>
            {
                if (transaction == null)
                {
                    return Result.Failure("No transaction");
                }
                
                var metricValidationResult = DoValidateTransaction(transaction);

                return metricValidationResult;
            }, transaction?.GetFullId(reference));
        }

        private Result DoValidateTransaction(PumpTransaction transaction)
        {
            Result metricValidationResult;
            try
            {
                var defaultState = IsDefaultState(transaction);
                if (defaultState.IsSuccess)
                {
                    DoDeferredLogging(LogLevel.Info, "DefaultState", () => new[] {AddResultToSummary(transaction, defaultState)}, methodName: nameof(ValidateTransaction));
                    return defaultState;
                }

                var zeroValueResult = CheckZeroValue(transaction);
                if (!zeroValueResult.IsSuccess)
                {
                    DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] {"Unable to evaluate transaction value"}, methodName: nameof(ValidateTransaction));
                    return zeroValueResult;
                }

                if (zeroValueResult.Value)
                {
                    DoDeferredLogging(LogLevel.Info, "ZeroValue", () => new[] {"Further validation not required"}, methodName: nameof(ValidateTransaction));
                    return zeroValueResult;
                }

                var metricValueResult = LoadMetricValues(transaction);

                if (!metricValueResult.IsSuccess)
                {
                    DoDeferredLogging(LogLevel.Warn, HeaderException,
                        () => new[] {$"Unable to extract metric value; Error: {metricValueResult.Error}", $"{AddResultToSummary(transaction, metricValueResult)}"},
                        methodName: nameof(ValidateTransaction));
                    return metricValueResult;
                }

                metricValidationResult = ValidateMetrics(metricValueResult.Value);

                if (!metricValidationResult.IsSuccess)
                {
                    DoDeferredLogging(LogLevel.Warn, "Failed", () => new[] {$"{AddResultToSummary(transaction, metricValidationResult)}"}, methodName: nameof(ValidateTransaction));
                    return metricValidationResult;
                }

                DoDeferredLogging(LogLevel.Info, "Passed", () => new[] {$"{AddResultToSummary(transaction, metricValidationResult)}"}, methodName: nameof(ValidateTransaction));
            }
            catch (Exception exception)
            {
                const string errorMessage = "Failed to validate transaction";
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {JsonConvert.SerializeObject(transaction)}, exception, methodName: nameof(ValidateTransaction));
                metricValidationResult = Result.Failure(errorMessage);
            }

            return metricValidationResult;
        }

        private static Result<bool> CheckZeroValue(PumpTransaction transaction)
        {
            if (!transaction.TransactionStates.Any())
            {
                return Result.Failure<bool>("Unable to evaluate transaction value");
            }

            var isZeroValue = transaction.TransactionStates.Values.All(x => x.Amount == 0 && x.Volume == 0);

            return Result.Success(isZeroValue);
        }

        private static Result IsDefaultState(PumpTransaction transaction)
        {
            return transaction.IsDefaultState()
                ? Result.Success()
                : Result.Failure("Transaction needs to be validated!");
        }

        private string AddResultToSummary(PumpTransaction transaction, Result result)
        {
            transaction.ValidationResult = result;
            return $"{JsonConvert.SerializeObject(transaction)} Configuration:{JsonConvert.SerializeObject(_validationConfiguration)}";
        }

        private Result ValidateMetrics(TransactionMetrics metrics)
        {
            var vendTime = (int) (metrics.FirstComplete - metrics.LastStart).TotalSeconds;
            if (vendTime < _validationConfiguration.VendTimeThreshold)
            {
                return Result.Failure($"Vend time of {vendTime} below threshold of {_validationConfiguration.VendTimeThreshold}");
            }

            var flowSpeed = CalculateFlowSpeed(vendTime, (int)metrics.TransactionVolume);
            if (flowSpeed > _validationConfiguration.FlowSpeedThreshold)
            {
                return Result.Failure($"Flow speed of {flowSpeed} above threshold of {_validationConfiguration.FlowSpeedThreshold}");
            }
            
            var completionTime = (metrics.FirstComplete - metrics.AuthTime).TotalSeconds;
            if (completionTime < _validationConfiguration.AuthTimeThreshold )
            {
                return Result.Failure($"Auth to vend complete time of {completionTime} below threshold of {_validationConfiguration.AuthTimeThreshold}");
            }

            return Result.Success();
        }

        private static Result<TransactionMetrics> LoadMetricValues(PumpTransaction transaction)
        {
            var lastStart = GetStart(transaction.TransactionStates);
            if (!lastStart.IsSuccess)
            {
                return Result.Failure<TransactionMetrics>(lastStart.Error);
            }

            var firstComplete = GetComplete(transaction.TransactionStates);
            if (!firstComplete.IsSuccess)
            {
                return Result.Failure<TransactionMetrics>(firstComplete.Error);
            }

            var authTime = GetAuthTime(transaction.PaymentStates);
            if (!authTime.IsSuccess)
            {
                return Result.Failure<TransactionMetrics>(authTime.Error);
            }
            
            return Result.Success(new TransactionMetrics
            {
                LastStart = lastStart.Value.FirstEntered,
                FirstComplete = firstComplete.Value.LastEntered,
                TransactionVolume = firstComplete.Value.Volume,
                AuthTime = authTime.Value
            });
        }

        private static Result<DateTime> GetAuthTime(IDictionary<PaymentResult, PumpTransactionPaymentState> pumpStatesPaymentStates)
        {
            var approvedStates = pumpStatesPaymentStates.Where(x => GetApproved(x.Key)).ToList();

            if (!approvedStates.Any())
            {
                return Result.Failure<DateTime>("No approved time could be found");
            }

            var max = approvedStates.Max(x => x.Value.WhenProcessed);

            return Result.Success(max);
        }

        private static bool GetApproved(PaymentResult paymentResult)
        {
            return paymentResult == PaymentResult.ApprovedAndAuthorised || paymentResult == PaymentResult.Approved;
        }

        private static int CalculateFlowSpeed(int transactionTime, int volumeVended)
        {
            return volumeVended / transactionTime;
        }

        private static Result<PumpTransactionState> GetStart(IDictionary<PumpState, PumpTransactionState> pumpStates)
        {
            var significantStates = new List<PumpTransactionState>();
            
            var vendStartStates = ExtractPumpTransactionStates(pumpStates, IsVendStart);

            if (vendStartStates.Any())
            {
                significantStates.Add(vendStartStates.SingleFromComparison(IsGreaterThan));
            }
            
            var deliveringStates = ExtractPumpTransactionStates(pumpStates, x => x == PumpState.Delivering);

            if (deliveringStates.Any())
            {
                significantStates.Add(deliveringStates.SingleFromComparison(IsLessThan));
            }
            
            if (!significantStates.Any())
            {
                return Result.Failure<PumpTransactionState>("No start time could be found");
            }

            return Result.Success(significantStates.SingleFromComparison(IsGreaterThan));
        }

        private static IList<PumpTransactionState> ExtractPumpTransactionStates(IDictionary<PumpState, PumpTransactionState> pumpStates, Func<PumpState, bool> comparisonFunc)
        {
            return pumpStates
                .Where(x => comparisonFunc(x.Key))
                .Select(x => x.Value)
                .ToList();
        }

        private static bool IsGreaterThan(PumpTransactionState curMin, PumpTransactionState x)
        {
            return curMin.LastEntered > x.LastEntered;
        }

        private static bool IsVendStart(PumpState pumpState)
        {
            return pumpState == PumpState.Request
                   || pumpState == PumpState.Authorise;
        }

        private static Result<PumpTransactionState> GetComplete(IDictionary<PumpState, PumpTransactionState> pumpStates)
        {
            var vendCompleteStates = ExtractPumpTransactionStates(pumpStates, IsVendComplete);

            if (!vendCompleteStates.Any())
            {
                return Result.Failure<PumpTransactionState>("No completion time could be found");
            }

            return Result.Success(vendCompleteStates.SingleFromComparison(IsLessThan));
        }

        private static bool IsLessThan(PumpTransactionState curMin, PumpTransactionState x)
        {
            return curMin.FirstEntered < x.FirstEntered;
        }

        private static bool IsVendComplete(PumpState pumpState)
        {
            return pumpState == PumpState.Finished
                   || pumpState == PumpState.Idle;
        }

        IHtecLogger IPumpTransactionValidator.GetLogger([CallerMemberName]string methodName=null)
        {
            return base.GetLogger(methodName);
        }
    }
}
