using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace Forecourt.Core.Helpers
{
    public class PrinterHelper : Loggable, IPrinterHelper<IMessageTracking>
    {
        public const int PosReceiptWidth = 38;
        public string InitPrinter { get; protected set; }
        public string BoldOn { get; protected set; }
        public string BoldOff { get; protected set; }
        public string Centred { get; protected set; }
        public string LeftJustified { get; protected set; }
        public string DoubleSize { get; protected set; }
        public string SingleSize { get; protected set; }
        public string FeedAndCut { get; protected set; }
        public string PrintLogo { get; protected set; }

        /// <inheritdoc />
        public bool IsLogoUploaded { get; set; }

        /// <inheritdoc cref="Loggable" />
        public PrinterHelper(IHtecLogger logger, IConfigurationManager configurationManager = null, string typeName = null) : base(logger, configurationManager, typeName)
        {
            DoCtor();
        }

        private void DoCtor()
        {
            InitPrinter = CreatePrintString(new List<byte> { 0x1B, 0x40 });
            BoldOn = CreatePrintString(new List<byte> { 0x1B, 0x45, 0x01 });
            BoldOff = CreatePrintString(new List<byte> { 0x1B, 0x45, 0x00 });
            Centred = CreatePrintString(new List<byte> { 0x1B, 0x61, 0x01 });
            LeftJustified = CreatePrintString(new List<byte> { 0x1B, 0x61, 0x00 });
            DoubleSize = CreatePrintString(new List<byte> { 0x1D, 0x21, 0x11 });
            SingleSize = CreatePrintString(new List<byte> { 0x1D, 0x21, 0x00 });
            FeedAndCut = CreatePrintString(new List<byte> { 0x1B, 0x64, 0x06, 0x1B, 0x69 });
            PrintLogo = CreatePrintString(new List<byte> { 0x1D, 0x2F, 0x00 });
        }

        private static string CreatePrintString(IEnumerable<byte> bytes) => string.Join(string.Empty, bytes.Select(x => (char)x));

        private string CentreJustifyReceiptLine(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            var padding = (PosReceiptWidth - text.Length) / 2;
            return new string(' ', padding) + text.TrimEnd();
        }

        /// <inheritdoc cref="IPrinterHelper{TMessageTracking}.FormatReceipt(string, bool, bool, IMessageTracking)"/>
        protected virtual Result<string> DoFormatReceipt(string receipt, bool isPrint, bool useControlChars, IMessageTracking message)
        {
            DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] { $"isPrint: {isPrint}; useControlChars: {useControlChars}" });
            var entry = new StringBuilder();

            if (isPrint)
            {
                /* remove to save space in limited OPT OCX receipt data block
                if (useControlChars)
                {
                    entry.Append(_boldOn);
                    entry.Append(_centred);
                    entry.Append(_doubleSize);
                    entry.AppendLine("DUPLICATE");
                    entry.AppendLine();
                    entry.Append(_singleSize);
                }
                else
                {
                    entry.AppendLine(CentreJustifyReceiptLine("DUPLICATE"));
                    entry.AppendLine();
                }
                */
                entry.AppendLine();

                if (IsLogoUploaded && useControlChars)
                {
                    entry.AppendLine(PrintLogo);
                }
            }

            bool centreAlign = false;

            try
            {
                XElement receiptElement = XElement.Parse(receipt);
                foreach (XElement element in receiptElement.Elements())
                {
                    if (isPrint)
                    {
                        if (element.Attribute("Align")?.Value == "0")
                        {
                            if (useControlChars)
                                entry.Append(Centred);

                            centreAlign = true;
                        }
                        else
                        {
                            if (useControlChars)
                                entry.Append(LeftJustified);

                            centreAlign = false;
                        }

                        if (useControlChars)
                        {
                            entry.Append(element.Attribute("Style")?.Value == "1" ? BoldOn : BoldOff);
                        }
                    }

                    if (element.Attribute("Text") != null)
                    {
                        if (element.Attribute("IsSingleChar")?.Value == "1")
                        {
                            var character = element.Attribute("Text").Value[0];
                            entry.AppendLine(new string(character, PosReceiptWidth));
                        }
                        else if (useControlChars)
                        {
                            entry.AppendLine($"{element.Attribute("Text").Value}");
                        }
                        else
                        {
                            var text = element.Attribute("Text").Value;
                            entry.AppendLine(centreAlign ? CentreJustifyReceiptLine(text) : text);
                        }
                    }
                    else if (element.Attribute("Left") != null && element.Attribute("Right") != null)
                    {
                        entry.AppendLine($"{element.Attribute("Left")?.Value} {element.Attribute("Right")?.Value}");
                    }
                    else if (element.Attribute("Left") != null)
                    {
                        entry.AppendLine($"{element.Attribute("Left")?.Value}");
                    }
                    else if (element.Attribute("Right") != null)
                    {
                        entry.AppendLine($"{element.Attribute("Right")?.Value}");
                    }
                }

                if (isPrint && useControlChars)
                {
                    entry.Append(FeedAndCut);
                }
            }
            catch (Exception ex)
            {
                var msg = receipt.Replace(Environment.NewLine, "\n").Replace("\n", Environment.NewLine);
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { $"Exception thrown for receipt: {msg}" }, ex);
                entry.AppendLine($"Receipt: {msg}");
            }

            return entry.ToString();
        }

        /// <inheritdoc cref="IPrinterHelper{TMessageTracking}.FormatReceipt(string, bool, bool, IMessageTracking)"/>
        public string FormatReceipt(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            var result = DoAction(() => { return DoFormatReceipt(receipt, isPrint, useControlChars, message); }, message.FullId);
            return result.IsSuccess ? result.Value : string.Empty;
        }

        /// <inheritdoc />
        public string ReplaceCommandStrings(string input)
        {
            return input.Replace(InitPrinter, "[Init Printer]").Replace(BoldOn, "[Bold On]").Replace(BoldOff, "[Bold Off]")
                .Replace(Centred, "[Centred]").Replace(LeftJustified, "[Left Justified]").Replace(PrintLogo, "[Print Logo]");
        }
    }
}
