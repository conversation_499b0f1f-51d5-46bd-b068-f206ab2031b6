using CSharpFunctionalExtensions;
using Htec.Foundation.Core;
using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces.Tracing;
using OPTService.OPTServiceClasses;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class WebController : LinkedLoggableApiController<IWeb>
    {
        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private IWeb Web => Loggable;

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        public WebController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger): base(web as Loggable)
        {
        }

        #region Data Retrieval

        /// <summary>
        /// Get the versions of the current service and those in OPTServiceUpgrade and OPTServiceArchive.
        /// </summary>
        /// <returns>Versions.</returns>
        [HttpGet]
        public async Task<IHttpActionResult> GetVersionInfo()
        {
            return await DoActionAsync(() => Task.FromResult(Result.Success(Web.GetVersionInfo()))).ConfigureAwait(false);
        }

        #endregion

        #region File Uploading

        [HttpPost]
        public HttpResponseMessage SaveFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving File {details.FileName}");
            string result = Web.SaveFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SaveSoftwareFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Software File {details.FileName}");
            string result = Web.SaveSoftwareFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SaveWhitelistFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Whitelist File {details.FileName}");
            string result = Web.SaveWhitelistFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SaveLayoutFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Layout File {details.FileName}");
            string result = Web.SaveLayoutFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SaveMediaFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Media File {details.FileName}");
            string result = Web.SaveMediaFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SavePlaylistFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Playlist File {details.FileName}");
            string result = Web.SavePlaylistFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SaveContactlessPropertiesFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Saving Contactless Properties File {details.FileName}");
            string result = Web.SaveContactlessPropertiesFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing File {details.FileName}");
            string result = Web.RemoveFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        #region File Removal

        [HttpPost]
        public HttpResponseMessage RemoveWhitelistFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Whitelist File {details.FileName}");
            string result = Web.RemoveWhitelistFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveLayoutFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Layout File {details.FileName}");
            string result = Web.RemoveLayoutFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveUpgradeFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Upgrade File {details.FileName}");
            string result = Web.RemoveUpgradeFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveSoftwareFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Software File {details.FileName}");
            string result = Web.RemoveSoftwareFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveMediaFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Media File {details.FileName}");
            string result = Web.RemoveMediaFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemovePlaylistFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Playlist File {details.FileName}");
            string result = Web.RemovePlaylistFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveDatabaseBackupFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing Database Backup File {details.FileName}");
            string result = Web.RemoveDatabaseBackupFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RemoveOptLogFile(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Removing OPT Log File {details.FileName}");
            string result = Web.RemoveOptLogFile(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        #region Database Backup

        [HttpGet]
        public HttpResponseMessage BackupDatabase()
        {
            Web.SendChatMessage(WebName, "Backing up Database");
            string result = Web.DatabaseBackup();
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage RestoreDatabase(UploadFileDetails details)
        {
            if (details?.FileName == null)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Filename is null");
            }

            Web.SendChatMessage(WebName, $"Restoring Database from File {details.FileName}");
            string result = Web.DatabaseRestore(details.FileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        #region Service Installation

        /// <summary>
        /// Upgrade the service.
        /// This will install the version of the service in the folder OPTServiceUpgrade.
        /// </summary>
        [HttpGet]
        public HttpResponseMessage UpgradeService()
        {
            Web.SendChatMessage(WebName, "Upgrading Service");
            Web.UpgradeService();
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        /// <summary>
        /// Rollback the service.
        /// This will install the version of the service in the folder OPTServiceArchive.
        /// </summary>
        [HttpGet]
        public HttpResponseMessage RollbackService()
        {
            Web.SendChatMessage(WebName, "Rolling Service Back");
            Web.RollbackService();
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        /// <summary>
        /// Restart the service.
        /// This will stop and restart the service.
        /// </summary>
        [HttpGet]
        public HttpResponseMessage RestartService()
        {
            Web.SendChatMessage(WebName, "Restarting Service");
            Web.RestartService();
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        #endregion

        #region Configuration Extract

        /// <summary>
        /// Retrieves all configuration data for the site
        /// </summary>
        /// <returns>All configuration data, in json format</returns>
        [HttpGet]
        public IHttpActionResult ExtractConfig()
        {
            Web.SendChatMessage(WebName, "Extracting Configuration");
            var resultData = Web.ConfigExtract();
            return Ok(resultData.Results);
        }

        #endregion

        #region Password
        /// <summary>
        /// Check a password.
        /// <param name="details">Includes user name and password to check.</param>
        /// <returns>Error message if password not correct.</returns>
        /// </summary>
        [HttpPost]
        public HttpResponseMessage CheckPassword(PasswordDetails details)
        {
            Web.SendChatMessage(WebName, "Checking Password");
            string result = Web.CheckPassword(details.UserName, details.Password);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }
        #endregion
    }
}