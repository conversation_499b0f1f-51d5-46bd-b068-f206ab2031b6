using CSharpFunctionalExtensions;
using FluentAssertions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPTService;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System;
using System.Web.Http.Results;
using Xunit;
using StatusCodeResult = Htec.Foundation.Models.StatusCodeResult;

namespace OPTServiceUnitTests.Controllers
{
    public class ShiftControllerTests
    {
        private readonly IWeb _web;
        private readonly IHtecLogger _logger;
        private readonly IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>> _bosWorker;

        public ShiftControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _web = Substitute.For<IWeb>();
            _bosWorker = Substitute.For<IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>>();
        }

        [Fact]
        public void test_GetShiftEndInfo_returns_Ok()
        {
            // Arrange
            var controller = new ShiftController(_bosWorker, _web);
            var webResult = new ShiftEndDetails() {
                ShiftEndTime = DateTime.Now,
                DayEndTime = DateTime.Now,
                AsdaDayEndReport = false,
                Unmanned = true
            };
            _web.GetShiftEndDetails().Returns(webResult);

            // Act
            var actual = controller.GetShiftEndInfo();

            // Assert
            actual.Should().BeOfType<OkNegotiatedContentResult<ShiftEndDetails>>().Which.Content.Should().BeEquivalentTo(webResult);
        }

        [Fact]
        public void test_GetShiftEndInfo_returns_NotFound()
        {
            // Arrange
            var controller = new ShiftController(_bosWorker, _web);
            ShiftEndDetails webResult = null;
            _web.GetShiftEndDetails().Returns(webResult);

            // Act
            var actual = controller.GetShiftEndInfo();

            // Assert
            actual.Should().BeOfType<NotFoundResult>();
        }
    }
}
