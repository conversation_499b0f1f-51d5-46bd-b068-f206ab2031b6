using CSharpFunctionalExtensions;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Interfaces.Core;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using core = Forecourt.Core.HydraDb.Models;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc/>
    public class WebApiPosInTransientWorker: PosReceiptXxxWorker, IWebApiPosInTransientWorker
    {
        private readonly IOptCollection _allOpts;

        /// <inheritdoc/>
        public WebApiPosInTransientWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IPumpCollection allPumps, IHydraDb hydraDb,
            IReceiptHelper receiptHelper, IOptCollection allOpts) : base(logManager, nameof(WebApiPosInTransientWorker), hydraDb, allPumps, receiptHelper, configurationManager)
        {
            _allOpts = allOpts ?? throw new ArgumentNullException(nameof(allOpts));
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> RequestReceipt(GetReceiptRequest request, IMessageTracking message = null)
        {
            return DoActionPump(request.Pump, message, (p) =>
            {
                var result = ReceiptHelper.GetReceipts(p, request);
                if (!result.IsSuccess)
                {
                    return StatusCodeResult.Specific(HttpStatusCode.BadRequest);
                }

                var results = result.Value.Select(x => {
                    var info = (ReceiptInfo)x;

                    var opt = _allOpts.GetOptForIdString<IOptCore>(info.Opt);

                    var resultRaw = opt.GetRawReceiptText((core.ReceiptInfo)info);
                    if (resultRaw.IsSuccess)
                    {
                        info.ReceiptContent = resultRaw.Value;
                    }
                    return info;
                });
                return StatusCodeResult<IEnumerable<ReceiptInfo>>.Success(results);
            });
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> RequestStatus(byte pump, IMessageTracking message = null)
        {
            return DoActionPump(pump, message, (p) =>
            {
                var result = GetStatus(p);
                if (!result.IsSuccess)
                {
                    return StatusCodeResult.Specific(HttpStatusCode.BadRequest);
                }

                return StatusCodeResult<StatusResponse>.Success(result.Value);
            });
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> RequestStatus(IPump pump, string loggingReference = null)
        {
            return RequestStatus(pump.Number, new MessageTracking() { IdAsString = loggingReference });
        }

        /// <summary>
        /// API is always connnected
        /// </summary>
        /// <returns>bool</returns>
        protected override bool DoIsConnected()
        {
            return true;
        }

        /// <inheritdoc/>
        Result IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result>.RequestReceipt(GetReceiptRequest request, IMessageTracking message)
        {
            return RequestReceipt(request, message);
        }

        /// <inheritdoc/>
        Result IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result>.RequestStatus(byte pump, IMessageTracking message)
        {
            return RequestStatus(pump, message);
        }

        /// <inheritdoc/>
        Result IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result>.RequestStatus(IPump pump, string loggingReference)
        {
            return RequestStatus(pump.Number, loggingReference.ToMessageTracking());
        }
    }
}
