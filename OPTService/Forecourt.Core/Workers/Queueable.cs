using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Htec.Foundation.Workers
{
    // TODO: Untimately move into Foundation, and both ctor types added with all parameters exposed

    /// <summary>
    /// Enhances <see cref="Workerable"/> with inbuild queueing capabilities
    /// </summary>
    /// <typeparam name="TKey">Type of queue dictionary key</typeparam>
    /// <typeparam name="TMessage">Base type of message to queue</typeparam>
    [HasConfiguration]
    public abstract class Queueable<TKey, TMessage> : Workerable
    {
        /// <summary>
        /// Dictionary of Queues of type {TMessage}
        /// </summary>
        private readonly ConcurrentDictionary<TKey, QueueableQueue<TMessage>> Queues = new();
     
        /// <inheritdoc/>
        protected Queueable(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager = null, ITimerFactory timerFactory = null, string defaultTimerInterval = "00:00:01") : 
            base(logManager, loggerName, configurationManager, timerFactory: timerFactory, defaultTimerInterval: defaultTimerInterval)
        {
        }

        /// <summary>
        /// Check for any messages in the queue and process
        /// </summary>
        /// <param name="key">Dictionary key</param>
        /// <param name="action">Action to run on a message</param>
        /// <param name="beforeAction">Action to run before, optional</param>
        /// <param name="afterAction">Action to run after, optional</param>
        /// <param name="processAll">Process all messages, or just the first one</param>
        /// <param name="nap">Sleep time betweeb cycles</param>
        /// <returns><see cref="Result"/></returns>
        public Result CheckAndProcessQueue(TKey key, Func<TMessage, Result> action, Func<TKey, Result> beforeAction = null, Action<TKey> afterAction = null, bool processAll = false, int nap = 50)
        {
            var result = Result.Success();
            while (!ShutdownToken.IsCancellationRequested)
            {
                //DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderStatus, () => new[] { $"{key}; ContainsQ: {Queues.ContainsKey(key)}; Count: {(!Queues.ContainsKey(key) ? 0 : Queues[key].Count)}" });

                result = beforeAction?.Invoke(key) ?? Result.Success();

                if (!result.IsSuccess ||!Queues.ContainsKey(key) || !Queues[key].TryDequeue(out var qMessage))
                {
                    afterAction?.Invoke(key);

                    if (!processAll)
                    {
                        break;
                    }
                    else
                    {
                        SnoozeAsync(nap);
                        continue;
                    }
                }

                result = Result.Combine(result, action(qMessage));

                afterAction?.Invoke(key);

                if (!processAll)
                {
                    break;
                }
                
                SnoozeAsync(nap);
            }

            return result;
        }

        /// <summary>
        /// Add a message to the correct queue
        /// </summary>
        /// <param name="key">Queue key</param>
        /// <param name="message">Queue messsage</param>
        public void Enqueue(TKey key, TMessage message)
        {
            if (!Queues.ContainsKey(key))
            {
                Queues[key] = new QueueableQueue<TMessage>();
            }

            Queues[key].Enqueue(message);
        }

        /// <summary>
        /// Determines if the given queue is empty
        /// </summary>
        /// <param name="key">Queue key</param>
        /// <returns>bool</returns>
        public bool IsEmpty(TKey key)
        {
            return !Queues.ContainsKey(key) || Queues[key].IsEmpty;
        }

        /// <summary>
        /// Peek any message, for the given queue
        /// </summary>
        /// <param name="key">Queue key</param>
        /// <returns>Queue message</returns>
        public TMessage TryPeek(TKey key)
        {
            return Queues.ContainsKey(key) && Queues[key].TryPeek(out var result) ? result : default;
        }

        /// <summary>
        /// All available {TKey}s
        /// </summary>
        public IEnumerable<TKey> Keys => Queues.Keys;

        /// <summary>
        /// Flushes all messages from the correct queue
        /// </summary>
        /// <param name="key">Queue key</param>
        public Result Flush(TKey key)
        {
            if (IsEmpty(key))
            {
                return Result.Failure($"IsEmpty: {TypeName}/{key}");
            }

            if (Queues.ContainsKey(key))
            {
                var q = Queues[key];
                while (q.TryDequeue(out var _)) { }
                q.LastFlushed = DateTime.UtcNow;
            }

            return Result.Success();
        }

        /// <summary>
        /// Returns the last flushed datetime, for the givem queue
        /// </summary>
        /// <param name="key">Queue key</param>
        /// <returns>DateTime last flushed, or DateTime.MinValue</returns>
        public DateTime LastFlushed(TKey key)
        {
            return !Queues.ContainsKey(key) ? DateTime.MinValue : Queues[key].LastFlushed;
        }

        /// <summary>
        /// Generic <see cref="ConcurrentQueue{T}"/> with additional properties
        /// </summary>
        /// <typeparam name="TMessage1">Base type of message to queue</typeparam>
        public class QueueableQueue<TMessage1> : ConcurrentQueue<TMessage1>
        {
            /// <summary>
            /// When was Queue last flushed
            /// </summary>
            public DateTime LastFlushed { get; protected internal set; }
        }
    }
}
