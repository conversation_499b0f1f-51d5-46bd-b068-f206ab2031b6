<div [formGroup]="formGroup" class="opt-receipt">
  <!-- Headers -->
  <div [formArrayName]="headerArrayName">
    <ng-container *ngIf="headerArray?.controls?.length">
      <ng-container *ngFor="let control of headerArray?.controls; let i = index">
        <app-label-text-button-button
          [id]="id+'ReceiptHeader'+i"
          [controlName]="i.toString()" 
          labelText="Receipt header (line {{i + 1}})"
          labelColClass="col-12 col-md-2 col-xl-3" textColClass="col-12 col-md-6 col-xl-5"
          secondaryButtonText="Remove" secondaryButtonClass="btn-danger"
          (primaryAction)="updateReceiptHeader(i, $event)" (secondaryAction)="removeReceiptHeader(i, $event)"
          [errorInAction]="headerErrors[i]" [errorInActionText]="headerErrors[i]">
        </app-label-text-button-button>
      </ng-container>
    </ng-container>
    <div class="d-flex justify-content-between">
      <button id="addReceiptHeaderBtn{{id}}" class="btn btn-success btn-labeled" (click)="addReceiptHeader($event)">
        <span class="btn-label"><i class="bi bi-plus"></i></span>Add receipt header line
      </button>
      <div class="ml-1">
        <span id="receiptHeaderError{{id}}" class="text-danger pt-1 col-auto" placement="left top" ngbTooltip="Error updating receipt header" *ngIf="headerErrors[ALL_INDEX]">
          <i class="bi bi-exclamation-triangle"></i>
        </span>
        <button id="allReceiptHeaderBtn{{id}}" class="btn btn-primary" (click)="setGlobalReceiptHeader($event)" *ngIf="!isGlobal && headerArray?.controls?.length">
          Apply header to all
        </button>
      </div>  
    </div>
  </div>

  <!-- Footers -->
  <div [formArrayName]="footerArrayName" class="pt-5">
    <ng-container *ngIf="footerArray?.controls?.length">
      <ng-container *ngFor="let control of footerArray?.controls; let i = index">
        <app-label-text-button-button
          [id]="id+'ReceipFooter'+i"
          [controlName]="i.toString()" 
          labelText="Receipt footer (line {{i + 1}})"
          labelColClass="col-12 col-md-2 col-xl-3" textColClass="col-12 col-md-6 col-xl-5"
          secondaryButtonText="Remove" secondaryButtonClass="btn-danger"
          (primaryAction)="updateReceiptFooter(i, $event)" (secondaryAction)="removeReceiptFooter(i, $event)"
          [errorInAction]="footerErrors[i]" [errorInActionText]="footerErrors[i]">
        </app-label-text-button-button>
      </ng-container>
    </ng-container>
    <div class="d-flex justify-content-between">
      <button id="addReceiptFooterBtn{{id}}" class="btn btn-success btn-labeled" (click)="addReceiptFooter($event)">
        <span class="btn-label"><i class="bi bi-plus"></i></span>Add receipt footer line
      </button>    
      <div class="ml-1">
        <span id="receiptFooterError{{id}}" class="text-danger pt-1 col-auto" placement="left top" ngbTooltip="Error updating receipt footer" *ngIf="footerErrors[ALL_INDEX]">
          <i class="bi bi-exclamation-triangle"></i>
        </span>
        <button id="allReceiptFooterBtn{{id}}" class="btn btn-primary" (click)="setGlobalReceiptFooter($event)" *ngIf="!isGlobal && footerArray?.controls?.length">
          Apply footer to all
        </button>
      </div>
    </div>
  </div>
</div>