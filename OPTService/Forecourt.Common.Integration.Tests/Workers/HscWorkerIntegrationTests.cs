using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Workers;
using Forecourt.Pump.Workers.Interfaces;
using HSC;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.HydraDb.Models;
using OPT.Common.Repositories.Interfaces;
using OPT.Common.Workers.Interfaces;
using System.Collections.Generic;
using System.Data;
using System.IO.Abstractions;
using Xunit;
using CorePumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using HscDispenser = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Dispenser6;
using HscPumpCommand = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpCommand;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using HscTransaction = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Transaction;
using HscVehicleRegistrationData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.VehicleRegistrationData;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Integration.Tests.Workers
{
    public class HscWorkerIntegrationTests
    {
        private readonly ISiteController _siteController;
        private readonly IFromOptWorker _optWorker;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IPumpWorker _hscWorker;
        private readonly IDbExecutor _dbExecutor;
        private readonly IHydraDb _hydraDb;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IJournalWorker _journalWorker;
        private readonly IDomsWorker _domsWorker;
        private readonly IConfigurationManager _configurationManager;
        private readonly ICacheHelper _cacheHelper;
        private readonly IPump _pump;
        private readonly ITimerFactory _timerFactory;
        private readonly IMessageTracking _messageTracking;
        private readonly IPumpCollection _pumpCollection;
        private readonly ITankGaugeWorker _tankGaugeWorker;
        private readonly IPumpController _pumpController;
        private readonly IPumpControllerFactory _pumpControllerFactory;
        private readonly IFileSystem _fileSystem;
        private readonly IFileVersionInfoHelper _fileVersionInfoHelper;

        public HscWorkerIntegrationTests()
        {
            _siteController = Substitute.For<ISiteController>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _dbExecutor = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(_dbExecutor);
            _logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _logManager.GetLogger(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<bool>(), Arg.Any<ILogFormatter>()).Returns(_logger);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _journalWorker = Substitute.For<IJournalWorker>();
            _domsWorker = Substitute.For<IDomsWorker>();
            _cacheHelper = Substitute.For<ICacheHelper>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _pump = Substitute.For<IPump>();
            _timerFactory = Substitute.For<ITimerFactory>();
            _tankGaugeWorker = Substitute.For<ITankGaugeWorker>();
            _pumpControllerFactory = Substitute.For<IPumpControllerFactory>();
            _fileSystem = Substitute.For<IFileSystem>();
            _fileVersionInfoHelper = Substitute.For<IFileVersionInfoHelper>();

            _journalWorker.TillNumber.Returns((short)99);
            var configurationRepository = Substitute.For<IConfigurationRepository>();
            _hydraDb = new Forecourt.Common.HydraDbClasses.HydraDb(_dbExecutorFactory, _logger, _telemetryWorker, _configurationManager, configurationRepository, _cacheHelper);
            _dbExecutor.Query<FuellingInfo>("GetFuelling", commandType: CommandType.StoredProcedure)
                .Returns(new List<FuellingInfo> { new FuellingInfo(true, 0, 0, 0, 0, 0) });

            _messageTracking = Substitute.For<IMessageTracking>();
            _pumpCollection = Substitute.For<IPumpCollection>();
            _hscWorker = new PumpWorker(_pumpControllerFactory, _journalWorker, _optWorker, _hydraDb, _logManager, _configurationManager, _timerFactory, _pumpCollection, Substitute.For<IGradeHelper>());

            _pumpController = new HydraPumpController(_logManager, _configurationManager, _hydraDb, _siteController, _fileSystem, _timerFactory, _fileVersionInfoHelper);
        }

        [Fact]
        public void test_receive_pump_data_in_sequence()
        {
            // Arrange
            const byte pump = 3;
            const byte number = 4;
            const byte pumpMake = 5;
            const byte pumpModel = 5;
            const byte pumpHoses = 4;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte tank4 = 4;
            const byte unknownGrade = 0;
            const byte grade1 = 1;
            const byte grade2 = 2;
            const byte grade3 = 3;
            const byte grade4 = 4;
            byte[] grade = { unknownGrade, grade1, grade2, grade3, grade4 };
            const ushort price1 = 1200;
            const ushort price2 = 1300;
            const ushort price3 = 1400;
            const ushort price4 = 1500;
            const bool optAvailable = true;
            const bool optInControl = false;
            const PumpState state = PumpState.Idle;
            const CommunicationState commErr = CommunicationState.Ok;
            const byte pos = 1;
            const byte currentHose = 2;
            const byte unknownHose = 0;
            const uint zeroVolume = 0;
            const uint zeroCash = 0;
            // ReSharper disable once InconsistentNaming
            const ushort zeroPPU = 0;
            // ReSharper disable once InconsistentNaming
            const ushort currentPPU = 1200;
            const uint currentVolume = 1000;
            const uint currentCash = 2000;
            const bool transaction1Paid = true;
            const byte transaction1Hose = 3;
            // ReSharper disable once InconsistentNaming
            const ushort transaction1PPU = 1200;
            const uint transaction1Volume = 2000;
            const uint transaction1Cash = 3000;
            const uint transaction1PrePaid = 4000;
            const bool transaction2Paid = true;
            const byte transaction2Hose = 4;
            // ReSharper disable once InconsistentNaming
            const ushort transaction2PPU = 1300;
            const uint transaction2Volume = 4000;
            const uint transaction2Cash = 5000;
            const uint transaction2PrePaid = 6000;
            const PrePayState prePayState = PrePayState.Active;
            const uint prePayment = 100;
            const bool vehicleRegistrationBlocked = false;
            const string vehicleRegistrationCurrent = "GEN 11";
            const string vehicleRegistrationTransaction1 = "772 YUJ";
            const string vehicleRegistrationTransaction2 = "TGK 681 M";
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    Number = number,
                    PumpMake = pumpMake,
                    PumpModel = pumpModel,
                    PumpHoses = pumpHoses,
                    Tank1 = tank1,
                    Tank2 = tank2,
                    Tank3 = tank3,
                    Tank4 = tank4,
                    Grade1 = grade[1],
                    Grade2 = grade[2],
                    Grade3 = grade[3],
                    Grade4 = grade[4],
                    Price1 = price1,
                    Price2 = price2,
                    Price3 = price3,
                    Price4 = price4,
                    OptAvailable = optAvailable,
                    OptInControl = optInControl,
                    State = (byte)state,
                    CommErr = (byte)commErr,
                    Pos = pos,
                    CurrentHose = currentHose,
                    CurrentPpu = currentPPU,
                    CurrentVolume = currentVolume,
                    CurrentCash = currentCash,
                    Transaction1 = new HscTransaction
                    {
                        Paid = transaction1Paid,
                        Hose = transaction1Hose,
                        Ppu = transaction1PPU,
                        Volume = transaction1Volume,
                        Cash = transaction1Cash,
                        PrePaid = transaction1PrePaid
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = transaction2Paid,
                        Hose = transaction2Hose,
                        Ppu = transaction2PPU,
                        Volume = transaction2Volume,
                        Cash = transaction2Cash,
                        PrePaid = transaction2PrePaid
                    },
                    PrePayState = (byte)prePayState,
                    PrePayment = prePayment
                },
                RegistrationData = new HscVehicleRegistrationData
                {
                    Blocked = vehicleRegistrationBlocked,
                    Current = vehicleRegistrationCurrent,
                    TransactionRegistration1 = vehicleRegistrationTransaction1,
                    TransactionRegistration2 = vehicleRegistrationTransaction2
                }
            };
            IList<PumpState> states = new List<PumpState>();
            IList<byte> pumps = new List<byte>();
            IList<byte> hoses = new List<byte>();
            IList<byte> grades = new List<byte>();
            IList<uint> volumes = new List<uint>();
            IList<uint> amounts = new List<uint>();
            IList<ushort> prices = new List<ushort>();
            IList<bool> paids = new List<bool>();
            IList<IList<byte>> allGradesLists = new List<IList<byte>>();
            _optWorker.WhenForAnyArgs(x => x.OnPumpState(_messageTracking, Arg.Any<PumpState>(), Arg.Any<byte>(), Arg.Any<byte>(), Arg.Any<byte>(),
                Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<ushort>(), Arg.Any<bool>(), Arg.Any<IList<byte>>())).Do(x =>
                {
                    states.Add((PumpState)x[0]);
                    pumps.Add((byte)x[1]);
                    hoses.Add((byte)x[2]);
                    grades.Add((byte)x[3]);
                    volumes.Add((uint)x[4]);
                    amounts.Add((uint)x[5]);
                    prices.Add((ushort)x[6]);
                    paids.Add((bool)x[7]);
                    allGradesLists.Add(new List<byte>((IList<byte>)x[8]));
                });

            // Act
            _hscWorker.RegisterWorker(_optWorker);
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            var pumpDataDispenser = pumpData.Dispenser;
            pumpDataDispenser.State = (byte)PumpState.Request;
            pumpDataDispenser.CurrentHose = unknownHose;
            pumpDataDispenser.CurrentVolume = zeroVolume;
            pumpDataDispenser.CurrentCash = zeroCash;
            pumpDataDispenser.CurrentPpu = zeroPPU;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Finished;
            pumpDataDispenser.CurrentHose = currentHose;
            pumpDataDispenser.CurrentVolume = currentVolume;
            pumpDataDispenser.CurrentCash = currentCash;
            pumpDataDispenser.CurrentPpu = currentPPU;
            var hscTransaction = pumpDataDispenser.Transaction2;
            hscTransaction.Paid = false;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Idle;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Request;
            pumpDataDispenser.CurrentHose = unknownHose;
            pumpDataDispenser.CurrentVolume = zeroVolume;
            pumpDataDispenser.CurrentCash = zeroCash;
            pumpDataDispenser.CurrentPpu = zeroPPU;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Finished;
            var transaction1 = pumpDataDispenser.Transaction1;
            transaction1.Paid = false;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Idle;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Idle;
            pumpDataDispenser.CurrentHose = currentHose;
            pumpDataDispenser.CurrentVolume = currentVolume;
            pumpDataDispenser.CurrentCash = currentCash;
            pumpDataDispenser.CurrentPpu = currentPPU;
            transaction1.Paid = true;
            hscTransaction.Paid = true;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            pumpDataDispenser.State = (byte)PumpState.Finished;
            pumpDataDispenser.CurrentHose = unknownHose;
            pumpDataDispenser.CurrentVolume = zeroVolume;
            pumpDataDispenser.CurrentCash = zeroCash;
            pumpDataDispenser.CurrentPpu = zeroPPU;
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            const int count = 9;
            _optWorker.Received(count).OnPumpState(_messageTracking, Arg.Any<PumpState>(), Arg.Any<byte>(), Arg.Any<byte>(), Arg.Any<byte>(), Arg.Any<uint>(),
                Arg.Any<uint>(), Arg.Any<ushort>(), Arg.Any<bool>(), Arg.Any<IList<byte>>());
            states.Should().HaveCount(count);
            states.Should().ContainInOrder(PumpState.Idle, PumpState.Request, PumpState.Finished, PumpState.Idle, PumpState.Request,
                PumpState.Finished, PumpState.Idle, PumpState.Idle, PumpState.Finished);
            pumps.Should().HaveCount(count);
            pumps.Should().OnlyContain(x => x == pump);
            hoses.Should().HaveCount(count);
            hoses.Should().ContainInOrder(currentHose, unknownHose, transaction2Hose, transaction2Hose, unknownHose, transaction1Hose,
                transaction1Hose, currentHose);
            grades.Should().HaveCount(count);
            grades.Should().ContainInOrder(grade[currentHose], grade[unknownHose], grade[transaction2Hose], grade[transaction2Hose],
                grade[unknownHose], grade[transaction1Hose], grade[transaction1Hose], grade[currentHose]);
            volumes.Should().HaveCount(count);
            volumes.Should().ContainInOrder(currentVolume, zeroVolume, transaction2Volume, transaction2Volume, zeroVolume,
                transaction1Volume, transaction1Volume, currentVolume);
            amounts.Should().HaveCount(count);
            amounts.Should().ContainInOrder(currentCash, zeroCash, transaction2Cash, transaction2Cash, zeroCash, transaction1Cash,
                transaction1Cash, currentCash);
            prices.Should().HaveCount(count);
            prices.Should().ContainInOrder(currentPPU, zeroPPU, transaction2PPU, transaction2PPU, zeroPPU, transaction1PPU, transaction1PPU,
                currentPPU);
            paids.Should().HaveCount(count);
            paids.Should().ContainInOrder(true, true, false, false, false, false, true, true);
            allGradesLists.Should().HaveCount(count);
            foreach (IList<byte> item in allGradesLists)
            {
                item.Should().BeEquivalentTo(new List<byte> { grade1, grade2, grade3, grade4 });
            }
        }

        #region Reload

        [Fact]
        public void test_reload_with_changed_address()
        {
            // Arrange
            PumpEndPoint pumpEndPoint1 = new PumpEndPoint("***************", 12345);
            PumpEndPoint pumpEndPoint2 = new PumpEndPoint("***************", 12346);
            _dbExecutor.Query<PumpEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { pumpEndPoint1 }, new List<PumpEndPoint> { pumpEndPoint2 });


            // Act
            _pumpController.Start();
            _pumpController.Restart();

            // Assert
            Received.InOrder(() =>
            {
                _siteController.Received(1).Start("***************", 12345, _pumpController as ISiteControllerEvents);
                _siteController.Received(1).Stop();
                _siteController.Received(1).Start("***************", 12346, _pumpController as ISiteControllerEvents);
            });
            _siteController.Received(2).Start(Arg.Any<string>(), Arg.Any<int>(), _pumpController as ISiteControllerEvents);
        }

        [Fact]
        public void test_reload_with_same_address()
        {
            // Arrange
            PumpEndPoint pumpEndPoint = new PumpEndPoint("***************", 12345);
            _dbExecutor.Query<PumpEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { pumpEndPoint }, new List<PumpEndPoint> { pumpEndPoint });


            // Act
            _pumpController.Start();
            _pumpController.Restart();

            // Assert
            _siteController.Received(1).Start("***************", 12345, _pumpController as ISiteControllerEvents);
            _siteController.Received(1).Start(Arg.Any<string>(), Arg.Any<int>(), _pumpController as ISiteControllerEvents);
        }

        #endregion

        #region OPT Tests

        [Fact]
        public void test_payment_approved_in_request_state()
        {
            // Arrange
            const byte pump = 2;
            _pump.Number.Returns(pump);
            const uint amount = 1000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Request
                }
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            // Act
            bool result = _hscWorker.PaymentApproved(_pump.Number, amount).IsSuccess;

            // Assert
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.AuthWithLimit,
                Pump = pump,
                Cash = amount
            };
            _siteController.Received(1).Command(pumpCommand);
            result.Should().BeTrue();
        }

        [Fact]
        public void test_payment_approved_in_stopped_state()
        {
            // Arrange
            const byte pump = 2;
            _pump.Number.Returns(pump);
            const uint amount = 3000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Stopped
                }
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            // Act
            bool result = _hscWorker.PaymentApproved(_pump.Number, amount).IsSuccess;

            // Assert
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.AuthWithLimit,
                Pump = pump,
                Cash = amount
            };
            _siteController.Received(1).Command(pumpCommand);
            result.Should().BeTrue();
        }

        [Fact]
        public void test_payment_cleared_in_idle_state_no_unpaid()
        {
            // Arrange
            const byte pump = 2;
            const uint amount = 3000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction1 = new HscTransaction
                    {
                        Paid = true,
                        Cash = amount
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = true,
                        Cash = amount
                    }
                }
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount).IsSuccess;

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void test_payment_cleared_in_idle_state_no_match()
        {
            // Arrange
            const byte pump = 2;
            const uint amount1 = 3000;
            const uint amount2 = 4000;
            const uint amount3 = 5000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction1 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount1
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount2
                    }
                }
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount3).IsSuccess;

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void test_payment_cleared_in_idle_state_match_first()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 99;
            const uint amount1 = 3000;
            const uint amount2 = 4000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction1 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount1
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount2
                    }
                }
            };
            HscPumpData pumpClaimedData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction1 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount1
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount2
                    }
                }
            };
            HscPumpCommand pumpCommand1 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 1
            };
            HscPumpCommand pumpCommand2 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = 1
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount1).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            _hscWorker.OnPumpData((CorePumpData)pumpClaimedData);

            // Assert
            Received.InOrder(() =>
            {
                _siteController.Received(1).Command(pumpCommand1);
                _siteController.Received(1).Command(pumpCommand2);
            });
            result.Should().BeTrue();
        }

        [Fact]
        public void test_payment_cleared_in_idle_state_match_second()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 99;
            const uint amount1 = 3000;
            const uint amount2 = 4000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction1 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount1
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount2
                    }
                }
            };
            HscPumpData pumpClaimedData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction1 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount1
                    },
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount2
                    }
                }
            };
            HscPumpCommand pumpCommand1 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            HscPumpCommand pumpCommand2 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount2).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            _hscWorker.OnPumpData((CorePumpData)pumpClaimedData);

            // Assert
            Received.InOrder(() =>
            {
                _siteController.Received(1).Command(pumpCommand1);
                _siteController.Received(1).Command(pumpCommand2);
            });
            result.Should().BeTrue();
        }

        [Fact]
        public void test_payment_cancelled_in_idle_state()
        {
            // Arrange
            const byte pump = 2;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle
                }
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCancelled(pump).IsSuccess;

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public void test_payment_cancelled_in_delivering_state()
        {
            // Arrange
            const byte pump = 2;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Delivering
                }
            };
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Stop,
                Pump = pump
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCancelled(pump).IsSuccess;

            // Assert
            _siteController.Received(1).Command(pumpCommand);
            result.Should().BeTrue();
        }

        [Fact]
        public void test_payment_cancelled_in_authorise_state()
        {
            // Arrange
            const byte pump = 2;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Authorise
                }
            };
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Stop,
                Pump = pump
            };
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCancelled(pump).IsSuccess;

            // Assert
            _siteController.Received(1).Command(pumpCommand);
            result.Should().BeTrue();
        }

        #endregion

        #region Reload Tests

        [Fact]
        public void test_fetch_pump_end_point()
        {
            // Arrange
            PumpEndPoint pumpEndPoint = new PumpEndPoint("***************", 12345);
            _dbExecutor.Query<PumpEndPoint>("GetPumpEndPoint", commandType: CommandType.StoredProcedure)
                .Returns(new List<PumpEndPoint> { pumpEndPoint });


            // Act
            var result = _hydraDb.FetchPumpEndPoint();

            // Assert
            result.Should().BeEquivalentTo(pumpEndPoint);
        }

        #endregion

        #region Cash Out Tests

        [Fact]
        public void test_repeat_claim()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 98;
            const uint amount = 3000;
            HscPumpData pumpData = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.SetPosClaimNumber(pos);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData);
            _hscWorker.OnPumpData((CorePumpData)pumpData);

            // Assert
            _siteController.Received(2).Command(pumpCommand);
            _siteController.ReceivedWithAnyArgs(2).Command(Arg.Any<HscPumpCommand>());
            result.Should().BeTrue();
        }

        [Fact]
        public void test_other_claim()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 98;
            const byte otherPos = 97;
            const uint amount = 3000;
            HscPumpData pumpData1 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = otherPos,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData2 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpCommand pumpCommand = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.SetPosClaimNumber(pos);
            _hscWorker.OnPumpData((CorePumpData)pumpData1);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData1);
            _hscWorker.OnPumpData((CorePumpData)pumpData2);

            // Assert
            _siteController.Received(1).Command(pumpCommand);
            _siteController.ReceivedWithAnyArgs(1).Command(Arg.Any<HscPumpCommand>());
            result.Should().BeTrue();
        }

        [Fact]
        public void test_repeat_cash_out()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 98;
            const uint amount = 3000;
            HscPumpData pumpData1 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData2 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpCommand pumpCommand1 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            HscPumpCommand pumpCommand2 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.SetPosClaimNumber(pos);
            _hscWorker.OnPumpData((CorePumpData)pumpData1);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData1);
            _hscWorker.OnPumpData((CorePumpData)pumpData2);
            _hscWorker.OnPumpData((CorePumpData)pumpData2);

            // Assert
            _siteController.Received(1).Command(pumpCommand1);
            _siteController.Received(2).Command(pumpCommand2);
            _siteController.ReceivedWithAnyArgs(3).Command(Arg.Any<HscPumpCommand>());
            result.Should().BeTrue();
        }

        [Fact]
        public void test_cash_out_done()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 98;
            const uint amount = 3000;
            HscPumpData pumpData1 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData2 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData3 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = true,
                        Cash = amount
                    }
                }
            };
            HscPumpCommand pumpCommand1 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            HscPumpCommand pumpCommand2 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.SetPosClaimNumber(pos);
            _hscWorker.OnPumpData((CorePumpData)pumpData1);

            // Act
            bool result = _hscWorker.PaymentCleared(pump, amount).IsSuccess;
            _hscWorker.OnPumpData((CorePumpData)pumpData1);
            _hscWorker.OnPumpData((CorePumpData)pumpData2);
            _hscWorker.OnPumpData((CorePumpData)pumpData3);

            // Assert
            _siteController.Received(1).Command(pumpCommand1);
            _siteController.Received(1).Command(pumpCommand2);
            _siteController.ReceivedWithAnyArgs(2).Command(Arg.Any<HscPumpCommand>());
            result.Should().BeTrue();
        }

        [Fact]
        public void test_cash_out_and_release()
        {
            // Arrange
            const byte pump = 2;
            const byte pos = 98;
            const uint amount = 3000;
            HscPumpData pumpData1 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData2 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction2 = new HscTransaction
                    {
                        Paid = false,
                        Cash = amount
                    }
                }
            };
            HscPumpData pumpData3 = new HscPumpData
            {
                PumpNumber = pump,
                Dispenser = new HscDispenser
                {
                    State = (byte)PumpState.Idle,
                    Pos = pos,
                    Transaction2 = new HscTransaction
                    {
                        Paid = true,
                        Cash = amount
                    }
                }
            };
            HscPumpCommand pumpCommand1 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Claim,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            HscPumpCommand pumpCommand2 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.CashOut,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            HscPumpCommand pumpCommand3 = new HscPumpCommand
            {
                Command = (byte)PumpCommandType.Release,
                Pump = pump,
                Pos = pos,
                TranNum = 2
            };
            _hscWorker.SetPosClaimNumber(pos);
            _hscWorker.OnPumpData((CorePumpData)pumpData1);

            var hscWorker = (PumpWorker)_hscWorker;
            var callBacks = (IPumpControllerCallbacks)_hscWorker;

            // Act
            bool result = hscWorker.PaymentCleared(pump, amount).IsSuccess;
            callBacks.OnPumpData((CorePumpData)pumpData1);
            callBacks.OnPumpData((CorePumpData)pumpData2);
            callBacks.OnPumpData((CorePumpData)pumpData3);

            // Assert
            _siteController.Received(1).Command(pumpCommand1);
            _siteController.Received(1).Command(pumpCommand2);
            _siteController.Received(1).Command(pumpCommand3);
            _siteController.ReceivedWithAnyArgs(3).Command(Arg.Any<HscPumpCommand>());
            result.Should().BeTrue();
        }

        #endregion
    }
}
