using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.HydraDb.Models;
using OPT.Common.Repositories.Interfaces;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using Xunit;
using ReceiptTransaction = OPT.Common.HydraDbClasses.ReceiptTransaction;

namespace OPT.Common.Integration.Tests
{
    public class HydraDbTests
    {
        private readonly IHydraDb _hydraDb;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IDbExecutor _dbExecutor;
        private readonly IHtecLogger _logger;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly ICacheHelper _cacheHelper;
        private readonly IConfigurationManager _configurationManager;
        private readonly IConfigurationRepository _configurationRepository;

        public HydraDbTests()
        {
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _dbExecutor = Substitute.For<IDbExecutor>();
            _dbExecutorFactory.CreateExecutor().Returns(_dbExecutor);
            _logger = Substitute.For<IHtecLogger>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _cacheHelper = Substitute.For<ICacheHelper>();
            _configurationRepository = Substitute.For<IConfigurationRepository>();
            _hydraDb = CreateDefaultInterface();
        }

        #region Receipt Tests

        [Fact]
        public void test_timeout_receipt()
        {
            // Arrange
            const string cardNumber = "1234";
            const string content = "A Receipt" + Forecourt.Common.HydraDbClasses.HydraDb.CustomerCopy;
            const string opt = "1234";
            IList<int> timeouts = new List<int> { 1 };
            _dbExecutor.Query<ReceiptInfo>("GetReceipts", commandType: CommandType.StoredProcedure).Returns(new List<ReceiptInfo>());
            _dbExecutor.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).Returns(timeouts);
            object dbAddParameters = null;
            _dbExecutor.Execute("AddReceipt", Arg.Do<object>(x => dbAddParameters = x), null, Arg.Any<int?>(), Arg.Any<CommandType?>());

            // Act
            _hydraDb.FetchReceipts();
            _hydraDb.SaveReceipt(cardNumber, new ReceiptTransaction { Details = content, CardNumber = cardNumber }, opt, 1);
            Thread.Sleep(2000);
            var result = _hydraDb.GetReceipt(cardNumber);

            // Assert
            dbAddParameters.Should().NotBeNull();
            result.Should().BeNull();
        }

        #endregion

        #region Helper Methods

        private IHydraDb CreateDefaultInterface()
        {
            return CreateDefaultInstance();
        }

        private Forecourt.Common.HydraDbClasses.HydraDb CreateDefaultInstance()
        {
            return new Forecourt.Common.HydraDbClasses.HydraDb(_dbExecutorFactory, _logger, _telemetryWorker, _configurationManager, _configurationRepository, _cacheHelper);
        }

        #endregion
    }
}
