using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(20050005, "ADO#646362 - Add all dbo.TransactionBookingState related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO646362_GetTransactionBookingState : Migrationable
    {
        /// <inheritdoc/>
        public ADO646362_GetTransactionBookingState(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetTransactionBooking", "dbo.GetPendingTransactionBookings", "dbo.UpdateTransactionBooking", "dbo.PruneTransactions", "dbo.AddTransaction" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO646362_GetTransactionBookingState), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO646362_GetTransactionBookingState), DbObjects.Reverse().ToArray());
        }
    }
}
