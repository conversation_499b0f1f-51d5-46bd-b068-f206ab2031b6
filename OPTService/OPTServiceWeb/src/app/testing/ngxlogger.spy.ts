import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';

export function NGX_LOGGER_PROVIDER(): { provide: typeof NGXLogger, useValue: any } {
  return {
    provide: NGXLogger,
    useValue: jasmine.createSpyObj('NGXLogger',['info','debug','warn','error']),
  };
}

export function NGX_LOGGER_SPY(): jasmine.SpyObj<NGXLogger> {
  return TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
}