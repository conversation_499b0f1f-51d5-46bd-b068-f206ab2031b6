import { Component, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Router, RoutesRecognized } from '@angular/router';
import { Subscription } from 'rxjs';
import { SignalREventType } from './core/enums/signalREventType';
import { LoadingService } from './services/loading.service';
import { SignalRService } from './services/signal-r.service';
import { trigger, style, animate, transition } from '@angular/animations';
import { PumpService } from './services/pump.service';
import { NGXLogger } from 'ngx-logger';
import { faStoreAlt, faUserSlash } from '@fortawesome/free-solid-svg-icons';
import { faTruckMoving } from '@fortawesome/free-solid-svg-icons';
import { faPlane } from '@fortawesome/free-solid-svg-icons';
import { faShip } from '@fortawesome/free-solid-svg-icons';
import { faQuestionCircle } from '@fortawesome/free-solid-svg-icons';
import { faWifi } from '@fortawesome/free-solid-svg-icons';
import { faSyncAlt } from '@fortawesome/free-solid-svg-icons';
import { Constants } from './helpers/constants';
import { AdvancedConfigService } from './services/advanced-config.service';
import { IntegrationType } from './core/enums/integrationType.enum';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  animations: [
    trigger(
      'inOutAnimation', 
      [
        transition(
          ':enter', 
          [
            style({ opacity: 0 }),
            animate('1s ease-out', 
                    style({ opacity: 1 }))
          ]
        ),
        transition(
          ':leave', 
          [
            style({ opacity: 1 }),
            animate('1s ease-in', 
                    style({ opacity: 0 }))
          ]
        )
      ]
    )
  ]
})

export class AppComponent implements OnInit {
  @ViewChild('nav') nav;

  title = 'OPTWebAdmin';
  activeScreenId: string = '';
  activeScreenName: string = '';
  private routeData;

  signalrConnectionStatus: number | undefined;
  advancedConfigRefreshDataSubscription: Subscription | undefined;
  signalRConnectionChangeSubscription: Subscription | undefined;
  getConnectionSignalRMessageSubscription: Subscription | undefined;

  faStoreAlt = faStoreAlt;
  faTruckMoving = faTruckMoving;
  faPlane = faPlane;
  faShip = faShip;
  faQuestionCircle = faQuestionCircle;
  faWifi = faWifi;
  faSyncAlt = faSyncAlt;
  faUserSlash = faUserSlash;

  retailSiteType = Constants.RetailSiteType;
  rdcSiteType = Constants.RdcSiteType;
  airfieldSiteType = Constants.AirfieldSiteType;
  marinaSiteType = Constants.MarinaSiteType;
  siteType: string = 'Unknown';
  siteName: string = 'Unknown';
  isPumpControllerEnabled: boolean = true;
  unmannedPseudoPos: boolean = false;

  integratorType: any;
  integrators: Map<IntegrationType, string> = new Map<IntegrationType, string>();

  /**
   * The application main component constructor
   * @param signalRService The signalR service
   * @param zone The angular zone service used to keep screen updated
   */
  constructor(
    public signalRService: SignalRService,
    private zone: NgZone,
    private router: Router,
    private logger: NGXLogger,
    private loadingService: LoadingService,
    private advancedConfigService: AdvancedConfigService,
    private pumpService: PumpService
  ) {
    this.integratorType = IntegrationType;

    this.signalRConnectionChangeSubscription = this.signalRService.getConnectionStatushChangeMessage().subscribe((newStatus) => {
      this.zone.run(() => {
        this.signalrConnectionStatus = newStatus;
      });
    });

    this.advancedConfigRefreshDataSubscription = this.signalRService.getAdvancedConfigSignalRMessage().subscribe(() => {
      this.zone.run(() => {
        this.refreshAdvancedData();
      });
    });

    this.getConnectionSignalRMessageSubscription = this.signalRService.getConnectionSignalRMessage().subscribe(() => {
      this.zone.run(() => {
          this.refreshPumpControllerData();
      });
    });
  }

  /**
   * Checks the connectivity with SignalR
   * @returns TRUE if connection with SignalR is active
   */
  isSignalRConnected(){
    return this.signalrConnectionStatus === this.signalRService.CONNECTION_STATUS_CONNECTED;
  }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    // Set the title
    this.router.events.subscribe((data) => {
      if (data instanceof RoutesRecognized) {
        this.routeData = data.state.root.firstChild.data;
        this.activeScreenName = this.routeData.routeTitle;
        this.activeScreenId = this.routeData.routeId;
      }
    });
    
    this.refreshData();

    // Start signalR connection
    this.signalRService.startConnection();    
  }

  refreshData(): void {
    this.refreshPumpControllerData();
    this.refreshAdvancedData();
  }

  private refreshPumpControllerData(): void {
    this.pumpService.getPumpControllerStatus().subscribe(
      (data) => {
        this.logger.debug('PumpControllerEnabled', data);
        this.isPumpControllerEnabled = data;
      },
      () => {
        this.logger.error('Problem getting OPT data');
        this.isPumpControllerEnabled = false;
      }
    );
  }

  private refreshAdvancedData(): void {
    this.advancedConfigService.getAdvancedConfig(true).subscribe(
      (data) => {
        this.logger.debug('Advanced Configuration data (Refresh)', data);
        this.siteType = data.SiteType;
        this.siteName = data.SiteName;
        this.unmannedPseudoPos = data.UnmannedPseudoPos
        this.integrators[this.integratorType.Pump] = data.PumpType;
        this.integrators[this.integratorType.Pos] = data.PosType;
        this.integrators[this.integratorType.SecAuth] = data.SecAuthType;
        this.integrators[this.integratorType.BackOffice] = data.BosType;
        this.integrators[this.integratorType.MobilePos] = data.MobilePosType;
      },
      () => {
        this.logger.error('Problem getting Advanced Config data');
      }
    );
  }

  /**
   * ngOnDestroy angular hook
   */
  ngOnDestroy(): void {
    this.advancedConfigRefreshDataSubscription?.unsubscribe();
    this.signalRConnectionChangeSubscription?.unsubscribe();
    this.getConnectionSignalRMessageSubscription?.unsubscribe();
  }

  /**
   * Refreshes the current component being displayed
   */
  refreshCurrentComponent() {
    //If signalR is not connected, call the SignalRService startConnection
    if(!this.isSignalRConnected()){
      this.signalRService.startConnection();
    }

    switch (this.activeScreenId) {
      case 'opts':
        this.signalRService.processPushedEvent(SignalREventType.OPTChanged);
        break;
      case 'pumps':
        this.signalRService.processPushedEvent(SignalREventType.PumpChanged);
        break;
      case 'fuel-prices':
        this.signalRService.processPushedEvent(SignalREventType.FuelPriceChanged);
        break;
      case 'connections':
        this.signalRService.processPushedEvent(SignalREventType.ConnectionChanged);
        break;
      case 'generic-opt-config':
        this.signalRService.processPushedEvent(SignalREventType.GenericOptConfigChanged);
        break;
      case 'divert-opt-service':
        this.signalRService.processPushedEvent(SignalREventType.DivertDetailsChanged);
        break;
      case 'advanced-config':
        this.signalRService.processPushedEvent(SignalREventType.AdvancedConfigChanged);
        break;
      case 'file-locations':
        this.signalRService.processPushedEvent(SignalREventType.FileLocationsChanged);
        break;
      case 'shift-end':
        this.signalRService.processPushedEvent(SignalREventType.ShiftEndChanged);
        break;
      case 'transactions':
        this.signalRService.processPushedEvent(SignalREventType.TransactionChanged);
        break;
      case 'local-accounts':
        this.signalRService.processPushedEvent(SignalREventType.LocalAccountsChanged);
        break;
      case 'doms':
        // this.signalRService.processPushedEvent(SignalREventType.DomsChanged);
        break;
      case 'info-messages':
        this.signalRService.processPushedEvent(SignalREventType.InfoMessageChanged);
        break;
      case 'about':
        this.signalRService.processPushedEvent(SignalREventType.AboutChanged);
        break;
      default:
        break;
    }
  }

  getSignalRConnectionStateText(){
    let result = 'Connecting';

    switch (this.signalrConnectionStatus) {
      case this.signalRService.CONNECTION_STATUS_CONNECTING:
        result = 'Connecting';
        break;
      case this.signalRService.CONNECTION_STATUS_CONNECTED:
        result = 'Connected';
        break;
      case this.signalRService.CONNECTION_STATUS_RECONNECTING:
        result = 'Reconnecting';
        break;
      case this.signalRService.CONNECTION_STATUS_DISCONNECTED:
          result = 'Disconnected';
          break;
    }

    return result;
  }

  shouldShowLoading(): boolean {
    return this.loadingService.showLoading;
  }

  shouldShowError(): boolean {
    return this.loadingService.showError;
  }

  getLastRefreshTime(): string {
    return this.loadingService.lastRefresh?.toLocaleString(Constants.DATE_TIME_LOCALE);
  }

}
