namespace OPT.Common.HydraDbClasses
{
    public class ItemSales
    {
        public short Category { get; }
        public short Subcategory { get; }
        public string GradeCode { get; }
        public string GradeName { get; }
        public uint Amount { get; }
        public uint Quantity { get; }

        // ReSharper disable once UnusedMember.Global
        public ItemSales(int category, int subcategory, string gradeCode, string gradeName, long amount, long quantity)
        {
            Category = (short) category;
            Subcategory = (short) subcategory;
            GradeCode = gradeCode;
            GradeName = gradeName;
            Amount = (uint) amount;
            Quantity = (uint) quantity;
        }
    }
}