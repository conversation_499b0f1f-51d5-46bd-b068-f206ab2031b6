using Forecourt.Pump.Controllers.Interfaces;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;

namespace Forecourt.Pump.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the HscWorker
    /// </summary>
    public interface IPumpWorker : IConnectable, 
        IPumpControllerCallbacks,    
        IPumpIntegratorIn<IMessageTracking>,
        IPumpIntegratorConfiguration
    {
        bool IndefiniteWait { get; }
        int WaitMinutes { get; }
        int BackoffAuth { get; }
        int BackoffPreAuth { get; }
        int BackoffStopStart { get; }
        int BackoffStopOnly { get; }  
    }
}