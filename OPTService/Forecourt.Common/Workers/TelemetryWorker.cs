using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Forecourt.Common.Workers
{
    [HasConfiguration()]
    public class TelemetryWorker : CoreTelemetryWorker, ITelemetryWorker
    {
        private readonly IHtecLogger _telemetry;
        private readonly IDictionary<IOpt, DateTime> _opts = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<IOpt, DateTime> _optRequestConfigs = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<string, DateTime> _optReloadConfigs = new ConcurrentDictionary<string, DateTime>();
        private readonly IDictionary<IOpt, DateTime> _optRequestWhitelists = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<IOpt, DateTime> _optRequestLayouts = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<IOpt, DateTime> _optRequestSoftware = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<IOpt, DateTime> _optRequestAsset = new ConcurrentDictionary<IOpt, DateTime>();
        private readonly IDictionary<byte, DateTime> _pumpClearedPayments = new ConcurrentDictionary<byte, DateTime>();
        private DateTime _carWash = DateTime.MinValue;
        private DateTime _tankGauge = DateTime.MinValue;
        private DateTime _dipsRequest = DateTime.MinValue;
        private DateTime _metersRequest = DateTime.MinValue;
        private readonly IDictionary<byte, DateTime> _anprs = new ConcurrentDictionary<byte, DateTime>();
        private readonly IDictionary<byte, DateTime> _thirdParties = new ConcurrentDictionary<byte, DateTime>();
        private readonly string _device;
        private readonly string _version;       

        public TelemetryWorker(string device, string version, IHtecLogger telemetry, IHtecLogger logger, IConfigurationManager configurationManager): base(logger, telemetry, configurationManager, device, version)
        {
        }

        public void MessageSentToOpt(IOpt opt)
        {
            DoAction(() =>
            {
                if (opt != null)
                {
                    _opts[opt] = DateTime.Now;
                }
            }, LoggingReference);
        }

        public void MessageReceivedFromOpt(IOpt opt, string message, string loggingReference = null)
        {
            LogMessageAndRemoveTarget(opt, message, _opts, (now) =>
            {
                if (string.Equals(message, "ConfigPending"))
                {
                    _optRequestConfigs[opt] = now;
                }
                else if (string.Equals(message, "WhitelistPending"))
                {
                    _optRequestWhitelists[opt] = now;
                }
                else if (string.Equals(message, "LayoutPending"))
                {
                    _optRequestLayouts[opt] = now;
                }
                else if (string.Equals(message, "SoftwarePending"))
                {
                    _optRequestSoftware[opt] = now;
                }
                else if (string.Equals(message, "AssetPending"))
                {
                    _optRequestAsset[opt] = now;
                }
            }, loggingReference: loggingReference);
        }

        public void WhitelistRequestReceivedFromOpt(IOpt opt)
        {
            LogMessageAndRemoveTarget(opt, "Whitelist", _optRequestWhitelists);
        }

        public void LayoutRequestReceivedFromOpt(IOpt opt)
        {
            LogMessageAndRemoveTarget(opt, "Layout", _optRequestLayouts);
        }

        public void SoftwareRequestReceivedFromOpt(IOpt opt)
        {
            LogMessageAndRemoveTarget(opt, "Software", _optRequestSoftware);
        }

        public void AssetRequestReceivedFromOpt(IOpt opt)
        {
            LogMessageAndRemoveTarget(opt, "Asset", _optRequestAsset);
        }

        public void ConfigRequestReceivedFromOpt(IOpt opt)
        {
            LogMessageAndRemoveTarget(opt, "Config", _optRequestConfigs);
        }

        public void SignInReceivedFromOpt(string opt)
        {
            LogMessageAndRemoveTarget(opt, "Config (Reload)", _optReloadConfigs);
        }

        public void DeliveredSentToOpt(byte pump, string loggingReference)
        {
            DoAction(() => _pumpClearedPayments[pump] = DateTime.Now, loggingReference);
        }

        public void PaymentClearedReceivedFromOpt(byte pump, string loggingReference)
        {
            LogMessageAndRemoveTarget(pump, "Payment Cleared", _pumpClearedPayments, targetName: "Pump", loggingReference: loggingReference);
        }

        public void MessageTimeoutFromOpt(IOpt opt, string message, string loggingReference)
        {
            LogMessageAndRemoveTarget(opt, message, _opts, isTimeout: true, loggingReference: loggingReference);
        }

        public void MessageSentToCarWash()
        {
            DoAction(() => _carWash = DateTime.Now, LoggingReference);
        }

        public void MessageReceivedFromCarWash()
        {
            LogMessage(_carWash, "Car Wash");
        }

        public void MessageTimeoutFromCarWash()
        {
            LogMessage(_carWash, "Car Wash", isTimeout: true);
        }

        public void MessageSentToTankGauge()
        {
            DoAction(() => _tankGauge = DateTime.Now, LoggingReference);
        }

        public void MessageReceivedFromTankGauge()
        {
            LogMessage(_tankGauge, "Tank Gauge");
        }

        public void MessageTimeoutFromTankGauge()
        {
            LogMessage(_tankGauge, "Tank Gauge", isTimeout: true);
        }

        public void DipsRequested()
        {
            DoAction(() => _dipsRequest = DateTime.Now, LoggingReference);
        }

        public void DipsReceived()
        {
            LogMessage(_dipsRequest, "Dips Request");
        }

        public void DipsTimedOut()
        {
            LogMessage(_dipsRequest, "Dips Request", isTimeout: true);
        }

        public void MetersRequested()
        {
            DoAction(() => _metersRequest = DateTime.Now, LoggingReference);
        }

        public void MetersReceived()
        {
            LogMessage(_metersRequest, "Meters Request");
        }

        public void MetersTimedOut()
        {
            LogMessage(_metersRequest, "Meters Request", isTimeout: true);
        }

        public void MessageSentToSecAuthHost(byte pump, string loggingReference)
        {
            DoAction(() => _anprs[pump] = DateTime.Now, loggingReference);
        }

        public void MessageReceivedFromSecAuthHost(byte pump, string loggingReference)
        {
            LogMessageAndRemoveTarget(pump, "SecAuth HOST", _anprs, targetName: "Pump", loggingReference: loggingReference);
        }

        public void MessageSentToThirdPartyPos(byte pump)
        {
            DoAction(() => _thirdParties[pump] = DateTime.Now, LoggingReference);
        }

        public void MessageReceivedFromThirdPartyPos(byte pump)
        {
            LogMessageAndRemoveTarget(pump, "Third Party POS", _thirdParties, targetName: "Pump");
        }

        public void MessageTimeoutFromSecAuthHost(byte pump, string loggingReference = null)
        {
            LogMessageAndRemoveTarget(pump, "SecAuth HOST", _anprs, targetName: "Pump", isTimeout: true, loggingReference: loggingReference);
        }

        public void QuerySentToEsocketDb(Guid guid)
        {
            DoAction(() => GuidTimes[guid] = DateTime.Now, $"{guid}");
        }

        public void QueryReturnedFromEsocketDb(string query, Guid guid)
        {
            LogMessageAndRemoveTarget(guid, query, GuidTimes, targetName: "eSocket.POS DB", loggingReference: $"{guid}");
        }

        public void StagedMessageStarted(Guid guid)
        {
            DoAction(() => GuidTimes[guid] = DateTime.Now, loggingReference: $"{guid}");
        }

        public void StagedMessageFinished(Guid guid, string filename, int length)
        {
            LogMessageAndRemoveTarget(guid, $"{filename}, {length} bytes", GuidTimes, targetName: "Staged message", loggingReference: $"{guid}");
        }       
    }
}
