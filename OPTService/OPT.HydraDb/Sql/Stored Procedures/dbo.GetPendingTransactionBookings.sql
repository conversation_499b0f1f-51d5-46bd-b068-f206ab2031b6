USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetPendingTransactionBookings') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetPendingTransactionBookings
END
GO

CREATE PROCEDURE [dbo].[GetPendingTransactionBookings]
AS
BEGIN
	SELECT 
		t.Id, 
		t.TransactionId,
        t.TxnN<PERSON>ber, 
        t.TransactionDate, 
        t.ExternalTransactionId,
        t.ShiftId,
        t.PeriodId,
        t.BusinessDate,
        t.HttpStatusCode,
        t.HttpResponse,
        t.BookedDate,
        t.RetryCount,
        t.NextRetryDate,
        t.CreatedDate,
        t.ModifiedDate,
        t.SendTransactionItem
	FROM TransactionBookingState t
	WHERE 
        (t.BookedDate IS NULL);

END
GO

GRANT EXECUTE ON GetPendingTransactionBookings TO Use<PERSON><PERSON>ole
GO
