using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Messages.Opt.Xsd;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Messaging;
using System.Collections.Generic;
using Xunit;

namespace OPT.Common.Tests.Workers.Messaging
{
    public class OptConfigurationBuilderTests
    {
        public class TestableGenericOptConfig : GenericOptConfig
        {
            public bool IsAmountEnabledFuel { get; set; }
            public override bool PredefinedAmountFuel => IsAmountEnabledFuel;

            public bool IsAmountEnabledPayment { get; set; }
            public override bool PredefinedAmountPayment=> IsAmountEnabledPayment;

            public bool IsAmountEnabledAccount { get; set; }
            public override bool PredefinedAmountLocalAccount => IsAmountEnabledAccount;

            public string PciRestartTimeOverride { get; set; } = DefaultValuePciRestartTime;

            public int MinimumAuthorisedAmountOverride { get; set; } = DefaultValueMinimumAuthorisedAmount;

            public override string PciRestartTime => PciRestartTimeOverride;

            public override int MinimumAuthorisedAmount => MinimumAuthorisedAmountOverride;

            public IList<int> Amounts { get; set; }
            public override IList<int> PredefinedAmounts=> Amounts;

            public int GetConfigRetryAttemptsOverride { get; set; } = DefaultValueGetConfigRetryAttempts;

            public override int GetConfigRetryAttempts => GetConfigRetryAttemptsOverride; 

            public TestableGenericOptConfig(IPaymentConfigIntegrator paymentConfig, IHydraDb hydraDb, string hydraId, IHtecLogManager logger, IConfigurationManager configurationManager) 
                : base(paymentConfig, hydraDb, hydraId, logger, configurationManager)
            {
            }
        }

        private const string HydraId = "Hydra1";

        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _logger;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IContactlessProperties _contactlessProperties;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;

        public OptConfigurationBuilderTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _logger = Substitute.For<IHtecLogger>();
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _contactlessProperties = Substitute.For<IContactlessProperties>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _logManager = Substitute.For<IHtecLogManager>();
        }

        #region Constructor

        [Fact]
        public void constructor_null_hydra_db_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new OptConfigurationBuilder(null, _logger);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "hydraDb");
        }

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new OptConfigurationBuilder(_hydraDb, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        #endregion

        #region Create Predefined Amounts

        [Fact]
        public void create_predefined_amounts_cards_enabled_default_generic_opt_config_returns_null()
        {
            // Arrange
            var optConfigurationBuilder = InitialiseGenericOptConfig();

            // Act
            var amounts = OptConfigurationBuilder.CreatePredefinedAmounts(optConfigurationBuilder);

            // Assert
            amounts.Should().BeNull();
        }

        public class PredefinedLimitsConfigTestCase
        {
            public bool FuelEnabled { get; set; }

            public bool PaymentEnabled { get; set; }

            public bool AccountEnabled { get; set; }

            public IList<int> Amounts { get; set; }

            public PredefinedLimitsConfigTestCase(bool fuelEnabled, bool paymentEnabled, bool accountEnabled, IList<int> amounts)
            {
                FuelEnabled = fuelEnabled;
                PaymentEnabled = paymentEnabled;
                AccountEnabled = accountEnabled;
                Amounts = amounts;
            }
        }

        public static TheoryData<PredefinedLimitsConfigTestCase> NullResultTestData => new TheoryData<PredefinedLimitsConfigTestCase>
        {
            new PredefinedLimitsConfigTestCase(true, false, false, null),
            new PredefinedLimitsConfigTestCase(false, true, false, null),
            new PredefinedLimitsConfigTestCase(false, false, true, null),
            new PredefinedLimitsConfigTestCase(false, false, false, new List<int>()),
            new PredefinedLimitsConfigTestCase(false, false, false, new List<int> { 50 })
        };

        [Theory]
        [MemberData(nameof(NullResultTestData), MemberType = typeof(OptConfigurationBuilderTests))]
        public void create_predefined_amounts_default_generic_opt_config_returns_null(PredefinedLimitsConfigTestCase testCase)
        {
            // Arrange
            var config = InitialiseGenericOptConfig();

            config.IsAmountEnabledFuel = testCase.FuelEnabled;
            config.IsAmountEnabledPayment = testCase.PaymentEnabled;
            config.IsAmountEnabledAccount = testCase.AccountEnabled;
            config.Amounts = testCase.Amounts;

            // Act
            var amounts = OptConfigurationBuilder.CreatePredefinedAmounts(config);

            // Assert
            amounts.Should().BeNull();
        }

        public static TheoryData<PredefinedLimitsConfigTestCase> TheoryTheoryData1 => new TheoryData<PredefinedLimitsConfigTestCase>
        {
            new PredefinedLimitsConfigTestCase(true, false, false, new List<int> { 50 }),
            new PredefinedLimitsConfigTestCase(false, true, false, new List<int> { 50 }),
            new PredefinedLimitsConfigTestCase(false, false, true, new List<int> { 50 }),
            new PredefinedLimitsConfigTestCase(true, true, false, new List<int> { 50 }),
            new PredefinedLimitsConfigTestCase(false, true, true, new List<int> { 50 }),
            new PredefinedLimitsConfigTestCase(true, true, true, new List<int> { 50 })
        };

        [Theory]
        [MemberData(nameof(TheoryTheoryData1), MemberType = typeof(OptConfigurationBuilderTests))]
        public void create_predefined_amounts_default_generic_opt_config_returns_null1(PredefinedLimitsConfigTestCase testCase)
        {
            // Arrange
            var config = InitialiseGenericOptConfig();

            config.IsAmountEnabledFuel = testCase.FuelEnabled;
            config.IsAmountEnabledPayment = testCase.PaymentEnabled;
            config.IsAmountEnabledAccount = testCase.AccountEnabled;
            config.Amounts = testCase.Amounts;

            // Act
            var amounts = OptConfigurationBuilder.CreatePredefinedAmounts(config);

            // Assert
            amounts.amount.Should().BeEquivalentTo(new amount { NullableValue = 50 });
        }

        #endregion

        #region PCI Restart time

        [Fact]
        public void create_default_opt_config_returns_default_pci_restart_time()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();

            // Act
            var result = config.PciRestartTime;

            // Assert
            result.Should().Be(GenericOptConfig.DefaultValuePciRestartTime);
        }


        [Fact]
        public void create_opt_config_with_pcirestarttime_override_returns_overidden_pci_restart_time()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();
            config.PciRestartTimeOverride = "05:00:00";

            // Act
            var result = config.PciRestartTime;

            // Assert
            result.Should().Be("05:00:00");
        }

        #endregion

        #region PCI Restart time

        [Fact]
        public void create_default_opt_config_returns_default_getconfig_retry_attempts()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();

            // Act
            var result = config.GetConfigRetryAttempts;

            // Assert
            result.Should().Be(GenericOptConfig.DefaultValueGetConfigRetryAttempts);
        }

        [Fact]
        public void create_opt_config_with_getconfig_retry_attempts_override_returns_overidden_getconfig_retry_attempts()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();
            config.GetConfigRetryAttemptsOverride = 8;

            // Act
            var result = config.GetConfigRetryAttempts;

            // Assert
            result.Should().Be(8);
        }

        #endregion

        #region Minimum Authorised Amount

        [Fact]
        public void create_default_opt_config_returns_minimum_authorised_amount()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();

            // Act
            var result = config.MinimumAuthorisedAmount;

            // Assert
            result.Should().Be(GenericOptConfig.DefaultValueMinimumAuthorisedAmount);
        }


        [Fact]
        public void create_opt_config_with_minimumauthorisedamount_override_returns_overidden_minimum_authorised_amount()
        {
            // Arrange
            var config = InitialiseGenericOptConfig();
            config.MinimumAuthorisedAmountOverride = 500;

            // Act
            var result = config.MinimumAuthorisedAmount;

            // Assert
            result.Should().Be(500);
        }

        #endregion

        #region Helper Methods

        private TestableGenericOptConfig InitialiseGenericOptConfig()
        {
            _hydraDb.FetchEndPoints(Arg.Any<string>())
                .Returns(new OptEndPoints());

            _hydraDb.GetSiteInfo()
                .Returns(new SiteInfo());

            _hydraDb.FetchOptMode(Arg.Any<string>())
                .Returns(new OptMode(OptCollection.GlobalOptId));

            var advConfg = new AdvancedConfig(_hydraDb, _logger, _configurationManager);
            _hydraDb.AdvancedConfig
                .Returns(advConfg);

            return new TestableGenericOptConfig(_paymentConfig, _hydraDb, HydraId, _logManager, _configurationManager);
        }

        #endregion
    }
}
