namespace Forecourt.Core.HydraDb.Models
{
    public class PumpDelivered
    {
        public byte Number { get; }
        public bool HasOptPayment { get; }
        public bool HasDelivered { get; }
        public byte Grade { get; }
        public byte Hose { get; }
        public uint Volume { get; }
        public uint Amount { get; }
        public string Name { get; }
        public ushort Price { get; }
        public uint NetAmount { get; }
        public uint VatAmount { get; }
        public float VatRate { get; }
        public int TransSeqNum { get; }

        // ReSharper disable once UnusedMember.Global
        public PumpDelivered(int number, bool optPayment, bool delivered, int grade, int hose, long volume, long amount, string name, int price, long netAmount, long vatAmount, double vatRate, int transSeqNum)
        {
            Number = (byte)number;
            HasOptPayment = optPayment;
            HasDelivered = delivered;
            Grade = (byte)grade;
            Hose = (byte)hose;
            Volume = (uint)volume;
            Amount = (uint)amount;
            Name = name;
            Price = (ushort)price;
            NetAmount = (uint)netAmount;
            VatAmount = (uint)vatAmount;
            VatRate = (float)vatRate;
            TransSeqNum = transSeqNum;
        }
    }
}
