import { Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { ControlContainer, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-label-text-button-button',
  templateUrl: './label-text-button-button.component.html',
  styleUrls: ['./label-text-button-button.component.css']
})
export class LabelTextButtonButtonComponent implements OnInit {

  public form: FormGroup;
  public control : FormControl;

  @HostBinding('attr.id') externalId = null;
  @Input() labelText: string = '';
  @Input() placeHolderText: string = '';
  @Input() primaryButtonText: string = 'Save';
  @Input() secondaryButtonText: string = 'Clear';
  @Input() errorInActionText: string = 'Field couldn\'t be updated';
  @Input() appendText: string = '';
  @Input() controlName: string = '';  
  @Input() errorInAction: boolean = false;
  @Input() labelColClass: string = 'col-2';
  @Input() textColClass: string = 'col-2';
  @Input() buttonColClass: string = 'col-auto';
  @Input() secondaryButtonClass: string = 'btn-primary';
  @Input() disabled: boolean = false;
  @Output() primaryAction= new EventEmitter();
  @Input('id') private _id: string;
  @Input() maxLength: number = 524288; //The default value for HTML input maxlength attribute
  
  @Output() secondaryAction= new EventEmitter();

  public get id(): string {
    return this._id || this.controlName || '';
  }

  public get fieldFeedbackId(): string {
    return `${this.id}FieldFeedback`;
  }

  public get primaryButtonId(): string {
    return `${this.id}PrimaryBtn`;
  }

  public get secondaryButtonId(): string {
    return `${this.id}SecondaryBtn`;
  }

  public get errorId(): string {
    return `${this.id}Error`;
  }

  constructor(private controlContainer: ControlContainer) { }

  ngOnInit(): void {
    this.form = <FormGroup>this.controlContainer.control;
    this.control = <FormControl>this.form.get(this.controlName);
  }

  doPrimaryAction(event?: Event): void {
    this.primaryAction.emit(event);
  }

  doSecondaryAction(event?: Event): void {
    this.secondaryAction.emit(event);
  }

}
