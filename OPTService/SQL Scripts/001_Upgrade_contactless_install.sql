USE [Hydra]

PRINT N'001_Upgrade_contactless_install...Begin';
PRINT N'';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationCategory') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationCategory]...';

	CREATE TABLE [dbo].[ConfigurationCategory](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[Category] [nvarchar](100) NOT NULL,
		[Description] [nvarchar](255) NULL,
	 CONSTRAINT [PK_ConfigurationCategory] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationType') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationType]...';
	
	CREATE TABLE [dbo].[ConfigurationType](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[Type] [nvarchar](100) NOT NULL,
		[Description] [nvarchar](255) NULL,
	 CONSTRAINT [PK_ConfigurationType] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationHeader') AND TYPE = 'u')
BEGIN
    PRINT N'Creating [dbo].[ConfigurationHeader]...';

	CREATE TABLE [dbo].[ConfigurationHeader](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[TypeId] [int] NOT NULL,
		[Description] [nvarchar](255) NULL,
		[ModifiedDateTime] [datetime] NOT NULL,
	 CONSTRAINT [PK_ConfigurationHeader] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
	ALTER TABLE [dbo].[ConfigurationHeader]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationHeader_ConfigurationType]
	FOREIGN KEY([TypeId]) REFERENCES [dbo].[ConfigurationType] ([id])

	ALTER TABLE [dbo].[ConfigurationHeader] CHECK CONSTRAINT [FK_ConfigurationHeader_ConfigurationType]
END
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('ConfigurationDetail') AND TYPE = 'u')
BEGIN
	PRINT N'Creating [dbo].[ConfigurationDetail]...';

	CREATE TABLE [dbo].[ConfigurationDetail](
		[Id] [int] IDENTITY(1,1) NOT NULL,
		[HeaderId] [int] NOT NULL,
		[CategoryId] [int] NOT NULL,
		[Key] [nvarchar](255) NOT NULL,
		[Value] [nvarchar](max) NOT NULL,
		[ModifiedDateTime] [datetime] NOT NULL,
	 CONSTRAINT [PK_Configuration] PRIMARY KEY CLUSTERED 
	(
		[Id] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 100) ON [PRIMARY],
	 CONSTRAINT [UQ_codes] UNIQUE NONCLUSTERED 
	(
		[HeaderId] ASC,
		[CategoryId] ASC,
		[Key] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 100) ON [PRIMARY]
	) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

	ALTER TABLE [dbo].[ConfigurationDetail]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationDetail_ConfigurationCategory]
	FOREIGN KEY([CategoryId]) REFERENCES [dbo].[ConfigurationCategory] ([Id])

	ALTER TABLE [dbo].[ConfigurationDetail] CHECK CONSTRAINT [FK_ConfigurationDetail_ConfigurationCategory]

	ALTER TABLE [dbo].[ConfigurationDetail]  WITH CHECK ADD  CONSTRAINT [FK_ConfigurationDetail_ConfigurationHeader]
	FOREIGN KEY([HeaderId]) REFERENCES [dbo].[ConfigurationHeader] ([id])

	ALTER TABLE [dbo].[ConfigurationDetail] CHECK CONSTRAINT [FK_ConfigurationDetail_ConfigurationHeader]
END
GO

PRINT N'Creating Procedure [dbo].[SetConfigValue]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('SetConfigValue') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE SetConfigValue
END

GO 

CREATE PROCEDURE [dbo].SetConfigValue
  @headerId int,
  @categoryId int,
  @key VARCHAR(255),
  @value VARCHAR(255) AS
	BEGIN TRY
		BEGIN TRANSACTION
			UPDATE dbo.[ConfigurationDetail] WITH (UPDLOCK, SERIALIZABLE) 
			SET [Value] = @value,
				[ModifiedDateTime] = GETDATE()
			WHERE HeaderId = @headerId
				AND CategoryId = @categoryId 
				AND [KEY] = @key
 
			IF @@ROWCOUNT = 0
			BEGIN
				INSERT [ConfigurationDetail] ([HeaderId],[CategoryId],[Key],[Value],[ModifiedDateTime]) 
				VALUES (@headerId, @categoryId, @key, @value,  GETDATE())
			END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW
	END CATCH
GO

PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN
 
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND [Key] = 'TTQ')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ 
	FROM SiteInfo
END
GO

PRINT N'Creating Procedure [dbo].[GetConfigurationTypes]...';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetConfigurationTypes') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetConfigurationTypes
END
GO

CREATE PROCEDURE GetConfigurationTypes AS
BEGIN 
	SELECT [Id], [Type]
	FROM ConfigurationType

	SELECT [Id], [Category]
	FROM ConfigurationCategory

	SELECT [Id], [TypeId], [Description]
	FROM ConfigurationHeader
END

GO

PRINT N'Creating GetConfigurationTypes Permission...';

GO
GRANT EXECUTE
    ON OBJECT::[dbo].[GetConfigurationTypes] TO [UserRole]
    AS [dbo];
GO

PRINT N'001_Upgrade_contactless_install...End';
GO