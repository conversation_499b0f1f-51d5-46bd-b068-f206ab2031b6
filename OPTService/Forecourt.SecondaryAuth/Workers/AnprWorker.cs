using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.SecondaryAuth.HydraDb.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Linq;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.SecondaryAuth.Workers.Interfaces.ITelemetryWorker;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <inheritdoc />
    public class AnprWorker : ClientWorker<string, IHydraDb, ITelemetryWorker>, IAnprWorker
    {
        private readonly IPumpCollection _allPumps;
        private INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        /// <summary>Constructor for ANPR Worker.</summary>
        /// <param name="telemetryWorker">Telemetry Worker for this class.</param>
        /// <param name="hydraDb">Hydra Database.</param>
        /// <param name="secAuthInWorker">Pump Worker to call.</param>
        /// <param name="allPumps">Collection of all pumps.</param>
        /// <param name="logger">IHtecLogger for this class.</param>
        /// <param name="connectionThread">ConnectionThread instance</param>
        public AnprWorker(ITelemetryWorker telemetryWorker, IHydraDb hydraDb, ISecAuthIntegratorInTransient<IMessageTracking> secAuthInWorker, IPumpCollection allPumps,
            IHtecLogger logger, IClientConnectionThread<string> connectionThread) : base(logger, telemetryWorker, connectionThread, hydraDb)
        {
            _allPumps = allPumps;

            RegisterWorker(secAuthInWorker ?? throw new ArgumentNullException(nameof(secAuthInWorker)));
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            return HydraDb.FetchAnprEndPoint();
        }

        /// <inheritdoc />
        public Result<string> SendSecondaryAuthRequest(IPump pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = Result.Success(string.Empty);

            return DoAction(() =>
            {
                var logRef = message.IdAsString;

                if (pump != null && !pump.IsSecAuthRequested)
                {
                    DoDeferredLogging(LogLevel.Info, "Pump", () => new[] { $"{pump.Number}" });
                    if (!IsConnected())
                    {
                        return Result.Success(" (pseudo)");
                    }
                    else
                    {
                        SendRequest($"GetReg={pump.Number}/", message);
                        return result;
                    }
                }

                return result;
            }, message.FullId);
        }        

        /// <summary>
        /// Called when text message is received from ANPR socket.
        /// </summary>
        /// <param name="message">Message received.</param>
        /// <returns>Null</returns>
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message)
        {
            var request = message.Request;

            var messages = GetMessagesFromMessage(request, message);
            if (messages.Any())
            {
                foreach (var thisMessage in messages)
                {
                    ProcessMessage(thisMessage, message);
                }
            }

            return null;
        }

        private IList<string> GetMessagesFromMessage(string message, IMessageTracking messageTracking)
        {
            var messages = new List<string>();
            var logRefFull = messageTracking.FullId;

            DoAction(() =>
            {
                while (message.Contains("REG=") || message.Contains("DET="))
                {
                    var regStart = message.IndexOf("REG=", StringComparison.InvariantCulture);
                    var detStart = message.IndexOf("DET=", StringComparison.InvariantCulture);
                    var start = regStart < 0 || !(detStart < 0 || regStart < detStart) ? detStart : regStart;
                    if (start > 0)
                    {
                        DoDeferredLogging(LogLevel.Warn, "DiscardingCharacters", () => new[] {message}, reference: logRefFull);
                        message = message.Substring(start);
                    }

                    if (message.Contains(":"))
                    {
                        var thisMessage = message.Substring(0, message.IndexOf(":", StringComparison.InvariantCulture) + 1);
                        messages.Add(thisMessage);
                        DoDeferredLogging(LogLevel.Debug, "Message", () => new[] {thisMessage}, reference: logRefFull);
                        message = message.Substring(message.IndexOf(":", StringComparison.InvariantCulture) + 1);
                    }
                    else
                    {
                        DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] {$"No : found in {message}"});
                        message = string.Empty;
                    }
                }

                if (message.Length > 0)
                {
                    DoDeferredLogging(LogLevel.Warn, "DiscardingCharacters", () => new[] {message}, reference: logRefFull);
                }

            }, logRefFull);

            return messages;
        }

        private void ProcessReg(byte pump, string reg, bool ok, bool empty, IMessageTracking messageTracking)
        {
            var logRef = messageTracking.IdAsString;

            DoDeferredLogging(LogLevel.Info, "Parameters", () => new[]
            {
                $"ANPR REG {(ok ? "OK" : "Reject")} message; Pump: {pump}{(empty ? string.Empty : $"; Reg: {reg}")}"
            }, reference: messageTracking.FullId);

            GetWorker<ISecAuthIntegratorInTransient<IMessageTracking>>()?.PreAuthReponse(
                new SecAuthResponse(messageTracking.ParentIdAsString, messageTracking.IdAsString, pump, reg, ok), messageTracking);         
        }

        private void ProcessMessage(string message, IMessageTracking messageTracking)
        {
            var parts = message.Split(',');
            if (parts.Length == 3 && (parts[0].StartsWith("REG=") || parts[0].StartsWith("DET=")) &&
                byte.TryParse(parts[0].Substring(4), out var pump))
            {
                var isReg = parts[0].StartsWith("REG=");
                var failed = parts[1].Equals("-FAILED-");
                var empty = parts[1].Length == 0;
                var ok = parts[2].Equals("OK:");
                var reg = empty ? "Empty" : parts[1];

               ControllerWorker?.SendInformation(
                    $"ANPR {(isReg ? "Reg" : "Details")}, Pump is {pump}," +
                    $" {(failed ? "ANPR Failed" : $"Reg is {reg}")}," + $" {(ok ? "OK" : "Reject")}");

                if (isReg)
                {
                    ProcessReg(pump, parts[1], ok, empty, messageTracking);
                }
            }
            else
            {
                DoDeferredLogging(LogLevel.Warn, "InvalidMessage", () => new[] {message}, reference: messageTracking.FullId);
            }

        }
    }
}
