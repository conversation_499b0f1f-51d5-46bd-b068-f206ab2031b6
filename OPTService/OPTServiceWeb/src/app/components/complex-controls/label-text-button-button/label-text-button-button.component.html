<div class="row form-group" [formGroup]="form">
    <div class="input-group" [ngClass]="labelColClass">
        <label class="col-form-label text-truncate" [for]="id" [title]="labelText">{{labelText}}</label>
    </div>
    <div class="input-group" [ngClass]="textColClass">
        <input type="text" class="form-control" [id]="id" [formControl]="control"
            [ngClass]="{'is-invalid':control.invalid && (control.dirty || control.touched)}"
            [placeholder]="placeHolderText" [attr.aria-describedby]="fieldFeedbackId" [maxlength]="maxLength"
            [readonly]="disabled">
        <div class="input-group-append" *ngIf="appendText">
            <span class="input-group-text">{{appendText}}</span>
        </div>
        <div [id]="fieldFeedbackId" class="invalid-feedback">
            <app-input-error-messages [control]="control"></app-input-error-messages>
        </div>
    </div>
    <div [ngClass]="buttonColClass">
        <button type="button" [id]="primaryButtonId" class="btn btn-primary" (click)="doPrimaryAction($event)"
            [disabled]="control.invalid || disabled">{{primaryButtonText}}</button>
        <button type="button" [id]="secondaryButtonId" class="btn ml-1" [ngClass]="secondaryButtonClass"
            (click)="doSecondaryAction($event)">{{secondaryButtonText}}</button>
        <span [id]="errorId" class="text-danger ml-2" placement="right top" [ngbTooltip]="errorInActionText" *ngIf="errorInAction"><i
                class="bi bi-exclamation-triangle"></i></span>
    </div>
</div>