using FluentAssertions;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Htec.DapperWrapper.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using System.Collections.Generic;
using System.IO.Abstractions;
using Xunit;

namespace OPT.Common.Tests.ESocketDbClasses
{
    public class EsocketDbTests
    {
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logMan;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IDbExecutorFactory _dbExecutorFactory;
        private readonly IFileSystem _fileSystem;
        private readonly ICustomDataFields _customDataOverride;

        public EsocketDbTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _logMan = Substitute.For<IHtecLogManager>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _dbExecutorFactory = Substitute.For<IDbExecutorFactory>();
            _fileSystem = Substitute.For<IFileSystem>();
            _customDataOverride = Substitute.For<ICustomDataFields>();
        }

        #region Get Capabilities

        [Fact]
        public void get_capabilities_null_query_result_uses_default_capability_values()
        {
            // Arrange
            var esocketDb = new EsocketDb(_logMan, _telemetryWorker, _fileSystem, _customDataOverride);
            esocketDb.SetDbExecutor(_dbExecutorFactory);

            // Act
            esocketDb.GetCapabilities();

            // Assert

            esocketDb.TermCapabilities.Should().BeNull();
            esocketDb.TermAddCapabilities.Should().BeNull();
        }

        [Fact]
        public void get_capabilities_explicit_null_query_result_uses_default_capability_values()
        {
            // Arrange
            var dbExecutor = Substitute.For<IDbExecutor>();
            dbExecutor.Query<Capabilities>(default, default, default, default, default, default)
                .ReturnsNull();

            _dbExecutorFactory.CreateExecutor()
                .Returns(dbExecutor);

            var esocketDb = new EsocketDb(_logMan, _telemetryWorker, _fileSystem, _customDataOverride);
            esocketDb.SetDbExecutor(_dbExecutorFactory);

            // Act
            esocketDb.GetCapabilities();

            // Assert
            esocketDb.TermCapabilities.Should().BeNull();
            esocketDb.TermAddCapabilities.Should().BeNull();
        }

        [Fact]
        public void get_capabilities_valid_query_result_uses_correct_values()
        {
            // Arrange
            var dbExecutor = Substitute.For<IDbExecutor>();
            dbExecutor.Query<Capabilities>(default, default, default, default, default, default)
                .ReturnsForAnyArgs(new List<Capabilities>
                {
                    new Capabilities("1234", "4321")
                });

            _dbExecutorFactory.CreateExecutor()
                .Returns(dbExecutor);

            var esocketDb = new EsocketDb(_logMan, _telemetryWorker, _fileSystem, _customDataOverride);
            esocketDb.SetDbExecutor(_dbExecutorFactory);

            // Act
            esocketDb.GetCapabilities();

            // Assert
            esocketDb.TermCapabilities.Should().Be("1234");
            esocketDb.TermAddCapabilities.Should().Be("4321");
        }

        #endregion
    }
}
