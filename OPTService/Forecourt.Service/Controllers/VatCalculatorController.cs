using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Description;

namespace Forecourt.Service.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all Vat Calculator requests
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "Swagger")]
    [RoutePrefix("api/vat")]
    public class VatCalculatorController : LinkedLoggableApiController<IVatCalculator>
    {
        /// <inheritdoc/>
        public VatCalculatorController(IVatCalculator loggable, IConfigurationManager configurationManager = null) : base(loggable as Loggable, configurationManager)
        {
        }

        /// <summary>
        /// Request Vat/Net amounts, using internal Hydra <see cref="uint"/> and <see cref="float"/> values
        /// </summary>
        /// <param name="request"><see cref="VatCaluclatorRequestHydra"/> instance</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        [HttpPost, Route("CalculateVat/hydra"), ResponseType(typeof(VatCaluclatorResponseHydra))]
        public async Task<IHttpActionResult> CalculateVatHydra([FromBody] VatCaluclatorRequestHydra request)
        {
            var result = Loggable.CalculateVat(request.GrossAmount, request.VatRate, request.DecimalPlaces);

            return Ok(new VatCaluclatorResponseHydra()
            {
                GrossAmount = request.GrossAmount,
                VatRate = request.VatRate,
                DecimalPlaces = request.DecimalPlaces,
                VatAmount = result.Item1,
                NetAmount = result.Item2
            });
        }

        /// <summary>
        /// Request Vat/Net amounts, using <see cref="double"/> values
        /// </summary>
        /// <param name="request"><see cref="VatCaluclatorRequestDouble"/> instance</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        [HttpPost, Route("CalculateVat/double"), ResponseType(typeof(VatCaluclatorResponseDouble))]
        public async Task<IHttpActionResult> CalculateVatDouble([FromBody] VatCaluclatorRequestDouble request)
        {
            var result = Loggable.CalculateVat(request.GrossAmount, request.VatRate, request.DecimalPlaces);

            return Ok(new VatCaluclatorResponseDouble()
            {
                GrossAmount = request.GrossAmount,
                VatRate = request.VatRate,
                DecimalPlaces = request.DecimalPlaces,
                VatAmount = result.Item1,
                NetAmount = result.Item2
            });
        }

        public class VatCaluclatorRequestHydra : VatCaluclatorRequest<uint, float> { }

        public class VatCaluclatorRequestDouble : VatCaluclatorRequest<double, double> { }

        public class VatCaluclatorRequest<TAmount, TVatRate>
        {
            public TAmount GrossAmount { get; set; }

            public TVatRate VatRate { get; set; }

            public byte DecimalPlaces { get; set; } = 2;
        }

        public class VatCaluclatorResponseHydra : VatCaluclatorResponse<uint, float> { }

        public class VatCaluclatorResponseDouble : VatCaluclatorResponse<double, double> { }

        public class VatCaluclatorResponse<TAmount, TVatRate>: VatCaluclatorRequest<TAmount, TVatRate>
        {
            public TAmount NetAmount { get; set; }

            public TAmount VatAmount { get; set; }
        }
    }
}
