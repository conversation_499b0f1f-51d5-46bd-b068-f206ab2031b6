using System;
using System.Collections.Specialized;
using System.IO.Abstractions.TestingHelpers;
using System.Xml.Linq;
using FluentAssertions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Logger.log4net.Helpers;
using Htec.Logger.log4net.Helpers.Interfaces;
using Htec.Testing.Helpers;
using NSubstitute;
using OPT.Common.Helpers;
using Xunit;

namespace OPT.Common.Tests.Helpers
{
    public class LoggingHelperTests
    {
        private const string SumoLogicDisabled = "Sumo Logic logging now Disabled!";
        private readonly MockFileSystem _fileSystem;
        private readonly IHtecLogger _logger;
        private readonly IConfigurationManager _configurationManager;
        private readonly IFileSavingHelper _fileSavingHelper;
        private readonly string _logFileLocation;
        public LoggingHelperTests()
        {
            _fileSystem = new MockFileSystem();
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _fileSavingHelper = new FileSavingHelper(_logger, _fileSystem); // Using a real one as the tests might be a bit anemic otherwise
            _logFileLocation = _fileSystem.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, LoggingHelper.LogConfigFile);
        }

        #region Constructor
        
        [Fact]
        public void constructor_null_file_system_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LoggingHelper(null, _logger, _configurationManager, _fileSavingHelper);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "fileSystem");
        }

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LoggingHelper(_fileSystem, null, _configurationManager, _fileSavingHelper);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        [Fact]
        public void constructor_null_configuration_manager_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LoggingHelper(_fileSystem, _logger, null, _fileSavingHelper);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "configurationManager");
        }

        [Fact]
        public void constructor_null_file_saving_helper_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new LoggingHelper(_fileSystem, _logger, _configurationManager, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "fileSavingHelper");
        }

        #endregion

        #region Set Log File Location

        [Fact]
        public void set_log_file_location_appender_missing_file_element_does_not_change_file()
        {
            // Arrange
            var xmlConfig = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"File_OPTService\" />" +
                            "</root>" +
                            "<appender name=\"File_OPTService\" type=\"log4net.Appender.RollingFileAppender\">" +
                            "<preserveLogFileNameExtension value=\"true\" />" +
                            "<datePattern value=\"'-'yyyy-MM-dd_HH\" />" +
                            "<staticLogFileName value=\"true\" />" +
                            "<rollingStyle value=\"Date\" />" +
                            "<appendToFile value=\"true\" />" +
                            "<maxSizeRollBackups value=\"-1\" />" +
                            "<countDirection value=\"0\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<acceptOnMatch value=\"true\" />" +
                            "<levelMin value=\"DEBUG\" />" +
                            "<levelMax value=\"FATAL\" />" +
                            "</filter>" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<conversionPattern value=\"%date %property{SiteName} [%thread] %-5level %logger - %message%newline\" />" +
                            "</layout><lockingModel type=\"log4net.Appender.FileAppender+MinimalLock\" />" +
                            "</appender><logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "</logger>" +
                            "</log4net>";

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetLogFileLocation("File_OPTService", "opt.log");

            // Assert
            var file = _fileSystem.GetFile(_logFileLocation);
            file.TextContents.Should().Be(xmlConfig);
        }

        [Fact]
        public void set_log_file_location_appender_missing_file_value_element_does_not_change_file()
        {
            // Arrange
            var xmlConfig = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"File_OPTService\" />" +
                            "</root>" +
                            "<appender name=\"File_OPTService\" type=\"log4net.Appender.RollingFileAppender\">" +
                            "<file type=\"log4net.Util.PatternString\">" +
                            "</file>" +
                            "<preserveLogFileNameExtension value=\"true\" />" +
                            "<datePattern value=\"'-'yyyy-MM-dd_HH\" />" +
                            "<staticLogFileName value=\"true\" />" +
                            "<rollingStyle value=\"Date\" />" +
                            "<appendToFile value=\"true\" />" +
                            "<maxSizeRollBackups value=\"-1\" />" +
                            "<countDirection value=\"0\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<acceptOnMatch value=\"true\" />" +
                            "<levelMin value=\"DEBUG\" />" +
                            "<levelMax value=\"FATAL\" />" +
                            "</filter>" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<conversionPattern value=\"%date %property{SiteName} [%thread] %-5level %logger - %message%newline\" />" +
                            "</layout>" +
                            "<lockingModel type=\"log4net.Appender.FileAppender+MinimalLock\" />" +
                            "</appender>" +
                            "<logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "</logger>" +
                            "</log4net>";

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetLogFileLocation("File_OPTService", "opt.log");

            // Assert
            var file = _fileSystem.GetFile(_logFileLocation);
            file.TextContents.Should().Be(xmlConfig);
        }

        [Fact]
        public void set_log_file_location_appender_valid_config_updates_file()
        {
            // Arrange
            var xmlConfig = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"File_OPTService\" />" +
                            "</root>" +
                            "<appender name=\"File_OPTService\" type=\"log4net.Appender.RollingFileAppender\">" +
                            "<file type=\"log4net.Util.PatternString\">" +
                            "<conversionPattern value=\"C:\\OptServiceLogs\\OPTService.log\" />" +
                            "</file>" +
                            "<preserveLogFileNameExtension value=\"true\" />" +
                            "<datePattern value=\"'-'yyyy-MM-dd_HH\" />" +
                            "<staticLogFileName value=\"true\" />" +
                            "<rollingStyle value=\"Date\" />" +
                            "<appendToFile value=\"true\" />" +
                            "<maxSizeRollBackups value=\"-1\" />" +
                            "<countDirection value=\"0\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<acceptOnMatch value=\"true\" />" +
                            "<levelMin value=\"DEBUG\" />" +
                            "<levelMax value=\"FATAL\" />" +
                            "</filter>" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<conversionPattern value=\"%date %property{SiteName} [%thread] %-5level %logger - %message%newline\" />" +
                            "</layout>" +
                            "<lockingModel type=\"log4net.Appender.FileAppender+MinimalLock\" />" +
                            "</appender>" +
                            "<logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "</logger>" +
                            "</log4net>";

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetLogFileLocation("File_OPTService", "C:\\Logs\\OptService\\opt.log");

            // Assert
            var expected = "<log4net>" +
                           "<root>" +
                           "<level value=\"INFO\" />" +
                           "<appender-ref ref=\"File_OPTService\" />" +
                           "</root>" +
                           "<appender name=\"File_OPTService\" type=\"log4net.Appender.RollingFileAppender\">" +
                           "<file type=\"log4net.Util.PatternString\">" +
                           "<conversionPattern value=\"C:\\Logs\\OptService\\opt.log\" />" +
                           "</file>" +
                           "<preserveLogFileNameExtension value=\"true\" />" +
                           "<datePattern value=\"'-'yyyy-MM-dd_HH\" />" +
                           "<staticLogFileName value=\"true\" />" +
                           "<rollingStyle value=\"Date\" />" +
                           "<appendToFile value=\"true\" />" +
                           "<maxSizeRollBackups value=\"-1\" />" +
                           "<countDirection value=\"0\" />" +
                           "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                           "<acceptOnMatch value=\"true\" />" +
                           "<levelMin value=\"DEBUG\" />" +
                           "<levelMax value=\"FATAL\" />" +
                           "</filter>" +
                           "<layout type=\"log4net.Layout.PatternLayout\">" +
                           "<conversionPattern value=\"%date %property{SiteName} [%thread] %-5level %logger - %message%newline\" />" +
                           "</layout>" +
                           "<lockingModel type=\"log4net.Appender.FileAppender+MinimalLock\" />" +
                           "</appender>" +
                           "<logger name=\"OPTService\">" +
                           "<level value=\"INFO\" />" +
                           "</logger>" +
                           "</log4net>";

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        #endregion

        #region Set SumoLogic Logging

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_already_enabled_does_not_change_file()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceDisabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true);

            // Assert
            var file = _fileSystem.GetFile(_logFileLocation);
            file.TextContents.Should().Be(xmlConfig);
        }

        [Fact]
        public void set_sumologic_logging_disabled_when_already_disabled_does_not_change_file()
        {
            // Arrange
            var xmlConfig = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "</root>" +
                            "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                            "</layout>" +
                            "<Url value=\"http://localhost\" />" +
                            "<SourceCategory value=\"HydraOpt\" />" +
                            "<SourceHost value=\"HydraOpt\" />" +
                            "<SourceName value=\"OPTService\" />" +
                            "<ConnectionTimeout value=\"30000\" />" +
                            "<RetryInterval value=\"5000\" />" +
                            "<MessagesPerRequest value=\"10\" />" +
                            "<MaxFlushInterval value=\"10000\" />" +
                            "<FlushingAccuracy value=\"250\" />" +
                            "<MaxQueueSizeBytes value=\"500000\" />" +
                            "<UseConsoleLog value=\"false\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<param name=\"LevelMin\" value=\"INFO\" />" +
                            "<param name=\"LevelMax\" value=\"FATAL\" />" +
                            "</filter>" +
                            "</appender>" +
                            "<logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "</logger>" +
                            "</log4net>";

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(false);

            // Assert
            var file = _fileSystem.GetFile(_logFileLocation);
            file.TextContents.Should().Be(xmlConfig);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_disabled_changes_file()
        {
            // Arrange
            var xmlConfig = CreateXmlDisabledWithOptServiceDisabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true);

            // Assert
            var expected = CreateXmlEnabledWithOptServiceDisabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact]
        public void set_sumologic_logging_disabled_when_enabled_changes_file()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceDisabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(false);

            // Assert
            var expected = CreateXmlDisabledWithOptServiceDisabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_already_enabled_with_info_appender_does_not_change_file()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceEnabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { "Logging::SumoLogic:InfoLoggers", "OPTService" }
            });

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true, "OPTService");

            // Assert
            var file = _fileSystem.GetFile(_logFileLocation);
            file.TextContents.Should().Be(xmlConfig);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_disabled_with_info_appender_changes_file()
        {
            // Arrange
            var xmlConfig = CreateXmlDisabledWithOptServiceDisabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { "Logging::SumoLogic:InfoLoggers", "OPTService" }
            });

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true);

            // Assert
            var expected = CreateXmlEnabledWithOptServiceEnabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_disabled_with_info_appender_not_present_changes_file()
        {
            // Arrange
            var xmlConfig = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "</root>" +
                            "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                            "</layout>" +
                            "<Url value=\"http://localhost\" />" +
                            "<SourceCategory value=\"HydraOpt\" />" +
                            "<SourceHost value=\"HydraOpt\" />" +
                            "<SourceName value=\"OPTService\" />" +
                            "<ConnectionTimeout value=\"30000\" />" +
                            "<RetryInterval value=\"5000\" />" +
                            "<MessagesPerRequest value=\"10\" />" +
                            "<MaxFlushInterval value=\"10000\" />" +
                            "<FlushingAccuracy value=\"250\" />" +
                            "<MaxQueueSizeBytes value=\"500000\" />" +
                            "<UseConsoleLog value=\"false\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<param name=\"LevelMin\" value=\"INFO\" />" +
                            "<param name=\"LevelMax\" value=\"FATAL\" />" +
                            "</filter>" +
                            "</appender>" +
                            "</log4net>";

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { "Logging::SumoLogic:InfoLoggers", "OPTService" }
            });

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true);

            // Assert
            var expected = CreateXmlEnabledWithOptServiceEnabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact]
        public void set_sumologic_logging_disabled_when_enabled_with_info_appender_changes_file()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceEnabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            _configurationManager.AppSettings.Returns(new NameValueCollection
            {
                { "Logging::SumoLogic:InfoLoggers", "OPTService" }
            });

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(false);

            // Assert
            var expected = CreateXmlDisabledWithOptServiceDisabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_change_loggers_using_info_appender_changes_file()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceEnabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true, "Worker");

            // Assert
            var expected = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                            "</root>" +
                            "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                            "</layout>" +
                            "<Url value=\"http://localhost\" />" +
                            "<SourceCategory value=\"HydraOpt\" />" +
                            "<SourceHost value=\"HydraOpt\" />" +
                            "<SourceName value=\"OPTService\" />" +
                            "<ConnectionTimeout value=\"30000\" />" +
                            "<RetryInterval value=\"5000\" />" +
                            "<MessagesPerRequest value=\"10\" />" +
                            "<MaxFlushInterval value=\"10000\" />" +
                            "<FlushingAccuracy value=\"250\" />" +
                            "<MaxQueueSizeBytes value=\"500000\" />" +
                            "<UseConsoleLog value=\"false\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<param name=\"LevelMin\" value=\"INFO\" />" +
                            "<param name=\"LevelMax\" value=\"FATAL\" />" +
                            "</filter>" +
                            "</appender>" +
                            "<logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "</logger>" +
                            "<logger name=\"Worker\">" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"InfoSumoLogicAppender\" />" +
                            "</logger>" +
                            "</log4net>";

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_change_loggers_using_info_appender_changes_file1()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithOptServiceEnabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true, "OPTService,Worker");

            // Assert
            var expected = "<log4net>" +
                            "<root>" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                            "</root>" +
                            "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                            "<layout type=\"log4net.Layout.PatternLayout\">" +
                            "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                            "</layout>" +
                            "<Url value=\"http://localhost\" />" +
                            "<SourceCategory value=\"HydraOpt\" />" +
                            "<SourceHost value=\"HydraOpt\" />" +
                            "<SourceName value=\"OPTService\" />" +
                            "<ConnectionTimeout value=\"30000\" />" +
                            "<RetryInterval value=\"5000\" />" +
                            "<MessagesPerRequest value=\"10\" />" +
                            "<MaxFlushInterval value=\"10000\" />" +
                            "<FlushingAccuracy value=\"250\" />" +
                            "<MaxQueueSizeBytes value=\"500000\" />" +
                            "<UseConsoleLog value=\"false\" />" +
                            "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                            "<param name=\"LevelMin\" value=\"INFO\" />" +
                            "<param name=\"LevelMax\" value=\"FATAL\" />" +
                            "</filter>" +
                            "</appender>" +
                            "<logger name=\"OPTService\">" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"InfoSumoLogicAppender\" />" +
                            "</logger>" +
                            "<logger name=\"Worker\">" +
                            "<level value=\"INFO\" />" +
                            "<appender-ref ref=\"InfoSumoLogicAppender\" />" +
                            "</logger>" +
                            "</log4net>";

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact]
        public void set_sumologic_logging_disabled_when_enabled_removes_appender_to_external_logger()
        {
            // Arrange
            var xmlConfig = CreateXmlEnabledWithHscEnabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(false, externalLoggers: Constants.LoggingConstants.LoggerHsc);

            // Assert
            var expected = CreateXmlDisabledWithHscDisabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        [Fact(Skip = SumoLogicDisabled)]
        public void set_sumologic_logging_enabled_when_disabled_adds_appender_to_external_logger()
        {
            // Arrange
            var xmlConfig = CreateXmlDisabledWithHscDisabled();

            _fileSystem.AddFile(_logFileLocation, new MockFileData(xmlConfig));

            var loggingHelper = CreateDefaultLoggingHelper();

            // Act
            loggingHelper.SetSumoLogicLogging(true, externalLoggers: Constants.LoggingConstants.LoggerHsc);

            // Assert
            var expected = CreateXmlEnabledWithHscEnabled();

            var file = _fileSystem.GetFile(_logFileLocation);
            var fileContents = XElement.Parse(file.TextContents).ToString(SaveOptions.DisableFormatting);
            fileContents.Should().Be(expected);
        }

        #region XML Creation Helper Methods

        private static string CreateXmlDisabledWithHscDisabled()
        {
            return "<log4net>" +
                   "<root>" +
                   "<level value=\"INFO\" />" +
                   "</root>" +
                   "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                   "<layout type=\"log4net.Layout.PatternLayout\">" +
                   "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                   "</layout>" +
                   "<Url value=\"http://localhost\" />" +
                   "<SourceCategory value=\"HydraOpt\" />" +
                   "<SourceHost value=\"HydraOpt\" />" +
                   "<SourceName value=\"OPTService\" />" +
                   "<ConnectionTimeout value=\"30000\" />" +
                   "<RetryInterval value=\"5000\" />" +
                   "<MessagesPerRequest value=\"10\" />" +
                   "<MaxFlushInterval value=\"10000\" />" +
                   "<FlushingAccuracy value=\"250\" />" +
                   "<MaxQueueSizeBytes value=\"500000\" />" +
                   "<UseConsoleLog value=\"false\" />" +
                   "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                   "<param name=\"LevelMin\" value=\"INFO\" />" +
                   "<param name=\"LevelMax\" value=\"FATAL\" />" +
                   "</filter>" +
                   "</appender>" +
                   "<logger name=\"Hsc\">" +
                   "<level value=\"INFO\" />" +
                   "</logger>" +
                   "</log4net>";
        }

        private static string CreateXmlEnabledWithHscEnabled()
        {
            return "<log4net>" +
                   "<root>" +
                   "<level value=\"INFO\" />" +
                   "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                   "</root>" +
                   "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                   "<layout type=\"log4net.Layout.PatternLayout\">" +
                   "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                   "</layout>" +
                   "<Url value=\"http://localhost\" />" +
                   "<SourceCategory value=\"HydraOpt\" />" +
                   "<SourceHost value=\"HydraOpt\" />" +
                   "<SourceName value=\"OPTService\" />" +
                   "<ConnectionTimeout value=\"30000\" />" +
                   "<RetryInterval value=\"5000\" />" +
                   "<MessagesPerRequest value=\"10\" />" +
                   "<MaxFlushInterval value=\"10000\" />" +
                   "<FlushingAccuracy value=\"250\" />" +
                   "<MaxQueueSizeBytes value=\"500000\" />" +
                   "<UseConsoleLog value=\"false\" />" +
                   "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                   "<param name=\"LevelMin\" value=\"INFO\" />" +
                   "<param name=\"LevelMax\" value=\"FATAL\" />" +
                   "</filter>" +
                   "</appender>" +
                   "<logger name=\"Hsc\">" +
                   "<level value=\"INFO\" />" +
                   "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                   "</logger>" +
                   "</log4net>";
        }

        private static string CreateXmlDisabledWithOptServiceDisabled()
        {
            return "<log4net>" +
                   "<root>" +
                   "<level value=\"INFO\" />" +
                   "</root>" +
                   "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                   "<layout type=\"log4net.Layout.PatternLayout\">" +
                   "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                   "</layout>" +
                   "<Url value=\"http://localhost\" />" +
                   "<SourceCategory value=\"HydraOpt\" />" +
                   "<SourceHost value=\"HydraOpt\" />" +
                   "<SourceName value=\"OPTService\" />" +
                   "<ConnectionTimeout value=\"30000\" />" +
                   "<RetryInterval value=\"5000\" />" +
                   "<MessagesPerRequest value=\"10\" />" +
                   "<MaxFlushInterval value=\"10000\" />" +
                   "<FlushingAccuracy value=\"250\" />" +
                   "<MaxQueueSizeBytes value=\"500000\" />" +
                   "<UseConsoleLog value=\"false\" />" +
                   "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                   "<param name=\"LevelMin\" value=\"INFO\" />" +
                   "<param name=\"LevelMax\" value=\"FATAL\" />" +
                   "</filter>" +
                   "</appender>" +
                   "<logger name=\"OPTService\">" +
                   "<level value=\"INFO\" />" +
                   "</logger>" +
                   "</log4net>";
        }

        private static string CreateXmlEnabledWithOptServiceDisabled()
        {
            return "<log4net>" +
                   "<root>" +
                   "<level value=\"INFO\" />" +
                   "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                   "</root>" +
                   "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                   "<layout type=\"log4net.Layout.PatternLayout\">" +
                   "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                   "</layout>" +
                   "<Url value=\"http://localhost\" />" +
                   "<SourceCategory value=\"HydraOpt\" />" +
                   "<SourceHost value=\"HydraOpt\" />" +
                   "<SourceName value=\"OPTService\" />" +
                   "<ConnectionTimeout value=\"30000\" />" +
                   "<RetryInterval value=\"5000\" />" +
                   "<MessagesPerRequest value=\"10\" />" +
                   "<MaxFlushInterval value=\"10000\" />" +
                   "<FlushingAccuracy value=\"250\" />" +
                   "<MaxQueueSizeBytes value=\"500000\" />" +
                   "<UseConsoleLog value=\"false\" />" +
                   "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                   "<param name=\"LevelMin\" value=\"INFO\" />" +
                   "<param name=\"LevelMax\" value=\"FATAL\" />" +
                   "</filter>" +
                   "</appender>" +
                   "<logger name=\"OPTService\">" +
                   "<level value=\"INFO\" />" +
                   "</logger>" +
                   "</log4net>";
        }

        private static string CreateXmlEnabledWithOptServiceEnabled()
        {
            return "<log4net>" +
                   "<root>" +
                   "<level value=\"INFO\" />" +
                   "<appender-ref ref=\"BufferedSumoLogicAppender\" />" +
                   "</root>" +
                   "<appender name=\"BufferedSumoLogicAppender\" type=\"SumoLogic.Logging.Log4Net.BufferedSumoLogicAppender, SumoLogic.Logging.Log4Net\">" +
                   "<layout type=\"log4net.Layout.PatternLayout\">" +
                   "<param name=\"ConversionPattern\" value=\"%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n\" />" +
                   "</layout>" +
                   "<Url value=\"http://localhost\" />" +
                   "<SourceCategory value=\"HydraOpt\" />" +
                   "<SourceHost value=\"HydraOpt\" />" +
                   "<SourceName value=\"OPTService\" />" +
                   "<ConnectionTimeout value=\"30000\" />" +
                   "<RetryInterval value=\"5000\" />" +
                   "<MessagesPerRequest value=\"10\" />" +
                   "<MaxFlushInterval value=\"10000\" />" +
                   "<FlushingAccuracy value=\"250\" />" +
                   "<MaxQueueSizeBytes value=\"500000\" />" +
                   "<UseConsoleLog value=\"false\" />" +
                   "<filter type=\"log4net.Filter.LevelRangeFilter\">" +
                   "<param name=\"LevelMin\" value=\"INFO\" />" +
                   "<param name=\"LevelMax\" value=\"FATAL\" />" +
                   "</filter>" +
                   "</appender>" +
                   "<logger name=\"OPTService\">" +
                   "<level value=\"INFO\" />" +
                   "<appender-ref ref=\"InfoSumoLogicAppender\" />" +
                   "</logger>" +
                   "</log4net>";
        }

        #endregion

        #endregion

        #region Helper Methods

        private ILoggingHelper CreateDefaultLoggingHelper()
        {
            return new LoggingHelper(_fileSystem, _logger, _configurationManager, _fileSavingHelper);
        }


        #endregion
    }
}
