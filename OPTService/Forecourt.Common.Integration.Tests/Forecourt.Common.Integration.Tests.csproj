<Project Sdk="Microsoft.NET.Sdk">
	
	<PropertyGroup>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<IsPackable>false</IsPackable>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Htec.DapperWrapper" Version="2.1.0" />
    <PackageReference Include="Htec.Hydra.Messages.Opt" Version="2.7.0" />
    <PackageReference Include="Htec.Logger.Interfaces" Version="5.0.0" />
    <PackageReference Include="Htec.SiteController" Version="4.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.3" />
    <PackageReference Include="Htec.Common.Abstractions" Version="3.0.0" />
    <PackageReference Include="Htec.Testing.Helpers" Version="3.1.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.extensibility.core" Version="2.4.2" />
    <PackageReference Include="xunit.runner.console" Version="2.4.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Forecourt.Common\Forecourt.Common.csproj" />
    <ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
  </ItemGroup>

</Project>

