USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[SetReceiptFooter]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE [<PERSON><PERSON><PERSON><PERSON><PERSON>Footer]
END
GO

CREATE PROCEDURE [dbo].[<PERSON>R<PERSON><PERSON><PERSON>Footer]
 @opt VARCHAR(1024),
 @receiptFooter VARCHAR(MAX) AS
 BEGIN
  IF (@opt IS NOT NULL AND LTRIM(@opt) != '')
   BEGIN
    BEGIN TRY
     BEGIN TRANSACTION
     IF(EXISTS (SELECT * FROM OPTMode WHERE OPT = @opt))
      BEGIN
       UPDATE OPTMode SET ReceiptFooter = @receiptFooter WHERE OPT = @opt
      END
     ELSE
      BEGIN
       INSERT OPTMode (OPT, ReceiptFooter) VALUES (@opt, @receiptFooter)
      END
     DELETE FROM OPTMode WHERE
      Contactless = 0 and
      (ReceiptHeader IS NULL OR LTRIM(ReceiptHeader) = '') AND
	  (ReceiptFooter IS NULL OR LTRIM(ReceiptFooter) = '') AND
      (PlaylistFileName IS NULL OR LTRIM(PlaylistFileName) = '') AND
      (LastLogTime IS NULL OR DATEADD(DAY, 1, LastLogTime) < SYSDATETIME())
     COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
     ROLLBACK TRANSACTION;
     THROW
    END CATCH
   END
 END
GO

GRANT EXECUTE ON SetReceiptFooter TO UserRole
GO
