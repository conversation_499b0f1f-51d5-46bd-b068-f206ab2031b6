using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Extensions;
using Forecourt.Core.Helpers;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Extensions;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.PaymentConfiguration.Extensions;
using Forecourt.Pos.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Hydra.Messages.Opt.Extensions;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Timers;
using bosCore = Htec.Hydra.Core.Bos.Interfaces.Core;
using bosMessage = Htec.Hydra.Core.Bos.Messages;
using CardAmountItem = Htec.Hydra.Core.Bos.Messages.Hydra.CardAmountItem;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using DiscountItem = Htec.Hydra.Core.Bos.Messages.DiscountItem;
using HscMeterReadings = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.MeterReadings;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using HscPumpState = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpState;
using HscPumpTotals = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpTotals;
using IHydraDb = Forecourt.Common.HydraDbClasses.IHydraDb;
using ITankGaugeIntegratorInJournal = Htec.Hydra.Core.Pump.Interfaces.ITankGaugeIntegratorInJournal;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using NewDip = Htec.Hydra.Core.Pump.Messages.Dip;
using optModels = Htec.Hydra.Messages.Opt.Models;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;
using ProductItem = OPT.Common.HydraOPT.ProductItem;
using ReceiptTransaction = Htec.Hydra.Messages.Opt.Models.ReceiptTransaction;
using SendTransactionItem = Htec.Hydra.Core.Bos.Messages.SendTransactionItem;
using TransactionItem = Htec.Hydra.Core.Bos.Messages.TransactionItem;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace OPT.Common.Workers
{
    public class JournalWorker : Connectable, IJournalWorker
    {
        private readonly IShiftDayEndConfig _shiftDayEndConfig;

        private const string NoAcquirerString = "Unknown Card Type";
        private const string UnknownCategory = "0";
        private const string UnmannedReposName = "UnmannedRepos";
        private const string UnmannedJournalName = "UnmannedJournal";
        private const string ReceiptFileName = "Receipt";
        private const string PrtExtension = ".prt";
        private const string DashLine = "--------------------------------------";
        //private const int PosReceiptWidth = 38;
        private const string GradeFormat = "FUEL{0}";

        private const string _tillUser = "1"; // TODO: Make configurable in HOPT-1620

        private const string MerchantCopy = "Text=\"Merchant Copy\"";
        private const string CustomerCopy = OPT.Common.ConfigConstants.CustomerCopy;

        private readonly IHtecLogger _journal;

        private IFromOptWorker OptWorker => GetWorker<IFromOptWorker>();
        private Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<EventType> NotificationWorker => GetWorker<Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<EventType>>();
        private IPumpIntegratorInJournal PumpWorker => GetWorker<IPumpIntegratorInJournal>();
        private ITankGaugeIntegratorInJournal TankGaugeWorker => GetWorker<ITankGaugeIntegratorInJournal>();
        private IBosIntegratorOut<IMessageTracking> BosOutWorker => GetWorker<IBosIntegratorOut<IMessageTracking>>();

        private bool _checkShiftEnd = true;
        public DateTime ShiftEndTime { get; private set; }
        public DateTime DayEndTime { get; private set; }
        public IList<CardReference> CardReferences { get; private set; }
        private int _shiftEndTransaction = 0;
        private int _shiftEndShiftNumber = 0;
        public short TillNumber { get; private set; }
        public short FuelCategory { get; private set; }

        public PrinterConfig PrinterConfig => new PrinterConfig(_printerEnabled, _printerPortName, _printerBaudRate,
            _printerHandshake.ToString(), _printerStopBits.ToString(), _printerDataBits);

        private bool _printerEnabled = false;
        private string _printerPortName;
        private int _printerBaudRate;
        private Handshake _printerHandshake;
        private int _printerDataBits;
        private StopBits _printerStopBits;

        private string _unmannedJournalFile = "";

        private DateTime _currentDipsTimeout;
        private DateTime _currentMetersTimeout;
        private bool _dipsPending = false;
        private bool _metersPending = false;
        private bool _shiftEndPending = false;
        private bool _shiftEndProcessing = false;
        private bool _dayEndPending = false;

        private class TankInfo
        {
            public string Name { get; set; }
            public ushort Price { get; set; }
        }

        private readonly IDictionary<int, NewDip> _currentDips = new ConcurrentDictionary<int, NewDip>();
        private readonly IDictionary<byte, HscMeterReadings> _currentMeters = new ConcurrentDictionary<byte, HscMeterReadings>();
        private readonly IDictionary<byte, TankInfo> _currentTankInfo = new ConcurrentDictionary<byte, TankInfo>();
        private readonly IDictionary<byte, IDictionary<byte, byte>> _currentTankHose = new ConcurrentDictionary<byte, IDictionary<byte, byte>>();
        private readonly IDictionary<byte, MeterReading> _prevDayEndMeters = new ConcurrentDictionary<byte, MeterReading>();

        private bool _bitmapRead = false;

        private byte[] _logoUploadCommand;

        private bool _printerBusy = false;
        private readonly object _printerBusyLock = new object();
        private readonly ILoggingHelper _loggingHelper;
        private readonly IPrinterHelper<IMessageTracking> _printerHelper;
        private readonly IOptCollection _allOpts;
        private readonly IPumpCollection _allPumps;
        private readonly IGradeHelper _gradeHelper;
        private readonly IReceiptHelper _receiptHelper;

        public uint ConfigValueMaxRetalixTransactionNumber => ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyMaxRetalixTransactionNumber, DefaultMaxRetalixTransactionNumber, LoggerIConfigurationManager);

        /// <inheritdoc/>
        public const string ConfigKeyMaxRetalixTransactionNumber = ConfigurationConstants.CategoryNamePOS + ConfigurationConstants.CategorySeparator + ConfigurationConstants.ConfigKeyMaxTransNumber;

        /// <inheritdoc/>
        public const uint DefaultMaxRetalixTransactionNumber = ConfigurationConstants.DefaultMaxTransactionNumber;

        /// <summary>
        /// Config key for, whether Pumps are create OnMaxPumps if internal list is Empty
        /// </summary>
        public const string ConfigKeyAutoCreatePumpsFromController = ConfigurationConstants.CategoryNamePump + ConfigurationConstants.CategorySeparator + "Pump:OnMaxPumps:AutoCreatePumps";

        /// <summary>
        /// Default value for, whether Pumps are create OnMaxPumps if internal list is Empty
        /// </summary>
        public const bool DefaultAutoCreatePumpsFromController = false;

        protected ConfigurableBool ConfigValueAutoCreatePumpsFromController { get; private set; }

        public bool IsPrinterBusy => GetPrinterBusy();

        private bool GetPrinterBusy()
        {
            lock (_printerBusyLock)
            {
                return _printerBusy;
            }
        }

        private bool TakePrinter()
        {
            lock (_printerBusyLock)
            {
                if (_printerBusy)
                {
                    return false;
                }

                _printerBusy = true;
                return true;
            }
        }

        private void FreePrinter()
        {
            lock (_printerBusyLock)
            {
                _printerBusy = false;
            }
        }

        public JournalWorker(IBosIntegratorOut<IMessageTracking> bosOutJournal, IHydraDb hydraDb,
            ITelemetryWorker telemetryWorker, IHtecLogger journal, IHtecLogManager logMan, ILoggingHelper loggingHelper, IConfigurationManager configurationManager,
            ITimerFactory timerFactory, IPumpIntegratorInJournal pumpInJournal, IPumpIntegratorInTransient<IMessageTracking> pumpInTransient, ITankGaugeIntegratorInJournal tankGaugeInJournal, IPrinterHelper<IMessageTracking> printerHelper,
            ILocalAccountWorker localAccountWorker, IOptCollection allOpts, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOutTransient,
            IPosIntegratorOutTransient<IMessageTracking> posOutTransient, IGradeHelper gradeHelper, IReceiptHelper receiptHelper, IPumpCollection allPumps,
            IShiftDayEndConfig shiftDayEndConfig, IPumpIntegratorConfiguration pumpConfig)
            : base(hydraDb, logMan, $"{ConfigConstants.LoggerPrefixWorker}Journal", configurationManager, nameof(JournalWorker), 0, timerFactory)
        {
            ConfigValueAutoCreatePumpsFromController = new ConfigurableBool(this, ConfigKeyAutoCreatePumpsFromController, DefaultAutoCreatePumpsFromController);

            _journal = journal;
            _loggingHelper = loggingHelper ?? throw new ArgumentNullException(nameof(loggingHelper));
            _printerHelper = printerHelper ?? throw new ArgumentNullException(nameof(printerHelper));
            _allOpts = allOpts ?? throw new ArgumentNullException(nameof(allOpts));
            _allPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));
            _gradeHelper = gradeHelper ?? throw new ArgumentNullException(nameof(gradeHelper));
            _receiptHelper = receiptHelper ?? throw new ArgumentNullException(nameof(receiptHelper));
            _shiftDayEndConfig = shiftDayEndConfig ?? throw new ArgumentNullException(nameof(shiftDayEndConfig));

            RegisterWorker(telemetryWorker ?? throw new ArgumentNullException(nameof(telemetryWorker)));
            RegisterWorker(bosOutJournal ?? throw new ArgumentNullException(nameof(bosOutJournal)));
            RegisterWorker(pumpInJournal ?? throw new ArgumentNullException(nameof(pumpInJournal)));
            RegisterWorker(pumpInTransient ?? throw new ArgumentNullException(nameof(pumpInTransient)));
            RegisterWorker(pumpConfig ?? throw new ArgumentNullException(nameof(pumpConfig)));
            RegisterWorker(tankGaugeInJournal ?? throw new ArgumentNullException(nameof(tankGaugeInJournal)));
            RegisterWorker(localAccountWorker ?? throw new ArgumentNullException(nameof(localAccountWorker)));
            RegisterWorker(secAuthOutTransient ?? throw new ArgumentNullException(nameof(secAuthOutTransient)));
            RegisterWorker(posOutTransient ?? throw new ArgumentNullException(nameof(posOutTransient)));

            ShiftEndTime = HydraDb.FetchShiftStart(false);
            DayEndTime = HydraDb.FetchShiftStart(true);
            CardReferences = HydraDb.FetchCardReferences();
            SiteInfo siteInfo = HydraDb.GetSiteInfo();
            TillNumber = siteInfo.TillNumber;
            FuelCategory = siteInfo.FuelCategory;
            PrinterConfig config = HydraDb.GetPrinterConfig();
            _printerEnabled = config?.Enabled ?? false;
            _printerPortName = config?.PortName ?? "COM1";
            _printerBaudRate = config?.BaudRate ?? 9600;
            _printerHandshake = config?.Handshake ?? Handshake.RequestToSend;
            _printerDataBits = config?.DataBits ?? 8;
            _printerStopBits = config?.StopBits ?? StopBits.One;
            foreach (var reading in HydraDb.FetchPreviousMeterReadings())
            {
                _prevDayEndMeters[reading.Pump] = reading;
            }
        }

        private static string CreatePrintString(IEnumerable<byte> bytes)
        {
            StringBuilder sb = new StringBuilder();
            foreach (byte b in bytes)
            {
                sb.Append(Convert.ToChar(b));
            }

            return sb.ToString();
        }

        private static byte[] CreateUploadLogoBytes(byte width, byte height, IEnumerable<byte> bytes)
        {
            List<byte> byteList = new List<byte> { 0x1D, 0x2A, width, height };
            byteList.AddRange(bytes);
            return byteList.ToArray();
        }

        /// <summary>Called once a second.</summary>
        /// <param name="sender">The timer.</param>
        /// <param name="e">The event.</param>
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            SetThreadName();

            var telemetryWorker = GetWorker<ITelemetryWorker>();
            DateTime now = DateTime.Now;
            if (_dipsPending && _currentDipsTimeout < now)
            {
                telemetryWorker.DipsTimedOut();
                _dipsPending = false;
                _currentDips.Clear();
            }

            if (_metersPending && _currentMetersTimeout < now)
            {
                telemetryWorker.MetersTimedOut();
                _metersPending = false;
                _currentMeters.Clear();
            }

            if (_shiftEndPending && !_dipsPending && !_metersPending)
            {
                var message = LoggingReference.ToMessageTracking();

                ShiftEnd(false, message);
                _shiftEndPending = false;
                _checkShiftEnd = false;
                if (_dayEndPending)
                {
                    ShiftEnd(true, message);
                    _dayEndPending = false;
                }
            }

            if (_printerEnabled && _bitmapRead && !_printerHelper.IsLogoUploaded && !IsPrinterBusy)
            {
                string result = SendToPrinter(_logoUploadCommand);
                if (result == null)
                {
                    _printerHelper.IsLogoUploaded = true;
                    DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Logo uploaded to printer" });

                }
                else
                {
                    DoDeferredLogging(LogLevel.Warn, HeaderParameters, () => new[] { $"Logo upload failed - {result}" });
                }
            }
        }

        private bool CardReferenceFuelCard(short cardRef)
        {
            return CardReferences.Any(x => x.CardRef == cardRef && x.FuelCard);
        }

        private void ShiftEndJournal
        (int shiftNumber, DateTime startTime, DateTime endTime, int firstTransaction, int lastTransaction, uint fuelAmount,
            uint dryAmount, IList<ItemSales> itemSales, IList<CardSales> cardSales, IList<CardVolumeSales> cardVolumeSales)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Preparing journal entry for shift end");
            StringBuilder entry = new StringBuilder();
            entry.AppendLine();
            entry.AppendLine($"OPT Shift Number: {(shiftNumber == 0 ? "TBD" : shiftNumber.ToString())}");

            entry.AppendLine($"Started at {startTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}");
            entry.AppendLine($"Finished at {endTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}");
            entry.AppendLine(DashLine);
            entry.AppendLine($"Transactions {firstTransaction} to {lastTransaction}");
            if (fuelAmount > 0)
            {
                entry.AppendLine();
                entry.AppendLine($"{"Total Fuel Sales",26} = £{fuelAmount / 100.0:F2}");
            }

            if (dryAmount > 0)
            {
                entry.AppendLine();
                entry.AppendLine($"{"Car Wash Sales",26} = £{dryAmount / 100.0:F2}");
            }

            entry.AppendLine();
            entry.AppendLine($"{"Total of Payments",26} = £{(fuelAmount + dryAmount) / 100.0:F2}");
            entry.AppendLine();


            if (itemSales.Any(x => x.Category == FuelCategory))
            {
                entry.AppendLine("Sales of fuel this shift:");
                entry.AppendLine($"{"Ltr.",8}{"VALUE",29}");
                foreach (ItemSales item in itemSales.Where(x => x.Category == FuelCategory))
                {
                    entry.AppendLine($"{item.Quantity / 1000.0,8:F2}{item.GradeName,21} £{item.Amount / 100.0,8:F2}");
                }

                entry.AppendLine();
            }

            if (itemSales.Any(x => x.Category != FuelCategory))
            {
                entry.AppendLine("Sales of car wash this shift:");
                entry.AppendLine($"{"Qty.",8}{"VALUE",29}");
                foreach (ItemSales item in itemSales.Where(x => x.Category != FuelCategory))
                {
                    entry.AppendLine($"{item.Quantity,8}{item.GradeName,21} £{item.Amount / 100.0,8:F2}");
                }

                entry.AppendLine();
            }


            if (cardSales.Any())
            {
                entry.AppendLine("Card Sales this shift:");
                foreach (CardSales item in cardSales)
                {
                    entry.AppendLine($"{item.ProductName,20} £{item.Amount / 100.0,12:F2}");
                }

                entry.AppendLine();
                entry.AppendLine($"{"TOTAL",13} £{(fuelAmount + dryAmount) / 100.00,12:F2}");
                entry.AppendLine();
            }

            if (cardVolumeSales.Any(x => CardReferenceFuelCard(x.CardRef)))
            {
                entry.AppendLine("Fuel Card Volumes:");
                short cardRef = 0;
                foreach (CardVolumeSales item in cardVolumeSales.Where(x => CardReferenceFuelCard(x.CardRef)).OrderBy(x => x.CardRef))
                {
                    if (item.CardRef != cardRef)
                    {
                        cardRef = item.CardRef;
                        entry.AppendLine($"{item.ProductName}:");
                    }

                    string gradeName = _gradeHelper.GetGradeName(item.Grade);
                    entry.AppendLine($"{gradeName,23} {item.Volume / 1000.0,9:F2} Ltr.");
                }

                entry.AppendLine();
            }

            entry.AppendLine(DashLine);
            entry.AppendLine();

            _journal.Info(entry.ToString());
            GetLogger().Info($"Journal entry is {entry}");

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void DayEndUnmannedJournal(DateTime time, IList<ItemSales> itemSales, IList<CardSales> cardSales, IEnumerable<Shift> shifts)
        {
            void UpdateTankSales(IDictionary<byte, uint> tankSales, byte pump, byte hose, HscPumpTotals totals, MeterReading prevTotals)
            {
                if (prevTotals?.Cash1 > 0 && prevTotals.Cash1 < totals.Cash && _currentTankHose.ContainsKey(pump) && _currentTankHose[pump].ContainsKey(hose))
                {
                    if (tankSales.ContainsKey(_currentTankHose[pump][hose]))
                    {
                        tankSales[_currentTankHose[pump][hose]] += totals.Cash - prevTotals.Cash1;
                    }
                    else
                    {
                        tankSales[_currentTankHose[pump][hose]] = totals.Cash - prevTotals.Cash1;
                    }
                }
            }

            DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Preparing unmanned journal entry for day end" });

            StringBuilder entry = new StringBuilder();
            entry.AppendLine($" {time:dd/MM/yyyy 'at' HH:mm}");
            entry.AppendLine();
            entry.AppendLine(" Shifts included in this day end");
            entry.AppendLine(DashLine);
            entry.AppendLine();
            foreach (Shift shift in shifts)
            {
                entry.AppendLine($" Shift {shift.ShiftNumber}");
                entry.AppendLine("From " + $"{shift.StartTime:hh:mm tt 'on' dd/MM/yyyy}".ToLower());
                entry.AppendLine(" until " + $"{shift.EndTime:hh:mm tt 'on' dd/MM/yyyy}".ToLower());
                entry.AppendLine();
                entry.AppendLine(DashLine);
            }

            entry.AppendLine(DashLine);
            entry.AppendLine($" {time:dd/MM/yyyy 'at' HH:mm}");
            entry.AppendLine();
            entry.AppendLine("END OF DAY REPORT");
            entry.AppendLine($"{time:dd/MM/yyyy HH:mm:ss}");
            entry.AppendLine();
            if (_currentDips.Any())
            {
                IDictionary<byte, uint> tankSales = new Dictionary<byte, uint>();
                if (_currentMeters.Any())
                {
                    foreach (var meter in _currentMeters.Values)
                    {
                        _prevDayEndMeters.TryGetValue(meter.Pump, out MeterReading prevMeter);
                        prevMeter ??= new MeterReading(meter.Pump, 0, 0, 0, 0, 0, 0, 0, 0);

                        UpdateTankSales(tankSales, meter.Pump, 1, meter.Meter1, prevMeter);
                        UpdateTankSales(tankSales, meter.Pump, 2, meter.Meter2, prevMeter);
                        UpdateTankSales(tankSales, meter.Pump, 3, meter.Meter3, prevMeter);
                        UpdateTankSales(tankSales, meter.Pump, 4, meter.Meter4, prevMeter);

                        // TODO: Make Six-Hose aware ADO #488369!!
                        //UpdateTankSales(tankSales, meter.Pump, 5, meter.Meter5, prevMeter);
                        //UpdateTankSales(tankSales, meter.Pump, 6, meter.Meter6, prevMeter);
                    }
                }

                entry.AppendLine("Volume Sales Report");
                entry.AppendLine($"{"Tnk",3} {"Grade",9} {"Dip",11} {"Tests",7} {"Sales",5}");
                foreach (var dip in _currentDips.Values)
                {
                    var gradeName = _currentTankInfo.TryGetValue((byte)dip.TankId, out var info) ? info.Name : ConfigConstants.Unknown;
                    uint sales = tankSales.TryGetValue((byte)dip.TankId, out uint value) ? value : 0;
                    entry.AppendLine($"{dip.TankId,2} {gradeName,15} {dip.DipLevel,6} {0,5} {sales,6}");
                }

                entry.AppendLine();
                entry.AppendLine("Stock Value");
                entry.AppendLine($"{"Tnk",3} {"Grade",5} {"Retail",14} {"Value",8}");
                int totalstock = 0;
                foreach (var dip in _currentDips.Values)
                {
                    var tankInfo = _currentTankInfo.TryGetValue((byte)dip.TankId, out var info) ? info : new TankInfo { Name = ConfigConstants.Unknown, Price = 0 };
                    entry.AppendLine($"{dip.TankId,2} {tankInfo.Name,15} {tankInfo.Price / 1000.0,5:N3} {dip.DipLevel * tankInfo.Price / 1000.0,10:N2}");
                    totalstock += dip.DipLevel * tankInfo.Price;
                }

                entry.AppendLine();
                entry.AppendLine($"Total Stock Value {totalstock / 1000.0,11:N2}");
                entry.AppendLine();
                entry.AppendLine(DashLine);
            }

            if (_currentMeters.Any())
            {
                entry.AppendLine();
                entry.AppendLine("Pump meters at end of Day:");
                entry.AppendLine($"{"Pump",4} {"Hose",4} {"Volume",14} {"Cash",12}");
                foreach (var kvp in _currentMeters)
                {
                    var meter = kvp.Value;
                    _prevDayEndMeters.TryGetValue(meter.Pump, out MeterReading prevMeter);
                    prevMeter ??= new MeterReading(meter.Pump, 0, 0, 0, 0, 0, 0, 0, 0);

                    if (meter.Meter1.Volume > 0)
                    {
                        entry.AppendLine($"{meter.Pump,3} {1,4} {meter.Meter1.Volume,15} {meter.Meter1.Cash,13}");
                        if (prevMeter?.Volume1 > 0 && prevMeter.Volume1 < meter.Meter1.Volume)
                        {
                            entry.AppendLine($"previous read {prevMeter.Volume1,10} {prevMeter.Cash1,13}");
                            entry.AppendLine(
                                $"Todays sales {(meter.Meter1.Volume - prevMeter.Volume1) / 1000.0,11:N2} {(meter.Meter1.Cash - prevMeter.Cash1) / 100.0,13:N2}");
                        }
                    }

                    if (meter.Meter2.Volume > 0)
                    {
                        entry.AppendLine($"{meter.Pump,3} {2,4} {meter.Meter2.Volume,15} {meter.Meter2.Cash,13}");
                        if (prevMeter?.Volume2 > 0 && prevMeter.Volume2 < meter.Meter2.Volume)
                        {
                            entry.AppendLine($"previous read {prevMeter.Volume2,10} {prevMeter.Cash2,13}");
                            entry.AppendLine(
                                $"Todays sales {(meter.Meter2.Volume - prevMeter.Volume2) / 1000.0,11:N2} {(meter.Meter2.Cash - prevMeter.Cash2) / 100.0,13:N2}");
                        }
                    }

                    if (meter.Meter3.Volume > 0)
                    {
                        entry.AppendLine($"{meter.Pump,3} {3,4} {meter.Meter3.Volume,15} {meter.Meter3.Cash,13}");
                        if (prevMeter?.Volume3 > 0 && prevMeter.Volume3 < meter.Meter3.Volume)
                        {
                            entry.AppendLine($"previous read {prevMeter.Volume3,10} {prevMeter.Cash3,13}");
                            entry.AppendLine(
                                $"Todays sales {(meter.Meter3.Volume - prevMeter.Volume3) / 1000.0,11:N2} {(meter.Meter3.Cash - prevMeter.Cash3) / 100.0,13:N2}");
                        }
                    }

                    if (meter.Meter4.Volume > 0)
                    {
                        entry.AppendLine($"{meter.Pump,3} {4,4} {meter.Meter4.Volume,15} {meter.Meter4.Cash,13}");
                        if (prevMeter?.Volume4 > 0 && prevMeter.Volume4 < meter.Meter4.Volume)
                        {
                            entry.AppendLine($"previous read {prevMeter.Volume4,10} {prevMeter.Cash4,13}");
                            entry.AppendLine(
                                $"Todays sales {(meter.Meter4.Volume - prevMeter.Volume4) / 1000.0,11:N2} {(meter.Meter4.Cash - prevMeter.Cash4) / 100.0,13:N2}");
                        }
                    }
                }

                entry.AppendLine();
                entry.AppendLine(DashLine);
            }

            entry.AppendLine($"{time:dd/MM/yyyy 'at' HH:mm}");
            entry.AppendLine();
            entry.AppendLine();
            entry.AppendLine("END OF DAY FOR OUTDOOR PAYMENT PUMPS");
            entry.AppendLine($"{time:dd/MM/yyyy HH:mm:ss}");
            uint totalFuel = 0;
            uint totalNonFuel = 0;
            if (itemSales.Any(x => x.Category == FuelCategory))
            {
                entry.AppendLine();
                entry.AppendLine("FUEL BREAKDOWN");
                foreach (ItemSales item in itemSales.Where(x => x.Category == FuelCategory))
                {
                    entry.AppendLine($"SALES OF {item.GradeName,10} £{item.Amount / 100.0:F2}");
                    totalFuel += item.Amount;
                }

                entry.AppendLine($"   TOTAL FUEL SALES £{totalFuel / 100.0:F2}");
            }

            if (itemSales.Any(x => x.Category != FuelCategory))
            {
                entry.AppendLine();
                entry.AppendLine("CREDIT CARD VALETING BREAKDOWN");
                foreach (ItemSales item in itemSales.Where(x => x.Category != FuelCategory))
                {
                    entry.AppendLine($"SALES OF {item.GradeName,10} £{item.Amount / 100.0:F2}");
                    totalNonFuel += item.Amount;
                }

                entry.AppendLine($"   TOTAL VALETING £{totalNonFuel / 100.0:F2}");
            }

            entry.AppendLine();
            entry.AppendLine($"   EXPECTED RECEIPTS £{(totalFuel + totalNonFuel) / 100.0:F2}");

            if (cardSales.Any())
            {
                IDictionary<string, uint> acquirers = new Dictionary<string, uint>();
                foreach (CardReference cardRef in CardReferences.Where(x => x.AcquirerName != null))
                {
                    acquirers[cardRef.AcquirerName] = 0;
                }

                uint noAcquirer = 0;

                foreach (CardSales item in cardSales)
                {
                    CardReference myCardRef = CardReferences.FirstOrDefault(x => x.CardRef == item.CardRef);
                    if (myCardRef?.AcquirerName == null)
                    {
                        noAcquirer += item.Amount;
                    }
                    else
                    {
                        if (acquirers.ContainsKey(myCardRef.AcquirerName))
                        {
                            acquirers[myCardRef.AcquirerName] += item.Amount;
                        }
                        else
                        {
                            acquirers[myCardRef.AcquirerName] = item.Amount;
                        }
                    }
                }

                if (acquirers.Keys.Any())
                {
                    entry.AppendLine();
                    entry.AppendLine("OUTDOOR PAYMENT EFTPOS BREAKDOWN");
                    uint amount = 0;
                    foreach (string acquirer in acquirers.Keys)
                    {
                        entry.AppendLine($"{acquirer,30} £{acquirers[acquirer] / 100.0,8:F2}");
                        amount += acquirers[acquirer];
                    }

                    if (noAcquirer > 0)
                    {
                        entry.AppendLine($"{NoAcquirerString,30} £{noAcquirer / 100.0,8:F2}");
                        amount += noAcquirer;
                    }

                    entry.AppendLine();
                    entry.AppendLine($"{"TOTAL",13} £{amount / 100.00,12:F2}");
                    entry.AppendLine();
                }
            }

            entry.AppendLine(DashLine);
            entry.AppendLine(DashLine);

            _loggingHelper.CheckUnmannedJournal(_unmannedJournalFile);
            _loggingHelper.WriteDayEndJournal(entry);

            DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Unmanned journal entry is {entry}" });

            if (_printerEnabled)
            {
                GetLogger().Info("Need to send journal entry to receipt printer");
                string result = SendToPrinter(entry.ToString().Replace("£", "GBP").getBytes());
                if (result != null)
                {
                    GetLogger().Error($"Unable to send journal to printer - {result}");
                }
            }
        }

        private string SendToPrinter(byte[] bytesToPrint)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            try
            {
                SerialPort sp = new SerialPort(_printerPortName)
                {
                    BaudRate = _printerBaudRate,
                    DataBits = _printerDataBits,
                    StopBits = _printerStopBits,
                    Handshake = _printerHandshake
                };
                if (TakePrinter())
                {
                    sp.Open();
                    GetLogger().Info("Opened serial port");
                    sp.Write(bytesToPrint, 0, bytesToPrint.Length);
                    GetLogger().Info("Message sent to serial port");
                    sp.Close();
                    FreePrinter();
                    GetLogger().Info("Closed serial port");
                    NotificationWorker?.SendInformation($"Message sent to serial port, bytes length {bytesToPrint.Length}");
                    NotificationWorker?.PushChange(EventType.ShiftEndChanged, EventType.TransactionsChanged);
                }
                else
                {
                    GetLogger().Error("Send to serial port failed - printer busy");
                    NotificationWorker?.SendInformation("Failed serial port - printer busy");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return "Failed Serial Port - printer busy";
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Send to serial port failed", e);
                NotificationWorker?.SendInformation("Failed serial port");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Failed Serial Port";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string PrintReceipt(long transactionNumber)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Printing receipt for transaction number {transactionNumber}");
            string receipt = HydraDb.GetReceipt(null, transactionNumber, true)?.ReceiptContent;
            if (string.IsNullOrEmpty(receipt))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Receipt Not Found";
            }

            if (!_printerEnabled)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Printer Not Enabled";
            }

            string receiptString = FormatReceipt(receipt, true).Replace("£", "GBP");
            GetLogger().Info($"Receipt is {_printerHelper.ReplaceCommandStrings(receiptString)}");
            NotificationWorker?.SendInformation($"Receipt is {_printerHelper.ReplaceCommandStrings(receiptString)}");

            GetLogger().Info("Need to send receipt to receipt printer");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return SendToPrinter(receiptString.getBytes());
        }

        /// <inheritdoc/>
        public string SaveReceipt(long transactionNumber)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Saving receipt for transaction number {transactionNumber}");
            string receipt = HydraDb.GetReceipt(null, transactionNumber, true)?.ReceiptContent;
            if (string.IsNullOrEmpty(receipt))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Receipt Not Found";
            }

            // ReSharper disable once RedundantArgumentDefaultValue
            string receiptString = FormatReceipt(receipt, false).Replace("£", "GBP");
            GetLogger().Info($"Receipt is {receiptString}");
            NotificationWorker?.SendInformation($"Receipt is {receiptString}");

            var resultFolder = BosOutWorker.GetTransactionFileFolder();
            string fileName = $"{resultFolder.Value}{ReceiptFileName}{transactionNumber}{PrtExtension}";
            try
            {
                using (TextWriter writer = new StreamWriter(fileName, true))
                {
                    writer.Write(receiptString);
                    writer.Close();
                }

                GetLogger().Info($"Receipt written to {fileName}");
                NotificationWorker?.SendInformation($"Receipt written to {fileName}");
            }
            catch (Exception e)
            {
                GetLogger().Error($"Error saving receipt to {fileName}", e);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string FormatReceipt(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null)
        {
            return _printerHelper.FormatReceipt(receipt, isPrint, useControlChars, message);
        }

        /// <inheritdoc />
        public void ReceiptJournal(string receipt, IMessageTracking message)
        {
            message ??= new MessageTracking();

            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, "Preparing", () => new[] { "Journal entry for receipt" });
                var receiptString = FormatReceipt(receipt, message: message);
                _journal.Info(Environment.NewLine + receiptString);
                DoDeferredLogging(LogLevel.Debug, "JournalEntry", () => new[] { $"{Environment.NewLine}{receiptString}" });
            }, message.FullId);
        }

        /// <inheritdoc cref="IJournalWorker.WriteSalesItems"/>
        public void WriteSalesItems
            (JournalTotalSalesItem total, IList<JournalFuelSalesItem> fuelSales, IList<JournalCarWashSalesItem> carWashSales,
            IList<JournalOtherSalesItem> otherSales, JournalDiscountItem discount, JournalLocalAccountItem localAccount, string txnNumber,
            out int transactionNumber,
            IMessageTracking message = null)
        {
            void ConvertAndAddItem(IList<bosMessage.TransactionItem> items, TransactionFileItem hydra, string reference)
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Hydra: {JsonConvert.SerializeObject(hydra)}" }, reference: reference);
                var bos = (bosMessage.TransactionItem)hydra;
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Bos: {JsonConvert.SerializeObject(bos)}" }, reference: reference);
                items.Add(bos);
            }

            message ??= new MessageTracking();
            var logRef = message.FullId;

            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            transactionNumber = 0;
            string receipt = "0";
            bool transactionNeeded = true;
            bool discountToDo = discount != null;

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderParameters, () => new[] {
                $"fuelSales: {string.Join("; ", fuelSales.Select(x => $"{JsonConvert.SerializeObject(x)}"))}",
                $"carWashSales: {string.Join("; ", carWashSales.Select(x => $"{JsonConvert.SerializeObject(x)}"))}",
                $"otherSales: {string.Join("; ", otherSales.Select(x => $"{JsonConvert.SerializeObject(x)}"))}"
            }, reference: logRef);

            DateTime now = DateTime.Now;
            IList<bosMessage.TransactionItem> transactionFileItems = new List<bosMessage.TransactionItem>();
            foreach (JournalFuelSalesItem item in fuelSales)
            {
                string transactionCode = string.Format(GradeFormat, item.Grade);
                string pumpDetails = $"PUMP {total.PumpNumber}:{total.Hose}";
                string category = $"{FuelCategory}";
                string subcategory = $"{item.Grade}";
                if (transactionNeeded)
                {
                    HydraDb.AddTransaction(item.GradeName, transactionCode, item.Quantity, item.Amount, pumpDetails, total.CardNumber, now,
                        category, subcategory, discount?.Name ?? string.Empty, discount?.Type ?? string.Empty, discount?.Value ?? 0,
                        discount?.CardNumber ?? string.Empty, localAccount?.Mileage ?? 0, localAccount?.Registration ?? string.Empty,
                        txnNumber ?? string.Empty, ConfigValueMaxRetalixTransactionNumber, out transactionNumber, message);
                    receipt = $"{transactionNumber}";
                    transactionNeeded = false;
                }
                else
                {
                    HydraDb.AddToTransaction(transactionNumber, item.GradeName, transactionCode, item.Quantity, item.Amount, pumpDetails,
                        total.CardNumber, now, category, subcategory, message);
                }

                HydraDb.AddDayEnd(item.Amount, 0, item.Quantity, transactionNumber, FuelCategory, item.Grade, transactionCode,
                    item.GradeName, total.CardProductName, discountToDo ? discount.Value : 0, message);
                discountToDo = false;
                ConvertAndAddItem(transactionFileItems, TransactionFileItem.ConstructSalesItem(TillNumber, receipt, item.GradeName, $"{item.Grade}",
                    item.Quantity, item.Amount, pumpDetails, now, category, subcategory), logRef);
            }

            foreach (JournalCarWashSalesItem item in carWashSales)
            {
                string transactionCode = $"CARWASH{item.ProgramId}";
                if (transactionNeeded)
                {
                    HydraDb.AddTransaction(item.WashName, transactionCode, item.Quantity, item.Amount, string.Empty, total.CardNumber, now,
                        item.Category.ToString(), item.Subcategory.ToString(), discount?.Name ?? string.Empty,
                        discount?.Type ?? string.Empty, discount?.Value ?? 0, discount?.CardNumber ?? string.Empty,
                        localAccount?.Mileage ?? 0, localAccount?.Registration ?? string.Empty, txnNumber ?? string.Empty, ConfigValueMaxRetalixTransactionNumber,
                        out transactionNumber, message);
                    receipt = $"{transactionNumber}";
                    transactionNeeded = false;
                }
                else
                {
                    HydraDb.AddToTransaction(transactionNumber, item.WashName, transactionCode, item.Quantity, item.Amount, string.Empty,
                        total.CardNumber, now, item.Category.ToString(), item.Subcategory.ToString(), message);
                }

                HydraDb.AddDayEnd(0, item.Amount, item.Quantity, transactionNumber, item.Category, item.Subcategory, transactionCode,
                    item.WashName, total.CardProductName, discountToDo ? discount.Value : 0, message);
                discountToDo = false;
                ConvertAndAddItem(transactionFileItems, TransactionFileItem.ConstructSalesItem(TillNumber, receipt, item.WashName, transactionCode,
                    item.Quantity * 1000, item.Amount, string.Empty, now, item.Category.ToString(), item.Subcategory.ToString()), logRef);
            }

            foreach (JournalOtherSalesItem item in otherSales)
            {
                string transactionCode = $"UNKNOWN{item.ProductCode}";
                string subcategory = $"{item.ProductCode}";
                if (transactionNeeded)
                {
                    HydraDb.AddTransaction("Unknown", transactionCode, item.Quantity, item.Amount, string.Empty, total.CardNumber, now,
                        UnknownCategory, subcategory, discount?.Name ?? string.Empty, discount?.Type ?? string.Empty, discount?.Value ?? 0,
                        discount?.CardNumber ?? string.Empty, localAccount?.Mileage ?? 0, localAccount?.Registration ?? string.Empty,
                        txnNumber ?? string.Empty, ConfigValueMaxRetalixTransactionNumber, out transactionNumber, message);
                    receipt = $"{transactionNumber}";
                    transactionNeeded = false;
                }
                else
                {
                    HydraDb.AddToTransaction(transactionNumber, string.Empty, transactionCode, item.Quantity, item.Amount, string.Empty,
                        total.CardNumber, now, UnknownCategory, subcategory, message);
                }

                HydraDb.AddDayEnd(0, item.Amount, item.Quantity, transactionNumber, 0, 0, transactionCode, string.Empty,
                    total.CardProductName, discountToDo ? discount.Value : 0, message);
                discountToDo = false;
                ConvertAndAddItem(transactionFileItems, TransactionFileItem.ConstructSalesItem(TillNumber, receipt, string.Empty, transactionCode,
                    item.Quantity * 1000, item.Amount, string.Empty, now, UnknownCategory, subcategory), logRef);
            }

            if (transactionNeeded)
            {
                HydraDb.AddTransaction(string.Empty, string.Empty, 0, total.Amount, string.Empty, total.CardNumber, now, null, null,
                    string.Empty, string.Empty, 0, string.Empty, 0, string.Empty, txnNumber ?? string.Empty, ConfigValueMaxRetalixTransactionNumber, out transactionNumber, message);
                _checkShiftEnd = true;
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            if (discount != null)
            {
                ConvertAndAddItem(transactionFileItems, TransactionFileItem.ConstructDiscount(TillNumber, receipt, discount.Name, discount.Type,
                    discount.Value, discount.CardNumber, now), logRef);
            }

            if (localAccount != null)
            {
                ConvertAndAddItem(transactionFileItems, new LocalAccountCardMethodOfPaymentItem(TillNumber, receipt, _tillUser, localAccount.Registration, localAccount.Mileage, total.Amount, total.CardNumber, now), logRef);
            }
            else
            {
                var refs = CardReferences.FirstOrDefault(x => x.CardProductName.Equals(total.CardProductName, StringComparison.OrdinalIgnoreCase));

                GetLogger().Warn(HeaderInformation, () => new[] { $"Card reference not found for {total.CardProductName}" });

                ConvertAndAddItem(transactionFileItems, new EftChipCardMethodOfPaymentItem(TillNumber, receipt, _tillUser, total.Amount, total.CardNumber, now, refs?.CardRef.ToString()), logRef);
            }

            var registration = GetRegistration(localAccount, total);
            var mileage = GetMileage(localAccount, total);

            if (!string.IsNullOrWhiteSpace(registration) || (mileage != null && mileage != 0))
            {
                ConvertAndAddItem(transactionFileItems, new RegistrationAndMileage(TillNumber, receipt, _tillUser, registration, mileage ?? 0, total.CardNumber, now), logRef);
            }

            ConvertAndAddItem(transactionFileItems, TransactionFileItem.ConstructEndOfSalesTransaction(TillNumber, receipt, now), logRef);

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderParameters, () => new[] {
                $"registration: {registration}; mileage: {mileage}",
                $"transactionFileItems: {string.Join("; ", transactionFileItems.Select(x => $"{JsonConvert.SerializeObject(x)}"))}"
            }, reference: logRef);

            BosOutWorker.WriteTransactionFile(transactionFileItems, now, null, message);

            CardReferences = HydraDb.FetchCardReferences(message);
            NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);

            _checkShiftEnd = true;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private static string GetRegistration(JournalLocalAccountItem localAccount, JournalTotalSalesItem total)
        {
            return localAccount?.Registration
                   ?? total.Registration;
        }

        private static uint? GetMileage(JournalLocalAccountItem localAccount, JournalTotalSalesItem total)
        {
            return localAccount?.Mileage
                   ?? total.Mileage;
        }

        private void ShiftEnd(bool isDayEnd, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                _shiftEndProcessing = true;
                try
                {
                    DoShiftEnd(isDayEnd, message);
                }
                catch (Exception e)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"ShiftEnd failed: isDayEnd: {isDayEnd}; _shiftEndPending: {_shiftEndPending}; _dayEndPending: {_dayEndPending}; Exception: {e.Message}" });
                }
                finally
                {
                    _shiftEndProcessing = false;
                    if (isDayEnd)
                    {
                        _dayEndPending = false;
                    }
                    else
                    {
                        _shiftEndPending = false;
                    }

                    DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"ShiftEnd Finished: isDayEnd: {isDayEnd}; _shiftEndPending: {_shiftEndPending}; _dayEndPending: {_dayEndPending}" });
                }
            }, message.FullId);
        }

        private void DoShiftEnd(bool isDayEnd, IMessageTracking message = null)
        {
            HydraDb.TakeDayEnd(isDayEnd, out uint fuelAmount, out uint dryAmount, out uint discount, out DateTime startTime,
                out DateTime endTime, out int firstTransaction, out int lastTransaction, out int shiftNumber);
            if (isDayEnd && shiftNumber != 0)
            {
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { "Day End shift number not zero" });
            }

            IEnumerable<Shift> shifts;
            if (isDayEnd)
            {
                shifts = new List<Shift>(HydraDb.TakeShiftList());
            }
            else
            {
                HydraDb.AddShiftToList(shiftNumber, startTime, endTime);
                shifts = new List<Shift>();
            }

            NotificationWorker?.SendInformation($"{(isDayEnd ? "Day" : "Shift")} End Processing");

            if (isDayEnd)
            {
                if (firstTransaction == 0)
                {
                    firstTransaction = _shiftEndTransaction;
                }

                if (_shiftEndTransaction > lastTransaction)
                {
                    lastTransaction = _shiftEndTransaction;
                }
            }
            else
            {
                HydraDb.AddEvent(endTime, out int transactionNumber);
                BosOutWorker.WriteTransactionFile(new[] { (bosMessage.TransactionItem)TransactionFileItem.ConstructShiftEnd(TillNumber, $"{transactionNumber}", endTime) }, endTime);

                if (firstTransaction == 0)
                {
                    firstTransaction = transactionNumber;
                }

                lastTransaction = transactionNumber;
                _shiftEndTransaction = transactionNumber;
                _shiftEndShiftNumber = shiftNumber;
            }

            IList<ItemSales> itemSales = HydraDb.TakeItemSales(isDayEnd);
            IList<CardSales> cardSales = HydraDb.TakeCardSales(isDayEnd);
            IList<CardVolumeSales> cardVolumeSales = HydraDb.TakeCardVolumeSales();

            IList<ItemSalesItem> itemSalesItems = new List<ItemSalesItem>();
            IList<CategorySalesItem> categorySalesItems = new List<CategorySalesItem>();
            IList<CardSalesItem> cardSalesItems = new List<CardSalesItem>();
            IList<CardVolumeSalesItem> cardVolumeSalesItems = new List<CardVolumeSalesItem>();
            IList<CardAmountItem> cardAmountItems = new List<CardAmountItem>();
            foreach (ItemSales item in itemSales)
            {
                itemSalesItems.Add(ItemSalesItem.ConstructSalesItem(TillNumber, item.GradeCode, item.Category, item.Subcategory, item.GradeName, item.Quantity, item.Amount, endTime));
                categorySalesItems.Add(CategorySalesItem.ConstructCategorySalesItem(TillNumber, item.Category, item.Subcategory, item.Amount, endTime));
            }

            foreach (CardSales item in cardSales)
            {
                cardSalesItems.Add(CardSalesItem.ConstructCardSalesItem(TillNumber, item.CardRef, item.Amount, endTime));
                cardAmountItems.Add(new CardAmountItem(item.CardRef, item.ProductName, item.Amount, endTime));
            }

            foreach (CardVolumeSales item in cardVolumeSales)
            {
                cardVolumeSalesItems.Add(CardVolumeSalesItem.ConstructCardVolumeSalesItem(item.CardRef, item.Grade, item.Volume, item.ProductName, endTime));
            }

            // TODO: Check data-types of shiftNumber, firstTransaction, lastTransaction across Hydra/Retalix
            var (bosShiftEnd, bosItemSales, bosCategorySales, bosCardSales, bosCardAmounts, bosCardVolumeSales) = (
                (bosMessage.ShiftEndItem)DayEndItem.ConstructDayEndItem(isDayEnd, TillNumber, startTime, endTime, (short)shiftNumber, fuelAmount, dryAmount, discount, (uint)firstTransaction, (uint)lastTransaction),
                itemSalesItems.Select(x => (bosMessage.ItemSalesItem)x), categorySalesItems.Select(x => (bosMessage.CategorySalesItem)x),
                cardSalesItems.Select(x => (bosMessage.CardSalesItem)x), cardAmountItems.Select(x => (bosMessage.CardAmountSalesItem)x), cardVolumeSalesItems.Select(x => (bosMessage.CardVolumeSalesItem)x));

            BosOutWorker.WriteShiftEndFiles(isDayEnd, startTime, endTime, bosShiftEnd, bosItemSales, bosCategorySales, bosCardSales, bosCardAmounts, bosCardVolumeSales);

            if (!isDayEnd)
            {
                ShiftEndJournal(shiftNumber, startTime, endTime, firstTransaction, lastTransaction, fuelAmount, dryAmount, itemSales, cardSales, cardVolumeSales);
            }

            if ((OptWorker?.UnmannedPseudoPos ?? false) && isDayEnd)
            {
                DayEndUnmannedJournal(endTime, itemSales, cardSales, shifts);
            }

            if (isDayEnd)
            {
                DayEndTime = endTime;
            }
            else
            {
                ShiftEndTime = endTime;
            }

            if (!isDayEnd)
            {
                BosOutWorker?.ShiftEndResponse(bosShiftEnd, bosItemSales, bosCardSales, bosCardVolumeSales, message);
            }
            else
            {
                uint total = 0;
                foreach (var item in cardAmountItems)
                {
                    total += item.Amount;
                }

                cardAmountItems.Add(new CardAmountItem(0, "Total", total));
                BosOutWorker?.DayEndResponse(bosShiftEnd, bosItemSales, bosCategorySales, bosCardSales, bosCardAmounts, bosCardVolumeSales, message);
            }

            CardReferences = HydraDb.FetchCardReferences();
            NotificationWorker?.PushChange(EventType.AdvancedConfigChanged, EventType.ShiftEndChanged);

            if (isDayEnd)
            {
                IList<MeterReading> readings = _currentMeters.Values
                    .Select(x => new MeterReading(x.Pump,
                                        x.Meter1.Volume, x.Meter1.Cash,
                                        x.Meter2.Volume, x.Meter2.Cash, 
                                        x.Meter3.Volume, x.Meter3.Cash, 
                                        x.Meter4.Volume, x.Meter4.Cash)).ToList();


                _prevDayEndMeters.Clear();

                foreach (var reading in readings)
                {
                    _prevDayEndMeters[reading.Pump] = reading;
                }

                HydraDb.StorePreviousMeterReadings(readings);

                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"DoShiftEnd - isDayEnd: MeterReading Count: {readings.Count}; " });
            }

            DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"DoShiftEnd Processed: isDayEnd: {isDayEnd}; _shiftEndPending: {_shiftEndPending}; _dayEndPending: {_dayEndPending}; _checkShiftEnd: {_checkShiftEnd} " });

            NotificationWorker?.SendInformation($"{(isDayEnd ? "Day" : "Shift")} End Complete");
        }

        /// <inheritdoc/>
        public Result RequestDayEnd(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() =>
            {

                if (_dayEndPending)
                {
                    return Result.Failure("DayEnd is pending");
                }

                DoDeferredLogging(LogLevel.Info, "IsProcessing", methodName: "DayEnd");

                if (_checkShiftEnd || DateTime.Now > ShiftEndTime.AddMinutes(_shiftDayEndConfig.MinimumIntervalFromShiftToDayEnd.TotalMinutes))
                {
                    RequestShiftEnd(message);
                    _dayEndPending = true;
                }
                else
                {
                    ShiftEnd(true, message);
                }

                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc/>
        public Result RequestShiftEnd(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() =>
            {
                var logRef = LoggingReference;

                if (_shiftEndPending)
                {
                    return Result.Failure("ShiftEnd is pending"); ;
                }

                if (_shiftEndProcessing)
                {
                    return Result.Failure("ShiftEnd is processing"); ;
                }

                DoDeferredLogging(LogLevel.Info, "IsProcessing", methodName: "ShiftEnd");

                if (TankGaugeWorker.RequestDips(logRef).IsSuccess)
                {
                    GetWorker<ITelemetryWorker>().DipsRequested();
                    _dipsPending = true;
                    _currentDipsTimeout = DateTime.Now.AddSeconds(_shiftDayEndConfig.ExpiryIntervalOverall.TotalSeconds);
                }

                if (PumpWorker.RequestMeters(logRef).IsSuccess)
                {
                    GetWorker<ITelemetryWorker>().MetersRequested();
                    _metersPending = true;
                    _currentMetersTimeout = DateTime.Now.AddSeconds(_shiftDayEndConfig.ExpiryIntervalOverall.TotalSeconds);
                }

                _shiftEndPending = true;

                return Result.Success();
            }, message.FullId);
        }

        public string SetTillNumber(short number)
        {
            DoAction(() =>
            {
                TillNumber = number;
                HydraDb.SetTillNumber(TillNumber);
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string SetFuelCategory(short category)
        {
            DoAction(() =>
            {
                FuelCategory = category;
                HydraDb.SetFuelCategory(FuelCategory);
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string SetCardReference(string name, int reference)
        {
            DoAction(() =>
            {
                HydraDb.SetCardReference(name, reference);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string ClearCardReference(string name)
        {
            DoAction(() =>
            {
                HydraDb.ClearCardReference(name);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string SetAcquirerReference(string cardName, string acquirerName)
        {
            DoAction(() =>
            {
                HydraDb.SetAcquirerReference(cardName, acquirerName);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            },string.Empty);

            return null;
        }

        public string ClearAcquirerReference(string cardName)
        {
            DoAction(() =>
            {
                HydraDb.ClearAcquirerReference(cardName);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            },string.Empty);

            return null;
        }

        public string SetFuelCard(string cardName, bool isFuelCard)
        {
            DoAction(() =>
            {
                HydraDb.SetFuelCard(cardName, isFuelCard);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string SetExternalName(string cardName, string externalCardName)
        {
            DoAction(() =>
            {
                HydraDb.SetExternalName(cardName, externalCardName);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        public string ClearExternalName(string cardName)
        {
            DoAction(() =>
            {
                HydraDb.ClearExternalName(cardName);
                CardReferences = HydraDb.FetchCardReferences();
                NotificationWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, string.Empty);

            return null;
        }

        private void ResetPrinter()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerEnabled && !IsPrinterBusy)
            {
                string result = SendToPrinter(_printerHelper.InitPrinter.getBytes());
                if (result == null)
                {
                    GetLogger().Info("Printer Reset");
                    _printerHelper.IsLogoUploaded = false;
                }
                else
                {
                    GetLogger().Warn($"Printer not reset - {result}");
                }
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public string SetPrinterEnabled(bool isEnabled)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerEnabled != isEnabled)
            {
                _printerEnabled = isEnabled;
                ResetPrinter();

                HydraDb.SetPrinterEnabled(isEnabled);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged, EventType.TransactionsChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPrinterPortName(string portName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerPortName != portName)
            {
                _printerPortName = portName;
                ResetPrinter();

                HydraDb.SetPrinterPortName(portName);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPrinterBaudRate(int baudRate)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerBaudRate != baudRate)
            {
                _printerBaudRate = baudRate;
                ResetPrinter();

                HydraDb.SetPrinterBaudRate(baudRate);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPrinterHandshake(Handshake handshake)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerHandshake != handshake)
            {
                _printerHandshake = handshake;
                ResetPrinter();

                HydraDb.SetPrinterHandshake(handshake);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPrinterStopBits(StopBits stopBits)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerStopBits != stopBits)
            {
                _printerStopBits = stopBits;
                ResetPrinter();

                HydraDb.SetPrinterStopBits(stopBits);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPrinterDataBits(int dataBits)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_printerDataBits != dataBits)
            {
                _printerDataBits = dataBits;
                ResetPrinter();

                HydraDb.SetPrinterDataBits(dataBits);
                NotificationWorker?.PushChange(EventType.ShiftEndChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        /// <inheritdoc />
        public void DipsReceived(IList<Dip> dips)
        {
        }

        /// <inheritdoc />
        public void OnDips(IEnumerable<NewDip> dips, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (_dipsPending)
                {
                    GetWorker<ITelemetryWorker>().DipsReceived();
                    _dipsPending = false;
                }

                _currentDips.Clear();
                foreach (var dip in dips)
                {
                    _currentDips[dip.TankId] = dip;
                }
            }, loggingReference);
        }

        /// <inheritdoc />
        public void MetersReceived(IList<HscMeterReadings> meters)
        {
        }

        /// <inheritdoc />
        public void OnMeters(IEnumerable<Htec.Hydra.Core.Pump.Messages.MeterReadings> meters, string loggingReference = null)
        {
            DoAction(() =>
            {
                DoDeferredLogging(Htec.Foundation.Core.LogLevel.Info, "Count", () => new[] { $"{meters.Count()}" });
                if (_metersPending)
                {
                    GetWorker<ITelemetryWorker>().MetersReceived();
                    _metersPending = false;
                }

                _currentMeters.Clear();
                foreach (var meter in meters)
                {
                    var hscMeter = (HscMeterReadings)meter;
                    _currentMeters[hscMeter.Pump] = hscMeter;
                }

            }, loggingReference);
        }

        private void SetTankData(byte pump, byte hose, byte tank, byte grade, ushort price, IDictionary<byte, IDictionary<byte, byte>> tankHoses)
        {
            if (tank > 0)
            {
                _currentTankInfo[tank] = new TankInfo { Name = _gradeHelper.GetGradeName(grade), Price = price };

                var dict = tankHoses.ContainsKey(pump) ? tankHoses[pump] : default;
                if (dict == null)
                {
                    dict = new ConcurrentDictionary<byte, byte>();
                    tankHoses[pump] = dict;
                }

                dict[hose] = tank;
            }
        }
        /// <inheritdoc />
        public void PumpDataReceived(HscPumpData pumpData, string loggingReference = null)
        {
        }

        /// <inheritdoc />
        public void OnPriceChange(Htec.Hydra.Core.Pump.Messages.PriceChange priceChange, IMessageTracking message = null)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, Pump.HeaderPump, () => new[] { $"{priceChange.Number}" });
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), Pump.HeaderPump, () => new[] { $"{priceChange.Number}; Json: {JsonConvert.SerializeObject(priceChange)}" });

                foreach (var hose in priceChange.Hoses)
                {
                    var totals = (HscPumpTotals)hose;
                    SetTankData(priceChange.Number, hose.Number, hose.TankNumber, totals.Grade, totals.Ppu, _currentTankHose);

                    if (PumpWorker.ShouldSynchroniseGrades)
                    {
                        var grade = _gradeHelper.GetGradeInfo(hose.GradeNumber);
                        if (hose.Grade != null && grade != null && !grade.Name.IsNullOrWhiteSpace() && !grade.Name.Equals(hose.Grade.Name, StringComparison.CurrentCultureIgnoreCase))
                        {
                            _gradeHelper.UpdateGrade(hose.GradeNumber, hose.Grade.Name, LoggingReference);
                        }
                    }

                    HydraDb.UpsertPumpGradePriceInfo(priceChange.Number, totals.Grade, hose.Number, totals.Ppu, message);
                }
            }, message?.FullId);
        }

        public void SetUnmannedJournalFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _unmannedJournalFile = fileName;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private static int GetFromBytes(IEnumerable<byte> bytes, int offset)
        {
            if (BitConverter.IsLittleEndian)
            {
                return BitConverter.ToInt32(bytes.Skip(offset).Take(4).ToArray(), 0);
            }
            else
            {
                return BitConverter.ToInt32(bytes.Skip(offset).Take(4).Reverse().ToArray(), 0);
            }
        }

        public void SetPrinterBitmapFile(string filename)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            FileInfo fileInfo = new FileInfo(filename);
            if (!fileInfo.Exists)
            {
                GetLogger().Info($"Bitmap file {filename} not found");
                NotificationWorker?.SendInformation($"Bitmap file {filename} not found");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            GetLogger().Info($"Bitmap file {filename} found");
            int length = 0;
            byte[] bytes = new byte[0];
            try
            {
                using (FileStream stream = new FileStream(filename, FileMode.Open))
                {
                    length = (int)stream.Length;
                    bytes = new byte[length];
                    stream.Read(bytes, 0, length);
                    stream.Close();
                }
            }
            catch (Exception e)
            {
                GetLogger().Error($"Error reading file {filename}", e);
            }

            GetLogger().Info($"Bitmap file {filename} has length {length}");
            // _controllerWorker?.SendInformation($"Bitmap file {filename} has length {length}");
            if (length < 38)
            {
                GetLogger().Error("Bitmap file length too short");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            string bitmapType = $"{(char)bytes[0]}{(char)bytes[1]}";
            if (bitmapType != "BM")
            {
                GetLogger().Error("Bitmap file not of type BM");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            int size = GetFromBytes(bytes, 2);
            int offset = GetFromBytes(bytes, 10);
            int width = GetFromBytes(bytes, 18);
            int paddedWidth = 32 * ((width + 31) / 32);
            int byteWidth = paddedWidth / 8;
            int height = GetFromBytes(bytes, 22);
            int imageSize = GetFromBytes(bytes, 34);
            GetLogger().Info($"Bitmap file {filename}" + $" has type {bitmapType}," + $" size {size}," + $" offset {offset}," +
                         $" width {width}," + $" padded width {paddedWidth}," + $" byte width {byteWidth}," + $" height {height}," +
                         $" image size {imageSize}");
            // _controllerWorker?.SendInformation($"Bitmap file {filename}" + $" has type {bitmapType}," + $" size {size}," +
            //                                    $" offset {offset}," + $" width {width}," + $" padded width {paddedWidth}," +
            //                                    $" byte width {byteWidth}," + $" height {height}," + $" image size {imageSize}");
            if (offset + imageSize != size)
            {
                GetLogger().Error("Bitmap image size does not match size");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            if (byteWidth * height != imageSize)
            {
                GetLogger().Error("Bitmap width and height do not match image size");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            byte[][] lines = new byte[height][];
            bool[][] dotLines = new bool[height][];
            for (int i = 0; i < height; i++)
            {
                lines[i] = bytes.Skip(offset + (height - i - 1) * byteWidth).Take(byteWidth).ToArray();
                dotLines[i] = new bool[width];
                for (int j = 0; j < width; j++)
                {
                    int x = j / 8;
                    int y = j % 8;
                    dotLines[i][j] = (lines[i][x] >> 7 - y & 0x01) == 0;
                }
            }

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < height; i++)
            {
                sb.AppendLine();
                for (int j = 0; j < width; j++)
                {
                    if (dotLines[i][j])
                    {
                        sb.Append("X");
                    }
                    else
                    {
                        sb.Append(" ");
                    }
                }
            }

            GetLogger().Debug($"Bitmap looks like {sb}");

            byte myWidth = (byte)((width + 7) / 8);
            byte myHeight = (byte)((height + 7) / 8);
            List<byte> myBytes = new List<byte>();
            for (int i = 0; i < myWidth; i++)
            {
                for (int j = 0; j < 8; j++)
                {
                    int x = 8 * i + j;
                    for (int k = 0; k < myHeight; k++)
                    {
                        byte b = 0;
                        for (int n = 0; n < 8; n++)
                        {
                            int y = 8 * k + n;
                            if (x < width && y < height && dotLines[y][x])
                            {
                                b += (byte)(1 << 7 - n);
                            }
                        }

                        myBytes.Add(b);
                    }
                }
            }

            List<byte> printBytes = new List<byte>();
            printBytes.AddRange(_printerHelper.InitPrinter.getBytes());
            printBytes.AddRange(CreateUploadLogoBytes(myWidth, myHeight, myBytes.ToArray()));
            _logoUploadCommand = printBytes.ToArray();
            _bitmapRead = true;
            _printerHelper.IsLogoUploaded = false;
            GetLogger().Debug($"Print command bytes is {BitConverter.ToString(printBytes.ToArray())}");


            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc />
        public void OnMaxPumps(int maxPump, string loggingReference = null)
        {
            if (!ConfigValueAutoCreatePumpsFromController.GetValue())
            {
                return;
            }

            var allOpts = _allOpts.GetAllOpts<IOptCore>();
            if (_allPumps.AllPumps.Count() == 0 && allOpts.Any(x => x is MobileOpt))
            {
                var opt = allOpts.First();
                for (var i = 0; i < maxPump; i++)
                {
                    var pump = (byte)(i + 1);
                    HydraDb.MapOpt(pump, opt.IdString);
                    HydraDb.SetMixed(pump, true);
                }

                GetWorker<IControllerWorker>().SetEsocketChanged();
            }
        }

        /// <inheritdoc />
        public Result<int> RecordTransaction(int id, IOptCore opt, IPump pump, IMessageTracking message, string txnNumber, out int transactionNumber, string cardNumber, string cardProductName, JournalTotalSalesItem totalSales,
            uint amountAuthed = 0, uint transactionVolume = 0, byte grade = 0, string gradeName = null, string productCode = null,
            IList<JournalFuelSalesItem> fuelSales = null, IList<JournalCarWashSalesItem> carWashSales = null, IList<JournalOtherSalesItem> otherSales = null, JournalDiscountItem discountItem = null, DiscountItem discount = null,
            JournalLocalAccountItem localAccountItem = null, LocalAccountPayment localAccountPayment = null, byte washProgram = 0, uint washPrice = 0, Payment payment = null)
        {
            message ??= new MessageTracking();
            transactionNumber = 0;

            return DoAction(() =>
            {
                var logRefFull = message.FullId;
                var isCancelled = amountAuthed == 0 && transactionVolume == 0 && grade == 0 && string.IsNullOrEmpty(gradeName) && string.IsNullOrEmpty(productCode) && carWashSales == null;

                WriteSalesItems(totalSales,
                    fuelSales ?? new List<JournalFuelSalesItem>(),
                    carWashSales ?? new List<JournalCarWashSalesItem>(),
                    otherSales ?? new List<JournalOtherSalesItem>(),
                    discountItem,
                    localAccountItem,
                    txnNumber, out var transactionNumber, message);

                if (!string.IsNullOrWhiteSpace(txnNumber))
                {
                    var receipt = opt.FetchTxnReceipt(txnNumber);
                    if (receipt == null)
                    {
                        opt.StoreTxnTransaction(txnNumber, transactionNumber);
                    }
                    else
                    {
                        if (HydraDb.SaveReceipt(cardNumber, (HydraDbClasses.ReceiptTransaction)receipt, opt.IdString, transactionNumber, logRefFull))
                        {
                            NotificationWorker.SendInformation(isCancelled ?
                                $"Payment.Cancelled: Stored Customer Receipt for {opt.FullIdentifier()}, transaction {transactionNumber}" :
                                $"Payment.Cleared: Stored Customer Receipt for {opt.FullIdentifier()}," + $" OPT transaction number {txnNumber}" + $" service transaction number {transactionNumber}");
                        }
                    }
                }

                var localAccountTransactionItem = (localAccountPayment == null) ? null : new bosMessage.LocalAccountTransactionItem
                {
                    Till = TillNumber,
                    CardNumber = localAccountPayment.CardNumber,
                    DateTime = DateTime.Now,
                    Registration = localAccountPayment.Registration,
                    Mileage = localAccountPayment.Mileage != 0 ? localAccountPayment.Mileage : 0,
                    Product = string.Format(GradeFormat, grade),
                    Volume = transactionVolume,
                    Amount = amountAuthed,
                    Grade = grade,
                    GradeName = gradeName
                };

                if (localAccountItem != null)
                {
                    BosOutWorker.WriteLocalAccountInvoiceFile(localAccountTransactionItem, message);
                }

                var resultDelivered = HydraDb.GetDelivered(pump.Number);
                var pumpDelivered = resultDelivered.IsSuccess ? resultDelivered.Value : null;

                var tq = pump.TransactionSummary;
                var dtmStartPay = tq.PaymentStates.Values.Where(x => x.PaymentResult != PaymentResult.Cleared).OrderByDescending(x => x.WhenProcessed).FirstOrDefault()?.WhenProcessed ?? DateTime.MinValue;
                var dtmStartPump = tq.TransactionStates.Values.Where(x => x.PumpState == HscPumpState.Request || x.PumpState == HscPumpState.Authorise).OrderBy(x => x.FirstEntered).FirstOrDefault()?.FirstEntered ?? DateTime.Now;
                var dtmFinish = tq.LocalEndTime;

                var info = new SendTransactionItem
                {
                    TillNumber = TillNumber,
                    ShiftNumber = 0, // TODO: Needs a sproc, migration, etc, as this is (only) around at ShiftEnd time
                    Id = transactionNumber,
                    OptId = opt.IdString,
                    Pump = pump.Number,
                    Hose = pump.DeliveredHose,
                    Price = pump.DeliveredPpu,
                    DateTime = DateTime.Now,
                    Number = $"{txnNumber}",
                    Volume = transactionVolume,
                    AuthAmount = amountAuthed,
                    PreAuthAmount = pump.AuthorisedAmount,
                    CardNumber = cardNumber,
                    CardProductName = cardProductName,
                    ProductCode = productCode,
                    WashTicket = null,
                    WashPrice = washPrice,
                    WashProgram = washProgram,
                    Discount = discount,
                    LocalAccountPayment = localAccountTransactionItem,
                    Grade = grade,
                    GradeName = gradeName,
                    Reference = payment?.Reference,
                    MerchantId = payment?.MerchantId,
                    TerminalId = payment?.TerminalId,
                    CardType = payment?.CardType,
                    TimeStamp = DateTime.Now,
                    AuthCode = payment?.AuthCode,
                    Registration = payment?.Registration,
                    Mileage = payment?.Mileage ?? 0,
                    TransactionStart = (dtmStartPump > dtmStartPay ? dtmStartPump : dtmStartPay).ToLocalTime(),
                    TransactionFinish = dtmFinish.ToLocalTime(),
                    TransactionSequenceNumber = pumpDelivered?.TransSeqNum ?? 0,
                    VatRate = pumpDelivered?.VatRate ?? 0,
                    VatAmount = pumpDelivered?.VatAmount ?? 0,
                };
                var booking = HydraDb.GetTransactionBooking(transactionNumber, txnNumber, message: message);
                if (booking.IsSuccess)
                {
                    HydraDb.UpdateTransactionBooking(booking.Value.Id, transactionNumber, info, message);
                }
                BosOutWorker.SendTransaction(info, message);

                if (localAccountPayment != null)
                {
                    GetWorker<ILocalAccountWorker>().ReduceBalance(localAccountPayment.CardNumber, amountAuthed, logRefFull);
                }

                GetWorker<IPumpIntegratorInTransient<IMessageTracking>>().PaymentClearedOrCancelledAcknowledged(pump.Number, info.TransactionSequenceNumber, message);

                return Result.Success(transactionNumber);
            }, message.FullId);
        }

        private Result<IOptCore> CheckOpt(int id, string action)
        {
            if (!_allOpts.TryGetOpt<IOptCore>(id, out var opt))
            {
                return Result.Failure<IOptCore>($"Unknown Opt Id: {id}");
            }

            CheckOptSignedIn(opt, action);

            return Result.Success(opt);
        }

        private Result CheckOptSignedIn(IOptCore opt, string action)
        {
            if (opt == null)
            {
                return Result.Failure("OPT is null");
            }

            if (!opt.SignedIn)
            {
                NotificationWorker?.SendInformation($"{opt.FullIdentifier()} is not signed in{(string.IsNullOrWhiteSpace(action) ? string.Empty : $", for {action}")}");
                opt.SetSignInRequired(true);
            }

            return Result.Success();
        }

        /// <inheritdoc />
        public Result StoreReceipt(int id, ReceiptTransaction receipt, IMessageTracking message)
        {
            var result = CheckOpt(id, "Store Receipt");
            if (!result.IsSuccess)
            {
                return result;
            }

            return StoreReceipt(result.Value, receipt, message);
        }

        /// <inheritdoc />
        public Result StoreReceipt(IOptCore opt, ReceiptTransaction receipt, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var logRef = message.FullId;

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Card number: {receipt.CardNumber}; Transaction number: {receipt.TxnNumber}; Receipt: {opt.FormatReceiptForLogging(receipt?.Details)}" }, reference: logRef);

                var result = CheckOptSignedIn(opt, "Store Receipt");
                if (!result.IsSuccess)
                {
                    return result;
                }

                if (string.IsNullOrWhiteSpace(receipt?.Details))
                {
                    return Result.Failure("No receipt details to store!");
                }

                var workerController = NotificationWorker;
                ReceiptJournal(receipt.Details, message);

                var customer = receipt.Details.Contains(CustomerCopy);
                var merchant = receipt.Details.Contains(MerchantCopy);
                long transactionNumber = 0;
                if (customer && !string.IsNullOrWhiteSpace(receipt.TxnNumber))
                {
                    transactionNumber = opt.FetchTxnTransaction(receipt.TxnNumber);
                    if (transactionNumber == 0)
                    {
                        opt.StoreTxnReceipt(receipt.TxnNumber, receipt);

                        workerController.SendInformation(
                            $"StoreReceipt.Internal: Stored Customer Receipt for {opt.FullIdentifier()}, OPT transaction number {receipt.TxnNumber}");
                    }
                }

                if (HydraDb.SaveReceipt(receipt.CardNumber, (HydraDbClasses.ReceiptTransaction)receipt, opt.IdString, transactionNumber, logRef))
                {
                    workerController.SendInformation(
                        $"StoreReceipt: Stored {(customer ? "Customer " : merchant ? "Merchant " : "")}Receipt for {opt.FullIdentifier()}," +
                        $" OPT transaction number {receipt.TxnNumber}" +
                        $"{(transactionNumber == 0 ? "" : $", service transaction number is  {transactionNumber}")}");
                }

                workerController.PushChange(EventType.TransactionsChanged);

                return Result.Success();
            }, logRef);
        }

        /// <inheritdoc />
        public Result<IEnumerable<ReceiptData>> GetReceipts(int id, string cardNumber, IMessageTracking message)
        {
            var result = CheckOpt(id, "Get Receipts");
            if (!result.IsSuccess)
            {
                return Result.Failure<IEnumerable<ReceiptData>>(result.Error);
            }

            return GetReceipts(result.Value, cardNumber, message);
        }

        /// <inheritdoc />
        public Result<IEnumerable<ReceiptData>> GetReceipts(IOptCore opt, string cardNumber, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var logRef = message.FullId;

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Card number: {cardNumber}" }, reference: logRef);

                var result = CheckOptSignedIn(opt, "Get Receipts");
                if (!result.IsSuccess)
                {
                    return Result.Failure<IEnumerable<ReceiptData>>(result.Error);
                }

                var workerController = NotificationWorker;

                var pump = opt.PumpList().Count() == 1 ? opt.PumpList().First() : (byte)0;
                if (pump > 0)
                {
                    var resultReserve = GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.ReservePump(pump, HydraDb.AdvancedConfig.PumpReserveLimit, message) ?? Result.Success();
                    if (!resultReserve.IsSuccess)
                    {
                        workerController.SendInformation($"GetReceipts from {opt.FullIdentifier()} Failed, pump is {pump}, No ReservePump");
                        return Result.Failure<IEnumerable<ReceiptData>>("!ReservePump");
                    }

                    GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.PreAuthRequest(new SecAuthRequest(message.ParentIdAsString, message.IdAsString, opt.IdString, pump), message);
                }

                var receiptInfo = string.IsNullOrWhiteSpace(cardNumber) ? null : HydraDb.GetReceipts(cardNumber);

                if (receiptInfo == null || !receiptInfo.Any())
                {
                    workerController.SendInformation($"Get Receipt for {opt.FullIdentifier()} card number not found");
                    return Result.Failure<IEnumerable<ReceiptData>>("No receipts found");
                }

                var receipts = new List<ReceiptData>();
                if (receipts.Any())
                {
                    workerController.SendInformation($"Got Receipt(s) for {opt.FullIdentifier()}");
                }

                foreach (var info in receiptInfo)
                {
                    var receiptDetails = (ReceiptDetails)info;
                    receipts.Add(new ReceiptData(receiptDetails));
                }

                return Result.Success(receipts.AsEnumerable());
            }, logRef);
        }

        /// <inheritdoc />
        public Result ReceiptPrinted(int id, ReceiptPrintedDetails receipt, IMessageTracking message)
        {
            var result = CheckOpt(id, "Receipt Printed");
            if (!result.IsSuccess)
            {
                return Result.Failure<IEnumerable<ReceiptData>>(result.Error);
            }

            return ReceiptPrinted(result.Value, receipt, message);
        }

        /// <inheritdoc />
        public Result ReceiptPrinted(IOptCore opt, ReceiptPrintedDetails receipt, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var logRef = message.FullId;

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { $"Card number: {receipt.CardNumber}; Transaction number: {receipt.TxnNumber}; Printed Count: {receipt.PrintedCount};{(receipt.TxnNumber == "0" ? " Receipt RePrint TimedOut!": string.Empty)}" }, reference: logRef);

                var result = CheckOptSignedIn(opt, "Receipt Printed");
                if (!result.IsSuccess)
                {
                    return Result.Failure<IEnumerable<ReceiptData>>(result.Error);
                }

                if (!(receipt == null || receipt.CardNumber.IsNullOrWhiteSpace() || receipt.TxnNumber.IsNullOrWhiteSpace() || receipt.TxnNumber == "0")) // ADO #746003
                {
                    result = HydraDb.UpdateReceiptPrintedCount(receipt.CardNumber, receipt.TxnNumber, opt.IdString, message);
                    if (!result.IsSuccess)
                    {
                        return Result.Failure("Unable to update printed count");
                    }

                    GetWorker<IControllerWorker>().SendInformation($"ReceiptPrinted: PrintedCount updated on, Receipt for {opt.FullIdentifier()}, OPT transaction number {receipt.TxnNumber}");
                }
                return Result.Success();

            }, logRef);
        }

        /// <inheritdoc />
        public Result PaymentApproved(PaymentResult result, IPump thePump, IOpt opt, IMessageTracking message, PaymentApproved payment, GenericOptConfig optConfig, bool autoAuth)
        {
            message ??= new MessageTracking();
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;
            var workerController = NotificationWorker;

            return DoAction(() =>
            {
                // Pulling out properties to minimize change in logging below
                var pump = payment.Pump;
                var amountAuthed = payment.AmountAuthed;

                if (!thePump.CanAddPayment)
                {
                    workerController.SendInformation($"Payment Approved from {opt.FullIdentifier()} Failed, pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("!CanAddPayment");
                }

                var resultReserve = GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.ReservePump(pump, HydraDb.AdvancedConfig.PumpReserveLimit, message) ?? Result.Success();
                if (!resultReserve.IsSuccess)
                {
                    workerController.SendInformation($"Payment Approved from {opt.FullIdentifier()} Failed, pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("!ReservePump");
                }

                IList<byte> allowedGrades = null;
                IList<byte> disallowedGrades = null;
                if (payment.Restrictions != null)
                {
                    allowedGrades = new List<byte>();
                    foreach (string code in payment.Restrictions.Codes)
                    {
                        TariffMapping mapping = optConfig.TariffMappings.FirstOrDefault(x => x.ProductCode.Equals(code));
                        if (mapping == null)
                        {
                            if (int.TryParse(code, out int codeNumber))
                            {
                                mapping = optConfig.TariffMappings.FirstOrDefault(x => int.TryParse(x.ProductCode, out int number) && number == codeNumber);
                            }
                        }

                        if (mapping != null)
                        {
                            allowedGrades.Add(mapping.Grade);
                        }
                    }
                }
                else
                {
                    disallowedGrades = optConfig.TariffMappings.Where(x => x.FuelCardsOnly).Select(x => x.Grade).ToList();
                }

                if (GetLogger().IsInfoEnabled)
                {
                    var info = new StringBuilder();
                    info.Append($"{opt.FullIdentifier(string.Empty)}; Pump: {pump}; Amount: £{amountAuthed / 100.0:F2}");
                    if (allowedGrades == null)
                    {
                        info.Append("; No allowed grades");
                    }
                    else
                    {
                        info.Append("; Allowed grades: ");
                        foreach (byte grade in allowedGrades)
                        {
                            info.Append($" {grade}");
                        }
                    }

                    if (disallowedGrades == null)
                    {
                        info.Append("; No disallowed grades");
                    }
                    else
                    {
                        info.Append("; Disallowed grades: ");
                        foreach (byte grade in disallowedGrades)
                        {
                            info.Append($" {grade}");
                        }
                    }

                    DoDeferredLogging(LogLevel.Info, $"{result}{ConfigConstants.NameOptUpper}", () => new[] { info.ToString() }, reference: logRefFull);
                }

                if (allowedGrades != null && thePump.AllGrades.Any() && !thePump.AllGrades.Any(allowedGrades.Contains))
                {
                    workerController.SendInformation($"Payment Approved from {opt.FullIdentifier()} Failed, pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("Some Grades are not allowed!");
                }

                if (disallowedGrades != null && thePump.AllGrades.Any() && thePump.AllGrades.All(disallowedGrades.Contains))
                {
                    workerController.SendInformation($"Payment Approved from {opt.FullIdentifier()} Failed,  pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("All Grades are not allowed");
                }

                if (!thePump.IsSecAuthRequested)
                {
                    GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.PostAuthRequest(new SecAuthRequest(message.ParentIdAsString, message.IdAsString, opt.IdString, thePump.Number), message);
                }

                thePump.AddPayment(amountAuthed, !autoAuth, allowedGrades, disallowedGrades, logRefFull);
                workerController.SendInformation($"Payment Approved from {opt.FullIdentifier()} Succeeded, pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                PushChangePumpStateChanged(thePump);
                if (autoAuth)
                {
                    GetWorker<IPosIntegratorOutTransient<IMessageTracking>>()?.StatusResponse(thePump.Number, message);
                }

                thePump.LogTransactionState(result, logRef);
                return Result.Success();
            }, logRefFull);
        }

        private void PushChangePumpStateChanged(IPump pump)
        {
            var workerController = NotificationWorker;
            workerController.PushChange(EventType.OPTChanged, pump.Opt?.IdString);
            workerController.PushChange(EventType.PumpChanged, pump.Number.ToString());
        }

        private Result<HydraDbClasses.PumpDelivered> RetrieveDelivered(IPump pump, IMessageTracking message)
        {
            var result = HydraDb.GetDelivered(pump.Number, message);
            if (result.IsSuccess)
            {
                pump.SetDeliveredHose(result.Value.Hose, result.Value.Price, message.IdAsString);
            }

            return result;
        }

        public Result PaymentCancelled(PaymentResult result, IPump thePump, IOpt opt, IMessageTracking message, PaymentCancelled payment)
        {
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;
            var workerController = NotificationWorker;

            return DoAction(() =>
            {
                // Pulling out properties to minimize change in logging below
                var pump = payment.Pump;
                var amountAuthed = payment.AmountAuthed;
                var cardNumber = payment.CardNumber;
                var cardProductName = payment.CardProductName;
                var txnNumber = payment.TxnNumber;

                if (!thePump.CanCancelPayment)
                {
                    workerController.SendInformation($"Payment Cancelled from {opt.FullIdentifier()} Failed, pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("!CanCancelPayment");
                }

                DoDeferredLogging(LogLevel.Info, ConfigConstants.NameOptUpper, () => new[] { $"{opt.FullIdentifier(string.Empty)}; {Pump.HeaderPump}: {pump}" }, reference: logRefFull);

                RetrieveDelivered(thePump, message);

                var totalSales = new JournalTotalSalesItem
                {
                    PumpNumber = thePump.Number,
                    Hose = thePump.DeliveredHose,
                    Amount = 0,
                    CardNumber = cardNumber,
                    CardProductName = cardProductName
                };

                RecordTransaction(opt.Id, opt, thePump, message, txnNumber, out var transactionNumber, cardNumber, cardProductName, totalSales, payment: payment);

                var prevState = thePump.PumpState;
                thePump.CancelPayment(logRef);

                GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.PaymentCancelled(pump, message);

                GetWorker<IPosIntegratorOutTransient<IMessageTracking>>()?.StatusResponse(thePump.Number, message);
                workerController.SendInformation($"Payment Cancelled from {opt.FullIdentifier()} Succeeded," + $" pump is {pump}");

                GetWorker<IPosIntegratorOutMode<IMessageTracking>>()?.ModeChangedResponse(pump, opt.Mode.ToModeChangeType(), prevState.ToPumpOptType(), message);

                workerController?.PushChange(EventType.TransactionsChanged);
                workerController?.PushChange(EventType.OPTChanged, opt.IdString);
                workerController?.PushChange(EventType.PumpChanged, $"{thePump.Number}");

                thePump.LogTransactionState(result, logRef);
                return Result.Success();
            }, logRefFull);
        }

        public Result PaymentCleared(PaymentResult result, DiscountItem discount, IPump thePump, IOptCore opt, IMessageTracking message, PaymentCleared payment)
        {
            return PaymentCleared(result, discount, thePump, opt, message, payment, OptWorker.GenericOptConfig);
        }

        public Result PaymentCleared(PaymentResult result, DiscountItem discount, IPump thePump, IOptCore opt, IMessageTracking message, PaymentCleared payment, GenericOptConfig optConfig)
        {
            var logRef = message.IdAsString;
            var logRefFull = message.FullId;
            var workerController = NotificationWorker;

            return DoAction(() =>
            {
                // Pulling out properties to minimize change in logging below
                var pump = payment.Pump;
                var amountAuthed = payment.AmountAuthed;
                var cardNumber = payment.CardNumber;
                var cardProductName = payment.CardProductName;
                var txnNumber = payment.TxnNumber;
                var localAccountCardNumber = payment.LocalAccountPayment?.CardNumber;
                string ticketNumber = null; // Passed in with old payment object, not used as carwash

                if (!thePump.CanClearPayment)
                {
                    workerController.SendInformation($"Payment Cleared from {opt.FullIdentifier()} Failed," +
                                                      $" pump is {pump}, amount is £{amountAuthed / 100.0:F2}");
                    thePump.LogTransactionState(result, logRef, false);
                    return Result.Failure("!CanClearPayment");
                }

                var resultDelivered = RetrieveDelivered(thePump, message);
                var pumpDelivered = resultDelivered.IsSuccess ? resultDelivered.Value : null;

                // Have we already got the transaction
                var checkInterval = HydraDb.BosConfig?.RecordTransactionDuplicateCheckInterval ?? TimeSpan.FromHours(4);
                var checkFrom = DateTime.Now.AddTicks(-checkInterval.Ticks);
                var resultTransaction = HydraDb.FetchFuelTransactions(checkFrom, DateTime.Now, ConfigValueMaxRetalixTransactionNumber);
                var dupTrans = resultTransaction?.Any(x => x.TxnNumber == payment.TxnNumber &&
                    x.PumpDetails == $"PUMP {pumpDelivered?.Number}:{pumpDelivered.Hose}" && x.Amount == payment.AmountAuthed &&
                    payment.Products.Any(p => p.Value == x.Amount && p.Quantity == x.FuelQuantity)) ?? false;

                if (dupTrans)
                {
                    DoDeferredLogging(LogLevel.Warn, "Transaction.Duplicate.FpId", () => new[] { $"{thePump.Number}; TxnNumber: {payment.TxnNumber}; Since: {checkFrom:dd/MM/yyyy HH:mm:ss.fff}" }, reference: logRefFull);
                }
                else
                {
                    byte grade = 0;
                    string theGradeName = null;
                    string productCode = null;
                    uint transactionVolume = 0;
                    byte washProgram = 0;
                    uint washPrice = 0;

                    IList<JournalFuelSalesItem> fuelSales = new List<JournalFuelSalesItem>();
                    IList<JournalCarWashSalesItem> carWashSales = new List<JournalCarWashSalesItem>();
                    IList<JournalOtherSalesItem> otherSales = new List<JournalOtherSalesItem>();
                    var totalSales = CreateTotalSalesItem(payment, amountAuthed, cardNumber, cardProductName, thePump);
                    var discountItem = CreateDiscountItem(discount);
                    var localAccountItem = CreateLocalAccountItem(payment.LocalAccountPayment);

                    foreach (ProductItem product in payment.Products.Convert()?.Products ?? new List<ProductItem>())
                    {
                        FindProductMapping(product.Code, out TariffMapping mapping, out Wash wash, optConfig);
                        if (mapping == null)
                        {
                            if (wash == null)
                            {
                                DoDeferredLogging(LogLevel.Warn, $"Unknown Product Code {product.Code} in Product List", reference: logRefFull);
                                otherSales.Add(new JournalOtherSalesItem
                                {
                                    ProductCode = product.Code,
                                    Quantity = product.Quantity,
                                    Amount = product.Value
                                });
                            }
                            else
                            {
                                washPrice += product.Value;
                                washProgram = wash.ProgramId;
                                carWashSales.Add(new JournalCarWashSalesItem
                                {
                                    ProgramId = wash.ProgramId,
                                    WashName = wash.Description,
                                    Quantity = product.Quantity,
                                    Amount = product.Value,
                                    Category = wash.Category,
                                    Subcategory = wash.Subcategory
                                });
                            }
                        }
                        else
                        {
                            transactionVolume += product.Quantity;
                            productCode = product.Code;
                            grade = mapping.Grade;
                            theGradeName = _gradeHelper.GetGradeName(mapping.Grade);

                            fuelSales.Add(new JournalFuelSalesItem
                            {
                                Grade = mapping.Grade,
                                GradeName = theGradeName,
                                Amount = product.Value,
                                Quantity = product.Quantity
                            });
                        }
                    }

                    RecordTransaction(opt.Id, opt, thePump, message, txnNumber, out var transactionNumber, cardNumber, cardProductName, totalSales,
                        amountAuthed, transactionVolume, grade, theGradeName, productCode, fuelSales, carWashSales, otherSales, discountItem, discount,
                        localAccountItem, payment.LocalAccountPayment, washProgram, washPrice, payment: payment);
                }

                var prevState = thePump.PumpState;
                thePump.ClearPayment(cardNumber, cardProductName, amountAuthed, logRef);
                GetWorker<ITelemetryWorker>().PaymentClearedReceivedFromOpt(thePump.Number, logRefFull);

                GetWorker<IPosIntegratorOutTransient<IMessageTracking>>()?.StatusResponse(thePump.Number, message);
                workerController.SendInformation($"Payment Cleared from {opt.FullIdentifier()}, pump is {pump}");

                GetWorker<IPosIntegratorOutMode<IMessageTracking>>()?.ModeChangedResponse(pump, opt.Mode.ToModeChangeType(), prevState.ToPumpOptType(), message);

                workerController?.PushChange(EventType.TransactionsChanged);

                thePump.LogTransactionState(result, logRef);
                return Result.Success();
            }, logRefFull);
        }

        private static JournalTotalSalesItem CreateTotalSalesItem(optModels.Payment paymentCleared, uint amountAuthed, string cardNumber, string cardProductName, IPump thePump)
        {
            return new JournalTotalSalesItem
            {
                PumpNumber = thePump.Number,
                Hose = thePump.DeliveredHose,
                Amount = amountAuthed,
                CardNumber = cardNumber,
                CardProductName = cardProductName ?? ConfigConstants.Unknown,
                Registration = paymentCleared.Registration,
                Mileage = paymentCleared.Mileage
            };
        }

        private static JournalLocalAccountItem CreateLocalAccountItem(LocalAccountPayment localAccount)
        {
            return localAccount == null
                ? null
                : new JournalLocalAccountItem
                {
                    Mileage = localAccount.Mileage,
                    Registration = localAccount.Registration
                };
        }

        private static JournalDiscountItem CreateDiscountItem(DiscountItem discount)
        {
            return discount == null
                ? null
                : new JournalDiscountItem
                {
                    Name = discount.Name,
                    Type = discount.Type,
                    Value = discount.Value,
                    CardNumber = discount.CardNumber
                };
        }

        private void FindProductMapping(string code, out TariffMapping mapping, out Wash wash, GenericOptConfig optConfig)
        {
            mapping = optConfig.TariffMappings.FirstOrDefault(x => x.ProductCode.Equals(code));
            if (mapping == null)
            {
                if (int.TryParse(code, out int codeNumber))
                {
                    mapping = optConfig.TariffMappings.FirstOrDefault(x =>
                        int.TryParse(x.ProductCode, out int number) && number == codeNumber);
                }
            }

            if (mapping == null)
            {
                wash = optConfig.Washes.FirstOrDefault(x => x.ProductCode.Equals(code));
                if (wash == null)
                {
                    if (int.TryParse(code, out int codeNumber))
                    {
                        wash = optConfig.Washes.FirstOrDefault(x =>
                            int.TryParse(x.ProductCode, out int number) && number == codeNumber);
                    }
                }
            }
            else
            {
                wash = null;
            }
        }

        private ushort GetPpl(TransactionItem transaction)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Transaction", () => new[] { $"Id: {transaction.Id}; TxnNumber: {transaction.Number}" });
            var booking = HydraDb.GetTransactionBooking(transId: transaction.Id, txnNumber: transaction.Number);
            if (booking.IsSuccess)
            {
                var item = booking.Value.SendTransactionItem;
                var itemInstance = item.IsNullOrWhiteSpace() ? null : JsonConvert.DeserializeObject<SendTransactionItem>(item);
                if (itemInstance != null && itemInstance.Price > 0)
                {
                    return itemInstance.Price;
                }
            }

            var grade = transaction.Grade;
            var info = _gradeHelper.GetGradeInfo(grade);
            var pumpConfig = GetWorker<IPumpIntegratorConfiguration>();
            var price = pumpConfig?.GradePrices[transaction.Pump].FirstOrDefault(x=> x.Id == grade);

            var tank = _currentTankInfo.Values.FirstOrDefault(x => x.Name.Equals(info.Name, StringComparison.InvariantCultureIgnoreCase));

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "GradeId", () => new[] { $"{grade}; Grade: {info?.Name ?? "n/a"}; PPU: {tank?.Name ?? "n/a"}" });
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Tanks", () => new[] { $"{string.Join("; ", _currentTankInfo.Select(x=> $"{x.Key}: {JsonConvert.SerializeObject(x.Value)}"))}"});

            return tank?.Price ?? price?.PriceAsShort ?? 0;
        }

        // TODO: Move into SLIB
        private bool IsCancelledTransaction(TransactionItem ti) => ti.Quantity == 0 && ti.Value == 0 && ti.Hose == 0 && ti.Grade == 0 && ti.Value == 0;

        private TransactionItem Cast(FuelTransaction ft)
        {
            var result = (TransactionItem)ft;
            if (result != null)
            {
                result.Till = TillNumber;
                result.OperatorId = _tillUser;
                result.PricePerLitre = GetPpl(result);

                // TODO: These need mapping, as best can be
                result.CardProductName = string.Empty;
                result.AuthorisedValue = 0;
                result.Type = string.Empty;
                result.WashProgram = 0;
                result.WashPrice = 0;
                result.WashType = string.Empty;
                result.WashVatCode = string.Empty;
                result.WashTicket = string.Empty;
            }

            return result;
        }

        /// <inheritdoc />
        public Result<StatusCodeResult> RequestTransaction(bosMessage.IdInfo transaction, IMessageTracking message = null)
        {
            return DoAction(() =>
            {
                var result = HydraDb.FetchTransaction(ConfigValueMaxRetalixTransactionNumber, transaction.Id, transaction.Number, message);
                if (!result.IsSuccess || result.Value == null)
                {
                    return Result.Success(StatusCodeResult.Specific(System.Net.HttpStatusCode.BadRequest));
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), result.Value.GetType().Name, () => new[] { $"{JsonConvert.SerializeObject(result.Value)}" });
                var transItem = Cast(result.Value);
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), transItem.GetType().Name, () => new[] { $"{JsonConvert.SerializeObject(transItem)}" });

                if (IsCancelledTransaction(transItem))
                {
                    return StatusCodeResult<TransactionItem>.Success(transItem);
                }

                if (!_allPumps.TryGetPump(transItem.Pump, out var pump))
                {
                    return Result.Success(StatusCodeResult.Specific(System.Net.HttpStatusCode.BadRequest));
                }

                var resultReceipt = _receiptHelper.GetReceipts(pump, new Htec.Hydra.Core.Pos.Messages.GetReceiptRequest { CardNumber = transItem.CardNumber, TransactionNumber = transaction.Id, Pump = transItem.Pump });
                if (!resultReceipt.IsSuccess)
                {
                    resultReceipt = _receiptHelper.GetReceipts(pump);
                }
                if (resultReceipt.IsSuccess && resultReceipt.Value.Any())
                {
                    var receiptInfo = resultReceipt.Value.FirstOrDefault();
                    if (receiptInfo != null)
                    {
                        var opt = _allOpts.GetOptForIdString<IOptCore>(receiptInfo.Opt);

                        var resultRaw = opt.GetRawReceiptText(receiptInfo);
                        if (resultRaw.IsSuccess)
                        {
                            transItem.ReceiptContents = resultRaw.Value;
                        }
                    }
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), transItem.GetType().Name, () => new[] { $"{JsonConvert.SerializeObject(transItem)}" });

                return StatusCodeResult<TransactionItem>.Success(transItem);

            }, message.FullId);
        }

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestDayEnd(IMessageTracking message)
        {
            var result = RequestDayEnd(message);
            return ResultHelpers.MapResult(result);
        }

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestShiftEnd(IMessageTracking message)
        {
            var result = RequestShiftEnd(message);
            return ResultHelpers.MapResult(result);
        }
    }
}