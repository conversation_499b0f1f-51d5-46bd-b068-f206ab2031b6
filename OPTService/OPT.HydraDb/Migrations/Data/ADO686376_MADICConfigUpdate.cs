using FluentMigrator;
using Htec.Common.Extensions;
using OPT.Common;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed by MADIC integrations (mostly Magical Advanced Config keys)
    /// </summary>
    [Migration(26200001, "ADO#686376 - Set further configuration needed for MADIC integrations #3"), Tags(Constants.MigrationTypeData)]
    public class ADO686376_MADICConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;
        private const string CategoryNameLoggingDeveloper = "LOGGING:DEVELOPER";
        private const string CategoryNameTQ = "TRANSACTIONQUALITY";
        private const string TQAction = "TransactionQuality:Action:";        

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC) 
            {
                return;
            }

            // Shift End/OPT Logging
            Execute.Sql($"EXEC dbo.SetLogInterval @interval = 3600");            

            // Transaction Quality
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{TQAction}Block:Pump', @value = 'False', @category = '{CategoryNameTQ}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{TQAction}Block:Opt', @value = 'False', @category = '{CategoryNameTQ}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{TQAction}Clear:Txn', @value = 'False', @category = '{CategoryNameTQ}'");

            // BOS
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BookTransaction:Ignore:Invalid', @value = 'True', @category = '{cfgConstants.CategoryNameBOS}'");

            // Pre-defined Amounts
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'PredefinedAmount:Enabled:PaymentCard', @value = 'False', @category = '{ConfigConstants.NameOptUpper}'");

            // // TODO: Produce Code Mappings
            //Execute.Sql($"EXEC dbo.ClearTariffMappings");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 1, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 2, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 3, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 4, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 5, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 6, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 7, @productCode = ''");
            //Execute.Sql($"EXEC dbo.AddTariffMapping @grade = 8, @productCode = ''");

            // Workers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:UpdateWorker', @value = '12:00:00', @category = '{cfgConstants.CategoryNameWorkers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:ConfigUpdateWorker', @value = '12:00:00', @category = '{cfgConstants.CategoryNameWorkers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:DomsSetupTcp', @value = '00:00:01', @category = '{cfgConstants.CategoryNameWorkers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:PumpWorker', @value = '00:00:01', @category = '{cfgConstants.CategoryNameWorkers}'");

            // Debug Logging
            //Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:BookingTransactionOrchestrator', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");

            // // TODO: Remove OPT ReceiptHeader
            //Execute.Sql($"UPDATE dbo.OPTMode SET ReceiptHeader = '' WHERE OPT != 'GLOBAL'");

            // SignalR HubClient
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'AcknowledgeServerMessage:Enabled', @value = 'False', @category = '{cfgConstants.CategoryNameSignalRHubClient}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC)
            {
                return;
            }

            // BOS
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BookTransaction:Ignore:Invalid', @value = 'False', @category = '{cfgConstants.CategoryNameBOS}'");
        }
    }
}
