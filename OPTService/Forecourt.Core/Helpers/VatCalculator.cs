using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using System;

namespace Forecourt.Core.Helpers
{
    public class VatCalculator : Loggable, IVatCalculator
    {
        public VatCalculator(IHtecLogger logger, IConfigurationManager configurationManager = null): base(logger, configurationManager)
        {
        }

        /// <inheritdoc/>
        public (double, double) CalculateVat(double amount, double vatrate, byte decPlaces = 2)
        {
            var vat = Math.Round(amount * vatrate / (100 + vatrate), decPlaces, MidpointRounding.AwayFromZero);
            var net = amount - vat;

            return (vat, net);
        }

        /// <inheritdoc/>
        public (uint, uint) CalculateVat(uint amount, float vatrate, byte decPlaces = 0)
        {
            var (vatAmount, netAmount) = CalculateVat((double)amount, (double)vatrate, decPlaces);

            return (Convert.ToUInt32(vatAmount), Convert.ToUInt32(netAmount));
        }
    }
}
