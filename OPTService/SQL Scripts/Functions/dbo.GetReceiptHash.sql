USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[GetOPTMode]') AND TYPE IN ('FN', 'P', 'PC'))
BEGIN
	DROP FUNCTION [getReceiptHash]
END
GO

CREATE FUNCTION [dbo].[getReceiptHash](
	@CardNumber varchar(20), @ReceiptContent varchar(MAX), @OPT varchar(1024), @TransactionNumber bigint, @Amount bigint, @Timestamp datetime2, @MaxContentLength int = NULL)
	RETURNS varbinary(64)
	AS
	BEGIN
		-- 620347 - Some HSC fail to generate a Hash (due to the receipt content length) so allow pre and post options
		IF (@MaxContentLength IS NULL) 
		  SET @MaxContentLength = 6144
		ELSE IF (@MaxContentLength = 0)
		  SET @MaxContentLength = LEN(@ReceiptContent);		
    
		DECLARE @salt CHAR(36) ='99AAECE0-0206-43F9-B43E-308A953ADEEE';
		DECLARE @pepper CHAR(36) ='BA23AEFB-05E5-4D5B-B30B-BB731C449C05';

		RETURN HASHBYTES('SHA2_256', CONCAT(@salt, @CardNumber, SUBSTRING(@ReceiptContent, 1, @MaxContentLength), @OPT, @TransactionNumber, @pepper, @Amount, @Timestamp));
	END
GO

GRANT EXECUTE ON getReceiptHash TO UserRole
GO
