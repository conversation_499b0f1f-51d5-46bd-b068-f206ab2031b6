namespace OPT.TransactionValidator.Common
{
    /// <summary>
    /// Common Constants
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// Configuration Category for all things Connectivity-wise.
        /// </summary>
        public const string ConfigKeyCategoryTransactionQuality = "TRANSACTIONQUALITY::";

        /// <summary>
        /// Root TransactionQuality key
        /// </summary>
        public const string ConfigKeyTransactionQualityAction = ConfigKeyCategoryTransactionQuality + "TransactionQuality:Action";

        /// <summary>
        /// Config key to override the 'Block Pump' action
        /// </summary>
        public const string ConfigKeyTransactionQualityBlockPump = ConfigKeyTransactionQualityAction + ":Block:Pump";

        /// <summary>
        /// Config key to override the 'Block Opt' action
        /// </summary>
        public const string ConfigKeyTransactionQualityBlockOpt = ConfigKeyTransactionQualityAction + ":Block:Opt";

        /// <summary>
        /// Config key to override the 'Clear Transaction' action
        /// </summary>
        public const string ConfigKeyTransactionQualityClearTransaction = ConfigKeyTransactionQualityAction + ":Clear:Txn";

        /// <summary>
        /// Default value for the 'Block Pump' action
        /// </summary>
        public const bool DefaultValueTransactionQualityBlockPump = false;

        /// <summary>
        /// Default value for the 'Block Opt' action
        /// </summary>
        public const bool DefaultValueTransactionQualityBlockOpt = false;

        /// <summary>
        /// Default value for the 'Clear Transaction' action
        /// </summary>
        public const bool DefaultValueTransactionQualityClearTransaction = false;

        /// <summary>
        /// Root TransactionQuality threshold key
        /// </summary>
        public const string ConfigKeyTransactionQualityThreshold = ConfigKeyCategoryTransactionQuality + "TransactionQuality:Threshold";

        /// <summary>
        /// Config key to set the threshold for vending time.
        /// </summary>
        public const string ConfigKeyVendTimeThreshold = ConfigKeyTransactionQualityThreshold + ":VendTime";

        /// <summary>
        /// Config key to set the threshold for flow speed.
        /// </summary>
        public const string ConfigKeyFlowSpeedThreshold = ConfigKeyTransactionQualityThreshold + ":FlowSpeed";

        /// <summary>
        /// Config key to set the threshold for authorization time.
        /// </summary>
        public const string ConfigKeyAuthTimeThreshold = ConfigKeyTransactionQualityThreshold + ":AuthTime";

        /// <summary>
        /// Default value for vend time threshold, in seconds.
        /// </summary>
        public const uint DefaultVendTimeThreshold = 10;

        /// <summary>
        /// Default value for flow speed threshold, in seconds.
        /// </summary>
        public const uint DefaultFlowSpeedThreshold = 1500;

        /// <summary>
        /// Default value for auth time threshold, in seconds.
        /// </summary>
        public const uint DefaultAuthTimeThreshold = 10;
    }
}
