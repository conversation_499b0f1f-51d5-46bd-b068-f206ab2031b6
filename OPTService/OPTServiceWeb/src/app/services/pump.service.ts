import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class PumpService {

  private baseUrl: string;

  /**
   * The PumpService constructor.
   * @param http The http client used for requests.
   */
  constructor(private http: HttpClient,
    private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.pumpController.base;
  }

  /**
   * Gets the list of Pumps.
   * @param pumpNumber The optional pump number
   */
  public getPumps(pumpNumber: string = ''): Observable<any> {
    const url = this.baseUrl + environment.pumpController.getPumpInfo;
    const params = {
      pumpNumber: pumpNumber
    };
    const options = {
      params: pumpNumber ? params : {}
    };

    this.logger.debug(`Getting ${pumpNumber? 'single pump' : 'all pumps'} via url: ${url} ${pumpNumber? 'pumpNumber: ' + pumpNumber : ''}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Gets the list of TIDs.
   */
  public getTids(): Observable<any> {
    const url = Utils.getBaseURLOptService(document) + environment.tidController.base + environment.tidController.getTidList;
    const params = {
    };
    const options = {
      params: params
    };

    this.logger.debug(`Getting all TIDs via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Gets the MaxFillOverride
   * This may be moved to a future AdvnacedConfig.service
   */
  public getMaxFillOverride(): Observable<any> {
    const url = Utils.getBaseURLOptService(document) + environment.advancedController.base + environment.advancedController.getMaxFillOverride;
    const params = {
    };
    const options = {
      params: params
    };

    this.logger.debug(`Getting MaxFillOverride via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Sets the TID for the given Pump
   * @param pump The target Pump to be updated..
   */
  public setTid(pump: any) {
    const url = this.baseUrl + environment.pumpController.MapToTid;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting Tid: ${pump.Tid} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Sets the OPT for the given Pump.
   * @param pump The target Pump to be updated.
   */
  public setOpt(pump: any) {
    const url = this.baseUrl + environment.pumpController.MapToOpt;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting OPT: ${pump.OptStringId} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Sets the DefaultDayMode for the given Pump.
   * @param pump The target Pump to be updated.
   */
  public setDefaultDayMode(pump: any) {
    const url = this.baseUrl + environment.pumpController.SetDefaultMode;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting DefaultDayMode: ${pump.DefaultDayMode} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Closes or Opens the given Pump.
   * @param pump The target Pump to be updated.
   */
  public changePumpStatus(pump: any) {
    const url = this.baseUrl + environment.pumpController.ClosePump;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Changing pump status: ${pump.Closed ? 'Closed' : 'Open'} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Forces the closure of the given Pump.
   * @param pump The target Pump to force closed.
   */
  public forceClosePump(pump: any) {
    const url = this.baseUrl + environment.pumpController.ForceClosePump;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Forcing close of Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Sets the MaxFillOverrideForFuelCards for the given Pump.
   * @param pump The target Pump to be updated.
   */
  public setMaxFillOverrideForFuelCards(pump: any) {
    const url = this.baseUrl + environment.pumpController.SetMaxFillOverrideForFuelCards;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting MaxFillOverrideForFuelCards: ${pump.MaxFillOverrideForFuelCards} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Sets the MaxFillOverrideForPaymentCards for the given Pump.
   * @param pump The target Pump to be updated.
   */
  public setMaxFillOverrideForPaymentCards(pump: any) {
    const url = this.baseUrl + environment.pumpController.SetMaxFillOverrideForPaymentCards;
    const params = {
    };
    const options = {
      params: params
    };
    this.logger.debug(`Setting MaxFillOverrideForPaymentCards: ${pump.MaxFillOverrideForPaymentCards} to Pump: ${pump.Number} via ${url}`);
    return this.http.post(url, pump, options);
  }

  /**
   * Gets the list of Opts fitering by @param optIdString 
   * @param optIdString The optional OPT IdString parameter.
   */
  public getPumpControllerStatus(): Observable<any> {
    const url = this.baseUrl + environment.pumpController.isPumpControllerEnabled;

    this.logger.debug(`Getting pump controller status`);
    return this.http.get(url)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
    * Handles error observable.
    * @param error 
    * @returns  
    */
  private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}

