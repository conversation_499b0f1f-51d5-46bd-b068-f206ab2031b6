using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class FuelSale
    {
        [JsonProperty("fuellingTransactionId")]
        public int FuellingTransactionId { get; set; }
        [JsonProperty("fuellingTimeStamp")]
        public string FuellingTimeStamp { get; set; }
        [JsonProperty("fuelGradeId")]
        public int FuelGradeId { get; set; }
        [JsonProperty("fuelItemId")]
        public string FuelItemId { get; set; }
        [JsonProperty("fuelingPointId")]
        public int FuelingPointId { get; set; }
        [JsonProperty("nozzleId")]
        public int NozzleId { get; set; }
        [JsonProperty("fuelSalePrice")]
        public double FuelSalePrice { get; set; }
        [JsonProperty("fuelEntryMethod")]
        public string FuelEntryMethod { get; set; }
        [JsonProperty("fuelSalesVolume")]
        public double FuelSalesVolume { get; set; }
        [JsonProperty("fuelSalesValue")]
        public double FuelSalesValue { get; set; }
        [JsonProperty("fuelGradeDescription")]
        public string FuelGradeDescription { get; set; }
    }
}
