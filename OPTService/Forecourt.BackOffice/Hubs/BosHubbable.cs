using System;
using Forecourt.Bos.Hubs.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using Microsoft.AspNet.SignalR;

namespace Forecourt.Bos.Hubs
{
    /// <summary>
    /// Bos version of <see cref="Hubbable{TMessage, TMessageTracking}"/> dealing with <see cref="BosMessage"/> based messaging
    /// </summary>
    /// <inheritdoc/>
    public class BosHubbable : Hubbable<BosMessage, IMessageTracking>, IBosHubbable, IBosHubbableIn
    {
        /// <inheritdoc/>
        public BosHubbable(IHtecLogManager logManager, INotificationWorker<string> notifier, IConfigurationManager configurationManager = null) : 
            base(logManager, nameof(BosHubbable), GlobalHost.ConnectionManager.GetHubContext<BosHub>(), configurationManager)
        {
            RegisterWorker(notifier ?? throw new ArgumentNullException(nameof(notifier)));
        }
    }
}
