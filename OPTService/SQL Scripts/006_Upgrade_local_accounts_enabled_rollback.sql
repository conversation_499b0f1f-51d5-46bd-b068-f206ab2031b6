USE [Hydra]

PRINT N'HOPT-1117 - Create Local Accounts Configuration...Begin';
PRINT N'Creating Procedure [dbo].[GetSiteInfo]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
  @headerId INT,
  @categoryId INT AS
BEGIN 
    DECLARE @siteInfoCategory int = (SELECT [Id] FROM ConfigurationCategory WHERE [Category] = 'SITEINFO')
	DECLARE @enabled bit = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'ENABLED')
	DECLARE @cardPreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'CARDPREAUTH')
	DECLARE @devicePreAuth int = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'DEVICEPREAUTH')
	DECLARE @ttq varchar(8) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @categoryId AND UPPER([Key]) = 'TTQ')
	DECLARE @siteType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'SITETYPE')
	DECLARE @posType varchar(16) = (SELECT [Value] FROM ConfigurationDetail WHERE [HeaderId] = @headerId AND [CategoryId] = @siteInfoCategory AND UPPER([Key]) = 'POSTYPE')

	SELECT Mode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride,
	@enabled AS IsContactlessEnabled, 
	@cardPreAuth AS ContactlessCardPreAuth, 
	@devicePreAuth AS ContactlessDevicePreAuth, 
	@ttq AS TTQ,
	@siteType AS SiteType,
	@posType AS PosType
	FROM SiteInfo
END

GO
GO
PRINT N'HOPT-1117 - Create Local Accounts Configuration...End';
PRINT N'';

GO
