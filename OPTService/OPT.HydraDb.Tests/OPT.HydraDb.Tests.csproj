<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<IsPackable>false</IsPackable>
		<Platforms>AnyCPU;x64</Platforms>
		<LangVersion>9.0</LangVersion>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentMigrator" Version="3.3.2" />
    <PackageReference Include="Htec.Testing.Helpers" Version="3.1.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.3" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.extensibility.core" Version="2.4.2" />
    <PackageReference Include="xunit.runner.console" Version="2.4.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OPT.HydraDb\OPT.HydraDb.csproj" />
  </ItemGroup>

</Project>
