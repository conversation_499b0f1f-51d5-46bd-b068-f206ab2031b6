import { DatePipe } from '@angular/common';
import { Component, NgZone, OnInit } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { InfoMessageDetails } from 'src/app/core/models/infoMessageDetails.model';
import { InfoMessagesService } from 'src/app/services/info-messages.service';
import { LoadingService } from 'src/app/services/loading.service';
import { SignalRService } from 'src/app/services/signal-r.service';

@Component({
  selector: 'app-info-messages',
  templateUrl: './info-messages.component.html',
  styleUrls: ['./info-messages.component.css']
})
export class InfoMessagesComponent implements OnInit {

  showContent: boolean = false;
  infoMessagesData: Array<InfoMessageDetails> | undefined;
  signalRData: Subscription | undefined;

  constructor(
    private signalRService: SignalRService,
    private zone: NgZone,
    private infoMessagesService: InfoMessagesService,
    private datePipe: DatePipe,
    private logger: NGXLogger,
    private loadingService: LoadingService) {
     this.signalRData = this.signalRService.getInfoMessageSignalRMessage().subscribe(() => {
      this.zone.run(() => {
        this.refreshData(false);
      });
    });
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param data The signalR pushed object. 
   */
  refreshData(showLoading: boolean = true): void {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen(); 
    }

    this.infoMessagesService.getInfoMessageDetails().subscribe(data => {
      this.logger.debug('Info messages details data', data);
      this.infoMessagesData = data;
    }, () => {
      this.logger.error('Problem getting messages details data');
      this.loadingService.errorDuringLoading();
    },() => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

  ngOnInit(): void {
    setTimeout(()=>{this.refreshData();});
  }

  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * Formates the date to display it
   * @param date Date to format
   */
  getTimestamp(date: Date): string {
    let text = this.datePipe.transform(date, "dd-MM-YYYY HH:mm:ss.SSS");
    return text? text : "";
  }
}
