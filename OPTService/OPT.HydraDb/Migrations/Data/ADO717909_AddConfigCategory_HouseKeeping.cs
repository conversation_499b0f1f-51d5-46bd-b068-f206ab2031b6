using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add HOUSEKEEPING Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(26200008, "ADO#717909 - add HOUSEKEEPING Configuration Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO717909_AddConfigurationCategory_HouseKeeping : Migrationable
    {
        private const string _categoryName = Forecourt.Core.Configuration.Constants.CategoryNameHouseKeeping;

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{_categoryName}', @description = 'All HouseKeeping related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{_categoryName}'");
        }
    }
}
