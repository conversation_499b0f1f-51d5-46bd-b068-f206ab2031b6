namespace OPT.TransactionValidator.Models
{
    /// <summary>
    /// Model for validation results.
    /// </summary>
    public class TransactionValidationResult
    {
        /// <summary>
        /// Validation passed, process transaction as normal
        /// </summary>
        public bool IsSuccessful => !(IsTransactionCancelled || IsOptStopped || IsPumpStopped);

        /// <summary>
        /// Should the pump be stopped.
        /// </summary>
        public bool IsPumpStopped { get; internal set; }

        /// <summary>
        /// Should the OPT be stopped.
        /// </summary>
        public bool IsOptStopped { get; internal set; }

        /// <summary>
        /// Should the transaction be cancelled as zero.
        /// </summary>
        public bool IsTransactionCancelled { get; internal set; }
    }
}
