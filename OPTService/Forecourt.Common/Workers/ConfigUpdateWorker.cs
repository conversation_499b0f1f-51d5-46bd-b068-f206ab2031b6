using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Abstractions;
using System.Linq;
using System.Security;
using System.Timers;
using optCommonConstants = OPT.Common.Constants.ConfigurationConstants;

namespace OPT.Common.Workers
{
    public class ConfigUpdateWorker : Connectable, IConfigUpdateWorker
    {
        private readonly IDictionary<UpdateType, UpdateTypeInfo> _updateTypeInfo = new Dictionary<UpdateType, UpdateTypeInfo>();

        private const int SixMonths = 183;
        private const int TwoWeeks = 14;
        private const string SoftwareFilter = "OPTApp_*.pkg";
        private const string SecureAssetsFilter = "SecureAssets_*.pkg";
        private const string CpatAssetsFilter = "CpatAssets_*.pkg";
        private const string ApplicationFile = ConfigConstants.ServiceName + ".exe";
        private const string DatabaseFilter = "*.bak";
        private const string LogFilter = ".log";
        private const string TextFilter = ".txt";

        private IPaymentConfigIntegrator _paymentConfig => GetWorker<IPaymentConfigIntegrator>();
        private readonly IFileSystem _fileSystem;

        private IFromOptWorker _optWorker => GetWorker<IFromOptWorker>();
        private IControllerWorker _controllerWorker => GetWorker<IControllerWorker>();
       
        private volatile bool _pruneFiles;

        public int FilePruneDays { get; private set; }
        public int ReceiptPruneDays { get; private set; }
        public int TransactionPruneDays { get; private set; }

        public IEnumerable<string> WhitelistFiles => _updateTypeInfo[UpdateType.Whitelist].Files;
        public IEnumerable<string> LayoutFiles => _updateTypeInfo[UpdateType.Layout].Files;
        public IEnumerable<string> UpgradeFiles => _updateTypeInfo[UpdateType.UpgradeFile].Files;
        public IEnumerable<string> SoftwareFiles =>
            _updateTypeInfo[UpdateType.Software].Files.Concat(_updateTypeInfo[UpdateType.SecureAsset].Files.Concat(_updateTypeInfo[UpdateType.CpatAsset].Files));
        public IEnumerable<string> MediaFiles => _updateTypeInfo[UpdateType.Media].Files;
        public IEnumerable<string> PlaylistFiles => _updateTypeInfo[UpdateType.Playlist].Files;
        public IEnumerable<string> DatabaseBackupFiles => _updateTypeInfo[UpdateType.DatabaseBackup].Files;
        public IEnumerable<string> OptLogFiles => _updateTypeInfo[UpdateType.OptLog].Files;

        private const string CategoryNameHouseKeeping = Forecourt.Core.Configuration.Constants.CategoryNameHouseKeeping + Forecourt.Core.Configuration.Constants.CategorySeparator;

        /// <summary>
        /// Config Key for, any additional filters added for file (groups) that also need managing
        /// </summary>
        public const string ConfigKeyHouseKeepingServiceLogAdditionalFilters = CategoryNameHouseKeeping + "ServiceLog:Filters:Extra";

        /// <summary>
        /// Default value for, any additional filters added for file (groups) that also need managing
        /// </summary>
        public const string DefaultValueHouseKeepingServiceLogAdditionalFilters = "";

        /// <summary>
        /// Configurable value for, any additional filters added for file (groups) that also need managing
        /// </summary>
        protected ConfigurableString ConfigValueHouseKeepingServiceLogAdditionalFilters { get; private set; }

        /// <summary>
        /// Config Key for, the maximum number of files that are taken in account each cycle
        /// </summary>
        public const string ConfigKeyHouseKeepingMaxFiles = CategoryNameHouseKeeping + "FileCount:Maximum";

        /// <summary>
        /// Default value for, the maximum number of files that are taken in account each cycle
        /// </summary>
        public const int DefaultValueHouseKeepingMaxFiles = 500;

        /// <summary>
        /// Configurable value for, the maximum number of files that are taken in account each cycle
        /// </summary>
        protected ConfigurableInt ConfigValueHouseKeepingMaxFiles { get; private set; }


        #region Initialisation

        public ConfigUpdateWorker(IHydraDb hydraDb, IPaymentConfigIntegrator paymentConfig, IHtecLogger logger, IConfigurationManager configurationManager, ITimerFactory timerFactory, IFileSystem fileSystem)
            : base(hydraDb, logger, configurationManager, timerFactory: timerFactory)
        {
            if (timerFactory == null)
            {
                throw new ArgumentNullException(nameof(timerFactory));
            }

            RegisterWorker(paymentConfig ?? throw new ArgumentNullException(nameof(paymentConfig)));
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));

            var pruneDays = hydraDb?.GetPruneDays();
            FilePruneDays = pruneDays?.FilePruneDays ?? SixMonths;
            TransactionPruneDays = pruneDays?.TransactionPruneDays ?? SixMonths;
            ReceiptPruneDays = pruneDays?.ReceiptPruneDays ?? TwoWeeks;

            ConfigValueHouseKeepingServiceLogAdditionalFilters = new ConfigurableString(this, ConfigKeyHouseKeepingServiceLogAdditionalFilters, DefaultValueHouseKeepingServiceLogAdditionalFilters);
            ConfigValueHouseKeepingMaxFiles = new ConfigurableInt(this, ConfigKeyHouseKeepingMaxFiles, DefaultValueHouseKeepingMaxFiles);

            Initialise();
        }

        private void Initialise()
        {
            CreateUpdateTypes();

            var allFileLocations = HydraDb?.GetFileLocations();
            SetWhitelistDirectory(ControllerWorker.DirectoryString(allFileLocations?.WhitelistDirectory));
            SetLayoutDirectory(ControllerWorker.DirectoryString(allFileLocations?.LayoutDirectory));
            SetContactlessPropertiesFile(_paymentConfig.CurrentContactlessFile);
            SetMediaDirectory(ControllerWorker.DirectoryString(allFileLocations?.MediaDirectory));
            SetSoftwareDirectory(ControllerWorker.DirectoryString(allFileLocations?.SoftwareDirectory));
            SetUpgradeDirectory(ControllerWorker.DirectoryString(allFileLocations?.UpgradeFileDirectory));
            SetRollbackDirectory(ControllerWorker.DirectoryString(allFileLocations?.RollbackFileDirectory));
            SetPlaylistDirectory(ControllerWorker.DirectoryString(allFileLocations?.PlaylistDirectory));
            SetDatabaseBackupDirectory(ControllerWorker.DirectoryString(allFileLocations?.DatabaseBackupDirectory));
            SetOptLogsDirectory(ControllerWorker.DirectoryString(allFileLocations?.OptLogFileDirectory));
            SetLogDirectory(ControllerWorker.DirectoryString(allFileLocations?.LogFileDirectory));
            SetTracesDirectory(ControllerWorker.DirectoryString(allFileLocations?.TraceFileDirectory));
            SetJournalDirectory(ControllerWorker.DirectoryString(allFileLocations?.JournalFileDirectory));
            SetTransactionDirectory(ControllerWorker.DirectoryString(allFileLocations?.TransactionFileDirectory));
            SetRetalixTransactionDirectory(ControllerWorker.DirectoryString(allFileLocations?.RetalixTransactionFileDirectory));

            CheckEsocketConfigFile();
        }

        private void CreateUpdateTypes()
        {
            void Add(UpdateType updateType, Action checkAction = null, string filter = null, UpdateActionType updateAction = UpdateActionType.FileWatcher, bool removeFolders = false) =>
                _updateTypeInfo[updateType] = new UpdateTypeInfo(updateType, checkAction, filter, updateAction, removeFolders);

            var allLogFilter = $"*{LogFilter}";

            Add(UpdateType.Whitelist, CheckForWhitelistUpdates);
            Add(UpdateType.Layout, CheckForLayoutUpdates);
            Add(UpdateType.Contactless, CheckForContactlessUpdates);
            Add(UpdateType.Media, CheckForMediaUpdates);
            Add(UpdateType.Playlist, CheckForPlaylistUpdates);
            Add(UpdateType.DatabaseBackup, CheckForDatabaseBackupsUpdates, DatabaseFilter);
            Add(UpdateType.OptLog, CheckForOptLogsUpdates, $"OPT_{allLogFilter}", UpdateActionType.Both, true);
            Add(UpdateType.Software, CheckForSoftwareUpdates, SoftwareFilter);
            Add(UpdateType.SecureAsset, CheckForSoftwareUpdates, SecureAssetsFilter);
            Add(UpdateType.CpatAsset, CheckForSoftwareUpdates, CpatAssetsFilter);
            Add(UpdateType.UpgradeFile, CheckForUpgradeUpdates);
            Add(UpdateType.Rollback, CheckForRollbackUpdates, ApplicationFile);
            Add(UpdateType.RollbackCommon, filter: optCommonConstants.OPTCommonFilename);
            Add(UpdateType.eSocketConfig, CheckForEsocketUpdates);
            Add(UpdateType.eSocketKeyStore, CheckForEsocketUpdates);
            Add(UpdateType.ServiceLog, filter: $"{ConfigConstants.LoggerPrefixOptService.Replace(".", string.Empty)}{allLogFilter};HSC{allLogFilter};DbMigrations{allLogFilter};{ConfigValueHouseKeepingServiceLogAdditionalFilters.GetValue()}", updateAction: UpdateActionType.Prune, removeFolders: true);
            Add(UpdateType.TraceLog, filter: $"{ControllerWorker.TraceFileName.Replace(LogFilter, allLogFilter)}", updateAction: UpdateActionType.Prune);
            Add(UpdateType.JournalLog, filter: $"{ControllerWorker.JournalFileName.Replace(TextFilter, $"*{TextFilter}")}", updateAction: UpdateActionType.Prune);
            Add(UpdateType.TransactionFiles, updateAction: UpdateActionType.Prune);
            Add(UpdateType.RetalixTransactionFiles, updateAction: UpdateActionType.Prune);
        }

        protected override Result DoStart(params object[] startParams)
        {
            DoBackgroundTask(HasSomethingChanged,CreateWatchers, methodName: "Execute");

            return base.DoStart(startParams);
        }

        #endregion

        #region Private Helper Methods

        private void CheckDirectory(string directory)
        {
            try
            {
                _fileSystem.Directory.CreateDirectory(directory);
            }
            catch (Exception ex) when (ex is ArgumentNullException || ex is ArgumentException || ex is PathTooLongException ||
                                       ex is DirectoryNotFoundException || ex is IOException || ex is UnauthorizedAccessException ||
                                       ex is NotSupportedException || ex is SecurityException)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"Error creating directory {directory}"}, ex);
            }
        }

        private void SetDirectoryInfo(UpdateType updateType, string directory, string filter = null)
        {
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info,$"{updateType}", () => new[] { directory });

                if (string.IsNullOrWhiteSpace(directory))
                {
                    return;
                }

                var info = _updateTypeInfo[updateType];
                info.Directory = directory;
                if (!filter.IsNullOrWhiteSpace())
                {
                    info.Filter = filter;
                }
                CheckDirectory(directory);

                info.CheckForUpdates = info.UpdateAction.HasFlag(UpdateActionType.FileWatcher);

                if (info.UpdateAction.HasFlag(UpdateActionType.Prune))
                {
                    _pruneFiles = true;
                }
            }, null);
        }

        private string SetPruneDays(string what, int days, Action action)
        {
            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info,$"SettingPruneDays.{what}", () => new[] { $"{days}" });
                if (days < 1)
                {
                    return Result.Success("Invalid number of prune days");
                }

                action();

                _pruneFiles = true;
                _controllerWorker?.PushChange(EventType.AdvancedConfigChanged);
                return Result.Success<string>(null);
            }, null).Value;
        }

        #endregion

        #region Public Actions

        public void SetWhitelistDirectory(string directory) => SetDirectoryInfo(UpdateType.Whitelist, directory);

        public void SetLayoutDirectory(string directory) => SetDirectoryInfo(UpdateType.Layout, directory);

        public void SetContactlessPropertiesFile(string filename)
        {
            try
            {
                SetDirectoryInfo(UpdateType.Contactless, _fileSystem.Path.GetDirectoryName(filename), _fileSystem.Path.GetFileName(filename));
            }
            catch (ArgumentException ex)
            {
                DoDeferredLogging(LogLevel.Error,HeaderException, () => new[] {ex.Message}, ex);
            }
        }

        public void SetMediaDirectory(string directory) => SetDirectoryInfo(UpdateType.Media, directory);

        public void SetPlaylistDirectory(string directory) => SetDirectoryInfo(UpdateType.Playlist, directory);
      
        public void SetDatabaseBackupDirectory(string directory) => SetDirectoryInfo(UpdateType.DatabaseBackup, directory);

        public void SetLogDirectory(string directory) => SetDirectoryInfo(UpdateType.ServiceLog, directory);

        public void SetOptLogsDirectory(string directory) => SetDirectoryInfo(UpdateType.OptLog, directory);

        public void SetTracesDirectory(string directory) => SetDirectoryInfo(UpdateType.TraceLog, directory);

        public void SetJournalDirectory(string directory) => SetDirectoryInfo(UpdateType.JournalLog, directory);

        public void SetTransactionDirectory(string directory) => SetDirectoryInfo(UpdateType.TransactionFiles, directory);

        public void SetRetalixTransactionDirectory(string directory) => SetDirectoryInfo(UpdateType.RetalixTransactionFiles, directory);

        public void SetSoftwareDirectory(string directory)
        {
            SetDirectoryInfo(UpdateType.Software, directory);
            SetDirectoryInfo(UpdateType.SecureAsset, directory);
            SetDirectoryInfo(UpdateType.CpatAsset, directory);
        }

        public void SetUpgradeDirectory(string directory) => SetDirectoryInfo(UpdateType.UpgradeFile, directory);

        public void SetRollbackDirectory(string directory)
        {
            SetDirectoryInfo(UpdateType.Rollback, directory);
            SetDirectoryInfo(UpdateType.RollbackCommon, directory);
        }

        public string SetFilePruneDays(int days) => SetPruneDays("File", days, () =>
        {
            FilePruneDays = days;
            HydraDb?.SetFilePruneDays(FilePruneDays);
        });

        public string SetTransactionPruneDays(int days) => SetPruneDays("Transaction", days, () =>
        {
            TransactionPruneDays = days;
            HydraDb?.SetTransactionPruneDays(TransactionPruneDays);
        });

        public string SetReceiptPruneDays(int days) => SetPruneDays("Receipt", days, () =>
        {
            ReceiptPruneDays = days;
            HydraDb?.SetReceiptPruneDays(ReceiptPruneDays);
        });

        #endregion

        #region Checks

        private void CheckEsocketConfigFile()
        {
            try
            {
                var file = _paymentConfig.CurrentConfigFile;
                SetDirectoryInfo(UpdateType.eSocketConfig, _fileSystem.Path.GetDirectoryName(file), _fileSystem.Path.GetFileName(file));

                file = _paymentConfig.CurrentKeystoreFile;
                SetDirectoryInfo(UpdateType.eSocketKeyStore, _fileSystem.Path.GetDirectoryName(file), _fileSystem.Path.GetFileName(file));
            }
            catch (ArgumentException ex)
            {
                DoDeferredLogging(LogLevel.Error,HeaderException, () => new[] { ex.Message }, ex);
            }
        }

        private void CheckForWhitelistUpdates() => RunUpdateAction(UpdateType.Whitelist, (info) => _optWorker?.CheckWhitelistFiles());

        private void CheckForLayoutUpdates() => RunUpdateAction(UpdateType.Layout, (info) => _optWorker?.CheckLayoutFiles());

        private void CheckForContactlessUpdates() => RunUpdateAction(UpdateType.Contactless, (info) => _optWorker?.CheckContactlessProperties());
      
        private void CheckForMediaUpdates() => RunUpdateAction(UpdateType.Media, (info) => _optWorker?.CheckMediaFiles());
        
        private void CheckForPlaylistUpdates() => RunUpdateAction(UpdateType.Playlist);

        private void CheckForDatabaseBackupsUpdates() => RunUpdateAction(UpdateType.DatabaseBackup);

        private void CheckForOptLogsUpdates() =>
            RunUpdateAction(UpdateType.OptLog, postFilesActionOverride: (files) => files.OrderByDescending(fi => fi.CreationTime).Select(x => x.Name));
        
        private void CheckForSoftwareUpdates()
        {
            RunUpdateAction(UpdateType.Software);
            RunUpdateAction(UpdateType.SecureAsset);
            RunUpdateAction(UpdateType.CpatAsset, (info) =>_optWorker?.CheckSoftwareFiles());
        }

        private void CheckForUpgradeUpdates() => RunUpdateAction(UpdateType.UpgradeFile);
        
        private void CheckForRollbackUpdates()
        {
            RunUpdateAction(UpdateType.Rollback);
            RunUpdateAction(UpdateType.RollbackCommon, (info) => _optWorker?.CheckRollbackFiles());
        }

        private void CheckForEsocketUpdates()
        {
            RunUpdateAction(UpdateType.eSocketConfig);
            RunUpdateAction(UpdateType.eSocketKeyStore, (info) =>
            {
                if (!_paymentConfig.UseConnectionString)
                {
                    DoDeferredLogging(LogLevel.Info,"eSocket", () => new[] {"Constructing connection string"});
                    _paymentConfig.MakeConnection();

                    var file = _paymentConfig.CurrentKeystoreFile;
                    info.Directory = _fileSystem.Path.GetDirectoryName(file);
                    info.Filter = _fileSystem.Path.GetFileName(file);

                    SetContactlessPropertiesFile(_paymentConfig.CurrentContactlessFile);
                    _controllerWorker.SetEsocketChanged();
                }
            });
        }

        private void RunUpdateAction(UpdateType type, Action<UpdateTypeInfo> action = null, Func<IEnumerable<IFileInfo>, IEnumerable<string>> postFilesActionOverride = null)
        {
            var info = _updateTypeInfo[type];
            IEnumerable<string> PostFilesActionDefault(IEnumerable<IFileInfo> files) =>
                files.Select(x => x.FullName.Replace(info.Directory, string.Empty).Trim()).Where(x => !string.IsNullOrWhiteSpace(x)).ToList();

            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, $"CheckingDirectory.{info.UpdateType}", () => new[] { _fileSystem.Path.Combine(info.Directory, info.Filter) });

                action?.Invoke(info);

                info.Files.Clear();
                try
                {
                    if (info.Directory.IsNullOrWhiteSpace())
                    {
                        DoDeferredLogging(LogLevel.Warn, $"{info.UpdateType}", () => new[] { $"Directory info missing; Filter: {info.Filter}" });
                        return;
                    }

                    var dir = _fileSystem.DirectoryInfo.New(info.Directory);
                    var fileInfos = info.Filter.IsNullOrWhiteSpace() ? dir.EnumerateFiles() : dir.EnumerateFiles(info.Filter).ToList();

                    info.Files = (postFilesActionOverride?.Invoke(fileInfos) ?? PostFilesActionDefault(fileInfos)).ToList();
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"CheckingDirectory.{info.UpdateType}", ex.Message }, ex);
                }
                finally
                {
                    info.CheckForUpdates = false;
                }
            }, LoggingReference);
        }

        #endregion

        #region Main Loop

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            foreach (var info in _updateTypeInfo.Values.Where(x => x.UpdateAction.HasFlag(UpdateActionType.FileWatcher)))
            {
                info.CheckForUpdates = true;
            }

            _pruneFiles = true;
        }

        private Result CreateWatchers()
        {
            foreach (var info in _updateTypeInfo.Values.Where(x => x.UpdateAction.HasFlag(UpdateActionType.FileWatcher)))
            {
                CreateUpdateWatcher(info.UpdateType);
            }

            return Result.Success();
        }

        private Result HasSomethingChanged()
        {
            try
            {
                if (_updateTypeInfo.Values.Any(x => x.CheckForUpdates))
                {
                    foreach (var info in _updateTypeInfo.Values.Where(x => x.CheckForUpdates))
                    {
                        var result = CheckForUpdates(info);
                        if (!result.IsSuccess)
                        {
                            return result;
                        }
                    }

                    _controllerWorker?.PushChange(EventType.AboutChanged);
                }

                if (_pruneFiles)
                {
                    foreach (var info in _updateTypeInfo.Values.Where(x => x.UpdateAction.HasFlag(UpdateActionType.Prune)))
                    {
                        PruneFiles(info, FilePruneDays);
                    }

                    HydraDb?.PruneReceipts(ReceiptPruneDays, LoggingReference);
                    HydraDb?.PruneTransactions(TransactionPruneDays, LoggingReference);
                    _pruneFiles = false;
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error,HeaderException, () => new[] {ex.Message}, ex);
            }

            return Result.Success();
        }

        private FileSystemWatcher CreateUpdateWatcher(UpdateType updateType)
        {
            var info = _updateTypeInfo[updateType];
            var path = info.Directory;

            var watcher = info.FileWatcher;
            if (watcher == null)
            {
                watcher = new FileSystemWatcher();
                watcher.Changed += info.Handler;
                watcher.Created += info.Handler;
                watcher.Renamed += new RenamedEventHandler(info.Handler);
                watcher.Deleted += info.Handler;

                info.FileWatcher = watcher;
            }

            if (info.Filter != null)
            {
                watcher.Filter = info.Filter;
            }

            if (path != null && _fileSystem.Directory.Exists(path))
            {
                watcher.Path = path;
                watcher.EnableRaisingEvents = true;
            }

            return watcher;
        }

        private void PruneFiles(UpdateTypeInfo info, int days)
        {
            DoAction(() =>
            {
                var directory = info.Directory;
                if (directory == null || !_fileSystem.Directory.Exists(directory))
                {
                    return;
                }

                var dir = _fileSystem.DirectoryInfo.New(directory);
                var checkDate = DateTime.Now.AddDays(-days);
                DoDeferredLogging(LogLevel.Info,"Directory", () => new[] {$"{directory}; DaysOld: {days};  CheckDate: {checkDate:dd/MM/yyyy HH:mm:ss.fff}"});

                var files = (info.Filter.IsNullOrWhiteSpace() ?
                    dir.EnumerateFiles(info.Filter.IsNullOrWhiteSpace() ? "*" : info.Filter, SearchOption.AllDirectories) :
                    info.Filters.AsParallel().SelectMany(x => dir.EnumerateFiles(x, SearchOption.AllDirectories))).Where(x => x.LastWriteTime < checkDate)
                    .Take(ConfigValueHouseKeepingMaxFiles.GetValue()).ToList();

                foreach (var file in files)
                {
                    try
                    {
                        file.Delete();
                        DoDeferredLogging(LogLevel.Info, "DeletedFile", () => new[] { $"{file.Name}; From {file.LastWriteTime:dd/MM/yyyy HH:mm:ss.fff}" });
                    }
                    catch (Exception ex)
                    {
                        DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                    }
                }

                if (info.RemoveFolders)
                {
                    var dirs = dir.EnumerateDirectories("*", SearchOption.AllDirectories).OrderBy(x => x.GetDirectories().Count()).ToList();
                    foreach (var d in dirs)
                    {
                        try
                        {
                            var sd = d.EnumerateDirectories("*", SearchOption.AllDirectories); 
                            var f = d.EnumerateFiles();
                            if (!(sd.Any() || f.Any()))
                            {
                                d.Delete();
                                DoDeferredLogging(LogLevel.Info, "DeletedFolder", () => new[] { d.FullName });
                            }
                        }
                        catch (Exception ex)
                        {
                            DoDeferredLogging(LogLevel.Error, $"{HeaderException}.Folder", () => new[] { $"{d.FullName}; Error: {ex.Message}" }, ex);
                        }
                    }
                }
            }, LoggingReference);
        }

        private Result CheckForUpdates(UpdateTypeInfo info)
        {
            try
            {
                var directory = info.Directory;
                var watcher = info.FileWatcher;

                watcher.EnableRaisingEvents = false;
                info.CheckAction?.Invoke();

                if (directory != null && _fileSystem.Directory.Exists(directory))
                {
                    watcher.Path = directory;
                    watcher.EnableRaisingEvents = true;
                }
            }
            catch (Exception ex)
            {
                return Result.Failure($"Error watching directory: {info.Directory}; Exception: {ex.Message}");
            }

            return Result.Success();
        }

        #endregion

        #region Embedded types and clases

        private enum UpdateType
        {
            Whitelist,
            Layout,
            Contactless,
            Media,
            Playlist,
            DatabaseBackup,
            OptLog,
            Software,
            SecureAsset,
            CpatAsset,
            UpgradeFile,
            Rollback,
            RollbackCommon,
            eSocketConfig,
            eSocketKeyStore,
            ServiceLog,
            TraceLog,
            JournalLog,
            TransactionFiles,
            RetalixTransactionFiles
        }

        [Flags]
        private enum UpdateActionType
        {
            None = 0,
            FileWatcher = 1,
            Prune = 2,
            Both = 3
        }

        private class UpdateTypeInfo
        {
            public UpdateTypeInfo(UpdateType updateType, Action checkAction = null, string filter = null, UpdateActionType updateAction = UpdateActionType.FileWatcher, bool removeFolders = false)
            {
                UpdateType = updateType;
                CheckAction = checkAction;
                Filter = filter;
                UpdateAction = updateAction;

                CheckForUpdates = false;
                Files = new List<string>();
                Handler = (s, e) => CheckForUpdates = true;

                var ss = filter?.Split(new[] { ";" }, StringSplitOptions.None) ?? null;
                Filters = ss?.ToArray() ?? null;
                RemoveFolders = removeFolders;
            }

            public UpdateType UpdateType { get; }
            public string Directory { get; set; }
            public string Filter { get; set; }
            public IEnumerable<string> Filters { get; private set; }
            public bool CheckForUpdates { get; set; }
            public FileSystemWatcher FileWatcher { get; set; }
            public UpdateActionType UpdateAction { get; }
            public FileSystemEventHandler Handler { get; }
            public Action CheckAction { get; }
            public IList<string> Files { get; set; }

            public bool RemoveFolders { get; set; }
        }

        #endregion
    }
}
