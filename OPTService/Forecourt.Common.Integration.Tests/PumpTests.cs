using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;
using NSubstitute;
using System.Threading;
using Xunit;

namespace OPT.Common.Integration.Tests
{
    public class PumpTests
    {
        private const byte PumpNumber = 1;
        private const byte Grade = 2;
        private const byte OtherGrade = 1;
        private const byte Hose = 3;
        private const ushort Ppu = 1269;
        private const uint PaymentAmount = 10000;
        private const uint ClearedAmount = 5000;
        private const string Tid = "99979901";
        private const string CardNumber = "1234********6789";
        private const string CardProductName = "Visa";
        private const int PaymentTimeout = 1;
        private const string LoggerName = "Logger";
        private readonly IHydraDb _hydraDb;
        private readonly IOpt _opt;
        private readonly IPump _pump;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;

        public PumpTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _opt = Substitute.For<IOpt>();
            _logManager = Substitute.For<IHtecLogManager>();
            _opt.PaymentTimeoutInSeconds.Returns(PaymentTimeout);
            _configurationManager = Substitute.For<IConfigurationManager>();

            _pump = CreateDefaultPump();
            _pump.OpenPump(false);
        }


        [Fact]
        public void test_insert_card_send_anpr_and_timeout()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            Thread.Sleep(13000);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.CardCheck);
            _pump.IsSecAuthRequested.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_and_timeout()
        {
            // Arrange

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            Thread.Sleep(2000);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.Idle);
            _pump.IsSecAuthRequested.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeFalse();
            _pump.InUse.Should().BeFalse();
            _pump.CanInsertCard.Should().BeTrue();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        [Fact]
        public void test_insert_card_send_anpr_anpr_ok_add_payment_and_not_timeout()
        {
            // Arrange
            const int newTimeout = 3;

            // Act
            _pump.CardInserted();
            _pump.SecAuthRequestSent();
            _pump.SetSecAuthResponse(true);
            _pump.AddPayment(PaymentAmount);
            _pump.ResetPaymentTimeout(newTimeout);
            Thread.Sleep(2000);

            // Assert
            _pump.State.Should().Be(HydraPosPumpState.TakeFuel);
            _pump.IsSecAuthRequested.Should().BeTrue();
            _pump.IsSecAuthApproved.Should().BeTrue();
            _pump.HasPayment.Should().BeTrue();
            _pump.InUse.Should().BeTrue();
            _pump.CanInsertCard.Should().BeFalse();
            _pump.AuthorisedAmount.Should().Be(PaymentAmount);
        }

        #region Helpers

        private Pump CreateDefaultPump(byte pumpNumber = PumpNumber)
        {
            return new Pump(pumpNumber, _hydraDb, _logManager, LoggerName, _opt, Tid, _configurationManager);
        }

        #endregion   
    }
}
