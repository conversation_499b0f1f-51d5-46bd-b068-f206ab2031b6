using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models;
using Htec.Hydra.Messages.Opt.Xsd;
using OPT.Common.Models;
using System;
using System.Linq;

namespace OPT.Common
{
    ///
    /// Name: CompareConfig
    /// Description: Class for comparing configs to check for changes.
    ///
    public static class CompareConfig
    {
        #region Public Comparisons

        /// <summary>Compare two cards structures for equality.</summary>
        /// <param name="x">Structure to compare.</param>
        /// <param name="y">Structure to compare.</param>
        /// <returns>True if equal, false otherwise.</returns>
        public static bool CardsEqual(cards x, cards y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return AidsEqual(x.aids, y.aids) && ClessAidsEqual(x.cless_aids, y.cless_aids) && CapksEqual(x.capks, y.capks) &&
                   FuelCardsEqual(x.fuelcards, y.fuelcards) && TariffMappingsEqual(x.tariffMapping, y.tariffMapping) &&
                   DiscountCardsEqual(x.discountCards, y.discountCards) && LocalAccountsEqual(x.localAccounts, y.localAccounts);
        }

        private static bool DiscountCardsEqual(discountCardList x, discountCardList y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return DiscountRangesEqual(x.discountRange, y.discountRange);
        }

        /// <summary>Compare two loyalty structures for equality.</summary>
        /// <param name="x">Structure to compare.</param>
        /// <param name="y">Structure to compare.</param>
        /// <returns>True if equal, false otherwise.</returns>
        public static bool LoyaltyEqual(loyalty x, loyalty y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return MorrisonsEqual(x.morrisons, y.morrisons);
        }

        /// <summary>Compare two wash lists for equality.</summary>
        /// <param name="x">List to compare.</param>
        /// <param name="y">List to compare.</param>
        /// <returns>True if equal, false otherwise.</returns>
        public static bool WashesEqual(wash[] x, wash[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => WashEqual(a, b))) && y.All(a => x.Any(b => WashEqual(a, b)));
        }

        /// <summary>Compare two int lists for equality.</summary>
        /// <param name="x">List to compare.</param>
        /// <param name="y">List to compare.</param>
        /// <returns>True if equal, false otherwise.</returns>
        public static bool AmountsEqual(amount[] x, amount[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => a.value == b.value)) && y.All(a => x.Any(b => a.value == b.value));
        }

        #endregion

        #region Local Comparisons

        private static bool AidsEqual(aid[] x, aid[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => AidEqual(a, b))) && y.All(a => x.Any(b => AidEqual(a, b)));
        }

        private static bool AidEqual(aid x, aid y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.aid1, y.aid1) && StringEquals(x.appVerTerm, y.appVerTerm) && StringEquals(x.tacDefault, y.tacDefault) &&
                   StringEquals(x.tacDenial, y.tacDenial) && StringEquals(x.tacOnline, y.tacOnline) &&
                   StringEquals(x.partialMatch, y.partialMatch) && StringEquals(x.tdol, y.tdol) && StringEquals(x.ddol, y.ddol) &&
                   StringEquals(x.floorLimit, y.floorLimit) && StringEquals(x.emvTarget, y.emvTarget) &&
                   StringEquals(x.emvMaxTarget, y.emvMaxTarget) && StringEquals(x.emvThreshold, y.emvThreshold);
        }

        private static bool ClessAidsEqual(cless_aid[] x, cless_aid[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => ClessAidEqual(a, b))) && y.All(a => x.Any(b => ClessAidEqual(a, b)));
        }

        private static bool ClessAidEqual(cless_aid x, cless_aid y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.aid, y.aid) && StringEquals(x.appVerTerm, y.appVerTerm) && StringEquals(x.transLimit, y.transLimit) &&
                   StringEquals(x.floorLimit, y.floorLimit) && StringEquals(x.cvmLimit, y.cvmLimit) &&
                   StringEquals(x.odcvmLimit, y.odcvmLimit) && StringEquals(x.termAddCapabilities, y.termAddCapabilities) &&
                   StringEquals(x.termCapabilitiesCvm, y.termCapabilitiesCvm) &&
                   StringEquals(x.termCapabilitiesNoCvm, y.termCapabilitiesNoCvm) && StringEquals(x.termRiskData, y.termRiskData) &&
                   StringEquals(x.udol, y.udol) && StringEquals(x.tacDefault, y.tacDefault) && StringEquals(x.tacDenial, y.tacDenial) &&
                   StringEquals(x.tacOnline, y.tacOnline) && DrlsEqual(x.drl, y.drl);
        }

        private static bool DrlsEqual(drl[] x, drl[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => DrlEqual(a, b))) && y.All(a => x.Any(b => DrlEqual(a, b)));
        }

        private static bool DrlEqual(drl x, drl y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.programId, y.programId) && StringEquals(x.transLimit, y.transLimit) &&
                   StringEquals(x.floorLimit, y.floorLimit) && StringEquals(x.cvmLimit, y.cvmLimit);
        }

        private static bool CapksEqual(capk[] x, capk[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => CapkEqual(a, b))) && y.All(a => x.Any(b => CapkEqual(a, b)));
        }

        private static bool CapkEqual(capk x, capk y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.rid, y.rid) && StringEquals(x.index, y.index) && StringEquals(x.modulus, y.modulus) &&
                   StringEquals(x.exponent, y.exponent) && StringEquals(x.checksum, y.checksum) && StringEquals(x.expiryDate, y.expiryDate);
        }

        private static bool FuelCardsEqual(fuelcard[] x, fuelcard[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => FuelCardEqual(a, b))) && y.All(a => x.Any(b => FuelCardEqual(a, b)));
        }

        private static bool TariffMappingsEqual(gradeMapping[] x, gradeMapping[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => TariffMappingEqual(a, b))) && y.All(a => x.Any(b => TariffMappingEqual(a, b)));
        }

        private static bool DiscountRangesEqual(discountRange[] x, discountRange[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => DiscountRangeEqual(a, b))) && y.All(a => x.Any(b => DiscountRangeEqual(a, b)));
        }

        private static bool LocalAccountsEqual(localAccountCustomer[] x, localAccountCustomer[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => LocalAccountEqual(a, b))) && y.All(a => x.Any(b => LocalAccountEqual(a, b)));
        }

        private static bool FuelCardEqual(fuelcard x, fuelcard y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.iinStart, y.iinStart) && StringEquals(x.iinEnd, y.iinEnd) && StringEquals(x.onlinePin, y.onlinePin);
        }

        private static bool TariffMappingEqual(gradeMapping x, gradeMapping y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return x.grade == y.grade && StringEquals(x.productCode, y.productCode);
        }

        private static bool DiscountRangeEqual(discountRange x, discountRange y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.iin, y.iin) && StringEquals(x.name, y.name) && StringEquals(x.type, y.type) &&
                   StringEquals(x.value, y.value) && x.grade == y.grade && WhitelistEquals(x.whitelist, y.whitelist);
        }

        private static bool LocalAccountEqual(localAccountCustomer x, localAccountCustomer y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.name, y.name) && x.pin == y.pin && x.printValue == y.printValue && x.allowLoyalty == y.allowLoyalty &&
                   x.fuelOnly == y.fuelOnly && x.getMiles == y.getMiles && x.getReg == y.getReg && x.isPrePay == y.isPrePay &&
                   StringEquals(x.transLimit, y.transLimit) && CardlistEquals(x.cards, y.cards);
        }

        private static bool WhitelistEquals(whitelistCard[] x, whitelistCard[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => WhitelistCardEquals(a, b))) && y.All(a => x.Any(b => WhitelistCardEquals(a, b)));
        }

        private static bool WhitelistCardEquals(whitelistCard x, whitelistCard y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.pan, y.pan);
        }

        private static bool CardlistEquals(card[] x, card[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => CardEquals(a, b))) && y.All(a => x.Any(b => CardEquals(a, b)));
        }

        private static bool CardEquals(card x, card y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.pan, y.pan) && StringEquals(x.description, y.description) && StringEquals(x.discount, y.discount) &&
                   x.restrictions1 == y.restrictions1 && x.restrictions2 == y.restrictions2;
        }

        private static bool WashEqual(wash x, wash y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return x.programId == y.programId && StringEquals(x.productCode, y.productCode) && StringEquals(x.displayDescription, y.displayDescription) &&
                   StringEquals(x.price, y.price) && StringEquals(x.vatRate, y.vatRate);
        }

        private static bool StringEquals(string x, string y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return x.Equals(y);
        }

        private static bool MorrisonsEqual(morrisons x, morrisons y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return TerminalEqual(x.terminal, y.terminal) && HostsEqual(x.hosts, y.hosts) && IinsEqual(x.iins, y.iins) &&
                   MappingsEqual(x.tariffMapping, y.tariffMapping);
        }

        private static bool TerminalEqual(terminal x, terminal y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.siteId, y.siteId) && StringEquals(x.terminalId, y.terminalId) && StringEquals(x.footer1, y.footer1) &&
                   StringEquals(x.footer2, y.footer2);
        }

        private static bool HostsEqual(host[] x, host[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => HostEqual(a, b))) && y.All(a => x.Any(b => HostEqual(a, b)));
        }

        private static bool HostEqual(host x, host y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.ip, y.ip) && x.port == y.port;
        }

        private static bool IinsEqual(iin[] x, iin[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => IinEqual(a, b))) && y.All(a => x.Any(b => IinEqual(a, b)));
        }

        private static bool IinEqual(iin x, iin y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.low, y.low) && StringEquals(x.high, y.high);
        }

        private static bool MappingsEqual(mapping[] x, mapping[] y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            if (x.Length != y.Length)
            {
                return false;
            }

            return x.All(a => y.Any(b => MappingEqual(a, b))) && y.All(a => x.Any(b => MappingEqual(a, b)));
        }

        private static bool MappingEqual(mapping x, mapping y)
        {
            if (x == null && y == null)
            {
                return true;
            }

            if (x == null || y == null)
            {
                return false;
            }

            return StringEquals(x.productCode, y.productCode) && StringEquals(x.loyaltyCode, y.loyaltyCode);
        }

        public static bool HasConfigChanged(IOpt opt, configType config, AdvancedConfig advancedConfig, GenericOptConfig genericOptConfig)
        {
            return !Equals(config.cards, opt.SentConfig.cards)
                   || !Equals(config.loyalty, opt.SentConfig.loyalty)
                   || !Equals(config.carWash.wash, opt.SentConfig.carWash.wash)
                   || !AmountsEqual(config.opt.predefinedAmounts.amount, opt.SentConfig.opt.predefinedAmounts.amount)
                   || config.opt.receiptLayoutMode != opt.SentConfig.opt.receiptLayoutMode
                   || !string.Equals(config.site.name, opt.SentConfig.site.name)
                   || !string.Equals(config.site.vatNumber, opt.SentConfig.site.vatNumber)
                   || !Equals(config, opt.SentConfig)
                   || (advancedConfig.NozzleUpForKioskUse && advancedConfig.UseReplaceNozzleScreen) != (opt.SentConfig.opt.mixedModeKioskTriggerMode == 1)
                   || (advancedConfig.NozzleUpForKioskUse && !advancedConfig.UseReplaceNozzleScreen) != (opt.SentConfig.opt.mixedModeKioskTriggerMode == 2)
                   || advancedConfig.CurrencyCode != opt.SentConfig.site.currencyCode
                   || advancedConfig.MaxFillOverride != opt.SentConfig.site.maxFillOverride
                   || !string.Equals(advancedConfig.SiteType.ToString(), opt.SentConfig.site.type, StringComparison.OrdinalIgnoreCase)
                   || HasContactlessConfigChanged(opt.SentConfig.contactless, genericOptConfig.ContactlessConfiguration)
                   || HaveTimeoutsChanged(opt, genericOptConfig)
                   || genericOptConfig.EnhancedLogging != opt.SentConfig.logging.enhancedLogging;
        }

        private static bool HasContactlessConfigChanged(contactless contactless, ContactlessConfiguration configuration)
        {
            return configuration.HasContactless != contactless.enabled
                   || configuration.SingleContactlessButton != contactless.showSingleContactlessButton
                   || configuration.ContactlessCardPreAuthLimit != contactless.cardpreauthamount
                   || configuration.ContactlessDevicePreAuthLimit != contactless.devicepreauthamount
                   || !string.Equals(configuration.Ttq, contactless.ttq, StringComparison.OrdinalIgnoreCase);
        }

        private static bool HaveTimeoutsChanged(IOpt opt, GenericOptConfig genericOptConfig)
        {
            return genericOptConfig.OptPaymentTimeout != opt.SentConfig.opt.timeouts.opt
                   || genericOptConfig.PodPaymentTimeout != opt.SentConfig.opt.timeouts.pod
                   || genericOptConfig.MixedPaymentTimeout != opt.SentConfig.opt.timeouts.mixed
                   || genericOptConfig.KioskTimeout != opt.SentConfig.opt.timeouts.kiosk
                   || genericOptConfig.NozzleDownTimeout != opt.SentConfig.opt.timeouts.nozzleDown
                   || genericOptConfig.SecAuthTimeout != opt.SentConfig.opt.timeouts.secAuth;
        }


        #endregion
    }
}