using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Forecourt.Core.Pump.Models
{
    public class OnPumpStateActiveInfo
    {
        public OnPumpStateActiveInfo()
        {
            ActiveChannels = new ConcurrentDictionary<Opt.Enums.SocketType, bool>();
        }

        public OnPumpStateActiveInfo(Opt.Enums.SocketType channel, bool value) : this()
        {
            ActiveChannels[channel] = value;
        }

        public IDictionary<Opt.Enums.SocketType, bool> ActiveChannels { get; }

        public bool IsActive => ActiveChannels.Any(x => x.Value);
    }
}
