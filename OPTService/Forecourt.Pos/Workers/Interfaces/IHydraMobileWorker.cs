using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using System.Xml.Linq;

namespace Forecourt.Pos.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the IHydraMobileWorker, related to Payment/Mobile
    /// </summary>
    public interface IHydraMobileWorker : IClientWorker<XElement>,
        IPosIntegratorIn<IMessageTracking, IPump, Result>,
        IPosIntegratorOut<IMessageTracking>,
        IBosIntegratorInJournal<IMessageTracking>,
        IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>,
        IPosIntegratorIn<IMessageTracking, IPump, Result<StatusCodeResult>>,
        ISecAuthIntegratorInTransient<IMessageTracking>,
        IPumpIntegratorOutTransient<IMessageTracking>
    {
    }
}