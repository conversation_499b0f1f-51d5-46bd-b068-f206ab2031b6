import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { ShiftEndService } from 'src/app/services/shift-end.service';

import { ShiftEndComponent } from './shift-end.component';

describe('ShiftEndComponent', () => {
  let component: ShiftEndComponent;
  let serviceSpy: jasmine.SpyObj<ShiftEndService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  const fakeData = {
    DayEndTime: new Date(),
    NextDayEnd: new Date(),
    AsdaDayEndReport: false,
    ShiftEndTime: new Date(),
    Unmanned: false,
    PrinterDetails: {
      BaudRate: 9600,
      DataBits: 8,
      Enabled: true,
      Handshake: "",
      IsPrinterBusy: false,
      PortName: "COM1",
      StopBits: ""
    },
    LogInterval: {
      Hours: 1,
      Minutes: 2,
      Seconds: 3
    }
  };

  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('serviceSpy', ['getShiftEndInfo', 'setInfoAsSimpleGet', 'setPrinterDetails', 'performShiftEnd', 'performDayEnd', 'clearAutoDayEnd', 'setAsdaDayEndReportOn', 'setAsdaDayEndReportOff', 'setNextDayEnd', 'setLogInterval', 'setPrinterEnabled', 'setPrinterPortName', 'setPrinterBaudRate', 'setPrinterHandshake', 'setPrinterStopBits', 'setPrinterDataBits']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        ShiftEndComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: ShiftEndService, useValue: serviceObjSpy },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    serviceSpy = TestBed.inject(ShiftEndService) as jasmine.SpyObj<ShiftEndService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    component = TestBed.inject(ShiftEndComponent);
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
     jasmine.clock().install();
    //Arrange
    serviceSpy.getShiftEndInfo.and.returnValue(of(fakeData));
    spyOn(component.shiftEndForm, 'patchValue').and.stub();

    //Act
    component.ngOnInit();
    jasmine.clock().tick(50);

    //Assert
    expect(component.shiftEndForm.patchValue).toHaveBeenCalledTimes(2);
    jasmine.clock().uninstall();
  });

  describe('.refreshData()', () => {

    it('.refreshData() should handle success response from service', () => {
      serviceSpy.getShiftEndInfo.and.returnValue(of(fakeData));

      component.refreshData();

      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
    });

    it('.refreshData() should handle unsuccess response from service', () => {
      serviceSpy.getShiftEndInfo.and.returnValue(throwError({status:500}));

      component.refreshData();

      expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
      expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
    });
  });

  it('.performShiftEnd() should handle success response from service', () => {
    //Arrange
    serviceSpy.performShiftEnd.and.returnValue(of({}));

    //Act
    component.performShiftEnd();

    //Assert
    expect(serviceSpy.performShiftEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.performShiftEnd() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.performShiftEnd.and.returnValue(throwError({ status: 500 }))

    //Act
    component.performShiftEnd();

    //Assert
    expect(serviceSpy.performShiftEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.performDayEnd() should handle success response from service', () => {
    //Arrange
    serviceSpy.performDayEnd.and.returnValue(of({}));

    //Act
    component.performDayEnd();

    //Assert
    expect(serviceSpy.performDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.performDayEnd() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.performDayEnd.and.returnValue(throwError({ status: 500 }))

    //Act
    component.performDayEnd();

    //Assert
    expect(serviceSpy.performDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.clearAutoDayEnd() should handle success response from service', () => {
    //Arrange
    serviceSpy.clearAutoDayEnd.and.returnValue(of({}));

    //Act
    component.clearAutoDayEnd();

    //Assert
    expect(serviceSpy.clearAutoDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.clearAutoDayEnd() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.clearAutoDayEnd.and.returnValue(throwError({ status: 500 }))

    //Act
    component.clearAutoDayEnd();

    //Assert
    expect(serviceSpy.clearAutoDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setAsdaDayEndReportOn() should handle success response from service', () => {
    //Arrange
    serviceSpy.setAsdaDayEndReportOn.and.returnValue(of({}));

    //Act
    component.setAsdaDayEndReportOn();

    //Assert
    expect(serviceSpy.setAsdaDayEndReportOn).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setAsdaDayEndReportOn() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setAsdaDayEndReportOn.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setAsdaDayEndReportOn();

    //Assert
    expect(serviceSpy.setAsdaDayEndReportOn).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setAsdaDayEndReportOff() should handle success response from service', () => {
    //Arrange
    serviceSpy.setAsdaDayEndReportOff.and.returnValue(of({}));

    //Act
    component.setAsdaDayEndReportOff();

    //Assert
    expect(serviceSpy.setAsdaDayEndReportOff).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setAsdaDayEndReportOff() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setAsdaDayEndReportOff.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setAsdaDayEndReportOff();

    //Assert
    expect(serviceSpy.setAsdaDayEndReportOff).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setNextDayEnd() should handle success response from service', () => {
    //Arrange
    serviceSpy.setNextDayEnd.and.returnValue(of({}));

    //Act
    component.setNextDayEnd();

    //Assert
    expect(serviceSpy.setNextDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setNextDayEnd() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setNextDayEnd.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setNextDayEnd();

    //Assert
    expect(serviceSpy.setNextDayEnd).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setLogInterval() should handle success response from service', () => {
    //Arrange
    serviceSpy.setLogInterval.and.returnValue(of({}));
    component.shiftEndDetails = fakeData;

    //Act
    component.setLogInterval();

    //Assert
    expect(serviceSpy.setLogInterval).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setLogInterval() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setLogInterval.and.returnValue(throwError({ status: 500 }));
    component.shiftEndDetails = fakeData;
    

    //Act
    component.setLogInterval();

    //Assert
    expect(serviceSpy.setLogInterval).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterEnabled() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterEnabled.and.returnValue(of({}));
    component.shiftEndDetails = fakeData;

    //Act
    component.setPrinterEnabled();

    //Assert
    expect(serviceSpy.setPrinterEnabled).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterEnabled() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterEnabled.and.returnValue(throwError({ status: 500 }));
    component.shiftEndDetails = fakeData;
    

    //Act
    component.setPrinterEnabled();

    //Assert
    expect(serviceSpy.setPrinterEnabled).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterPortName() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterPortName.and.returnValue(of({}));

    //Act
    component.setPrinterPortName();

    //Assert
    expect(serviceSpy.setPrinterPortName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterPortName() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterPortName.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setPrinterPortName();

    //Assert
    expect(serviceSpy.setPrinterPortName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterBaudRate() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterBaudRate.and.returnValue(of({}));

    //Act
    component.setPrinterBaudRate();

    //Assert
    expect(serviceSpy.setPrinterBaudRate).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterBaudRate() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterBaudRate.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setPrinterBaudRate();

    //Assert
    expect(serviceSpy.setPrinterBaudRate).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterHandshake() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterHandshake.and.returnValue(of({}));

    //Act
    component.setPrinterHandshake();

    //Assert
    expect(serviceSpy.setPrinterHandshake).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterHandshake() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterHandshake.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setPrinterHandshake();

    //Assert
    expect(serviceSpy.setPrinterHandshake).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterStopBits() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterStopBits.and.returnValue(of({}));

    //Act
    component.setPrinterStopBits();

    //Assert
    expect(serviceSpy.setPrinterStopBits).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterStopBits() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterStopBits.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setPrinterStopBits();

    //Assert
    expect(serviceSpy.setPrinterStopBits).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterDataBits() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrinterDataBits.and.returnValue(of({}));

    //Act
    component.setPrinterDataBits();

    //Assert
    expect(serviceSpy.setPrinterDataBits).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
  });

  it('.setPrinterDataBits() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrinterDataBits.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setPrinterDataBits();

    //Assert
    expect(serviceSpy.setPrinterDataBits).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });
});
