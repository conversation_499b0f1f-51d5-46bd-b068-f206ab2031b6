export const environment = {
  production: false,
  optServiceBase: '/api/',
  optController: {
    base: 'Opt/',
    getOptInfo: 'GetOptInfo',
    setReceiptHeader: 'SetReceiptHeader',
    setReceiptFooter: 'setReceiptFooter',
    getGenericOptConfig: 'GetGenericOptConfig',
    setSetGenericServiceAddress : 'SetServiceAddress',
    addEsocket: 'AddEsocket',
    removeEsocket : 'RemoveEsocket',
    addTariffMapping: 'SetTariffMappingsAddMapping',
    removeTariffMapping : 'SetTariffMappingsRemoveMapping',
    RestartOpt: 'RestartOpt',
    SendConfigPending: 'SendConfigPending',
    SendRequestLogFile: 'SendRequestLogFile',
    SetContactlessAllowed: 'SetContactlessAllowed',
    SetPlaylistFileName: 'SetPlaylistFileName',
    setTariffMappingFuelCardsOnly : 'SetTariffMappingFuelCardsOnly',
    addDiscountCard: 'AddDiscountCard',
    removeDiscountCard: 'RemoveDiscountCard',
    addWash: 'AddWash',
    removeWashByProgramId: 'RemoveWashByProgramId',
    addPredefinedAmount: 'AddPredefinedAmount',
    removePredefinedAmount: 'RemovePredefinedAmount',
    setReceiptReprintAvailability: 'SetReceiptReprintAvailability',
    setReceiptMaxCount: 'SetReceiptMaxCount',
    setReceiptLayoutMode: 'SetReceiptLayoutMode',
    reloadOptConfiguration: 'ReloadOptConfiguration',
    startConfigBatch: 'StartConfigBatch',
    stopConfigBatch: 'StopConfigBatch',
    setContactlessCardPreAuth: 'SetContactlessCardPreAuth',
    setContactlessDevicePreAuth: 'SetContactlessDevicePreAuth',
    setContactlessTtq: 'SetContactlessTtq',
  },
  pumpController: {
    base: 'Pump/',
    getPumpInfo: 'GetPumpInfo',
    MapToTid: 'MapToTid',
    MapToOpt: 'MapToOpt',
    SetDefaultMode: 'SetDefaultMode',
    ClosePump: 'ClosePump',
    ForceClosePump: 'ForceClosePump',
    SetMaxFillOverrideForFuelCards: 'SetMaxFillOverrideForFuelCards',
    SetMaxFillOverrideForPaymentCards: 'SetMaxFillOverrideForPaymentCards',
    isPumpControllerEnabled: 'IsPumpControllerEnabled'
  },
  tidController: {
    base: 'Tid/',
    getTidList: 'GetTidList'
  },
  advancedController: {
    base: 'Advanced/',
    getMaxFillOverride: 'GetMaxFillOverride',
    getAdvancedConfig: 'GetAdvancedConfiguration',
    setAutoAuth: 'SetAutoAuth',
    setMediaChannel: 'SetMediaChannel',
    setUnmannedPseudoPos: 'SetUnmannedPseudoPos',
    setSiteName: 'SetSiteName',
    setVatNumber: 'SetVatNumber',
    setCurrencyCode: 'SetCurrencyCode',
    setPaymentTimeout: 'SetPaymentTimeout',
    setTillNumber: 'SetTillNumber',
    setFuelCategory: 'SetFuelCategory',
    setPosClaimNumber: 'SetPosClaimNumber',
    setFilePruneDays: 'SetFilePruneDays',
    setTransactionPruneDays: 'SetTransactionPruneDays',
    setReceiptPruneDays: 'SetReceiptPruneDays',
    setFuellingIndefiniteWait: 'SetFuellingIndefiniteWait',
    setFuellingWaitMinutes: 'SetFuellingWaitMinutes',
    setFuellingBackoffAuth: 'SetFuellingBackoffAuth',
    setFuellingBackoffPreAuth: 'SetFuellingBackoffPreAuth',
    setFuellingBackoffStopStart: 'SetFuellingBackoffStopStart',
    setFuellingBackoffStopOnly: 'SetFuellingBackoffStopOnly',
    setForwardFuelPriceUpdate: 'SetForwardFuelPriceUpdate',
    setCardReference: 'SetCardReference',
    clearCardReference: 'ClearCardReference',
    setAcquirerReference: 'SetAcquirerReference',
    clearAcquirerReference: 'ClearAcquirerReference',
    setFuelCard: 'SetFuelCard',
    setExternalName: 'SetExternalName',
    clearExternalName: 'ClearExternalName',
    getIsConfigBatch: 'GetIsConfigBatch',
    setCategories: 'setCategories',
    setIntegrationType: 'SetIntegrationType',
    setNozzleUpForKioskUse: 'SetNozzleUpForKioskUse',
    setUseReplaceNozzleScreen: 'SetUseReplaceNozzleScreen',
    setMaxFillOverride: 'SetMaxFillOverride',
    clearMaxFillOverride: 'ClearMaxFillOverride',
    setEsocketUseConnectionString: 'setEsocketUseConnectionString',
    setEsocketConnectionString: 'setEsocketConnectionString',
    setEsocketOverrideProperties: 'setEsocketOverrideProperties',
    setEsocketConfigFile: 'setEsocketConfigFile',
    setEsocketOverrideKeystore: 'setEsocketOverrideKeystore',
    setEsocketKeystoreFile: 'setEsocketKeystoreFile',
    setEsocketOverrideUrl: 'setEsocketOverrideUrl',
    setEsocketDbUrl: 'setEsocketDbUrl',
  },
  loyaltyController:{
    base: 'Loyalty/',
    addLoyalty: 'AddLoyalty',
    deleteLoyalty: 'DeleteLoyalty',
    setLoyaltyPresent: 'SetLoyaltyPresent',
    setLoyaltyTerminalSiteId: 'SetLoyaltyTerminalSiteId',
    setLoyaltyTerminalTerminalId: 'SetLoyaltyTerminalTerminalId',
    setLoyaltyTerminalFooter1: 'SetLoyaltyTerminalFooter1',
    setLoyaltyTerminalFooter2: 'SetLoyaltyTerminalFooter2',
    setLoyaltyTerminalTimeout: 'SetLoyaltyTerminalTimeout',
    setLoyaltyTerminalApiKey: 'SetLoyaltyTerminalApiKey',
    setLoyaltyTerminalHttpHeader: 'SetLoyaltyTerminalHttpHeader',
    setLoyaltyHostsAddHost: 'SetLoyaltyHostsAddHost',
    setLoyaltyHostsRemoveHost: 'SetLoyaltyHostsRemoveHost',
    setLoyaltyAddHostname: 'SetLoyaltyAddHostname',
    setLoyaltyRemoveHostname: 'SetLoyaltyRemoveHostname',
    setLoyaltyIinsAddIin: 'SetLoyaltyIinsAddIin',
    setLoyaltyIinsRemoveIin: 'SetLoyaltyIinsRemoveIin',
    setLoyaltyMappingsAddMapping: 'SetLoyaltyMappingsAddMapping',
    setLoyaltyMappingsRemoveMapping: 'SetLoyaltyMappingsRemoveMapping'
  },
  transactionController:{
    base: 'Transaction/',
    getFuelTransactions: 'GetFuelTransactions',
    getReceipt: 'GetReceipt',
    getOtherEvents: 'GetOtherEvents',
    printReceipt: 'PrintReceipt',
    saveReceipt: 'SaveReceipt'
  },
  fileLocationsController: {
    base: 'FileLocation/',
    getFileLocations: 'GetFileLocations',
    setContactlessPropertiesFile: 'setContactlessPropertiesFile',
    setRetalixTransactionFileDirectory: 'setRetalixTransactionFileDirectory',
    setTransactionFileDirectory: 'setTransactionFileDirectory',
    setWhitelistDirectory: 'setWhitelistDirectory',
    setLayoutDirectory: 'setLayoutDirectory',
    setSoftwareDirectory: 'setSoftwareDirectory',
    setFuelDataUpdateFile: 'setFuelDataUpdateFile',
    setUpgradeFileDirectory: 'setUpgradeFileDirectory',
    setRollbackFileDirectory: 'setRollbackFileDirectory',
    setOptLogFileDirectory: 'setOptLogFileDirectory',
    setLogFileDirectory: 'setLogFileDirectory',
    setTraceFileDirectory: 'setTraceFileDirectory',
    setJournalFileDirectory: 'setJournalFileDirectory',
    setReceivedUpdateDirectory: 'setReceivedUpdateDirectory',
    setDatabaseBackupDirectory: 'setDatabaseBackupDirectory',
    setEsocketOverrideContactless: 'setEsocketOverrideContactless',
    setMediaDirectory: 'setMediaDirectory',
    setPlaylistDirectory: 'setPlaylistDirectory'
  },
  fuelPriceController: {
    base: 'FuelPrice/',
    getFuelPrices: 'GetFuelPrices',
    setPrice: 'SetPrice',
    setGradeName: 'SetGradeName',
    setGradeVatRate: 'SetGradeVatRate'
  },
  connectionController: {
    base: 'Connection/',
    getConnections: 'GetConnections',    
    setFromOptPort: 'setFromOptPort',
    setToOptPort: 'setToOptPort',
    setHeartbeatPort: 'setHeartbeatPort',    
    setHydraPosPort: 'setHydraPosPort',
    setRetalixPosPrimaryIpAddress: 'setRetalixPosPrimaryIpAddress',
    setRetalixPosPort: 'setRetalixPosPort',
    setThirdPartyPosPort: 'setThirdPartyPosPort',
    setMediaChannelPort: 'setMediaChannelPort',
    setPumpControllerAddress: 'setPumpControllerAddress',
    setPumpControllerLogonInfo: 'setPumpControllerLogonInfo',
    setAnprAddress: 'setAnprAddress',
    setAnprPort: 'setAnprPort',
    setCarWashAddress: 'setCarWashAddress',
    setCarWashPort: 'setCarWashPort',
    setTankGaugeAddress: 'setTankGaugeAddress',
    setHydraMobileAddress: 'setHydraMobileAddress',
    setHydraMobilePort: 'setHydraMobilePort',
    removeRetalixPosPrimaryIpAddress: 'removeRetalixPosPrimaryIpAddress'
  },
  divertController:{
    base: 'Divert/',
    getDivertDetails: 'getDivertDetails',
    divertOptService: 'divertOptService',
    cancelDivertOptService: 'cancelDivertOptService'
  },
  infoMessagesController: {
    base: 'InfoMessages/',
    getInfoMessageDetails: 'getInfoMessageDetails'
  },
  shiftEndController: {
    base: 'Shift/',
    getShiftEndInfo: 'GetShiftEndInfo',
    performDayEnd: 'PerformDayEnd',
    performShiftEnd: 'PerformShiftEnd',
    clearAutoDayEnd: 'ClearAutoDayEnd',
    setNextDayEnd: 'SetNextDayEnd',
    setLogInterval: 'SetLogInterval',
    setAsdaDayEndReportOn: 'SetAsdaDayEndReportOn',
    setAsdaDayEndReportOff: 'SetAsdaDayEndReportOff',
    setPrinterEnabled: 'SetPrinterEnabled',
    setPrinterPortName: 'SetPrinterPortName',
    setPrinterBaudRate: 'SetPrinterBaudRate',
    setPrinterHandshake: 'SetPrinterHandshake',
    setPrinterStopBits: 'SetPrinterStopBits',
    setPrinterDataBits: 'SetPrinterDataBits'
  },  
  webController: {
    base: 'Web/',
    GetVersionInfo: 'getVersionInfo',
    UpgradeService: 'upgradeService',
    RollbackService: 'rollbackService',
    RestartService: 'restartService',
    SaveFile: 'saveFile',
    SaveSoftwareFile: 'saveSoftwareFile',
    SaveWhitelistFile: 'saveWhitelistFile',
    SaveLayoutFile: 'saveLayoutFile',
    SaveMediaFile: 'saveMediaFile',
    SavePlaylistFile: 'savePlaylistFile',
    SaveContactlessPropertiesFile: 'saveContactlessPropertiesFile',
    RemoveFile: 'removeFile',
    RemoveWhitelistFile: 'removeWhitelistFile',
    RemoveLayoutFile: 'removeLayoutFile',
    RemoveUpgradeFile: 'removeUpgradeFile',
    RemoveSoftwareFile: 'removeSoftwareFile',
    RemoveMediaFile: 'removeMediaFile',
    RemovePlaylistFile: 'removePlaylistFile',
    RestoreDatabase: 'restoreDatabase',
    RemoveDatabaseBackupFile: 'removeDatabaseBackupFile',
    BackupDatabase: 'backupDatabase',
    RemoveOptLogFile: 'removeOptLogFile',
    UploadFile: 'uploadFile',
    UploadZipFile: 'uploadZipFile'
  },
  localAccountController:{
    base: 'LocalAccount/',
    getLocalAccounts: 'getLocalAccounts',
    addLocalAccountCustomer: 'addLocalAccountCustomer',
    removeLocalAccountCustomer: 'removeLocalAccountCustomer',
    setLocalAccountsEnabled: 'setLocalAccountsEnabled',
    setLocalAccountCustomerBalance: 'setLocalAccountCustomerBalance',
    clearLocalAccountCustomerBalance: 'clearLocalAccountCustomerBalance',
    addLocalAccountCard: 'addLocalAccountCard',
    removeLocalAccountCard: 'removeLocalAccountCard',
    setLocalAccountCardHot: 'setLocalAccountCardHot'
  }
};
