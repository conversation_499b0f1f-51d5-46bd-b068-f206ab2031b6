# Powershell Utility to analyse OPTService log files and extract all completed transactions for a given site and date
# Extracted data to include the given date and the preceding day
# Purpose: To enable the volume and value of transactions to be summarized in Excel
# Author: <PERSON> g<PERSON>.<EMAIL>
# Version: 0.1

# Accepts the following command line parameters
# 1. folder path containing subfolders by date (usually including the name of the site)
# 2. date for which the data extract is required
param (
    [Parameter(Mandatory=$true)][string]$sitefolder = "C:\Users\<USER>\Downloads\MFG-FS2628-SITE_2024-07-11",
    [Parameter(Mandatory=$true)][Datetime]$extractdate = 11/7/2024
 )
 
# Reads all the files for the specified site/date
$hydraOPTService = "HydraOPTService"
$datefolder = Join-Path ( Join-Path $sitefolder $extractDate.ToString("yyyy-MM-dd")) $hydraOPTService
$prevdatefolder = Join-Path ( Join-Path $sitefolder $extractDate.AddDays(-1).ToString("yyyy-MM-dd")) $hydraOPTService
$outputFile = Join-Path $datefolder "OPTServiceTransactionData.csv"

$initialGradeRegex = "(?<logDate>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}(?:,\d{3}\b)?).*((FpId: )(?<pump>\d+)(; ))((Active SMId: )(?<smId>\d+)(; )).*((LockId: )(?<lockId>\d+)(; ))((GradeId: )(?<initialGrade>\d+)(; )).*((GradeOption: )(?<gradeOption>\d+)(; ))"

# add a row of content to the output file
# the content has already been successfully parsed into groups using a regex
 function Add-CSVContent {
 
	param ( $groups, $initialGrades )

	if ($null -ne $groups) {

		$lockId = $groups['lockId'].Value
		if (($lockId -eq 75) -or ($lockId -eq 1)) {
			# from the list of initial grades, extract only those where logDate <= transaction.logDate
			$initialGradeSublist = @()
			$initialGrades | ForEach-Object {
				$igc = [regex]::Matches($_, $initialGradeRegex)
				if ($igc.Success -eq $true) {
					$optionDate = [datetime]::ParseExact($igc[0].Groups['logDate'].Value, 'yyyy-MM-dd HH:mm:ss,fff', $null)
					$txDate = [datetime]::ParseExact($groups['logDate'].Value, 'yyyy-MM-dd HH:mm:ss,fff', $null)
					if ($optionDate -le $txDate) {
						$initialGradeSublist += $_
					}
				}
			}	
			# the initial grade is take from the last item in the sublist, which is closest in time to the transaction
			$initialGrade = -1
			$gradeOption = -1
			if ($initialGradeSublist.length -gt 0) {
				$igc2 = [regex]::Matches($initialGradeSublist[-1], $initialGradeRegex)
				$initialGrade = $igc2[0].Groups['initialGrade'].Value
				$gradeOption = $igc2[0].Groups['gradeOption'].Value
			}
		
			$fpTransBuf = $groups['fpTransBuf'].Value
			$volume = $groups['vol'].Value / 100
			$cash = $groups['cash'].Value / 100
			$rowContent = "$($groups['logDate'].Value),$($fpTransBuf),$($groups['pump'].Value),$($groups['trans'].Value),$($groups['seqNum'].Value),$($groups['smId'].Value),$($lockId),$($cash),$($volume),$($groups['grade'].Value),$initialGrade,$gradeOption"
			Add-Content -Path $outputFile -Value $rowContent
		}
	}
}

# create empty content array
$content = @()
# filter to include only desired files
$filefilter = "OPTService*.log"
# filter to include only desired rows
$rowfilter = "(DomsMessageReader.Fp).*(TransBuf)"

# for the initial grade data
$initialGradeContent = @()
$initialGradeRowFilter = "(DomsMessageReader.FpStatus).*( State: Calling)"

# process the previous day files first
Get-ChildItem $prevdatefolder -Filter $filefilter  | 
Foreach-Object {
	# lists the files as they are processed
	write-output $_.FullName
    $content += Get-Content $_.FullName | Select-String -pattern $rowfilter
	$initialGradeContent += Get-Content $_.FullName | Select-String -pattern $initialGradeRowFilter
}

# then add the requested date files
Get-ChildItem $datefolder -Filter $filefilter  | 
Foreach-Object {
	# lists the files as they are processed
	write-output $_.FullName
    $content += Get-Content $_.FullName | Select-String -pattern $rowfilter
	$initialGradeContent += Get-Content $_.FullName | Select-String -pattern $initialGradeRowFilter
}

# convert $initialGradeContent to a hash of lists of objects by pump
$initialGradeHash = @{}
$initialGradeContent | ForEach-Object {
	$igc = [regex]::Matches($_, $initialGradeRegex)
	if ($igc.Success -eq $true) {
		$pump = $igc[0].Groups['pump'].Value
		if ($initialGradeHash.ContainsKey("$($pump)")) {
			$initialGradeHash["$($pump)"] += $_
		}
		else {
			$initialGradeHash["$($pump)"] = @($_)
		}
	}
}

# Outputs the result as a CSV file with a header line
 
#Remove the CSV if exists
If (Test-Path -Path $outputFile) { Remove-Item -Path $outputFile -Force}
 
#Add Header to the CSV file
$header = "logDate,ms,fpTransBuf,pump,trans,seqNum,smId,lockId,cash,volume,grade,initialGrade,gradeOption"
Add-Content -Path $outputFile -Value $header

# Extracts the relevant data as a list of objects (key/value pairs?)
$regex1 = "(?<logDate>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}(?:,\d{3}\b)?).*((DomsMessageReader\.)(?<fpTransBuf>.*))\.Status\.((FpId: )(?<pump>\d+)(; ))((Trans: )(?<trans>\d+)(; ))((SeqNum: )(?<seqNum>\d+)(; ))((SMId: )(?<smId>\d+)(; ))((LockId: )(?<lockId>\d+)(; )).*((Cash: )(?<cash>\d+)(; ))((Volume: )(?<vol>\d+)(; ))((Grade: )(?<grade>\d+))"
$regex2 = "(?<logDate>\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}(?:,\d{3}\b)?).*((DomsMessageReader\.)(?<fpTransBuf>.*))\.Status\.((FpId: )(?<pump>\d+)(; ))((Trans: )(?<trans>\d+)(; ))((SeqNum: )(?<seqNum>\d+)(; ))((SMId: )(?<smId>\d+)(; ))((LockId: )(?<lockId>\d+)(; )).*((Volume: )(?<vol>\d+)(; ))((Cash: )(?<cash>\d+)(; ))((Grade: )(?<grade>\d+))"
$content | ForEach-Object {
	$m = [regex]::Matches($_, $regex1)
	if ($m.Success -eq $true ) {
		$initialGradeList = $initialGradeHash["$($m[0].Groups['pump'].Value)"]
		Add-CSVContent -groups $m[0].Groups -initialGrades $initialGradeList
	}
	else {
		$m2 = [regex]::Matches($_, $regex2)
		if ($m2.Success -eq $true ) {
			$initialGradeList2 = $initialGradeHash["$($m2[0].Groups['pump'].Value)"]
			Add-CSVContent -groups $m2[0].Groups -initialGrades $initialGradeList2
		}
	}
}

write-output "$($outputFile) done."
