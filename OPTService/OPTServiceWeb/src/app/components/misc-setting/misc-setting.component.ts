import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { MiscConfigLevel } from 'src/app/core/enums/miscConfigLevel.enum';
import { ConfigurationCategory } from 'src/app/core/models/configurationCategory.model';
import { Setting } from 'src/app/core/models/setting.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';

@Component({
  selector: 'app-misc-setting',
  templateUrl: './misc-setting.component.html',
  styleUrls: ['./misc-setting.component.css']
})
export class MiscSettingComponent implements OnInit {

  @HostBinding('attr.id') externalId = null;
  @Input() id: number;
  @Input() category: string;
  @Input() categoryId: number;
  @Input() key: any;
  @Input() setting: Setting;

  valueError: boolean = false;

  form = this.fb.group({
    Key: [''],
    Value: ['']
  });

  constructor(private advancedConfigService: AdvancedConfigService, private fb: FormBuilder,private logger: NGXLogger) { }

  /**
   * The getter method for the form
   */
  get formControl() {
    return this.form.controls;
  }

  /**
   * Updates the setting value
   */
  updateValue(event?: Event): void {
    ButtonHelper.clicked(event);
    let value = this.setting.value['Value'];
    this.logger.info('Updating ' + this.category + '.' + this.key  + ' = ' + value );
    this.sendUpdate(value, event);
  }

  revertValue(event?: Event): void {
    ButtonHelper.clicked(event);
    let value:any;
    if (this.setting.value['Level'] == MiscConfigLevel.GlobalOverride){
      value = this.setting.value['DefaultValue'];
    } else {
      value = this.setting.value['GlobalOverrideValue'];
    }
    this.logger.info('Reverting ' + this.category + '.' + this.key  + ' = ' + value );
    this.sendUpdate(value, event);
  }

  private sendUpdate(value: any, event: Event): void{
    // Compose request body
    let configurationCategories : Array<ConfigurationCategory> = [<ConfigurationCategory>({
      Category : this.category, 
      ID: this.categoryId,
      Settings : { [this.key]: {Value : value} }
    })];
    
    this.advancedConfigService.setCategories(configurationCategories).subscribe(() => {
      this.logger.debug(this.category + '.' + this.key + ' updated ok');
      this.valueError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating ' + this.category + '.' + this.key);
      this.valueError = true;
      ButtonHelper.reset(event);
    });
  }

  ngOnInit(): void {
    this.form.patchValue({
      Key: this.key,
      Value: this.setting.value['Value']
    });
    Object.keys(this.form.controls).forEach(key => {
      this.form.controls[key].valueChanges.subscribe(value => {
        console.log('** ' + this.key + ' ' + this.setting.value['Value']);
        this.setting.value['Value']  = value;
      });
    });
  }

}
