using System;
using System.Collections.Generic;

namespace Forecourt.Core.Opt.Interfaces
{
    public interface IOptCollection
    {
        int AllConnectedCount { get; }
        int ToOptConnectedCount { get; }
        int FromOptConnectedCount { get; }
        int HeartbeatConnectedCount { get; }
        IEnumerable<IOpt> AllOpts { get; }
        IEnumerable<IOpt> AllOptsAllConnected { get; }
        string GlobalOptStringId { get; }

        int LogInterval { get; }

        /// <summary>
        /// Get the OPT with the given ID if present.
        /// </summary>
        /// <param name="optId">OPT ID to search for.</param>
        /// <param name="opt">OPT with the given ID, or null if not present.</param>
        /// <returns>True if OPT is found, false otherwise.</returns>
        bool TryGetOpt(int optId, out IOpt opt);

        /// <summary>
        /// Get the OPT with the given ID String if present.
        /// </summary>
        /// <param name="idString">ID String to search for.</param>
        /// <returns>OPT with the given ID String or null if not found.</returns>
        IOpt GetOptForIdString(string idString);

        IEnumerable<TOpt> GetAllOpts<TOpt>() where TOpt : IOptCore;

        IEnumerable<TOpt> GetAllOptsAllConnected<TOpt>() where TOpt : IOptCore;

        /// <summary>
        /// Get the OPT with the given ID if present.
        /// </summary>
        /// <param name="optId">OPT ID to search for.</param>
        /// <param name="opt">OPT with the given ID, or null if not present.</param>
        /// <returns>True if OPT is found, false otherwise.</returns>
        bool TryGetOpt<TOpt>(int optId, out TOpt opt) where TOpt : IOptCore;
        /// <summary>
        /// Get the OPT with the given ID String if present.
        /// </summary>
        /// <param name="idString">ID String to search for.</param>
        /// <param name="ctor">Action to create a custom OPT</param>
        /// <returns>OPT with the given ID String or null if not found.</returns>
        TOpt GetOptForIdString<TOpt>(string idString, Func<string, HydraDb.Models.OptMode, TOpt> ctor = null) where TOpt : IOptCore;

        /// <summary>
        /// Get the ID of the OPT with the given ID String if present.
        /// </summary>
        /// <param name="idString">ID String to search for.</param>
        /// <returns>ID of the OPT with the given ID String or zero if not found.</returns>
        int GetOptIdForIdString(string idString);

        void SetLogInterval(int interval);

        /// <summary>
        /// Provides the global HydraId so it can ripple down into AllOpts and the MessageBuilder
        /// </summary>
        /// <param name="hydraId"></param>
        void SetHydraId(string hydraId);
    }
}