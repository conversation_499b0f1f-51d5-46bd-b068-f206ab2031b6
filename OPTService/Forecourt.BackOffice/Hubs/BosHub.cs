using CSharpFunctionalExtensions;
using Forecourt.Bos.Hubs.Interfaces;
using Forecourt.Bos.HydraDb.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Bos.Messages.SignalR;
using Microsoft.AspNet.SignalR.Hubs;
using System;
using System.Runtime.CompilerServices;

namespace Forecourt.Bos.Hubs
{
    /// <summary>
    /// Publishes the requests from <see cref="IBosIntegratorOut{IMessageTracking}"/> to the outside world
    /// </summary>
    [HubName(HubName)]
    public class BosHub: Hub<BosMessage, IMessageTracking>, IBosHub, IBosHubIn
    {
        /// <summary>
        /// Name of the Pos SignalR Hub
        /// </summary>
        public const string HubName = "Htec-Hubs:Forecourt-Service:Bos";

        private readonly IHydraDb _hydraDb;

        /// <inheritdoc/>
        public BosHub(IHubbableIn<BosMessage, IMessageTracking> hubbable, IHydraDb hydraDb) : base(hubbable)
        {
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
        }

        /// <inheritdoc/>
        public void TransactionBooked(CompleteBooking request)
        {
            DoTransactionBooked(request, Guid.Parse(request.MessageId));
        }

        /// <inheritdoc/>
        public void TransactionBooked(CompleteBooking request, Guid retryMessageId)
        {
            DoTransactionBooked(request, retryMessageId);
        }

        private void DoTransactionBooked(CompleteBooking request, Guid retryMessageId, [CallerMemberName] string methodName = null)
        { 
            Hubbable.ProcessRequest(Context.ConnectionId, request.MessageId, request, (tracking, cb, logger, notifier) =>
            {
                var result = _hydraDb.GetTransactionBooking(cb.TransId, cb.TxnNumber, message: tracking);
                if (!result.IsSuccess)
                {
                    return Result.Failure($"BookingTransaction NotFound: TransId: {cb.TransId}; TxnNumber: {cb.TxnNumber}");
                }

                var resultComplete = Result.Success();
                var booking = result.Value;
                if (booking != null)
                {
                    if (booking.BookedDate == DateTime.MinValue)
                    {
                        resultComplete = _hydraDb.CompleteTransactionBooking(booking.Id, booking.TransactionId, request.BookedDate, tracking);
                        notifier?.SendInformation($"Transaction Booked into External System: TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate:dd/MM/yyyy}");
                    }
                }

                return resultComplete;
            }, methodName);
        }
    }
}
