using Forecourt.BackOffice.Workers.Interfaces;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.BackOffice.Workers
{
    /// <summary>
    /// Any and all capabilities of the RestAPI back-office systems (Bos)
    /// </summary>
    public abstract class RestApiBosOutWorker : BosOutWorker, IRestApiBosOutWorker
    {
        /// <inheritdoc/>
        protected RestApiBosOutWorker(IHtecLogManager logManager, string loggerName, IHydraDb hydraDb, IConfigurationManager configurationManager = null) : base(logManager, loggerName, configurationManager, hydraDb)
        {
        }
    }
}
