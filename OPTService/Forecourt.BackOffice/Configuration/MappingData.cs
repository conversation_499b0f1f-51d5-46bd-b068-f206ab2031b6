using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Forecourt.Bos.Configuration
{
    /// <summary>
    /// Maps an Entity from one sub-system to another
    /// </summary>
    /// <typeparam name="TId">Id of source type</typeparam>
    /// <typeparam name="TSource">Source model</typeparam>
    /// <typeparam name="TTarget">Target model</typeparam>
    public class MappingData<TId, TSource, TTarget>
    {
        /// <summary>
        /// Class to hold mapping item data
        /// </summary>
        public class MappingDataItem
        {
            /// <summary>
            /// Main constructor
            /// </summary>
            /// <param name="source">Source item</param>
            /// <param name="target">Target item</param>
            /// <param name="isDefault">Is default (source) item</param>
            public MappingDataItem(TSource source, TTarget target, bool isDefault = false)
            {
                Source = source;
                Target = target;
                IsDefault = isDefault;
            }

            /// <summary>
            /// Source item
            /// </summary>
            public TSource Source { get; set; }

            /// <summary>
            /// Target item
            /// </summary>
            public TTarget Target { get; set; }

            /// <summary>
            /// Is default (source) item
            /// </summary>
            public bool IsDefault { get; set; }
        }

        /// <summary>
        /// Dictionary of source ids, to a <see cref="Tuple{TSource,TTarget}"/> of values
        /// </summary>
        public ConcurrentDictionary<TId, MappingDataItem> Map { get; } = new ConcurrentDictionary<TId, MappingDataItem>();

        /// <summary>
        /// List of source values
        /// </summary>
        public IEnumerable<TSource> SourceValues => Map.Values.Where(x => x.Source != null).Select(x => x.Source);

        /// <summary>
        /// List of target values
        /// </summary>
        public IEnumerable<TTarget> TargetValues => Map.Values.Where(x => x.Target != null).Select(x => x.Target);

        /// <summary>
        /// Source Default, if a match could not be found
        /// </summary>
        public TSource DefaultSource
        {
            get
            {
                var result = Map.Values.Where(x => x.Source != null).FirstOrDefault(x => x.IsDefault) ?? Map.Values.FirstOrDefault(x => x.Source != null);
                return result != null ? result.Source : default;
            }
        }

        /// <summary>
        /// Target Default, if a match could not be found
        /// </summary>
        public TTarget DefaultTarget
        {
            get
            {
                var result = Map.Values.Where(x => x.Target != null).FirstOrDefault(x => x.IsDefault) ?? Map.Values.FirstOrDefault(x => x.Target != null);
                return result != null ? result.Target : default;
            }
        }

        /// <summary>
        /// Adds an item to the mapping dictionary
        /// </summary>
        /// <param name="id">Source id</param>
        /// <param name="source">Source item</param>
        /// <param name="target">Target item</param>
        /// <returns><see cref="MappingDataItem"/> instance</returns>
        public MappingDataItem Add(TId id, TSource source, TTarget target = default)
        {
            var item = new MappingDataItem(source, target);
            Map[id] = item;
            return item;
        }
    }
}
