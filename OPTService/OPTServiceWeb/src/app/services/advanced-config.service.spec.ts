import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { AdvancedConfigService } from './advanced-config.service';
import { environment } from 'src/environments/environment';
import { AdvancedConfigProperty } from '../core/enums/advancedConfigProperty.enum';
import { AdvancedConfig } from '../core/models/advancedConfig.model';
import { CardProperty } from '../core/enums/cardProperty.enum';
import { CardReferenceDetails } from '../core/models/cardReferenceDetails.model';
import { DOCUMENT } from '@angular/common';
import { ESocketPosConfigDetails } from '../core/models/eSocketPosConfigDetails.model';
import { PaymentTimeoutType } from '../core/enums/paymentTimeoutType.enum';

describe('AdvancedConfigService', () => {
  let advancedConfigService: AdvancedConfigService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  let API_SET_PREFIX = 'set';
  let API_CLEAR_PREFIX = 'clear';
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        AdvancedConfigService,
        {provide: NGXLogger, useValue: ngxLoggertSpy},
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    advancedConfigService = TestBed.inject(AdvancedConfigService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(advancedConfigService).toBeTruthy();
  });

  it('.getAdvancedConfig() should handle success response from API', () => {
    //Arrange
    let mockedAdvancedConfig: AdvancedConfig = {
      AsdaDayEndReport: false,
      AutoAuth: true,
      CardReferences: [{
        Acquirer: "Visa,Mastercard,Delta&Electron",
        FuelCard: false,
        InUse: false,
        Name: "VISA",
        Reference: 1,
        External: "VISA2"
      }],
      FilePruneDays: 30,
      ForwardFuelPriceUpdate: true,
      FuelCategory: 99,
      FuellingBackoffAuth: 0,
      FuellingBackoffPreAuth: 0,
      FuellingBackoffStopOnly: 0,
      FuellingBackoffStopStart: 0,
      FuellingIndefiniteWait: true,
      FuellingWaitMinutes: 5,
      LoyaltyAvailable: ["Morrisons"],
      MediaChannel: true,
      MorrisonLoyaltyAvailable: true,
      PaymentTimeoutMixed: 15,
      PaymentTimeoutNozzleDown: 15,
      PaymentTimeoutOpt: 15,
      PaymentTimeoutPod: 300,
      TimeoutKiosk: 15,
      TimeoutSecAuth: 12,
      PosClaimNumber: 99,
      ReceiptMaxCount: 12,
      ReceiptPruneDays: 14,
      ReceiptTimeout: 3600,
      TillNumber: 99,
      TransactionPruneDays: 50,
      UnmannedPseudoPos: true,
      NozzleUpForKioskUse: false,
      UseReplaceNozzleScreen: false,
      MaxFillOverride: 0,
      SiteType: 'Retail',
      SiteName: '',
      VatNumber: '',
      CurrencyCode: 0,
      LocalAccountsEnabled: false,
      ConfigurationCategories: [],
      ESocketPosConfig: {} as ESocketPosConfigDetails,
      PosType: 'Hydra',
      PosTypes: [{ Key: 'Hydra/VBO.Net', Value: 'HYDRA' }, { Key: 'Morrions Bos', Value: 'RETALIX' }, { Key: 'None', Value: 'NONE' }],
      PumpType: 'HSC',
      PumpTypes: [{ Key: 'HSC', Value: 'HSC' }, { Key: 'DOMS', Value: 'DOMS' }],
      MobilePaymentType: 'NONE',
      MobilePaymentTypes: [{ Key: 'None', Value: 'NONE' }, { Key: 'HydraMobile', Value: 'HYDRAMOBILE' }],
      MobilePosType: 'None',
      MobilePosTypes: [{ Key: 'None', Value: 'NONE' }, { Key: 'HydraMobile', Value: 'HYDRAMOBILE' }],
      BosType: 'HYDRA',
      BosTypes: [{ Key: 'Hydra', Value: 'HYDRA' }, { Key: 'Retalix', Value: 'RETALIX' }, { Key: 'None', Value: 'NONE' }],
      SecAuthType: 'HYDRA',
      SecAuthTypes: [{ Key: 'Hydra ANPR', Value: 'HYDRA' }, { Key: 'None', Value: 'NONE' }],
      PaymentConfigType: 'ESOCKET',
      PaymentConfigTypes: [{ Key: 'eSocket-POS', Value: 'ESOCKET' }, { Key: 'None', Value: 'NONE' }]
    };
    
    //Act
    advancedConfigService.getAdvancedConfig().subscribe((advancedConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(advancedConfig).toEqual(mockedAdvancedConfig);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.getAdvancedConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedAdvancedConfig);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getAdvancedConfig() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.getAdvancedConfig().subscribe(advancedConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.getAdvancedConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setProperty() should handle success response from API', () => {
    //Arrange
    let targetProperty = AdvancedConfigProperty.MediaChannel;
    let newValue = new AdvancedConfig();
    newValue.MediaChannel = true;
        
    //Act
    advancedConfigService.setProperty(targetProperty,newValue).subscribe((advancedConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(advancedConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_SET_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setProperty() should handle unsuccess response from API', () => {
    //Arrange
    let targetProperty = AdvancedConfigProperty.MediaChannel;
    let newValue = new AdvancedConfig();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setProperty(targetProperty,newValue).subscribe(advancedConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_SET_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPaymentTimeout() should handle success response from API', () => {
    //Arrange
    let targetProperty = AdvancedConfigProperty.PaymentTimeoutKiosk;
    let newValue = new AdvancedConfig();
    newValue.TimeoutKiosk = 30;

    //Act
    advancedConfigService.setPaymentTimeout(PaymentTimeoutType.Kiosk, newValue, targetProperty).subscribe((advancedConfig) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(advancedConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + 'SetPaymentTimeout?type=Kiosk&value=30');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPaymentTimeout() should handle unsuccess response from API', () => {
    //Arrange
    let targetProperty = AdvancedConfigProperty.PaymentTimeoutMixed;
    let newValue = new AdvancedConfig();
    newValue.PaymentTimeoutMixed = 45;
    let errorMsg = 'Deliberate 500 error';

    //Act
    advancedConfigService.setPaymentTimeout(PaymentTimeoutType.Mixed, newValue, targetProperty).subscribe(advancedConfig => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + 'SetPaymentTimeout?type=Mixed&value=45');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setCardProperty() should handle success response from API', () => {
    //Arrange
    let targetProperty = CardProperty.Acquirer;
    let newValue = new CardReferenceDetails();
    newValue.Reference = 1;
    newValue.Acquirer = 'The new acquirer';
        
    //Act
    advancedConfigService.setCardProperty(targetProperty,newValue).subscribe((advancedConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(advancedConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_SET_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setCardProperty() should handle unsuccess response from API', () => {
    //Arrange
    let targetProperty = CardProperty.Acquirer;
    let newValue = new CardReferenceDetails();
    newValue.Reference = 1;
    newValue.Acquirer = 'The new acquirer';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setCardProperty(targetProperty,newValue).subscribe(advancedConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_SET_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.clearCardProperty() should handle success response from API', () => {
    //Arrange
    let targetProperty = CardProperty.Acquirer;
    let oldValue = new CardReferenceDetails();
    oldValue.Reference = 1;
        
    //Act
    advancedConfigService.clearCardProperty(targetProperty,oldValue).subscribe((advancedConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(advancedConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_CLEAR_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setCardProperty() should handle unsuccess response from API', () => {
    //Arrange
    let targetProperty = CardProperty.Acquirer;
    let oldValue = new CardReferenceDetails();
    oldValue.Reference = 1;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.clearCardProperty(targetProperty,oldValue).subscribe(advancedConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController[API_CLEAR_PREFIX + targetProperty]);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setSiteName() should handle success response from API', () => {
    //Arrange
    let siteName = 'New name';
        
    //Act
    advancedConfigService.setSiteName(siteName).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setSiteName);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setSiteName() should handle unsuccess response from API', () => {
    //Arrange
    let siteName = 'New name';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setSiteName(siteName).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setSiteName);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setVatNumber() should handle success response from API', () => {
    //Arrange
    let vatNumber = '12345';
        
    //Act
    advancedConfigService.setVatNumber(vatNumber).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setVatNumber);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setVatNumber() should handle unsuccess response from API', () => {
    //Arrange
    let vatNumber = '12345';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setVatNumber(vatNumber).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setVatNumber);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setCurrencyCode() should handle success response from API', () => {
    //Arrange
    let currencyCode = 'GBP';
        
    //Act
    advancedConfigService.setCurrencyCode(currencyCode).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setCurrencyCode);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setCurrencyCode() should handle unsuccess response from API', () => {
    //Arrange
    let currencyCode = 'GBP';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setCurrencyCode(currencyCode).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setCurrencyCode);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setNozzleUpForKioskUse() should handle success response from API', () => {
    //Arrange
    let nozzleUpForKioskUse = true;
        
    //Act
    advancedConfigService.setNozzleUpForKioskUse(nozzleUpForKioskUse).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setNozzleUpForKioskUse);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setNozzleUpForKioskUse() should handle unsuccess response from API', () => {
    //Arrange
    let nozzleUpForKioskUse = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setNozzleUpForKioskUse(nozzleUpForKioskUse).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setNozzleUpForKioskUse);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setUseReplaceNozzleScreen() should handle success response from API', () => {
    //Arrange
    let useReplaceNozzleScreen = true;
        
    //Act
    advancedConfigService.setUseReplaceNozzleScreen(useReplaceNozzleScreen).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setUseReplaceNozzleScreen);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setUseReplaceNozzleScreen() should handle unsuccess response from API', () => {
    //Arrange
    let useReplaceNozzleScreen = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setUseReplaceNozzleScreen(useReplaceNozzleScreen).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setUseReplaceNozzleScreen);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setMaxFillOverride() should handle success response from API', () => {
    //Arrange
    let maxFillOverride = 100;
        
    //Act
    advancedConfigService.setMaxFillOverride(maxFillOverride).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setMaxFillOverride);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setMaxFillOverride() should handle unsuccess response from API', () => {
    //Arrange
    let maxFillOverride = 100;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.setMaxFillOverride(maxFillOverride).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.setMaxFillOverride);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.clearMaxFillOverride() should handle success response from API', () => {
    //Arrange
        
    //Act
    advancedConfigService.clearMaxFillOverride().subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.clearMaxFillOverride);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.clearMaxFillOverride() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    advancedConfigService.clearMaxFillOverride().subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.advancedController.base + environment.advancedController.clearMaxFillOverride);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

});
