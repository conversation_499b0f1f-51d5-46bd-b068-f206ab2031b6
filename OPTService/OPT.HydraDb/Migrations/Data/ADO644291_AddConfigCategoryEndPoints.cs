using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set add ENDPOINTS Configuraion Category (to be used by Magical Advanced Config keys)
    /// </summary>
    [Migration(20050009, "ADO#644291 - add ENDPOINTS Configuraion Category (to be used by advanced config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO644291_AddConfigCategoryEndPoints : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameEndPointsApi}', @description = 'All Endpoints Api related options'");
            Execute.Sql($"EXEC dbo.AddConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameEndPointsSignalR}', @description = 'All Endpoints SignalR related options'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameEndPointsApi}'");
            Execute.Sql($"EXEC dbo.RemoveConfigurationCategory @category = '{Forecourt.Core.Configuration.Constants.CategoryNameEndPointsSignalR}'");
        }
    }
}
