using CSharpFunctionalExtensions;
using Forecourt.Common.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.System.Diagnostics.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Workers;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Reflection;
using FileVersionInfo = Htec.Foundation.Models.FileVersionInfo;
using optCommonConstants = OPT.Common.Constants.ConfigurationConstants;

namespace Forecourt.Common.Helpers
{
    public class ServiceFilesHelper : Workerable, IServiceFilesHelper
    {
        private readonly IFileSystem _fileSystem;
        private readonly IFileVersionInfoFactory _fileInfoVersionFactory;
        private readonly IFileVersionInfoHelper _fileVersionInfo;

        private IControllerWorker ControllerWorker => GetWorker<IControllerWorker>();
        private IConfigUpdateWorker ConfigUpdateWorker => GetWorker<IConfigUpdateWorker>();

        public ServiceFilesHelper(IHtecLogManager logManager, IConfigurationManager configurationManager, IControllerWorker controllerWorker, IConfigUpdateWorker configUpdateWorker, 
            IPumpIntegratorConfiguration pumpConfig, IFileSystem fileSystem, IFileVersionInfoFactory fileVersionInfoFactory, IFileVersionInfoHelper fileVersionInfo) : base(logManager, nameof(ServiceFilesHelper), configurationManager)        
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _fileInfoVersionFactory = fileVersionInfoFactory ?? throw new ArgumentNullException(nameof(fileVersionInfoFactory));
            _fileVersionInfo = fileVersionInfo ?? throw new ArgumentNullException(nameof(fileVersionInfo));

            RegisterWorkers(controllerWorker ?? throw new ArgumentNullException(nameof(controllerWorker)));
            RegisterWorker(configUpdateWorker ?? throw new ArgumentNullException(nameof(configUpdateWorker)));
            RegisterWorker(pumpConfig ?? throw new ArgumentNullException(nameof(pumpConfig)));
        }

        /// <inheritdoc />
        public IEnumerable<string> UploadedFileNames => ControllerWorker.UploadedFileNames;

        /// <inheritdoc />
        public IEnumerable<string> WhitelistFiles => ConfigUpdateWorker.WhitelistFiles;

        /// <inheritdoc />
        public IEnumerable<string> LayoutFiles => ConfigUpdateWorker.LayoutFiles;

        /// <inheritdoc />
        public IEnumerable<string> UpgradeFiles => ConfigUpdateWorker.UpgradeFiles;

        /// <inheritdoc />
        public IEnumerable<string> SoftwareFiles => ConfigUpdateWorker.SoftwareFiles;

        /// <inheritdoc />
        public IEnumerable<string> MediaFiles => ConfigUpdateWorker.MediaFiles;

        /// <inheritdoc />
        public IEnumerable<string> PlaylistFiles => ConfigUpdateWorker.PlaylistFiles;

        /// <inheritdoc />
        public IEnumerable<string> DatabaseBackupFiles => ConfigUpdateWorker.DatabaseBackupFiles;

        /// <inheritdoc />
        public IEnumerable<string> OptLogFiles => ConfigUpdateWorker.OptLogFiles;

        /// <inheritdoc />
        public IEnumerable<FileVersionInfo> Current => GetFileSet();

        /// <inheritdoc />
        public IEnumerable<FileVersionInfo> Rollback => GetFileSet(ControllerWorker.RollbackFileDirectory, "rollback");

        /// <inheritdoc />
        public IEnumerable<FileVersionInfo> Upgrade => GetFileSet(ControllerWorker.UpgradeFileDirectory, "upgrade");

        /// <inheritdoc />
        public IEnumerable<FileVersionInfo> PumpIntegrator => GetWorker<IPumpIntegratorConfiguration>().FileVersions;

        /// <inheritdoc />
        public IEnumerable<FileVersionInfo> OfflineFileService
        {
            get
            {
                var info = DoExtractFileVersionInfo("Htec.HydraOpt.OfflineTransactionService.exe", ConfigurationManager.AppSettings["OfflineTransactionServicePath"], "OTFService");

                if (info != null)
                {
                    yield return info;
                }
            }
        }

        /// <inheritdoc />
        public Result<IFileVersionInfoWrapper> ExtractAssemblyVersionInfoWrapper(string targetFile, string targetFolder = null, string targetName = "current")
        {
            return DoAction(() =>
            {
                try
                {
                    targetFolder = ResolveTargetFolder(targetFolder);

                    var assemblyFile = _fileSystem.Path.Combine(targetFolder, targetFile);

                    if (!_fileSystem.File.Exists(assemblyFile))
                    {
                        return Result.Failure<IFileVersionInfoWrapper>($"Assembly file {assemblyFile} not found");
                    }

                    return Result.Success(_fileInfoVersionFactory.GetFileVersionInfoWrapper(assemblyFile));
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Error looking up {targetName} assembly" }, ex);
                    return Result.Failure<IFileVersionInfoWrapper>($"Error looking up {targetName} assembly");
                }

            }, LoggingReference);
        }

        private string ResolveTargetFolder(string targetFolder) => string.IsNullOrWhiteSpace(targetFolder) ? _fileSystem.Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) : targetFolder;

        private FileVersionInfo DoExtractFileVersionInfo(string fileName, string folder = null, string target = "Current")
        {
            folder = ResolveTargetFolder(folder);
            var resultWrapper = ExtractAssemblyVersionInfoWrapper(fileName, folder, target);
            if (!resultWrapper.IsSuccess)
            {
                return null;
            }

            var wrapper = resultWrapper.Value;
            var result = _fileVersionInfo.ExtractFileVersionInfo(_fileSystem.Path.Combine(folder, "ToDrop"), fileName);
            if (!result.IsSuccess)
            {
                return new FileVersionInfo(wrapper.FileName, wrapper.ProductVersion, "n/a", DateTime.MinValue);
            }

            var info = result.Value;

            return result.IsSuccess ? new FileVersionInfo(info.Name, string.IsNullOrEmpty(wrapper.ProductVersion) ? info.Version : wrapper.ProductVersion, info.Checksum, info.DateModified) : null;
        }

        private IEnumerable<FileVersionInfo> GetFileSet(string folder = null, string target = "current")
        {
            var info = DoExtractFileVersionInfo($"{ConfigConstants.ServiceName}.exe", folder, target);

            if (info != null)
            {
                yield return info;
            }
            info = DoExtractFileVersionInfo(ConfigConstants.ServiceFileName, folder, $"{target} (OPT)");
            if (info != null)
            {
                yield return info;
            }

            info = DoExtractFileVersionInfo(optCommonConstants.OPTCommonFilename, folder, $"{target} (OPT.Common)");
            if (info != null)
            {
                yield return info;
            }
        }
    }
}
