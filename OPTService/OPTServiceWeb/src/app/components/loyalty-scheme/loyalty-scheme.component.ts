import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbPanelChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { Constants } from 'src/app/helpers/constants';
import { LoyaltyService } from 'src/app/services/loyalty.service';

@Component({
  selector: 'app-loyalty-scheme',
  templateUrl: './loyalty-scheme.component.html',
  styleUrls: ['./loyalty-scheme.component.css']
})
export class LoyaltySchemeComponent implements OnInit {

  @Input() form: FormGroup;
  @Input() activeSection: string;

  // API call fail vars
  presentError: boolean = false;
  siteIdError: boolean = false;
  terminalIdError: boolean = false;
  footer1Error: boolean = false;
  footer2Error: boolean = false;
  timeoutError: boolean = false;
  apiKeyError: boolean = false;
  httpHeaderError: boolean = false;
  addHostError: boolean = false;
  addHostnameError: boolean = false;
  addIinError: boolean = false;
  addLoyaltyMapError: boolean = false;

  // Form
  // The form for adding new host
  addHostForm: FormGroup;
  // The form for adding new hostname
  addHostnameForm: FormGroup;
  // The form for adding new IIN
  addIinForm: FormGroup;
  // The form for adding new loyalty mapping
  addLoyaltyMapForm: FormGroup;

  // Active IDs in accordiion controls
  @Output() activeSectionChangeEvent = new EventEmitter<string>();

  /**
   * The constructor
   * @param fb 
   * @param modalService 
   */
  constructor(
    private fb: FormBuilder,
    private modalService: NgbModal,
    private loyaltyService: LoyaltyService,
    private logger: NGXLogger) { }

  /**
   * The getter for the main form
   */
  get formControl() {
    return this.form.controls;
  }

  /**
   * The getter for the Hosts array in main form
   */
  get hosts() {
    return this.form.get('Hosts') as FormArray;
  }

  /**
   * The getter for the form for adding new host
   */
  get addHostFormControl() {
    return this.addHostForm.controls;
  }

  /**
   * The getter for the Hostnames array in main form
   */
  get hostnames() {
    return this.form.get('Hostnames') as FormArray;
  }

  /**
   * The getter for the form for adding new hostname
   */
  get addHostnameFormControl() {
    return this.addHostnameForm.controls;
  }

  /**
   * The getter for the Iins array in main form
   */
  get iins() {
    return this.form.get('Iins') as FormArray;
  }

  /**
   * The getter for the form for adding new Iin
   */
  get addIinFormControl() {
    return this.addIinForm.controls;
  }

  /**
   * The getter for the LoyaltyMap array in main form
   */
  get loyaltyMap() {
    return this.form.get('LoyaltyMappings') as FormArray;
  }

  /**
   * The getter for the form for adding new loyalty mapping
   */
  get addLoyaltyMapFormControl() {
    return this.addLoyaltyMapForm.controls;
  }

  /**
     * Creates or resets the form for adding a new host
     */
  createAddHostForm() {
    this.addHostForm = this.fb.group({
      IpAddress: ['', [Validators.required, Validators.pattern('^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')]],
      Port: [0, [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10)]]
    });
  }

  /**
   * Creates or resets the form for adding a new hostname
   */
  createAddHostnameForm() {
    this.addHostnameForm = this.fb.group({
      Hostname: ['', [Validators.required]]
    });
  }

  /**
   * Creates or resets the form for adding a new IIN
   */
  createAddIinForm() {
    this.addIinForm = this.fb.group({
      Low: ['', [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10)]],
      High: ['', [Validators.required, Validators.pattern(Constants.PositiveNumberRegxPattern), Validators.maxLength(10)]]
    });
  }

  /**
   * Creates or resets the form for adding a new loyalty map
   */
  createAddLoyaltyMapForm() {
    this.addLoyaltyMapForm = this.fb.group({
      ProductCode: ['', [Validators.required]],
      LoyaltyCode: ['', [Validators.required]]
    });
  }

  /**
   * Resets the addHostForm and opens a modal windows containing the form
   * @param content
   */
  openAddHostForm(content: any) {
    this.createAddHostForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addHostnameForm and opens a modal windows containing the form
   * @param content
   */
  openAddHostnameForm(content: any) {
    this.createAddHostnameForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addIinForm and opens a modal windows containing the form
   * @param content
   */
  openAddIinForm(content: any) {
    this.createAddIinForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => { })
  }

  /**
   * Resets the addLoyaltyMapForm and opens a modal windows containing the form
   * @param content
   */
  openAddLoyaltyMapForm(content: any) {
    this.createAddLoyaltyMapForm();
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
    }, (reason) => { })
  }

  toogleSection(props: NgbPanelChangeEvent) {
    this.activeSectionChangeEvent.emit(props.panelId);
  }

  /**
   * Calls the API to update the value of the Present field
   */
  updatePresent(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var present = this.form.get('Present').value;
    //Call the API
    this.logger.info('Setting loyalty present', name, present);
    this.loyaltyService.setLoyaltyPresent(name, present).subscribe(() => {
      this.logger.debug('Loyalty present set ok');
      //Mark form group as valid
      this.presentError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty present');
      //Mark form group as invalid
      this.presentError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API to update the value of site ID
   */
  updateSiteId(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var siteId = this.form.get('SiteId').value;
    //Call the API
    this.logger.info('Setting loyalty terminal site ID', name, siteId);
    this.loyaltyService.setLoyaltyTerminalSiteId(name, siteId).subscribe(() => {
      this.logger.debug('Loyalty terminal site ID set ok');
      //Mark form group as valid
      this.siteIdError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty terminal site ID');
      //Mark form group as invalid
      this.siteIdError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
     * Calls the API to update the value of terminal ID
     */
  updateTerminalId(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var terminalId = this.form.get('TerminalId').value;
    //Call the API
    this.logger.info('Setting loyalty terminal terminal ID', name, terminalId);
    this.loyaltyService.setLoyaltyTerminalTerminalId(name, terminalId).subscribe(() => {
      this.logger.debug('Loyalty terminal terminal ID set ok');
      //Mark form group as valid
      this.terminalIdError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty terminal terminal ID');
      //Mark form group as invalid
      this.terminalIdError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
       * Calls the API to update the value of footer 1
       */
  updateFooter1(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var footer1 = this.form.get('Footer1').value;
    //Call the API
    this.logger.info('Setting loyalty terminal footer 1', name, footer1);
    this.loyaltyService.setLoyaltyTerminalFooter1(name, footer1).subscribe(() => {
      this.logger.debug('Loyalty terminal footer 1 set ok');
      //Mark form group as valid
      this.footer1Error = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty footer 1');
      //Mark form group as invalid
      this.footer1Error = true;
      ButtonHelper.reset(event);
    });
  }

  /**
       * Calls the API to update the value of footer 2
       */
  updateFooter2(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var footer2 = this.form.get('Footer2').value;
    //Call the API
    this.logger.info('Setting loyalty terminal footer 2', name, footer2);
    this.loyaltyService.setLoyaltyTerminalFooter2(name, footer2).subscribe(() => {
      this.logger.debug('Loyalty terminal footer 2 set ok');
      //Mark form group as valid
      this.footer2Error = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty footer 2');
      //Mark form group as invalid
      this.footer2Error = true;
      ButtonHelper.reset(event);
    });
  }

  /**
         * Calls the API to update the value of timeout
         */
  updateTimeout(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var timeout = this.form.get('Timeout').value;
    //Call the API
    this.logger.info('Setting loyalty terminal timeout', name, timeout);
    this.loyaltyService.setLoyaltyTerminalTimeout(name, timeout).subscribe(() => {
      this.logger.debug('Loyalty terminal timeout set ok');
      //Mark form group as valid
      this.timeoutError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty timeout');
      //Mark form group as invalid
      this.timeoutError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
         * Calls the API to update the value of API key
         */
  updateApiKey(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var apiKey = this.form.get('ApiKey').value;
    //Call the API
    this.logger.info('Setting loyalty terminal API key', name, apiKey);
    this.loyaltyService.setLoyaltyTerminalApiKey(name, apiKey).subscribe(() => {
      this.logger.debug('Loyalty terminal API key set ok');
      //Mark form group as valid
      this.apiKeyError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty API key');
      //Mark form group as invalid
      this.apiKeyError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
         * Calls the API to update the value of HTTP header
         */
  updateHttpHeader(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var httpHeader = this.form.get('HttpHeader').value;
    //Call the API
    this.logger.info('Setting loyalty terminal HTTP header', name, httpHeader);
    this.loyaltyService.setLoyaltyTerminalHttpHeader(name, httpHeader).subscribe(() => {
      this.logger.debug('Loyalty terminal HTTP header set ok');
      //Mark form group as valid
      this.httpHeaderError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting loyalty HTTP header');
      //Mark form group as invalid
      this.httpHeaderError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for adding anew host
   */
  onAddHostFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var ipAddress = this.addHostForm.get('IpAddress').value;
    var port = this.addHostForm.get('Port').value;
    //Call the API
    this.logger.info('Adding host', name, ipAddress, port);
    this.loyaltyService.setLoyaltyHostsAddHost(name, ipAddress, port).subscribe(() => {
      this.logger.debug('Host added ok');
      //Mark form group as valid
      this.addHostError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding host');
      //Mark form group as invalid
      this.addHostError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for removing an existing host
   * @param index 
   */
  removeHost(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.hosts.at(index).get('Name').value;
    var ipAddress = this.hosts.at(index).get('IpAddress').value;
    var port = this.hosts.at(index).get('Port').value;
    //Call the API
    this.logger.info('Removing host', name, ipAddress, port);
    this.loyaltyService.setLoyaltyHostsRemoveHost(name, ipAddress, port).subscribe(() => {
      this.logger.debug('Host removed ok');
      //Mark form group as valid
      this.hosts.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing host');
      //Mark form group as invalid
      this.hosts.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for adding a new hostname
   */
  onAddHostnameFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var hostname = this.addHostnameForm.get('Hostname').value;
    //Call the API
    this.logger.info('Adding hostname', name, hostname);
    this.loyaltyService.setLoyaltyAddHostname(name, hostname).subscribe(() => {
      this.logger.debug('Hostname added ok');
      //Mark form group as valid
      this.addHostnameError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding hostname');
      //Mark form group as invalid
      this.addHostnameError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Call the API for removing an existing hostname
   * @param index 
   */
  removeHostname(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.hostnames.at(index).get('Name').value;
    var hostname = this.hostnames.at(index).get('Hostname').value;
    //Call the API
    this.logger.info('Removing hostname', name, hostname);
    this.loyaltyService.setLoyaltyRemoveHostname(name, hostname).subscribe(() => {
      this.logger.debug('Hostname removed ok');
      //Mark form group as valid
      this.hostnames.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing hostname');
      //Mark form group as invalid
      this.hostnames.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the aPI for adding a new IIN
   */
  onAddIinFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var low = this.addIinForm.get('Low').value;
    var high = this.addIinForm.get('High').value;
    //Call the API
    this.logger.info('Adding IIN', name, low, high);
    this.loyaltyService.setLoyaltyIinsAddIin(name, low, high).subscribe(() => {
      this.logger.debug('IIN added ok');
      //Mark form group as valid
      this.addIinError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding IIN');
      //Mark form group as invalid
      this.addIinError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for removing an exising IIn
   * @param index 
   */
  removeIin(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var low = this.iins.at(index).get('Low').value;
    var high = this.iins.at(index).get('High').value;
    //Call the API
    this.logger.info('Removing IIN', name, low, high);
    this.loyaltyService.setLoyaltyIinsRemoveIin(name, low, high).subscribe(() => {
      this.logger.debug('IIN removed ok');
      //Mark form group as valid
      this.iins.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing IIN');
      //Mark form group as invalid
      this.iins.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for adding a new loyalty mapping
   */
  onAddLoyaltyMapFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var productCode = this.addLoyaltyMapForm.get('ProductCode').value;
    var loyaltyCode = this.addLoyaltyMapForm.get('LoyaltyCode').value;
    //Call the API
    this.logger.info('Adding loyalty mapping', name, productCode, loyaltyCode);
    this.loyaltyService.setLoyaltyMappingsAddMapping(name, productCode, loyaltyCode).subscribe(() => {
      this.logger.debug('Loyalty mapping added ok');
      //Mark form group as valid
      this.addLoyaltyMapError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding loyalty mapping');
      //Mark form group as invalid
      this.addLoyaltyMapError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Calls the API for removing an existing loyalty mapping
   * @param index 
   */
  removeMap(index: number, event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve de data
    var name = this.form.get('Name').value;
    var productCode = this.loyaltyMap.at(index).get('ProductCode').value;
    var loyaltyCode = this.loyaltyMap.at(index).get('LoyaltyCode').value;
    //Call the API
    this.logger.info('Removing loyalty mapping', name, productCode, loyaltyCode);
    this.loyaltyService.setLoyaltyMappingsRemoveMapping(name, productCode, loyaltyCode).subscribe(() => {
      this.logger.debug('Loyalty mapping removed ok');
      //Mark form group as valid
      this.loyaltyMap.at(index).get('RemoveError').setValue(false);
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem removing loyalty mapping');
      //Mark form group as invalid
      this.loyaltyMap.at(index).get('RemoveError').setValue(true);
      ButtonHelper.reset(event);
    });
  }

  ngOnInit(): void {
  }

}
