using Forecourt.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pos.Messages;

namespace Forecourt.Pos.Extensions
{
    /// <summary>
    ///  Any and all helper methods related to <see cref="IPump"/> for use by Pos integrators
    /// </summary>
    public static class PumpExtensions
    {
        /// <summary>
        /// Extracts the relevant status information from the pump, for Pos purposes
        /// </summary>
        /// <param name="pump"><see cref="IPump"/> instance</param>
        /// <param name="autoAuth">Is auto auth in operation</param>
        /// <returns><see cref="StatusResponse"/> instance</returns>
        public static StatusResponse ToStatusResponse(this IPump pump, bool autoAuth = false)
        {
            return new StatusResponse
            {
                Pump = pump.Number,
                PumpState = (ushort)pump.PumpState,
                IsOptLinked = pump.Opt != null,
                OptId = pump.Opt?.IdString,
                IsInUse = pump.InUse,
                IsOutsideOnly = pump.OutsideOnly,
                IsAutoAuth = autoAuth,
                IsOffline = pump.Opt?.Offline ?? true,
                IsKioskUse = pump.KioskUse,
                CashLimit = pump.Opt?.CashLimit ?? 0
            };
        }
    }
}
