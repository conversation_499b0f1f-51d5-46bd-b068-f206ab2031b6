<ng-container *ngIf="showContent">
  <div id="card-divert-opt" class="card form-group" [formGroup]="divertForm">
    <div class="card-header">
      <button type="button" (click)="divertOptCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
        <span>Divert OPT</span>
        <div id="divertedBadge">
          <span class="badge badge-success mx-1" *ngIf="divertDetailsData?.IsDiverted">Diverted</span>
          <span class="badge badge-danger mx-1" *ngIf="divertDetailsData?.IsDiverted === false">Not diverted</span>
          <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':divertOptIsCollapsed,'bi-chevron-up':divertOptIsCollapsed===false }"></i>
        </div>
      </button>
    </div>
    <div #divertOptCollapse="ngbCollapse" [(ngbCollapse)]="divertOptIsCollapsed" class="card-body">
      <app-label-text id="divertIpAddress" labelColClass="col-3" textColClass="col-4" labelText="Service address"
                      controlName="IpAddress" [formGroup]="divertForm" maxLength="15">
      </app-label-text>

      <app-label-text id="divertFromOptPort" labelColClass="col-3" textColClass="col-4" labelText="From OPT port"
                      controlName="FromOptPort" [formGroup]="divertForm" maxLength="15">
      </app-label-text>

      <app-label-text id="divertToOptPort" labelColClass="col-3" textColClass="col-4" labelText="To OPT port"
                      controlName="ToOptPort" [formGroup]="divertForm" maxLength="15">
      </app-label-text>

      <app-label-text id="divertHeartbeatPort" labelColClass="col-3" textColClass="col-4" labelText="Heartbeat port"
                      controlName="HeartbeatPort" [formGroup]="divertForm" maxLength="15">
      </app-label-text>

      <app-label-text id="divertMediaChannelPort" labelColClass="col-3" textColClass="col-4" labelText="Media channel port"
                      controlName="MediaChannelPort" [formGroup]="divertForm" maxLength="15"
                      *ngIf="divertDetailsData?.MediaChannel">
      </app-label-text>

      <div *ngIf="divertDetailsData?.IsDiverted === false" class="text-center">
        <button id="divertBtn" type="button" class="btn btn-primary" (click)="setDivertDetails($event)">Divert</button>
        <span id="divertError" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="divertErrors?.SetDivertDetailsText"
              *ngIf="divertErrors.SetDivertDetails"><i class="bi bi-exclamation-triangle"></i></span>
      </div>
      <div *ngIf="divertDetailsData?.IsDiverted" class="text-center">
        <button id="divertCancelBtn" type="button" class="btn btn-primary" (click)="cancelDivertDetails($event)">Cancel divert</button>
        <span id="divertCancelError" class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="divertErrors?.CancelDivertDetailsText"
              *ngIf="divertErrors.CancelDivertDetails"><i class="bi bi-exclamation-triangle"></i></span>
      </div>
    </div>
  </div>
</ng-container>