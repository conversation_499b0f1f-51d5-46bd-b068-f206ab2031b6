USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddConfigurationCategory') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE AddConfigurationCategory
END
GO

CREATE PROCEDURE AddConfigurationCategory
@category NVARCHAR(100),
@description VARCHAR(255),
@isStandardEditable BIT = 1
AS		
BEGIN

	IF NOT EXISTS(SELECT 1 FROM dbo.ConfigurationCategory t WHERE t.Category = @category)
	BEGIN
		INSERT INTO dbo.ConfigurationCategory(Category, [Description], IsStandardEditable) VALUES(@category, @description, @isStandardEditable);
	END

END
GO

GRANT EXECUTE ON AddConfigurationCategory TO UserRole
