USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetReceipts') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetReceipts
END
GO

CREATE procedure [dbo].[GetReceipts] as
 begin
  select CardNumber, 
  CASE WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, NULL) = ReceiptHash) THEN ReceiptContent
  WHEN (dbo.getReceiptHash(CardNumber, ReceiptContent, OPT, TransactionNumber, Amount, TransactionTime, 0) = ReceiptHash) THEN ReceiptContent
  ELSE cast('Error with receipt data hash.' AS VARCHAR(MAX)) END AS ReceiptContent,
  Expiry,
  OPT,
  TransactionNumber,
  Amount,
  TransactionTime,
  PrintedCount
  from Receipts;
 END
 GO

GRANT EXECUTE ON GetReceipts TO UserRole
GO
