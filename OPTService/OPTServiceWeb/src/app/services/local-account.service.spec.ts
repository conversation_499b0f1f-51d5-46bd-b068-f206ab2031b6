import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { HttpTestingController, HttpClientTestingModule } from '@angular/common/http/testing';
import { LocalAccountService } from './local-account.service';
import { environment } from 'src/environments/environment';
import { DOCUMENT } from '@angular/common';
import { LocalAccounts } from '../core/models/localAccounts.model';
import { LocalAccountCard } from '../core/models/LocalAccountCard.model';

describe('localAccountService', () => {
  let localAccountService: LocalAccountService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        LocalAccountService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    localAccountService = TestBed.inject(LocalAccountService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(localAccountService).toBeTruthy();
  });

  it('.getLocalAccounts() should handle success response from API', () => {
    //Arrange
    let localAccountDetails = {
      AsdaDayEndReport: false,
      AutoAuth: true,
      CardReferences: [{
        Acquirer: "Visa,Mastercard,Delta&Electron",
        FuelCard: false,
        InUse: false,
        Name: "VISA",
        Reference: 1
      }],
      FilePruneDays: 30,
      ForwardFuelPriceUpdate: true,
      FuelCategory: 99,
      FuellingBackoffAuth: 0,
      FuellingBackoffPreAuth: 0,
      FuellingBackoffStopOnly: 0,
      FuellingBackoffStopStart: 0,
      FuellingIndefiniteWait: true,
      FuellingWaitMinutes: 5,
      LoyaltyAvailable: ["Morrisons"],
      MediaChannel: true,
      MorrisonLoyaltyAvailable: true,
      PaymentTimeoutMixed: 15,
      PaymentTimeoutNozzleDown: 15,
      PaymentTimeoutOpt: 15,
      PaymentTimeoutPod: 300,
      PosClaimNumber: 99,
      ReceiptMaxCount: 12,
      ReceiptPruneDays: 14,
      ReceiptTimeout: 3600,
      TillNumber: 99,
      TransactionPruneDays: 50,
      UnmannedPseudoPos: true
    };
    
    //Act
    localAccountService.getLocalAccounts().subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(localAccountDetails);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.getLocalAccounts);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(localAccountDetails);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getLocalAccounts() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.getLocalAccounts().subscribe(DataTransfer =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.getLocalAccounts);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addLocalAccountCard() should handle success response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
        
    //Act
    localAccountService.addLocalAccountCard(localAccountsData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.addLocalAccountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addLocalAccountCard() should handle unsuccess response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.addLocalAccountCard(localAccountsData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.addLocalAccountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeLocalAccountCustomer() should handle success response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
        
    //Act
    localAccountService.removeLocalAccountCustomer(localAccountsData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.removeLocalAccountCustomer);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeLocalAccountCustomer() should handle unsuccess response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.removeLocalAccountCustomer(localAccountsData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.removeLocalAccountCustomer);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountCustomerBalance() should handle success response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
        
    //Act
    localAccountService.setLocalAccountCustomerBalance(localAccountsData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountCustomerBalance);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountCustomerBalance() should handle unsuccess response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.setLocalAccountCustomerBalance(localAccountsData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountCustomerBalance);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addLocalAccountCustomer() should handle success response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
        
    //Act
    localAccountService.addLocalAccountCustomer(localAccountsData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.addLocalAccountCustomer);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addLocalAccountCustomer() should handle unsuccess response from API', () => {
    //Arrange
    let localAccountsData = new LocalAccounts();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.addLocalAccountCustomer(localAccountsData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.addLocalAccountCustomer);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountsEnabled() should handle success response from API', () => {
    //Arrange
        
    //Act
    localAccountService.setLocalAccountsEnabled(false).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountsEnabled + "?enabled=false");

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountsEnabled() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.setLocalAccountsEnabled(true).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountsEnabled + "?enabled=true");

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeLocalAccountCard() should handle success response from API', () => {
    //Arrange
    let cardData = new LocalAccountCard();
        
    //Act
    localAccountService.removeLocalAccountCard(cardData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.removeLocalAccountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeLocalAccountCard() should handle unsuccess response from API', () => {
    //Arrange
    let cardData = new LocalAccountCard();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.removeLocalAccountCard(cardData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.removeLocalAccountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountCardHot() should handle success response from API', () => {
    //Arrange
    let cardData = new LocalAccountCard();
        
    //Act
    localAccountService.setLocalAccountCardHot(cardData).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountCardHot);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLocalAccountCardHot() should handle unsuccess response from API', () => {
    //Arrange
    let cardData = new LocalAccountCard();
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    localAccountService.setLocalAccountCardHot(cardData).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.localAccountController.base + environment.localAccountController.setLocalAccountCardHot);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
});
