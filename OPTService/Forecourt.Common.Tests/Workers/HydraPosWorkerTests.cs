using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Text;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    public class HydraPosWorkerTests
    {
        private const string DefaultLoggingReference = "LOGGING-REF";

        private IList<string> receipts = new List<string>
            {
                "�0.01 13:01 06/06/20\r\n\r\n" +
                "             Customer Copy\r\n" +
                "O668 HTEC TEST Till 75 O6/O6/2O 13:O1\r\n" +
                "         Merchant No. ***68O65\r\n" +
                "\r\n" +
                "            ICC TRANSACTION\r\n" +
                "      TRAN NO. 3665  EFT NO. 522\r\n" +
                "             SALE CANCELLED\r\n" +
                "\r\n" +
                "         Terminal No. ***OOOO\r\n" +
                "\r\n" +
                "        VISA CREDIT 1 *****OO1O\r\n" +
                "            PAN Seq. No. O1\r\n" +
                "              Auth Code: \r\n" +
                "\r\n" +
                "         AID: AOOOOOOOO31O1OO1\r\n",

                "�0.02 17:21 06/06/20\r\n\r\n" +
                "             Customer Copy\r\n" +
                "O668 HTEC TEST Till 75 O6/O6/2O 13:O1\r\n" +
                "         Merchant No. ***68O65\r\n" +
                "\r\n" +
                "            ICC TRANSACTION\r\n" +
                "      TRAN NO. 3666  EFT NO. 522\r\n" +
                "             SALE CANCELLED\r\n" +
                "\r\n" +
                "         Terminal No. ***OOOO\r\n" +
                "\r\n" +
                "        VISA CREDIT 1 *****OO1O\r\n" +
                "            PAN Seq. No. O1\r\n" +
                "              Auth Code: \r\n" +
                "\r\n" +
                "         AID: AOOOOOOOO31O1OO2\r\n"
            };

        private const uint OptOneCashLimit = 10000;
        private const uint OptTwoCashLimit = 5000;
        private const string OptOneIdString = "IdOne";
        private readonly IJournalWorker _journalWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pumpOne;
        private readonly IPump _pumpTwo;
        private readonly IOpt _optOne;
        private readonly IOpt _optTwo;
        private readonly IHtecLogger _logger;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IListenerConnectionThread<string> _connectionThread;
        private readonly IMessageTracking<string> _message;
        private readonly IConfigurationManager _configurationManager;
        private LoggableMock _loggableMock;
        private readonly IPrinterHelper<IMessageTracking> _printerHelper;
        private readonly IPosIntegratorOut<IMessageTracking> _posOut;
        protected readonly IPosIntegratorInMode<IMessageTracking> _posInMode;
        private readonly NameValueCollection _appSettings;

        public HydraPosWorkerTests()
        {
            _journalWorker = Substitute.For<IJournalWorker>();
            _hydraDb = Substitute.For<IHydraDb>();
            _allPumps = Substitute.For<IPumpCollection>();
            _pumpOne = Substitute.For<IPump>();
            _pumpTwo = Substitute.For<IPump>();
            _optOne = Substitute.For<IOpt>();
            _optTwo = Substitute.For<IOpt>();
            _allPumps.TryGetPump(Arg.Any<byte>(), out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (byte)x[0] == 1 ? _pumpOne : (byte)x[0] == 2 ? _pumpTwo : null;
                return (byte)x[0] == 1 || (byte)x[0] == 2;
            });
            _allPumps.AllPumps.Returns(new List<IPump> { _pumpOne, _pumpTwo });
            _pumpOne.Number.Returns((byte)1);
            _pumpOne.Opt.Returns(_optOne);
            _pumpOne.OutsideOnly.Returns(true);
            _pumpOne.State.Returns(HydraPosPumpState.Idle);
            _pumpOne.InUse.Returns(false);
            _pumpOne.KioskUse.Returns(false);
            _optOne.Offline.Returns(false);
            _optOne.CashLimit.Returns(OptOneCashLimit);
            _optOne.IdString.Returns(OptOneIdString);
            _pumpTwo.Number.Returns((byte)2);
            _pumpTwo.Opt.Returns(_optTwo);
            _pumpTwo.OutsideOnly.Returns(false);
            _pumpTwo.State.Returns(HydraPosPumpState.TakeFuel);
            _pumpTwo.InUse.Returns(true);
            _pumpTwo.KioskUse.Returns(false);
            _optTwo.Offline.Returns(false);
            _optTwo.CashLimit.Returns(OptTwoCashLimit);
            _logger = Substitute.For<IHtecLogger>();
            _printerHelper = Substitute.For<IPrinterHelper<IMessageTracking>>();
            _posOut = Substitute.For<IPosIntegratorOut<IMessageTracking>>();
            _posInMode = Substitute.For<IPosIntegratorInMode<IMessageTracking>>();

            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _connectionThread = Substitute.For<IListenerConnectionThread<string>>();

            _message = Substitute.For<IMessageTracking<string>>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _appSettings = new NameValueCollection();
            _configurationManager.AppSettings.Returns(_appSettings);

            _loggableMock = new LoggableMock(_logger, _configurationManager);
        }


        [Fact]
        public void on_text_from_known_client_hydra_db_throwing_exception_is_not_rethrown()
        {
            // Arrange
            _hydraDb.GetReceiptsForOpt(Arg.Any<string>()).Throws(new InvalidOperationException());

            IHydraPosWorker hydraPosWorker = InitialiseHydraPosWorker();

            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns("Receipts{pump}:");

            // Act
            Action act = () => hydraPosWorker.OnMessageReceived(_message, id);

            // Assert
            act.Should().NotThrow();
        }

        [Fact]
        public void on_text_from_known_client_controller_worker_throwing_exception_is_not_rethrown()
        {
            // Arrange
            var controllerWorker = Substitute.For<IControllerWorker>();
            controllerWorker.When(x => x.SendInformation(Arg.Any<string>())).Throw(new InvalidOperationException());

            IHydraPosWorker hydraPosWorker = InitialiseHydraPosWorker();

            hydraPosWorker.RegisterWorker(controllerWorker);

            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Receipts{pump}:");

            // Act
            Action act = () => hydraPosWorker.OnMessageReceived(_message, id);

            // Assert
            act.Should().NotThrow();
        }

        [Fact]
        public void test_unknown_command_returns_failure()
        {
            // Arrange
            const int id = 1;
            _message.Request.Returns(ConfigConstants.Unknown);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id);

            // Assert
            result.IsSuccess.Should().BeFalse();
        }


        [Fact]
        public void test_update_command_no_pump()
        {
            // Arrange
            const int id = 1;
            const byte pump = 3;
            _message.Request.Returns($"Update{pump}:");
            string expectedResult =
                ConstructUpdateMessage(pump, false, false, false, (ushort)HydraPosPumpState.Closed, true, false, true, 0);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            string result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void test_update_command_default_pump()
        {
            // Arrange
            const int id = 1;
            const byte pump = 2;
            _message.Request.Returns($"Update{pump}:");
            string expectedResult = ConstructUpdateMessage(pump, false, true, true, (ushort)HydraPosPumpState.Closed, true, false, false, 0);
            
            _allPumps.TryGetPump(Arg.Is(pump), out _).Returns(x =>
            {
                x[1] = new Pump(pump, _hydraDb, Substitute.For<IHtecLogManager>(), "Pump");
                return true;
            });

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void test_update_command_pump_one()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Update{pump}:");
            string expectedResult = ConstructUpdateMessage(pump, true, true, true, (ushort)HydraPosPumpState.Idle, false, false, false,
                OptOneCashLimit);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void test_update_command_pump_two()
        {
            // Arrange
            const int id = 1;
            const byte pump = 2;
            _message.Request.Returns($"Update{pump}:");
            string expectedResult = ConstructUpdateMessage(pump, true, false, true, (ushort)HydraPosPumpState.TakeFuel, false, true,
                false, OptTwoCashLimit);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void test_update_command_pump_one_and_two()
        {
            // Arrange
            const int id = 1;
            const byte pumpOne = 1;
            const byte pumpTwo = 2;
            _message.Request.Returns($"Update{pumpOne}:Update{pumpTwo}:");
            string expectedResult =
                ConstructUpdateMessage(pumpOne, true, true, true, (ushort)HydraPosPumpState.Idle, false, false, false,
                    OptOneCashLimit) + ConstructUpdateMessage(pumpTwo, true, false, true, (ushort)HydraPosPumpState.TakeFuel, false,
                    true, false, OptTwoCashLimit);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            string result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }


        [Fact]
        public void test_day_end_command()
        {
            // Arrange
            const int id = 1;
            _message.Request.Returns("DayEnd");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).Received().RequestDayEnd(Arg.Any<IMessageTracking>());
        }

        [Fact]
        public void test_kiosk_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Kiosk{pump}:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.Kiosk, Arg.Any<string>());
        }

        [Fact]
        public void test_opt_use_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"OPTuse{pump}:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.OptUse, Arg.Any<string>());
        }

        [Fact]
        public void test_close_command_hydrapos()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Close{pump}:");

            var siteInfo = new SiteInfo(1, "Site Name", "VAT Number", true, true, 100, true, 100, 200, 0);
            _hydraDb.GetSiteInfo().Returns(siteInfo);
            _appSettings[AdvancedConfig.ConfigKeyPosType] = $"{PosType.HydraPos}";
            var advConfig = new AdvancedConfig(_hydraDb, _logger, _configurationManager);
            _hydraDb.AdvancedConfig.Returns(advConfig);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received(1).HydraPosCommand(HydraPosCommand.Close, Arg.Any<string>());
            _pumpOne.Received(0).HydraPosCommand(HydraPosCommand.Open, Arg.Any<string>());
            _pumpOne.Received(0).HydraPosCommand(HydraPosCommand.Kiosk, Arg.Any<string>());
        }

        [Fact]
        public void test_close_command_retalix()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Close{pump}:");

            var siteInfo = new SiteInfo(1, "Site Name", "VAT Number", true, true, 100, true, 100, 200, 0);
            _hydraDb.GetSiteInfo().Returns(siteInfo);
            var advConfig = new AdvancedConfig(_hydraDb, _logger, _configurationManager);
            _hydraDb.AdvancedConfig.Returns(advConfig);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received(1).HydraPosCommand(HydraPosCommand.Close, Arg.Any<string>());
            _pumpOne.Received(1).HydraPosCommand(HydraPosCommand.Open, Arg.Any<string>());
            _pumpOne.Received(1).HydraPosCommand(HydraPosCommand.Kiosk, Arg.Any<string>());
        }

        [Fact]
        public void test_open_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Open{pump}:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.Open, Arg.Any<string>());
        }

        [Fact]
        public void test_out_side_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"OutSide{pump}:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.OutSide, Arg.Any<string>());
        }

        [Fact]
        public void test_mix_mode_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"MixMode{pump}:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.MixMode, Arg.Any<string>());
        }

        [Fact]
        public void test_night_mode_command()
        {
            // Arrange
            const int id = 1;
            _message.Request.Returns("NightMode:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.NightMode, Arg.Any<string>());
            _pumpTwo.Received().HydraPosCommand(HydraPosCommand.NightMode, Arg.Any<string>());
        }
        [Fact]
        public void test_day_mode_command()
        {
            // Arrange
            const int id = 1;
            _message.Request.Returns("DayMode:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received(1).HydraPosCommand(HydraPosCommand.DayMode, Arg.Any<string>());
        }

        [Fact]
        public void test_evening_mode_command()
        {
            // Arrange
            const int id = 1;
            _message.Request.Returns("EveningMode:");

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().BeEquivalentTo(string.Empty);
            _pumpOne.Received().HydraPosCommand(HydraPosCommand.EveningMode, Arg.Any<string>());
            _pumpTwo.Received().HydraPosCommand(HydraPosCommand.EveningMode, Arg.Any<string>());
        }
        [Fact]
        public void test_empty_receipts_command()
        {
            // Arrange
            const int id = 1;
            const byte pump = 1;
            _message.Request.Returns($"Receipts{pump}:");
            string expectedResult = ConstructReceiptsMessage(pump);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            var result = hydraPosWorker.OnMessageReceived(_message, id).Value;

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void test_receipt_creation()
        {
            List<byte> messageBytes = new List<byte>();

            var lines = 7 * receipts.Count;

            messageBytes.AddRange(HydraPosWorker.ConstructDelphiString($"RECEIPTS PUMP=1 LINES={lines}", 120));

            using (var hydraPosWorker = new HydraPosWorker(_journalWorker, _telemetryWorker, _connectionThread, _hydraDb, null, _logger, _configurationManager, _printerHelper, _posOut, _posInMode))
            {
                foreach (var receipt in receipts)
                {
                    messageBytes.AddRange(hydraPosWorker.ConstructReceiptBlock(receipt, new ReceiptInfo("123456", "Receipt", DateTime.Now, "OPT1", 1, 500, new DateTime(2022, 6, 10, 13, 5, 0), 1)));
                }
            }

            var result = BitConverter.ToString(messageBytes.ToArray());
            Assert.Equal(messageBytes.Count, 121 * (1 + lines));
        }

        #region Get Opt Ocx Receipt Line1

        [Fact]
        public void get_opt_ocx_receipt_line1_receipt_text_is_parsed()
        {
            // Arrange
            var testString = new ReceiptInfo("123456", "Receipt", DateTime.Now, "OPT1", 1, 500, new DateTime(2022, 6, 10, 13, 5, 0), 1);

            // Act
            var line1 = HydraPosWorker.GetOptOcxReceiptLine1(testString);

            // Assert
            line1.Should().Be($"{HydraPosWorker.GbpSymbol}5.00 13:05 10/06/22");
        }

        #endregion

        // TODO: Questionable as a unit test as we're using _connectionThread as a mock and a stub due to IsConnected
        [Fact]
        public void test_update_pump_one_when_connected_message_is_sent()
        {
            // Arrange
            const byte pump = 1;
            var expectedResult = ConstructUpdateBytes(pump, true, true, true, (ushort)HydraPosPumpState.Idle, false, false, false,
                OptOneCashLimit);
            var bytesSent = Array.Empty<byte>();

            _connectionThread.When(x => x.Send(Arg.Any<byte[]>(), Arg.Any<string>(), Arg.Any<int>()))
                .Do(x => bytesSent = x.ArgAt<byte[]>(0));
            
            _connectionThread.IsConnected()
                .Returns(true);

            var hydraPosWorker = InitialiseHydraPosWorker();

            // Act
            hydraPosWorker.RequestStatus(_pumpOne);

            // Assert
            bytesSent.Should().BeEquivalentTo(expectedResult);
        }

        private static byte[] ConstructUpdateBytes
            (byte pump, bool anyOpt, bool noKiosk, bool autoAuth, ushort state, bool optOffline, bool inUse, bool kioskUse, uint cashLimit)
        {
            List<byte> bytes = new List<byte>
            {
                7,
                (byte) 'O',
                (byte) 'P',
                (byte) 'T',
                (byte) 'D',
                (byte) 'A',
                (byte) 'T',
                (byte) 'A',
                pump
            };
            bytes.AddRange(BitConverter.GetBytes(anyOpt));
            bytes.AddRange(BitConverter.GetBytes(noKiosk));
            bytes.AddRange(BitConverter.GetBytes(autoAuth));
            // while (bytes.Count % sizeof(ushort) > 0)
            // {
            //     bytes.Add(0);
            // }

            bytes.AddRange(BitConverter.GetBytes(state));
            bytes.AddRange(BitConverter.GetBytes(optOffline));
            bytes.AddRange(BitConverter.GetBytes(inUse));
            bytes.AddRange(BitConverter.GetBytes(kioskUse));
            while (bytes.Count % sizeof(uint) > 0)
            {
                bytes.Add(0);
            }

            bytes.AddRange(BitConverter.GetBytes(cashLimit));
            return bytes.ToArray();
        }
        private static string ConstructUpdateMessage
            (byte pump, bool anyOpt, bool noKiosk, bool autoAuth, ushort state, bool optOffline, bool inUse, bool kioskUse, uint cashLimit)
        {
            StringBuilder sb = new StringBuilder();
            foreach (byte b in ConstructUpdateBytes(pump, anyOpt, noKiosk, autoAuth,state, optOffline, inUse, kioskUse,cashLimit))
            {
                sb.Append((char)b);
            }

            return sb.ToString();
        }

        private static string ConstructReceiptsMessage(byte pump)
        {
            List<byte> bytes = new List<byte>
            {
                23,
                (byte) 'R',
                (byte) 'E',
                (byte) 'C',
                (byte) 'E',
                (byte) 'I',
                (byte) 'P',
                (byte) 'T',
                (byte) 'S',
                (byte) ' ',
                (byte) 'P',
                (byte) 'U',
                (byte) 'M',
                (byte) 'P',
                (byte) '=',
                (byte) ((byte)'0' + pump % 10),
                (byte) ' ',
                (byte) 'L',
                (byte) 'I',
                (byte) 'N',
                (byte) 'E',
                (byte) 'S',
                (byte) '=',
                (byte) '0'
            };
            while (bytes.Count < 121)
            {
                bytes.Add(0);
            }
            StringBuilder sb = new StringBuilder();
            foreach (byte b in bytes)
            {
                sb.Append((char)b);
            }

            return sb.ToString();
        }

        #region Helpers

        public class LoggableMock : Loggable
        {
            public LoggableMock(IHtecLogger logger, IConfigurationManager testLoggable)
                : base(logger, testLoggable)
            {
            }
        }

        private HydraPosWorker InitialiseHydraPosWorker()
        {
            // Required for deferred logging
            _connectionThread.LogRxTx.Returns(new ConfigurableBool(_loggableMock, "Key", false));

            return new HydraPosWorker(_journalWorker, _telemetryWorker, _connectionThread, _hydraDb, _allPumps, _logger, _configurationManager, _printerHelper, _posOut, _posInMode);
        }

        #endregion
    }
}