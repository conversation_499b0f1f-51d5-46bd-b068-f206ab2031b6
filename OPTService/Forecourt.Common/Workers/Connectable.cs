using Forecourt.Common.HydraDbClasses;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;

namespace OPT.Common.Workers
{
    [HasConfiguration]
    public abstract class Connectable : Workers.HydraDbable<IHydraDb>
    {
        protected Connectable(IHydraDb hydraDb, IHtecLogger logger, IConfigurationManager configurationManager = null, string name = null, int? id = null, ITimerFactory timerFactory = null,
            string defaultTimerInterval = null) : base(hydraDb, logger, configurationManager, name, id, timerFactory, defaultTimerInterval)
        {
        }

        protected Connectable(IHydraDb hydraDb, IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager = null, string name = null, int? id = null,
            ITimerFactory timerFactory = null, string defaultTimerInterval = null, bool useXmlConfigurator = false, ILogFormatter logFormatter = null) : base(hydraDb, logManager, loggerName,
            configurationManager, name, id, timerFactory, defaultTimerInterval, useXmlConfigurator, logFormatter)
        {
        }
    }
}
