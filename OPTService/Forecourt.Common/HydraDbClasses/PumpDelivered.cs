namespace OPT.Common.HydraDbClasses
{
    public class PumpDelivered : Forecourt.Core.HydraDb.Models.PumpDelivered
    {
        public PumpDelivered(int number, bool optPayment, bool delivered, int grade, long volume, long amount, string name, int price, long netAmount, long vatAmount, double vatRate, int transSeqNum, byte hose) :
            base(number, optPayment, delivered, grade, hose, volume, amount, name, price, netAmount, vatAmount, vatRate, transSeqNum)
        {
        }
    }
}