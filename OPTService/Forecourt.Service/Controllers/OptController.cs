using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces.Tracing;
using OPTService.OPTServiceClasses;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Unity;

namespace OPTService.Controllers
{
    [HasConfiguration()]
    [Authorize(Roles = "WebUi,Swagger")]
    public class OptController : LinkedLoggableApiController<IWeb>
    {
        private const string WebName = "Web";
        private const int VatMin = 0;
        private const int VatMax = 100;

        /// <summary>
        /// The Web object
        /// </summary>
        private IWeb _web => Loggable;

        // TODO: Until we have the time to change all non HOPT-1964 routes
        private readonly IHtecLogger _logger;

        #region Constructor

        /// <summary>
        /// OPT Controller constructor
        /// </summary>
        /// <param name="logger">IHtecLogger instance</param>
        /// <param name="web">IWeb instance</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        public OptController([Dependency(UnityConfig.DependencyWebLogger)] IHtecLogger logger, IWeb web, IConfigurationManager configurationManager) : base(web as Loggable, configurationManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }
        }

        #endregion

        #region Update Data

        #region Contactless

        /// <summary>
        /// Set an OPT to allow or disallow contactless.
        /// </summary>
        /// <param name="details">Includes serial number of OPT to set and flag.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetContactlessAllowed(ContactlessDetails details)
        {
            _web.SendChatMessage(WebName, $"Set Contactless{(details.IsEnabled ? string.Empty : " Not")} Allowed");
            var result = _web.SetContactlessAllowed(details.IsEnabled);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SetContactlessCardPreAuth(ContactlessDetails details)
        {
            //Do we have a max value allowed?

            // If zero or negative then return Bad Request
            if (details.CardPreAuthLimit <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest);
            }

            _web.SendChatMessage(WebName, $"Set Contactless Card pre-auth amount {details.CardPreAuthLimit}");
            var result = _web.SetContactlessCardPreAuth(details.CardPreAuthLimit);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SetContactlessDevicePreAuth(ContactlessDetails details)
        {
            //Do we have a max value allowed?

            // If zero or negative then return Bad Request
            if (details.DevicePreAuthLimit <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest);
            }

            _web.SendChatMessage(WebName, $"Set Contactless Device pre-auth amount {details.DevicePreAuthLimit}");
            var result = _web.SetContactlessDevicePreAuth(details.DevicePreAuthLimit);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SetContactlessTtq(ContactlessDetails details)
        {
            _web.SendChatMessage(WebName, $"Set Contactless TTQ value {details.Ttq}");
            var result = _web.SetContactlessTtq(details.Ttq);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SetContactlessSingleButton(ContactlessDetails details)
        {
            _web.SendChatMessage(WebName, $"Set Contactless Single Button {(details.IsEnabled ? string.Empty : " Not")} Enabled");
            var result = _web.SetContactlessSingleButton(details.ShowSingleButton);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        /// <summary>
        /// Send restart to an OPT.
        /// </summary>
        /// <param name="details">Includes serial number of OPT.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RestartOpt(OptDetails details)
        {
            string optIdString = details.StringId?.Trim() ?? string.Empty;
            if (optIdString.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName, $"Restart OPT {optIdString}");
                string result = _web.RestartOpt(optIdString);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in OPT");
            }
        }

        /// <summary>
        /// Send configuration refresh to an OPT.
        /// </summary>
        /// <param name="optId">OPT identifier string</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public IHttpActionResult SendConfigPending(string optId = null)
        {
            string error = null;

            if (optId != null)
            {
                string optIdString = optId.Trim() ?? string.Empty;
                if (optIdString.All(char.IsLetterOrDigit))
                {
                    _web.SendChatMessage(WebName, $"Refresh OPT {optIdString}");
                    error = _web.RefreshOpt(optIdString);                    
                }
                else
                {
                    error = "Invalid Characters in OPT Id";
                }
            }
            else
            {
                error = _web.RefreshOpt();
            }

            if (error == null)
            {
                return Ok();
            }
            else
            {
                return BadRequest(error);
            }
        }

        /// <summary>
        /// Send request log file to an OPT.
        /// </summary>
        /// <param name="optId">OPT identifier string</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public IHttpActionResult SendRequestLogFile(string optId = null)
        {
            string error = null;

            if (optId == null)
            {
                error = _web.RequestOptLog();
            }
            else
            {
                string optIdString = optId.Trim() ?? string.Empty;
                if (optIdString.All(char.IsLetterOrDigit))
                {
                    _web.SendChatMessage(WebName, $"Refresh OPT {optIdString}");
                    error = _web.RequestOptLog(optIdString);
                }
                else
                {
                    error = "Invalid Characters in OPT Id";
                }
            }

            return error == null ? Ok() : BadRequest(error);
        }

        /// <summary>
        /// Send request to reset an OPT to delete files instead of an engineer's visit.
        /// </summary>
        /// <param name="optId">OPT identifier string</param>
        /// <returns>Error message if failed.</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="401">Unauthorized access</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [Authorize(Roles = "Swagger")]
        [HttpPost]
        public IHttpActionResult EngineerReset(string optId)
        {
            string error = null;

            string optIdString = !string.IsNullOrEmpty(optId) ? optId.Trim() : string.Empty;

            if (optIdString.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName, $"Reset OPT {optIdString}");
                error = _web.EngineerResetOpt(optIdString);
            }
            else
            {
                error = "Invalid Characters in OPT Id";
            }

            return error == null ? Ok() : BadRequest(error);
        }

        /// <summary>
        /// Set the IP Address for the OPT connection in the OPT Config.
        /// </summary>
        /// <param name="details">Includes the IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetServiceAddress(EndPointDetails details)
        {
            string address = details.IpAddress?.Trim() ?? string.Empty;
            if (IPAddress.TryParse(address, out IPAddress ip))
            {
                _web.SendChatMessage(WebName, $"Set Service Address {ip}");
                string result = _web.SetServiceAddress(ip);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid IP Address");
            }
        }

        /// <summary>
        /// Add an eSocket.POS address to the OPT Config.
        /// </summary>
        /// <param name="details">Incudes the IP Address and Port to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddEsocket(EndPointDetails details)
        {
            string address = details.IpAddress?.Trim() ?? string.Empty;
            int port = details.Port;
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Port Number");
            }

            if (IPAddress.TryParse(address, out IPAddress ip))
            {
                _web.SendChatMessage(WebName, $"Add eSocket.POS {ip}:{port}");
                string result = _web.AddEsocket(ip, port);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid IP Address");
            }
        }

        /// <summary>
        /// Remove an eSocket.POS address from the OPT Config.
        /// </summary>
        /// <param name="details">Includes the IP Address and Port to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemoveEsocket(EndPointDetails details)
        {
            string address = details.IpAddress?.Trim() ?? string.Empty;
            int port = details.Port;
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Port Number");
            }

            if (IPAddress.TryParse(address.Trim(), out IPAddress ip))
            {
                _web.SendChatMessage(WebName, $"Remove eSocket.POS {ip}:{port}");
                string result = _web.RemoveEsocket(ip, port);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid IP Address");
            }
        }

        /// <summary>
        /// Start a Config Batch.
        /// Once a batch has started, config changes will not be notified to the OPT until the batch is stopped.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage StartConfigBatch()
        {
            _web.SendChatMessage(WebName, "Start Config Batch");
            string result = _web.SetConfigBatch(true);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Stop a Config Batch.
        /// Once a batch has started, config changes will not be notified to the OPT until the batch is stopped.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [HttpGet]
        public HttpResponseMessage StopConfigBatch()
        {
            _web.SendChatMessage(WebName, "Stop Config Batch");
            string result = _web.SetConfigBatch(false);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add a Wash to the Washes section of the OPT Config.
        /// </summary>
        /// <param name="details"> Includes Program Id, Product Code, Description, Price, VAT Rate, and Transaction Category and Subcategory of the wash to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddWash(WashDetails details)
        {
            byte programId = details.ProgramId;
            string productCode = details.ProductCode?.Trim() ?? string.Empty;
            string description = details.Description?.Trim() ?? string.Empty;
            float price = details.Price;
            float vatRate = details.VatRate;
            short category = details.Category;
            short subcategory = details.Subcategory;
            if (float.IsNaN(price) || price < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Price");
            }
            else if (float.IsNaN(vatRate) || vatRate < VatMin || vatRate > VatMax)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid VAT Rate");
            }

            if (!productCode.All(char.IsLetterOrDigit))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Product Code");
            }
            else
            {
                _web.SendChatMessage(WebName,
                    $"Add Wash: Program Id {programId}, Product Code {productCode}, Description {description}, Price {price}, VAT Rate {vatRate}, Category {category}, Subcategory {subcategory}");
                string result = _web.AddWash(programId, productCode, description, price, vatRate, category, subcategory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Remove a Wash from the Washes section of the OPT Config.
        /// </summary>
        /// <param name="details">Includes Program Id of the wash to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemoveWashByProgramId(WashDetails details)
        {
            byte programId = details.ProgramId;
            _web.SendChatMessage(WebName, $"Remove Wash {programId}");
            string result = _web.RemoveWashByProgramId(programId);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add a Tariff Mapping to the Cards section of the OPT Config.
        /// </summary>
        /// <param name="details">Grade Product Code and Fuel Cards Only flag of the mapping to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetTariffMappingsAddMapping(TariffMappingDetails details)
        {
            byte grade = details.Grade;
            string productCode = details.ProductCode?.Trim() ?? string.Empty;
            bool fuelCardsOnly = details.FuelCardsOnly;
            if (productCode.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName,
                    $"Set Tariff Mappings Add Mapping {grade} => {productCode}{(fuelCardsOnly ? " Fuel Cards Only" : string.Empty)}");
                string result = _web.SetTariffMappingsAddMapping(grade, productCode, fuelCardsOnly);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Tariff Mapping");
            }
        }

        /// <summary>
        /// Remove a Tariff Mapping from the Cards section of the OPT Config.
        /// </summary>
        /// <param name="details">Grade and Product Code of the mapping to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetTariffMappingsRemoveMapping(TariffMappingDetails details)
        {
            byte grade = details.Grade;
            string productCode = details.ProductCode?.Trim() ?? string.Empty;
            if (productCode.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName, $"Set Tariff Mappings Remove Mapping {grade} => {productCode}");
                string result = _web.SetTariffMappingsRemoveMapping(grade, productCode);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Tariff Mapping");
            }
        }

        /// <summary>
        /// Set the Fuel Cards Only flag for a Tariff Mapping in the Cards section of the OPT Config.
        /// </summary>
        /// <param name="details">Grade and Flag for the mapping to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetTariffMappingFuelCardsOnly(TariffMappingDetails details)
        {
            byte grade = details.Grade;
            bool flag = details.FuelCardsOnly;
            _web.SendChatMessage(WebName, $"{(flag ? "Set" : "Clear")} Tariff Mapping Fuel Cards Only for grade {grade}");
            string result = _web.SetTariffMappingFuelCardsOnly(grade, flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add a Predefined Amount to the  OPT Config.
        /// </summary>
        /// <param name="details">Amount to add.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddPredefinedAmount(PredefinedAmountDetails details)
        {
            int amount = details.Amount;
            if (amount > 0)
            {
                _web.SendChatMessage(WebName, $"Add Predefined Amount {amount}");
                string result = _web.AddPredefinedAmount(amount);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Amount");
            }
        }

        /// <summary>
        /// Remove a Predefined Amount from the OPT Config.
        /// </summary>
        /// <param name="details">Amount to remove.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemovePredefinedAmount(PredefinedAmountDetails details)
        {
            int amount = details.Amount;
            _web.SendChatMessage(WebName, $"Remove Predefined Amount {amount}");
            string result = _web.RemovePredefinedAmount(amount);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Add a Discount to the  OPT Config.
        /// </summary>
        /// <param name="details">Details including IIN, Name, Type, Value and Grade.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddDiscountCard(DiscountCardDetails details)
        {
            string iin = details.Iin?.Trim() ?? string.Empty;
            string name = details.Name?.Trim() ?? string.Empty;
            string type = details.Type?.Trim() ?? string.Empty;
            float value = details.Value;
            byte grade = details.Grade;
            if (!iin.All(char.IsDigit))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in IIN");
            }
            else if (value < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Value");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Add Discount Card {iin}, {name}, {type}, {value}, {grade}");
                string result = _web.AddDiscountCard(iin, name, type, value, grade);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Remove a Discount from the  OPT Config.
        /// </summary>
        /// <param name="details">Details including IIN.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemoveDiscountCard(DiscountCardDetails details)
        {
            string iin = details.Iin?.Trim() ?? string.Empty;
            if (!iin.All(char.IsDigit))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in IIN");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Remove Discount Card {iin}");
                string result = _web.RemoveDiscountCard(iin);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        /// <summary>
        /// Add a Whitelist Card to a Discount in the OPT Config.
        /// </summary>
        /// <param name="details">Details including IIN and PAN.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage AddDiscountWhitelist(WhitelistDetails details)
        {
            string iin = details.Iin?.Trim() ?? string.Empty;
            string pan = details.Pan?.Trim() ?? string.Empty;
            if (iin.All(char.IsDigit) && pan.All(char.IsDigit))
            {
                _web.SendChatMessage(WebName, $"Add Discount Whitelist {iin}, {pan}");
                string result = _web.AddDiscountWhitelist(iin, pan);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Whitelist");
            }
        }

        /// <summary>
        /// Remove a Whitelist Card from a Discount in the OPT Config.
        /// </summary>
        /// <param name="details">Details including IIN and PAN.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage RemoveDiscountWhitelist(WhitelistDetails details)
        {
            string iin = details.Iin?.Trim() ?? string.Empty;
            string pan = details.Pan?.Trim() ?? string.Empty;
            if (iin.All(char.IsDigit) && pan.All(char.IsDigit))
            {
                _web.SendChatMessage(WebName, $"Remove Discount Whitelist {iin}, {pan}");
                string result = _web.RemoveDiscountWhitelist(iin, pan);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in Whitelist");
            }
        }

        /// <summary>
        /// Reload the OPT configuration from the database.
        /// </summary>
        [HttpGet]
        public HttpResponseMessage ReloadOptConfiguration()
        {
            _web.SendChatMessage(WebName, "Reload OPT Configuration");
            _web.ReloadOptConfiguration();
            return Request.CreateResponse(HttpStatusCode.OK);
        }

        /// <summary>
        /// Set the receipt reprint availability timeout.
        /// </summary>
        /// <param name="details">Includes timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetReceiptReprintAvailability(ReceiptLayoutModeDetails details)
        {
            int timeout = details.ReceiptReprintAvailability;
            if (timeout < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Timeout");
            }

            _web.SendChatMessage(WebName, $"Set Receipt Timeout {timeout}");
            string result = _web.SetReceiptTimeout(timeout);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the receipt maximum count.
        /// </summary>
        /// <param name="details">Includes count to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetReceiptMaxCount(ReceiptLayoutModeDetails details)
        {
            int count = details.ReceiptMaxCount;
            if (count < 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Count");
            }

            _web.SendChatMessage(WebName, $"Set Receipt Max Count {count}");
            string result = _web.SetReceiptMaxCount(count);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set the Receipt Layout Mode for the OPT Config.
        /// </summary>
        /// <param name="details">Mode to set.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetReceiptLayoutMode(ReceiptLayoutModeDetails details)
        {
            int mode = details.Mode;
            _web.SendChatMessage(WebName, $"Set Receipt Layout Mode {mode}");
            string result = _web.SetReceiptLayoutMode(mode);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        /// <summary>
        /// Set receipt header for an OPT.
        /// </summary>
        /// <param name="details">Includes serial number of OPT to set and receipt header.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public IHttpActionResult SetReceiptHeader(ReceiptHeaderDetails details)
        {
            return SetReceiptText(details, SetReceiptHeaderText);
        }

        private string SetReceiptHeaderText(ReceiptHeaderDetails details, string optIdString)
        {
            var headers = details.ReceiptHeaders ?? Array.Empty<string>();

            _web.SendChatMessage(WebName, $"Set Global Receipt Header, OPT {optIdString}, Header {headers}");

            return _web.SetReceiptHeaders(optIdString, headers);
        }

        /// <summary>
        /// Set receipt footer for an OPT.
        /// </summary>
        /// <param name="details">Includes serial number of OPT to set and receipt footer.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public IHttpActionResult SetReceiptFooter(ReceiptHeaderDetails details)
        {
            return SetReceiptText(details, SetReceiptFooterText);
        }

        private string SetReceiptFooterText(ReceiptHeaderDetails details, string optIdString)
        {
            var headers = details.ReceiptFooters ?? Array.Empty<string>();

            _web.SendChatMessage(WebName, $"Set Global Receipt Footer, OPT {optIdString}, Footers {headers}");

            return _web.SetReceiptFooters(optIdString, headers);
        }

        private IHttpActionResult SetReceiptText(ReceiptHeaderDetails details, Func<ReceiptHeaderDetails, string, string> receiptTextFunc)
        {
            var idString = details.StringId?.Trim();
            
            if (!IsOptIdValid(idString))
            {
                return BadRequest("Invalid Characters in OPT");
            }

            var optIdString = !details.IsGlobal
                ? idString
                : null;

            var result = receiptTextFunc(details, optIdString);
            return result == null ? Ok() : BadRequest(result) as IHttpActionResult;
        }

        private static bool IsOptIdValid(string optIdString)
        {
            return string.IsNullOrWhiteSpace(optIdString) || optIdString.All(char.IsLetterOrDigit);
        }

        /// <summary>
        /// Set playlist file name for an OPT.
        /// </summary>
        /// <param name="details">Includes serial number of OPT to set and file name.</param>
        /// <returns>Error message if failed.</returns>
        [HttpPost]
        public HttpResponseMessage SetPlaylistFileName(OptDetails details)
        {
            string optIdString = details.StringId?.Trim() ?? string.Empty;
            string filename = details.PlaylistFileName?.Trim() ?? string.Empty;
            if (optIdString.All(char.IsLetterOrDigit))
            {
                _web.SendChatMessage(WebName, $"Set Playlist File Name, OPT {optIdString}, File Name {filename}");
                string result = _web.SetPlaylistFileName(optIdString, filename);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Invalid Characters in OPT");
            }
        }

        #endregion

        #region Get Data

        /// <summary>
        /// Gets all or one opt details.
        /// </summary>
        /// <param name="idString">The taget Opt Identifier. If not received or received zero, all Opts will be returned.</param>
        /// <param name="reference">The Logging reference</param>
        /// <response code="200">OPT information located, or default pump used</response>
        /// <response code="400">Invalid parameters</response>
        /// <response code="404">OPT not found for given Id</response>        
        /// <response code="500">Route exceptioned</response>
        /// <returns>An array containing the results.</returns>
        [HttpGet]
        public IHttpActionResult GetOptInfo(string idString = null, string reference = null)
        {
            var results = Loggable.GetOpts(idString, reference);

            return GetActionResult(results);
        }

        /// <summary>
        /// Gets the generic configuration for new OPTs
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <returns>The generic configuration details</returns>
        public IHttpActionResult GetGenericOptConfig(string reference = null)
        {
            var results = Loggable.GetGenericOptConfig(reference);

            return GetActionResult(results);
        }

        /// <summary>
        /// Gets Sent Config for an opt.
        /// </summary>
        /// <param name="idString">The target Opt Identifier, must be specified</param>
        /// <param name="reference">The Logging reference</param>
        /// <response code="200">OPT information located, or default pump used</response>
        /// <response code="400">Invalid parameters</response>
        /// <response code="404">OPT not found for given Id</response>
        /// <response code="500">Route exceptioned</response>
        /// <returns>An Sent Config.</returns>
        [HttpGet]
        public IHttpActionResult GetSentConfig(string idString = null, string reference = null)
        {
            var results = Loggable.GetOptSentConfig(idString, reference);

            return GetActionResult(results);
        }

        [HttpPost]
        public IHttpActionResult SendOptUnBlock(string idString = null, string reference = null)
        {
            var results = Loggable.SendOptUnBlock(idString, reference);

            return GetActionResult(results);
        }

        #endregion     
    }
}
