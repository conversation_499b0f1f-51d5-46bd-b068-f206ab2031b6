using FluentAssertions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using OPTService.Controllers;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Results;
using Xunit;

namespace OPTService.Tests.Controllers
{
    public class OptControllerTests
    {
        private readonly IHtecLogger _logger;
        private readonly IWeb _web;
        private readonly IConfigurationManager _configurationManager;

        public OptControllerTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _web = Substitute.For<IWeb, Loggable>(_logger, _configurationManager, null);
        }

        public static IEnumerable<OptDetails> GetTestData_Opts()
        {
            yield return new OptDetails()
            {
                StringId = "1",
                Pumps = new List<PumpDetails>()
            };
            yield return new OptDetails()
            {
                StringId = "2",
                Pumps = new List<PumpDetails>()
            };
        }

        #region Get OPT Info

        [Fact]
        public void test_GetOptInfo_success_response_returns_ok()
        {
            // Arrange
            var testOpts = GetTestData_Opts().ToList();
            _web.GetOpts(Arg.Any<string>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<OptDetails>>.Success(testOpts));

            var optController = CreateDefaultOptController();

            // Act
            var result = optController.GetOptInfo();

            // Assert
            var responseResult = result as ResponseMessageResult;
            responseResult.Should().NotBeNull();
            responseResult?.Response.StatusCode.Should().Be(HttpStatusCode.OK);
        }


        [Fact]
        public async Task test_GetOptInfo_success_response_all_opts()
        {
            // Arrange
            var testOpts = GetTestData_Opts().ToList();
            _web.GetOpts(Arg.Any<string>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<OptDetails>>.Success(testOpts));

            var optController = CreateDefaultOptController();

            // Act
            var result = optController.GetOptInfo();

            // Assert
            var responseResult = result as ResponseMessageResult;
            var content = await responseResult?.Response.Content.ReadAsStringAsync();
            content.Should().Be(JsonConvert.SerializeObject(testOpts));
        }

        [Fact]
        public void test_GetOptInfo_notfound()
        {
            // Arrange
            _web.GetOpts(Arg.Any<string>(), Arg.Any<string>())
                .Returns(StatusCodeResult<IEnumerable<OptDetails>>.Specific(HttpStatusCode.NotFound));

            var optController = CreateDefaultOptController();

            // Act
            var result = optController.GetOptInfo("5");

            // Assert
            var responseResult = result as ResponseMessageResult;
            responseResult.Should().NotBeNull();
            responseResult?.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        #endregion

        #region Send Config Pending

        [Theory]
        [InlineData(null, "error")]
        [InlineData("1", "error")]
        [InlineData("12abc_99xyz", "Invalid Characters in OPT Id")]
        [InlineData("*12abc99xyz", "Invalid Characters in OPT Id")]
        [InlineData("12abc99xyz^", "Invalid Characters in OPT Id")]
        public void test_send_config_pending_errors(string optId, string errorText)
        {
            // Arrange
            _web.RefreshOpt(optId).Returns(errorText);
            var optController = new OptController(_logger, _web, _configurationManager);

            // Act
            var result = optController.SendConfigPending(optId);

            // Assert
            result.Should().BeOfType<BadRequestErrorMessageResult>();
        }

        [Fact]
        public void test_send_config_pending_optId_format_error()
        {
            // Arrange
            //_web.RefreshOpt(optId).Returns("error");
            var optController = new OptController(_logger, _web, _configurationManager);

            // Act
            var result = optController.SendConfigPending("asdfg");

            // Assert
            result.Should().BeOfType<BadRequestErrorMessageResult>();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("1")]
        public void test_send_config_pending_ok(string optId)
        {
            // Arrange
            string errorText = null;
            _web.RefreshOpt(optId).Returns(errorText);
            var optController = new OptController(_logger, _web, _configurationManager);

            // Act
            var result = optController.SendConfigPending(optId);

            // Assert
            result.Should().BeOfType<OkResult>();
        }

        #endregion

        #region Receipt Header

        private IHttpActionResult ExecuteSetReceiptHeaderTest(string stringId, bool isGlobal)
        {
            // Arrange
            _web.SetReceiptHeaders(Arg.Any<string>(), Arg.Any<string[]>())
                .ReturnsNull();
            var optController = CreateDefaultOptController();

            // Act
            var details = new ReceiptHeaderDetails
            {
                StringId = stringId,
                IsGlobal = isGlobal,
                ReceiptHeaders = Array.Empty<string>(),
            };

            return optController.SetReceiptHeader(details);
        }

        [Fact]
        public void set_receipt_header_for_opt_returns_ok()
        {
            // Arrange & Act
            const string stringId = "OPT1";

            var result = ExecuteSetReceiptHeaderTest(stringId, false);

            // Assert
            result.Should().BeOfType<OkResult>();
        }
        
        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("OPT1")]
        public void set_receipt_header_global_returns_ok(string optId)
        {
            // Arrange & Act
            var result = ExecuteSetReceiptHeaderTest(optId, true);

            // Assert
            result.Should().BeOfType<OkResult>();
        }

        [Theory]
        [InlineData(null, true, null)]
        [InlineData("", true, null)]
        [InlineData("OPT1", true, null)]
        public void set_receipt_header_global_sends_opt_id_as_null(string optId, bool isGlobal, string expectedId)
        {
            // Arrange
            ExecuteSetReceiptHeaderTest(optId, isGlobal);
            
            // Assert
            _web.Received().SetReceiptHeaders(expectedId, Arg.Any<string[]>());
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void set_receipt_header_invalid_opt_id(bool isGlobal)
        {
            // Arrange & Act
            var result = ExecuteSetReceiptHeaderTest("?@#", isGlobal);

            // Assert
            result.Should().BeOfType<BadRequestErrorMessageResult>().Which.Message.Should().Be("Invalid Characters in OPT");
        }

        #endregion

        #region Receipt Footer

        private IHttpActionResult ExecuteSetReceiptFooterTest(string stringId, bool isGlobal)
        {
            // Arrange
            _web.SetReceiptFooters(Arg.Any<string>(), Arg.Any<string[]>())
                .ReturnsNull();
            var optController = CreateDefaultOptController();

            // Act
            var details = new ReceiptHeaderDetails
            {
                StringId = stringId,
                IsGlobal = isGlobal,
                ReceiptFooters = Array.Empty<string>(),
            };

            return optController.SetReceiptFooter(details);
        }

        [Fact]
        public void set_receipt_footer_for_opt_returns_ok()
        {
            // Arrange & Act
            const string stringId = "OPT1";

            var result = ExecuteSetReceiptFooterTest(stringId, false);

            // Assert
            result.Should().BeOfType<OkResult>();
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("OPT1")]
        public void set_receipt_footer_global_returns_ok(string optId)
        {
            // Arrange & Act
            var result = ExecuteSetReceiptFooterTest(optId, true);

            // Assert
            result.Should().BeOfType<OkResult>();
        }

        [Theory]
        [InlineData(null, true, null)]
        [InlineData("", true, null)]
        [InlineData("OPT1", true, null)]
        public void set_receipt_footer_global_sends_opt_id_as_null(string optId, bool isGlobal, string expectedId)
        {
            // Arrange
            ExecuteSetReceiptFooterTest(optId, isGlobal);

            // Assert
            _web.Received().SetReceiptFooters(expectedId, Arg.Any<string[]>());
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void set_receipt_footer_invalid_opt_id(bool isGlobal)
        {
            // Arrange & Act
            var result = ExecuteSetReceiptFooterTest("?@#", isGlobal);

            // Assert
            result.Should().BeOfType<BadRequestErrorMessageResult>().Which.Message.Should().Be("Invalid Characters in OPT");
        }

        #endregion

        #region Helper Methods

        private OptController CreateDefaultOptController()
        {
            return new OptController(_logger, _web, _configurationManager);
        }

        #endregion
    }
}
