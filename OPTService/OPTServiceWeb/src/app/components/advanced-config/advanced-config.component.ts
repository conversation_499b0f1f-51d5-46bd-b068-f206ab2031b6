import { Component, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { AdvancedConfigProperty } from 'src/app/core/enums/advancedConfigProperty.enum';
import { CardProperty } from 'src/app/core/enums/cardProperty.enum';
import { AdvancedConfig } from 'src/app/core/models/advancedConfig.model';
import { CardReferenceDetails } from 'src/app/core/models/cardReferenceDetails.model';
import { ConfigurationCategory } from 'src/app/core/models/configurationCategory.model';
import { SettingDetails } from 'src/app/core/models/settingDetails.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { Constants } from 'src/app/helpers/constants';
import { Options } from 'src/app/helpers/options';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';
import { LoadingService } from 'src/app/services/loading.service';
import { LocalAccountService } from 'src/app/services/local-account.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { IntegrationType } from 'src/app/core/enums/integrationType.enum';
import { PaymentTimeoutType } from 'src/app/core/enums/paymentTimeoutType.enum';

@Component({
  selector: 'app-advanced-config',
  templateUrl: './advanced-config.component.html',
  styleUrls: ['./advanced-config.component.css']
})

export class AdvancedConfigComponent implements OnInit {

  showContent: boolean = false;
  advancedConfigData: AdvancedConfig;
  cardReferenceData: CardReferenceDetails;
  signalRData: Subscription | undefined;
  localAccountsData: Subscription | undefined;
  public fuellingWaitMinutesDisabled = true;
  public oldCardReferenceList: Array<CardReferenceDetails> = [];
  activeCardReference: string = '';
  eSocketConnected: boolean = false;
  forceReload: boolean = true;

  // Dropdown options
  siteTypeOptions = Options.SiteTypes;
  posTypeOptions: Array<SettingDetails> | undefined;
  discountCardTypeOptions = Options.DiscountCardTypes;
  layoutOptions = Options.ReceiptLayouts;
  pumpTypeOptions: Array<SettingDetails> | undefined;
  mobilePaymentTypeOptions: Array<SettingDetails> | undefined;
  mobilePosTypeOptions: Array<SettingDetails> | undefined;
  bosTypeOptions: Array<SettingDetails> | undefined;
  secAuthTypeOptions: Array<SettingDetails> | undefined;
  paymentConfigTypeOptions: Array<SettingDetails> | undefined;

  // Errors
  autoAuthError: boolean = false;
  mediaChannelError: boolean = false;
  unmannedPseudoPosError: boolean = false;
  paymentTimeoutOptError: boolean = false;
  paymentTimeoutPodError: boolean = false;
  paymentTimeoutMixedError: boolean = false;
  paymentTimeoutNozzleDownError: boolean = false;
  timeoutKioskError: boolean = false;
  timeoutSecAuthError: boolean = false;
  tillNumberError: boolean = false;
  fuelCategoryError: boolean = false;
  posClaimNumberError: boolean = false;
  filePruneDaysError: boolean = false;
  transactionPruneDaysError: boolean = false;
  receiptPruneDaysError: boolean = false;
  indefiniteFuellingWaitError: boolean = false;
  fuellingWaitMinutesError: boolean = false;
  backoffAuthError: boolean = false;
  backoffPreAuthError: boolean = false;
  backoffStopStartError: boolean = false;
  backoffStopOnlyError: boolean = false;
  forwardFuelPriceUpdateError: boolean = false;
  addCardError: boolean = false;
  localAccountsEnabledError: boolean = false;
  siteTypeError: boolean = false;
  posTypeError: boolean = false;
  pumpTypeError: boolean = false;
  mobilePaymentTypeError: boolean = false;
  mobilePosTypeError: boolean = false;
  bosTypeError: boolean = false;
  secAuthTypeError: boolean = false;
  paymentConfigTypeError: boolean = false;
  siteNameError: boolean = false;
  vatNumberError: boolean = false;
  currencyCodeError: boolean = false;
  nozzleUpForKioskUseError: boolean = false;
  useReplaceNozzleScreenError: boolean = false;
  maxFillOverrideError: boolean = false;

  paymentCards: number = 0;
  fuelCards: number = 0;

  // Section collapse vars
  integrationsIsCollapsed: boolean = true;
  generalSettingsIsCollapsed: boolean = true;
  housekeepingIsCollapsed: boolean = true;
  loyaltyAvailabilityIsCollapsed: boolean = true;
  timeoutIsCollapsed: boolean = true;
  fuellingSettingsIsCollapsed: boolean = true;
  cardReferencesIsCollapsed: boolean = true;
  miscConfigurablesIsCollapsed: boolean = true;

  advancedForm = this.fb.group({
    AutoAuth: [false],
    MediaChannel: [false],
    UnmannedPseudoPos: [false],
    AsdaDayEndReport: [false],
    PaymentTimeoutOpt: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    PaymentTimeoutPod: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    PaymentTimeoutMixed: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    PaymentTimeoutNozzleDown: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    TimeoutKiosk: [0, [Validators.required, Validators.pattern('[0-9]*'), Validators.maxLength(10)]],
    TimeoutSecAuth: [0, [Validators.required, Validators.pattern('[0-9]*'), Validators.maxLength(10)]],
    TillNumber: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuelCategory: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    ForwardFuelPriceUpdate: [false],
    FuellingIndefiniteWait: [false],
    FuellingWaitMinutes: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuellingBackoffAuth: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuellingBackoffPreAuth: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuellingBackoffStopStart: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FuellingBackoffStopOnly: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    PosClaimNumber: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    FilePruneDays: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    TransactionPruneDays: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    ReceiptPruneDays: [0, [Validators.required,Validators.pattern('[0-9]*'),Validators.maxLength(10)]],
    LocalAccountsEnabled: [''],
    SiteType: [''],
    PosType: [''],
    PumpType: [''],
    MobilePaymentType: [''],
    MobilePosType: [''],
    BosType: [''],
    SecAuthType: [''],
    PaymentConfigType: [''],
    SiteName: [''],
    VatNumber: [''],
    CurrencyCode: [''],
    NozzleUpForKioskUse: [false],
    UseReplaceNozzleScreen: [false],
    MaxFillOverride: [''],
  });

  addCardForm = this.fb.group({
    Name: ['', [Validators.required]],
    FuelCard: [true],
  }); 

  constructor(
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private advancedConfigService: AdvancedConfigService,
    private localAccountService: LocalAccountService,
    private logger: NGXLogger,
    private modalService: NgbModal,
    private loadingService: LoadingService) {
    this.signalRData = this.advancedConfigService.getConfigDataChanged().subscribe((data) => {
      this.zone.run(() => {
        const showLoading = data === Constants.SignalRRefreshItemId;
        this.refreshData(showLoading, false);
        this.forceReload = false;
      });
    });

    // Local Accounts signalR message
    this.localAccountsData = this.signalRService.getLocalAccountsSignalRMessage().subscribe((data) => {
      this.zone.run(() => {
        const showLoading = data === Constants.SignalRRefreshItemId;
        this.refreshData(showLoading, true);
      });
    });

    this.cardReferenceData = new CardReferenceDetails();
    this.cardReferenceData.FuelCard = true;
    Object.keys(this.addCardForm.controls).forEach(key => {
      this.addCardForm.controls[key].valueChanges.subscribe(val => {
        this.cardReferenceData[key] = val;
      })
    })
  }

  /**
   * The getter method for the form
   */
  get advancedFormControl() {
    return this.advancedForm.controls;
  }

  /**
   * The getter method for the add card form
   */
  get addCardFormControl() {
    return this.addCardForm.controls;
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param data The signalR pushed object. 
   * Not used at the moment since all Advanced Config Data will be gotten via API call.
   */
  refreshData(showLoading: boolean = true, forceReload: boolean = false): void {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();  
    }

    this.oldCardReferenceList = this.advancedConfigData?.CardReferences;

    this.advancedConfigService.getAdvancedConfig(forceReload).subscribe(data => {
      this.logger.debug('Advanced Configuration data', data);
      this.advancedConfigData = data;

      this.advancedForm.patchValue(data);

      this.fuelCards = this.advancedConfigData.CardReferences?.filter(x => x.FuelCard == true).length;
      this.paymentCards = this.advancedConfigData.CardReferences?.filter(x => x.FuelCard == false).length;
      this.posTypeOptions = this.advancedConfigData.PosTypes;
      this.pumpTypeOptions = this.advancedConfigData.PumpTypes;
      this.mobilePaymentTypeOptions = this.advancedConfigData.MobilePaymentTypes;
      this.mobilePosTypeOptions = this.advancedConfigData.MobilePosTypes;
      this.bosTypeOptions = this.advancedConfigData.BosTypes;
      this.secAuthTypeOptions = this.advancedConfigData.SecAuthTypes;
      this.paymentConfigTypeOptions = this.advancedConfigData.PaymentConfigTypes;

      Object.keys(this.advancedForm.controls).forEach(key => {
        this.advancedForm.controls[key].valueChanges.subscribe(val => {
          this.advancedConfigData[key] = val;
        })
      })
    }, () => {
      this.logger.error('Problem getting advanced configuration');
      this.loadingService.errorDuringLoading();
    }, () => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      } else {
        this.loadingService.updateLastRefresh();
      }
    });
  }

  updateESocketConnected(connected: boolean): void {
    this.eSocketConnected = connected;
  }

  onFuellingIndefiniteWaitChanges() {
    this.advancedForm.get('FuellingIndefiniteWait')?.valueChanges.subscribe(selectedValue => {
      if (selectedValue) {
        this.fuellingWaitMinutesDisabled = true;
      }
      else {
        this.fuellingWaitMinutesDisabled = false;
      }
    });
  }

  open(content: any): void {
    this.addCardForm.reset({Name:'',FuelCard:true});
    this.modalService.open(content, { ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-add-card-reference' }).result.then((result) => {
    }, (reason) => { })
  }

  isNewCardReference(item:CardReferenceDetails, index: number){
    var result = this.oldCardReferenceList?.findIndex(x => x.Name === item.Name) < 0;
    if(result){
      this.activeCardReference = 'item' + index;
    }
    return result;
  }

  /**
   * Handles the submit event for the add card modal form
   */
  onCardFormSubmit(event?: Event): void {
    ButtonHelper.clicked(event);
    var cardName = this.cardReferenceData.Name;
    this.logger.info('Adding card reference', cardName);
    this.advancedConfigService.setCardProperty(CardProperty.FuelCard, this.cardReferenceData).subscribe(() => {
      this.logger.debug('Card reference added ok');
      this.addCardError = false;
      this.modalService.dismissAll();
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem adding card reference');
      this.addCardError = true;
      ButtonHelper.reset(event);
    });
  }  

  /**
 * Updates the automatic authentication value
 */
updateAutoAuth(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating automatic authentication', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.AutoAuth,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Automatic authentication updated ok');
    this.autoAuthError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating automatic authentication');
    this.autoAuthError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the media channel value
 */
updateMediaChannel(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating media channel', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.MediaChannel,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Media channel updated ok');
    this.mediaChannelError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating media channel');
    this.mediaChannelError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the unmanned pseudo POS value
 */
updateUnmannedPseudoPos(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating unmanned pseudo POS', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.UnmannedPseudoPos,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Unmanned pseudo POS updated ok');
    this.unmannedPseudoPosError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating unmanned pseudo POS');
    this.unmannedPseudoPosError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the payment timeout for OPT mode
 */
  updatePaymentTimeoutOpt(event?: Event): void {
    ButtonHelper.clicked(event);
    let type = PaymentTimeoutType.Opt;
    this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
    this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutOpt).subscribe(() => {
      this.logger.debug(`Payment timeout ${type} updated ok`);
      this.paymentTimeoutOptError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating payment timeout OPT');
      this.paymentTimeoutOptError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
 * Updates the payment timeout for POD mode
 */
updatePaymentTimeoutPod(event?: Event): void {
  ButtonHelper.clicked(event);
  let type = PaymentTimeoutType.Pod;
  this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
  this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutPod).subscribe(() => {
    this.logger.debug(`Payment timeout ${type} updated ok`);
    this.paymentTimeoutPodError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating payment timeout POD');
    this.paymentTimeoutPodError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the payment timeout for mixed mode
 */
updatePaymentTimeoutMixed(event?: Event): void {
  ButtonHelper.clicked(event);
  let type = PaymentTimeoutType.Mixed;
  this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
  this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutMixed).subscribe(() => {
    this.logger.debug(`Payment timeout ${type} updated ok`);
    this.paymentTimeoutMixedError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating payment timeout mixed');
    this.paymentTimeoutMixedError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the payment timeout for nozzle down mode
 */
  updatePaymentTimeoutNozzleDown(event?: Event): void {
    ButtonHelper.clicked(event);
    let type = PaymentTimeoutType.NozzleDown;
    this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
    this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutNozzleDown).subscribe(() => {
      this.logger.debug(`Payment timeout ${type} updated ok`);
      this.paymentTimeoutNozzleDownError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating payment timeout nozzle down');
      this.paymentTimeoutNozzleDownError = true;
      ButtonHelper.reset(event);
    });
  }

/**
 * Updates the timeout for kiosk mode
 */
  updateTimeoutKiosk(event?: Event): void {
    ButtonHelper.clicked(event);
    let type = PaymentTimeoutType.Kiosk;
    this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
    this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutKiosk).subscribe(() => {
      this.logger.debug(`Payment timeout ${type} updated ok`);
      this.timeoutKioskError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating timeout kiosk');
      this.timeoutKioskError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
 * Updates the timeout for secauth mode
 */
  updateTimeoutSecAuth(event?: Event): void {
    ButtonHelper.clicked(event);
    let type = PaymentTimeoutType.SecAuth;
    this.logger.info(`Updating payment timeout ${type}`, this.advancedConfigData);
    this.advancedConfigService.setPaymentTimeout(type, this.advancedConfigData, AdvancedConfigProperty.PaymentTimeoutSecAuth).subscribe(() => {
      this.logger.debug(`Payment timeout ${type} updated ok`);
      this.timeoutSecAuthError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating timeout SecAuth');
      this.timeoutSecAuthError = true;
      ButtonHelper.reset(event);
    });
  }

/**
 * Updates the till number
 */
updateTillNumber(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating till number', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.TillNumber,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Till number updated ok');
    this.tillNumberError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating till number');
    this.tillNumberError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuel category
 */
updateFuelCategory(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuel category', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuelCategory,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuel category updated ok');
    this.fuelCategoryError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuel category');
    this.fuelCategoryError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the POS claim number
 */
updatePosClaimNumber(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating POS claim number', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.PosClaimNumber,this.advancedConfigData).subscribe(() => {
    this.logger.debug('POS claim number updated ok');
    this.posClaimNumberError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating POS claim number');
    this.posClaimNumberError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the file prune days
 */
updateFilePruneDays(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating file prune days', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FilePruneDays,this.advancedConfigData).subscribe(() => {
    this.logger.debug('File prune days updated ok');
    this.filePruneDaysError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating file prune days');
    this.filePruneDaysError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the transaction prune days
 */
updateTransactionPruneDays(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating transaction prune days', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.TransactionPruneDays,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Transaction prune days updated ok');
    this.transactionPruneDaysError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating transaction prune days');
    this.transactionPruneDaysError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the receipt prune days
 */
updateReceiptPruneDays(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating receipt prune days', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.ReceiptPruneDays,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Receipt prune days updated ok');
    this.receiptPruneDaysError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating receipt prune days');
    this.receiptPruneDaysError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling indefinite wait
 */
updateFuellingIndefiniteWait(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling indefinite wait', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingIndefiniteWait,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling indefinite wait updated ok');
    this.indefiniteFuellingWaitError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling indefinite wait');
    this.indefiniteFuellingWaitError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling wait minutes
 */
updateFuellingWaitMinutes(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling wait minutes', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingWaitMinutes,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling wait minutes updated ok');
    this.fuellingWaitMinutesError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling wait minutes');
    this.fuellingWaitMinutesError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling backoff auth value
 */
updateFuellingBackoffAuth(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling backoff auth', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingBackoffAuth,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling backoff auth updated ok');
    this.backoffAuthError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling backoff auth');
    this.backoffAuthError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling backoff preauth value
 */
updateFuellingBackoffPreAuth(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling backoff preauth', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingBackoffPreAuth,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling backoff preauth updated ok');
    this.backoffPreAuthError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling backoff preauth');
    this.backoffPreAuthError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling backoff stop start value
 */
updateFuellingBackoffStopStart(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling backoff stop start', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingBackoffStopStart,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling backoff stop start updated ok');
    this.backoffStopStartError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling backoff stop start');
    this.backoffStopStartError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates the fuelling backoff stop only value
 */
updateFuellingBackoffStopOnly(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating fuelling backoff stop only', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.FuellingBackoffStopOnly,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Fuelling backoff stop only updated ok');
    this.backoffStopOnlyError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating fuelling backoff stop only');
    this.backoffStopOnlyError = true;
    ButtonHelper.reset(event);
  });
}

/**
 * Updates forward fuel price update
 */
updateForwardFuelPriceUpdate(event?: Event): void {
  ButtonHelper.clicked(event);
  this.logger.info('Updating forward fuel price update', this.advancedConfigData);
  this.advancedConfigService.setProperty(AdvancedConfigProperty.ForwardFuelPriceUpdate,this.advancedConfigData).subscribe(() => {
    this.logger.debug('Forward fuel price update updated ok');
    this.forwardFuelPriceUpdateError = false;
    ButtonHelper.reset(event);
  }, () => {
    this.logger.error('Problem updating forward fuel price update');
    this.forwardFuelPriceUpdateError = true;
    ButtonHelper.reset(event);
  });
}

  /**
   * Updates the value for site type
   */
  updateSiteType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var siteType = this.advancedForm.get('SiteType').value;
    //Call the API
    this.logger.info('Updating site type', siteType);
    this.advancedConfigService.setIntegrationType(IntegrationType.Site, siteType).subscribe(() => {
      this.logger.debug('Site type set ok');
      this.siteTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting site type');
      this.siteTypeError = true;
      ButtonHelper.reset(event);
    });
  }
  
  /**
   * Updates the value for POS Type
   */
  updatePosType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var posType = this.advancedForm.get('PosType').value;
    //Call the API
    this.logger.info('Updating POS Type', posType);
    this.advancedConfigService.setIntegrationType(IntegrationType.Pos, posType).subscribe(() => {
      this.logger.debug('POS Type set ok');
      this.posTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting POS Type');
      this.posTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for PUMP Type
   */
  updatePumpType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var pumpType = this.advancedForm.get('PumpType').value;
    //Call the API
    this.logger.info('Updating PUMP Type', pumpType);
    this.advancedConfigService.setIntegrationType(IntegrationType.Pump, pumpType).subscribe(() => {
      this.logger.debug('PUMP Type set ok');
      this.pumpTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting PUMP Type');
      this.pumpTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for Mobile (Payment) Type
   */
  updateMobilePaymentType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var mobilePaymentType = this.advancedForm.get('MobilePaymentType').value;
    //Call the API
    this.logger.info('Updating Mobile (Payment) Type', mobilePaymentType);
    this.advancedConfigService.setIntegrationType(IntegrationType.MobilePayment, mobilePaymentType).subscribe(() => {
      this.logger.debug('Mobile (Payment) Type set ok');
      this.mobilePaymentTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting Mobile (Payment) Type');
      this.mobilePaymentTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for Mobile (Pos) Type
   */
  updateMobilePosType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var mobilePosType = this.advancedForm.get('MobilePosType').value;
    //Call the API
    this.logger.info('Updating Mobile (Pos) Type', mobilePosType);
    this.advancedConfigService.setIntegrationType(IntegrationType.MobilePos, mobilePosType).subscribe(() => {
      this.logger.debug('Mobile (Pos) Type set ok');
      this.mobilePosTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting Mobile (Pos) Type');
      this.mobilePosTypeError = true;
      ButtonHelper.reset(event);
    });
  }
  /**
     * Updates the value for BackOffice Type
     */
  updateBackOfficeType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var bosType = this.advancedForm.get('BosType').value;
    //Call the API
    this.logger.info('Updating BackOffice Type', bosType);
    this.advancedConfigService.setIntegrationType(IntegrationType.BackOffice, bosType).subscribe(() => {
      this.logger.debug('BackOffice Type set ok');
      this.bosTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting BackOffice Type');
      this.bosTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
     * Updates the value for SecAuth Type
     */
  updateSecAuthType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var secAuthType = this.advancedForm.get('SecAuthType').value;
    //Call the API
    this.logger.info('Updating SecAuth Type', secAuthType);
    this.advancedConfigService.setIntegrationType(IntegrationType.SecAuth, secAuthType).subscribe(() => {
      this.logger.debug('SecAuth Type set ok');
      this.secAuthTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting SecAuth Type');
      this.secAuthTypeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
       * Updates the value for PaymentConfig Type
       */
  updatePaymentConfigType(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var paymentConfigType = this.advancedForm.get('PaymentConfigType').value;
    //Call the API
    this.logger.info('Updating PaymentConfig Type', paymentConfigType);
    this.advancedConfigService.setIntegrationType(IntegrationType.PaymentConfig, paymentConfigType).subscribe(() => {
      this.logger.debug('PaymentConfig Type set ok');
      this.paymentConfigTypeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting PaymentConfig Type');
      this.paymentConfigTypeError = true;
      ButtonHelper.reset(event);
    });
  }

/**
   * Update local accounts enabled flag
   */
  updateLocalAccountsEnabled(event?: Event): void {
    ButtonHelper.clicked(event);
    // Retrieve the data
    var localAccountsEnabled = this.advancedForm.get('LocalAccountsEnabled').value;
    // Call the API
    this.logger.info('Updating local accounts enabled flag', localAccountsEnabled);
    this.localAccountService.setLocalAccountsEnabled(localAccountsEnabled).subscribe(() => {
      this.logger.debug('Local accounts enabled flag updated ok');
      this.localAccountsEnabledError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating local accounts enabled flag');
      this.localAccountsEnabledError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for site name
   */
  updateSiteName(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var siteName = this.advancedForm.get('SiteName').value;
    //Call the API
    this.logger.info('Updating site name', siteName);
    this.advancedConfigService.setSiteName(siteName).subscribe(() => {
      this.logger.debug('Site name set ok');
      this.siteNameError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting site name');
      this.siteNameError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for VAT number
   */
  updateVatNumber(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var vatNumber = this.advancedForm.get('VatNumber').value;
    //Call the API
    this.logger.info('Updating VAT number', vatNumber);
    this.advancedConfigService.setVatNumber(vatNumber).subscribe(() => {
      this.logger.debug('VAT number set ok');
      this.vatNumberError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting VAT number');
      this.vatNumberError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for currency code
   */
  updateCurrencyCode(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var currencyCode = this.advancedForm.get('CurrencyCode').value;
    //Call the API
    this.logger.info('Updating currency code', currencyCode);
    this.advancedConfigService.setCurrencyCode(currencyCode).subscribe(() => {
      this.logger.debug('Currency code set ok');
      this.currencyCodeError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem setting currency code');
      this.currencyCodeError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for nozzle up for kiosk use
   */
  updateNozzleUpForKioskUse(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var nozzleUpForKioskUse = this.advancedForm.get('NozzleUpForKioskUse').value;
    //Call the API
    this.logger.info('Updating nozzle up for kiosk use', nozzleUpForKioskUse);
    this.advancedConfigService.setNozzleUpForKioskUse(nozzleUpForKioskUse).subscribe(() => {
      this.logger.debug('Nozzle up for kiosk use updated ok');
      this.nozzleUpForKioskUseError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating nozzle up for kiosk use');
      this.nozzleUpForKioskUseError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for use replace nozzle screen
   */
  updateUseReplaceNozzleScreen(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var useReplaceNozzleScreen = this.advancedForm.get('UseReplaceNozzleScreen').value;
    //Call the API
    this.logger.info('Updating use replace nozzle screen', useReplaceNozzleScreen);
    this.advancedConfigService.setUseReplaceNozzleScreen(useReplaceNozzleScreen).subscribe(() => {
      this.logger.debug('Use replace nozzle screen updated ok');
      this.useReplaceNozzleScreenError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating use replace nozzle screen');
      this.useReplaceNozzleScreenError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Updates the value for max fill override
   */
  updateMaxFillOverride(event?: Event): void {
    ButtonHelper.clicked(event);
    //Retrieve the data
    var maxFillOverride = this.advancedForm.get('MaxFillOverride').value;
    //Call the API
    this.logger.info('Updating max fill override', maxFillOverride);
    this.advancedConfigService.setMaxFillOverride(maxFillOverride).subscribe(() => {
      this.logger.debug('Max fill override updated ok');
      this.maxFillOverrideError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem updating max fill override');
      this.maxFillOverrideError = true;
      ButtonHelper.reset(event);
    });
  }

  /**
   * Clears the value of max fill override (Sets to zero)
   */
  clearMaxFillOverride(event?: Event): void {
    ButtonHelper.clicked(event);
    //Call the API
    this.logger.info('Clearing max fill override');
    this.advancedConfigService.clearMaxFillOverride().subscribe(() => {
      this.logger.debug('Max fill override clean ok');
      this.maxFillOverrideError = false;
      ButtonHelper.reset(event);
    }, () => {
      this.logger.error('Problem clearing max fill override');
      this.maxFillOverrideError = true;
      ButtonHelper.reset(event);
    });
  }

  ngOnInit(): void {
    this.onFuellingIndefiniteWaitChanges();
    setTimeout(()=>{
      this.refreshData(true, this.forceReload);
    }, 100);
    
  }

  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
    this.localAccountsData?.unsubscribe();
  }

  /**
   * Returns the number of config keys in a category
   * @param category category object
   * @returns number of config keys in settings category
   */
  getCategoryCount(category: ConfigurationCategory): number {
    return category.Settings? Object.keys(category.Settings).length : 0;
  }
}
