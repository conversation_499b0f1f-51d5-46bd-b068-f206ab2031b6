using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    /// <inheritdoc/>
    [Migration(20050016, "ADO#658914 - Add ExternalName column to CardReference table"), Tags(Constants.MigrationTypeSchema)]
    public class ADO658914_Add_CardReferenceExternalName : Migrationable
    {
        private const string Name = "AddExternalName";

        /// <inheritdoc/>
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.dbo.{Name}.sql");
        }

        /// <inheritdoc/>
        public override void Down()
        {

        }
    }
}
