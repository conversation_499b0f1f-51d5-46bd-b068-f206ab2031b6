using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <summary>
    /// Migration for stored procedures to support single contactless button config.
    /// </summary>
    [Migration(20030009, "HOPT-1565 - related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class Hopt1565 : MigrationableProgrammibility
    {
        /// <summary>
        /// Creates a new instance of the <see cref="Hopt1565"/> class.
        /// </summary>
        /// <param name="logger">The abstracted logger.</param>
        /// <param name="dbExecutorFactory">The factory for database connectivity.</param>
        /// <param name="fileSystem">The abstracted file system.</param>
        public Hopt1565(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) 
            : base(null, new List<string> { "dbo.GetSiteInfo" }, logger, dbExecutorFactory, fileSystem)
        {
        }
    }
}
