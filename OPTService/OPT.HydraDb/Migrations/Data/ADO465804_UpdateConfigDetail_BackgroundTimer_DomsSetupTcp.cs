using FluentMigrator;
using OPT.HydraDb.Common;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to update WORKERS::BackgroundTask:Interval:DomsSetupTcp (used in Magical Advanced Config keys)
    /// </summary>
    [Migration(20050018, "ADO#465804 - update WORKERS::BackgroundTask:Interval:DomsSetupTcp (used in Magical Advanced Config keys)"), Tags(Constants.MigrationTypeData)]
    public class ADO465804_UpdateConfigDetail_BackgroundTimer_DomsSetupTcp : Migrationable
    {
        private const string ConfigKey = "BackgroundTask:Interval:DomsSetupTcp";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{ConfigKey}', @value = '00:00:01', @category = '{cfgConstants.CategoryNameWorkers}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // Restore to Default
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{ConfigKey}', @value = '', @category = '{cfgConstants.CategoryNameWorkers}'");
        }
    }
}
