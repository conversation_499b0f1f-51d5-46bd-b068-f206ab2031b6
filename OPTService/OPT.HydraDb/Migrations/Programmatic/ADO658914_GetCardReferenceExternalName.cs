using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(20050017, "ADO#658914 - Add all External Nmae related stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO658914_GetExternalName : Migrationable
    {
        /// <inheritdoc/>
        public ADO658914_GetExternalName(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.FetchCardReferences", "dbo.SetExternalName" , "dbo.ClearExternalName" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO658914_GetExternalName), DbObjects.ToArray());
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            RestoreDbObjects(nameof(ADO658914_GetExternalName), DbObjects.Reverse().ToArray());
        }
    }
}

