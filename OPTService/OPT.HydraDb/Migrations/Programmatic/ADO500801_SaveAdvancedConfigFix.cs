using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc cref="Migrationable"/>
    [Migration(20040001, "ADO#500801 - Fix to Save AdvancedConfig (values > 256 characteres)"), Tags(Constants.MigrationTypeSchema)]
    public class ADO500801_SaveAdvancedConfigFix : MigrationableProgrammibility
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">Migration logger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        public ADO500801_SaveAdvancedConfigFix(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory, IFileSystem fileSystem) : 
            base(null, new List<string> { "dbo.SetConfigValue" }, logger, dbExecutorFactory, fileSystem)
        { }
    }
}
