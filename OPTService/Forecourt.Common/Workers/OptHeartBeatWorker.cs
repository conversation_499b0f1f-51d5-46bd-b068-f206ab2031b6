using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Messages.Opt.Messages;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Logger.Interfaces;
using OPT.Common.Workers.Interfaces;
using System.Xml.Linq;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace OPT.Common.Workers
{
    /// <inheritdoc />
    public class OptHeartbeatWorker : OptListenerWorker, IOptHeartbeatWorker
    {
        /// <inheritdoc />
        public OptHeartbeatWorker(IHtecLogManager logMan, ITelemetryWorker telemetryWorker, IListenerConnectionThread<XElement> connectionThread, IHydraDb hydraDb,
            IConfigurationManager configurationManager, IPumpCollection allPumps, IOptCollection allOpts, IControllerWorker controllerWorker, IPosIntegratorOutTransient<IMessageTracking> posWorker) :
            base(logMan, "HeartbeatOPT", telemetryWorker, connectionThread, hydraDb, configurationManager, allPumps, allOpts, controllerWorker, posWorker)
        {
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();

            var endpoint = endPoints.HeartbeatBindEndPoint;

            return new GenericEndPoint(endpoint.Address, endpoint.Port);
        }

        /// <inheritdoc cref="Htec.Foundation.Connections.Workers.Interfaces.IListenerWorker{T}.OnMessageReceived(IMessageTracking{T}, int)"/>
        protected override Result<XElement> DoOnMessageReceived(IMessageTracking<XElement> message, int id)
        {
            var request = message.Request;

            DoDeferredLogging(LogLevel.Debug, HeaderOpt, () => new[] {LogCommandInfo(request, id)});

            XElement result = null;
            RegisterReceived(id, SocketType.Heartbeat);

            if (AllOpts.TryGetOpt(id, out var opt))
            {
                if (MessageParser.ParseXElement(request) is HeartbeatRequest _)
                {
                    result = GetResponseMessage(opt, new HeartbeatResponse(ResultSuccess));
                }
            }

            return Result.SuccessIf(result != null, result, "Unknown HydraOptMessageType");
        }

        /// <inheritdoc />
        protected override void DoOnDisconnected(int? id = null)
        {
            RegisterDisconnect(SocketType.Heartbeat, id);

            base.DoOnDisconnected(id);
        }
    }
}
