USE [Hydra]
GO

PRINT N'ADO498985 - Set Contactless Custom Data overrides (in advanced config keys)..Rollback..Begin';

DECLARE @catIdGeneral INT;
SELECT @catIdGeneral = cc.Id FROM dbo.ConfigurationCategory cc WHERE cc.Category = 'GENERAL';

DECLARE @headerIdSC INT;
SELECT @headerIdSC =  ch.Id FROM dbo.ConfigurationHeader ch WHERE ch.Description = 'Site Controller';

EXEC dbo.SetConfigValue @headerId = @headerIdSC, @categoryId = @catIdGeneral, @key = 'CustomDataOverride', @value = '';

PRINT N'ADO498985 - Set Contactless Custom Data overrides (in advanced config keys)..Rollback..End';