import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class LoyaltyService {

private baseUrl: string;

  /**
   * The AdvancedConfigService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.loyaltyController.base;
  }

  /**
   * Add loyalty scheme
   * @param name The name of the loyalty scheme
   */
  public addLoyalty(name: string){
    const url = this.baseUrl + environment.loyaltyController.addLoyalty;
    this.logger.debug(`Adding loyalty: ${name} via ${url}`);
    return this.http.post(url, {Name: name});
  }

  /**
   * Delete loyalty scheme
   * @param name The name of the loyalty scheme
   */
  public deleteLoyalty(name: string){
    const url = this.baseUrl + environment.loyaltyController.deleteLoyalty;
    this.logger.debug(`Deleting loyalty: ${name} via ${url}`);
    return this.http.post(url, {Name: name});
  }

  /**
   * Set the present field for a given name
   * @param name 
   * @param present 
   */
  public setLoyaltyPresent(name: string, present: boolean){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyPresent;
    this.logger.debug(`Setting loyalty present: ${name} = ${present} via ${url}`);
    return this.http.post(url, {Name: name, Present: present});
  }

  /**
   * Set the terminal site ID field for a given name
   * @param name 
   * @param siteId 
   */
   public setLoyaltyTerminalSiteId(name: string, siteId: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalSiteId;
    this.logger.debug(`Setting loyalty terminal site ID: ${name} = ${siteId} via ${url}`);
    return this.http.post(url, {Name: name, SiteId: siteId});
  }

  /**
   * Set the terminal terminal ID field for a given name
   * @param name 
   * @param terminalId 
   */
   public setLoyaltyTerminalTerminalId(name: string, terminalId: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalTerminalId;
    this.logger.debug(`Setting loyalty terminal terminal ID: ${name} = ${terminalId} via ${url}`);
    return this.http.post(url, {Name: name, TerminalId: terminalId});
  }

  /**
   * Set the terminal footer 1 field for a given name
   * @param name 
   * @param footer1 
   */
   public setLoyaltyTerminalFooter1(name: string, footer1: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalFooter1;
    this.logger.debug(`Setting loyalty terminal footer 1: ${name} = ${footer1} via ${url}`);
    return this.http.post(url, {Name: name, Footer1: footer1});
  }

  /**
   * Set the terminal footer 2 field for a given name
   * @param name 
   * @param footer2 
   */
   public setLoyaltyTerminalFooter2(name: string, footer2: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalFooter2;
    this.logger.debug(`Setting loyalty terminal footer 2: ${name} = ${footer2} via ${url}`);
    return this.http.post(url, {Name: name, Footer2: footer2});
  }

  /**
   * Set the terminal timeout field for a given name
   * @param name 
   * @param timeout
   */
   public setLoyaltyTerminalTimeout(name: string, timeout: number){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalTimeout;
    this.logger.debug(`Setting loyalty terminal timeout: ${name} = ${timeout} via ${url}`);
    return this.http.post(url, {Name: name, Timeout: timeout});
  }

  /**
   * Set the terminal API key field for a given name
   * @param name 
   * @param apiKey 
   */
   public setLoyaltyTerminalApiKey(name: string, apiKey: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalApiKey;
    this.logger.debug(`Setting loyalty terminal API key: ${name} = ${apiKey} via ${url}`);
    return this.http.post(url, {Name: name, ApiKey: apiKey});
  }

  /**
   * Set the terminal http header field for a given name
   * @param name 
   * @param httpHeader 
   */
   public setLoyaltyTerminalHttpHeader(name: string, httpHeader: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyTerminalHttpHeader;
    this.logger.debug(`Setting loyalty terminal HTTP header: ${name} = ${httpHeader} via ${url}`);
    return this.http.post(url, {Name: name, HttpHeader: httpHeader});
  }

  /**
   * Add new host
   * @param name 
   * @param ipAddress 
   * @param port 
   */
  public setLoyaltyHostsAddHost(name: string, ipAddress: string, port: number){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyHostsAddHost;
    this.logger.debug(`Adding host: ${name}, ${ipAddress}:${port} via ${url}`);
    return this.http.post(url, {Name: name, IpAddress: ipAddress, Port: port});
  }

  /**
   * Removes an existing host
   * @param name 
   * @param ipAddress 
   * @param port 
   */
  public setLoyaltyHostsRemoveHost(name: string, ipAddress: string, port: number){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyHostsRemoveHost;
    this.logger.debug(`Removing host: ${name}, ${ipAddress}:${port} via ${url}`);
    return this.http.post(url, {Name: name, IpAddress: ipAddress, Port: port});
  }

  /**
   * Adds a new hostname
   * @param name 
   * @param hostname 
   */
  public setLoyaltyAddHostname(name: string, hostname: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyAddHostname;
    this.logger.debug(`Adding host: ${name}, ${hostname} via ${url}`);
    return this.http.post(url, {Name: name, Hostname: hostname});
  }

  /**
   * Removes an existing hostname
   * @param name 
   * @param hostname 
   */
  public setLoyaltyRemoveHostname(name: string, hostname: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyRemoveHostname;
    this.logger.debug(`Removing host: ${name}, ${hostname} via ${url}`);
    return this.http.post(url, {Name: name, Hostname: hostname});
  }

  /**
   * Adds a new Iin
   * @param name 
   * @param Low 
   * @param High 
   */
  public setLoyaltyIinsAddIin(name: string, low: string, high: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyIinsAddIin;
    this.logger.debug(`Adding IIN: ${name}, ${low}-${high} via ${url}`);
    return this.http.post(url, {Name: name, Low: low, High: high});
  }

  /**
   * Removes an existing iin
   * @param name 
   * @param Low 
   * @param High 
   */
  public setLoyaltyIinsRemoveIin(name: string, low: string, high: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyIinsRemoveIin;
    this.logger.debug(`Removing IIN: ${name}, ${low}-${high} via ${url}`);
    return this.http.post(url, {Name: name, Low: low, High: high});
  }

  /**
   * Adds a new loyalty mapping
   * @param name 
   * @param productCode 
   * @param loyaltyCode 
   */
  public setLoyaltyMappingsAddMapping(name: string, productCode: string, loyaltyCode: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyMappingsAddMapping;
    this.logger.debug(`Adding loyalty mapping: ${name}, ${productCode}->${loyaltyCode} via ${url}`);
    return this.http.post(url, {Name: name, ProductCode: productCode, LoyaltyCode: loyaltyCode});
  }

  /**
   * Removes an existing loyalty mapping
   * @param name 
   * @param productCode 
   * @param loyaltyCode 
   */
  public setLoyaltyMappingsRemoveMapping(name: string, productCode: string, loyaltyCode: string){
    const url = this.baseUrl + environment.loyaltyController.setLoyaltyMappingsRemoveMapping;
    this.logger.debug(`Removing loyalty mapping: ${name}, ${productCode}->${loyaltyCode} via ${url}`);
    return this.http.post(url, {Name: name, ProductCode: productCode, LoyaltyCode: loyaltyCode});
  }


  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }

}
