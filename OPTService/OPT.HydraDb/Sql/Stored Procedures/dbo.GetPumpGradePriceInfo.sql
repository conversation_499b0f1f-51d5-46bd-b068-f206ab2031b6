USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetPumpGradePriceInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetPumpGradePriceInfo
END
GO

CREATE PROCEDURE [dbo].[GetPumpGradePriceInfo]
AS
BEGIN
	SELECT 
		t.Id, 
        t.Pump,
		t.Hose,
        t.Grade, 
        t.Price, 
        t.CreatedDate,
        t.ModifiedDate
	FROM PumpGradePriceInfo t;	
END
GO

GRANT EXECUTE ON GetPumpGradePriceInfo TO UserRole
GO
