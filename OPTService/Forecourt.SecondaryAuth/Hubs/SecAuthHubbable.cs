using Forecourt.SecondaryAuth.Hubs.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using Microsoft.AspNet.SignalR;

namespace Forecourt.SecondaryAuth.Hubs
{
    /// <summary>
    /// SecAuth version of <see cref="Hubbable{TMessage, TMessageTracking}"/> dealing with <see cref="SecAuthMessage"/> based messaging
    /// </summary>
    /// <inheritdoc/>
    public class SecAuthHubbable : Hubbable<SecAuthMessage, IMessageTracking>, ISecAuthHubbable, ISecAuthHubbableIn
    {
        /// <inheritdoc/>
        public SecAuthHubbable(IHtecLogManager logManager, IConfigurationManager configurationManager = null) : 
            base(logManager, nameof(SecAuthHubbable), GlobalHost.ConnectionManager.GetHubContext<SecAuthHub>(), configurationManager)
        {
        }
    }
}
