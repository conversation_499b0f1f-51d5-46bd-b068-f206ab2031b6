import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, ValidationErrors } from '@angular/forms';

import { InputErrorMessagesComponent } from './input-error-messages.component';

describe('InputErrorComponent', () => {
  let component: InputErrorMessagesComponent;
  let fixture: ComponentFixture<InputErrorMessagesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ InputErrorMessagesComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InputErrorMessagesComponent);
    component = fixture.componentInstance;
    component.control = new FormControl();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getErrorMessages', () => {

    it('should return min error message', () => {
      const errors = { 'min': { min: 1 } } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Min value of 1');
    });
    
    it('should return max error message', () => {
      const errors = { 'max': { max: 100 } } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Max value of 100');
    });
        
    it('should return min length error message', () => {
      const errors = { 'minlength': { requiredLength: 2 } } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Min length of 2');
    });
            
    it('should return max length error message', () => {
      const errors = { 'maxlength': { requiredLength: 5 } } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Max length of 5');
    });

    it('should return pattern error message', () => {
      const errors = { 'pattern': '[0-9]' } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Invalid character entered');
    });

    it('should return general error message', () => {
      const errors = { 'required': true } as ValidationErrors;
      component.control.setErrors(errors);

      const result = component.getErrorMessages();

      expect(result).toContain('Please provide a valid value');
    });
  });
});
