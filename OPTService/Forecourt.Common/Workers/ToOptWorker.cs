using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Extensions;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications;
using Htec.Logger.Interfaces;
using OPT.Common.Workers.Interfaces;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Xml.Linq;
using ConnectionsConstants = Htec.Foundation.Connections.Common.Constants;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace OPT.Common.Workers
{
    public class ToOptWorker : OptListenerWorker, IToOptWorker
    {
        /// <inheritdoc />
        public ToOptWorker(IHtecLogManager logMan, ITelemetryWorker telemetryWorker, IListenerConnectionThread<XElement> connectionThread, IHydraDb hydraDb,
            IConfigurationManager configurationManager, IPumpCollection allPumps, IOptCollection allOpts, IControllerWorker controllerWorker, IPosIntegratorOutTransient<IMessageTracking> posWorker) :
            base(logMan, "ToOPT", telemetryWorker, connectionThread, hydraDb, configurationManager, allPumps, allOpts, controllerWorker, posWorker)
        {
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();

            var endpoint = endPoints.ToOptBindEndPoint;

            return new GenericEndPoint(endpoint.Address, endpoint.Port);
        }

        /// <inheritdoc cref="Htec.Foundation.Connections.Workers.Interfaces.IListenerWorker{T}.OnMessageReceived(IMessageTracking{T}, int)"/>
        protected override Result<XElement> DoOnMessageReceived(IMessageTracking<XElement> message, int id)
        {
            SetOnPumpStateActive(message, id, SocketType.ToOpt, true, out var pump);
            try
            {
                AllPumps.UpdateParentId(pump, message);

                return DoOnXmlFromToOpt(message, id);
            }
            finally
            {
                SetOnPumpStateActive(message, pump, SocketType.ToOpt, false);
            }
        }

        private Result<XElement> DoOnXmlFromToOpt(IMessageTracking<XElement> message, int id, [CallerMemberName]string methodName = null)
        {
            RegisterReceived(id, SocketType.ToOpt);

            var element = message.Request;
            var logRef = message.FullId;

            if (!(MessageParser.ParseXElement(element) is OptResponse response))
            {
                var msg = $"Incompatible message received, with Element name: {element.Name}";
                DoDeferredLogging(LogLevel.Info, HeaderEndFail, () => new[] {msg}, reference: logRef, methodName: methodName);
                return Result.Failure<XElement>(msg);
            }

            message.Context = GetShortNotificationName(response);
            if (response is HeartbeatResponse)
            {
                DoDeferredLogging(LogLevel.Debug, HeaderOpt, () => new[] {LogCommandInfo(element, id)}, reference: logRef, methodName: methodName);
            }
            else if (response is NotificationResponse)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderOpt, () => new[] {LogCommandInfo(element, id)}, reference: logRef, methodName: methodName);

                if (AllOpts.TryGetOpt(id, out var opt))
                {
                    var currentNotification = opt.NotificationResponseReceived();
                    var msg = GetShortNotificationName(currentNotification?.GetType().Name ?? ConfigConstants.Unknown);
                    message.Context = GetShortNotificationName(currentNotification ?? (XmlTyped)response);

                    if (currentNotification is DeliveredNotificationRequest deliveredNotification)
                    {
                        var pump = deliveredNotification.Notification.Pump ?? 0;
                        HydraDb.ClearDelivered(pump, message);
                        GetWorker<ITelemetryWorker>()?.DeliveredSentToOpt(pump, logRef);
                    }
                    else if (currentNotification is SignInRequiredNotificationRequest)
                    {
                        opt.SetSignInRequired(false);
                    }
                    else if (currentNotification is PaidAtKioskNotificationRequest paidAtKioskNotification)
                    {
                        if (AllPumps.TryGetPump(paidAtKioskNotification?.Notification?.Pump ?? 0, out var thePump))
                        {
                            thePump.ResetTransactionState(message.IdAsString);
                        }
                    }
                    else if (currentNotification is ModeChangeNotificationRequest modeChangeNotification)
                    {
                        var posWorker = GetWorker<IPosIntegratorOutTransient<IMessageTracking>>();
                        var modeChangeWorker = GetWorker<IPosIntegratorOutMode<IMessageTracking>>();
                        var mode = modeChangeNotification.Notification.Change.Mode;
                        var mct =
                            mode == NotificationOptMode.KioskOnly ? ModeChangeType.KioskOnly :
                            mode == NotificationOptMode.Mixed ? ModeChangeType.Mixed :
                            ModeChangeType.OutsideOnly;

                        foreach (var pump in opt.PumpList())
                        {
                            if (AllPumps.TryGetPump(pump, out var thePump))
                            {
                                posWorker?.StatusResponse(pump, message);

                                modeChangeWorker?.ModeChangedResponse(pump, mct, thePump.PreviousPumpState.ToPumpOptType(), message, true);
                            }
                        }
                    }

                    GetWorker<ITelemetryWorker>()?.MessageReceivedFromOpt(opt, msg, logRef);

                    Task.Run(() =>
                    {
                        SetThreadName(nameof(DoOnMessageReceived));
                        SendCurrentNotificationToOpt(opt, message);
                    });
                }
            }

            return Result.Success<XElement>(null);
        }

        /// <inheritdoc cref="IToOptWorker.SendCurrentNotificationToOpt"/>
        public void SendCurrentNotificationToOpt(IOpt opt, IMessageTracking currentMessage = null)
        {
            DoAction(() =>
            {
                var logRef = currentMessage?.FullId ?? LoggingReference;

                var currentNotification = opt?.GetCurrentNotification();
                if (currentNotification != null && !opt.RequestSentToOpt)
                {
                    var header = string.Empty;
                    var logMessage = $"{opt.IdString} ({opt.Id})";
                    if (!opt?.ToOptConnected ?? false)
                    {
                        header = "Forced.Heartbeat.OPT";
                        DoDeferredLogging(LogLevel.Info, header, () => new[] {logMessage}, reference: logRef);
                        SendToOptHeartbeat(opt, currentMessage);
                    }
                    else
                    {
                        header = $"{GetShortNotificationName(currentNotification)}.OPT";
                        DoDeferredLogging(LogLevel.Info, header, () => new[] {logMessage}, reference: logRef);
                        var request = opt.MessageBuilder.GetRequestMessage(currentNotification);

                        SendRequest(request.ToXElement(), opt.Id, currentMessage, () => opt.SendingRequestToOpt(currentMessage?.StaleReceiptAfterUtc));
                    }

                    GetWorker<ITelemetryWorker>()?.MessageSentToOpt(opt);
                    GetWorker<IControllerWorker>()?.SendInformation($"Sending Notification {header} to {logMessage}");
                }
            }, null);
        }

        /// <inheritdoc cref="IToOptWorker.SendToOptHeartbeat"/>
        public void SendToOptHeartbeat(IOpt opt = null, IMessageTracking currentMessage = null)
        {
            var request = GetRequestMessage(opt, new HeartbeatRequest());

            SendRequest(request, ConnectionsConstants.UnknownConnectionId, currentMessage);
        }

        /// <inheritdoc />
        protected override void DoOnDisconnected(int? id = null)
        {
            RegisterDisconnect(SocketType.ToOpt, id);

            base.DoOnDisconnected(id);
        }

    }
}
