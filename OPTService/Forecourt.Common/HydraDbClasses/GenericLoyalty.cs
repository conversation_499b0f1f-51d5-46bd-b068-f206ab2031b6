using System.Collections.Generic;

namespace OPT.Common.HydraDbClasses
{
    public class GenericLoyalty
    {
        public LoyaltyTerminal Terminal { get; }
        public IList<GenericEndPoint> Hosts { get; }
        public IList<string> Hostnames { get; }
        public IList<LoyaltyIin> Iins { get; }
        public IList<LoyaltyMapping> TariffMappings { get; }
        public bool IsPresent { get; set; }

        public GenericLoyalty(LoyaltyTerminal terminal, IList<GenericEndPoint> hosts, IList<string> hostnames, IList<LoyaltyIin> iins, IList<LoyaltyMapping> tariffMappings, bool isPresent)
        {
            Terminal = terminal;
            Hosts = hosts;
            Hostnames = hostnames;
            Iins = iins;
            TariffMappings = tariffMappings;
            IsPresent = isPresent;
        }
    }
}
