<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>9.0</LangVersion>
		<Platforms>AnyCPU;x64;x86</Platforms>
		<RuntimeIdentifiers>win7-x64;win7-x86;win-x64</RuntimeIdentifiers>
	</PropertyGroup>
	
	<PropertyGroup>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>
	
	<ItemGroup>
	  <ProjectReference Include="..\Forecourt.Common\Forecourt.Common.csproj" />
	  <ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	  <ProjectReference Include="..\Forecourt.PaymentConfiguration\Forecourt.PaymentConfiguration.csproj" />
	  <ProjectReference Include="..\Forecourt.Pos\Forecourt.Pos.csproj" />
	  <ProjectReference Include="..\Forecourt.Pump\Forecourt.Pump.csproj" />
	  <ProjectReference Include="..\Forecourt.SecondaryAuth\Forecourt.SecondaryAuth.csproj" />
	  <ProjectReference Include="..\OPT.HydraDb\OPT.HydraDb.csproj" />
	  <ProjectReference Include="..\OPT.TransactionValidator\OPT.TransactionValidator.csproj" />
	  <ProjectReference Include="..\OPTServiceWeb\OPTServiceWeb.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<Reference Include="System.Configuration.Install" />
		<PackageReference Include="Htec.Hydra.Opt.Service" Version="[2.4.0,)" />
		<PackageReference Include="Htec.Hydra.Messages.Opt" Version="[2.7.0,)" />
		<PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="[6.0.0,)" />
    </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.VisualStudio.SlowCheetah" Version="[4.0.50,)">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Unity.Abstractions" Version="[5.11.7,)" />
		<PackageReference Include="Unity.Container" Version="[5.11.11,)" />
		<PackageReference Include="Unity.WebAPI" Version="[5.4.0,)" />
		<PackageReference Include="WebActivatorEx" Version="[2.2.0,)" />
		<PackageReference Include="Microsoft.Web.Infrastructure" Version="[2.0.0,)" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="[5.2.9,)" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="[5.2.9,)" />
  		<PackageReference Include="Microsoft.AspNet.WebApi.Cors" Version="[5.2.9,)" />
  		<PackageReference Include="Microsoft.AspNet.WebApi.Owin" Version="[5.2.9,)" />
  		<PackageReference Include="Microsoft.AspNet.WebApi.WebHost" Version="[5.2.9,)" />  
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNet.SignalR.Core" Version="[2.4.3,)" />
		<PackageReference Include="Microsoft.AspNet.SignalR.SystemWeb" Version="[2.4.3,)" />
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="[7.0.0,)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Swagger-Net" Version="[8.5.10.302,)" />
	</ItemGroup>
	
	<ItemGroup>
		<PackageReference Include="Owin" Version="[1.0,)" />
		<PackageReference Include="Microsoft.Owin" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.Cors" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.FileSystems" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.Host.HttpListener" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.Hosting" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.Security" Version="[4.2.2,)" />
		<PackageReference Include="Microsoft.Owin.StaticFiles" Version="[4.2.2,)" />
	</ItemGroup>
	
	<ItemGroup>
	  <None Update="App.config">
	    <TransformOnBuild>true</TransformOnBuild>
	  </None>
	  <None Update="App.Debug.config">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>App.config</DependentUpon>
	  </None>
	  <None Update="App.Release.config">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>App.config</DependentUpon>
	  </None>
	  <None Update="connections.config">
	    <TransformOnBuild>true</TransformOnBuild>
	  </None>
	  <None Update="connections.Debug.config">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>connections.config</DependentUpon>
	  </None>
	  <None Update="connections.Release.config">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>connections.config</DependentUpon>
	  </None>
	  <None Update="Forecourt.Service.log4net.Debug.xml">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>Forecourt.Service.log4net.xml</DependentUpon>
	  </None>
	  <None Update="Forecourt.Service.log4net.Release.xml">
	    <IsTransformFile>true</IsTransformFile>
	    <DependentUpon>Forecourt.Service.log4net.xml</DependentUpon>
	  </None>
	  <None Update="Forecourt.Service.log4net.xml">
	    <TransformOnBuild>true</TransformOnBuild>
	  </None>
	</ItemGroup>

	<ItemGroup Label="DOMS Components-x64" Condition="'$(Platform)' != 'x86'">
		<None Include="..\DOMS\64bit\PSS_Forecourt_Lib.dll" Link="PSS_Forecourt_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="..\DOMS\64bit\PSS_TcpIp_Lib.dll" Link="PSS_TcpIp_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<Reference Include="PSS_Forecourt_Lib">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup Label="DOMS Components-x86" Condition="'$(Platform)' == 'x86'">
		<None Include="..\DOMS\32bit\PSS_Forecourt_Lib.dll" Link="PSS_Forecourt_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="..\DOMS\32bit\PSS_TcpIp_Lib.dll" Link="PSS_TcpIp_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<Reference Include="PSS_Forecourt_Lib">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
	</ItemGroup>
</Project>
