using CSharpFunctionalExtensions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.UpdateFileClasses;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Helpers;
using OPT.Common.HydraDb.Models;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Security;
using System.Threading;
using System.Timers;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Workers
{
    public class ControllerWorker : Connectable, IControllerWorker
    {
        private const string ValidPassword = "HT3C";
        private const string ServiceName = "HydraOPT Service";
        public const string ConfigKeyLogFolderDateFormat = ConfigConstants.ConfigKeyLogFolderDateFormat;
        public const string DefaultValueLogFolderDateFormat = ConfigConstants.DefaultValueLogFolderDateFormat;

        private readonly IInfoMessagesConfig _infoMessagesConfig;
        private const int PauseTime = 50;
        private const int SiteNameMaxLength = 32;
        private const int VatNumberMaxLength = 32;
        private const int CurrencyCodeMin = 0;
        private const int CurrencyCodeMax = 999;
        private readonly string _databaseFilename;
        private OptEndPoints _currentEndPoints = new OptEndPoints();
        private string _myIdString = OptEndPoints.DefaultHydraId;
        private const int SecondsToWaitForNewPrices = 2;
        private const int DayEndCheckMinutes = 5;
        private const string JournalAppenderName = "OPTServiceJournal";
        public const string JournalFileName = "OPTJour.txt";
        private const string UpgradeFileName = "upgrade.log";
        private const string TraceAppenderName = "OPTServiceTelemetry";
        public const string TraceFileName = "HydraOPT_Instrumentation.log";
        private const string LogAppenderName = "File_OPTService";
        private const string UnmannedJournalFileName = "UPosJournal.txt";
        private const string PrinterBitmapFile = "print_logo.bmp";
        public string UpgradeFileDirectory { get; private set; }
        private readonly string _serviceFileDirectory;
        public string RollbackFileDirectory { get; private set; }
        public string DatabaseBackupDirectory { get; private set; }
        public string LogFileDirectory { get; private set; }
        public string TraceFileDirectory { get; private set; }
        public string JournalFileDirectory { get; private set; }
        public string FuelDataUpdateFile { get; private set; }
        private string _upgradeLogFile;
        public bool IsConfigBatch { get; private set; } = false;
        private readonly IRetalixTransactionFile _retalixTransactionFile;
        private readonly IHydraTransactionFile _transactionFile;

        public IEnumerable<string> UploadedFileNames => _uploadedFiles.Keys;
        public IDictionary<byte, float> GradePriceToSet { get; } = new ConcurrentDictionary<byte, float>();

        public DateTime? NextDayEnd { get; private set; }
        public void RefreshAbout() => RefreshAboutEvent?.Invoke();
        public void PushChange(EventType eventType, EventItem itemId) => PushChangeEvent?.Invoke(eventType, itemId.ToString());
        public void PushChange(EventType eventType, string itemId = null, string additionalData = null) => PushChangeEvent?.Invoke(eventType, itemId, additionalData);
        public void PushChange(params EventType[] eventType) => eventType.ToList().ForEach(x => PushChangeEvent?.Invoke(x));

        public void PushChange(string eventType, string itemId = null, string additionalData = null)
        {
            if (Enum.TryParse(eventType, out EventType et))
            {
                PushChange(et, itemId, additionalData);

                if (et == EventType.ConnectionChanged && !string.IsNullOrEmpty(itemId))
                {
                    if (_allOpts.TryGetOpt(Convert.ToInt32(itemId), out var opt))
                    {
                        PushChange(EventType.OPTChanged, opt.IdString);
                    }
                }
            }
        }

        public void PushChange(params string[] eventType)
        {
            foreach (var et in eventType)
            {
                PushChange(et);
            }
        }

        /// <inheritdoc/>
        public Connections GetConnections()
        {
            var broker = GetWorker<IMessageBroker>();
            return new Connections(
                _allOpts.AllConnectedCount, _allOpts.FromOptConnectedCount,
                _allOpts.ToOptConnectedCount, _allOpts.HeartbeatConnectedCount,
                broker.IsConnectableConnected<IHydraPosWorker>().IsSuccess ? 1 : broker.IsConnectableConnected<IRetalixPosWorker>().IsSuccess ? 1 : 0,
                broker.IsConnectableConnected<IRetalixPosWorker>().IsSuccess ? 1 : 0,
                broker.IsConnectableConnected<IThirdPartyPosWorker>().IsSuccess ? 1 : 0,
            GetWorker<IMediaChannelWorker>()?.ConnectedCount ?? 0,
            broker.IsConnectableConnected<ISecAuthIntegratorOutTransient<IMessageTracking>>().IsSuccess,
            GetWorker<ICarWashWorker>()?.IsConnected() ?? false,
            GetWorker<IPumpWorker>()?.IsConnected() ?? false,
            GetWorker<ITankGaugeWorker>()?.IsConnected() ?? false,
            broker.IsConnectableConnected<IHydraMobileWorker>().IsSuccess,
            broker.IsConnectableConnected<IPaymentConfigIntegrator>().IsSuccess,
            broker.IsConnectableConnected<ISignalRPosOutWorker>().IsSuccess ? 1 : 0,
            broker.IsConnectableConnected<ISecAuthIntegratorOutTransient<IMessageTracking>>().IsSuccess ? 1 : 0,
            broker.IsConnectableConnected<ISignalRPosInModeWorker>().IsSuccess ? 1 : 0,
            broker.IsConnectableConnected<IBosIntegratorOut<IMessageTracking>>().IsSuccess ? 1 : 0);
        }

        /// <inheritdoc/>
        public Endpoints GetEndpoints() => new(_currentEndPoints.FromOptEndPoint, _currentEndPoints.ToOptEndPoint,
            _currentEndPoints.HeartbeatEndPoint, _currentEndPoints.HydraPosEndPoint, _currentEndPoints.RetalixPosEndPoint,
            _currentEndPoints.ThirdPartyPosEndPoint, _currentEndPoints.MediaChannelEndPoint, GetWorker<IPumpWorker>()?.EndPoint, _anprEndPoint.EndPoint,
            _carWashEndPoint.EndPoint, GetWorker<ITankGaugeWorker>()?.EndPoint, _hydraMobileEndPoint.EndPoint, _esocketList.ToArray());

        private IFromOptWorker OptWorker => GetWorker<IFromOptWorker>();
        private readonly IOptCollection _allOpts;
        private readonly IPumpCollection _allPumps;
        private readonly IUpdateWorker _updateWorker;
        private readonly ILoggingHelper _loggingHelper;
        private readonly IIntegratorFactories _integratorFactories;

        // ReSharper disable once PrivateFieldCanBeConvertedToLocalVariable
        private readonly IHydraDb _hydraDb;
        private IPaymentConfigIntegrator _paymentConfig => GetWorker<IPaymentConfigIntegrator>();
        private bool _connectionsChanged = true;
        private readonly object _lockObject1 = new object();
        private bool _esocketChanged = true;
        private readonly object _lockObject2 = new object();
        private connGenericEndPoint _pumpEndPoint = new PumpEndPoint();
        private connGenericEndPoint _anprEndPoint = new AnprEndPoint();
        private connGenericEndPoint _carWashEndPoint = new CarWashEndPoint();
        private connGenericEndPoint _tankGaugeEndPoint = new TankGaugeEndPoint();
        private connGenericEndPoint _hydraMobileEndPoint = new HydraMobileEndPoint();
        private readonly IList<IPEndPoint> _esocketList = new List<IPEndPoint>();
        private readonly IDictionary<string, byte[]> _uploadedFiles = new ConcurrentDictionary<string, byte[]>();
        private DateTime? _newPricesTime = null;

        private readonly IList<InfoMessage> _infos = new List<InfoMessage>();

        public delegate void RefreshAboutDelegate();

        public event RefreshAboutDelegate RefreshAboutEvent;

        public event PushChangeDelegate PushChangeEvent;

        #region Initialisation

        /// <summary>Constructor for Controller Worker.</summary>
        /// <param name="posWorkers">POS Worker to call.</param>
        /// <param name="mediaChannelWorker">Media Channel Worker to call.</param>
        /// <param name="secAuthInWorker">SecAuth Worker to receive from.</param>
        /// <param name="secAuthOutWorker">SecAuth Worker to call.</param>
        /// <param name="carWashWorker">Car Wash Worker to call.</param>
        /// <param name="tankGaugeJournal">Tank Gauge Worker to call.</param>
        /// <param name="posInModeWorker">Pos integrator In Mode worker</param>
        /// <param name="mobileBosJournal">Hydra Mobile Pos Worker to call.</param>
        /// <param name="pumpJournal">Pump Journal (Worker) to call.</param>
        /// <param name="pumpSetup">Pump Setup (Worker) to call.</param>
        /// <param name="journalWorker">Journal Worker to call.</param>
        /// <param name="updateWorker">Update Worker to call.</param>
        /// <param name="configUpdateWorker">Config Update Worker to call.</param>
        /// <param name="localAccountWorker">Local Account Worker to call.</param>
        /// <param name="allOpts">Collection of all OPTs.</param>
        /// <param name="allPumps">Collection of all pumps.</param>
        /// <param name="hydraDb">Hydra Database.</param>
        /// <param name="paymentConfig">eSocket Database.</param>
        /// <param name="transactionFile">Transaction File to use.</param>
        /// <param name="retalixTransactionFile">Retalix Transaction File to use.</param>
        /// <param name="logger">Htec Logger for this class.</param>
        /// <param name="databaseFilename">File name or database name.</param>
        /// <param name="loggingHelper">Helper for logging configuration.</param>
        /// <param name="configurationManager">The application configuration manager</param>
        /// <param name="telemetryWorker">Telemetry worker to use.</param>
        /// <param name="timerFactory">The timer factory</param>
        /// <param name="infoMessagesConfig">Info messages configuration</param>
        /// <param name="integratorFactories">All integrator factories</param>
        public ControllerWorker((IPosIntegratorOutTransient<IMessageTracking>, IPosIntegratorOutTransient<IMessageTracking>) posWorkers,
            IMediaChannelWorker mediaChannelWorker, ISecAuthIntegratorInTransient<IMessageTracking> secAuthInWorker, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOutWorker,
            ICarWashWorker carWashWorker, ITankGaugeIntegratorInJournal tankGaugeJournal, IPosIntegratorInMode<IMessageTracking> posInModeWorker,
            IBosIntegratorInJournal<IMessageTracking> mobileBosJournal, IPumpIntegratorInJournal pumpJournal, 
            IPumpIntegratorInSetup pumpSetup, IJournalWorker journalWorker, IUpdateWorker updateWorker,
            IConfigUpdateWorker configUpdateWorker, ILocalAccountWorker localAccountWorker, IOptCollection allOpts,
            IPumpCollection allPumps, IHydraDb hydraDb, IPaymentConfigIntegrator paymentConfig, IHydraTransactionFile transactionFile,
            IRetalixTransactionFile retalixTransactionFile, IHtecLogger logger, string databaseFilename,
            ILoggingHelper loggingHelper, IConfigurationManager configurationManager, ITelemetryWorker telemetryWorker, ITimerFactory timerFactory,
            IInfoMessagesConfig infoMessagesConfig, IIntegratorFactories integratorFactories = null)
            : base(hydraDb, logger, configurationManager, nameof(ControllerWorker), 0, timerFactory)
        {
            RegisterWorker(mobileBosJournal); 
            _updateWorker = updateWorker;
            RegisterWorker(posWorkers.Item1);
            RegisterWorker(posWorkers.Item2);
            RegisterWorker(mediaChannelWorker ?? throw new ArgumentNullException(nameof(mediaChannelWorker)));
            RegisterWorker(secAuthInWorker ?? throw new ArgumentNullException(nameof(secAuthInWorker)));
            RegisterWorker(secAuthOutWorker);
            RegisterWorker(carWashWorker);
            RegisterWorker(tankGaugeJournal ?? throw new ArgumentNullException(nameof(tankGaugeJournal)));
            RegisterWorker(pumpJournal ?? throw new ArgumentNullException(nameof(pumpJournal)));
            RegisterWorker(pumpSetup ?? throw new ArgumentNullException(nameof(pumpSetup)));
            RegisterWorker(journalWorker ?? throw new ArgumentNullException(nameof(journalWorker)));
            RegisterWorker(configUpdateWorker ?? throw new ArgumentNullException(nameof(configUpdateWorker)));
            RegisterWorker(localAccountWorker);
            RegisterWorker(telemetryWorker ?? throw new ArgumentNullException(nameof(telemetryWorker)));
            RegisterWorker(posInModeWorker ?? throw new ArgumentNullException(nameof(posInModeWorker)));
            
            _hydraDb = hydraDb;
            RegisterWorker(paymentConfig ?? throw new ArgumentNullException(nameof(paymentConfig)));
            _allOpts = allOpts;
            _allPumps = allPumps;
            _databaseFilename = databaseFilename;
            _loggingHelper = loggingHelper;
            _integratorFactories = integratorFactories ?? throw new ArgumentNullException(nameof(integratorFactories));
            _infoMessagesConfig = infoMessagesConfig ?? throw new ArgumentNullException(nameof(infoMessagesConfig));

            AllFileLocations allFileLocations = _hydraDb.GetFileLocations();
            UpgradeFileDirectory = ServiceDirectoryString(allFileLocations?.UpgradeFileDirectory);
            _serviceFileDirectory = ServiceDirectoryString(AppDomain.CurrentDomain.BaseDirectory);
            LogFileDirectory = DirectoryString(allFileLocations?.LogFileDirectory);
            TraceFileDirectory = DirectoryString(allFileLocations?.TraceFileDirectory);
            JournalFileDirectory = DirectoryString(allFileLocations?.JournalFileDirectory);
            RollbackFileDirectory = ServiceDirectoryString(allFileLocations?.RollbackFileDirectory);
            DatabaseBackupDirectory = DirectoryString(allFileLocations?.DatabaseBackupDirectory);
            FuelDataUpdateFile = allFileLocations?.FuelDataUpdateFile;
            _transactionFile = transactionFile;
            _retalixTransactionFile = retalixTransactionFile;
            NextDayEnd = _hydraDb.GetNextDayEnd();
            GetLogger().Info($"Service File Directory is {_serviceFileDirectory}");
            GetLogger().Info($"Process is {(Environment.Is64BitProcess ? "64 bit" : "32 bit")}");
            GetLogger().Info($"OS is {(Environment.Is64BitOperatingSystem ? "64 bit" : "32 bit")}");

            _loggingHelper.SetLogFileLocation(LogAppenderName, LogFileName());
            _loggingHelper.SetLogFileLocation(TraceAppenderName, Path.Combine(TraceFileDirectory, TraceFileName));
            _loggingHelper.SetLogFileLocation(JournalAppenderName, Path.Combine(JournalFileDirectory, JournalFileName));

            _upgradeLogFile = LogFileDirectory + UpgradeFileName;
            GetLogger().Info($"Upgrade Log File is {_upgradeLogFile}");
            secAuthInWorker.RegisterWorker(this);
            secAuthOutWorker?.RegisterWorker(this);
            carWashWorker.RegisterWorker(this);
            pumpJournal.RegisterWorker(this);
            pumpSetup.RegisterWorker(this);
            posWorkers.Item1?.RegisterWorker(this);
            posWorkers.Item2?.RegisterWorker(this);
            mediaChannelWorker.RegisterWorker(this);
            _updateWorker.RegisterWorker(this);
            configUpdateWorker.RegisterWorker(this);
            localAccountWorker.RegisterWorker(this);
            journalWorker.RegisterWorker(this);
            tankGaugeJournal.RegisterWorker(this);
            mobileBosJournal?.RegisterWorker(this);
            _paymentConfig.RegisterWorker(this);
            posInModeWorker.RegisterWorker(this);

            _paymentConfig.SetServiceDirectory(_serviceFileDirectory);
            journalWorker.SetUnmannedJournalFile(JournalFileDirectory + UnmannedJournalFileName);
            journalWorker.SetPrinterBitmapFile(_serviceFileDirectory + Path.DirectorySeparatorChar + PrinterBitmapFile);
        }

        protected override Result DoStart(params object[] startParams)
        {
            if (!startParams.Any())
            {
                return Result.Failure("HydraId not found");
            }

            var hydraId = startParams[0].ToString();

            GetLogger().Info($"Controller Worker Starting - Hydra ID is {hydraId}");
            OptEndPoints optEndPoints = _hydraDb.FetchEndPoints(hydraId);
            if (optEndPoints.ValidPorts)
            {
                _currentEndPoints = optEndPoints;

                _myIdString = optEndPoints.HydraId;
            }

            _pumpEndPoint = GetPumpEndPoint();
            _anprEndPoint = _hydraDb.FetchAnprEndPoint();
            _carWashEndPoint = _hydraDb.FetchCarWashEndPoint();
            _tankGaugeEndPoint = GetTankGaugeEndPoint();
            _hydraMobileEndPoint = _hydraDb.FetchHydraMobileEndPoint();
            FetchEsocketEndpoints();

            return base.DoStart(startParams);
        }

        public void ReloadListener()
        {
            DoAction(() =>
            {
                OptEndPoints optEndPoints = _hydraDb.FetchEndPoints(_myIdString);
                // ReSharper disable once MergeSequentialChecksWhenPossible
                if (optEndPoints != null && optEndPoints.ValidChange(_currentEndPoints, false, GetLogger()))
                {
                    _myIdString = optEndPoints.HydraId;
                    _currentEndPoints = optEndPoints;
                }

                PushChange(EventType.ConnectionChanged);
            }, LoggingReference);
        }

        #endregion

        #region Public Actions

        public void ConnectionsChanged()
        {
            DoAction(() =>
            {
                lock (_lockObject1)
                {
                    _connectionsChanged = true;
                }
            }, LoggingReference);
        }

        public void SendInformation(string message)
        {
            DoAction(() =>
            {
                GetLogger().Info(message);
                var maximumInfos = _infoMessagesConfig.MaxInfoMessageEntries();
                while (_infos.Count >= maximumInfos)
                {
                    _infos.RemoveAt(0);
                }

                _infos.Add(new InfoMessage(message));

                PushChange(EventType.NewMessageChanged, "Controller", message);
                PushChange(EventType.InfoMessageChanged);
            }, LoggingReference);
        }

        public IEnumerable<InfoMessage> GetInfo()
        {
            try
            {
                return _infos.ToArray();
            }
            catch (Exception)
            {
                // Nothing being logged here due to the frequency this method is called
                return new InfoMessage[0];
            }
        }

        public string OpenPump(byte pump)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump openPump) && (openPump.PumpIsClosed || openPump.ClosePending))
            {
                GetLogger().Info($"Open pump {openPump.Number} received");
                OpenPump(openPump);
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Open Pump";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ClosePump(byte pump)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump closePump) && !closePump.PumpIsClosed)
            {
                GetLogger().Info($"Close pump {closePump.Number} received");
                ClosePump(closePump);
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Close Pump";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ForceClosePump(byte pump)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump closePump) && !closePump.PumpIsClosed)
            {
                GetLogger().Info($"Force Close Pump {closePump.Number} received");
                ForceClosePump(closePump);
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Force Close Pump";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ForcePumpOutside(byte pump)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump outsidePump))
            {
                GetLogger().Info($"Force Pump {outsidePump.Number} Outside received");
                ForcePumpOutside(outsidePump);
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Force Pump Outside";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetMaxFillOverrideForFuelCards(byte pump, bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump thePump))
            {
                GetLogger().Info($"{(flag ? "Set" : "Clear")} Max Fill Override For Fuel Cards for pump {thePump.Number} received");
                thePump.SetMaxFillOverrideForFuelCards(flag);
                OptWorker?.CheckOptConfig(thePump.Opt);
                PushChange(EventType.PumpChanged, pump.ToString());
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Set Flag";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetMaxFillOverrideForPaymentCards(byte pump, bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_allPumps.TryGetPump(pump, out IPump thePump))
            {
                GetLogger().Info($"{(flag ? "Set" : "Clear")} Max Fill Override For Payment Cards for pump {thePump.Number} received");
                thePump.SetMaxFillOverrideForPaymentCards(flag);
                OptWorker?.CheckOptConfig(thePump.Opt);
                PushChange(EventType.PumpChanged, pump.ToString());
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Unable to Set Flag";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string MapToTid(byte pump, string tid)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            MapTid(pump, tid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public Result MapToOpt(byte pump, string opt, string reference = null)
        {
            return DoAction(() => MapOpt(pump, opt, reference), reference);
        }

        /// <inheritdoc />
        public Result SetGradePrice(byte grade, int price)
        {
            return SetGradePrices(new List<FuelPriceItem>() { new() { Fuel = grade, Ppu = price } });
        }

        /// <inheritdoc />
        public Result SetGradePrices(IList<FuelPriceItem> gradePrices)
        {
            if (gradePrices == null)
            {
                return Result.Failure("No Prices found!");
            }

            return DoAction(() =>
            {
                var result = GetWorker<IPumpIntegratorConfiguration>().SetGradePrices(gradePrices.Select(x => new Htec.Hydra.Core.Pump.Messages.Grade(x.Fuel, x.Ppu)), LoggingReference);
                if (result.IsSuccess)
                {
                    _newPricesTime = DateTime.Now.AddSeconds(SecondsToWaitForNewPrices);
                    foreach (var item in gradePrices)
                    {
                        GradePriceToSet[item.Fuel] = item.Ppu;
                        PushChange(EventType.FuelPriceChanged, item.Fuel.ToString());
                    }
                }

                return result;
            }, null);
        }

        public string SetGradeName(byte grade, string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetGradeName(grade, name);
            PushChange(EventType.FuelPriceChanged, grade.ToString());
            PushChange(EventType.GenericOptConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetGradeVatRate(byte grade, float vatRate)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetGradeName(grade, vatRate);
            PushChange(EventType.FuelPriceChanged, grade.ToString());
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        private void SetDefaultMode(IPump pump, bool kioskOnly, bool outsideOnly)
        {
            var prevState = pump.SetPreviousPumpState();

            if (kioskOnly)
            {
                pump.SetKioskOnly(true);
            }
            else if (outsideOnly)
            {
                pump.SetOutsideOnly(true);
            }
            else
            {
                pump.SetMixed(true);
            }
            pump.SetModeFromDefault();

            var message = new MessageTracking() { IdAsString = LoggingReference };
            DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.StatusResponse(pump.Number, message), LoggingReference);

            OptWorker?.CheckPumpState(pump, prevState, null, true);
            OptWorker?.CheckOptConfig(pump.Opt);
        }

        public Result SetDefaultMode(byte pump, bool kioskOnly, bool outsideOnly, string reference = null)
        {
            return DoAction(() =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure("Pump Not Found");
                }

                SetDefaultMode(thePump, kioskOnly, outsideOnly);
                return Result.Success();
            }, reference);
        }

        public string SetAutoAuth(bool isOn)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetAutoAuth(isOn);
            PushChange(EventType.ConnectionChanged, EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetForwardFuelPriceUpdate(bool isOn)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _updateWorker?.SetForwardFuelPriceUpdate(isOn);
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetMediaChannel(bool isOn)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            OptWorker?.SetMediaChannel(isOn);
            PushChange(EventType.FileLocationsChanged, EventItem.MediaChannel);
            PushChange(EventType.OPTChanged, EventType.ConnectionChanged, EventType.AdvancedConfigChanged, EventType.DivertDetailsChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);

            return null;
        }

        public string SetUnmannedPseudoPos(bool isOn)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetUnmannedPseudoPos(isOn);
            PushChange(EventType.ConnectionChanged, EventType.AdvancedConfigChanged, EventType.ShiftEndChanged, EventType.PumpChanged, EventType.OPTChanged, EventType.TransactionsChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetAsdaDayEndReport(bool isAsda)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetAsdaDayEndReport(isAsda);
            PushChange(EventType.ShiftEndChanged, EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetContactlessAllowed(bool isAllowed)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetContactless(isAllowed);
            OptWorker?.CheckEsocketChanges();
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }
        
        public string SetContactlessCardPreAuth(uint limit)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            if (limit == 0)
            {
                var preAuthFail = $"Invalid card pre-auth value {limit}";
                GetLogger().Debug(HeaderEndFail, () => new[]{preAuthFail});
                
                return preAuthFail;
            }

            _hydraDb.SetContactlessCardPreAuth((int)limit);
            OptWorker?.CheckEsocketChanges();
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetContactlessDevicePreAuth(uint limit)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            if (limit == 0)
            {
                var preAuthFail = $"Invalid device pre-auth value {limit}";
                GetLogger().Debug(HeaderEndFail, () => new[] { preAuthFail });

                return preAuthFail;
            }

            _hydraDb.SetContactlessDevicePreAuth((int)limit);
            OptWorker?.CheckEsocketChanges();
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetContactlessTtq(string ttq)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetContactlessTtq(ttq);
            OptWorker?.CheckEsocketChanges();
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetContactlessSingleButton(bool showSingleButton)
        {
            DoAction(() =>
            {
                _hydraDb.SetContactlessSingleButton(showSingleButton);
                OptWorker?.CheckEsocketChanges();
                FetchGenericOptConfig();
            }, string.Empty);

            return null;
        }

        public string SetReceiptHeader(string optIdString, string header)
        {
            DoAction(() =>
            {
                var opt = GetOptFromId(optIdString);

                opt.SetReceiptHeader(header);
                _hydraDb.SetReceiptHeader(opt.IdString, opt.ReceiptHeader);
                
                NotifyReceiptChange(optIdString, opt);
            }, string.Empty);

            return null;
        }

        public string SetReceiptFooter(string optIdString, string footer)
        {
            DoAction(() =>
            {
                var opt = GetOptFromId(optIdString);

                opt.SetReceiptFooter(footer);
                _hydraDb.SetReceiptFooter(opt.IdString, opt.ReceiptFooter);

                NotifyReceiptChange(optIdString, opt);
            }, string.Empty);

            return null;
        }

        private IOpt GetOptFromId(string optIdString)
        {
            var id = !string.IsNullOrWhiteSpace(optIdString)
                ? optIdString
                : _allOpts.GlobalOptStringId;

            return _allOpts.GetOptForIdString(id);
        }

        private void NotifyReceiptChange(string optIdString, IOpt opt)
        {
            if (string.IsNullOrWhiteSpace(optIdString))
            {
                FetchGenericOptConfig();
            }
            else
            {
                OptWorker?.CheckOptConfig(opt);
                PushChange(EventType.OPTChanged, opt.IdString);
            }
        }

        public string SetPlaylistFileName(string optIdString, string filename)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            IOpt opt = _allOpts.GetOptForIdString(optIdString);
            opt.SetPlaylistFileName(filename);
            _hydraDb.SetPlaylistFileName(opt.IdString, opt.PlaylistFileName);
            opt.MediaUpdateCheckRequired();
            OptWorker?.CheckOptConfig(opt);
            PushChange(EventType.OPTChanged, opt.IdString);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetLogInterval(int interval)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _allOpts.SetLogInterval(interval);
            PushChange(EventType.ShiftEndChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RestartOpt(string optIdString)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            bool result = OptWorker?.RestartOpt(optIdString) ?? false;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result ? null : "Unable to Restart OPT";
        }

        public string EngineerResetOpt(string optIdString)
        {
            string result = null;

            DoAction(() =>
            {
                var opt = _allOpts.AllOpts.FirstOrDefault(x => x.IdString.Equals(optIdString));

                if (opt == null)
                {
                    result = "OPT not found";
                }
                else
                {
                    OptWorker?.EngineerResetOptNotification(opt);
                }
            }, null);

            return result;
        }

        public string RefreshOpt(string optIdString = null)
        {
            string result = null;

            DoAction(() =>
            {
                if (optIdString != null)
                {
                    var opt = _allOpts.AllOpts.FirstOrDefault(x => x.IdString.Equals(optIdString));

                    if (opt == null)
                    {
                        result = "OPT not found";
                    }
                    else
                    {
                        OptWorker?.SendConfigPendingNotification(opt);
                    }
                }
                else
                {
                    foreach (var opt in _allOpts.AllOpts)
                    {
                        OptWorker?.SendConfigPendingNotification(opt);
                    }
                }

            }, null);
            return result;
        }

        public string RequestOptLog(string optIdString = null)
        {
            string result = null;

            DoAction(() =>
            {
                if (optIdString != null)
                {
                    var opt = _allOpts.AllOpts.FirstOrDefault(x => x.IdString.Equals(optIdString));

                    if (opt == null)
                    {
                        result = "OPT not found";
                    }
                    else
                    {
                        opt.SetLastLogTime(null);
                        OptWorker?.SendRequestLogNotification(opt);
                    }
                }
                else
                {
                    foreach (var opt in _allOpts.AllOpts)
                    {
                        opt.SetLastLogTime(null);
                        OptWorker?.SendRequestLogNotification(opt);
                    }
                }

            }, null);
            return result;
        }

        public bool SetPaymentTimeout(string modeString, int timeout)
        {
            var result = DoAction(() =>
            {
                if (string.IsNullOrEmpty(modeString))
                {
                    return Result.Failure("Missing mode value for Payment Timeout");
                }

                if (!Enum.TryParse<PaymentTimeoutType>(modeString.Trim(), true, out var mode))
                {
                    return Result.Failure($"Invalid value for Payment Timeout {modeString}");
                }

                OptWorker?.SetPaymentTimeout(mode, timeout);
                PushChange(EventType.AdvancedConfigChanged);
                return Result.Success();
            }, null);

            return result.IsSuccess;
        }

        public string SetReceiptTimeout(int timeout)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetReceiptTimeout(timeout);
            PushChange(EventType.GenericOptConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetReceiptMaxCount(int count)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetReceiptMaxCount(count);
            PushChange(EventType.GenericOptConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        private connGenericEndPoint GetPumpEndPoint() => CastTo(GetWorker<IPumpIntegratorIn<IMessageTracking>>().EndPoint);

        private connGenericEndPoint CastTo(IPEndPoint endPoint) => endPoint == null ? null : new(endPoint.Address.ToString(), endPoint.Port);

        private connGenericEndPoint GetTankGaugeEndPoint() => CastTo(GetWorker<ITankGaugeIntegratorConfiguration>().EndPoint);

        /// <inheritdoc />
        public Result SetPumpControllerAddress(IPAddress address, int port)
        {
            return DoAction(() =>
            {
                if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
                {
                    return Result.Failure("Invalid Port for Site Controller");
                }

                var result = GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetIpAddress(address, port);

                _pumpEndPoint = GetPumpEndPoint();

                return result;
            }, null);
        }

        public string SetAnpr(IPAddress address, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid Port for ANPR Link";
            }

            _hydraDb.SetAnprEndPoint(address, port);
            _anprEndPoint = _hydraDb.FetchAnprEndPoint();
            GetWorker<IAnprWorker>()?.Restart();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetCarWash(IPAddress address, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid Port for Car Wash Link";
            }

            _hydraDb.SetCarWashEndPoint(address, port);
            _carWashEndPoint = _hydraDb.FetchCarWashEndPoint();
            GetWorker<ICarWashWorker>()?.Restart();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public Result SetTankGaugeAddress(IPAddress address, int port)
        {
            return DoAction(() =>
            {
                if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
                {
                    return Result.Failure("Invalid Port for Tank Gauge");
                }

                var result = GetWorker<ITankGaugeIntegratorConfiguration>().SetIpAddress(address, port);

                _tankGaugeEndPoint = GetTankGaugeEndPoint();

                return result;
            }, null);
        }

        public string SetHydraMobile(IPAddress address, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (port < IPEndPoint.MinPort || port > IPEndPoint.MaxPort)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid Port for Hydra Mobile";
            }

            _hydraDb.SetHydraMobileEndPoint(address, port);
            _hydraMobileEndPoint = _hydraDb.FetchHydraMobileEndPoint();
            GetWorker<IHydraMobileWorker>()?.Restart();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetServicePorts
            (int fromOpt, int toOpt, int heartbeat, int hydraPos, int retalixPos, int thirdPartyPos, int mediaChannelPort)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptEndPoints endPoints = new OptEndPoints(_myIdString, _currentEndPoints.ToOptEndPoint.Address.ToString(), fromOpt, toOpt,
                heartbeat, hydraPos, retalixPos, thirdPartyPos, mediaChannelPort, _currentEndPoints.AutoAuth,
                _currentEndPoints.MediaChannel, _currentEndPoints.UnmannedPseudoPos);
            if (endPoints.ValidChange(_currentEndPoints, true, GetLogger()))
            {
                _hydraDb.SetServicePorts(_myIdString, fromOpt, toOpt, heartbeat, hydraPos, retalixPos, thirdPartyPos, mediaChannelPort);
                // TODO: OptWorker.Reload, reloads ControllerWorker!!
                // TODO: Inconsistent behaviour here!!
                OptWorker?.Restart();
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return null;
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Not a Valid Change of Service Ports";
            }
        }

        public string SetServiceAddress(IPAddress address)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetServiceAddress(_myIdString, address);
            // TODO: OptWorker.Reload, reloads ControllerWorker!!
            // TODO: Inconsistent behaviour here!! And Duplication!!!
            FetchGenericOptConfig();
            OptWorker?.Restart();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddEsocket(IPAddress address, int port)
        {
            return DoAddRemoveESocket(() => _hydraDb.AddEsocket(address, port));
        }

        private string DoAddRemoveESocket(Action action, [CallerMemberName]string methodName = null)
        {
            GetLogger().Debug(HeaderBegin);
            action();
            FetchGenericOptConfig();
            FetchEsocketEndpoints();
             OptWorker?.CheckEsocketChanges();
            GetLogger().Debug(HeaderEnd);
            return null;

        }

        public string RemoveEsocket(IPAddress address, int port)
        {
            return DoAddRemoveESocket(() => _hydraDb.RemoveEsocket(address, port));
        }

        public void ReloadOptConfiguration()
        {
            DoAction(() =>
            {
                OptWorker?.Restart();
                PushChange(EventType.PumpChanged, EventType.OPTChanged, EventType.GenericOptConfigChanged);
            }, LoggingReference);
        }

        public string DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            OptWorker?.DivertOptService(address, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort);
            PushChange(EventType.DivertDetailsChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);

            return null;
        }

        public string CancelDivertOptService()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            OptWorker?.CancelDivertOptService();
            PushChange(EventType.DivertDetailsChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);

            return null;
        }

        public bool IsOptServiceDiverted
            (out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort, out int mediaChannelPort)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (OptWorker == null)
            {
                address = _currentEndPoints.HeartbeatEndPoint.Address;
                fromOptPort = _currentEndPoints.FromOptEndPoint.Port;
                toOptPort = _currentEndPoints.ToOptEndPoint.Port;
                heartbeatPort = _currentEndPoints.HeartbeatEndPoint.Port;
                mediaChannelPort = _currentEndPoints.MediaChannelEndPoint.Port;
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return OptWorker.IsOptServiceDiverted(out address, out fromOptPort, out toOptPort, out heartbeatPort,
                    out mediaChannelPort);
            }
        }

        public string SetConfigBatch(bool isBatch)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            IsConfigBatch = isBatch;
            PushChange(EventType.GenericOptConfigChanged, EventType.LocalAccountsChanged);
            if (!isBatch)
            {
                foreach (IOpt opt in _allOpts.AllOpts)
                {
                    OptWorker?.CheckOptConfig(opt);
                }
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddGenericLoyalty(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);

            _hydraDb.AddGenericLoyalty(name);
            FetchGenericOptConfig();

            PushChange(EventType.AdvancedConfigChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string DeleteGenericLoyalty(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.DeleteGenericLoyalty(name);
            FetchGenericOptConfig();

            PushChange(EventType.AdvancedConfigChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetGenericLoyaltyPresent(string name, bool present)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetGenericLoyaltyPresent(name, present);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }


        public string SetGenericLoyalty(string name, GenericLoyalty loyalty)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (loyalty?.TariffMappings != null)
            {
                bool mappingDuplicates = loyalty.TariffMappings.GroupBy(x => new {x.ProductCode, x.LoyaltyCode}).Any(y => y.Count() > 1);
                bool productCodeDuplicates = loyalty.TariffMappings.GroupBy(x => x.ProductCode).Any(y => y.Count() > 1);
                bool loyaltyCodeDuplicates = loyalty.TariffMappings.GroupBy(x => x.LoyaltyCode).Any(y => y.Count() > 1);
                if (mappingDuplicates)
                {
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return "Duplicate mapping";
                }
                else if (productCodeDuplicates && loyaltyCodeDuplicates)
                {
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return "Duplicate product code and duplicate loyalty code";
                }
                else if (productCodeDuplicates)
                {
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return "Duplicate product code";
                }
                else if (loyaltyCodeDuplicates)
                {
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return "Duplicate loyalty code";
                }
            }

            _hydraDb.SetGenericLoyalty(name, loyalty);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetTariffMappings(IList<TariffMapping> mappings)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            bool mappingDuplicates = mappings.GroupBy(x => new {x.Grade, x.ProductCode}).Any(y => y.Count() > 1);
            bool gradeDuplicates = mappings.GroupBy(x => x.Grade).Any(y => y.Count() > 1);
            bool productCodeDuplicates = mappings.GroupBy(x => x.ProductCode).Any(y => y.Count() > 1);
            if (mappingDuplicates)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Duplicate mapping";
            }
            else if (gradeDuplicates && productCodeDuplicates)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Duplicate grade and product code";
            }
            else if (gradeDuplicates)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Duplicate grade";
            }
            else if (productCodeDuplicates)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Duplicate product code";
            }

            _hydraDb.SetTariffMappings(mappings);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetTariffMappingFuelCardsOnly(byte grade, bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetTariffMappingFuelCardsOnly(grade, flag);
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPredefinedAmounts(IList<int> amounts)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            bool duplicates = amounts.GroupBy(x => x).Any(y => y.Count() > 1);
            if (duplicates)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Duplicate amount";
            }

            _hydraDb.SetPredefinedAmounts(amounts);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddWash(Wash wash)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.AddWash(wash);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveWashByProgramId(byte programId)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.RemoveWashByProgramId(programId);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetRetalixPosPrimaryIpAddress(IPAddress address)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetRetalixPosPrimaryIpAddress(address);
            PushChange(EventType.ConnectionChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketConnectionString(string newConnectionString)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetConnectionString(newConnectionString);
            _hydraDb.SetEsocketConnectionString(newConnectionString);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketUseConnectionString(bool useConnectionString)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetUseConnectionString(useConnectionString);
            _hydraDb.SetEsocketUseConnectionString(useConnectionString);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketConfigFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetConfigFile(fileName);
            _hydraDb.SetEsocketConfigFile(fileName);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketKeystoreFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetKeystoreFile(fileName);
            _hydraDb.SetEsocketKeystoreFile(fileName);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketDbUrl(string url)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetDbUrl(url);
            _hydraDb.SetEsocketDbUrl(url);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketOverrideProperties(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetOverrideProperties(flag);
            _hydraDb.SetEsocketOverrideProperties(flag);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketOverrideKeystore(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetOverrideKeystore(flag);
            _hydraDb.SetEsocketOverrideKeystore(flag);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetEsocketOverrideUrl(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetOverrideUrl(flag);
            _hydraDb.SetEsocketOverrideUrl(flag);
            SetEsocketChanged();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public void SetEsocketChanged()
        {
            DoAction(() =>
            {
                lock (_lockObject2)
                {
                    _esocketChanged = true;
                }
                PushChange(EventType.AdvancedConfigChanged);
            }, LoggingReference);
        }

        public string SetEsocketOverrideContactless(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            
            _paymentConfig.SetOverrideContactless(flag);
            _hydraDb.SetEsocketOverrideContactless(flag);
            FetchGenericOptConfig();
            GetWorker<IConfigUpdateWorker>().SetContactlessPropertiesFile(_paymentConfig.CurrentContactlessFile);
            PushChange(EventType.FileLocationsChanged, EventItem.EsocketOverrideContactless);
            
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public static string DirectoryString(string directory)
        {
            if (string.IsNullOrWhiteSpace(directory) || directory.EndsWith($"{Path.DirectorySeparatorChar}"))
            {
                return directory;
            }
            else
            {
                return directory + Path.DirectorySeparatorChar;
            }
        }

        private static string ServiceDirectoryString(string directory)
        {
            while (!string.IsNullOrWhiteSpace(directory) && directory.EndsWith($"{Path.DirectorySeparatorChar}"))
            {
                directory = directory.Substring(0, directory.Length - 1);
            }

            return directory;
        }

        private static string PathString(string directory, string filename)
        {
            return DirectoryString(directory) + filename;
        }

        public string SetRetalixTransactionFileDirectory(string directory = null)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _retalixTransactionFile?.SetFileDirectory(DirectoryString(directory));
            GetWorker<IConfigUpdateWorker>().SetRetalixTransactionDirectory(directory);
            PushChange(EventType.FileLocationsChanged, EventItem.RetalixTransactionFileDirectory);
            PushChange(EventType.ConnectionChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetTransactionFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _transactionFile.SetFileDirectory(DirectoryString(directory));
            GetWorker<IConfigUpdateWorker>().SetTransactionDirectory(directory);
            PushChange(EventType.FileLocationsChanged, EventItem.TransactionFileDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetWhitelistDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetWhitelistDirectory(DirectoryString(directory));
            PushChange(EventType.FileLocationsChanged, EventItem.WhitelistDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetLayoutDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetLayoutDirectory(DirectoryString(directory));
            PushChange(EventType.FileLocationsChanged, EventItem.LayoutDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetSoftwareDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetSoftwareDirectory(DirectoryString(directory));
            OptWorker?.FetchAvailableSoftware();
            PushChange(EventType.FileLocationsChanged, EventItem.SoftwareDirectory);
            PushChange(EventType.OPTChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetMediaDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetMediaDirectory(DirectoryString(directory));
            OptWorker?.FetchMediaFilesList();
            PushChange(EventType.FileLocationsChanged, EventItem.MediaDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPlaylistDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetPlaylistDirectory(DirectoryString(directory));
            OptWorker?.FetchPlaylistFilesList();
            PushChange(EventType.FileLocationsChanged, EventItem.PlaylistDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetOptLogFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.SetOptLogFileDirectory(DirectoryString(directory));
            PushChange(EventType.FileLocationsChanged, EventItem.OptLogFileDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        private string LogFileName()
        {
            return Path.Combine(LogFileDirectory, $"%date{{{ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyLogFolderDateFormat, DefaultValueLogFolderDateFormat, Logger)}}}", "HydraOPTService",  "OPTService.log"); //should be IFileSystem, but not right now
        }

        public string SetLogFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Log File Directory is empty";
            }

            LogFileDirectory = DirectoryString(directory);
            _hydraDb.SetLogFileDirectory(LogFileDirectory);
            _loggingHelper.SetLogFileLocation(LogAppenderName, LogFileName());
            _upgradeLogFile = LogFileDirectory + UpgradeFileName;
            GetWorker<IConfigUpdateWorker>().SetLogDirectory(LogFileDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.LogFileDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetTraceFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Log File Directory is empty";
            }

            TraceFileDirectory = DirectoryString(directory);
            _hydraDb.SetTraceFileDirectory(TraceFileDirectory);
            _loggingHelper.SetLogFileLocation(TraceAppenderName, Path.Combine(TraceFileDirectory, TraceFileName));
            GetWorker<IConfigUpdateWorker>().SetTracesDirectory(TraceFileDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.TraceFileDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetJournalFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrWhiteSpace(directory))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Log File Directory is empty";
            }

            JournalFileDirectory = DirectoryString(directory);
            _hydraDb.SetJournalFileDirectory(JournalFileDirectory);
            _loggingHelper.SetLogFileLocation(JournalAppenderName, Path.Combine(JournalFileDirectory, JournalFileName));
            GetWorker<IJournalWorker>().SetUnmannedJournalFile(JournalFileDirectory + UnmannedJournalFileName);
            GetWorker<IConfigUpdateWorker>().SetJournalDirectory(JournalFileDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.JournalFileDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetReceivedUpdateDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _updateWorker.SetReceivedUpdateDirectory(DirectoryString(directory));
            PushChange(EventType.FileLocationsChanged, EventItem.ReceivedUpdateDirectory);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetContactlessPropertiesFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _paymentConfig.SetContactlessFile(fileName);
            _hydraDb.SetContactlessPropertiesFile(fileName);
            GetWorker<IConfigUpdateWorker>().SetContactlessPropertiesFile(_paymentConfig.CurrentContactlessFile);
            FetchGenericOptConfig();
            PushChange(EventType.FileLocationsChanged, EventItem.ContactlessPropertiesFile);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuelDataUpdateFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            FuelDataUpdateFile = fileName;
            _hydraDb.SetFuelDataUpdateFile(FuelDataUpdateFile);
            PushChange(EventType.FileLocationsChanged, EventItem.FuelDataUpdateFile);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetUpgradeFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            UpgradeFileDirectory = ServiceDirectoryString(directory);
            _hydraDb.SetUpgradeFileDirectory(UpgradeFileDirectory);
            GetWorker<IConfigUpdateWorker>().SetUpgradeDirectory(UpgradeFileDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.UpgradeFileDirectory);
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }


        public string SetRollbackFileDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            RollbackFileDirectory = ServiceDirectoryString(directory);
            _hydraDb.SetRollbackFileDirectory(RollbackFileDirectory);
            GetWorker<IConfigUpdateWorker>().SetRollbackDirectory(RollbackFileDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.RollbackFileDirectory);
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetDatabaseBackupDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            DatabaseBackupDirectory = DirectoryString(directory);
            _hydraDb.SetDatabaseBackupDirectory(DatabaseBackupDirectory);
            GetWorker<IConfigUpdateWorker>().SetDatabaseBackupDirectory(DatabaseBackupDirectory);
            PushChange(EventType.FileLocationsChanged, EventItem.DatabaseBackupDirectory);
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetReceiptLayoutMode(int mode)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetReceiptLayoutMode(mode);
            FetchGenericOptConfig();

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetSiteName(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (name.Length > SiteNameMaxLength)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid Site Name";
            }

            _hydraDb.SetSiteName(name);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetVatNumber(string number)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (number.Length > VatNumberMaxLength)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid VAT Number";
            }

            _hydraDb.SetVatNumber(number);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetCurrencyCode(int number)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (number < CurrencyCodeMin || number > CurrencyCodeMax)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Invalid Currency Code";
            }

            _hydraDb.SetCurrencyCode(number);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetNozzleUpForKioskUse(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetNozzleUpForKioskUse(flag);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetUseReplaceNozzleScreen(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetUseReplaceNozzleScreen(flag);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetMaxFillOverride(uint maxFillOverride)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.SetMaxFillOverride(maxFillOverride);
            FetchGenericOptConfig();
            PushChange(EventType.AdvancedConfigChanged, EventType.PumpChanged);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetNextDayEnd(DateTime? dayEnd)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (dayEnd == null)
            {
                NextDayEnd = null;
            }
            else
            {
                NextDayEnd = DateTime.Today.Add(dayEnd.Value.TimeOfDay);
                if (NextDayEnd < DateTime.Now)
                {
                    NextDayEnd = NextDayEnd.Value.AddDays(1);
                }
            }

            _hydraDb.SetNextDayEnd(NextDayEnd);
            PushChange(EventType.ShiftEndChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddDiscountCard(string iin, string name, string type, float value, byte grade)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.AddDiscountCard(iin, name, type, value, grade);
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveDiscountCard(string iin)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.RemoveDiscountCard(iin);
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.AddDiscountWhitelist(iin, pan);
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.RemoveDiscountWhitelist(iin, pan);
            FetchGenericOptConfig();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveLocalAccountCustomer(string customerReference)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetWorker<ILocalAccountWorker>().Delete(customerReference);
            PushChange(EventType.GenericOptConfigChanged, EventType.LocalAccountsChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddLocalAccountCustomer
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, bool pin, bool printValue,
            bool allowLoyalty)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            var localAccountWorker = GetWorker<ILocalAccountWorker>();

            if (string.IsNullOrEmpty(customerReference))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Customer Reference is empty";
            }

            localAccountWorker.Add(customerReference, name, transactionsAllowed, transactionLimit, fuelOnly, registrationEntry,
                mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, LoggingReference);
            localAccountWorker.SetFlags(customerReference, pin, printValue, allowLoyalty, LoggingReference);
            PushChange(EventType.GenericOptConfigChanged);
            PushChange(EventType.LocalAccountsChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetLocalAccountCustomerBalance(string customerReference, uint balance)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetWorker<ILocalAccountWorker>().Balance(customerReference, balance, LoggingReference);
            PushChange(EventType.LocalAccountsChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddLocalAccountCardWithoutRestrictions(string customerReference, string pan, string description, float discount)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrEmpty(pan))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "PAN is empty";
            }

            GetWorker<ILocalAccountWorker>().AddCardWithoutRestrictions(pan, customerReference, description, discount, LoggingReference);
            PushChange(EventType.GenericOptConfigChanged);
            PushChange(EventType.LocalAccountsChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AddLocalAccountCardWithRestrictions
        (string customerReference, string pan, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (string.IsNullOrEmpty(pan))
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "PAN is empty";
            }

            GetWorker<ILocalAccountWorker>().AddCardWithRestrictions(pan, customerReference, description, discount, unleaded, diesel, lpg, lrp, gasOil,
                adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods, LoggingReference);
            PushChange(EventType.GenericOptConfigChanged);
            PushChange(EventType.LocalAccountsChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetLocalAccountCardHot(string pan, bool hot)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            var localAccountWorker = GetWorker<ILocalAccountWorker>();

            if (hot)
            {
                localAccountWorker.HotCard(pan, LoggingReference);
            }
            else
            {
                localAccountWorker.OkCard(pan, LoggingReference);
            }

            PushChange(EventType.LocalAccountsChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveLocalAccountCard(string pan)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetWorker<ILocalAccountWorker>().DeleteCard(pan, LoggingReference);
            PushChange(EventType.GenericOptConfigChanged);
            PushChange(EventType.LocalAccountsChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        /// <summary>
        /// Retrieves all categories information
        /// </summary>
        public IList<CategoryConfiguration> GetCategoriesConfiguration()
        {
            return _hydraDb.GetCategoriesConfiguration();
        }

        #endregion

        #region Local Actions

        private void FetchGenericOptConfig(bool checkEsocket = false, bool forceRefresh = true)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptWorker?.FetchGenericOptConfig(checkEsocket, forceRefresh);
            PushChange(EventType.GenericOptConfigChanged, EventType.OPTChanged);
            foreach (IOpt opt in _allOpts.AllOpts)
            {
                opt.ConfigCheckRequired();
                OptWorker?.CheckOptConfig(opt);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            var pumpWorker = GetWorker<IPumpIntegratorInJournal>();
            var journalWorker = GetWorker<IBosIntegratorInJournal<IMessageTracking>>();
            SetThreadName();
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (_newPricesTime.HasValue && _newPricesTime.Value < DateTime.Now && GetWorker<IMessageBroker>().IsConnectableConnected<IPumpIntegratorInJournal>().IsSuccess)
            {
                pumpWorker.RequestPrices();
                _newPricesTime = null;
            }

            if (NextDayEnd != null && NextDayEnd < DateTime.Now)
            {
                var requested = Result.Success();
                if (NextDayEnd > journalWorker.DayEndTime.AddMinutes(DayEndCheckMinutes))
                {
                    requested = journalWorker.RequestDayEnd();
                }

                if (requested.IsSuccess)
                {
                    SetNextDayEnd(NextDayEnd);
                }
            }

            var sendXml = false;
            lock (_lockObject1)
            {
                if (_connectionsChanged)
                {
                    sendXml = true;
                    _connectionsChanged = false;
                }
            }

            if (sendXml)
            {
                PushChange(EventType.OPTChanged);
                OptWorker?.SendToOptHeartbeat();
                OptWorker?.CheckConfigNeeded();
            }

            var eSocketChange = false;
            lock (_lockObject2)
            {
                if (_esocketChanged)
                {
                    eSocketChange = true;
                    _esocketChanged = false;
                }
            }

            if (eSocketChange)
            {
                var init = _allPumps.AllPumps.Count() == 0;
                FetchGenericOptConfig(true, false);
                _allPumps.AllocatePumps(_paymentConfig?.CheckGetAllPumpTids(LoggingReference));

                if (init)
                {
                    foreach (var pump in _allPumps.AllPumps)
                    {
                        pump.SetModeFromDefault(LoggingReference);
                    }
                }

                PushChange(EventType.FileLocationsChanged, EventType.TidsChanged, EventType.PumpChanged);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void FetchEsocketEndpoints()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _esocketList.Clear();
            var eSocketEndPoints = _paymentConfig.EndPoints;
            foreach (var endpoint in eSocketEndPoints)
            {
                _esocketList.Add(new connGenericEndPoint(endpoint.Item1, endpoint.Item2).EndPoint);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void OpenPump(IPump pump)
        {
            DoPumpAction(pump, "OpenPump", (p) =>
            {
                GetWorker<IPumpIntegratorInSetup>().OpenPump(p.Number);                
                p.OpenPump();
            });
        }

        private void ClosePump(IPump pump)
        {
            DoPumpAction(pump, "ClosePump", (p) =>
            {
                GetWorker<IPumpIntegratorInSetup>().ClosePump(p.Number);
                p.ClosePump();
            });
        }

        private void ForceClosePump(IPump pump)
        {
            DoPumpAction(pump, "ForceClosePump", (p) =>
            {
                GetWorker<IPumpIntegratorInSetup>().ClosePump(pump.Number);
                p.ForceClosePump();
            });
        }

        private void ForcePumpOutside(IPump pump)
        {
            DoPumpAction(pump, "ForcePumpOutside", (p) => { p.SetOutsideOnly(); });
        }

        private void DoPumpAction(IPump pump, string logHeader, Action<IPump> action)
        {
            var message = new MessageTracking();
            DoAction(() => {

                DoDeferredLogging(LogLevel.Info, logHeader, () => new[] { $"{pump.Number}"});
                var prevState = pump.SetPreviousPumpState();

                action(pump);
                DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.StatusResponse(pump.Number, message), message.FullId);

                OptWorker?.CheckPumpState(pump, prevState);
                if (pump.Opt != null)
                {
                    PushChange(EventType.OPTChanged, pump.Opt.IdString);
                }
                PushChange(EventType.PumpChanged, pump.Number.ToString());

            }, message.FullId);
        }

        public string PerformShiftEnd()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Perform Shift End");
            GetWorker<IBosIntegratorInJournal<IMessageTracking>>().RequestShiftEnd();
            PushChange(EventType.ShiftEndChanged, EventType.TransactionsChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string PerformDayEnd()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Perform Day End");
            GetWorker<IBosIntegratorInJournal<IMessageTracking>>().RequestDayEnd();
            PushChange(EventType.ShiftEndChanged, EventType.TransactionsChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        private void MapTid(byte pump, string tid)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            IPump fromPump = _allPumps.GetPumpForTid(tid);
            if (fromPump != null && fromPump.Number != pump)
            {
                fromPump.SetTid(null);
            }

            IPump toPump = _allPumps.GetPump(pump);
            toPump.SetTid(tid);
            OptWorker?.CheckOptConfig(toPump.Opt);
            if (fromPump?.Opt != toPump.Opt)
            {
                OptWorker?.CheckOptConfig(fromPump?.Opt);
            }

            _hydraDb.MapTid(pump, tid);
            if (toPump?.Opt != null)
            {
                PushChange(EventType.OPTChanged, toPump.Opt.IdString);
            }
            PushChange(EventType.PumpChanged, pump.ToString());
            if (fromPump != null && fromPump.Number != pump)
            {
                if (fromPump?.Opt != null) 
                {
                    PushChange(EventType.OPTChanged, fromPump.Opt.IdString);
                }                    
                PushChange(EventType.PumpChanged, fromPump.Number.ToString());
            }
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private Result MapOpt(byte pump, string opt, string reference = null)
        {
            return DoAction(() =>
            {
                var mappedPump = _allPumps.GetPump(pump);
                var fromOpt = mappedPump.Opt;
                var toOpt = _allOpts.GetOptForIdString(opt);

                mappedPump.SetOpt(toOpt);

                if (fromOpt != toOpt)
                {
                    fromOpt?.RemovePump(mappedPump);
                    fromOpt?.ConfigCheckRequired();
                    toOpt?.AddPump(mappedPump);
                    toOpt?.ConfigCheckRequired();
                    OptWorker?.CheckOptConfig(fromOpt);
                    OptWorker?.CheckOptConfig(toOpt);
                }

                _hydraDb.MapOpt(pump, opt);
                if (fromOpt != null)
                {
                    OptWorker?.SetPaymentTimeout(fromOpt);
                    PushChange(EventType.OPTChanged, fromOpt.IdString);
                }

                if (toOpt != null)
                {
                    OptWorker?.SetPaymentTimeout(toOpt);
                    PushChange(EventType.OPTChanged, toOpt.IdString);
                }

                PushChange(EventType.PumpChanged);

                return Result.Success();
            }, reference);
        }

        private string UpgradeScript(string serviceName)
        {
            string script = "( echo Start %date% %time% & " + $"net stop \"{serviceName}\" & " +
                            "timeout 2 & " + $"( if exist \"{UpgradeFileDirectory}\" ( " + $"rmdir /s /q \"{RollbackFileDirectory}\" & " +
                            $"xcopy /e /i /y /q \"{_serviceFileDirectory}\" \"{RollbackFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.exe\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.dll\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.html\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.config\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.xml\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.jpg\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.ico\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.bmp\" \"{_serviceFileDirectory}\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.bak\" \"{RollbackFileDirectory}{Path.DirectorySeparatorChar}Database Backups\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.sql\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}SQL Scripts\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.bat\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}Batch Files\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.js\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}Scripts\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.css\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}Styles\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.class\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}Java\" & " +
                            $"xcopy /i /y /q \"{UpgradeFileDirectory}{Path.DirectorySeparatorChar}*.jar\" \"{_serviceFileDirectory}{Path.DirectorySeparatorChar}Java\" & " +
                            $"rmdir /s /q \"{UpgradeFileDirectory}\" " + ") else ( " + "echo Warning: Upgrade does not exist " + ")) & " +
                            $"net start \"{serviceName}\" & " + $"echo Finished & echo ======== ) >>{_upgradeLogFile} 2>&1";
            return script;
        }

        private string RollbackScript(string serviceName)
        {
            string script = "( echo Start %date% %time% & " + $"net stop \"{serviceName}\" & " +
                            "timeout 2 & " + $"( if exist \"{RollbackFileDirectory}{Path.DirectorySeparatorChar}OPTService.exe\" ( " +
                            $"rmdir /s /q \"{_serviceFileDirectory}\" & " +
                            $"xcopy /e /i /y /q \"{RollbackFileDirectory}\" \"{_serviceFileDirectory}\" " + ") else ( " +
                            "echo Warning: Rollback does not exist " + ")) & " +
                            $"net start \"{serviceName}\" & " + $"echo Finished & echo ======== ) >>{_upgradeLogFile} 2>&1";
            return script;
        }

        private string RestartScript(string serviceName)
        {
            string script = "( echo Start %date% %time% & " + $"net stop \"{serviceName}\" & " + $"net start \"{serviceName}\" & " +
                            $"echo Finished & echo ======== ) >>{_upgradeLogFile} 2>&1";
            return script;
        }

        private void BackupDatabase(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            string backupFile = directory + Path.DirectorySeparatorChar + $"Hydra_{DateTime.Now:yyyyMMddHHmmss}_Backup.bak";

            SendInformation($"Backing up database to {backupFile}");
            Thread.Sleep(PauseTime);

            _hydraDb.RunQuery($"backup database [{_databaseFilename}] to disk = '{backupFile}' with copy_only, init");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void RestoreDatabase(string filename)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            _hydraDb.RunQuery($"use master; alter database [{_databaseFilename}] set single_user with rollback immediate;" +
                              $" restore database [{_databaseFilename}] from disk = '{filename}';" +
                              $" alter database [{_databaseFilename}] set multi_user;");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void UpgradeService()
        {
            DoAction(() =>
            {
                OptWorker?.WaitForOpts();
                if (!Directory.Exists(UpgradeFileDirectory))
                {
                    GetLogger().Warn("Upgrade directory does not exist");
                    SendInformation("Upgrade directory does not exist");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return;
                }

                BackupDatabase(UpgradeFileDirectory);

                foreach (string fileName in Directory.GetFiles(UpgradeFileDirectory, "*.sql"))
                {
                    SendInformation($"Running SQL script {fileName}");
                    _hydraDb.RunScript(fileName);
                }

                string upgradeService = UpgradeScript(ServiceName);
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe", $"/c {upgradeService}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = true,
                    Verb = "runas"
                };
                Process process = Process.Start(processInfo);
                if (process != null)
                {
                    GetLogger().Debug("Process Started");
                }
                else
                {
                    GetLogger().Debug("Process is null");
                }
            }, LoggingReference);
        }

        private string GetLatestDatabaseBackup()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            string latestFile = null;
            DateTime latestTime = DateTime.MinValue;
            string backupDirectory = RollbackFileDirectory + Path.DirectorySeparatorChar + "Database Backups";
            if (Directory.Exists(backupDirectory))
            {
                string[] files = Directory.GetFiles(backupDirectory);
                foreach (string fileName in files)
                {
                    try
                    {
                        DateTime fileTime = new FileInfo(fileName).LastWriteTime;
                        if (fileTime > latestTime)
                        {
                            latestTime = fileTime;
                            latestFile = fileName;
                        }
                    }
                    catch (Exception e)
                    {
                        GetLogger().Error($"Error getting file time for file {fileName}", e);
                    }
                }
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return latestFile;
        }

        public void RollbackService()
        {
            DoAction(() =>
            {
                OptWorker?.WaitForOpts();

                string latestBackup = GetLatestDatabaseBackup();
                if (latestBackup != null)
                {
                    SendInformation($"Should be restoring database from {latestBackup}");
                    RestoreDatabase(latestBackup);
                    _hydraDb.RunQuery($"use master; alter database [{_databaseFilename}] set single_user with rollback immediate;" +
                                      $" restore database [{_databaseFilename}] from disk = '{latestBackup}';" +
                                      $" alter database [{_databaseFilename}] set multi_user;");
                }

                string rollbackService = RollbackScript(ServiceName);
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe", $"/c {rollbackService}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = true,
                    Verb = "runas"
                };
                Process process = Process.Start(processInfo);
                if (process != null)
                {
                    GetLogger().Debug("Process Started");
                }
                else
                {
                    GetLogger().Debug("Process is null");
                }
            }, LoggingReference);
        }

        public void RestartService()
        {
            DoAction(() =>
            {
                OptWorker?.WaitForOpts();
                string restartService = RestartScript(ServiceName);
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe", $"/c {restartService}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = true,
                    Verb = "runas"
                };
                Process process = Process.Start(processInfo);
                if (process != null)
                {
                    GetLogger().Debug("Process Started");
                }
                else
                {
                    GetLogger().Debug("Process is null");
                }
            }, LoggingReference);
        }

        public void RestartEsocketService()
        {
            DoAction(() =>
            {
                GetLogger().Info("Restarting eSocket.POS service");
                string restartEsocketService = RestartScript("EspRun");
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe", $"/c {restartEsocketService}")
                {
                    CreateNoWindow = true,
                    UseShellExecute = true,
                    Verb = "runas"
                };
                Process process = Process.Start(processInfo);
                if (process != null)
                {
                    GetLogger().Debug("Process Started");
                }
                else
                {
                    GetLogger().Debug("Process is null");
                }
            }, LoggingReference);
        }

        private void CheckDirectory(string directory)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            try
            {
                if (!string.IsNullOrWhiteSpace(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
            catch (Exception e) when (e is ArgumentNullException || e is ArgumentException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error($"Error creating directory {directory}", e);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public string SaveFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving file {fileName}");
            CheckDirectory(UpgradeFileDirectory);
            try
            {
                File.WriteAllBytes(PathString(UpgradeFileDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving file", e);
                SendInformation($"Save file {fileName} failed");
                return $"Error saving file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SaveSoftwareFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving software file {fileName}");
            CheckDirectory(OptWorker?.SoftwareDirectory);
            try
            {
                File.WriteAllBytes(PathString(OptWorker?.SoftwareDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving software file", e);
                SendInformation($"Save software file {fileName} failed");
                return $"Error saving software file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved software file {fileName}");
            OptWorker?.FetchAvailableSoftware();
            PushChange(EventType.AboutChanged);
            PushChange(EventType.OPTChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SaveWhitelistFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving whitelist file {fileName}");
            CheckDirectory(OptWorker?.WhitelistDirectory);
            try
            {
                File.WriteAllBytes(PathString(OptWorker?.WhitelistDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving whitelist file", e);
                SendInformation($"Save whitelist file {fileName} failed");
                return $"Error saving whitelist file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved whitelist file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SaveLayoutFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving layout file {fileName}");
            CheckDirectory(OptWorker?.LayoutDirectory);
            try
            {
                File.WriteAllBytes(PathString(OptWorker?.LayoutDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving layout file", e);
                SendInformation($"Save layout file {fileName} failed");
                return $"Error saving layout file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved layout file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SaveMediaFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving media file {fileName}");
            CheckDirectory(OptWorker?.MediaDirectory);
            try
            {
                File.WriteAllBytes(PathString(OptWorker?.MediaDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving media file", e);
                SendInformation($"Save media file {fileName} failed");
                return $"Error saving media file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved media file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SavePlaylistFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving playlist file {fileName}");
            CheckDirectory(OptWorker?.PlaylistDirectory);
            try
            {
                File.WriteAllBytes(PathString(OptWorker?.PlaylistDirectory, fileName), _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving playlist file", e);
                SendInformation($"Save playlist file {fileName} failed");
                return $"Error saving playlist file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved playlist file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SaveContactlessPropertiesFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Saving contactless properties file {fileName}");
            if (string.IsNullOrWhiteSpace(_paymentConfig.CurrentContactlessFile))
            {
                GetLogger().Warn("Unable to save contactless properties file, file not used");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return "Contactless Properties File Not Used";
            }

            try
            {
                File.WriteAllBytes(_paymentConfig.CurrentContactlessFile, _uploadedFiles[fileName]);
            }
            catch (Exception e) when (e is KeyNotFoundException || e is ArgumentNullException || e is PathTooLongException ||
                                      e is DirectoryNotFoundException || e is IOException || e is UnauthorizedAccessException ||
                                      e is NotSupportedException || e is SecurityException)
            {
                GetLogger().Error("Error saving file", e);
                SendInformation($"Save file {fileName} failed");
                return $"Error saving file {fileName}";
            }

            _uploadedFiles.Remove(fileName);
            SendInformation($"Saved contactless properties file {fileName}");
            FetchGenericOptConfig();
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing file {fileName}");
            _uploadedFiles.Remove(fileName);
            SendInformation($"Removed file {fileName}");
            PushChange(EventType.AboutChanged);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        private string RemoveFileFromDirectory(string directory, string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            string path = PathString(directory, fileName);
            try
            {
                if (File.Exists(path))
                {
                    File.Delete(path);
                }
                else
                {
                    GetLogger().Warn($"File {fileName} not found in directory {directory}");
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return $"File {fileName} not found in directory {directory}";
                }
            }
            catch (Exception e)
            {
                GetLogger().Error($"Error deleting file {fileName} from directory {directory}", e);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return $"Unable to delete file {fileName} from directory {directory}";
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string RemoveWhitelistFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing whitelist file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.WhitelistDirectory, fileName);
            SendInformation(result ?? $"Removed whitelist file {fileName}");

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveLayoutFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing layout file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.LayoutDirectory, fileName);
            SendInformation(result ?? $"Removed layout file {fileName}");

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveUpgradeFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing upgrade file {fileName}");
            string result = RemoveFileFromDirectory(UpgradeFileDirectory, fileName);
            SendInformation(result ?? $"Removed upgrade file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveSoftwareFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing software file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.SoftwareDirectory, fileName);
            SendInformation(result ?? $"Removed software file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveMediaFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing media file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.MediaDirectory, fileName);
            SendInformation(result ?? $"Removed media file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemovePlaylistFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing playlist file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.PlaylistDirectory, fileName);
            SendInformation(result ?? $"Removed playlist file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveDatabaseBackupFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing database backup file {fileName}");
            string result = RemoveFileFromDirectory(DatabaseBackupDirectory, fileName);
            SendInformation(result ?? $"Removed database backup file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string RemoveOptLogFile(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Removing OPT log file {fileName}");
            string result = RemoveFileFromDirectory(OptWorker?.OptLogFileDirectory, fileName);
            SendInformation(result ?? $"Removed OPT log file {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return result;
        }

        public string DatabaseBackup()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Backing up Hydra database");
            BackupDatabase(DatabaseBackupDirectory);
            SendInformation("Hydra database backed up");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string DatabaseRestore(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Restoring Hydra database from file {fileName}");
            RestoreDatabase(DatabaseBackupDirectory + fileName);
            SendInformation($"Hydra database restored from {fileName}");
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public Result SetPumpControllerLogonInfo(string loginString)
        {
            return DoAction(() => { return GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetLogonInfo(loginString, LoggingReference); }, null);
        }

        public string CheckDomsState()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Checking DOMS State");
            GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.RequestState();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ResetDoms()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Reset DOMS");
            GetWorker<IPumpIntegratorInSetup>()?.ResetController();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string MasterResetDoms()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Master Reset DOMS");
            GetWorker<IPumpIntegratorInSetup>()?.MasterResetController();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ReconnectDoms()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Checking DOMS State");
            GetWorker<IPumpIntegratorInSetup>()?.ReconnectController();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string ClearDomsTransaction(byte fpId, int seqNo)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Clearing DOMS Transaction, FP {fpId}, Seq {seqNo}");
            GetWorker<IDomsWorker>()?.ClearTransaction(fpId, seqNo);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string AuthoriseDoms(byte fpId, uint limit)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Authorising DOMS, FP {fpId}, limit {limit}");
            GetWorker<IDomsWorker>()?.Authorise(fpId, limit);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string DomsEmergencyStop(byte fpId)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Emergency Stopping DOMS, FP {fpId}");
            GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.EmergencyStop(fpId, string.Empty);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string DomsCancelEmergencyStop(byte fpId)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Cancelling Emergency Stop DOMS, FP {fpId}");
            GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.EmergencyStopCancel(fpId);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingIndefiniteWait(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Indefinte Wait {flag}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetIndefiniteWait(flag);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingWaitMinutes(int minutes)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Wait Minutes {minutes}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetWaitMinutes(minutes);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingBackoffAuth(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Backoff Auth {backoff}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetBackoffValue(BackoffType.Auth, backoff);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingBackoffPreAuth(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Backoff Pre Auth {backoff}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetBackoffValue(BackoffType.PreAuth, backoff);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingBackoffStopStart(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Backoff Stop Start {backoff}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetBackoffValue(BackoffType.StopStart, backoff);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetFuellingBackoffStopOnly(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting Fuelling Backoff Stop Only {backoff}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetBackoffValue(BackoffType.StopOnly, backoff);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string SetPosClaimNumber(byte number)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Setting POS Claim Number {number}");
            GetWorker<IPumpIntegratorIn<IMessageTracking>>().SetPosClaimNumber(number);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public string CheckPassword(string username, string password)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return string.IsNullOrEmpty(password) || password != ValidPassword ? "Invalid Password" : null;
        }

        /// <inheritdoc />
        public Result SetSiteType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.Site, type, $"{_hydraDb.AdvancedConfig.SiteType}", reference, () =>
            {
                if (!Enum.TryParse(type, true, out SiteType value) || !Enum.IsDefined(typeof(SiteType), value))
                {
                    return Result.Failure($"Invalid site type {type}. Accepted types are {string.Join(",", Enum.GetNames(typeof(SiteType)))}");
                }

                return Result.Success();
            });
        }

        private Result SetIntegrationType(IntegrationType key, string newValue, string currentValue, string reference, Func<Result> validate = null, Action<string, bool> handleConnection = null)
        {
            return DoAction(() => {

                var result = validate?.Invoke() ?? Result.Success();
                if (!result.IsSuccess)
                {
                    return result;
                }
               
                handleConnection?.Invoke(currentValue, false);

                result = _hydraDb.SetIntegrationType(key, newValue, LoggingReference);
                if (!result.IsSuccess)
                {
                    return result;
                }

                handleConnection?.Invoke(newValue, true);

                FetchGenericOptConfig(forceRefresh: true);
                PushChange(EventType.AdvancedConfigChanged);

                return Result.Success();
            }, reference);
        }

        /// <inheritdoc />
        public Result SetPosType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.Pos, type, $"{_hydraDb.AdvancedConfig.PosType}", reference, () =>
            {
                if (!Enum.TryParse(type, true, out PosType value) || !Enum.IsDefined(typeof(PosType), value))
                {
                    return Result.Failure($"Invalid POS type {type}. Accepted types are {string.Join(",", Enum.GetNames(typeof(PosType)))}");
                }

                return Result.Success();
            }, (value, open) =>
            {
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.Pos, value, open, LoggingReference);
            });
        }

        /// <inheritdoc />
        public Result SetPumpType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.Pump, type, _hydraDb.AdvancedConfig.PumpType, reference, null, (value, open) => 
            { 
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.Pump, value, open, LoggingReference); 
            });
        }

        /// <inheritdoc />
        public Result SetMobilePaymentType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.MobilePayment, type, _hydraDb.AdvancedConfig.MobilePaymentType, reference, () =>
            {
                // TODO: Validate against IMobilePaymentIntegratorFactory.GetDiscrininators() - ADO #464273
                return Result.Success();
            }, (value, open) =>
            {
                // TODO: Close current integrator - ADO #464273

                // TODO: Open new integrator - ADO #464273
            });
        }

        /// <inheritdoc />
        public Result SetMobilePosType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.MobilePos, type, _hydraDb.AdvancedConfig.MobilePosType, reference, () =>
            {
                var values = _integratorFactories.MobilePosIntegratorInFactory.GetDiscriminatorValues(reference);
                return Result.SuccessIf(values.ContainsKey(type), 
                    $"Invalid (Mobile) POS type: {type}. Accepted types are {string.Join(",", values.Keys)}");
            }, (value, open) =>
            {
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.MobilePos, value, open, LoggingReference);
            });
        }

        /// <inheritdoc />
        public Result SetBosType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.BackOffice, type, _hydraDb.AdvancedConfig.BosType, reference, () =>
            {
                var values = _integratorFactories.BosIntegratorOutFactory.GetDiscriminatorValues(reference);
                return Result.SuccessIf(values.ContainsKey(type),
                    $"Invalid BOS type: {type}. Accepted types are {string.Join(",", values.Keys)}");
            }, (value, open) =>
            {
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.BackOffice, value, open, LoggingReference);
            });
        }

        /// <inheritdoc />
        public Result SetSecAuthType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.SecAuth, type, _hydraDb.AdvancedConfig.SecAuthType, reference, () =>
            {
                var values = _integratorFactories.SecAuthIntegratorOutFactory.GetDiscriminatorValues(reference);
                return Result.SuccessIf(values.ContainsKey(type),
                    $"Invalid SecAuth type: {type}. Accepted types are {string.Join(",", values.Keys)}");
            }, (value, open) =>
            {
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.SecAuth, value, open, LoggingReference);
            });
        }

        /// <inheritdoc />
        public Result SetPaymentConfigType(string type, string reference = null)
        {
            return SetIntegrationType(IntegrationType.PaymentConfig, type, _hydraDb.AdvancedConfig.PaymentConfigType, reference, () =>
            {
                var values = _integratorFactories.PaymentConfigIntegratorOutFactory.GetDiscriminatorValues(reference);
                return Result.SuccessIf(values.ContainsKey(type),
                    $"Invalid PaymentConfig type: {type}. Accepted types are {string.Join(",", values.Keys)}");
            }, (value, open) =>
            {
                GetWorker<ICore>()?.HandleIntegrationConnection(IntegrationType.PaymentConfig, value, open, LoggingReference);
            });
        }

        /// <inheritdoc />
        public string SetLocalAccountsEnabled(bool enabled)
        {
            DoAction(() =>
            {
                _hydraDb.SetLocalAccountsEnabled(enabled);
                FetchGenericOptConfig(forceRefresh: true);
                PushChange(EventType.LocalAccountsChanged);
            }, string.Empty);

            return null;
        }

        /// <inheritdoc />
        public string NotifyOptConfigChange()
        {
            DoAction(() =>
            {
                FetchGenericOptConfig();
            }, string.Empty);

            return null;
        }

        #endregion

    }
}
