using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Custom PosInModeWorker, specfic to HydraPos
    /// </summary>
    public class HydraPosInModeWorker : PosInModeWorker, IHydraPosInModeWorker
    {
        /// <inheritdoc/>
        public HydraPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutWorker, IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutWorker, configurationManager)
        {
        }

        /// <inheritdoc/>
        protected override void DoRequestDefaultMode(IPump pump, string loggingReference)
        {
            pump.SetModeFromDefault(loggingReference, false);

            base.DoRequestDefaultMode(pump, loggingReference);
        }

        /// <inheritdoc/>
        protected override bool DoRequestModeChange(IPump pump, ModeChangeType mode, string reference, bool overridePumpIsClosed)
        {
            pump.OptIsClosed = mode == ModeChangeType.Close;

            var result = base.DoRequestModeChange(pump, mode, reference, true);

            if (result && !pump.OptIsClosed && (pump.PumpIsClosed || pump.ClosePending))
            {
                pump.OpenPump(reference: reference);
            }

            return result;
        }

        protected override bool DoRequestTimeModeChange(IPump pump, TimeModeChangeType mode, IMessageTracking message)
        {
            pump.OptIsClosed = false;

            return base.DoRequestTimeModeChange(pump, mode, message);
        }
    }
}
