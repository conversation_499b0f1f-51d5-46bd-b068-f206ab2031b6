USE [Hydra]

PRINT N'002_Upgrade_transaction-cycling_install...Begin';

PRINT N'';
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransactionRecord') AND TYPE IN ('P', 'PC'))
   BEGIN
    DROP PROCEDURE AddTransactionRecord
   END
GO

CREATE PROCEDURE [dbo].[AddTransactionRecord]
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20),
 @discountName varchar(20),
 @discountCode varchar(20),
 @discountValue bigint,
 @discountCardNumber varchar(20),
 @localAccountMileage bigint,
 @localAccountRegistration varchar(20),
 @txnNumber varchar(20),
 @maxTransactionNumber bigint,
 @hasValue bit,
 @transactionNumber int out as
 begin
  begin try
   begin transaction
   declare @temp table (id bigint)
   if(@hasValue = 0)
   begin
	   insert into Transactions (TransactionTime)
	   output INSERTED.rowid into @temp values (@transactionTime)
	   set @transactionNumber = (select top 1 id from @temp)
   end
   else
	begin   
	   if(nullif(@pumpDetails,'') is not null)
		begin
		 insert into Transactions
		  (GradeName, FuelCode, FuelQuantity, Amount, PumpDetails, CardNumber,
		   TransactionTime, FuelCategory, FuelSubcategory, DiscountName, DiscountCode,
		   DiscountValue, DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration,
		   TxnNumber)
		  output INSERTED.rowid into @temp values
		   (@gradeName, @code, @quantity, @amount, @pumpDetails, @cardNumber,
			@transactionTime, @category, @subcategory, @discountName, @discountCode,
			@discountValue, @discountCardNumber, @localAccountMileage, @localAccountRegistration,
			@txnNumber)
		end
	   else
		begin
		 insert into Transactions
		  (WashName, WashCode, WashQuantity, Amount, CardNumber, TransactionTime,
		   WashCategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
		   DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration,
		   TxnNumber)
		  output INSERTED.rowid into @temp values
		   (@gradeName, @code, @quantity, @amount, @cardNumber, @transactionTime,
			@category, @subcategory, @discountName, @discountCode, @discountValue,
			@discountCardNumber, @localAccountMileage, @localAccountRegistration,
			@txnNumber)
		end
	end
   set @transactionNumber = (select top 1 id from @temp)
   commit transaction
  end try
  begin catch
   rollback transaction;
   throw
  end catch
 end
 
 GO
 
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransaction') AND TYPE IN ('P', 'PC'))
   BEGIN
    DROP PROCEDURE AddTransaction
   END
GO
PRINT N'Creating Procedure [dbo].[AddTransaction]...';
GO

CREATE PROCEDURE [dbo].[AddTransaction]
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20),
 @discountName varchar(20),
 @discountCode varchar(20),
 @discountValue bigint,
 @discountCardNumber varchar(20),
 @localAccountMileage bigint,
 @localAccountRegistration varchar(20),
 @txnNumber varchar(20),
 @maxTransactionNumber bigint,
 @hasValue bit,
 @transactionNumber int out as
 begin
	DECLARE @rawTransactionNumber BIGINT

	EXEC AddTransactionRecord 
	@gradeName, 
	@code, 
	@quantity, 
	@amount, 
	@pumpDetails, 
	@cardNumber, 
	@transactionTime, 
	@category, 
	@subcategory, 
	@discountName ,
	@discountCode,
	@discountValue,
	@discountCardNumber,
	@localAccountMileage,
	@localAccountRegistration,
	@txnNumber,
	@maxTransactionNumber,
	@hasValue,
	@rawTransactionNumber out
	
	 
	-- Check for transaction number recycling
	SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	IF(@transactionNumber = 0)
	BEGIN
		-- Remove 0 txn number row
		DELETE FROM [Transactions]
		WHERE rowid = @rawTransactionNumber
		
		-- Re-add so txn number != 0
		EXEC AddTransactionRecord 
			@gradeName, 
			@code, 
			@quantity, 
			@amount, 
			@pumpDetails, 
			@cardNumber, 
			@transactionTime, 
			@category, 
			@subcategory, 
			@discountName ,
			@discountCode,
			@discountValue,
			@discountCardNumber,
			@localAccountMileage,
			@localAccountRegistration,
			@txnNumber,
			@maxTransactionNumber,
			@hasValue,
			@rawTransactionNumber out

			SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	END
 END
 GO

PRINT N'Creating AddTransaction Permission...';
GO
IF NOT EXISTS (SELECT * FROM sys.database_permissions p
			INNER   JOIN sys.database_principals dp
			   ON     p.grantee_principal_id = dp.principal_id
			   WHERE major_id=object_id('dbo.AddTransaction'))
BEGIN
GRANT EXECUTE
    ON OBJECT::[dbo].[AddTransaction] TO [UserRole]
    AS [dbo];
END
GO

PRINT N'002_Upgrade_transaction-cycling_install...End';