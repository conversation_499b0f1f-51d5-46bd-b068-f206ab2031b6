using FluentMigrator;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed for DOMS/Pump workers
    /// </summary>
    [Migration(26200002, "ADO#686595 - Set further configuration needed for revised DOMS/Pump workers"), Tags(Constants.MigrationTypeData)]
    public class ADO686595_DOMSPumpConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;
        private const string baseUrlBos = "http://evobook.evolutiontlm.co.uk:38410";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);

            if (client.ToLower() == ClientSettings.ClientNameMADIC)
            {
                // Endpoints Config - API, correction to ADO #666492
                Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BOS', @value = '{baseUrlBos}/api', @category = '{cfgConstants.CategoryNameEndPointsApi}'");

                // SignalR HubClient, correction to ADO #686376
                Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'AcknowledgeServerMessage:Enabled:', @value = 'False', @category = '{cfgConstants.CategoryNameSignalRHubClient}'");
            }

            // Timers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:DomsSetupTcp+EmbeddedTimerable', @value = '00:00:02', @category = '{cfgConstants.CategoryNameTimers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:PumpWorker+EmbeddedTimerable', @value = '00:00:02', @category = '{cfgConstants.CategoryNameTimers}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
