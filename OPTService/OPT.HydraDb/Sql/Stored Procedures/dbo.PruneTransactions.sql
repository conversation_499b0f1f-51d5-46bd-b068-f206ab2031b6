USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('PruneTransactions') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE PruneTransactions
END
GO

CREATE PROCEDURE [dbo].[PruneTransactions]
 @checkDate DateTime2 AS
BEGIN
  DELETE FROM Transactions WHERE TransactionTime < @checkDate;

  DELETE FROM TransactionBookingState WHERE TransactionDate < @checkDate;
END
GO

GRANT EXECUTE ON PruneTransactions TO UserRole
GO
