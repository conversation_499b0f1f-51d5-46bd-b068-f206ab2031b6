using Forecourt.Core.Configuration;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Interfaces;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;
using System;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;

namespace Forecourt.Core.HydraDb.Models
{
    /// <summary>
    /// Holds all Advanced Configuration options
    /// </summary>
    [HasConfiguration()]
    public class AdvancedConfig : Loggable
    {
        private readonly SiteInfo _siteInfo;

        public int ReceiptLayoutMode => _siteInfo.ReceiptLayoutMode;
        public string SiteName => _siteInfo.SiteName;
        public string VatNumber => _siteInfo.VatNumber;
        public bool NozzleUpForKioskUse => _siteInfo.NozzleUpForKioskUse;
        public bool UseReplaceNozzleScreen => _siteInfo.UseReplaceNozzleScreen;
        public int CurrencyCode => _siteInfo.CurrencyCode;
        public uint MaxFillOverride => _siteInfo.MaxFillOverride;

        #region Advanced Config Configurables

        private const string ConfigCategorySiteInfo = Constants.CategoryNameSiteInfo + Constants.CategorySeparator;

        /// <summary>
        /// Config key for, SiteType
        /// </summary>
        public const string ConfigKeySiteType = ConfigCategorySiteInfo + "SITETYPE";

        /// <summary>
        /// Default vaue for, SiteType
        /// </summary>
        public const string DefaultValueSiteType = "Retail";

        /// <summary>
        /// Configurable value for, SiteType
        /// </summary>
        protected ConfigurableString ConfigValueSiteType { get; set; }

        /// <summary>
        /// SiteType
        /// </summary>
        public SiteType SiteType => Enum.TryParse<SiteType>(ConfigValueSiteType.GetValue(), true, out var result) ? result : SiteType.Retail;

        /// <summary>
        /// Config key for, PosType
        /// </summary>
        public const string ConfigKeyPosType = ConfigCategorySiteInfo + "POSTYPE";

        /// <summary>
        /// Default vaue for, PosType
        /// </summary>
        public const string DefaultValuePosType = "Retalix";

        /// <summary>
        /// Configurable value for, PosType
        /// </summary>
        protected ConfigurableString ConfigValuePosType { get; set; }

        /// <summary>
        /// PosType
        /// </summary>
        public PosType PosType => Enum.TryParse<PosType>(ConfigValuePosType.GetValue(), true, out var result) ? result : PosType.None;

        /// <summary>
        /// Config key for, if Local Accounts are enabled
        /// </summary>
        public const string ConfigKeyLocalAccountsEnabled = ConfigCategorySiteInfo + "LOCALACCOUNTSENABLED";

        /// <summary>
        /// Default vaue for, if Local Accounts are enabled
        /// </summary>
        public const bool DefaultValueLocalAccountsEnabled = false;

        /// <summary>
        /// Configurable value for, if Local Accounts are enabled
        /// </summary>
        protected ConfigurableBool ConfigValueLocalAccountsEnabled { get; set; }

        /// <summary>
        ///  Are Local account enabled
        /// </summary>
        public bool LocalAccountsEnabled => ConfigValueLocalAccountsEnabled.GetValue();

        /// <summary>
        /// Config key for, PumpType
        /// </summary>
        public const string ConfigKeyPumpType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorPump;

        /// <summary>
        /// Default vaue for, PumpType
        /// </summary>
        public const string DefaultValuePumpType = Constants.Integrator.PumpTypeHsc;

        /// <summary>
        /// Configurable value for, PumpType
        /// </summary>
        protected ConfigurableString ConfigValuePumpType { get; set; }

        /// <summary>
        /// PumpType
        /// </summary>
        public string PumpType => ConfigValuePumpType.GetValue();

        /// <summary>
        /// Config key for, Mobile (Payment) Type
        /// </summary>
        public const string ConfigKeyMobilePaymentType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorPaymentMobile;

        /// <summary>
        /// Default vaue for, Mobile (Payment) Type
        /// </summary>
        public const string DefaultValueMobilePaymentType = ConfigConstants.NoneUpper;

        /// <summary>
        /// Configurable value for, Mobile (Payment) Type
        /// </summary>
        protected ConfigurableString ConfigValueMobilePaymentType { get; set; }

        /// <summary>
        /// Mobile (Payment) Type 
        /// </summary>
        [Obsolete("Not implemented yet!")]
        public string MobilePaymentType => ConfigValueMobilePaymentType.GetValue();

        /// <summary>
        /// Config key for, Mobile (Pos) Type
        /// </summary>
        public const string ConfigKeyMobilePosType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorPosMobile;

        /// <summary>
        /// Default vaue for, Mobile (Pos) Type
        /// </summary>
        public const string DefaultValueMobilePosType = ConfigConstants.NoneUpper;

        /// <summary>
        /// Configurable value for, Mobile (Pos) Type
        /// </summary>
        protected ConfigurableString ConfigValueMobilePosType { get; set; }

        /// <summary>
        /// Mobile (Pos) Type 
        /// </summary>
        public string MobilePosType => ConfigValueMobilePosType.GetValue();

        /// <summary>
        /// Config key for, BosType
        /// </summary>
        public const string ConfigKeyBosType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorBos;

        /// <summary>
        /// Default vaue for, BosType
        /// </summary>
        public const string DefaultValueBosType = ConfigConstants.NoneUpper;

        /// <summary>
        /// Configurable value for, BosType
        /// </summary>
        protected ConfigurableString ConfigValueBosType { get; set; }

        /// <summary>
        /// BosType Type
        /// </summary>
        public string BosType => ConfigValueBosType.GetValue();

        /// <summary>
        /// Config key for, SecAuth Type
        /// </summary>
        public const string ConfigKeySecAuthType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorSecAuth;

        /// <summary>
        /// Default vaue for, SecAuth Type
        /// </summary>
        public const string DefaultValueSecAuthType = ConfigConstants.NoneUpper;

        /// <summary>
        /// Configurable value for, SecAuthType
        /// </summary>
        protected ConfigurableString ConfigValueSecAuthType { get; set; }

        /// <summary>
        /// SecAuth Type
        /// </summary>
        public string SecAuthType => ConfigValueSecAuthType.GetValue();

        /// <summary>
        /// Config key for, PaymentConfgurationType
        /// </summary>
        public const string ConfigKeyPaymentConfigType = ConfigCategorySiteInfo + Constants.Integrator.ConfigKeyIntegratorPaymentConfig;

        /// <summary>
        /// Default vaue for, PaymentConfguration Type
        /// </summary>
        public const string DefaultValuePaymentConfigType = ConfigConstants.NoneUpper;

        /// <summary>
        /// Configurable value for, PaymentConfguration Type
        /// </summary>
        protected ConfigurableString ConfigValuePaymentConfigType { get; set; }

        /// <summary>
        /// PaymentConfguration Type
        /// </summary>
        public string PaymentConfigType => ConfigValuePaymentConfigType.GetValue();

        /// <summary>
        /// Config Key for, pump transaction reserve limit
        /// </summary>
        public const string ConfigKeyPumpReserveLimit = ConfigurationConstants.CategoryNamePump + ConfigurationConstants.CategorySeparator + "Reserve:Limit";

        /// <summary>
        /// Default value for, pump transaction reserve limit
        /// </summary>
        public const uint DefaultValuePumpReserveLimit = 12000;

        /// <summary>
        /// Configurable value for, pump transaction reserve limit
        /// </summary>
        protected ConfigurableUInt ConfigValuePumpReserveLimit { get; set; }

        /// <summary>
        /// Pump reserve Timeout
        /// </summary>
        public uint PumpReserveLimit => ConfigValuePumpReserveLimit.GetValue();

        /// <summary>
        /// Config Key for, pump transaction reserve Timeout
        /// </summary>
        public const string ConfigKeyPumpReserveTimeout = ConfigurationConstants.CategoryNamePump + ConfigurationConstants.CategorySeparator + "Reserve:Timeout:Interval";

        /// <summary>
        /// Default value for, pump transaction reserve Timeout, matches Max OPT Contactless Present Card and ICC Pin Entry timeout times Number Pin Attempts
        /// </summary>
        public const string DefaultValuePumpReserveTimeout = "00:02:00";

        /// <summary>
        /// Configurable value for, pump transaction reserve Timeout
        /// </summary>
        protected ConfigurableTimeSpan ConfigValuePumpReserveTimeout { get; set; }

        /// <summary>
        /// Pump reserve Timeout
        /// </summary>
        public TimeSpan PumpReserveTimeout => ConfigValuePumpReserveTimeout.GetValue();

        #endregion

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="logger"><see cref="IHtecLogger"/> instance</param>
        /// <param name="configurationManager"><see cref="IConfigurationManager"/> instance</param>
        public AdvancedConfig(IHydraDb hydraDb, IHtecLogger logger, IConfigurationManager configurationManager): base(logger, configurationManager)
        {
            _siteInfo = hydraDb.GetSiteInfo();

            ConfigValueSiteType = new ConfigurableString(this, ConfigKeySiteType, DefaultValueSiteType);
            ConfigValuePosType = new ConfigurableString(this, ConfigKeyPosType, DefaultValuePosType);
            ConfigValueLocalAccountsEnabled = new ConfigurableBool(this, ConfigKeyLocalAccountsEnabled, DefaultValueLocalAccountsEnabled);
            ConfigValuePumpType = new ConfigurableString(this, ConfigKeyPumpType, DefaultValuePumpType);
            ConfigValueMobilePaymentType = new ConfigurableString(this, ConfigKeyMobilePaymentType, DefaultValueMobilePaymentType);
            ConfigValueBosType = new ConfigurableString(this, ConfigKeyBosType, DefaultValueBosType);
            ConfigValueMobilePosType = new ConfigurableString(this, ConfigKeyMobilePosType, DefaultValueMobilePosType);
            ConfigValueSecAuthType = new ConfigurableString(this, ConfigKeySecAuthType, DefaultValueSecAuthType);
            ConfigValuePaymentConfigType = new ConfigurableString(this, ConfigKeyPaymentConfigType, DefaultValuePaymentConfigType);
            ConfigValuePumpReserveLimit = new ConfigurableUInt(this, ConfigKeyPumpReserveLimit, DefaultValuePumpReserveLimit);
            ConfigValuePumpReserveTimeout = new ConfigurableTimeSpan(this, ConfigKeyPumpReserveTimeout, DefaultValuePumpReserveTimeout);
        }
    }
}
