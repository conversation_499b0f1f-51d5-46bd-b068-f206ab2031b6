using Htec.Foundation.Models;
using System.Collections.Generic;
namespace OPTService.OPTServiceClasses
{
    public class VersionInfo
    {
        public IEnumerable<FileVersionInfo> Current { get; set; }
        public IEnumerable<FileVersionInfo> Rollback { get; set; }
        public IEnumerable<FileVersionInfo> Upgrade { get; set; }
        public IEnumerable<FileVersionInfo> PumpIntegrator { get; set; }
        public IEnumerable<FileVersionInfo> OfflineFileService { get; set; }      
        public IEnumerable<string> UploadedFileNames { get; set; }
        public IEnumerable<string> WhitelistFiles { get; set; }
        public IEnumerable<string> LayoutFiles { get; set; }
        public IEnumerable<string> UpgradeFiles { get; set; }
        public IEnumerable<string> SoftwareFiles { get; set; }
        public IEnumerable<string> MediaFiles { get; set; }
        public IEnumerable<string> PlaylistFiles { get; set; }
        public IEnumerable<string> DatabaseBackupFiles { get; set; }
        public IEnumerable<string> OptLogFiles { get; set; }
    }
}
