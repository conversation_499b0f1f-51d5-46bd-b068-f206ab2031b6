import { environment } from "src/environments/environment";

export default class Utils {

    /**
     * Extracts the origin of a document
     * @param document Document object
     * @returns string with URL including protocol but no port
     */
    static getBaseURL(document: Document): string {
        var path: string = ""

        var origin = document.location.origin;

        if (origin) {
            var url = new URL(origin);

            path = url.protocol + '//' + url.hostname;
        }

        return path;
    }

    /**
     * Builds the OPT Service API URL
     * @param document Document object
     * @returns string with URL including protocol and path but no port
     */
    static getBaseURLOptService(document: Document): string {
        return Utils.getBaseURL(document) + environment.optServiceBase;
    }

}