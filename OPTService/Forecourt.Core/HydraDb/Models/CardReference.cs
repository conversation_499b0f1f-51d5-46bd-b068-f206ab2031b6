namespace Forecourt.Core.HydraDb.Models
{
    public class CardReference
    {
        public int CardRef { get; }
        public string CardProductName { get; }
        public bool FuelCard { get; }
        public string AcquirerName { get; }
        public bool InUse { get; }
        public string CardExternalName { get; }

        // ReSharper disable once UnusedMember.Global
        public CardReference(int cardRef, string cardProductName, bool fuelCard, string acquirerName, bool inUse, string cardExternalName)
        {
            CardRef = cardRef;
            CardProductName = cardProductName;
            FuelCard = fuelCard;
            AcquirerName = acquirerName;
            InUse = inUse;
            CardExternalName = cardExternalName;
        }
    }
}
