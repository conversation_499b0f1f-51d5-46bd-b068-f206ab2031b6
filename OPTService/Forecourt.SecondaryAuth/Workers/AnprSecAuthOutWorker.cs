using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.SecondaryAuth.Workers
{

    /// <inheritdoc/>
    public class AnprSecAuthOutWorker : SecAuthOutWorker, IAnprSecAuthOutWorker
    {
        /// <inheritdoc/>
        public AnprSecAuthOutWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IPumpCollection allPumps, 
            ITelemetryWorker telemetryWorker, IAnprWorker anprWorker) : base(logManager, nameof(AnprSecAuthOutWorker), configurationManager, allPumps, telemetryWorker)
        {
            RegisterWorker(anprWorker ?? throw new ArgumentNullException(nameof(anprWorker)));
        }

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            var result = base.DoStart(startParams);
            if (!result.IsSuccess)
            { 
                return result; 
            }

            return GetWorker<IAnprWorker>()?.Start(startParams) ?? Result.Success();
        }

        /// <inheritdoc/>
        protected override Result DoStop()
        {
            GetWorker<IAnprWorker>()?.Stop();

            return base.DoStop();
        }

        /// <inheritdoc/>
        public override Result PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return Result.Failure($"{FailureReasonPrefix}{HeaderPostAuth}");
        }

        /// <inheritdoc/>
        public override Result PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            return DoProcessRequest(request, message, (pump) => GetWorker<IAnprWorker>().SendSecondaryAuthRequest(pump, message));
        }
    }
}
