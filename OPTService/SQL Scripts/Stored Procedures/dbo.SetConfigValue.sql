USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[SetConfigValue]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE [SetConfigValue]
END
GO

CREATE PROCEDURE [dbo].SetConfigValue
  @headerId int,
  @categoryId int,
  @key VARCHAR(255),
  @value VARCHAR(max) AS
	BEGIN TRY
		BEGIN TRANSACTION
			UPDATE dbo.[ConfigurationDetail] WITH (UPDLOCK, SERIALIZABLE) 
			SET [Value] = @value,
				[ModifiedDateTime] = GETDATE()
			WHERE HeaderId = @headerId
				AND CategoryId = @categoryId 
				AND [KEY] = @key
 
			IF @@ROWCOUNT = 0
			BEGIN
				INSERT [ConfigurationDetail] ([HeaderId],[CategoryId],[Key],[Value],[ModifiedDateTime]) 
				VALUES (@headerId, @categoryId, @key, @value,  GETDATE())
			END
		COMMIT TRANSACTION
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		THROW
	END CATCH
GO

GRANT EXECUTE ON SetConfigValue TO UserRole
GO
