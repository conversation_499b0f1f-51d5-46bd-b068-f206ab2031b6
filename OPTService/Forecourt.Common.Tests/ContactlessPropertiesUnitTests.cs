using FluentAssertions;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using Xunit;

namespace OPT.Common.Tests
{
    [SuppressMessage("ReSharper", "PrivateFieldCanBeConvertedToLocalVariable")]
    public class ContactlessPropertiesUnitTests
    {
        private readonly IContactlessProperties _ContactlessProperties;
        private readonly IHtecLogger _logger;

        public ContactlessPropertiesUnitTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _ContactlessProperties = new ContactlessProperties(_logger);
        }

        #region region DRL Tests

        private static string DrlString
        (byte recordFormat, ushort usageControl, byte? programIdLength = null, string programId = null, byte? zeroCheck = null,
            uint? transactionLimit = null, uint? cvmLimit = null, uint? floorLimit = null)
        {
            return
                $"{recordFormat:X2}{usageControl:X4}{programIdLength:X2}{programId}{zeroCheck:X2}{transactionLimit:X8}{cvmLimit:X8}{floorLimit:X8}";
        }

        [Fact]
        public void test_incorrect_record_format()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 2;
            const ushort usageControl = 0;
            string drlString = DrlString(recordFormat, usageControl);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeNull();
        }

        [Fact]
        public void test_no_usage_drl()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0;
            string drlString = DrlString(recordFormat, usageControl);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, null, null, null, null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_empty_program_id()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0x8000;
            const string programId = "";
            byte programIdLength = (byte) (programId.Length / 2);
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, null, null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_just_program_id()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0x8000;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, null, null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_program_id_with_missing_zero_check()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xA000;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeNull();
        }

        [Fact]
        public void test_program_id_with_zero_check()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xA000;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, null, null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_transaction_limit()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xB000;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint transactionLimit = 1000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, transactionLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, transactionLimit.ToString(), null, null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_cvm_limit()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xA800;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint cvmLimit = 1000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, cvmLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, cvmLimit.ToString(), null);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_floor_limit()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xA400;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint floorLimit = 1000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, null, null, floorLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, null, floorLimit.ToString());

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_cvm_and_floor_limit()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xAC00;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint cvmLimit = 1000;
            const uint floorLimit = 2000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, null, cvmLimit, floorLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, null, cvmLimit.ToString(), floorLimit.ToString());

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_all_limits_without_zero_check()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0x9C00;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const uint transactionLimit = 1000;
            const uint cvmLimit = 2000;
            const uint floorLimit = 3000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, null, transactionLimit, cvmLimit,
                floorLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, transactionLimit.ToString(), cvmLimit.ToString(),
                floorLimit.ToString());

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_all_limits_with_zero_check()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xBC00;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint transactionLimit = 1000;
            const uint cvmLimit = 2000;
            const uint floorLimit = 3000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, transactionLimit, cvmLimit,
                floorLimit);
            CardClessDrl expectedDrl = new CardClessDrl(aidString, programId, transactionLimit.ToString(), cvmLimit.ToString(),
                floorLimit.ToString());

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeEquivalentTo(expectedDrl);
        }

        [Fact]
        public void test_missing_limit()
        {
            // Arrange
            const string aidString = "My AID";
            const byte recordFormat = 1;
            const ushort usageControl = 0xBC00;
            const string programId = "Program Id";
            byte programIdLength = (byte) (programId.Length / 2);
            const byte zeroCheck = 1;
            const uint cvmLimit = 2000;
            const uint floorLimit = 3000;
            string drlString = DrlString(recordFormat, usageControl, programIdLength, programId, zeroCheck, null, cvmLimit, floorLimit);

            // Act
            CardClessDrl drl = ContactlessProperties.GetDrl(aidString, drlString, _logger);

            // Assert
            drl.Should().BeNull();
        }

        #endregion

        #region Contactless Properties tests

        private static string DrlString
            (string programId = null, byte? zeroCheck = null, uint? transactionLimit = null, uint? cvmLimit = null, uint? floorLimit = null)
        {
            const byte recordFormat = 1;
            ushort usageControl = (ushort) ((programId == null ? 0 : 0x8000) | (zeroCheck == null ? 0 : 0x2000) |
                                            (transactionLimit == null ? 0 : 0x1000) | (cvmLimit == null ? 0 : 0x0800) |
                                            (floorLimit == null ? 0 : 0x0400));
            byte? programIdLength = (byte?) (programId?.Length / 2);
            return
                $"{recordFormat:X2}{usageControl:X4}{programIdLength:X2}{programId}{zeroCheck:X2}{transactionLimit:X8}{cvmLimit:X8}{floorLimit:X8}";
        }

        private static string PropsString(IList<CardClessAid> cards, IList<CardClessDrl> drls = null, bool useRid = false)
        {
            StringBuilder props = new StringBuilder();
            props.Append("aid_list=");
            bool first = true;
            foreach (CardClessAid card in cards)
            {
                if (first)
                {
                    first = false;
                }
                else
                {
                    props.Append(",");
                }

                props.Append(card.Aid);
            }

            props.AppendLine();

            if (useRid)
            {
                props.AppendLine($"rid_list={cards[0].Aid.Substring(0, 10)}");
                props.AppendLine($"{cards[0].Aid.Substring(0, 10)}.default_udol={cards[0].Udol}");
                if (drls != null && drls.Count > 0)
                {
                    props.Append($"{drls[0].Aid.Substring(0, 10)}.risk_parameter_record_list=" + DrlString(drls[0].ProgramId, 1,
                                     drls[0].TransLimit == null ? null : (uint?) uint.Parse(drls[0].TransLimit),
                                     drls[0].CvmLimit == null ? null : (uint?) uint.Parse(drls[0].CvmLimit),
                                     drls[0].FloorLimit == null ? null : (uint?) uint.Parse(drls[0].FloorLimit)));

                    props.AppendLine();
                }
            }

            foreach (CardClessAid card in cards)
            {
                props.AppendLine($"{card.Aid}.app_ver={card.AppVerTerm}");
                props.AppendLine($"{card.Aid}.transaction_limit={card.TransLimit}");
                props.AppendLine($"{card.Aid}.contactless_floor_limit={card.FloorLimit}");
                props.AppendLine($"{card.Aid}.cvm_limit={card.CvmLimit}");
                props.AppendLine($"{card.Aid}.dcv_limit={card.OdcvmLimit}");
                props.AppendLine($"{card.Aid}.additional_terminal_capabilities={card.TermAddCapabilities}");
                props.AppendLine($"{card.Aid}.contactless_terminal_capabilities={card.TermCapabilitiesCvm}");
                props.AppendLine($"{card.Aid}.terminal_risk_management_data={card.TermRiskData}");
                if (!useRid)
                {
                    props.AppendLine($"{card.Aid}.default_udol={card.Udol}");
                }

                props.AppendLine($"{card.Aid}.tac_default={card.TacDefault}");
                props.AppendLine($"{card.Aid}.tac_denial={card.TacDenial}");
                props.AppendLine($"{card.Aid}.tac_online={card.TacOnline}");
                if (!useRid && drls != null)
                {
                    bool firstDrl = true;
                    foreach (CardClessDrl drl in drls.Where(x => x.Aid.Equals(card.Aid)))
                    {
                        if (firstDrl)
                        {
                            props.Append($"{card.Aid}.risk_parameter_record_list=");
                            firstDrl = false;
                        }
                        else
                        {
                            props.Append(",");
                        }

                        props.Append(DrlString(drl.ProgramId, 1, drl.TransLimit == null ? null : (uint?) uint.Parse(drl.TransLimit),
                            drl.CvmLimit == null ? null : (uint?) uint.Parse(drl.CvmLimit),
                            drl.FloorLimit == null ? null : (uint?) uint.Parse(drl.FloorLimit)));
                    }

                    if (!firstDrl)
                    {
                        props.AppendLine();
                    }
                }
            }

            return props.ToString();
        }


        [Fact]
        public void test_aid()
        {
            // Arrange
            IList<CardClessAid> cards = new List<CardClessAid>
            {
                new CardClessAid("A0000000031010", "0096", "3001", "0000", "1001", "2000", "6000F0B001", "E0B8C8", "E0B8C8", "AAAA",
                    "9F6A04", "DC4000A800", "0010000000", "DC4004F800", "", "", "", "", "", "", "", "", "", "", "", ""),
                new CardClessAid("A0000000032010", "0098", "3000", "1000", "2000", "4000", "6000F0B001", "E0B8C8", "E0B8C8", "BBBB",
                    "9F6A04", "DC4000A800", "0010000000", "DC4004F800", "", "", "", "", "", "", "", "", "", "", "", "")
            };
            IList<CardClessDrl> drls = new List<CardClessDrl>
            {
                new CardClessDrl("A0000000031010", "ABBAABBA", "3001", "4001", "6001"),
                new CardClessDrl("A0000000032010", "CDDCAA", "2000", "5000", "7000"),
                new CardClessDrl("A0000000032010", "CDDCAB", "2001", "5001", "7001")
            };

            string propsString = PropsString(cards, drls);

            // Act
            _ContactlessProperties.ExtractContactlessProperties(propsString);
            IList<CardClessAid> result = _ContactlessProperties.Cards;
            IList<CardClessDrl> resultDrls = _ContactlessProperties.Drls;

            // Assert
            //propsString.Should().BeEquivalentTo("");
            result.Should().BeEquivalentTo(cards);
            resultDrls.Should().BeEquivalentTo(drls);
        }

        [Fact]
        public void test_aid_with_rid()
        {
            // Arrange
            IList<CardClessAid> cards = new List<CardClessAid>
            {
                new CardClessAid("A0000000031010", "0096", "3001", "0000", "1001", "2000", "6000F0B001", "E0B8C8", "E0B8C8", "AAAA",
                    "9F6A04", "DC4000A800", "0010000000", "DC4004F800", "", "", "", "", "", "", "", "", "", "", "", ""),
                new CardClessAid("A0000000032010", "0098", "3000", "1000", "2000", "4000", "6000F0B001", "E0B8C8", "E0B8C8", "BBBB",
                    "9F6A04", "DC4000A800", "0010000000", "DC4004F800", "", "", "", "", "", "", "", "", "", "", "", "")
            };
            IList<CardClessDrl> drls = new List<CardClessDrl>
            {
                new CardClessDrl("A0000000031010", "ABBAABBA", "3001", "4001", "5001"),
                new CardClessDrl("A0000000032010", "ABBAABBA", "3001", "4001", "5001")
            };

            string propsString = PropsString(cards, drls, true);

            // Act
            _ContactlessProperties.ExtractContactlessProperties(propsString);
            IList<CardClessAid> result = _ContactlessProperties.Cards;
            IList<CardClessDrl> resultDrls = _ContactlessProperties.Drls;

            // Assert
            //propsString.Should().BeEquivalentTo("");
            result.Should().BeEquivalentTo(cards);
            resultDrls.Should().BeEquivalentTo(drls);
        }

        [Fact]
        public void test_single_aid()
        {
            // Arrange
            IList<CardClessAid> cards = new List<CardClessAid>
            {
                new CardClessAid("A0000000031010", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                    "", "", "")
            };
            const string propsString = "aid_list=A0000000031010";

            // Act
            _ContactlessProperties.ExtractContactlessProperties(propsString);
            IList<CardClessAid> result = _ContactlessProperties.Cards;
            IList<CardClessDrl> resultDrls = _ContactlessProperties.Drls;

            // Assert
            //propsString.Should().BeEquivalentTo("");
            result.Should().BeEquivalentTo(cards);
            resultDrls.Should().BeEmpty();
        }

        [Fact]
        public void test_single_aid_with_app_ver()
        {
            // Arrange
            IList<CardClessAid> cards = new List<CardClessAid>
            {
                new CardClessAid("A0000000031010", "1234", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                    "", "", "", "")
            };
            const string propsString = "aid_list=A0000000031010\nA0000000031010.app_ver=1234";

            // Act
            _ContactlessProperties.ExtractContactlessProperties(propsString);
            IList<CardClessAid> result = _ContactlessProperties.Cards;
            IList<CardClessDrl> resultDrls = _ContactlessProperties.Drls;

            // Assert
            //propsString.Should().BeEquivalentTo("");
            result.Should().BeEquivalentTo(cards);
            resultDrls.Should().BeEmpty();
        }

        [Fact]
        public void test_duplicate_props_uses_first_value()
        {
            // Arrange
            IList<CardClessAid> cards = new List<CardClessAid>
            {
                new CardClessAid("A0000000031010", "1234", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
                    "", "", "", "")
            };

            const string propsString = "aid_list=A0000000031010\nA0000000031010.app_ver=1234\nA0000000031010.app_ver=5678";

            // Act
            _ContactlessProperties.ExtractContactlessProperties(propsString);
            IList<CardClessAid> result = _ContactlessProperties.Cards;
            IList<CardClessDrl> resultDrls = _ContactlessProperties.Drls;

            // Assert
            //propsString.Should().BeEquivalentTo("");
            result.Should().BeEquivalentTo(cards);
            resultDrls.Should().BeEmpty();
        }

        #endregion
    }
}
