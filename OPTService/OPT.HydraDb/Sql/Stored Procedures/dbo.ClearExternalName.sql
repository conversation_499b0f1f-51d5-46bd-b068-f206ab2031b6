USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('[ClearExternalName]') AND TYPE IN ('P', 'PC'))
BEGIN
	DROP PROCEDURE ClearExternalName
END
GO

create procedure ClearExternalName
 @cardName VARCHAR(MAX) AS
 BEGIN
  BEGIN TRY
   BEGIN TRANSACTION
   UPDATE CardReference set CardExternalName = null WHERE CardProductName = @cardName
   COMMIT TRANSACTION
  END TRY
  BEGIN CATCH
   ROLLBACK TRANSACTION;
   THROW
  END CATCH
 END

GO

GRANT EXECUTE ON ClearExternalName TO UserRole
GO