export enum AdvancedConfigProperty {
    AutoAuth = "AutoAuth",
    MediaChannel = "MediaChannel",
    UnmannedPseudoPos = "UnmannedPseudoPos",
    AsdaDayEndReport ="AsdaDayEndReport",
    MorrisonLoyaltyAvailable = "MorrisonLoyaltyAvailable",
    PaymentTimeoutOpt = "PaymentTimeoutOpt",
    PaymentTimeoutPod ="PaymentTimeoutPod",
    PaymentTimeoutMixed = "PaymentTimeoutMixed",
    PaymentTimeoutNozzleDown = "PaymentTimeoutNozzleDown",
    PaymentTimeoutKiosk = "TimeoutKiosk",
    PaymentTimeoutSecAuth = "TimeoutSecAuth",
    ReceiptTimeout = "ReceiptTimeout",
    ReceiptMaxCount = "ReceiptMaxCount",
    TillNumber = "TillNumber",
    FuelCategory = "FuelCategory",
    ForwardFuelPriceUpdate = "ForwardFuelPriceUpdate",
    FuellingIndefiniteWait = "FuellingIndefiniteWait",
    FuellingWaitMinutes = "FuellingWaitMinutes",
    FuellingBackoffAuth = "FuellingBackoffAuth",
    FuellingBackoffPreAuth = "FuellingBackoffPreAuth",
    FuellingBackoffStopStart = "FuellingBackoffStopStart",
    FuellingBackoffStopOnly = "FuellingBackoffStopOnly",
    PosClaimNumber = "PosClaimNumber",
    FilePruneDays = "FilePruneDays",
    TransactionPruneDays = "TransactionPruneDays",
    ReceiptPruneDays = "ReceiptPruneDays",
    CardReferences = "CardReferences"
}
