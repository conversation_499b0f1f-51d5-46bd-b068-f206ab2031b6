using Forecourt.Core.Configuration;
using System.ComponentModel;

namespace Forecourt.Core.Enums
{
    /// <summary>
    /// All kinds of Integration
    /// </summary>
    public enum IntegrationType
    {
        /// <summary>
        /// Site
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorSite)]
        Site,

        /// <summary>
        /// Pos
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorPos)]
        Pos,

        /// <summary>
        /// Pump
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorPump)]
        Pump,

        /// <summary>
        /// Mobile (payment)
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorPaymentMobile)]
        MobilePayment,

        /// <summary>
        /// BosType
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorBos)]
        BackOffice,

        /// <summary>
        /// Mobile (payments) host(s)
        /// </summary>
        MobilePaymentHost,

        /// <summary>
        /// Mobile Pos
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorPosMobile)]
        MobilePos,

        /// <summary>
        /// Payments host
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorPaymentConfig)]
        PaymentConfig,

        /// <summary>
        /// Carwash
        /// </summary>
        CarWash,

        /// <summary>
        /// Secondary Authorisation
        /// </summary>
        [Description(Constants.Integrator.ConfigKeyIntegratorSecAuth)]
        SecAuth
    }
}
