using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using OPT.TransactionValidator.Common;

namespace OPT.TransactionValidator.Models
{
    /// <summary>
    /// Configuration object for validation thresholds
    /// </summary>
    public class ValidationConfiguration : Loggable
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logMan">IHtecLogManager instance</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        public ValidationConfiguration(IHtecLogManager logMan, IConfigurationManager configurationManager) : base(logMan, "PumpTransactionValidator", configurationManager)
        {
        }

        /// <summary>
        /// Threshold for vending time.
        /// </summary>
        public uint VendTimeThreshold => ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyVendTimeThreshold, Constants.DefaultVendTimeThreshold, LoggerIConfigurationManager);

        /// <summary>
        /// Threshold for flow speed.
        /// </summary>
        public uint FlowSpeedThreshold => ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyFlowSpeedThreshold, Constants.DefaultFlowSpeedThreshold, LoggerIConfigurationManager);


        /// <summary>
        /// Threshold for authorization time.
        /// </summary>
        public uint AuthTimeThreshold => ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyAuthTimeThreshold, Constants.DefaultAuthTimeThreshold, LoggerIConfigurationManager);
    }
}
