import { DOCUMENT } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { environment } from 'src/environments/environment';
import { GenericOptConfig } from '../core/models/genericOptConfig.model';
import { Opt } from '../core/models/opt.model';
import { OptService } from './opt.service';

describe('OptService', () => {
  let service: OptService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        OptService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    service = TestBed.inject(OptService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('.getGenericOptConfig() with force reload true calls the API', () => {
    //Arrange
    let mockedGenericOptConfig = {};
    
    //Act
    service.getGenericOptConfig(true).subscribe(()=>{
      //Do nothing
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getGenericOptConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedGenericOptConfig);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getGenericOptConfig() with force reload false calls the API when cached data is empty', () => {
    //Arrange
    let mockedGenericOptConfig = {};
    
    //Act
    service.getGenericOptConfig(false).subscribe(()=>{
      //Do nothing
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getGenericOptConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedGenericOptConfig);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getGenericOptConfig() with force reload false does not call the API when cached data is not empty', () => {
    //Arrange
    service['cachedGenericOptConfig'] = {
      ServiceAddress : '*******'
    } as GenericOptConfig;
    
    //Act
    service.getGenericOptConfig(false).subscribe(()=>{
      //Do nothing
    });

    //Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectNone(URL + environment.optServiceBase + environment.optController.base + environment.optController.getGenericOptConfig);
    
    //Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getGenericOptConfig() should handle success response from API', () => {
    //Arrange
    let mockedGenericOptConfig = {} as GenericOptConfig;
    
    //Act
    service.getGenericOptConfig().subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toEqual(mockedGenericOptConfig);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getGenericOptConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedGenericOptConfig);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getGenericOptConfig() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.getGenericOptConfig().subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getGenericOptConfig);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGenericServiceAdress() should handle success response from API', () => {
    //Arrange
    let newValue = '*********';
        
    //Act
    service.setGenericServiceAdress(newValue).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setSetGenericServiceAddress);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setGenericServiceAdress() should handle unsuccess response from API', () => {
    //Arrange
    let newValue = '*********';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setGenericServiceAdress(newValue).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setSetGenericServiceAddress);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addEsocketEndpoint() should handle success response from API', () => {
    //Arrange
    let newIpAddress = '*********';
    let newPort = 5001;
        
    //Act
    service.addEsocketEndpoint(newIpAddress,newPort).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addEsocket);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addEsocketEndpoint() should handle unsuccess response from API', () => {
    //Arrange
    let newIpAddress = '*********';
    let newPort = 5001;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.addEsocketEndpoint(newIpAddress, newPort).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addEsocket);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeEsocketEndpoint() should handle success response from API', () => {
    //Arrange
    let newIpAddress = '*********';
    let newPort = 5001;
        
    //Act
    service.removeEsocketEndpoint(newIpAddress,newPort).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeEsocket);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeEsocketEndpoint() should handle unsuccess response from API', () => {
    //Arrange
    let newIpAddress = '*********';
    let newPort = 5001;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.removeEsocketEndpoint(newIpAddress, newPort).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeEsocket);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addTariffMapping() should handle success response from API', () => {
    //Arrange
    let grade = 1;
    let productCode = '123';
    let fuelCardsOnly = true;
        
    //Act
    service.addTariffMapping(grade,productCode,fuelCardsOnly).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addTariffMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addTariffMapping() should handle unsuccess response from API', () => {
    //Arrange
    let grade = 1;
    let productCode = '123';
    let fuelCardsOnly = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.addTariffMapping(grade, productCode, fuelCardsOnly).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addTariffMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeTariffMapping() should handle success response from API', () => {
    //Arrange
    let grade = 1;
    let productCode = '123';
    let fuelCardsOnly = true;
        
    //Act
    service.removeTariffMapping(grade,productCode).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeTariffMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeTariffMapping() should handle unsuccess response from API', () => {
    //Arrange
    let grade = 1;
    let productCode = '123';
    let fuelCardsOnly = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.removeTariffMapping(grade, productCode).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeTariffMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addDiscountCard() should handle success response from API', () => {
    //Arrange
    let iin = '12345';
    let name = 'Discount Card 1';
    let type = '1';
    let value = 1;
    let grade = 1;
        
    //Act
    service.addDiscountCard(iin,name,type,value,grade).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addDiscountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addDiscountCard() should handle unsuccess response from API', () => {
    //Arrange
    let iin = '12345';
    let name = 'Discount Card 1';
    let type = '1';
    let value = 1;
    let grade = 1;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.addDiscountCard(iin,name,type,value,grade).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addDiscountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeDiscountCard() should handle success response from API', () => {
    //Arrange
    let iin = '12345';
        
    //Act
    service.removeDiscountCard(iin).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeDiscountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addDiscountCard() should handle unsuccess response from API', () => {
    //Arrange
    let iin = '12345';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.removeDiscountCard(iin).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeDiscountCard);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addWash() should handle success response from API', () => {
    //Arrange
    let programId = 1;
    let productCode = '1';
    let description = 'Basic wash';
    let price = 500;
    let vatRate = 21;
    let category = '1';
    let subcategory = '1';
        
    //Act
    service.addWash(programId,productCode,description,price,vatRate,category,subcategory).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addWash);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addWash() should handle unsuccess response from API', () => {
    //Arrange
    let programId = 1;
    let productCode = '1';
    let description = 'Basic wash';
    let price = 500;
    let vatRate = 21;
    let category = '1';
    let subcategory = '1';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.addWash(programId,productCode,description,price,vatRate,category,subcategory).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addWash);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeWash() should handle success response from API', () => {
    //Arrange
    let programId = 1;
    let productCode = '1';
    let description = 'Basic wash';
    let price = 500;
    let vatRate = 21;
    let category = '1';
    let subcategory = '1';
        
    //Act
    service.removeWash(programId,productCode,description,price,vatRate,category,subcategory).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeWashByProgramId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removeWash() should handle unsuccess response from API', () => {
    //Arrange
    let programId = 1;
    let productCode = '1';
    let description = 'Basic wash';
    let price = 500;
    let vatRate = 21;
    let category = '1';
    let subcategory = '1';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.removeWash(programId,productCode,description,price,vatRate,category,subcategory).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removeWashByProgramId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addPredefinedAmount() should handle success response from API', () => {
    //Arrange
    let amount = 100;
        
    //Act
    service.addPredefinedAmount(amount).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addPredefinedAmount);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addPredefinedAmount() should handle unsuccess response from API', () => {
    //Arrange
    let amount = 100;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.addPredefinedAmount(amount).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.addPredefinedAmount);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removePredefinedAmount() should handle success response from API', () => {
    //Arrange
    let amount = 100;
        
    //Act
    service.removePredefinedAmount(amount).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removePredefinedAmount);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.removePredefinedAmount() should handle unsuccess response from API', () => {
    //Arrange
    let amount = 100;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.removePredefinedAmount(amount).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.removePredefinedAmount);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptLayoutMode() should handle success response from API', () => {
    //Arrange
    let mode = 100;
        
    //Act
    service.setReceiptLayoutMode(mode).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptLayoutMode);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptLayoutMode() should handle unsuccess response from API', () => {
    //Arrange
    let mode = 100;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setReceiptLayoutMode(mode).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptLayoutMode);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.reloadOptConfiguration() should handle success response from API', () => {
    //Arrange
        
    //Act
    service.reloadOptConfiguration().subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.reloadOptConfiguration);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.reloadOptConfiguration() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.reloadOptConfiguration().subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.reloadOptConfiguration);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.startConfigBatch() should handle success response from API', () => {
    //Arrange
        
    //Act
    service.startConfigBatch().subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.startConfigBatch);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.startConfigBatch() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.startConfigBatch().subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.startConfigBatch);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.stopConfigBatch() should handle success response from API', () => {
    //Arrange
        
    //Act
    service.stopConfigBatch().subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.stopConfigBatch);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.stopConfigBatch() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.stopConfigBatch().subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.stopConfigBatch);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getOpts() should handle success response from API', () => {
    //Arrange
    let mockedResponse = {
      StringId: 1,
      Status: 'Pod',
      Pumps: [
        {
          Number: 0,
          Tid: null,
          Closed: false,
          ClosePending: false,
          OptStringId: 1,
          InUse: false,
          NozzleUp: false,
          HasPayment: false,
          AnprRequested: false,
          AnprOk: false,
          Delivering: false,
          Delivered: false,
          ThirdPartyPending: false,
          PodMode: true,
          KioskOnly: true,
          OutsideOnly: true,
          DefaultKioskOnly: false,
          DefaultOutsideOnly: true,
          MaxFillOverrideForFuelCards: false,
          MaxFillOverrideForPaymentCards: false
        },
        {
          Number: 3,
          Tid: null,
          Closed: false,
          ClosePending: false,
          OptStringId: 1,
          InUse: false,
          NozzleUp: false,
          HasPayment: false,
          AnprRequested: false,
          AnprOk: false,
          Delivering: false,
          Delivered: false,
          ThirdPartyPending: false,
          PodMode: true,
          KioskOnly: true,
          OutsideOnly: false,
          DefaultKioskOnly: true,
          DefaultOutsideOnly: false,
          MaxFillOverrideForFuelCards: false,
          MaxFillOverrideForPaymentCards: false
        }
      ],
      Connected: false,
      SignedIn: false,
      PrinterError: false,
      PaperLow: false,
      HasContactless: false,
      InUse: false,
      SoftwareVersion: null,
      AvailableSoftware: [],
      AvailableSecureAssets: [],
      AvailableCpatAssets: [],
      IpAddress: null,
      Subnet: null,
      Gateway: null,
      Dns1: null,
      Dns2: null,
      ReceiptHeader: 'OPT1 Header',
      PlaylistFileName: null,
      MediaChannel: true,
      SecureAssetsVersion: null,
      MultimediaAssetsVersion: null,
      CpatAssetsVersion: null,
      OptFirmwareVersion: null,
      EmvKernelVersion: null,
      PluginType: null,
      PluginVersion: null
    };

    //Act
    service.getOpts().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getOptInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getOpts() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.getOpts().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.getOptInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptHeader() should handle success response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.ReceiptHeaders = ['test header'];
        
    //Act
    service.setReceiptHeader(opt).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptHeader);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptHeader() should handle unsuccess response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.ReceiptHeaders = ['test header'];
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setReceiptHeader(opt).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptHeader);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptFooter() should handle success response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.ReceiptFooters = ['test footer'];
        
    //Act
    service.setReceiptFooter(opt).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptFooter);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setReceiptFooter() should handle unsuccess response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.ReceiptFooters = ['test footer'];
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setReceiptFooter(opt).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setReceiptFooter);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.restartOpt() should handle success response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
        
    //Act
    service.restartOpt(opt).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.RestartOpt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.restartOpt() should handle unsuccess response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.restartOpt(opt).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.RestartOpt);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.refreshOpt() should handle success response from API', () => {
    //Arrange
    let stringId = '1';
        
    //Act
    service.refreshOpt(stringId).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SendConfigPending + "?optId=" + stringId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.refreshOpt() should handle unsuccess response from API', () => {
    //Arrange
    let stringId = '1';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.refreshOpt(stringId).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SendConfigPending + "?optId=" + stringId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPlaylistFileName() should handle success response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.PlaylistFileName = 'test PlaylistFileName';
        
    //Act
    service.setPlaylistFileName(opt).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SetPlaylistFileName);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPlaylistFileName() should handle unsuccess response from API', () => {
    //Arrange
    let opt = new Opt();
    opt.StringId = '1';
    opt.PlaylistFileName = 'test PlaylistFileName';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPlaylistFileName(opt).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SetPlaylistFileName);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessAllowed() should handle success response from API', () => {
    //Arrange
    let value = true;
        
    //Act
    service.setContactlessAllowed(value).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SetContactlessAllowed);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessAllowed() should handle unsuccess response from API', () => {
    //Arrange
    let value = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setContactlessAllowed(value).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.SetContactlessAllowed);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessCardPreAuth() should handle success response from API', () => {
    //Arrange
    let value = 1;
        
    //Act
    service.setContactlessCardPreAuth(value).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessCardPreAuth);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessCardPreAuth() should handle unsuccess response from API', () => {
    //Arrange
    let value = 1;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setContactlessCardPreAuth(value).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessCardPreAuth);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessDevicePreAuth() should handle success response from API', () => {
    //Arrange
    let value = 1;
        
    //Act
    service.setContactlessDevicePreAuth(value).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessDevicePreAuth);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessDevicePreAuth() should handle unsuccess response from API', () => {
    //Arrange
    let value = 1;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setContactlessDevicePreAuth(value).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessDevicePreAuth);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessTtq() should handle success response from API', () => {
    //Arrange
    let value = 'X';
        
    //Act
    service.setContactlessTtq(value).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessTtq);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setContactlessTtq() should handle unsuccess response from API', () => {
    //Arrange
    let value = 'X';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setContactlessTtq(value).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.optController.base + environment.optController.setContactlessTtq);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

});


