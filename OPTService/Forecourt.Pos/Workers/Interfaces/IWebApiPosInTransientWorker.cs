using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Interfaces;

namespace Forecourt.Pos.Workers.Interfaces
{
    public interface IWebApiPosInTransientWorker: 
        IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>, 
        IPosIntegratorInTransient<IMessageTracking, IPump, Result>
    {
    }
}
