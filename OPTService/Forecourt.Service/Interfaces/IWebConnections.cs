using CSharpFunctionalExtensions;
using Htec.Foundation.Models;
using OPTService;
using System.Net;

namespace Forecourt.Service.Interfaces
{
    /// <summary>
    /// Anything and everything related to the Connections tab in the Web UI
    /// </summary>
    public interface IWebConnections
    {
        /// <inheritdoc cref="IWeb.ConnectionsDetails"/>
        Result<StatusCodeResult> ConnectionsDetailsHttp();

        /// <summary>
        /// Set the IP Address (and optionally port) for the Pump Controller connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <param name="port">Port number, optional.  Uses existing port if default used</param>
        /// <returns>Result wrapped <see cref="StatusCodeResult"/></returns>
        Result<StatusCodeResult> SetPumpControllerAddressHttp(IPAddress address, int port = 0);

        /// <summary>
        /// Set the login info for the Pump Controller connection.
        /// </summary>
        /// <param name="loginInfo">The login info to set.</param>
        /// <returns>Result wrapped <see cref="StatusCodeResult"/></returns>
        Result<StatusCodeResult> SetPumpControllerLogonInfoHttp(string loginInfo);

        /// <summary>
        /// Set the IP Address (and optionally port) for the Tank Gauge connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <param name="port">Port number, optional.  Uses existing port if default used</param>
        /// <returns>Result wrapped <see cref="StatusCodeResult"/></returns>
        Result<StatusCodeResult> SetTankGaugeAddressHttp(IPAddress address, int port = 0);
    }
}