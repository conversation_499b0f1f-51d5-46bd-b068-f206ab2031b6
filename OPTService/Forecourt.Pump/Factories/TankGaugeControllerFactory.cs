using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Pump.Factories
{
    /// <summary>
    /// Pump integrator factory
    /// </summary>
    public class TankGaugeControllerFactory : Factory<string, ITankGaugeController>, ITankGaugeControllerFactory
    {
        private readonly Func<string, ITankGaugeController> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public TankGaugeControllerFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, ITankGaugeController> resolveTypeInstance) : base(logManager, $"{nameof(PumpIntegratorInFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(Core.Configuration.Constants.Integrator.PumpTypeHsc, "Htec Site Controller / HydraFDC", (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.PumpTypeDoms, "DOMS PSS 5000 Controller", (key) => _resolveTypeInstance(key));
        }
    }
}
