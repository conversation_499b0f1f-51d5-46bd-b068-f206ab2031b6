using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pump.DomsSetupClasses
{
    public class DomsSetupPosProtocol : DomsSetup
    {
        public DomsSetupPosProtocol(IPumpControllerCallbacks callbacks, byte posOpt, byte posId, IHtecLogManager logManage, bool update, IConfigurationManager configurationManager,
            IPumpCollection allPumps) : base(callbacks, posOpt, posId, logManage, update, configurationManager, allPumps)
        {
        }
    }
}
