using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using OPTService;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using System.Web.Http.Description;
using Unity;
using bosMessage = Htec.Hydra.Core.Bos.Messages;
using TransactionItem = Htec.Hydra.Core.Bos.Messages.TransactionItem;

namespace Forecourt.Service.Controllers
{
    /// <summary>
    /// Provides a RestAPI for all BOS requests
    /// </summary>
    /// <inheritdoc/>
    [EnableCors("*", "*", "*")]
    [Authorize(Roles = "WebUi,Swagger,ThirdParty,Orbis")]
    [RoutePrefix("api/bos")]
    public class BosController : LinkedLoggableApiController<IBosIntegratorIn<IMessageTracking, Result<StatusCodeResult>>>
    {
        /// <inheritdoc/>
        public BosController([Dependency(UnityConfig.MessageBrokerName)] IBosIntegratorIn<IMessageTracking, Result<StatusCodeResult>> loggable, IConfigurationManager configurationManager = null) : base(loggable as Loggable, configurationManager)
        {
        }

        /// <summary>
        /// Request Transaction information
        /// </summary>
        /// <param name="request"><see cref="bosMessage.SignalR.TransactionIds"/> instance</param>
        /// <param name="loggindReference">Logging reference</param>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpPost, Route("transaction"), ResponseType(typeof(TransactionItem))]
        public async Task<IHttpActionResult> RequestTransaction([FromBody] bosMessage.SignalR.TransactionIds request, [FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync<TransactionItem>(async () =>
                Loggable.RequestTransaction((bosMessage.IdInfo)request, loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Request that Day-End processing is performed
        /// </summary>
        /// <param name="loggindReference"></param>
        /// <returns></returns>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpGet, Route("dayEnd")]
        public async Task<IHttpActionResult> RequestDayEnd([FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync(async () => Loggable.RequestDayEnd(loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }

        /// <summary>
        /// Request that Shift-End processing is performed
        /// </summary>
        /// <param name="loggindReference"></param>
        /// <returns></returns>
        /// <returns>HttpActionResult</returns>
        /// <response code="200">Success, request was processed</response>
        /// <response code="400">Bad/invalid request</response>
        /// <response code="501">Service is not configured correctly - Integrator mismatch</response>
        [HttpGet, Route("shiftEnd")]
        public async Task<IHttpActionResult> RequestShiftEnd([FromUri] string loggindReference = null)
        {
            var result = await DoHttpActionAsync(async () => Loggable.RequestShiftEnd(loggindReference.ToMessageTracking())).ConfigureAwait(false);
            return result;
        }
    }
}

