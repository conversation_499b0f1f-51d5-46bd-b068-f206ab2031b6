using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.Core.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Messages.Opt.Messages;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.Models.Notifications;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Xml.Linq;
using Pump = Forecourt.Core.Pump.Models.Pump;

namespace OPT.Common.Workers
{
    public abstract class OptListenerWorker : ListenerWorker<XElement, IHydraDb, ITelemetryWorker>
    {
        public const string HeaderOnPumpState = ConfigConstants.HeaderOnPumpState;
        public const string HeaderOpt = "OPT";
        public const string ResultFailure = "Failure";
        public const string ResultSuccess = "Success";

        protected IOptCollection AllOpts { get; private set; }
        protected IPumpCollection AllPumps { get; private set; }

        protected MessageParser MessageParser { get; private set; }
        protected MessageBuilder MessageBuilder { get; private set; }

        /// <summary>
        /// Indicates whether the client connections are newly established 
        /// </summary>
        protected ConcurrentDictionary<int, bool> IsPseudoSocketReplacedConnection { get; } = new ConcurrentDictionary<int, bool>();

        protected OptListenerWorker(IHtecLogger logger, ITelemetryWorker telemetryWorker, IListenerConnectionThread<XElement> connectionThread,
            IHydraDb hydraDb, IConfigurationManager configurationManager, IPumpCollection allPumps, IOptCollection allOpts, IControllerWorker controllerWorker,
            IPosIntegratorOutTransient<IMessageTracking> posWorker, ITimerFactory timerFactory = null, string defaultTimerInterval = null) :
            base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, defaultTimerInterval)
        {
            DoCtor(allPumps, allOpts, controllerWorker, posWorker);
        }

        protected OptListenerWorker(IHtecLogManager logMan, string loggerName, ITelemetryWorker telemetryWorker, IListenerConnectionThread<XElement> connectionThread,
           IHydraDb hydraDb, IConfigurationManager configurationManager, IPumpCollection allPumps, IOptCollection allOpts, IControllerWorker controllerWorker,
           IPosIntegratorOutTransient<IMessageTracking> posWorker, ITimerFactory timerFactory = null, string defaultTimerInterval = null) :
           base(logMan, loggerName, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, defaultTimerInterval)
        {
            DoCtor(allPumps, allOpts, controllerWorker, posWorker);
        }

        private void DoCtor(IPumpCollection allPumps, IOptCollection allOpts, IControllerWorker controllerWorker, IPosIntegratorOutTransient<IMessageTracking> posInWorker)
        {
            AllPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));
            AllOpts = allOpts ?? throw new ArgumentNullException(nameof(allOpts));

            RegisterWorker(controllerWorker);
            RegisterWorker(posInWorker);
            posInWorker?.RegisterWorker(this);

            MessageParser = new MessageParser(Logger);
            MessageBuilder = new MessageBuilder(MyIdString, "AnyOPTId");
        }

        /// <summary>
        /// Register that a message has been received from a given OPT.
        /// Reset time of latest message and note that the OPT is connected.
        /// </summary>
        /// <param name="id">Id of OPT.</param>
        /// <param name="socket">Which socket has received.</param>
        protected void RegisterReceived(int id, SocketType socket)
        {
            DoAction(() =>
            {
                if (!AllOpts.TryGetOpt(id, out var opt))
                {
                    return;
                }

                var wasToOptConnected = opt.ToOptConnected;
                var wasFromOptConnected = opt.FromOptConnected;
                var wasHeartbeatConnected = opt.HeartbeatConnected;
                var wasAllConnected = opt.AllConnected;
                opt.Received(socket);

                if (opt.ToOptConnected != wasToOptConnected ||
                    opt.FromOptConnected != wasFromOptConnected ||
                    opt.HeartbeatConnected != wasHeartbeatConnected)
                {
                    GetWorker<IControllerWorker>().ConnectionsChanged();
                }

                if (opt.AllConnected != wasAllConnected)
                {
                    UpdateAllPumpsOnPos(opt, LoggingReference);
                }
            }, LoggingReference);
        }

        protected string LogCommandInfo(XElement element, int id)
        {
            return $"{id}; Xml: {element.ToString(SaveOptions.DisableFormatting).Replace(Environment.NewLine, "\n").Replace("\n", Environment.NewLine)}";
        }

        /// <summary>
        /// Matches {IOpt}.IdString against the incoming message, to see if an IOpt has already been linked, if not it assigned the next Id in sequence
        /// </summary>
        /// <param name="message">{T} message received.</param>
        /// <returns></returns>
        protected override Result<int> DoGetNextClientId(IMessageTracking<XElement> message)
        {
            var msgOpt = MessageParser.ParseXElement(message.Request) is OptMessage msg ? msg : null;
            var id = AllOpts.GetOptIdForIdString(msgOpt?.Opt.Id);
            IsPseudoSocketReplacedConnection.AddOrUpdate(id, true, (k, v) => true);
            return Result.Success(id);
        }

        protected bool IsOnPumpStateActive(IOpt opt)
        {
            var result = opt?.PumpList()?.Any(x => AllPumps.OnPumpStateActive.TryGetValue(x, out var info) && info.IsActive) ?? false;
            if (result)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "OPT", () => new[] {$"{opt.IdString} has a pump being processed!"});
            }

            return result;
        }

        protected bool IsOnPumpStateActive(byte pump)
        {
            var result = AllPumps.OnPumpStateActive.TryGetValue(pump, out var info) && info.IsActive;
            if (result)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{Pump.HeaderPump}", () => new[] {$"{pump} is being processed!"});
            }

            return result;
        }

        protected bool SetOnPumpStateActive(IMessageTracking message, byte pump, SocketType channel, bool value)
        {
            if (pump == 0)
            {
                DoDeferredLogging(LogLevel.Warn, $"{Pump.HeaderPump}", () => new[] {$"{pump} is being processed!"},
                    reference: message.ParentId == Guid.Empty ? message.IdAsString : message.FullId);
            }

            if (AllPumps.OnPumpStateActive.TryGetValue(pump, out var existingValue))
            {
                existingValue.ActiveChannels[channel] = value;
            }

            return AllPumps.OnPumpStateActive.AddOrUpdate(pump, new OnPumpStateActiveInfo(channel, value), 
                (key, oldValue) => existingValue ?? new OnPumpStateActiveInfo(channel, value)).IsActive;
        }

        protected bool SetOnPumpStateActive(IMessageTracking message, int optId, SocketType channel, bool value, out byte pump)
        {
            pump = AllPumps.AllPumps.FirstOrDefault(x => x.Opt?.Id == optId)?.Number ??
                   (byte) (!AllOpts.TryGetOpt(optId, out var opt)
                       ? 0
                       : (opt?.GetCurrentNotification() is IPumpNotificationRequest<PumpNotification> pumpNotification)
                           ? pumpNotification.Notification.Pump
                           : 0);

            return SetOnPumpStateActive(message, pump, channel, value);
        }

        /// <inheritdoc cref="MessageBuilder"/>
        protected XElement GetRequestMessage(IOpt opt, OptRequest request, string messageId = null, bool ignoreHydraId = false, bool ignoreOptId = false)
        {
            return (opt?.MessageBuilder ?? MessageBuilder).GetRequestMessage(request, messageId, ignoreHydraId, opt == null || ignoreOptId).ToXElement();
        }

        /// <inheritdoc cref="MessageBuilder"/>
        protected XElement GetResponseMessage(IOpt opt, OptResponse response, OptRequest originalRequest = null)
        {
            return opt.MessageBuilder.GetResponseMessage(response, originalRequest).ToXElement();
        }

        /// <summary>
        /// Registers the disconnect of each channel, with the Opt
        /// </summary>
        /// <param name="channel">Opt channel being disconnected</param>
        /// <param name="id">Opt id</param>
        protected void RegisterDisconnect(SocketType channel, int? id = null)
        {
            if (id == null)
            {
                return;
            }

            if (AllOpts.TryGetOpt(id.Value, out var opt))
            {
                DoDeferredLogging(LogLevel.Info, $"OPT {opt.IdString} ({id})");

                opt.Disconnected(channel);
                if (!opt.Connected)
                {
                    opt.SignOut();
                    UpdateAllPumpsOnPos(opt,  null);
                }
            }
        }

        protected void UpdateAllPumpsOnPos(IOpt opt, string reference)
        {
            var message = new MessageTracking() { IdAsString = reference };

            foreach (var pump in opt.PumpList())
            {
                if (AllPumps.TryGetPump(pump, out var thePump))
                {
                    GetWorker<IPosIntegratorOutTransient<IMessageTracking>>()?.StatusResponse(thePump.Number, message);
                }
            }

            NotificationWorker.PushChange(EventType.ConnectionChanged);
        }

        /// <summary>
        /// Extracts the short name from an OptNotification or Notification name
        /// </summary>
        /// <param name="message">Message to extract short name for</param>
        /// <returns>The short name</returns>
        protected string GetShortNotificationName(XmlTyped message)
        {
            return GetShortNotificationName(message?.GetType().Name);
        }

        /// <summary>
        /// Extracts the short name from an OptNotification or Notification name
        /// </summary>
        /// <param name="fullName">The full name</param>
        /// <returns>The short name</returns>
        protected string GetShortNotificationName(string fullName)
        {
            // TODO: There will be a better way when this is moved into the  HydraOpt.OptMessage namespace!!!
            return fullName?.Replace("OptNotificationRequest", string.Empty).Replace("OptNotificationResponse", string.Empty)
                .Replace("NotificationRequest", string.Empty)
                .Replace("NotificationResponse", string.Empty);
        }
    }
}
