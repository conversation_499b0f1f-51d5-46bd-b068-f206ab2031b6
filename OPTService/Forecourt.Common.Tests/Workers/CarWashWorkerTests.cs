using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using Xunit;

namespace OPT.Common.Tests.Workers
{
    public class CarWashWorkerTests
    {
        private readonly ICarWashWorker _carWashWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IFromOptWorker _optWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IHtecLogger _logger;
        private readonly IOpt _optOne;
        private readonly IClientConnectionThread<string> _connectionThread;
        private readonly IConfigurationManager _configurationManager;
        private readonly ITimerFactory _timerFactory;

        public CarWashWorkerTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _logger = Substitute.For<IHtecLogger>();
            _optOne = Substitute.For<IOpt>();
            _connectionThread = Substitute.For<IClientConnectionThread<string>>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _timerFactory = Substitute.For<ITimerFactory>();

            _carWashWorker = new CarWashWorker(_telemetryWorker, _hydraDb, _logger, _connectionThread, _configurationManager, _timerFactory);
            _carWashWorker.RegisterWorker(_optWorker);
            _carWashWorker.Start();
            _carWashWorker.OnConnected();
        }

        [Fact]
        public void test_unknown_command()
        {
            // Arrange
            const string message = ConfigConstants.Unknown;

            // Act
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _optWorker.DidNotReceiveWithAnyArgs().CarWashNoTicket(null);
            _optWorker.DidNotReceiveWithAnyArgs().CarWashTicket(null, "", "");
        }

        [Fact]
        public void test_invalid_command()
        {
            // Arrange
            const string message = "XTICKET";

            // Act
            _carWashWorker.OnMessageReceived(new MessageTracking<string>(message));

            // Assert
            _optWorker.DidNotReceiveWithAnyArgs().CarWashNoTicket(null);
            _optWorker.DidNotReceiveWithAnyArgs().CarWashTicket(null, "", "");
        }

        [Fact]
        public void test_send_request_message_is_sent_to_connection_thread()
        {
            // Arrange
            const string machine = "Brush";
            const byte prog = 1;
            string expectedText = $"Valet={machine}/Normal/{prog}/";

            var bytesSent = "";

            InitialiseConnectionThread(x => bytesSent = x);

            // Act
            _carWashWorker.SendRequestToCarWash(_optOne, machine, prog);

            // Assert
            bytesSent.Should().Be(expectedText);
        }
        
        #region Helpers

        private void InitialiseConnectionThread(Action<string> doFunc)
        {
            _connectionThread.When(x => x.Send(Arg.Any<MessageTracking<string>>(), Arg.Any<AsyncSocketState<Htec.Foundation.Connections.Sockets.Interfaces.ISocketWithBuffer>>(), Arg.Any<Action>()))
                .Do(x => doFunc(x.ArgAt<MessageTracking<string>>(0).Request));

            _connectionThread.IsConnected()
                .Returns(true);
        }

        #endregion
    }
}
