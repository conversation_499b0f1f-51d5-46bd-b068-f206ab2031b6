import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { DivertDetails } from '../core/models/divertDetails.model';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class DivertService {

  private baseUrl: string;

  /**
   * The DivertService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.divertController.base;
  }

  /**
   * Gets the divert details
   */
  public getDivertDetails(): Observable<any> {
    const url = this.baseUrl + environment.divertController.getDivertDetails;
    const options = {
      params: {}
    };

    this.logger.debug(`Getting divert details via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Sets the divert details
   * @param divertDetails The divert details values
   */
  public divertOptService(divertDetails: DivertDetails) {
    const url = this.baseUrl + environment.divertController.divertOptService;
    this.logger.debug(`Setting divert details via ${url}`);
    return this.http.post(url, divertDetails);
  }

  /**
   * Cancel the divert details override
   */
  public cancelDivertOptService() {
    const url = this.baseUrl + environment.divertController.cancelDivertOptService;
    this.logger.debug(`Cancel divert details via ${url}`);
    return this.http.get(url);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
