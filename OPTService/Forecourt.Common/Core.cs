using CSharpFunctionalExtensions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.System.Diagnostics.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Foundation.Workers;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common.Constants;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Connectable = Htec.Foundation.Connections.Core.Connectable;
using coreConfig = Forecourt.Core.Configuration;
using FuelTransaction = Forecourt.Core.HydraDb.Models.FuelTransaction;

namespace OPT.Common
{
    [HasConfiguration()]
    public class Core : Connectable, ICore
    {
        public const string ConfigKeyCustomDataOverride = ConfigConstants.ConfigKeyCustomDataOverride;

        private readonly IFileSystem _fileSystem;
        private readonly IFileVersionInfoFactory _fileInfoVersionFactory;
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        private readonly IPumpIntegratorConfiguration _pumpWorker;
        private readonly IFromOptWorker _optWorker;
        private readonly IHydraDb _hydraDb;
        private IPaymentConfigIntegrator _paymentConfig => GetWorker<IPaymentConfigIntegrator>();
        private readonly IJournalWorker _journalWorker;
        private readonly IHydraTransactionFile _transactionFile;
        private readonly IUpdateWorker _updateWorker;
        private readonly IConfigUpdateWorker _configUpdateWorker;
        private readonly ILocalAccountWorker _localAccountWorker;
        private readonly IDomsWorker _domsWorker;
        private readonly IIntegratorFactories _integratorFactories;
        private readonly ICacheHelper _cacheHelper;

        private IRetalixTransactionFile _retalixTransactionFile;

        private readonly Func<Type, IWorkerable> _resolveTypeInstance;  // TODO: When POS Factoried, will need to be <string, Type, IPosIntegratorOut<IMessageTracking>>, or such like ADO #477693

        public IControllerWorker ControllerWorker { get; }
        public IOptCollection AllOpts { get; }
        public IPumpCollection AllPumps { get; }

        public IList<TermId> FetchedTids => _optWorker.FetchedTids ?? new List<TermId>();
        public ICollection<GradeName> GradeNames => _optWorker.GradeNames ?? new List<GradeName>();
        public AdvancedConfig AdvancedConfig => _hydraDb.AdvancedConfig;
        public GenericOptConfig GenericOptConfig => _optWorker.GenericOptConfig;

        public IEnumerable<string> WhitelistFiles => _configUpdateWorker.WhitelistFiles;
        public IEnumerable<string> LayoutFiles => _configUpdateWorker.LayoutFiles;
        public IEnumerable<string> UpgradeFiles => _configUpdateWorker.UpgradeFiles;
        public IEnumerable<string> SoftwareFiles => _configUpdateWorker.SoftwareFiles;
        public IEnumerable<string> MediaFiles => _configUpdateWorker.MediaFiles;
        public IEnumerable<string> PlaylistFiles => _configUpdateWorker.PlaylistFiles;
        public IEnumerable<string> DatabaseBackupFiles => _configUpdateWorker.DatabaseBackupFiles;
        public IEnumerable<string> OptLogFiles => _configUpdateWorker.OptLogFiles;
        public int FilePruneDays => _configUpdateWorker.FilePruneDays;
        public int TransactionPruneDays => _configUpdateWorker.TransactionPruneDays;
        public int ReceiptPruneDays => _configUpdateWorker.ReceiptPruneDays;
        public string SetFilePruneDays(int days) => _configUpdateWorker.SetFilePruneDays(days);
        public string SetTransactionPruneDays(int days) => _configUpdateWorker.SetTransactionPruneDays(days);
        public string SetReceiptPruneDays(int days) => _configUpdateWorker.SetReceiptPruneDays(days);

        public IList<long> ReceiptTrans => _hydraDb.ReceiptTrans();

        /// <inheritdoc cref="ICore.GetCategoriesConfiguration"/>
        public IList<CategoryConfiguration> GetCategoriesConfiguration => ControllerWorker?.GetCategoriesConfiguration();

        /// <inheritdoc cref="ICore.SetCategories(IEnumerable{CategoryConfiguration})"/>
        public Result SetCategories(IEnumerable<CategoryConfiguration> categories)
        {
            return DoAction(() =>
            {
                var preActions = new List<ConfigKeyValueValidator> {
                new (GenericOptConfig.EnhancedLoggingKey, CategoryConfigurationExtensions.IsValueBool),
                new (GenericOptConfig.FuelCardEnabledKey, CategoryConfigurationExtensions.IsValueBool),
                new (GenericOptConfig.PaymentCardEnabledKey, CategoryConfigurationExtensions.IsValueBool),
                new (GenericOptConfig.LocalAccountEnabledKey, CategoryConfigurationExtensions.IsValueBool),
                new (GenericOptConfig.PciRestartTimeKey, CategoryConfigurationExtensions.IsValueTime),
                new (GenericOptConfig.GetConfigRetryAttemptsKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.MinimumAuthorisedAmountKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.CardRemovalTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.MainThreadHeartbeatTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.SignInMsgTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.ConfigMsgTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.PaymentApprovedMsgTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.ConfigKeyPaymentClearedMsgTimeout, CategoryConfigurationExtensions.IsValueInt),
                new (GenericOptConfig.GeneralMessageAckTimeoutKey, CategoryConfigurationExtensions.IsValueInt),
                };

                var postActions = new List<Action<IEnumerable<CategoryConfiguration>>> {
                SetEnhancedOptLogging,
                SetOptConfig,
                SetTimerInterval,
                SetEsocketCustomData,
                SetSignalRConfig};

                var result = _hydraDb?.SetCategories(categories, preActions, postActions) ?? Result.Failure("No HydraDb!");

                if (result.IsSuccess)
                {
                    ControllerWorker.PushChange(EventType.AdvancedConfigChanged);
                }

                return result;
            }, null);
        }

        private void SetEsocketCustomData(IEnumerable<CategoryConfiguration> categories)
        {
            var customData = categories.GetCategoryByKey(ConfigKeyCustomDataOverride);

            if (customData != null)
            {
                _paymentConfig?.RefreshCustomDataOverrides(LoggingReference);
                _optWorker.CheckEsocketChanges();
            }
        }

        private void SetEnhancedOptLogging(IEnumerable<CategoryConfiguration> categories)
        {
            var enhancedOptLoggingSetting = categories.GetCategoryByKey(GenericOptConfig.EnhancedLoggingKey);

            if (enhancedOptLoggingSetting != null)
            {
                ControllerWorker.NotifyOptConfigChange();
            }
        }

        private void SetOptConfig(IEnumerable<CategoryConfiguration> categories)
        {
            var configChanged = categories.IsKnownConfigKeyValueValid(GenericOptConfig.FuelCardEnabledKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.PaymentCardEnabledKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.LocalAccountEnabledKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.PciRestartTimeKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.GetConfigRetryAttemptsKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.MinimumAuthorisedAmountKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.CardRemovalTimeoutKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.MainThreadHeartbeatTimeoutKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.SignInMsgTimeoutKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.ConfigMsgTimeoutKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.PaymentApprovedMsgTimeoutKey)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.ConfigKeyPaymentClearedMsgTimeout)
                || categories.IsKnownConfigKeyValueValid(GenericOptConfig.GeneralMessageAckTimeoutKey);

            if (configChanged)
            {
                ControllerWorker.NotifyOptConfigChange();
            }
        }

        private void SetTimerInterval(IEnumerable<CategoryConfiguration> categories)
        {
            var category = categories.FirstOrDefault(x => x.Category.Equals(Htec.Foundation.System.Timerable.CategoryNameTimers, StringComparison.InvariantCultureIgnoreCase));

            if (category == null)
            {
                return;
            }

            foreach (var setting in category.Settings)
            {
                var key = setting.Key
                    .Replace(
                        Htec.Foundation.System.Timerable.ConfigKeyPrefixTimerInterval.Replace($"{Htec.Foundation.System.Timerable.CategoryNameTimers}{ConfigurationConstants.CategorySeparator}",
                            string.Empty), string.Empty)
                    .Replace($"+{nameof(EmbeddedTimerable)}", string.Empty);

                foreach (var worker in Workers.Where(x => x is Workerable && (x.GetType().Name == key || string.IsNullOrWhiteSpace(key))).Select(x => x as Workerable))
                {
                    worker?.RestartTimer();
                }
            }
        }

        /// <summary>
        /// If and SignalR configuration has changed then restart the POS integration
        /// </summary>
        /// <param name="categories">Configuration categories</param>
        private void SetSignalRConfig(IEnumerable<CategoryConfiguration> categories)
        {
            var category = categories.FirstOrDefault(x => x.Category == coreConfig.Constants.CategoryNameEndPointsApi ||
                x.Category == coreConfig.Constants.CategoryNameEndPointsSignalR ||
                x.Category == coreConfig.Constants.CategoryNameSignalRHubClient ||
                x.Category == coreConfig.Constants.CategoryNameBOS);

            if (category != null)
            {
                var advConfig = _hydraDb.AdvancedConfig;
                var logRef = LoggingReference;

                _cacheHelper.ForceExpirationOnCachedItem($"{coreConfig.Constants.ConfigKeySuffixHydraDb}{coreConfig.Constants.CategoryNameBOS}:", coreConfig.Constants.CachedItemHydraToExternalConfigDataMap);

                // Restart the POS integration
                HandleIntegrationConnection(IntegrationType.Pos, $"{advConfig.PosType}", false, logRef, false);
                HandleIntegrationConnection(IntegrationType.Pos, $"{advConfig.PosType}", true, logRef, true);

                // Restart the BOS integration
                HandleIntegrationConnection(IntegrationType.BackOffice, advConfig.BosType, false, logRef, false);
                HandleIntegrationConnection(IntegrationType.BackOffice, advConfig.BosType, true, logRef, true);
            }
        }

        public IList<string> AvailableSoftware => _optWorker.AvailableSoftware ?? new List<string>();
        public IList<string> AvailableSecureAssets => _optWorker.AvailableSecureAssets ?? new List<string>();
        public IList<string> AvailableCpatAssets => _optWorker.AvailableCpatAssets ?? new List<string>();

        public ICollection<LocalAccountCustomer> LocalAccountCustomers => _localAccountWorker.Customers;

        public IDictionary<byte, IEnumerable<Grade>> GradePrices =>
            _pumpWorker?.GradePrices ?? new ConcurrentDictionary<byte, IEnumerable<Grade>>();

        public IDictionary<byte, float> GradePriceToSet => ControllerWorker?.GradePriceToSet ?? new ConcurrentDictionary<byte, float>();
        public bool AutoAuth => _optWorker.AutoAuth;
        public bool MediaChannel => _optWorker.MediaChannel;
        public bool UnmannedPseudoPos => _optWorker.UnmannedPseudoPos;
        public bool AsdaDayEndReport => _optWorker.AsdaDayEndReport;
        public PrinterConfig PrinterConfig => _journalWorker.PrinterConfig;
        public int ReceiptTimeout => _hydraDb.ReceiptTimeout;
        public int ReceiptMaxCount => _hydraDb.ReceiptMaxCount;
        public short TillNumber => _journalWorker.TillNumber;
        public byte PosClaimNumber => _pumpWorker.PosClaimNumber;
        public short FuelCategory => _journalWorker.FuelCategory;
        public bool ForwardFuelPriceUpdate => _updateWorker.ForwardFuelPriceUpdate;
        public IList<CardReference> CardReferences => _journalWorker.CardReferences;

        public bool FuellingIndefiniteWait => _pumpWorker.Fuelling.IsIndefiniteWait;
        public int FuellingWaitMinutes => _pumpWorker.Fuelling.WaitMinutes;
        public int FuellingBackoffAuth => _pumpWorker.Fuelling.BackoffAuth;
        public int FuellingBackoffPreAuth => _pumpWorker.Fuelling.BackoffPreAuth;
        public int FuellingBackoffStopStart => _pumpWorker.Fuelling.BackoffStopStart;
        public int FuellingBackoffStopOnly => _pumpWorker.Fuelling.BackoffStopOnly;
        public DateTime DayEndTime => _journalWorker.DayEndTime;
        public DateTime ShiftEndTime => _journalWorker.ShiftEndTime;
        public IPAddress RetalixPosPrimaryIpAddress => GetWorker<IRetalixPosWorker>()?.PrimaryIpAddress;
        public string TransactionFileDirectory => _transactionFile.FileDirectory;
        public string EsocketConnectionString => _paymentConfig?.ConnectionString;
        public bool EsocketUseConnectionString => _paymentConfig?.UseConnectionString ?? false;
        public string EsocketConfigFile => _paymentConfig?.ConfigFile;
        public string EsocketKeystoreFile => _paymentConfig?.KeystoreFile;
        public string EsocketDbUrl => _paymentConfig?.DbUrl;
        public bool EsocketOverrideProperties => _paymentConfig?.OverrideProperties ?? false;
        public bool EsocketOverrideKeystore => _paymentConfig?.OverrideKeystore ?? false;
        public bool EsocketOverrideUrl => _paymentConfig?.OverrideUrl ?? false;
        public bool EsocketOverrideContactless => _paymentConfig?.OverrideContactless ?? false;
        public bool EsocketConnectionMade => _paymentConfig?.ConnectionMade ?? false;

        public string RetalixTransactionFileDirectory =>
            _retalixTransactionFile != null && _retalixTransactionFile.Available ? _retalixTransactionFile.FileDirectory : null;

        public string WhitelistDirectory => _optWorker.WhitelistDirectory;
        public string LayoutDirectory => _optWorker.LayoutDirectory;
        public string SoftwareDirectory => _optWorker.SoftwareDirectory;
        public string MediaDirectory => _optWorker.MediaDirectory;
        public string PlaylistDirectory => _optWorker.PlaylistDirectory;
        public string OptLogFileDirectory => _optWorker.OptLogFileDirectory;
        public string ContactlessPropertiesFile => _paymentConfig?.ContactlessFile;
        public string ReceivedUpdateDirectory => _updateWorker.ReceivedUpdateDirectory;

        public IPAddress DomsIpAddress => _domsWorker?.IpAddress;
        public string DomsLoginString => _domsWorker?.LoginString;
        public bool DomsStateEnabled => _domsWorker?.StateEnabled ?? false;
        public bool DomsStateConnected => _domsWorker?.StateConnected ?? false;
        public IDomsSetup DomsFetchedSetup => _domsWorker?.FetchedSetup;
        public IDomsSetup DomsPreparedSetup => _domsWorker?.TcpPreparedSetup;
        public IDomsSetup DomsTcpSetup => _domsWorker?.TcpSetup;

        public DomsState DomsState => _domsWorker?.State;

        public IEnumerable<PaymentTimeout> GetPaymentTimeouts() => _hydraDb.FetchPaymentTimeouts();

        public IEnumerable<FuelTransaction> GetFuelTransactions
            (DateTime startTime, DateTime endTime) => _hydraDb.FetchFuelTransactions(startTime, endTime, _journalWorker.ConfigValueMaxRetalixTransactionNumber);

        public IEnumerable<OtherEvent> GetOtherEvents
            (DateTime startTime, DateTime endTime) => _hydraDb.FetchOtherEvents(startTime, endTime);

        public string FetchReceipt(long transactionNumber) =>
            _journalWorker.FormatReceipt(_hydraDb.GetReceipt(null, transactionNumber, true)?.ReceiptContent);

        public string PrintReceipt(long transactionNumber) => _journalWorker.PrintReceipt(transactionNumber);
        public string SaveReceipt(long transactionNumber) => _journalWorker.SaveReceipt(transactionNumber);

        public bool IsLoyaltyAvailable(string name) => _hydraDb.IsGenericLoyaltyAvailable(name);
        public bool IsLoyaltyPresent(string name) => _hydraDb.IsGenericLoyaltyPresent(name);

        public bool IsPrinterBusy => _journalWorker.IsPrinterBusy;

        public string GetOptIpAddressString(IOpt opt) => _optWorker.GetOptIpAddress(opt)?.ToString();

        public string SetTillNumber(short number) => _journalWorker.SetTillNumber(number);
        public string SetFuelCategory(short category) => _journalWorker.SetFuelCategory(category);
        public string SetCardReference(string name, int reference) => _journalWorker.SetCardReference(name, reference);
        public string ClearCardReference(string name) => _journalWorker.ClearCardReference(name);

        public string SetAcquirerReference
            (string cardName, string acquirerName) => _journalWorker.SetAcquirerReference(cardName, acquirerName);

        public string ClearAcquirerReference(string cardName) => _journalWorker.ClearAcquirerReference(cardName);
        public string SetFuelCard(string cardName, bool isFuelCard) => _journalWorker.SetFuelCard(cardName, isFuelCard);
        public string SetExternalName(string cardName, string externalCardName) => _journalWorker.SetExternalName(cardName, externalCardName);
        public string ClearExternalName(string cardName) => _journalWorker.ClearExternalName(cardName);

        public string SetPrinterEnabled(bool isEnabled) => _journalWorker.SetPrinterEnabled(isEnabled);
        public string SetPrinterPortName(string portName) => _journalWorker.SetPrinterPortName(portName);
        public string SetPrinterBaudRate(int baudRate) => _journalWorker.SetPrinterBaudRate(baudRate);
        public string SetPrinterHandshake(Handshake handshake) => _journalWorker.SetPrinterHandshake(handshake);
        public string SetPrinterStopBits(StopBits stopBits) => _journalWorker.SetPrinterStopBits(stopBits);
        public string SetPrinterDataBits(int dataBits) => _journalWorker.SetPrinterDataBits(dataBits);

        public Core(IHtecLogManager logManager, IConfigurationManager configurationManager, IFileSystem fileSystem, IFileVersionInfoFactory fileVersionInfoFactory, 
            IPaymentConfigIntegrator paymentConfig, IHydraDb hydraDb, IControllerWorker controllerWorker, IConfigUpdateWorker configUpdateWorker, IUpdateWorker updateWorker, 
            IJournalWorker journalWorker, ILocalAccountWorker localAccountWorker, ICarWashWorker carWashWorker, 
            ISecAuthIntegratorInTransient<IMessageTracking> secAuthInWorker, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOutWorker,
            IToOptWorker toOptWorker, IOptHeartbeatWorker heartbeatOptWorker, IFromOptWorker fromOptWorker, IMediaChannelWorker mediaChannelWorker,
            IPumpIntegratorConfiguration pumpConfiguration, ITankGaugeWorker tankGaugeWorker, [CanBeNull] IPosIntegratorIn<IMessageTracking, IPump> mobilePosInWorker,
            IPumpCollection allPumps, IOptCollection allOpts, IHydraTransactionFile transactionFile, IRetalixTransactionFile retalixTransactionFile,
            Func<Type, IWorkerable> resolveTypeInstance, IIntegratorFactories integratorFactories, IMessageBroker messageBroker, IServiceFilesHelper serviceFilesHelper, ICacheHelper cacheHelper) 
            : base(logManager, $"{ConfigConstants.LoggerPrefixOptService}Core", configurationManager, useXmlConfigurator: false)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _fileInfoVersionFactory = fileVersionInfoFactory ?? throw new ArgumentNullException(nameof(fileVersionInfoFactory));
            _cacheHelper = cacheHelper ?? throw new ArgumentException(nameof(cacheHelper));

            RegisterWorker(paymentConfig ?? throw new ArgumentNullException(nameof(paymentConfig)));
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));

            ControllerWorker = controllerWorker ?? throw new ArgumentNullException(nameof(controllerWorker));
            _configUpdateWorker = configUpdateWorker ?? throw new ArgumentNullException(nameof(configUpdateWorker));
            _updateWorker = updateWorker ?? throw new ArgumentNullException(nameof(updateWorker));
            _journalWorker = journalWorker ?? throw new ArgumentNullException(nameof(journalWorker));
            _localAccountWorker = localAccountWorker ?? throw new ArgumentNullException(nameof(localAccountWorker));
            _optWorker = fromOptWorker ?? throw new ArgumentNullException(nameof(fromOptWorker));

            _pumpWorker = pumpConfiguration ?? throw new ArgumentNullException(nameof(pumpConfiguration));
            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AllPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));
            AllOpts = allOpts ?? throw new ArgumentNullException(nameof(allOpts));

            _integratorFactories = integratorFactories ?? throw new ArgumentNullException(nameof(integratorFactories));
            RegisterWorker(messageBroker ?? throw new ArgumentNullException(nameof(messageBroker)));

            _transactionFile = transactionFile;
            _retalixTransactionFile = retalixTransactionFile;

            var result = _hydraDb.GetVersionInfo();
            if (result.IsSuccess)
            {
                var results = result.Value;
                DoDeferredLogging(LogLevel.Info, "Database.Migration.Status", () => new[] {Environment.NewLine, Environment.NewLine +
                    string.Join(Environment.NewLine, results.Select(x => "Db / Applied / Version / Description: " +
                    $"{x.DbName,6} / {x.AppliedOn:dd/MM/yyyy HH:mm:ss} / {x.Version:D8} / {x.Description}")) }, null);
            }

            AdvancedConfig siteInfo = null;
            try
            {
                siteInfo = _hydraDb.AdvancedConfig;
                Logger.SetLoggerProperty("SiteName", siteInfo.SiteName);
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { "Failed to set branch for logging" }, ex);
            }

            RegisterWorker(journalWorker);
            RegisterWorker(toOptWorker ?? throw new ArgumentNullException(nameof(toOptWorker)));
            RegisterWorker(heartbeatOptWorker ?? throw new ArgumentNullException(nameof(heartbeatOptWorker)));
            RegisterWorker(fromOptWorker);
            RegisterWorker(ControllerWorker);
            ControllerWorker?.RegisterWorker(this);
            RegisterWorker(_pumpWorker);
            RegisterWorker(secAuthInWorker ?? throw new ArgumentNullException(nameof(secAuthInWorker)));
            RegisterWorker(secAuthOutWorker);
            RegisterWorker(tankGaugeWorker ?? throw new ArgumentNullException(nameof(tankGaugeWorker)));
            RegisterWorker(configUpdateWorker);
            RegisterWorker(carWashWorker);
            RegisterWorker(mediaChannelWorker ?? throw new ArgumentNullException(nameof(mediaChannelWorker)));
            if (_domsWorker != null)
            {
                RegisterWorker(_domsWorker);
            }
            RegisterWorker(updateWorker);
            RegisterWorker(localAccountWorker);

            // Dynamically retrieved the runtime instance of a IHydraMobileWorker from a factory that is associated
            // with Mobile POS Integrator and is responsible for POS integration.
            RegisterWorker(mobilePosInWorker);

            if (serviceFilesHelper == null)
            {
                throw new ArgumentNullException(nameof(serviceFilesHelper));
            }

            DoDeferredLogging(LogLevel.Info, HeaderStatus, () => new[] { "OPT Service Initialised" });
            foreach (var info in serviceFilesHelper.Current)
            {
                DoDeferredLogging(LogLevel.Info, $"{nameof(FileVersionInfo)}.{info.Name}", () => new[] { $"{info.Version}; {info.Checksum}; {info.DateModified}" });
            }
            DoDeferredLogging(LogLevel.Info, "Device name", () => new[] { GetDeviceName() });
            DoDeferredLogging(LogLevel.Info, "Process", () => new[] { (Environment.Is64BitProcess ? "64 bit" : "32 bit") });
            DoDeferredLogging(LogLevel.Info, "OS", () => new[] { (Environment.Is64BitOperatingSystem ? "64 bit" : "32 bit") });
        }

        /// <inheritdoc />
        protected override Result DoStart(params object[] startParams)
        {
            var hydraId = startParams.First().ToString();

            // When debugging, allows dev time to Re-Attach, etc
            var sleepInterval = ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigConstants.ConfigKeyDebugStartDelayInterval, ConfigConstants.DefaultValueDebugStartDelayInterval, LoggerIConfigurationManager);
            Thread.Sleep((int)sleepInterval.GetTimeSpan().TotalMilliseconds);

            var siteInfo = _hydraDb.GetSiteInfo();
            if (siteInfo == null || string.IsNullOrWhiteSpace(siteInfo.SiteName) || string.IsNullOrWhiteSpace(siteInfo.VatNumber))
            {
                return Result.Failure("OPT Service failed to start - NO SiteInfo defined!");
            }

            var logRef = LoggingReference;

            _hydraDb.SetId(hydraId);
            AllOpts.SetHydraId(hydraId);
            AllPumps.AllocatePumps(_paymentConfig?.CheckGetAllPumpTids(logRef));

            var result = _pumpWorker.Start();
            if (!result.IsSuccess)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { result.Error });
            }

            RegisterWorker(_pumpWorker);

            var advConfig = _hydraDb.AdvancedConfig;

            HandleIntegrationConnection(IntegrationType.Pump, advConfig.PumpType, true, logRef, false);
            HandleIntegrationConnection(IntegrationType.Pos, $"{advConfig.PosType}", true, logRef, false);
            HandleIntegrationConnection(IntegrationType.MobilePos, advConfig.MobilePosType, true, logRef, false);
            HandleIntegrationConnection(IntegrationType.BackOffice, advConfig.BosType, true, logRef, false);
            HandleIntegrationConnection(IntegrationType.SecAuth, advConfig.SecAuthType, true, logRef, false);
            HandleIntegrationConnection(IntegrationType.PaymentConfig, advConfig.PaymentConfigType, true, logRef, false);

            Task.Run(() =>
            {
                DoActionOnWorkers<IWorkerable>((w) => w.Start(hydraId, _cancellationTokenSource), logRef);

                GetWorker<IPosIntegratorIn<IMessageTracking, IPump>>()?.RequestDefaultMode(logRef);
            });

            return Result.Success();
        }

        /// <inheritdoc />
        protected override Result DoStop()
        {
            _cancellationTokenSource.Cancel();

            GetWorker<IFromOptWorker>().WaitForOpts();

            DoActionOnWorkers<IConnectable>((cw) => cw.Stop(), LoggingReference);

            _domsWorker?.Stop();

            return Result.Success();
        }

        /// <inheritdoc cref="MiscHelper"/>
        public static string GetDeviceName() => MiscHelper.GetDeviceName();

        /// <inheritdoc />
        protected override void DoDisposeDisposing()
        {
            DoActionOnWorkers<IConnectable>((cw) => cw?.Dispose(), string.Empty);

            _domsWorker?.Dispose();

            _cancellationTokenSource?.Dispose();

            base.DoDisposeDisposing();
        }

        /// <inheritdoc />
        public Result Reload()
        {
            throw new NotImplementedException();
        }

        private bool IsDomsEnabled()
        {
            var info = _hydraDb.GetDomsInfo();
            var enabled = (info?.Detect ?? false) || (info?.Enabled ?? false);
            return enabled;
        }

        public bool IsPumpControllerEnabled()
        {
            return DoAction(() => GetWorker<IMessageBroker>()?.IsConnectableConnected<IPumpIntegratorInTransient<IMessageTracking>>() ?? Result.Failure("NotConnected"), string.Empty).IsSuccess;
        }

        private Result HandleConnection<TWorkerable>(bool openConnection, Func<TWorkerable> getInstance, string loggingReference, bool doOpen, Func<IMessageBroker, IControllerWorker, TWorkerable, IEnumerable<IWorkerable>> extraWorkers = null) where TWorkerable : IWorkerable
        {
            return DoAction(() =>
            {
                var broker = GetWorker<IMessageBroker>();
                var controller = GetWorker<IControllerWorker>();

                // Get instance
                var worker = getInstance.Invoke();
                if (worker == null)
                {
                    return Result.Success(); // This is allowed!
                }

                var result = Result.Success();
                var extraWorkersStop = extraWorkers?.Invoke(broker, controller, worker) ?? Enumerable.Empty<IWorkerable>();

                if (openConnection)
                {
                    worker.RegisterWorkers(broker, controller);
                    var allWorkers = extraWorkersStop.Concat(new IWorkerable[] { worker }).ToArray();
                    broker.RegisterWorkers(allWorkers);
                    controller.RegisterWorkers(allWorkers);
                    RegisterWorkers(allWorkers);
                    if (doOpen)
                    {
                        result = worker.Start(StartParameters?.FirstOrDefault().ToString(), _cancellationTokenSource);
                    }
                }
                else
                {
                    result = worker.Stop();
                    worker.DeregisterWorkers(broker, controller);
                    var allWorkers = extraWorkersStop.Concat(new IWorkerable[] { worker }).ToArray();
                    broker.DeregisterWorkers(allWorkers);
                    controller.DeregisterWorkers(allWorkers);
                    DeregisterWorkers(allWorkers);
                }

                return result;
            }, loggingReference);
        }

        private Result HandlePumpConnection(string typeValue, bool openConnection, string loggingReference, bool doOpen = true)
        {
            var result = HandleConnection<IPumpIntegratorConfiguration>(openConnection, () => _integratorFactories.PumpIntegratorInFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen);
            return !result.IsSuccess ? result : HandleConnection<ITankGaugeIntegratorInJournal>(openConnection, () => _integratorFactories.TankGaugeIntegratorInFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen);
        }

        private Result HandlePaymentConfigConnection(string typeValue, bool openConnection, string loggingReference, bool doOpen = true)
        {
            var broker = GetWorker<IMessageBroker>();
            var oldPaymentConfig = broker.GetWorker<IPaymentConfigIntegrator>();
            if (oldPaymentConfig != null)
            {
                DeregisterWorker(oldPaymentConfig);
                broker.GetWorker<IConfigUpdateWorker>()?.DeregisterWorker(oldPaymentConfig);
                broker.GetWorker<IFromOptWorker>()?.DeregisterWorker(oldPaymentConfig);
            }

            var result = HandleConnection(openConnection, () => _integratorFactories.PaymentConfigIntegratorOutFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen);
            if (result.IsSuccess)
            {
                var paymentConfig = broker.GetWorker<IPaymentConfigIntegrator>();
                if (paymentConfig != null)
                {
                    RegisterWorker(paymentConfig);
                    broker.GetWorker<IConfigUpdateWorker>()?.RegisterWorker(paymentConfig);
                    broker.GetWorker<IFromOptWorker>()?.RegisterWorker(paymentConfig);
                }
            }

            GetWorker<IControllerWorker>()?.SetEsocketChanged();
            return result;
        }

        /// <inheritdoc cref="ICore"/>     
        public Result HandleIntegrationConnection(IntegrationType integrationType, string typeValue, bool openConnection, string loggingReference, bool doOpen = true)
        {
            var result = integrationType switch
            {
                IntegrationType.Pump => HandlePumpConnection(typeValue, openConnection, loggingReference, doOpen),
                IntegrationType.MobilePos => HandleIntegrationConnection_MobilePos(typeValue, openConnection, loggingReference, doOpen),
                IntegrationType.BackOffice => HandleConnection(openConnection, () => _integratorFactories.BosIntegratorOutFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen),
                IntegrationType.SecAuth => HandleConnection(openConnection, () => _integratorFactories.SecAuthIntegratorOutFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen),
                IntegrationType.Pos => HandleIntegrationConnection_Pos(typeValue, openConnection, loggingReference, doOpen),
                IntegrationType.PaymentConfig => HandlePaymentConfigConnection(typeValue, openConnection, loggingReference, doOpen),
                _ => Result.Failure($"Un-processed IntegrationType: {integrationType}")
            };

            return result;
        }

        private Result HandleIntegrationConnection_Pos(string typeValue, bool openConnection, string loggingReference, bool doOpen = true)
        {
            var resultOut = HandleConnection(openConnection, () => _integratorFactories.PosIntegratorOutFactory.GetInstance(loggingReference, $"{typeValue}"), loggingReference, doOpen,
                (broker, controller, worker) =>
                {
                    var hpWorker = worker.GetWorker<IHydraPosWorker>();
                    return hpWorker == null ? null : new[] { hpWorker };
                });

            var resultIn = HandleConnection(openConnection, () => _integratorFactories.PosIntegratorInFactory.GetInstance(loggingReference, $"{typeValue}"), loggingReference, doOpen);

            var resultInModeChange = HandleConnection(openConnection, () => _integratorFactories.PosInModeFactory.GetInstance(loggingReference, $"{typeValue}"), loggingReference, doOpen);

            return Result.Combine(resultOut, resultIn, resultInModeChange);
        }

        private Result HandleIntegrationConnection_MobilePos(string typeValue, bool openConnection, string loggingReference, bool doOpen = true)
        {
            var result = HandleConnection(openConnection, () => _integratorFactories.MobilePosIntegratorInFactory.GetInstance(loggingReference, typeValue), loggingReference, doOpen);

            if (typeValue == coreConfig.Constants.Integrator.PosMobileTypeHydraMobile)
            {
                var mobOpt = AllOpts.GetOptForIdString<IOptCore>("HMOB", (optId, mode) => new MobileOpt(LogManager, optId, _hydraDb.Id, ConfigurationManager));
            }

            return result;
        }
    }
}
