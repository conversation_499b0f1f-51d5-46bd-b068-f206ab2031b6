USE [Hydra]

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable, Rollback...Begin';

PRINT N'Updating Procedure [dbo].[GetConfigurationTypes]...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetConfigurationTypes') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetConfigurationTypes
END
GO

CREATE PROCEDURE GetConfigurationTypes AS
BEGIN 
	SELECT [Id], [Type]
	FROM ConfigurationType

	SELECT [Id], [Category]
	FROM ConfigurationCategory

	SELECT [Id], [TypeId], [Description]
	FROM ConfigurationHeader
END

GO

PRINT N'HOPT-1377 - OPT Service - add ConfigigurationCategory.IsEditable, Rollback...End';
PRINT N'';

GO