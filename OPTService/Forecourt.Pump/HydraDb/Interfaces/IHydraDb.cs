using CSharpFunctionalExtensions;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Pump.Models.Doms;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using System.Collections.Generic;
using System.Net;
using corePumpDelivered = Forecourt.Core.HydraDb.Models.PumpDelivered;

namespace Forecourt.Pump.HydraDb.Interfaces
{
    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pump, split down by area
    /// </summary>
    public interface IHydraDb:
        Core.HydraDb.Interfaces.IHydraDb, 
        IHydraDbPump, 
        IHydraDbDoms, 
        IHydraDbFuelling,
        IHydraDbTankGauge,
        IHydraDbPaymentTimeouts,
        IHydraDbTransaction,
        IHydraDbPumpPrices
    {
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pump, core functionality
    /// </summary>
    public interface IHydraDbPump
    {
        /// <summary>Store kiosk only mode for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="setDefault">True if also setting default mode, false if only setting current mode.</param>
        /// <param name="message">Current message tracking instance</param>
        void SetKioskOnly(byte number, bool setDefault, IMessageTracking message = null);

        /// <summary>Store mixed mode for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="setDefault">True if also setting default mode, false if only setting current mode.</param>
        /// <param name="message">Current message tracking instance</param>
        void SetMixed(byte number, bool setDefault, IMessageTracking message = null);

        /// <summary>Store outside only mode for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="setDefault">True if also setting default mode, false if only setting current mode.</param>
        /// <param name="message">Current message tracking instance</param>
        void SetOutsideOnly(byte number, bool setDefault, IMessageTracking message = null);

        /// <summary>Store pump max fill override for fuel cards flag for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="flag">True if overridden, false if not.</param>
        void SetPumpMaxFillOverrideForFuelCards(byte number, bool flag);

        /// <summary>Store pump max fill override for payment cards flag for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="flag">True if overridden, false if not.</param>
        void SetPumpMaxFillOverrideForPaymentCards(byte number, bool flag);

        /// <summary>Store pump closed state for the given pump in the database.</summary>
        /// <param name="number">Pump number.</param>
        /// <param name="closed">True if closed, false if open.</param>
        void SetPumpClosed(byte number, bool closed);

        /// <summary>Fetch the set of Pump TIDs from the database.</summary>
        /// <param name="allTids">List of tids</param>
        /// <returns>The TIDs.</returns>
        IList<PumpTid> FetchAllPumps(IEnumerable<string> allTids = null);

        ///// <summary>Store mapping from pump to OPT in the database.</summary>
        ///// <param name="number">Pump number.</param>
        ///// <param name="opt">OPT ID.</param>
        void MapOpt(byte number, string opt);

        ///// <summary>Store mapping from pump to TID in the database.</summary>
        ///// <param name="number">Pump number.</param>
        ///// <param name="tid">TID.</param>
        void MapTid(byte number, string tid);

        PosClaim GetPosClaim();

        void SetPosClaimNumber(byte number);

        void SetMaxFillOverride(uint maxFillOverride);

        /// <summary>Store End Point for HSC in the database.</summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="port">Port number to set.</param>
        void SetPumpEndPoint(IPAddress ip, int port);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pump, DOMS functionality
    /// </summary>

    public interface IHydraDbDoms
    {
        DomsInfo GetDomsInfo();
        void SetDomsEnabled(bool enabled);
        void SetDomsDetect(bool detect);
        void SetDomsIpAddress(IPAddress ip);
        void SetDomsLoginString(string loginString);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pump, Fuelling functionality
    /// </summary>

    public interface IHydraDbFuelling
    {
        FuellingInfo GetFuelling();
        void SetFuellingIndefiniteWait(bool flag);
        void SetFuellingWaitMinutes(int minutes);
        void SetFuellingBackoffAuth(int backoff);
        void SetFuellingBackoffPreAuth(int backoff);
        void SetFuellingBackoffStopStart(int backoff);
        void SetFuellingBackoffStopOnly(int backoff);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Pump, TankGauge functionality
    /// </summary>
    public interface IHydraDbTankGauge
    {
        /// <summary>
        /// Fetch the Tank Gauge end point from the database.
        /// </summary>
        /// <returns>The Tank Gauge end point.</returns>
        GenericEndPoint FetchTankGaugeEndPoint();

        /// <summary>
        /// Store End Point for Tank Gauge in the database.
        /// </summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="port">Port number to set.</param>
        void SetTankGaugeEndPoint(IPAddress ip, int port);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Payment Timeouts
    /// </summary>
    public interface IHydraDbPaymentTimeouts
    {
        /// <summary>
        /// Fetch all Payment timeouts from the database.
        /// </summary>
        /// <returns>List of payment timeouts</returns>
        IEnumerable<PaymentTimeout> FetchPaymentTimeouts();

        /// <summary>
        /// Fetch the payment timeout for the given mode from the database.
        /// </summary>
        /// <param name="mode">Mode for which to fetch the timeout.</param>
        /// <returns>The timeout.</returns>
        int FetchPaymentTimeout(PaymentTimeoutType mode);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Transactions
    /// </summary>
    public interface IHydraDbTransaction
    {
        /// <summary>
        /// Get delivery notification for pump.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="reference">The current logging reference</param>
        Result<corePumpDelivered> GetDeliveredInfo(byte pump, string reference = null);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Fuel Prices
    /// </summary>
    public interface IHydraDbPumpPrices
    {
        /// <summary>
        /// Get all Pump/Grade/Hose/Price information
        /// </summary>
        /// <param name="message">The current message</param>
        /// <returns>Result wrapped list of <see cref="PumpGradePriceInfo"/></returns>
        Result<IEnumerable<PumpGradePriceInfo>> GetPumpGradePriceInfos(IMessageTracking message = null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pump">The pump number</param>
        /// <param name="grade">The grade number</param>
        /// <param name="hose">The hose number</param>
        /// <param name="price">Current price per unit</param>
        /// <param name="message">The current message</param>
        /// <returns>Result</returns>
        Result UpsertPumpGradePriceInfo(byte pump, byte grade, byte hose, ushort price, IMessageTracking message = null);
    }
}