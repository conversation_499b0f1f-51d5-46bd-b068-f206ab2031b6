using Forecourt.Bos.HydraDb.Models;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models
{
    /// <summary>
    /// Model that contains all data relating to booking a transaction into an external system
    /// </summary>
    public class BookTransactionModel : IOrchestratedCommand
    {
        /// <summary>
        /// Current Orchestration Step namme
        /// </summary>
        public string StepName { get; set; }

        /// <summary>
        /// Current state of the transaction booking
        /// </summary>
        public TransactionBookingState BookingState { get; set; }

        /// <summary>
        /// Source transaction item details
        /// </summary>
        public SendTransactionItem Item { get; set; }

        /// <summary>
        /// Http client instance for access Config API
        /// </summary>
        public IHttpClientWrapper ConfigHttpClient { get; set; }

        /// <summary>
        /// Http client instance for access BOS API
        /// </summary>
        public IHttpClientWrapper BosHttpClient { get; set; }

        /// <summary>
        /// Receipt lines.
        /// </summary>
        public List<string> ReceiptLines { get; set; }

        /// <summary>
        /// Configuration data mapping
        /// </summary>
        public IOrchestratedCommand ConfigDataMap { get; set; }

        /// <summary>
        /// Current message tracking
        /// </summary>
        public IMessageTracking MessageTracking { get; set; }

        /// <summary>
        /// Current logging reference
        /// </summary>
        public string LoggingReference => MessageTracking.IdAsString;

        /// <inheritdoc cref="IOrchestratedCommand.CorrelationId"/>
        string IOrchestratedCommand.CorrelationId
        {
            get { return MessageTracking.IdAsString; }
            set { MessageTracking.IdAsString = value; }
        }

        /// <summary>
        /// Indicates whether the API is currently available
        /// </summary>
        public bool IsApiAvailable { get; set; }

        /// <summary>
        /// Internal booking identifier
        /// </summary>
        public long BookingId { get; set; }

        /// <summary>
        /// External transaction identifier
        /// </summary>
        public int ExternalTransactionId { get; set; }
        
        /// <summary>
        /// Shift identifier
        /// </summary>
        public int ShiftId { get; set; }

        /// <summary>
        /// Period identifier
        /// </summary>
        public int PeriodId { get; set; }

        /// <summary>
        /// Business date to book the transaction against
        /// </summary>
        public string BusinessDate { get; set; }

        /// <summary>
        /// Current booking retry count
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Booking content, SaleTransaction as Json string
        /// </summary>
        public string BookingContent { get; set; }
    }
}
