using Htec.Foundation.Connections.Workers.Interfaces;

namespace Forecourt.Pos.Workers.Interfaces
{
    public interface IThirdPartyPosWorker : IListenerWorker<string>
    {
        /// <summary>
        /// Send Auth Request to Third Party POS.
        /// </summary>
        /// <param name="pump">Pump number to be authorised.</param>
        /// <param name="amount">Amount to be authorised.</param>
        /// <param name="loggingReference">The logging reference</param>
        void SendAuthRequestToPos(byte pump, uint amount, string loggingReference = null);

        /// <summary>
        /// Send Clear Request to Third Party POS.
        /// </summary>
        /// <param name="pump">Pump number to be cleared.</param>
        /// <param name="loggingReference">The logging reference</param>
        void SendClearRequestToPos(byte pump, string loggingReference = null);
    }
}