import { Component, NgZone, OnInit } from '@angular/core';
import { forkJoin, Observable, of, Subscription } from 'rxjs';
import { Opt } from 'src/app/core/models/opt.model';
import { OptService } from 'src/app/services/opt.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { NGXLogger } from 'ngx-logger';
import { PumpService } from 'src/app/services/pump.service';
import { LoadingService } from 'src/app/services/loading.service';
import { catchError } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { PersistentDataService } from 'src/app/services/persistent-data.service';
import { SignalREventType } from 'src/app/core/enums/signalREventType';
import { ButtonHelper } from 'src/app/helpers/button.helper';

@Component({
  selector: 'app-opts',
  templateUrl: './opts.component.html',
  styleUrls: ['./opts.component.css']
})

/**
 * The OPTs component class
 */
export class OptsComponent implements OnInit {

  showContent: boolean = false;
  optsData: Array<Opt> = [];
  signalRData: Subscription | undefined;
  TidDataSubs: Subscription | undefined;
  orphanPumps: any;
  tids: any;
  optCardStates = new Map<string, string>();
  maxFillOverride: any;
  pumpCardStates = new Map<string, Map<string, string>>();
  
  /**
   * The OPTs component constructor.
   * @param signalRService The signalR service.
   * @param zone The angular zone service used to keep screen updated.
   * @param optService The OPT service.
   * @param pumpService The Pump service.
   * @param logger The logger.
   */
  constructor(
    private signalRService: SignalRService,
    private zone: NgZone,
    private optService: OptService,
    private pumpService: PumpService,
    private logger: NGXLogger,
    private loadingService: LoadingService,
    private persistentDataService: PersistentDataService
  ) {
    this.signalRData = this.signalRService.getOptSignalRMessage().subscribe(optStringId => {
      this.zone.run(() => {
        this.setOptCardStates();
        this.refreshData(optStringId, false);
      });
    });
  }

  /**
   * ngOnInit angular hook.
   */
  ngOnInit(): void {
    setTimeout(()=>{this.refreshData('');}, 100)
  }

  /**
   * ngOnDestroy angular hook.
   */
  ngOnDestroy(): void {
    this.signalRData?.unsubscribe();
  }

  /**
   * The refresh data method called when received a push from signalR
   * @param optStringId The signalR pushed OPT idString.
   */
  refreshData(optStringId: string = '', showLoading: boolean = true): void {
    //Show loading screen
    if(showLoading){
      this.showContent = false;
      this.loadingService.showLoadingScreen();
    }
    
    this.refreshTids();
    this.refreshMaxFillOverride();

    let observables : Observable<any>[] = [];   

    let opts = this.optService.getOpts(optStringId).pipe(
      catchError((err:HttpErrorResponse) => {
        this.logger.error('Error getting OPTs: ', err);
        if(err.status === 404){
          if(optStringId){
            this.optsData.forEach(element => {
              if (element.StringId === optStringId) {
                this.optsData.splice(this.optsData.indexOf(element),1);
              }
            });
          }
        }
        this.loadingService.errorDuringLoading();
        return of(undefined);
      })
    );

    observables.push(opts);
    if(!optStringId){
      let pumps =  this.pumpService.getPumps().pipe(
        catchError(err => {
          this.logger.error('Error getting orphan pumps: ', err);
          this.loadingService.errorDuringLoading();
          return of(undefined);
        })
      );
      observables.push(pumps)
    }

    forkJoin(observables).subscribe(
      results => {
        //pumps result. results[1]
        if(results[1]!== undefined){
          if (!optStringId) {
            this.orphanPumps = results[1].filter((x: { OptStringId: any; }) => x.OptStringId === '' || x.OptStringId === null || x.OptStringId === undefined);
            this.logger.debug('Orphan pumps: ', this.orphanPumps);
          } 
        }                   

        //opts result. results[0]
        if(results[0]!== undefined){
          if (optStringId) {
            this.logger.debug(`Refreshing single OPT ${optStringId}`, results[0]);
            
              if (results[0].length === 1) {
                // Assuming there is only 1 element coming back from the API at this point
                this.optsData.forEach(element => {
                  if (element.StringId === optStringId) {
                    this.optsData[this.optsData.indexOf(element)] = results[0][0];
                  }
                });
              } else {
                // In this case, something unexpected has happened. So refreshing all.
                this.logger.debug('Unexpected number of OPTs returned by the API. Refresing all of them', results[0]);
                this.refreshData('', false);
              }     
          } else {
            this.logger.debug('Refreshing all OPTs data', results[0]);
            this.optsData = results[0];
          }
        }        
      },
      err => {},
      () => {
        if(showLoading){
          this.loadingService.hideLoadingScreen();
          this.showContent = true;
        }
      }
    );
  }

  /**
   * Resfreshes the tid list from the relevant persistent data service.
   */
  refreshTids() {
    this.tids = this.persistentDataService.getTids();
  }

  /**
   * Resfreshes the MaxFillOverride from the relevant persistent data service.
   */
   refreshMaxFillOverride() {
    this.maxFillOverride = this.persistentDataService.getMaxFillOverride();
  }

  /**
   * Restarts an OPT.
   */
  restartOpt(opt: Opt, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Restarting OPT', opt);
    this.optService.restartOpt(opt).subscribe(() => {
      this.logger.debug(`OPT ${opt.StringId} restart sent OK`);
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error(`Problem restarting OPT ${opt.StringId}`, error);
      ButtonHelper.reset(event);
    });
  }

  /*
   * Refresh the configuration information of a given OPT
   */
  refreshOpt(opt: Opt, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Refreshing OPT', opt);
    this.optService.refreshOpt(opt.StringId).subscribe(() => {
      this.logger.debug(`OPT ${opt.StringId} refresh sent OK`);
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error(`Problem refreshing OPT ${opt.StringId}`, error);
      ButtonHelper.reset(event);
    });
  }

   /*
   * Request the Logs of a given OPT
   */
  requestOptLog(opt: Opt, event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Requesting logs for OPT', opt);
    this.optService.requestOptLog(opt.StringId).subscribe(() => {
      this.logger.debug(`OPT ${opt.StringId} request logs sent OK`);
      ButtonHelper.reset(event);
    }, error => {
      this.logger.error(`Problem requesting logs for OPT ${opt.StringId}`, error);
      ButtonHelper.reset(event);
    });
  }

  /**
   * Sets the current array of opt card states so it can be restored afterwards.
   */
  setOptCardStates() {
    const cards = $("[id^=collapse-opt-body]").toArray();
    cards.forEach(card => {
      this.optCardStates.set(card.id, card.className);
    });
  }

  /**
   * Gets the state of the opt card so the according class can be set.
   * @param id The opt card identifier.
   */
  getOptCardState(id: string) {
    return this.optCardStates.get(id) ?? 'collapse';
  }

  updatePumpCardStates(opt: Opt, data: Map<string,string>){
    this.pumpCardStates.set(opt.StringId, data);
  }

  getPumpCardStates(opt){
    return this.pumpCardStates.get(opt.StringId);
  }

}
