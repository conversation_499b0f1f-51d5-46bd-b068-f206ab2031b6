using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Messages;
using System;

namespace Forecourt.Service.OPTServiceClasses
{
    public class OnPumpStateChange
    {
        public OnPumpStateChange()
        {
            Pump = 1;
            State = DispenserState.Idle;
            PreviousState = DispenserState.Idle;
            Hose = 1;
            Grade = 1;
            TransSeqNum = 1;
            IsPaid = true;
            IsOptAvailable = true;

            IsOptInControl = false;
            IsError = false;
            IsTimer = false;
            IsPendingTxnOnReboot = false;
            IsBlocked = false;
        }

        public byte Pump { get; set; }
        public DispenserState PreviousState { get; set; }
        public DispenserState State { get; set; }
        public byte Hose { get; set; }
        public byte Grade { get; set; }
        //public IEnumerable<byte> AllGrades { get; set; }
        public decimal Volume { get; set; }
        public decimal Amount { get; set; }
        //public ushort Ppu { get; set; }
        public int TransSeqNum { get; set; }
        public DateTime TimeStamp { get; set; }

        public byte PosClaim { get; set; }
        public bool IsPaid { get; set; }
        public TransactionPaymentType TransactionPaymentType { get; set; }

        public bool IsTimer { get; set; }
        //public bool WasBlocked { get; set; }
        public bool IsBlocked { get; set; }
        public bool IsOptAvailable { get; set; }
        public bool IsOptInControl { get; set; }
        public bool IsPendingTxnOnReboot { get; set; }
        public bool IsError { get; set; }
    }
}
