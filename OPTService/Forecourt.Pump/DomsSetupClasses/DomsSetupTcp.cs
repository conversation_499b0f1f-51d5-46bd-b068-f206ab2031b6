using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Logger.Interfaces;

namespace Forecourt.Pump.DomsSetupClasses
{
    [HasConfiguration]
    public class DomsSetupTcp : DomsSetup
    {
        public DomsSetupTcp(IPumpControllerCallbacks callbacks, byte posOpt, byte posId1, byte posId2, byte posId4, byte posId5, IHtecLogManager logManager, bool update, IConfigurationManager configurationManager,
            IPumpCollection allPumps, ITimerFactory timerFactory = null) : 
            base(callbacks, posOpt, posId1, posId2, posId4, posId5, logManager, update, configurationManager, allPumps, true, timerFactory, "00:00:00.500")
        {
        }
    }
}