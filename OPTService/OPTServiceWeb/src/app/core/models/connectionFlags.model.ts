export class ConnectionFlags {
  static readonly CATEGORY: string = 'CONNECTIVITY';
  static readonly SETTING_PREFIX: string = 'ConnectionThread:Execute:Active:';

  AnprConnectionThread: boolean = false;
  CarWashConnectionThread: boolean = false;
  FromOptConnectionThread: boolean = false;
  HydraMobileConnectionThread: boolean = false;
  HydraPosConnectionThread: boolean = false;
  MediaChannelConnectionThread: boolean = false;
  OptHeartbeatConnectionThread: boolean = false;
  RetalixConnectionThread: boolean = false;
  TankGaugeConnectionThread: boolean = false;
  ThirdPartyPosConnectionThread: boolean = false;
  ToOptConnectionThread: boolean = false;
  PaymentConfigWorker: boolean = true;
}
