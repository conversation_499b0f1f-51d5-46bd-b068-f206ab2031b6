using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.ConnectionThreads;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Logger.Interfaces;
using System.IO.Abstractions;
using ConnectionsConstants = Htec.Foundation.Connections.Common.Constants;

namespace OPT.Common.ConnectionThreads
{
    [HasConfiguration]
    public class MediaChannelConnectionThread : MediaMessageListenerConnectionThread
    {
        public MediaChannelConnectionThread(IHtecLogManager logMan, ISocketWrapperFactory socketWrapperFactory, IFileInfoFactory fileInfoFactory,
            IConfigurationManager configurationManager, ITelemetryWorker telemetryWorker,
            byte lengthSize = ConnectionsConstants.DefaultLengthSize, bool logRxTx = true, MessageLengthFlowType messageLengthFlow = MessageLengthFlowType.Both)
            : base(logMan, "MediaChannel", socketWrapperFactory, fileInfoFactory, configurationManager, telemetryWorker, lengthSize, logRxTx, messageLengthFlow)
        {
        }
    }
}
