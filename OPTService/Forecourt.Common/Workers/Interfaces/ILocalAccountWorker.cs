using CSharpFunctionalExtensions;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using OPT.Common.HydraDbClasses;
using System.Collections.Generic;

namespace OPT.Common.Workers.Interfaces
{
    public interface ILocalAccountWorker: IConnectable
    {
        ICollection<LocalAccountCustomer> Customers { get; }
     
        void Add
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, string loggingReference = null, bool sendPushChange = true);

        void Delete(string customerReference, string loggingReference = null, bool sendPushChange = true);
        void Stop(string customerReference, string loggingReference = null, bool sendPushChange = true);
        void Go(string customerReference, string loggingReference = null, bool sendPushChange = true);
        void AddCardWithoutRestrictions(string pan, string customerReference, string description, float discount, string loggingReference = null, bool sendPushChange = true);

        void AddCardWithRestrictions
        (string pan, string customerReference, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods, string loggingReference = null, bool sendPushChange = true);

        void DeleteCard(string pan, string loggingReference = null, bool sendPushChange = true);
        void HotCard(string pan, string loggingReference = null, bool sendPushChange = true);
        void OkCard(string pan, string loggingReference = null, bool sendPushChange = true);
        void Balance(string customerReference, uint value, string loggingReference = null, bool sendPushChange = true);
        void TopUp(string customerReference, uint value, string loggingReference = null, bool sendPushChange = true);
        void SetFlags(string customerReference, bool pin, bool printValue, bool allowLoyalty, string loggingReference = null);
        Result<LocalAccountBalance> GetBalance(string cardNumber, string loggingReference = null);
        void ReduceBalance(string cardNumber, uint amount, string loggingReference = null);
    }
}