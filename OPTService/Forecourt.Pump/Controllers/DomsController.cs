using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.DomsSetupClasses;
using Forecourt.Pump.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using OPT.Common;
using OPT.Common.Helpers.Interfaces;
using PSS_Forecourt_Lib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Timers;
using coreGrade = Htec.Hydra.Core.Pump.Messages.Grade;
using domsForecourt = PSS_Forecourt_Lib.Forecourt;

namespace Forecourt.Pump.Controllers
{
    /// <summary>
    /// Abstracts the pump commands issued to DOMS (via PSS) away from PumpWorker
    /// </summary>
    public class DomsController : HydraDbable<HydraDb.Interfaces.IHydraDb>, IDomsController
    {
        public class FetchPumpStateInfo
        {
            public FetchPumpStateInfo(bool isPending = true, bool isProcessing = false)
            {
                IsPending = isPending;
                IsProcessing = isProcessing;
            }

            public bool IsPending { get; set; }
            public bool IsProcessing { get; set; }
        }

        private readonly IFileVersionInfoHelper _fileInfoVersionHelper;
        private readonly ICacheHelper _cacheHelper;

        /// <summary>
        /// Constant for the DOMS PSS POS Dll
        /// </summary>
        public const string PssPosDll = "PSS_Forecourt_Lib.dll";

        /// <summary>
        /// Constant for the DOMS PSS TCP Dll
        /// </summary>
        public const string PssTcpDll = "PSS_TcpIp_Lib.dll";

        private const string PosVersionId = "*******";
        private const byte DefaultPosIdForFetched = 22;
        private const byte DefaultPosIdForState = 23;
        private const byte DefaultPosIdForTcp1 = 24;
        private const byte DefaultPosIdForTcp2 = 25;
        private const byte DefaultPosIdForTcp4 = 28;
        private const byte DefaultPosIdForTcp5 = 26;
        private const byte DefaultPosIdForTcpPrepared = 27;
        private const int ClearedTimeoutInSeconds = 10;
        private byte _logonPos;

        private readonly ConcurrentDictionary<int, FetchPumpStateInfo> _fetchPumpState = new ConcurrentDictionary<int, FetchPumpStateInfo>();

        /// <summary>
        /// Config key, for the fetch data max re-tries
        /// </summary>
        public const string ConfigKeyMaxFetchTries = Constants.ConfigKeyCategoryConnectivity + "Pump:FetchData:MaxAttempts:DOMS";

        /// <summary>
        /// Default value, for the fetch data max re-tries
        /// </summary>
        public const byte DefaultValueMaxFetchTries = 5;

        /// <summary>
        /// Configuration value for, the fetch data max re-tries
        /// </summary>
        protected ConfigurableInt MaxFetchTries { get; private set; }

        private const uint CountryCode = 44;

        private const string LightGreen = "33CC00";
        private const string Black = "000000";
        private const string Yellow = "FFFF00";
        private const string Red = "FF0000";
        private const string Pink = "FF99CC";
        private const string DarkGreen = "006600";
        private const string Cyan = "00FFFF";
        private const string Purple = "9999FF";

        public bool StateEnabled { get; private set; } = false;

        public bool StateConnected { get; private set; } = false;

        private bool _fcInit = false;

        public bool Enabled => StateEnabled || (FetchedSetup?.Enabled ?? false) || (TcpSetup?.Enabled ?? false) || (TcpPreparedSetup?.Enabled ?? false);

        private bool Reload => _stateReload || (FetchedSetup?.Reload ?? false) || (TcpSetup?.Reload ?? false) || (TcpPreparedSetup?.Reload ?? false);

        private bool AllConnected => (StateConnected || !StateEnabled) &&
            ((FetchedSetup?.Connected ?? false) || !(FetchedSetup?.Enabled ?? false)) &&
            ((TcpSetup?.Connected ?? false) || !(TcpSetup?.Enabled ?? false)) &&
            ((TcpPreparedSetup?.Connected ?? false) || !(TcpPreparedSetup?.Enabled ?? false));

        public bool Connected => StateConnected || (FetchedSetup?.Connected ?? false) || (TcpSetup?.Connected ?? false) || (TcpPreparedSetup?.Connected ?? false);

        public string LogonInfo { get; private set; }

        public IDomsSetup FetchedSetup { get; private set; }
        public IDomsSetup TcpSetup { get; private set; }
        public IDomsSetup TcpPreparedSetup { get; private set; }

        public DomsState State { get; private set; } = new DomsState();

        private bool _stateReload = false;

        private string _softwareVersion;
        private string _softwareDate;

        private ForecourtClass _fc;
        private domsForecourt Forecourt => _fc;
        private IFCConfig Config => _fc;
        private bool _fetchingState = false;
        private readonly object _stateLockObject = new();
        private FetchFcRequestType _fcRequestType = FetchFcRequestType.All;
        private FetchFpRequestType _fpRequestType = FetchFpRequestType.None;
        private int _fetchTries = 0;

        private bool _pendingMeters = false;
        private bool _pendingDips = false;
        //private readonly IList<MeterReadings> _meters = new List<MeterReadings>();
        private readonly IList<Dip> _dips = new List<Dip>();
        private volatile bool _masterReset;

        private bool _loginRunning = false;
        private readonly object _loginLockObject = new();
        private DateTime _loginWait = DateTime.Now;
        private int _loginFails = 0;
        private byte _posOpt;
        private ITimerFactory _timerFactory;

        /// <inheritdoc/>
        public DomsController(IHtecLogManager logManager, IConfigurationManager configurationManager, HydraDb.Interfaces.IHydraDb hydraDb,
            IFileVersionInfoHelper fileVersionInfoHelper, ICacheHelper cacheHelper, IPumpCollection allPumps, byte logonPos, ITimerFactory timerFactory = null) :
            base(hydraDb, logManager, nameof(DomsController), allPumps, configurationManager, timerFactory: timerFactory)
        {
            _fileInfoVersionHelper = fileVersionInfoHelper ?? throw new ArgumentNullException(nameof(fileVersionInfoHelper));
            _cacheHelper = cacheHelper ?? throw new ArgumentNullException(nameof(cacheHelper));
            _timerFactory = timerFactory ?? throw new ArgumentNullException(nameof(timerFactory));

            hydraDb.SetDomsEnabled(true);
            hydraDb.SetDomsDetect(false);

            var info = hydraDb.GetDomsInfo();
            LogonInfo = info?.LoginString ?? string.Empty;

            _logonPos = logonPos;

            MaxFetchTries = new ConfigurableInt(this, ConfigKeyMaxFetchTries, DefaultValueMaxFetchTries);
            ConfigValueDOMSServiceModeKioskOnly = new ConfigurableInt(this, DomsSetup.ConfigKeyDOMSServiceModeKioskOnly, DomsSetup.DefaultValueDOMSServiceModeKioskOnly);
            ConfigValueDOMSServiceModeKioskUse = new ConfigurableInt(this, DomsSetup.ConfigKeyDOMSServiceModeKioskUse, DomsSetup.DefaultValueDOMSServiceModeKioskUse);
            ConfigValueDOMSServiceModeOptOnly = new ConfigurableInt(this, DomsSetup.ConfigKeyDOMSServiceModeOptOnly, DomsSetup.DefaultValueDOMSServiceModeOptOnly);
            ConfigValueDOMSServiceModeMixed = new ConfigurableInt(this, DomsSetup.ConfigKeyDOMSServiceModeMixed, DomsSetup.DefaultValueDOMSServiceModeMixed);
        }

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            if (_fc != null)
            {
                return base.DoStart(startParams);
            }

            try
            {
                _fc = new ForecourtClass();
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { comEx.Message }, comEx);
                return Result.Failure("PSS Classes are not COM Registered");
            }
            catch (Exception ex)
            {
                return Result.Failure(ex.Message);
            }

            _posOpt = startParams.OfType<byte>().ToList().FirstOrDefault();

            var callbackWorker = GetWorker<IPumpControllerCallbacks>();
            if (callbackWorker == null)
            {
                return Result.Failure("No callback instance defined");
            }

            FetchedSetup = new DomsSetupPosProtocol(callbackWorker, _posOpt, DefaultPosIdForFetched, LogManager, true, ConfigurationManager, AllPumps);
            TcpSetup = new DomsSetupTcp(callbackWorker, _posOpt, DefaultPosIdForTcp1, DefaultPosIdForTcp2, DefaultPosIdForTcp4, DefaultPosIdForTcp5, LogManager, true, ConfigurationManager, AllPumps, _timerFactory);
            TcpSetup.Start(startParams);
            TcpPreparedSetup = new DomsSetupTcp(callbackWorker, _posOpt, DefaultPosIdForTcpPrepared, DefaultPosIdForTcpPrepared, DefaultPosIdForTcpPrepared, DefaultPosIdForTcpPrepared, LogManager, false, ConfigurationManager, AllPumps);
            SetEnabled(true, true);

            return base.DoStart(startParams);
        }

        protected override Result DoStop()
        {
            TcpSetup.Stop();
            DisconnectDoms();

            return base.DoStop();
        }

        /// <inheritdoc/>
        string IPumpControllerConfiguration.LogHeader => "DOMS";

        /// <inheritdoc/>
        public bool GotMaxPumps => TcpSetup?.GotMaxPumps ?? false;

        /// <inheritdoc/>
        public byte NumberOfPumps => TcpSetup?.NumberOfPumps ?? 0;

        /// <inheritdoc/>
        public GenericEndPoint CurrentEndPoint
        {
            get
            {
                var info = HydraDb.GetDomsInfo();
                return new GenericEndPoint(info?.IpAddress, 0);
            }
        }

        public string LogHeader => "DOMS";

        private void DoControllerAction(Action action, string loggingReference)
        {
            DoAction(() =>
            {
                if (TcpSetup.Enabled && TcpSetup.Connected)
                {
                    action();
                }
            }, loggingReference);
        }

        private Result DoControllerAction(Func<Result> action, string loggingReference)
        {
            return DoAction(() =>
            {
                if (TcpSetup != null && TcpSetup.Enabled && TcpSetup.Connected)
                {
                    return action();
                }
                return Result.Failure(ConfigConstants.NotConnected);
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void OpenPump(byte fpId, string loggingReference)
        {
            DoControllerAction(() => { TcpSetup.OpenPump(fpId); }, loggingReference);
        }

        /// <inheritdoc/>
        public void ClosePump(byte fpId, string loggingReference)
        {
            DoControllerAction(() => { TcpSetup.ClosePump(fpId); }, loggingReference);
        }

        /// <inheritdoc/>
        public Result AuthWithLimit(byte fpId, uint limit, byte pos, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                return Result.SuccessIf(TcpSetup.Authorise(fpId, limit, pos) || TcpSetup.Pump(fpId).IsAuthorised, $"Could not Auth, FpId: {fpId}; Limit: {limit}; Pos: {pos}");
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RemoveAuth(byte fpId, byte pos, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                return Result.SuccessIf(TcpSetup.CancelAuthorise(fpId, pos) || !TcpSetup.Pump(fpId).IsAuthorised, $"Could not remove Auth, FpId: {fpId}; Pos: {pos}");
            }, loggingReference);
        }

        private int GetSeqNumber(DomsSetupPump pump, byte tran) => pump.Transaction(0, tran)?.SequenceNumber ?? PumpWorker.DefaultValueTransSeqNum;

        /// <inheritdoc/>
        public Result Cashout(byte fpId, byte pos, byte tran, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                var pump = TcpSetup.Pump(fpId);
                var seqNo = GetSeqNumber(pump, tran);

                var result =
                    pump.IsLocked ? Result.SuccessIf(TcpSetup.ClearTransaction(fpId, tran, seqNo, pos), $"Could not Clear transaction, FpId: {fpId}; TranNum: {tran}; Pos: {pos}; SeqNum: {seqNo}") :
                    !pump.AllTransactions.Any() ? Result.SuccessIf(TcpSetup.CancelAuthorise(fpId, pos), $"Could not (Pseudo) Clear transaction, FpId: {fpId}; TranNum: {tran}; Pos: {pos}; SeqNum: {seqNo}") :
                    Result.Failure($"Unknown Cashout State, FpId: {fpId}; TranNum: {tran}; Pos: {pos}; SeqNum: {seqNo}");

                if (result.IsSuccess)
                {
                    pump.AddCashedOutState(seqNo, TransactionCashedOutState.CashedOut);
                }

                return result;
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result Claim(byte fpId, byte pos, byte tran, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                var pump = TcpSetup.Pump(fpId);
                var seqNo = GetSeqNumber(pump, tran);

                var result = !pump.AllTransactions.Any() ? Result.Success() :
                    Result.SuccessIf(TcpSetup.ReadClaimTransactionFpUnSup(fpId, tran, seqNo, pos), $"Could not Claim transaction, FpId: {fpId}; TranNum: {tran}; Pos: {pos}; SeqNum: {seqNo}");

                if (result.IsSuccess)
                {
                    pump.AddCashedOutState(seqNo, TransactionCashedOutState.Claimed);
                }

                return result;
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result StartCashOut(byte fpId, byte tran, int transSeqNum, uint amount, bool isZeroPaid, string loggingReference)
        {
            DoControllerAction(() =>
            {
                TcpSetup.Pump(fpId).AddCashedOutState(transSeqNum, TransactionCashedOutState.Started);
            }, loggingReference);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result PaymentClearedOrCancelledAcknowledged(byte fpId, int transSeqNo, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                var pump = TcpSetup.Pump(fpId);
                pump.AddCashedOutState(transSeqNo, TransactionCashedOutState.PaymentAcknowledged);

                var states = pump.GetCashedOutStates(transSeqNo);
                if (!states.ContainsKey(TransactionCashedOutState.CashedOut))
                {
                    if (states.Any(x => x.Key != TransactionCashedOutState.TransactionReceived && x.Key != TransactionCashedOutState.PaymentAcknowledged))
                    {
                        DoDeferredLogging(LogLevel.Warn, "CashedOutState.FpId", () => new[] { $"{fpId}; SeqNum: {transSeqNo}; json: {JsonConvert.SerializeObject(states)}" });
                    }

                    var transaction = pump.Transaction(seqNo: transSeqNo);
                    return transaction == null ? Result.Success() : TcpSetup.AutoClearTransaction(fpId, transaction.TransNum, transSeqNo, transaction.ServiceModeId, transaction.Volume, transaction.Amount, transaction.TransLockId);
                }

                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void EmergencyStop(byte fpId, string reg, string loggingReference)
        {
            DoControllerAction(() => { TcpSetup.EmergencyStop(fpId); }, loggingReference);
        }

        /// <inheritdoc/>
        public void EmergencyStopCancel(byte fpId, string loggingReference)
        {
            DoControllerAction(() => { TcpSetup.CancelEmergencyStop(fpId); }, loggingReference);
        }

        /// <inheritdoc/>
        public void EmergencyStopUpdate(byte fpId, string reg, string loggingReference)
        {
        }

        /// <inheritdoc/>
        protected virtual Result<IEnumerable<FileVersionInfo>> DoGetVersionInfo()
        {
            var dllCommon = Assembly.GetExecutingAssembly();

            var infoPos = _fileInfoVersionHelper.ExtractFileVersionInfo(dllCommon.Location, PssPosDll);
            if (!infoPos.IsSuccess)
            {
                return Result.Failure<IEnumerable<FileVersionInfo>>(infoPos.Error);
            }

            var infoTcp = _fileInfoVersionHelper.ExtractFileVersionInfo(dllCommon.Location, PssTcpDll);
            if (!infoTcp.IsSuccess)
            {
                return Result.Failure<IEnumerable<FileVersionInfo>>(infoTcp.Error);
            }

            var versions = new List<FileVersionInfo>()
                {
                    infoPos.Value,
                    infoTcp.Value,
                };

            if (!(string.IsNullOrWhiteSpace(_softwareDate) || string.IsNullOrWhiteSpace(_softwareVersion)))
            {
                var dtm = DateTime.TryParseExact(_softwareDate, "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out var dtm1) ? dtm1 : DateTime.MinValue;
                versions.Insert(0, new FileVersionInfo("DOMS Software", _softwareVersion, null, dtm));
            }

            return Result.Success(versions.AsEnumerable());
        }

        /// <inheritdoc/>
        public Result<IEnumerable<FileVersionInfo>> GetVersionInfo()
        {
            return DoAction(DoGetVersionInfo, null);
        }

        /// <inheritdoc/>
        public Result RemoveReserve(byte fpId, byte posId, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                // To force Pump into the correct state
                var pump = TcpSetup.Pump(fpId);
                pump.SetIsReserved(true);
                pump.SetLockId(posId);

                return Result.SuccessIf(TcpSetup.CancelAuthorise(fpId, posId), $"Could not Remove Reserve, FpId: {fpId}; Pos: {posId}");
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result Release(byte fpId, byte pos, byte tran, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                var pump = TcpSetup.Pump(fpId);
                var seqNo = GetSeqNumber(pump, tran);

                return Result.SuccessIf(TcpSetup.ReleaseTransaction(fpId, tran, seqNo, pos), $"Could not Release transaction, FpId: {fpId}; TranNum: {tran}; Pos: {pos}; SeqNum: {seqNo}");
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RequestMaxPumps(string loggingReference = null)
        {
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result RequestDips(string loggingReference = null)
        {
            return DoControllerAction(() =>
            {
                _fpRequestType |= FetchFpRequestType.DipStatus;
                _pendingDips = true;
                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RequestMeters(DateTime? now = null, string loggingReference = null)
        {
            return DoControllerAction(() =>
            {
                _fpRequestType |= FetchFpRequestType.MeterStatus;
                _pendingMeters = true;
                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RequestPrices(string loggingReference = null)
        {
            CheckForRequestPumpData(0, FetchFpRequestType.InstallData | FetchFpRequestType.Info, loggingReference);
            CheckForRequestForecourtData(FetchFcRequestType.PriceSet | FetchFcRequestType.GradeText, loggingReference);
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result SetPrices(IEnumerable<coreGrade> gradePrices, string loggingReference = null)
        {
            return DoAction(() =>
            {

                if (!StateEnabled || !StateConnected || Config == null)
                {
                    return Result.Failure("Invalid Controller State to run Price Update!");
                }

                try
                {
                    Config.GetPrices2(PriceSetTypes.PST_ACTUAL_PRICE_SET, out byte priceSetId, out PriceGroupCollection prices, out _);
                    foreach (var item in gradePrices)
                    {
                        // NB Can't use var here as PSS models prevent it!!
                        foreach (PriceGroup pg in prices)
                        {
                            foreach (GradePrice gp in pg.GradePrices)
                            {
                                if (gp.Id == item.Id)
                                {
                                    gp.Price = (float)(item.Price / (decimal)1000.0);
                                }
                            }
                        }
                    }

                    Config.SetPrices(priceSetId, prices);
                    _fcRequestType |= FetchFcRequestType.PriceSet;
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                    return Result.Failure(ex.Message);
                }

                return Result.Success();
            }, loggingReference);
        }

        private void SetPumpStatePending(int fpId, DateTime? lastProcessed = null) => _fetchPumpState.AddOrUpdate(fpId, new FetchPumpStateInfo(), (k, v) => { v.IsPending = true; return v; });
        private void SetPumpStateProcessing(int fpId) => _fetchPumpState.AddOrUpdate(fpId, new FetchPumpStateInfo(true, true), (k, v) => { v.IsProcessing = true; return v; });

        private void SetPumpStateProcessed(int fpId) => _fetchPumpState.AddOrUpdate(fpId, new FetchPumpStateInfo(false), (k, v) => { v.IsPending = false; v.IsProcessing = false; return v; });

        /// <inheritdoc/>
        public Result RequestPumpData(byte fpId, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                SetPumpStatePending(fpId);
                _fpRequestType |= FetchFpRequestType.Status;
                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void Stop(byte fpId, string loggingReference)
        {
            DoControllerAction(() =>
            {
                TcpSetup.EmergencyStop(fpId);
                SetPumpStatePending(fpId);
            }, loggingReference);
        }


        /// <inheritdoc/>
        public void CheckForDisconnection(string loggingReference)
        {
            DoAction(() =>
            {
                if (Connected && Reload)
                {
                    DisconnectDoms();
                }
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void CheckForLogin(byte pos, string loggingReference)
        {
            DoAction(() =>
            {
                if (!AllConnected)
                {
                    LoginDoms(pos);
                }
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void CheckForMasterReset(string loggingReference)
        {
            DoAction(() =>
            {
                if (TcpPreparedSetup != null && TcpPreparedSetup.Connected && _masterReset)
                {
                    MasterResetDoms();
                }
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void CheckForRequestForecourtData(FetchFcRequestType fcRequestType, string loggingReference)
        {
            _fcRequestType |= fcRequestType;
            TcpSetup.LastFcStatus = DateTime.MinValue;
        }

        /// <inheritdoc/>
        public void CheckForRequestPumpData(byte fpId, FetchFpRequestType fpRequestType, string loggingReference)
        {
            foreach (var p in _fetchPumpState.Keys.Where(x => fpId == 0 || fpId == x))
            {
                SetPumpStatePending(p, lastProcessed: DateTime.MinValue);
            }
            _fpRequestType |= fpRequestType;
        }

        /// <inheritdoc/>
        public void CheckForRequestPumpData(string loggingReference)
        {
            DoAction(() =>
            {
                if (!(GotMaxPumps && _fetchPumpState.Any()))
                {
                    FetchState("Initialising (State|Pump)", string.Empty, null, FetchFcRequestType.All, FetchFpRequestType.InstallData | FetchFpRequestType.Info);
                    _fetchTries = _fetchTries + 1 < MaxFetchTries.GetValue() ? _fetchTries + 1 : 0;
                    return;
                }

                if (_fcRequestType != FetchFcRequestType.None)
                {
                    var fc = _fcRequestType;
                    FetchState("OnStateData", string.Empty, null, fc, FetchFpRequestType.None);
                }

                if (_fpRequestType != FetchFpRequestType.None)
                {
                    var fp = _fpRequestType;
                    foreach (var pump in _fetchPumpState.Where(x => x.Value.IsPending && !x.Value.IsProcessing).Select(x => x.Key))
                    {
                        FetchState("OnPumpData", string.Empty, (byte)pump, FetchFcRequestType.None, fp);
                    }
                }
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void Reconnect(string loggingReference = null)
        {
            SetReload();
        }

        /// <inheritdoc/>
        public void Reset(string loggingReference = null)
        {
            TcpPreparedSetup.SendToDoms(true);
            _fcRequestType = FetchFcRequestType.All;
            _fpRequestType = FetchFpRequestType.Initialisation;
        }

        /// <inheritdoc/>
        public void MasterReset(string loggingReference = null)
        {
            _masterReset = true;
        }

        /// <inheritdoc/>
        public Result SetIpAddress(IPAddress ipAddress, int port = 0, string loggingReference = null)
        {
            HydraDb.SetDomsIpAddress(ipAddress);
            SetReload();
            return Result.Success();
        }

        /// <inheritdoc/>
        public Result SetLogonInfo(string logonInfo, string loggingReference = null)
        {
            HydraDb.SetDomsLoginString(logonInfo);
            LogonInfo = logonInfo;
            SetReload();
            return Result.Success();
        }

        /// <inheritdoc/>
        public uint ToAmount(decimal amount) => amount.ToUInt();

        /// <inheritdoc/>
        public ushort ToPrice(decimal price) => price.ToUShort();

        /// <inheritdoc/>
        public uint ToVolume(decimal volume) => volume.ToUInt();

        #region Callbacks

        private void OpModeChanged(byte modeNumber, ITimeStamp timestamp)
        {
            FetchState("ModeChange.Op", $"{modeNumber}; {timestamp.DateTime:dd/MM/yyyy HH:mm:ss.fff}", null, FetchFcRequestType.All, FetchFpRequestType.Initialisation);
        }

        private void StatusChanged(FcStatus status)
        {
            FetchState("StatusChange", $"Status1: {status.FcStatus1Flags}; Status2: {status.FcStatus2Flags}", null, FetchFcRequestType.All, FetchFpRequestType.Initialisation);
        }

        private void TerminalStatusChanged(Terminal terminal, TermMainStates mainState, byte subState, byte errorState)
        {
            FetchState("StatusChange.TerminalId", $"{terminal.Id}; States: {mainState}/{subState}/{errorState}");
        }

        private void FuellingPointStatusChanged(FuellingPoint fp, byte actSmId, FpMainStates mainState, byte subState, byte lockId)
        {
            if (!$"|{ConfigValueDOMSServiceModeMixed.GetValue()}|{ConfigValueDOMSServiceModeOptOnly.GetValue()}|{ConfigValueDOMSServiceModeKioskOnly.GetValue()}|{ConfigValueDOMSServiceModeKioskUse.GetValue()}|".Contains($"|{actSmId}|"))
            {
                return;
            }

            FetchState("StatusChange.FpId", $"{fp.Id}; States: {mainState}/{subState}; Active SMId: {actSmId}; LockId: {lockId}", fp.Id);
        }

        private void DioStatusChanged(DIO dio, DIOStatusParmCollection dioStatusParmCollection)
        {
            FetchState("StatusChange.DIOId", $"{dio.Id}");
        }

        private void PricePoleStatusChanged(PricePole pp, PpMainStates mainState, byte subState)
        {
            FetchState("StatusChange.PricePoleId", $"{pp.Id}; States: {mainState}/{subState}");
        }

        private void TankGaugeStatusChanged(TankGauge tg, TgMainStates mainState, byte status, int alarmStatus)
        {
            FetchState("StatusChange.TankGaugeId", $"{tg.Id}; States: {mainState}/{status}/{alarmStatus}", null, FetchFcRequestType.All, FetchFpRequestType.Initialisation);
        }

        private void SiteDeliveryStatusChanged(byte statusFlags, byte deliveryReportSeqNo, TankGaugeCollection tankGauges)
        {
            FetchState("StatusChange.SiteDelivery", $"Flags: {statusFlags}; SeqNo: {deliveryReportSeqNo}");
        }

        #endregion

        private void FetchState(string logHeader, string logMessage, byte? fpId = null, FetchFcRequestType fcRequestType = FetchFcRequestType.None, FetchFpRequestType fpRequestType = FetchFpRequestType.None)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), logHeader, () => new[] { logMessage });

            _fcRequestType = FetchFcRequestType.None;
            _fpRequestType = FetchFpRequestType.None;

            // FcXxx or Initialisation related
            if (fpId == null)
            {
                // Core Initialisation
                DoFetchState(0, fcRequestType, fpRequestType);

                // Now force initial Read PumpState, on all pumps
                fcRequestType = FetchFcRequestType.None;
                var fp = fpRequestType;
                fpRequestType = FetchFpRequestType.None |
                    (fp.HasFlag(FetchFpRequestType.Info) ? FetchFpRequestType.None : FetchFpRequestType.Info) |
                    (fp.HasFlag(FetchFpRequestType.Status) ? FetchFpRequestType.None : FetchFpRequestType.Status);
                fpId = 0;
            }

            // fpXxx related
            var allPumps = !fpId.HasValue ? new int[] { } : TcpSetup.AllPumps().Where(x => fpId == 0 || x.FpId == fpId).Select(x => (int)x.FpId).ToArray();
            foreach (var pump in allPumps)
            {
                if (!_fetchPumpState.ContainsKey(pump))
                {
                    _fetchPumpState[pump] = new FetchPumpStateInfo();
                }

                if (_fetchPumpState.TryGetValue(pump, out var info) && (fpId == 0 || (info.IsPending && !info.IsProcessing)))
                {
                    SetPumpStateProcessing(pump);
                    try
                    {
                        DoFetchState(pump == -1 ? null : (byte?)pump, fcRequestType, fpRequestType);
                    }
                    finally
                    {
                        SetPumpStateProcessed(pump);
                    }
                }
            }
        }

        // TODO: What to fetch FcXxx every 5mins, Fpxxx on demand
        private Result DoFetchState(byte? fpId, FetchFcRequestType fcRequestType, FetchFpRequestType fpRequestType)
        {
            if (!(Enabled && AllConnected))
            {
                return Result.Failure(ConfigConstants.NotConnected);
            }

            FetchedSetup.FetchFromDoms(fcRequestType, fpRequestType, fpId);
            var previousSetup = TcpSetup.Replicate();
            var result = TcpSetup.FetchFromDoms(fcRequestType, fpRequestType, fpId);
            if (!result.IsSuccess)
            {
                return result;
            }

            if (!(string.Equals(_softwareVersion, TcpSetup.SoftwareVersion) && string.Equals(_softwareDate, TcpSetup.SoftwareDate)))
            {
                _softwareVersion = TcpSetup.SoftwareVersion;
                _softwareDate = TcpSetup.SoftwareDate;
                _cacheHelper.ForceExpirationOnCachedItem(nameof(PumpWorker), nameof(FileVersionInfo));
            }


            if (fpRequestType.HasFlag(FetchFpRequestType.Status))
            {
                var allPumps = TcpSetup.AllPumps();

                //_fetchStateNeeded = allPumps.Count == 0 && _fetchTries < MaxFetchTries.GetValue();
                //_fetchTries = _fetchStateNeeded ? _fetchTries + 1 : 0;

                foreach (var pump in allPumps.Where(x => fpId == 0 || x.FpId == fpId))
                {
                    TcpSetup.FetchStatePump(pump, previousSetup, null, (DomsMessageCode.FpStatus, 3));
                }
            }

            return Result.Success();
        }

        private void LoginDoms(byte pos)
        {
            var result = LoginLockCheck(true);
            if (!result.IsSuccess)
            {
                return;
            }

            try
            {
                var result1 = DoLoginDoms_Setups(pos);

                var result2 = DoLoginDoms_Forecourt(pos);

                var allConnected = AllConnected;
                if (allConnected)
                {
                    ProcessOnConnection();
                }
                else
                {
                    _loginFails++;
                    if (_loginFails > 20)
                    {
                        _loginWait = DateTime.Now.AddSeconds(60);
                    }
                    else if (_loginFails > 10)
                    {
                        _loginWait = DateTime.Now.AddSeconds(10);
                    }
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"Connection.{HeaderStatus}", () => new[]
                {
                    $"Result 1/2: {result1.IsSuccess}{(result1.IsSuccess ? string.Empty : $": {result1.Error}")}/{result2.IsSuccess}{(result2.IsSuccess ? string.Empty : $": {result2.Error}")}; " +
                    $"AllConnected: {allConnected}; _loginFails: {_loginFails}; _loginWait: {_loginWait:dd/MM/yyyy HH:mm:ss.fff}"
                });
            }
            finally
            {
                LoginUnlock();
            }
        }

        private string ToAccessCode(string id, byte pos) => string.IsNullOrWhiteSpace(id) ? string.Empty : LogonInfo.Replace("APPL_ID=H1", $"APPL_ID=H{pos}.{id}");

        private Result DoLoginDoms_Setups(byte pos)
        {
            Result LoginDoms(IDomsSetup setup, string setupMarker, string code, string code2 = "", string code4 = "", string code5 = "", IList<DomsMessageId> unSol = null,
                IList<DomsMessageId> unSol2 = null, IList<DomsMessageId> sol4 = null, IList<DomsMessageId> unSol5 = null)
            {
                var ipAddress = CurrentEndPoint.IpAddress;

                var result = setup.LoginDoms(ipAddress,
                    ToAccessCode(code, pos), ToAccessCode(code2, pos), ToAccessCode(code4, pos), ToAccessCode(code5, pos),
                    CountryCode, PosVersionId,
                    unSol ?? new List<DomsMessageId>(), unSol2 ?? new List<DomsMessageId>(), sol4 ?? new List<DomsMessageId>(), unSol5 ?? new List<DomsMessageId>());

                DoDeferredLogging(LogLevel.Info, $"Attempting DOMS connection ({setupMarker}), IP Address", () => new[]
                {
                    ipAddress,
                    $"AccessCodes: {string.Join(", ", new[] { code, code2, code4, code5 })}",
                    $"Result: {(result ? "Done": "Failed")}"
                });

                return Result.SuccessIf(result, $"DOMS connection ({setupMarker}) failed");
            }

            try
            {
                var result = Result.Success();

                if (FetchedSetup.Enabled && !FetchedSetup.Connected)
                {
                    result = LoginDoms(FetchedSetup, "Fetched", "F");
                    if (!result.IsSuccess)
                    {
                        return result;
                    }
                }

                if (TcpSetup.Enabled && !TcpSetup.Connected)
                {
                    result = LoginDoms(TcpSetup, "TCP", "T1", "T2", "T4", "T5",
                        // Solicited messages
                        null,
                        new List<DomsMessageId>
                        {
                            new DomsMessageId(DomsMessageCode.FcStatus, 0x02),
                            new DomsMessageId(DomsMessageCode.FpStatus, 0x03),
                            new DomsMessageId(DomsMessageCode.FpSupTransBufStatus, 0x03),  // Track P@K transactions (with GradeId)
                            //new DomsMessageId(DomsMessageCode.FpSupTransBufStatus, 0x00),  // Track P@K transactions
                            new DomsMessageId(DomsMessageCode.TgStatus, 0x01),
                            new DomsMessageId(DomsMessageCode.FcPriceSetStatus, 0x00),
                            new DomsMessageId(DomsMessageCode.OperationModeStatus, 0x00),
                            new DomsMessageId(DomsMessageCode.FcInstallStatus, 0x01)
                            //new DomsMessageId(DomsMessageCode.PosConnectionStatus, 0x00),
                            //new DomsMessageId(DomsMessageCode.PssPeripheralsStatus, 0x00),
                            //new DomsMessageId(DomsMessageCode.EptStatus, 0x01),
                            //new DomsMessageId(DomsMessageCode.PpStatus, 0x00),
                            //new DomsMessageId(DomsMessageCode.SiteDeliveryStatus, 0x01),
                            //new DomsMessageId(DomsMessageCode.WpStatus, 0x00),
                            //new DomsMessageId(DomsMessageCode.DiopStatus, 0x00),
                            //new DomsMessageId(DomsMessageCode.EptDeviceStatus, 0x00),
                        },
                        // Solicited messages
                        null,
                        new List<DomsMessageId>
                        {
                            new DomsMessageId(DomsMessageCode.FpUnSupTransBufStatus, 0x03),  // Track OPT transactions (with GradeId)
                            //new DomsMessageId(DomsMessageCode.FpUnSupTransBufStatus, 0x00),  // Track OPT transactions
                            //new DomsMessageId(DomsMessageCode.BnaExchangeRateStatus, 0x00)
                        });
                    if (!result.IsSuccess)
                    {
                        return result;
                    }
                }

                if (TcpPreparedSetup.Enabled && !TcpPreparedSetup.Connected)
                {
                    result = LoginDoms(TcpPreparedSetup, "TCP Prepared", "TP");
                    if (!result.IsSuccess)
                    {
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {
                        $"DOMS Error HResult: {ex.HResult:x8}",
                        $"Text: {Forecourt?.HResult2Text(ex.HResult) ?? "Unavailable"}" }, ex);

                return Result.Failure(ex.Message);
            }

            return Result.Success();
        }

        private Result DoLoginDoms_Forecourt(byte pos)
        {
            if (!(StateEnabled && !StateConnected && Forecourt != null))
            {
                return Result.Failure("Invalid Forecourt Login state");
            }

            try
            {
                if (!_fcInit)
                {
                    Forecourt.HostName = CurrentEndPoint.IpAddress;
                    Forecourt.PosId = DefaultPosIdForState;
                    Forecourt.Initialize();

                    Forecourt.FcOperationModeChanged += OpModeChanged;
                    Forecourt.FcStatusChanged += StatusChanged;
                    Forecourt.TerminalStatusChanged += TerminalStatusChanged;
                    Forecourt.FuellingPointStatusChanged += FuellingPointStatusChanged;
                    Forecourt.TankGaugeStatusChanged += TankGaugeStatusChanged;
                    //Forecourt.DIOStatusChanged += DioStatusChanged;
                    //Forecourt.PricePoleStatusChanged += PricePoleStatusChanged;
                    //Forecourt.SiteDeliveryStatusChanged += SiteDeliveryStatusChanged;
                    _fcInit = true;
                }

                if (!StateConnected)
                {
                    var parameters = new FcLogonParms();
                    parameters.EnableFcEvent(FcEvents.FcOperationModeChanged);
                    parameters.EnableFcEvent(FcEvents.FcStatusChanged);
                    parameters.EnableFcEvent(FcEvents.TerminalStatusChanged);
                    parameters.EnableFcEvent(FcEvents.FuellingPointStatusChanged);
                    parameters.EnableFcEvent(FcEvents.TankGaugeStatusChanged);
                    parameters.EnableFcEvent(FcEvents.xxxxCfgChanged);
                    //parameters.EnableFcEvent(FcEvents.DIOStatusChanged);
                    //parameters.EnableFcEvent(FcEvents.PricePoleStatusChanged);
                    //parameters.EnableFcEvent(FcEvents.SiteDeliveryStatusChanged);
                    Forecourt.FcLogon2(ToAccessCode("S", pos), parameters);
                    StateConnected = true;
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
            }

            return Result.Success();
        }

        private Result LoginLockCheck(bool loginWaitCheck = false)
        {
            lock (_loginLockObject)
            {
                if (_loginRunning)
                {
                    DoDeferredLogging(LogLevel.Warn, HeaderEndFail, () => new[] { "Login currently running" });
                    return Result.Failure("Login currently running");
                }

                if (!loginWaitCheck || (DateTime.Now > _loginWait))
                {
                    _loginRunning = true;
                }
            }

            return Result.Success();
        }

        private void LoginUnlock()
        {
            lock (_loginLockObject)
            {
                _loginRunning = false;
            }
        }

        private void DisconnectDoms()
        {
            var result = LoginLockCheck();
            if (!result.IsSuccess)
            {
                return;
            }

            try
            {
                DoDisconnectDoms();
            }
            finally
            {
                OnDisconnected();
                LoginUnlock();
            }
        }

        private void DoDisconnectDoms()
        {
            if (FetchedSetup.Connected)
            {
                FetchedSetup.Disconnect();
            }

            if (StateConnected)
            {
                try
                {
                    Forecourt.Disconnect();
                    StateConnected = false;
                    _fcInit = false;
                    _stateReload = false;
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {
                        $"DOMS Error HResult: {ex.HResult:x8}",
                        $"Text: {Forecourt?.HResult2Text(ex.HResult) ?? "Unavailable"}" }, ex);
                }
            }

            if (TcpSetup.Connected)
            {
                TcpSetup.Disconnect();
            }

            if (TcpPreparedSetup.Connected)
            {
                TcpPreparedSetup.Disconnect();
            }

            _fcRequestType = FetchFcRequestType.All;
            _fpRequestType = FetchFpRequestType.Initialisation;
        }

        private Result MasterResetDoms()
        {
            var result = LoginLockCheck();
            if (!result.IsSuccess)
            {
                return result;
            }

            try
            {
                var success = true;
                if (TcpPreparedSetup.Enabled && TcpPreparedSetup.Connected)
                {
                    var tcpPreparedLoginString = ToAccessCode("MR", DefaultPosIdForTcpPrepared) + ",MASTER-RESET";
                    GetLogger().Info($"DOMS - Master Reset using access code {tcpPreparedLoginString}");

                    success = TcpPreparedSetup.SendOneLogin(tcpPreparedLoginString, CountryCode, PosVersionId, new List<DomsMessageId>());
                    _masterReset = false;
                    _fcRequestType = FetchFcRequestType.All;
                    _fpRequestType = FetchFpRequestType.Initialisation;
                }

                return Result.SuccessIf(success, "Unable to perform Master-Reset");
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {
                        $"DOMS Error HResult: {ex.HResult:x8}",
                        $"Text: {Forecourt?.HResult2Text(ex.HResult) ?? "Unavailable"}" }, ex);

                return Result.Failure(ex.Message);
            }
            finally
            {
                LoginUnlock();
            }
        }

        private void SetReload()
        {
            FetchedSetup.SetReload();
            TcpSetup.SetReload();
            TcpPreparedSetup.SetReload();
            _stateReload = StateEnabled;
        }

        private void SetEnabled(bool value, bool stateEnabled = false)
        {
            FetchedSetup.SetEnabled(false);
            TcpSetup.SetEnabled(value);
            TcpPreparedSetup.SetEnabled(value);
            StateEnabled = stateEnabled;
        }

        /// <inheritdoc/>
        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            base.DoOnConnected(ipAddress, port);

            GetWorker<IPumpControllerCallbacks>()?.OnConnected(ipAddress, port);
        }

        /// <inheritdoc/>
        protected override void DoOnDisconnected(int? id = null)
        {
            base.DoOnDisconnected(id);

            GetWorker<IPumpControllerCallbacks>()?.OnDisconnected(id);
        }

        protected override bool DoIsConnected()
        {
            return AllConnected;
        }

        /// <inheritdoc/>
        public Result ReserveWithLimit(byte fpId, uint limit, byte pos, string loggingReference)
        {
            return DoControllerAction(() =>
            {
                var success = TcpSetup.Reserve(fpId, limit, pos) || TcpSetup.Pump(fpId).IsReserved;
                if (!success)
                {
                    return Result.Failure($"Could not Reserve, FpId: {fpId}; Limit: {limit}; Pos: {pos}");
                }

                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result CheckForStuckSales((bool, bool) checkLevels, byte pump, bool isDeliveredPending, int transSeqNum, uint amount, uint volume, byte grade, byte pos, string loggingReference)
        {
            if (isDeliveredPending)
            {
                return Result.Failure("Delivered is still Pending!");
            }

            var domsPump = TcpSetup.Pump(pump);

            // Level-1 Check - No Forecourt.Service reboots
            if (checkLevels.Item1)
            {
                if (domsPump.IsReserved || domsPump.IsAuthorised || domsPump.IsLocked || domsPump.LockId != 0 || domsPump.AllTransactions.Any())
                {
                    var trx = domsPump.AllTransactions.FirstOrDefault(x => x.SequenceNumber == transSeqNum && x.Amount == amount && x.ActualVolume == volume && x.GradeId == grade);
                    if (trx != null)
                    {
                        DoDeferredLogging(LogLevel.Warn, $"FpUnSupTransBuf.{HeaderStatus}.FpId", () => new[] { $"{pump}; Trans: {trx.TransNum}; SeqNum: {trx.SequenceNumber}; SMId: {trx.ServiceModeId}; LockId: {trx.TransLockId}; Cash: {amount}; Volume: {volume}; Grade: {grade}" }, reference: loggingReference);
                        var result = Cashout(pump, pos, trx.TransNum, loggingReference);
                        if (result.IsSuccess)
                        {
                            DoDeferredLogging(LogLevel.Warn, $"{LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {trx.TransNum}" }, reference: loggingReference, methodName: "SendCashOut");
                        }

                        return result;
                    }
                }
            }

            // Level-2 Check - Request FpUnSupTransBuf on ACP 1 or 4 (with different DomsMessageReader.ExtractFpUnSupTransBuf action!)
            if (checkLevels.Item2)
            {
                // TODO:
            }

            return Result.Failure("CheckForStuckSales not Enabled, or no Stuck Sale!");
        }

        /// <summary>Config Value, for DOMS Service modes mapping, for Mixed</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeMixed { get; set; }
        /// <summary>Config Value, for DOMS Service modes mapping, for KioskOnly</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeKioskOnly { get; set; }
        /// <summary>Config Value, for DOMS Service modes mapping, for OptkOnly</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeOptOnly { get; set; }
        /// <summary>Config Value, for DOMS Service modes mapping, for KioskUse</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeKioskUse { get; set; }

        /// <inheritdoc/>
        public bool ShouldSynchroniseGrades => true;

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            var logRef = LoggingReference;

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.AllConnected", () => new[]
            {
                $"({StateConnected} || {!StateEnabled}) && " +
                $"(({FetchedSetup?.Connected ?? false}) || {!(FetchedSetup?.Enabled ?? false)}) && " +
                $"(({TcpSetup?.Connected ?? false}) || {!(TcpSetup?.Enabled ?? false)}) && " +
                $"(({TcpPreparedSetup?.Connected ?? false}) || {!(TcpPreparedSetup?.Enabled ?? false)}); " +
                $"_loginFails: {_loginFails}; _loginWait: {_loginWait}"
            });

            if (!AllConnected ||
                (TcpSetup == null || TcpSetup.Enabled && TcpSetup.Reload) ||
                (TcpPreparedSetup == null || TcpPreparedSetup.Enabled && TcpPreparedSetup.Reload) ||
                (FetchedSetup == null || FetchedSetup.Enabled && FetchedSetup.Reload))
            {
                CheckForLogin(_logonPos, logRef);
                return;
            }
            else if (_loginFails > 0)
            {
                ProcessOnConnection();
            }

            CheckForDisconnection(logRef);
        }

        private void ProcessOnConnection()
        {
            _loginFails = 0;
            _fcRequestType = FetchFcRequestType.All;
            _fpRequestType = FetchFpRequestType.Initialisation;

            var endPoint = CurrentEndPoint.EndPoint;

            OnConnected(endPoint.Address, endPoint.Port);
        }

        /// <inheritdoc/>
        public Result SetPumpHoseGradePrice(byte pump, byte hose, byte grade, uint price, string name)
        {
            var domsPump = TcpSetup.Pump(pump);
            TcpSetup.SetGradeName(grade, name);
            domsPump.SetGradeOption(hose, grade, hose, 0, 0);
            domsPump.SetGradePrice(grade, price);
            return Result.Success();
        }
    }
}
