import { TestBed } from "@angular/core/testing";
import { SignalRService } from "./signal-r.service";

export function SIGNAL_R_SERVICE_PROVIDER(): { provide: typeof SignalRService, useValue: any } {
  return {
    provide: SignalRService,
    useValue: jasmine.createSpyObj('SignalRService', [
      'getAboutSignalRMessage',
      'getAdvancedConfigSignalRMessage',
      'getConnectionSignalRMessage',
      'getConnectionStatushChangeMessage',
      'getDivertDetailsSignalRMessage',
      'getFileLocationSignalRMessage',
      'getFuelPriceSignalRMessage',
      'getGenericOptConfigSignalRMessage',
      'getInfoMessageSignalRMessage',
      'getLocalAccountsSignalRMessage',
      'getOptSignalRMessage',
      'getPumpSignalRMessage',
      'getTransactionSignalRMessage',
      'processPushedEvent',
      'startConnection',
    ]),
  };
}

export function SIGNAL_R_SERVICE_SPY(): jasmine.SpyObj<SignalRService> {
  return TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
}