<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>9.0</LangVersion>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CsvHelper" Version="[30.0.1,)" />
		<PackageReference Include="Htec.Hydra.Messages.Opt" Version="[2.7.0,)" />
		<PackageReference Include="Htec.Hydra.Core.SecAuth" Version="[1.0.0,)" />
		<PackageReference Include="Htec.Hydra.Opt.Common" Version="[2.4.0,)" />
		<PackageReference Include="Htec.Logger.log4Net" Version="[7.0.0,)" />
		<PackageReference Include="Htec.Foundation" Version="[4.3.0,)" />
		<PackageReference Include="System.IO.Abstractions" Version="[19.2.51,)" />
		<PackageReference Include="Htec.Hydra.Core.Pos" Version="[2.2.0,)" />
		<PackageReference Include="Htec.Hydra.Core.Bos" Version="1.2.0-alpha0008" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Forecourt.BackOffice\Forecourt.BackOffice.csproj" />
	  <ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	  <ProjectReference Include="..\Forecourt.PaymentConfiguration\Forecourt.PaymentConfiguration.csproj" />
	  <ProjectReference Include="..\Forecourt.Pos\Forecourt.Pos.csproj" />
	  <ProjectReference Include="..\Forecourt.Pump\Forecourt.Pump.csproj" />
	  <ProjectReference Include="..\Forecourt.SecondaryAuth\Forecourt.SecondaryAuth.csproj" />
	  <ProjectReference Include="..\OPT.HydraDb\OPT.HydraDb.csproj" />
	  <ProjectReference Include="..\OPT.TransactionValidator\OPT.TransactionValidator.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Reference Include="System.Configuration" />
	</ItemGroup>
	
	<ItemGroup>
		<Reference Include="PSS_Forecourt_Lib" Condition="'$(Platform)' != 'x86'">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib" Condition="'$(Platform)' != 'x86'">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_Forecourt_Lib" Condition="'$(Platform)' == 'x86'">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib" Condition="'$(Platform)' == 'x86'">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Forecourt.Common.Tests" />
		<InternalsVisibleTo Include="Forecourt.Common.Integration.Tests" />
	</ItemGroup>

</Project>
