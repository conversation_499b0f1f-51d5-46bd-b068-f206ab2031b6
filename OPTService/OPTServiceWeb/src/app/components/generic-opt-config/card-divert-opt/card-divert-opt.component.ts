import { Component, Ng<PERSON>one, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>, FormBuilder } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { DivertDetails } from 'src/app/core/models/divertDetails.model';
import { ButtonHelper } from 'src/app/helpers/button.helper';
import { DivertService } from 'src/app/services/divert.service';
import { LoadingService } from 'src/app/services/loading.service';
import { SignalRService } from 'src/app/services/signal-r.service';

const PATTERN_IP = '^([0-9]{1,3})[.]([0-9]{1,3})[.]([0-9]{1,3})[.]([0-9]{1,3})$';
const PATTERN_DIGITS = '[0-9]*';
const DEFAULT_ERROR_TEXT = 'Field could not be updated';

export class DivertErrors {
  SetDivertDetails: boolean = false;
  SetDivertDetailsText: string = DEFAULT_ERROR_TEXT;
  CancelDivertDetails: boolean = false;
  CancelDivertDetailsText: string = DEFAULT_ERROR_TEXT;
}

@Component({
  selector: 'app-card-divert-opt',
  templateUrl: './card-divert-opt.component.html',
  styleUrls: ['./card-divert-opt.component.css']
})
export class CardDivertOptComponent implements OnInit {

  public signalRData: Subscription | undefined;
  public showContent: boolean = false;

  public divertOptIsCollapsed: boolean = true;

  public divertDetailsData: DivertDetails = new DivertDetails();
  public divertErrors: DivertErrors = new DivertErrors();
  public divertForm = this.fb.group({
    IsDiverted: [ false ],
    IpAddress: [ '', [ Validators.required, Validators.pattern(PATTERN_IP), Validators.maxLength(15) ] ],
    FromOptPort: [ 0, [ Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5) ] ],
    ToOptPort: [ 0, [ Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5) ] ],
    HeartbeatPort: [ 0, [ Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)] ],
    MediaChannelPort: [ 0, [ Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)] ],
    MediaChannel: [ false ]
  });

  constructor(
    private fb: FormBuilder,
    private logger: NGXLogger,
    private zone: NgZone,
    private signalRService: SignalRService,
    private divertService: DivertService,
    private loadingService: LoadingService,
  ) { 
    this.signalRData = this.signalRService.getDivertDetailsSignalRMessage().subscribe(() => {
      this.zone.run(() => {
        this.refreshDivertData(false);
      });
    });
  }

  ngOnInit(): void {
    setTimeout(()=>{
      this.refreshDivertData();
    });
  }

    /**
   * The refresh divert data method called when received a push from signalR
   */
  refreshDivertData(showLoading: boolean = true): void {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen();  
    }

    this.divertService.getDivertDetails().subscribe(data => {
      this.logger.debug('Divert details data', data);
      this.divertDetailsData = data;

      this.divertForm.patchValue(data);
      Object.keys(this.divertForm.controls).forEach(key => {
        this.divertForm.controls[key].valueChanges.subscribe(val => {
          this.divertDetailsData[key] = val;
        })
      })
    }, () => {
      this.logger.error('Problem getting divert details');
      this.loadingService.errorDuringLoading();
    },() => {
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
  }

  /**
   * Sets the divert details in the OPT Service
   */
  setDivertDetails(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Updating divert details');
    this.divertService.divertOptService(this.divertDetailsData).subscribe(
    () => {
      this.logger.debug('Divert details udpated ok');
      this.divertErrors.SetDivertDetails = false;
      ButtonHelper.reset(event);
    }, 
    (error) => {
      this.logger.error('Problem updating divert details');
      this.divertErrors.SetDivertDetails = true;
      if (error?.message) {
        this.divertErrors.SetDivertDetailsText = error.message;
      } else {
        this.divertErrors.SetDivertDetailsText = DEFAULT_ERROR_TEXT;
      }
      ButtonHelper.reset(event);    
    });
  }

  /**
   * Cancel the divert details in the OPT Service
   */
  cancelDivertDetails(event?: Event): void {
    ButtonHelper.clicked(event);
    this.logger.info('Cancelling divert details');
    this.divertService.cancelDivertOptService().subscribe(
    () => {
      this.logger.debug('Divert details cancelled ok');
      this.divertErrors.CancelDivertDetails = false;
      ButtonHelper.reset(event);
    }, 
    (error) => {
      this.logger.error('Problem cancelling divert details');
      this.divertErrors.CancelDivertDetails = true;
      if (error?.message) {
        this.divertErrors.CancelDivertDetailsText = error.message;
      } else {
        this.divertErrors.CancelDivertDetailsText = DEFAULT_ERROR_TEXT;
      }
      ButtonHelper.reset(event);
    });
  }
}
