using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;

namespace Forecourt.Pos.Workers.Interfaces
{
    public interface INonePosXxxWorker : IWorkerable, IPosIntegratorInMode<IMessageTracking>, IPosIntegratorOut<IMessageTracking>, IPosIntegratorInTransient<IMessageTracking, IPump, Result>
    {
    }
}
