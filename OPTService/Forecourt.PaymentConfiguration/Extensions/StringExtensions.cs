using ikvm.extensions;

namespace Forecourt.PaymentConfiguration.Extensions
{
    /// <summary>
    /// String extension methods specific to PaymentConfiguration (or the packages references by it)
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// Convert a string to byte[] using the IVKM nuget package
        /// </summary>
        /// <param name="value">String value</param>
        /// <returns>byte[]</returns>
        public static byte[] getBytes(this string value) => value.getBytes();
    }
}
