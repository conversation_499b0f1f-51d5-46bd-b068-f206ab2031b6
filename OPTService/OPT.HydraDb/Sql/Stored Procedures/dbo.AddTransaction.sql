USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('AddTransaction') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE AddTransaction
END
GO

CREATE procedure [dbo].[AddTransaction]
 @gradeName varchar(20),
 @code varchar(13),
 @quantity bigint,
 @amount bigint,
 @pumpDetails varchar(20),
 @cardNumber varchar(20),
 @transactionTime DateTime2,
 @category varchar(20),
 @subcategory varchar(20),
 @discountName varchar(20),
 @discountCode varchar(20),
 @discountValue bigint,
 @discountCardNumber varchar(20),
 @localAccountMileage bigint,
 @localAccountRegistration varchar(20),
 @txnNumber varchar(20),
 @maxTransactionNumber bigint,
 @hasValue bit,
 @transactionNumber int out as
 begin
	DECLARE @rawTransactionNumber BIGINT

	EXEC AddTransactionRecord 
	@gradeName, 
	@code, 
	@quantity, 
	@amount, 
	@pumpDetails, 
	@cardNumber, 
	@transactionTime, 
	@category, 
	@subcategory, 
	@discountName ,
	@discountCode,
	@discountValue,
	@discountCardNumber,
	@localAccountMileage,
	@localAccountRegistration,
	@txnNumber,
	@maxTransactionNumber,
	@hasValue,
	@rawTransactionNumber out
	
	 
	-- Check for transaction number recycling
	SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	IF(@transactionNumber = 0)
	BEGIN
		-- Remove 0 txn number row
		DELETE FROM [Transactions]
		WHERE rowid = @rawTransactionNumber
		
		-- Re-add so txn number != 0
		EXEC AddTransactionRecord 
			@gradeName, 
			@code, 
			@quantity, 
			@amount, 
			@pumpDetails, 
			@cardNumber, 
			@transactionTime, 
			@category, 
			@subcategory, 
			@discountName ,
			@discountCode,
			@discountValue,
			@discountCardNumber,
			@localAccountMileage,
			@localAccountRegistration,
			@txnNumber,
			@maxTransactionNumber,
			@hasValue,
			@rawTransactionNumber out

			SET @transactionNumber = @rawTransactionNumber % @maxTransactionNumber
	END

	INSERT INTO TransactionBookingState(TransactionId, TxnNumber, TransactionDate, CreatedDate, ModifiedDate)
	VALUES (@transactionNumber, @txnNumber, @transactionTime, GETDATE(), GETDATE());

 END


GRANT EXECUTE ON AddTransaction TO UserRole
GO
