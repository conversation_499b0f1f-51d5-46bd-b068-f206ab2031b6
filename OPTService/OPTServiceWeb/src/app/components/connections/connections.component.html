<ng-container *ngIf="showContent">
  <form [formGroup]="connectionsForm">
    <!-- OPT -->
    <div class="card form-group">
      <div class="card-header">
        <button type="button" (click)="optCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>OPT</span>
          <div>
            <span class="badge mx-2" id="optConnectedCount" [ngClass]="connectionsData?.OptConnectedCount > 0 ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.OptConnectedCount > 0 ? connectionsData?.OptConnectedCount + ' Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':optIsCollapsed,'bi-chevron-up':optIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #optCollapse="ngbCollapse" [(ngbCollapse)]="optIsCollapsed" class="card-body">
        <div class="row form-group" *ngIf="connectionsData?.OptConnectedCount > 0">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.OptIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
        <app-label-text-button id="fromOptPort" labelText="From OPT port" controlName="FromOptPort"
                               (action)="setFromOptPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.FromOptPort" [errorInActionText]="errors.FromOptPort">
        </app-label-text-button>

        <app-label-text-button id="toOptPort" labelText="To OPT port" controlName="ToOptPort"
                               (action)="setToOptPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.ToOptPort" [errorInActionText]="errors.ToOptPort">
        </app-label-text-button>

        <app-label-text-button id="heartbeatPort" labelText="Heartbeat port" controlName="HeartbeatPort"
                               (action)="setHeartbeatPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.HeartbeatPort" [errorInActionText]="errors.HeartbeatPort">
        </app-label-text-button>
      </div>
    </div>
    <!-- POS -->
    <div class="card form-group">
      <div class="card-header">
        <button type="button" (click)="posCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>POS [{{posType}}]</span>
          <div>
            <span class="badge mx-2" id="posConnected" [ngClass]="connectionsData?.IsPosConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.IsPosConnected ? 'Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':posIsCollapsed,'bi-chevron-up':posIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #posCollapse="ngbCollapse" [(ngbCollapse)]="posIsCollapsed" class="card-body">
    <!-- SignalR POS -->
    <div class="card form-group" *ngIf="posType === constants.POS_TYPE_SIGNALR">
        <div class="row form-group" *ngIf="connectionsData?.IsSignalRPosConnected">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.SignalRPosIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
    </div>
    <!-- SignalR POS HubClient-->
    <div class="card form-group" *ngIf="posType === constants.POS_TYPE_SIGNALR_HUBCLIENT_MADIC">
        <div class="row form-group" *ngIf="connectionsData?.IsSignalRPosInConnected">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.SignalRPosInIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
    </div>
    <!-- Hydra POS -->
    <div class="card form-group" *ngIf="posType === constants.POS_TYPE_HYDRAPOS || posType === constants.POS_TYPE_RETALIX">
      <div class="card-header">
        <button type="button" (click)="hydraPosCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Hydra POS</span>
          <div>
            <span class="badge mx-2" id="hydraPosConnectedCount" [ngClass]="connectionsData?.IsHydraPosConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.IsHydraPosConnected ? connectionsData?.HydraPosConnectedCount + ' Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':posIsCollapsed,'bi-chevron-up':posIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #hydraPosCollapse="ngbCollapse" [(ngbCollapse)]="posIsCollapsed" class="card-body">
        <div class="row form-group" *ngIf="connectionsData?.IsHydraPosConnected">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.HydraPosIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
        <app-label-text-button id="hydraPosPort" labelText="Port" controlName="HydraPosPort"
                               (action)="setHydraPosPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.HydraPosPort" [errorInActionText]="errors.HydraPosPort">
        </app-label-text-button>
      </div>
    </div>
    <!-- Retalix POS -->
    <div class="card form-group" *ngIf="posType === constants.POS_TYPE_RETALIX">
      <div class="card-header">
        <button type="button" (click)="retalixPosCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Retalix POS</span>
          <div>
            <span class="badge mx-2" id="retalixPosConnectedCount" [ngClass]="connectionsData?.IsRetalixPosConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.IsRetalixPosConnected ? connectionsData?.RetalixPosConnectedCount + ' Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':posIsCollapsed,'bi-chevron-up':posIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #retalixPosCollapse="ngbCollapse" [(ngbCollapse)]="posIsCollapsed" class="card-body">
        <div class="row form-group" *ngFor="let ip of connectionsData?.RetalixPosIpAddresses">
          <div class="input-group col-3">
            <label class="col-form-label">{{ip}}</label>
          </div>
          <div class="col-auto">
            <button type="button" class="btn btn-primary" (click)="setAsPrimary(ip)">Set as primary</button>
            <span class="text-danger ml-2 pt-1" placement="right" [ngbTooltip]="getErrorDescription(ip)" *ngIf="checkError(ip)"><i class="bi bi-exclamation-triangle"></i></span>
          </div>
        </div>
        <app-label-text-button id="retalixPosPrimaryIpAddress" labelText="Primary IP address" controlName="RetalixPosPrimaryIpAddress"
                               (action)="setRetalixPosPrimaryIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.RetalixPosPrimaryIpAddress" placeHolderText="NOT SET" [errorInActionText]="errors.RetalixPosPrimaryIpAddress">
        </app-label-text-button>
        <app-label-text-button id="retalixPosPort" labelText="Port" controlName="RetalixPosPort"
                               (action)="setRetalixPosPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.RetalixPosPort" [errorInActionText]="errors.RetalixPosPort">
        </app-label-text-button>
      </div>
    </div>
    <!-- Third party POS -->
    <div class="card form-group" *ngIf="posType == constants.POS_TYPE_3RDPARTY">
      <div class="card-header">
        <button type="button" (click)="thirdPartyPosCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Third party POS</span>
          <div>
            <span class="badge mx-2" id="thirdPartyPosConnectedCount" [ngClass]="connectionsData?.IsThirdPartyPosConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.IsThirdPartyPosConnected ? connectionsData?.ThirdPartyPosConnectedCount + ' Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':posIsCollapsed,'bi-chevron-up':posIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #thirdPartyPosCollapse="ngbCollapse" [(ngbCollapse)]="posIsCollapsed" class="card-body">
        <div class="row form-group" *ngIf="connectionsData?.ThirdPartyPosConnectedCount > 0">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.ThirdPartyPosIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
        <app-label-text-button id="ThirdPartyPosPort" labelText="Port" controlName="ThirdPartyPosPort"
                               (action)="setThirdPartyPosPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.ThirdPartyPosPort" [errorInActionText]="errors.ThirdPartyPosPort">
        </app-label-text-button>
      </div>
    </div>
    </div>
    </div>
      <!-- BOS -->
      <div class="card form-group">
        <div class="card-header">
          <button type="button" (click)="bosCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
            <span>BOS [{{bosType}}]</span>
            <div>
              <span class="badge mx-2" id="bosConnected" [ngClass]="connectionsData?.IsBosConnected ? 'badge-success' : 'badge-danger'">
                {{connectionsData?.IsBosConnected ? 'Connected' : 'Not connected'}}
              </span>
              <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':bosIsCollapsed,'bi-chevron-up':bosIsCollapsed===false }"></i>
            </div>
          </button>
        </div>
        <div #bosCollapse="ngbCollapse" [(ngbCollapse)]="bosIsCollapsed" class="card-body">
      <!-- SignalR BOS Hub-->
      <div class="card form-group" *ngIf="bosType === constants.POS_TYPE_SIGNALR">
          <div class="row form-group" *ngIf="connectionsData?.IsSignalRBosConnected">
            <div class="input-group">
              <label class="col-form-label col-3">IP list</label>
              <div class="col-7">
                <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.SignalRBosIpAddresses">{{ip}}</label>
              </div>
            </div>
          </div>
      </div>
    </div>
    </div>    
    <!-- Media channel -->
    <div class="card form-group" *ngIf="connectionFlags?.MediaChannelConnectionThread === true">
      <div class="card-header">
        <button type="button" (click)="mediaChannelCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Media channel</span>
          <div>
            <span class="badge mx-2" id="mediaChannelConnectedCount" [ngClass]="connectionsData?.MediaChannelConnectedCount > 0 ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.MediaChannelConnectedCount > 0 ? connectionsData?.MediaChannelConnectedCount + ' Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':mediaChannelIsCollapsed,'bi-chevron-up':mediaChannelIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #mediaChannelCollapse="ngbCollapse" [(ngbCollapse)]="mediaChannelIsCollapsed" class="card-body">
        <div class="row form-group" *ngIf="connectionsData?.MediaChannelConnectedCount > 0">
          <div class="input-group">
            <label class="col-form-label col-3">IP list</label>
            <div class="col-7">
              <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.MediaChannelIpAddresses">{{ip}}</label>
            </div>
          </div>
        </div>
        <app-label-text-button id="mediaChannelPort" labelText="Port" controlName="MediaChannelPort"
                               (action)="setMediaChannelPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.MediaChannelPort" [errorInActionText]="errors.MediaChannelPort">
        </app-label-text-button>
      </div>
    </div>
    <!-- Pump controller -->
    <div class="card form-group">
      <div class="card-header">
        <button type="button" (click)="pumpControllerCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Pump Controller [{{pumpType}}]</span>
          <div>
            <span class="badge mx-2" id="pumpControllerConnected" [ngClass]="connectionsData?.PumpControllerConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.PumpControllerConnected ? 'Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':pumpControllerIsCollapsed,'bi-chevron-up':pumpControllerIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #pumpControllerCollapse="ngbCollapse" [(ngbCollapse)]="pumpControllerIsCollapsed" class="card-body">
        <app-label-text-button id="pumpControllerIpAddress" labelText="IP address" controlName="PumpControllerIpAddress"
                               (action)="setPumpControllerIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.PumpControllerIpAddress" [errorInActionText]="errors.PumpControllerIpAddress">
        </app-label-text-button>
        <app-label-text-button id="pumpControllerPort" *ngIf="pumpType == 'HSC'" labelText="Port" controlName="PumpControllerPort"
                               (action)="setPumpControllerPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.PumpControllerPort" [errorInActionText]="errors.PumpControllerPort">
        </app-label-text-button>
        <app-label-text-button id="pumpControllerLogonInfo" *ngIf="pumpType == 'DOMS'" labelText="Logon info" controlName="PumpControllerLogonInfo"
                               (action)="setPumpControllerLogonInfo()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.PumpControllerLogonInfo" [errorInActionText]="errors.PumpControllerLogonInfo">
        </app-label-text-button>
      </div>
    </div>
    <!-- Tank gauge -->
    <div class="card form-group" *ngIf="connectionFlags?.TankGaugeConnectionThread === true">
      <div class="card-header">
        <button type="button" (click)="tankGaugeCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Tank Gauge Controller [{{pumpType}}]</span>
          <div>
            <span class="badge mx-2" id="tankGaugeConnected" [ngClass]="connectionsData?.TankGaugeConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.TankGaugeConnected ? 'Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':tankGaugeIsCollapsed,'bi-chevron-up':tankGaugeIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #tankGaugeCollapse="ngbCollapse" [(ngbCollapse)]="tankGaugeIsCollapsed" class="card-body">
        <app-label-text-button id="tankGaugeIpAddress" labelText="IP address" controlName="TankGaugeIpAddress"
                               (action)="setTankGaugeIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.TankGaugeIpAddress" [errorInActionText]="errors.TankGaugeIpAddress">
        </app-label-text-button>
        <app-label-text-button id="tankGaugePort" *ngIf="pumpType == 'HSC'" labelText="Port" controlName="TankGaugePort"
                               (action)="setTankGaugePort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.TankGaugePort" [errorInActionText]="errors.TankGaugePort">
        </app-label-text-button>
      </div>
    </div>
      <!-- Secondary Auth link -->
      <div class="card form-group">
        <div class="card-header">
          <button type="button" (click)="secAuthCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
            <span>Secondary Auth [{{secAuthType}}]</span>
            <div>
              <span class="badge mx-2" id="secAuthConnected" [ngClass]="connectionsData?.IsSecAuthConnected ? 'badge-success' : 'badge-danger'">
                {{connectionsData?.IsSecAuthConnected ? 'Connected' : 'Not connected'}}
              </span>
              <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':secAuthIsCollapsed,'bi-chevron-up':secAuthIsCollapsed===false }"></i>
            </div>
          </button>
        </div>
        <div #secAuthCollapse="ngbCollapse" [(ngbCollapse)]="secAuthIsCollapsed" class="card-body">
          <div class="card form-group" *ngIf="secAuthType === constants.SECAUTH_TYPE_ANPR">
            <div #anprSecAuthCollapse="ngbCollapse" [(ngbCollapse)]="secAuthIsCollapsed" class="card-body">
                <app-label-text-button id="anprIpAddress" labelText="IP address" controlName="AnprIpAddress"
                                 (action)="setAnprIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                                 buttonColClass="col-auto" [errorInAction]="errors.AnprIpAddress" [errorInActionText]="errors.AnprIpAddress">
              </app-label-text-button>
              <app-label-text-button id="anprPort" labelText="Port" controlName="AnprPort"
                                    (action)="setAnprPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                                    buttonColClass="col-auto" [errorInAction]="errors.AnprPort" [errorInActionText]="errors.AnprPort">
              </app-label-text-button>
            </div>
          </div>
          <div class="card form-group" *ngIf="secAuthType === constants.SECAUTH_TYPE_SIGNALR_PRE || secAuthType === constants.SECAUTH_TYPE_SIGNALR_POST">
            <div #signalRSecAuthCollapse="ngbCollapse" [(ngbCollapse)]="secAuthIsCollapsed" class="card-body">
              <div class="row form-group" *ngIf="connectionsData?.IsSecAuthConnected">
                <div class="input-group">
                  <label class="col-form-label col-3">IP list</label>
                  <div class="col-7">
                    <label class="mr-3 col-form-label" *ngFor="let ip of connectionsData?.SignalRSecAuthIpAddresses">{{ip}}</label>
                  </div>
                </div>
              </div>      
            </div>
          </div>
        </div>
      </div>
    <!-- Car wash -->
    <div class="card form-group" *ngIf="connectionFlags?.CarWashConnectionThread === true">
      <div class="card-header">
        <button type="button" (click)="carWashCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Car wash</span>
          <div>
            <span class="badge mx-2" id="carWashConnected" [ngClass]="connectionsData?.CarWashConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.CarWashConnected ? 'Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':carWashIsCollapsed,'bi-chevron-up':carWashIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #carWashCollapse="ngbCollapse" [(ngbCollapse)]="carWashIsCollapsed" class="card-body">
        <app-label-text-button id="carWashIpAddress" labelText="IP address" controlName="CarWashIpAddress"
                               (action)="setCarWashIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.CarWashIpAddress" [errorInActionText]="errors.CarWashIpAddress">
        </app-label-text-button>
        <app-label-text-button id="carWashPort" labelText="Port" controlName="CarWashPort"
                               (action)="setCarWashPort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.CarWashPort" [errorInActionText]="errors.CarWashPort">
        </app-label-text-button>
      </div>
    </div>
    <!-- Hydra mobile -->
    <div class="card form-group" *ngIf="connectionFlags?.HydraMobileConnectionThread === true">
      <div class="card-header">
        <button type="button" (click)="hydraMobileCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
          <span>Hydra Mobile (POS)</span>
          <div>
            <span class="badge mx-2" id="hydraMobileConnected" [ngClass]="connectionsData?.HydraMobileConnected ? 'badge-success' : 'badge-danger'">
              {{connectionsData?.HydraMobileConnected ? 'Connected' : 'Not connected'}}
            </span>
            <i class="bi" style="font-size: 1.5rem;line-height: 1.5rem;" [ngClass]="{'bi-chevron-down':hydraMobileIsCollapsed,'bi-chevron-up':hydraMobileIsCollapsed===false }"></i>
          </div>
        </button>
      </div>
      <div #hydraMobileCollapse="ngbCollapse" [(ngbCollapse)]="hydraMobileIsCollapsed" class="card-body">
        <app-label-text-button id="hydraMobileIpAddress" labelText="IP address" controlName="HydraMobileIpAddress"
                               (action)="setHydraMobileIpAddress()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.HydraMobileIpAddress" [errorInActionText]="errors.HydraMobileIpAddress">
        </app-label-text-button>
        <app-label-text-button id="hydraMobilePort" labelText="Port" controlName="HydraMobilePort"
                               (action)="setHydraMobilePort()" [formGroup]="connectionsForm" labelColClass="col-3" textColClass="col-3"
                               buttonColClass="col-auto" [errorInAction]="errors.HydraMobilePort" [errorInActionText]="errors.HydraMobilePort">
        </app-label-text-button>
      </div>
    </div>
  </form>
</ng-container>


