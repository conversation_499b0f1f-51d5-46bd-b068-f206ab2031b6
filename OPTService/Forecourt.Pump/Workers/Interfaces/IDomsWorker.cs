using Forecourt.Core.UpdateFileClasses;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Pump.Workers.Interfaces
{
    public interface IDomsWorker : IWorkerable
    {
        IPAddress IpAddress { get; }
        string LoginString { get; }
        string SoftwareVersion { get; }
        string SoftwareDate { get; }

        int BackoffStopStart { get; set; }
        int BackoffStopOnly { get; set; }
        
        /// <summary>Fetch the current state of the DOMS unit.</summary>
        /// <returns>Current state.</returns>
        bool StateEnabled { get; }

        bool StateConnected { get; }

        bool Enabled { get; }
        bool Connected { get; }

        /// <summary>Fetch the current state of the DOMS unit.</summary>
        /// <returns>Current state.</returns>
        IDomsSetup FetchedSetup { get; }

        /// <summary>Fetch the current state of the DOMS unit.</summary>
        /// <returns>Current state.</returns>
        // IDomsSetup PreparedSetup { get; }
        IDomsSetup TcpSetup { get; }
        IDomsSetup TcpPreparedSetup { get; }

        DomsState State { get; }
      
        /// <summary>Enable the DOMS Worker.</summary>
        void Enable();

        /// <summary>Disable the DOMS Worker.</summary>
        void Disable();

        /// <summary>Master Reset the DOMS.</summary>
        void MasterReset();

        void SetIpAddress(IPAddress ipAddress);
        void SetLoginString(string loginString);
        void ClearTransaction(byte fpId, int seqNo);
        void OpenPump(byte fpId);
        void ClosePump(byte fpId);
        void Authorise(byte fpId, uint limit);
        bool PaymentApproved(byte fpId, uint limit);
        bool PaymentCancelled(byte fpId);
        bool PaymentCleared(byte fpId, uint limnit);
        bool EmergencyStop(byte fpId);
        bool CancelEmergencyStop(byte fpId);
        void Detect();
        bool SetGradePrices(IList<FuelPriceItem> updates);
        void SetGradeName(byte grade, string text);
        void CheckState();
        void ResetDoms();
        void ReconnectDoms();
    }
}