using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;

namespace Forecourt.Common.Workers.Interfaces
{
    /// <summary>
    /// <see cref="IWorkerable"/> class that acts as a broker between the various workers in the service
    /// </summary>
    public interface IMessageBroker : IWorkerable, 
        IPumpIntegratorInJournal,
        IPumpIntegratorInSetup,
        IPumpIntegratorInTransient<IMessageTracking>,
        IPumpIntegratorOutJournal<IMessageTracking>, 
        IPumpIntegratorOutTransient<IMessageTracking>,
        ITankGaugeIntegratorInJournal,
        ITankGaugeIntegratorOutJournal,
        IBosIntegratorInJournal<IMessageTracking>,
        IBosIntegratorIn<IMessageTracking>,
        IBosIntegratorIn<IMessageTracking, Result<StatusCodeResult>>,
        IPosIntegratorIn<IMessageTracking, IPump, Result>,
        IPosIntegratorIn<IMessageTracking, IPump, Result<StatusCodeResult>>,
        IPosIntegratorOut<IMessageTracking>, 
        IBosIntegratorOut<IMessageTracking>,
        ISecAuthIntegratorInTransient<IMessageTracking>,
        ISecAuthIntegratorOutTransient<IMessageTracking>
    {
        /// <summary>
        /// Checks to see if the selected <see cref="IWorkerable"/> is connected, via <see cref="IConnectable"/>.IsConnected()
        /// </summary>
        /// <returns>Result</returns>
        Result IsConnectableConnected<TWorkerable>() where TWorkerable : IWorkerable;
    }
}

