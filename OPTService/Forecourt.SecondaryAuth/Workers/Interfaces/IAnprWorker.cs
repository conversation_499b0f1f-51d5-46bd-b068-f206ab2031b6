using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;

namespace Forecourt.SecondaryAuth.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the IAnprWorker
    /// </summary>
    public interface IAnprWorker : IClientWorker<string>
    {
        /// <summary>
        /// Send the request to ANPR
        /// </summary>
        /// <param name="pump">The pump</param>
        /// <param name="message">Current loggin reference</param>
        /// <returns>String wrapped Result, indicating any additional info</returns>
        Result<string> SendSecondaryAuthRequest(IPump pump, IMessageTracking message);

    }
}