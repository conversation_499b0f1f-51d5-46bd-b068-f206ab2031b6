using Forecourt.Bos.Factories.Interfaces;
using Forecourt.PaymentConfiguration.Factories.Interfaces;
using Forecourt.Pos.Factories.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.SecondaryAuth.Factories.Interfaces;

namespace Forecourt.Common.Factories.Interfaces
{
    /// <summary>
    /// Interfaces that defines all of the Integration factories
    /// </summary>
    public interface IIntegratorFactories
    {
        /// <summary>
        /// Factory for the Pump integrator
        /// </summary>
        IPumpIntegratorInFactory PumpIntegratorInFactory { get;}

        /// <summary>
        /// Factory for the Tank Gauge integrator
        /// </summary>
        ITankGaugeIntegratorInFactory TankGaugeIntegratorInFactory { get;}

        /// <summary>
        /// Factory for the POS Out integrator
        /// </summary>
        IPosIntegratorOutFactory PosIntegratorOutFactory { get; }

        /// <summary>
        /// Factory for the POS In integrator
        /// </summary>
        IPosIntegratorInFactory PosIntegratorInFactory { get; }

        /// <summary>
        /// Factory for the POS mode change
        /// </summary>
        IPosInModeFactory PosInModeFactory { get; }

        /// <summary>
        /// Factory for the (Mobile) POS integrator
        /// </summary>
        IMobilePosIntegratorInFactory MobilePosIntegratorInFactory { get; }

        /// <summary>
        /// Factory for the (Mobile) BOS integrator
        /// </summary>
        IMobileBosIntegratorInFactory MobileBosIntegratorInFactory { get; }

        /// <summary>
        /// Factory for the BOS (out) integrator
        /// </summary>
        IBosIntegratorOutFactory BosIntegratorOutFactory { get; }

        /// <summary>
        /// Factory for the SecAuth (out) integrator
        /// </summary>
        ISecAuthIntegratorOutFactory SecAuthIntegratorOutFactory { get; }

        /// <summary>
        /// Factory for the Payment Configuration (out) integrator
        /// </summary>
        IPaymentConfigIntegratorFactory PaymentConfigIntegratorOutFactory { get; }
    }
}
