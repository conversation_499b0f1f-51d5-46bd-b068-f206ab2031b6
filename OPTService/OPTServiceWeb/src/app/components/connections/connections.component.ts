import { Component, Input, Ng<PERSON>one, OnInit } from '@angular/core';
import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { forkJoin, merge, of, Subscription } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { AdvancedConfig } from 'src/app/core/models/advancedConfig.model';
import { ConnectionFlags } from 'src/app/core/models/connectionFlags.model';
import { Connections } from 'src/app/core/models/connections.model';
import { GenericOptConfig } from 'src/app/core/models/genericOptConfig.model';
import { Constants } from 'src/app/helpers/constants';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';
import { ConnectionService } from 'src/app/services/connection.service';
import { LoadingService } from 'src/app/services/loading.service';
import { OptService } from 'src/app/services/opt.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { environment } from 'src/environments/environment';

const PATTERN_IP = '^([0-9]{1,3})[.]([0-9]{1,3})[.]([0-9]{1,3})[.]([0-9]{1,3})$';
const PATTERN_IP_EMPTY = '^$|' + PATTERN_IP;
const PATTERN_DIGITS = '[0-9]*';

class Errors {
  ToOptPort: string = '';
  FromOptPort: string = '';
  HeartbeatPort: string = '';
  HydraPosPort: string = '';
  RetalixPosPort: string = '';
  ThirdPartyPosPort: string = '';
  MediaChannelPort: string = '';
  AnprIpAddress: string = '';
  AnprPort: string = '';
  CarWashIpAddress: string = '';
  CarWashPort: string = '';
  PumpControllerIpAddress: string = '';
  PumpControllerPort: string = '';
  PumpControllerLogonInfo: string = '';
  TankGaugeIpAddress: string = '';
  TankGaugePort: string = '';
  HydraMobileIpAddress: string = '';
  HydraMobilePort: string = '';
  RetalixPosPrimaryIpAddress: string = ''; 
  RetalixPosIpAddresses: Map<string, string> = new Map<string, string>();
};

@Component({
  selector: 'app-connections',
  templateUrl: './connections.component.html',
  styleUrls: ['./connections.component.css']
})
export class ConnectionsComponent implements OnInit {

  showContent: boolean = false;

  // Section collapse vars
  optIsCollapsed: boolean = true;
  posIsCollapsed: boolean = true;
  mediaChannelIsCollapsed: boolean = true;
  pumpControllerIsCollapsed: boolean = true;
  secAuthIsCollapsed: boolean = true;
  carWashIsCollapsed: boolean = true;
  tankGaugeIsCollapsed: boolean = true;
  hydraMobileIsCollapsed: boolean = true;
  paymentConfigIsCollapsed: boolean = true;
  bosIsCollapsed: boolean = true;

  @Input()
  connectionsData: Connections | any;

  connectionsForm = this.fb.group({
    ToOptPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    FromOptPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    HeartbeatPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    HydraPosPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    RetalixPosPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    ThirdPartyPosPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    MediaChannelPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    AnprIpAddress: ['', [Validators.required, Validators.pattern(PATTERN_IP)]],
    AnprPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    CarWashIpAddress: ['', [Validators.required, Validators.pattern(PATTERN_IP)]],
    CarWashPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    PumpControllerIpAddress: ['', [Validators.required, Validators.pattern(PATTERN_IP)]],
    PumpControllerPort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    PumpControllerLogonInfo: [''],
    TankGaugeIpAddress: ['', [Validators.required, Validators.pattern(PATTERN_IP)]],
    TankGaugePort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    HydraMobileIpAddress: ['', [Validators.required, Validators.pattern(PATTERN_IP)]],
    HydraMobilePort: [0, [Validators.required, Validators.pattern(PATTERN_DIGITS), Validators.maxLength(5)]],
    OptConnectedCount: [0],
    HydraPosConnectedCount: [0],
    RetalixPosConnectedCount: [0],
    ThirdPartyPosConnectedCount: [0],
    MediaChannelConnectedCount: [0],
    AnprConnected: [false],
    CarWashConnected: [false],
    TankGaugeConnected: [false],
    HydraMobileConnected: [false],
    PumpControllerConnected: [false],
    PaymentConfigConnected: [false],
    AutoAuth: [false],
    MediaChannel: [false],
    UnmannedPseudoPos: [false],
    RetalixDefined: [false],
    OptIpAddresses: [''],
    HydraPosIpAddresses: [''],
    RetalixPosIpAddresses: [''],
    ThirdPartyPosIpAddresses: [''],
    MediaChannelIpAddresses: [''],
    RetalixPosPrimaryIpAddress: ['', [Validators.pattern(PATTERN_IP_EMPTY)]]
  });
  connectionFlags: ConnectionFlags;

  signalRDataSubscription: Subscription | undefined;
  configDataSubscription: Subscription | undefined;
  forceRefreshConnections: boolean = true;

  errors: Errors = new Errors;

  posType: string;
  bosType: string;
  pumpType: string;
  secAuthType: string;
  paymentConfigType: string;

  get constants(): typeof Constants {
    return Constants;
  }

  constructor(    
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private connectionService: ConnectionService,
    private advancedConfigService: AdvancedConfigService,
    private optService: OptService,
    private logger: NGXLogger,
    private loadingService: LoadingService
  ) { 
      this.signalRDataSubscription = this.signalRService.getConnectionSignalRMessage().subscribe(() => {
        this.zone.run(() => {
          this.refreshData(true, true);
        });
      });
      this.configDataSubscription = this.advancedConfigService.getConfigDataChanged().subscribe(() => {
        this.zone.run(() => {
          this.refreshData(true, false);
          this.forceRefreshConnections = false;
        });
      });
  }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {
    setTimeout(() => {
      this.refreshData(true, this.forceRefreshConnections);
    }, 100);
  }

  /**
   * ngOnDestroy angular hook
   */
  ngOnDestroy(): void {
    this.signalRDataSubscription?.unsubscribe();
    this.configDataSubscription?.unsubscribe();
  }

  /**
   * The refresh data method called when received a push from signalR
   */
  refreshData(showLoading: boolean = true, forceConnectionsReload: boolean = false): Subscription {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen(); 
    }

    const advancedConfigObserver = this.advancedConfigService.getAdvancedConfig().pipe(
      catchError(() => {
        this.logger.error('Problem getting advanced configuration');
        return of(undefined as AdvancedConfig);
      })
    );

    const connectionsObserver = this.connectionService.getConnections(forceConnectionsReload).pipe(
      catchError(() => {
        this.logger.error('Problem getting connection details');
        return of(undefined as GenericOptConfig);
      })
    );

    return forkJoin([advancedConfigObserver, connectionsObserver])
      .pipe(
        finalize(() => {
          if (showLoading) {
            // Hide loading screen
            this.loadingService.hideLoadingScreen();
            this.showContent = true;
          }
        })
      )
      .subscribe(([advancedConfig, connections]) => {
          // Advanced Config
          if (advancedConfig !== undefined) {
            this.logger.debug('All advanced configuration data', advancedConfig);
            this.posType = advancedConfig.PosType.toUpperCase();
            this.bosType = advancedConfig.BosType.toUpperCase();
            this.pumpType = advancedConfig.PumpType;
            this.secAuthType = advancedConfig.SecAuthType;
            this.paymentConfigType = advancedConfig.PaymentConfigType;
            this.setConnectionFlags(advancedConfig);
          }

          // Connections
          if (connections !== undefined) {
            this.logger.debug('All connection details data', connections);
            this.connectionsData = JSON.parse(JSON.stringify(connections));
            Object.keys(this.connectionsForm.controls).forEach(key => {
              if(this.connectionsForm.controls[key].dirty){
                delete connections[key];
                this.connectionsData[key] = this.connectionsForm.controls[key].value;
              }
            });
            this.connectionsForm.patchValue(connections);
            Object.keys(this.connectionsForm.controls).forEach(key => {
              this.connectionsForm.controls[key].valueChanges.subscribe(val => {
                this.connectionsData[key] = val;
              });
            });
          }
      });
  }

  /**
   * Sets connection feature flags to show/hide sections in the HTML
   */
  setConnectionFlags(advancedConfig: AdvancedConfig): void {
    this.connectionFlags = new ConnectionFlags();
    let settings = advancedConfig?.ConfigurationCategories?.find(cat => cat.Category === ConnectionFlags.CATEGORY)?.Settings;
    if (settings) {
      Object.keys(settings)
        .filter(key => key.startsWith(ConnectionFlags.SETTING_PREFIX))
        .forEach(key => {
          const property = key.replace(ConnectionFlags.SETTING_PREFIX, '');
          this.connectionFlags[property] = settings[key]?.Value?.toLowerCase() === 'true';
        });
    }
  }

  /**
   * Checks if there is any error when setting a given IP in RetalixPosIpAddresses as primary
   */
  checkError(ip: string) {
    if (this.errors.RetalixPosIpAddresses.has(ip)) {
      return true;
    };
    return false;
  }

  /**
   * Retrieves any error description when setting a given IP in RetalixPosIpAddresses as primary
   */
  getErrorDescription(ip: string) {
    return this.errors.RetalixPosIpAddresses.get(ip);
  }

  /**
   * Returns the formatted error description
   * @param error HTTP error object
   */
  getServiceErrorText(error: any) {
    return (error.statusText ? error.statusText + ': ': '') + error.error;
  }
  
  /**
   * Updates FromOptPort property
   */
  setFromOptPort() {
    this.logger.info('Updating FromOptPort');

    this.connectionService.setProperty("", this.connectionsData.FromOptPort, environment.connectionController.setFromOptPort).subscribe(result => {
      this.logger.debug('FromOptPort updated ok');
      this.errors.FromOptPort = '';
    }, error => {
      this.logger.error('Problem updating FromOptPort ' + error.message);
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.FromOptPort = message;
    });
  }

  /**
   * Updates ToOptPort property
   */
  setToOptPort() {
    this.logger.info('Updating ToOptPort');
    this.connectionService.setProperty("", this.connectionsData.ToOptPort, environment.connectionController.setToOptPort).subscribe(result => {
      this.logger.debug('ToOptPort updated ok');
      this.errors.ToOptPort = '';
    }, error => {
      this.logger.error('Problem updating ToOptPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.ToOptPort = message;
    });
  }
  
  /**
   * Updates HeartbeatPort property
   */
  setHeartbeatPort() {
    this.logger.info('Updating HeartbeatPort');
    this.connectionService.setProperty("", this.connectionsData.HeartbeatPort, environment.connectionController.setHeartbeatPort).subscribe(result => {
      this.logger.debug('HeartbeatPort updated ok');
      this.errors.HeartbeatPort = '';
    }, error => {
      this.logger.error('Problem updating HeartbeatPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.HeartbeatPort = message;
    });
  }

  /**
   * Updates HydraPosPort property
   */
  setHydraPosPort() {
    this.logger.info('Updating HydraPosPort');
    this.connectionService.setProperty("", this.connectionsData.HydraPosPort, environment.connectionController.setHydraPosPort).subscribe(result => {
      this.logger.debug('HydraPosPort updated ok');
      this.errors.HydraPosPort = '';
    }, error => {
      this.logger.error('Problem updating HydraPosPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.HydraPosPort = message;
    });
  }
  
  /**
   * Updates RetalixPosPrimaryIpAddress property
   */
  setRetalixPosPrimaryIpAddress() {
    this.logger.info('Updating RetalixPosPrimaryIpAddress');

    if (this.connectionsData.RetalixPosPrimaryIpAddress) {
      this.connectionService.setProperty(this.connectionsData.RetalixPosPrimaryIpAddress, 0, environment.connectionController.setRetalixPosPrimaryIpAddress).subscribe(result => {
        this.logger.debug('RetalixPosPrimaryIpAddress updated ok');
        this.errors.RetalixPosPrimaryIpAddress = '';
      }, error => {
        this.logger.error('Problem updating RetalixPosPrimaryIpAddress');
        let message: string;
        if (error.message) {
          message = error.message;
        } else {
          message = "Error while setting IP value";
        }
        this.errors.RetalixPosPrimaryIpAddress = message;
      });
    } else {      
      this.connectionService.removeRetalixPosPrimaryIpAddress().subscribe(result => {
        this.logger.debug('RetalixPosPrimaryIpAddress removed ok');
        this.errors.RetalixPosPrimaryIpAddress = '';
      }, error => {
        this.logger.error('Problem removing RetalixPosPrimaryIpAddress');
        let message: string;
        if (error.message) {
          message = error.message;
        } else {
          message = "Error while removing IP value";
        }
        this.errors.RetalixPosPrimaryIpAddress = message;
      });
    }

  }
  
  /**
   * Updates RetalixPosPort property
   */
  setRetalixPosPort() {
    this.logger.info('Updating RetalixPosPort');
    this.connectionService.setProperty("", this.connectionsData.RetalixPosPort, environment.connectionController.setRetalixPosPort).subscribe(result => {
      this.logger.debug('RetalixPosPort updated ok');
      this.errors.RetalixPosPort = '';
    }, error => {
      this.logger.error('Problem updating RetalixPosPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.RetalixPosPort = message;
    });
  }

  /**
   * Updates ThirdPartyPosPort property
   */
  setThirdPartyPosPort() {
    this.logger.info('Updating ThirdPartyPosPort');
    this.connectionService.setProperty("", this.connectionsData.ThirdPartyPosPort, environment.connectionController.setThirdPartyPosPort).subscribe(result => {
      this.logger.debug('ThirdPartyPosPort updated ok');
      this.errors.ThirdPartyPosPort = '';
    }, error => {
      this.logger.error('Problem updating ThirdPartyPosPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.ThirdPartyPosPort = message;
    });
  }
  
  /**
   * Updates MediaChannelPort property
   */
  setMediaChannelPort() {
    this.logger.info('Updating MediaChannelPort');
    this.connectionService.setProperty("", this.connectionsData.MediaChannelPort, environment.connectionController.setMediaChannelPort).subscribe(result => {
      this.logger.debug('MediaChannelPort updated ok');
      this.errors.MediaChannelPort = '';
    }, error => {
      this.logger.error('Problem updating MediaChannelPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.MediaChannelPort = message;
    });
  }
  
  /**
   * Updates PumpControllerIpAddress property
   */
  setPumpControllerIpAddress() {
    this.logger.info('Updating PumpControllerIpAddress');
    this.connectionService.setProperty(this.connectionsData.PumpControllerIpAddress, this.connectionsData.PumpControllerPort, environment.connectionController.setPumpControllerAddress).subscribe(result => {
      this.logger.debug('PumpControllerIpAddress updated ok');
      this.errors.PumpControllerIpAddress = '';
    }, error => {
      this.logger.error('Problem updating PumpControllerIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.PumpControllerIpAddress = message;
    });
  }
  
  /**
   * Updates PumpControllerPort property
   */
  setPumpControllerPort() {
    this.logger.info('Updating PumpControllerPort');
    this.connectionService.setProperty(this.connectionsData.PumpControllerIpAddress, this.connectionsData.PumpControllerPort, environment.connectionController.setPumpControllerAddress).subscribe(result => {
      this.logger.debug('PumpControllerPort updated ok');
      this.errors.PumpControllerPort = '';
    }, error => {
      this.logger.error('Problem updating PumpControllerPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.PumpControllerPort = message;
    });
  }

  /**
   * Updates PumpControllerLogonInfo property
   */
  setPumpControllerLogonInfo() {
    this.logger.info(`Updating PumpControllerLogonInfo: ${this.connectionsData.PumpControllerLogonInfo}`);
    this.connectionService.setPumpControllerLogonInfo(this.connectionsData.PumpControllerLogonInfo).subscribe(result => {
      this.logger.debug('PumpControllerLogonInfo updated ok');
      this.errors.PumpControllerLogonInfo = '';
    }, error => {
      this.logger.error('Problem updating PumpControllerLogonInfo');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting logon info value";
      }
      this.errors.PumpControllerLogonInfo = message;
    });
  }

  /**
   * Updates AnprIpAddress property
   */
  setAnprIpAddress() {
    this.logger.info('Updating AnprIpAddress');
    this.connectionService.setProperty(this.connectionsData.AnprIpAddress, 0, environment.connectionController.setAnprAddress).subscribe(result => {
      this.logger.debug('AnprIpAddress updated ok');
      this.errors.AnprIpAddress = '';
    }, error => {
      this.logger.error('Problem updating AnprIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.AnprIpAddress = message;
    });
  }
  
  /**
   * Updates AnprPort property
   */
  setAnprPort() {
    this.logger.info('Updating AnprPort');
    this.connectionService.setProperty("", this.connectionsData.AnprPort, environment.connectionController.setAnprPort).subscribe(result => {
      this.logger.debug('AnprPort updated ok');
      this.errors.AnprPort = '';
    }, error => {
      this.logger.error('Problem updating AnprPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.AnprPort = message;
    });
  }
  
  /**
   * Updates CarWashIpAddress property
   */
  setCarWashIpAddress() {
    this.logger.info('Updating CarWashIpAddress');
    this.connectionService.setProperty(this.connectionsData.CarWashIpAddress, 0, environment.connectionController.setCarWashAddress).subscribe(result => {
      this.logger.debug('CarWashIpAddress updated ok');
      this.errors.CarWashIpAddress = '';
    }, error => {
      this.logger.error('Problem updating CarWashIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.CarWashIpAddress = message;
    });
  }
  
  /**
   * Updates CarWashPort property
   */
  setCarWashPort() {
    this.logger.info('Updating CarWashPort');
    this.connectionService.setProperty("", this.connectionsData.CarWashPort, environment.connectionController.setCarWashPort).subscribe(result => {
      this.logger.debug('CarWashPort updated ok');
      this.errors.CarWashPort = '';
    }, error => {
      this.logger.error('Problem updating CarWashPort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.CarWashPort = message;
    });
  }
  
  /**
   * Updates TankGaugeIpAddress property
   */
  setTankGaugeIpAddress() {
    this.logger.info('Updating TankGaugeIpAddress');
    this.connectionService.setProperty(this.connectionsData.TankGaugeIpAddress, this.connectionsData.TankGaugePort, environment.connectionController.setTankGaugeAddress).subscribe(result => {
      this.logger.debug('TankGaugeIpAddress updated ok');
      this.errors.TankGaugeIpAddress = '';
    }, error => {
      this.logger.error('Problem updating TankGaugeIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.TankGaugeIpAddress = message;
    });
  }
  
  /**
   * Updates TankGaugePort property
   */
  setTankGaugePort() {
    this.logger.info('Updating TankGaugePort');
    this.connectionService.setProperty(this.connectionsData.TankGaugeIpAddress, this.connectionsData.TankGaugePort, environment.connectionController.setTankGaugeAddress).subscribe(result => {
      this.logger.debug('TankGaugePort updated ok');
      this.errors.TankGaugePort = '';
    }, error => {
      this.logger.error('Problem updating TankGaugePort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.TankGaugePort = message;
    });
  }
  
  /**
   * Updates HydraMobileIpAddress property
   */
  setHydraMobileIpAddress() {
    this.logger.info('Updating HydraMobileIpAddress');
    this.connectionService.setProperty(this.connectionsData.HydraMobileIpAddress, 0, environment.connectionController.setHydraMobileAddress).subscribe(result => {
      this.logger.debug('HydraMobileIpAddress updated ok');
      this.errors.HydraMobileIpAddress = '';
    }, error => {
      this.logger.error('Problem updating HydraMobileIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.HydraMobileIpAddress = message;
    });
  }
  
  /**
   * Updates HydraMobilePort property
   */
  setHydraMobilePort() {
    this.logger.info('Updating HydraMobilePort');
    this.connectionService.setProperty("", this.connectionsData.HydraMobilePort, environment.connectionController.setHydraMobilePort).subscribe(result => {
      this.logger.debug('HydraMobilePort updated ok');
      this.errors.HydraMobilePort = '';
    }, error => {
      this.logger.error('Problem updating HydraMobilePort');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting port value";
      }
      this.errors.HydraMobilePort = message;
    });
  }

  /**
   * Updates RetalixPosPrimaryIpAddress with the given ip value
   */
  setAsPrimary(ip: string) {
    this.logger.info('Set as Primary (RetalixPosPrimaryIpAddress) = ' + ip);

    this.connectionService.setProperty(ip, 0, environment.connectionController.setRetalixPosPrimaryIpAddress).subscribe(result => {
      this.logger.debug('RetalixPosPrimaryIpAddress updated ok');
      this.connectionsData.RetalixPosPrimaryIpAddress = ip;
      this.errors.RetalixPosIpAddresses.delete(ip);
    }, error => {
      this.logger.error('Problem updating RetalixPosPrimaryIpAddress');
      let message: string;
      if (error.error) {
        message = this.getServiceErrorText(error);
      } else {
        message = "Error while setting IP value";
      }
      this.errors.RetalixPosIpAddresses.set(ip, message);
    });
  }

}
