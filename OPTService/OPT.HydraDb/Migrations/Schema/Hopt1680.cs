using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    [Migration(20030001, "HOPT-1680 - Add dbo.OptMode.ReceiptFooter"), Tags(Constants.MigrationTypeSchema)]
    public class Hopt1680: Migrationable
    {
        public override void Up()
        {
            if (!Schema.Table("OptMode").Column("ReceiptFooter").Exists())
            {
                Create.Column("ReceiptFooter").OnTable("OptMode").AsAnsiString(int.MaxValue).Nullable();
            }
        }

        public override void Down()
        {
            if (Schema.Table("OptMode").Column("ReceiptFooter").Exists())
            {
                Delete.Column("ReceiptFooter").FromTable("OptMode");
            }
        }
    }
}
