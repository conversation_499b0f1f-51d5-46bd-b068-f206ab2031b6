import { TestBed } from "@angular/core/testing";
import { LoyaltyService } from "./loyalty.service";

export function LOYALTY_SERVICE_PROVIDER(): { provide: typeof LoyaltyService, useValue: any } {
  return {
    provide: LoyaltyService,
    useValue: jasmine.createSpyObj('LoyaltyService', [
      'addLoyalty', 
      'deleteLoyalty',
      'setLoyaltyAddHostname',
      'setLoyaltyHostsAddHost',
      'setLoyaltyHostsRemoveHost',
      'setLoyaltyIinsRemoveIin',
      'setLoyaltyIinsAddIin',
      'setLoyaltyMappingsAddMapping',
      'setLoyaltyMappingsRemoveMapping',
      'setLoyaltyPresent',
      'setLoyaltyRemoveHostname',
      'setLoyaltyTerminalApiKey',
      'setLoyaltyTerminalFooter1',
      'setLoyaltyTerminalFooter2',
      'setLoyaltyTerminalHttpHeader',
      'setLoyaltyTerminalSiteId',
      'setLoyaltyTerminalTerminalId',
      'setLoyaltyTerminalTimeout',
    ]),
  };
}

export function LOYALTY_SERVICE_SPY(): jasmine.SpyObj<LoyaltyService> {
  return TestBed.inject(LoyaltyService) as jasmine.SpyObj<LoyaltyService>;
}