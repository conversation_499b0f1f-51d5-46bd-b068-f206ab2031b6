using Force.Crc32;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace OPT.Common
{
    public class DllCheck : Loggable
    {
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetModuleHandle(string lpModulName);

        [DllImport("kernel32.dll", SetLastError = true)]
        [PreserveSig]
        private static extern uint GetModuleFileName
            ([In] IntPtr hModule, [Out] StringBuilder lpFilename, [In] [MarshalAs(UnmanagedType.U4)] int nSize);

        private const int MaxSbSize = 1000;

        private readonly string _dllName;

        private bool _running = false;
        private readonly object _lockObject = new object();

        public bool IsAvailable = false;

        public string FileName = string.Empty;

        public uint Crc = 0;

        public string ChecksumString = string.Empty;

        public DllCheck(string dllName, IHtecLogger logger)
            : base(logger)
        {
            _dllName = dllName;
            GetLogger().Info($"DLL Check initialised with file name {_dllName}");
        }

        private string GetFileName(string dllName)
        {
            if (dllName.Contains($"{Path.DirectorySeparatorChar}"))
            {
                GetLogger().Info($"DLL file name is {dllName}");
                return dllName;
            }
            else
            {
                IntPtr handle = IntPtr.Zero;
                try
                {
                    handle = GetModuleHandle(dllName);
                }
                catch (Exception e)
                {
                    GetLogger().Error("Error getting module handle", e);
                }

                if (!handle.Equals(IntPtr.Zero))
                {
                    StringBuilder sb = new StringBuilder(MaxSbSize);
                    uint size = 0;
                    try
                    {
                        size = GetModuleFileName(handle, sb, sb.Capacity);
                    }
                    catch (Exception e)
                    {
                        GetLogger().Error("Error getting module file name", e);
                    }

                    if (size > 0)
                    {
                        GetLogger().Info($"DLL file name is {sb}");
                        return sb.ToString();
                    }
                    else
                    {
                        GetLogger().Debug($"Get Module File Name size is zero for {dllName}");
                    }
                }
                else
                {
                    GetLogger().Debug($"Get Module Handle is zero for {dllName}");
                }
            }

            return string.Empty;
        }


        public void Process()
        {
            GetLogger().Info($"DLL Check processing with file name {_dllName}");
            bool run = false;
            lock (_lockObject)
            {
                if (!_running)
                {
                    _running = true;
                    run = true;
                }
            }

            if (run)
            {
                FileName = GetFileName(_dllName);

                if (!string.IsNullOrEmpty(FileName))
                {
                    try
                    {
                        byte[] bytes = File.ReadAllBytes(FileName);
                        Crc = Crc32Algorithm.Compute(bytes);
                        ChecksumString = $"{Crc:X8}";
                        IsAvailable = true;
                    }
                    catch (Exception e)
                    {
                        GetLogger().Error("Error reading DLL for checksum", e);
                    }
                }

                lock (_lockObject)
                {
                    _running = false;
                }
            }
        }
    }
}