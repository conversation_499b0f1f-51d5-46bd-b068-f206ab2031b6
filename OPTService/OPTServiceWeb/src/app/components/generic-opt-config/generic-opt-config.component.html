<ng-container *ngIf="showContent">
    <div class="d-flex justify-content-center mt-2 mb-2">
        <div class="mr-1">
            <button id="reloadOptConfigBtn" type="button" class="btn btn-primary col-auto" (click)="reloadOptConfiguration($event)">Reload</button>
            <span id="reloadOptConfigError" class="text-danger pt-1 col-auto" placement="right top" ngbTooltip="Could not reload"
            *ngIf="reloadOptConfigurationError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
        <div class="ml-1">
            <button id="toggleConfigBatchBtn" type="button" class="btn btn-primary col-auto" (click)="toggleIsConfigBatch($event)">{{isConfigBatch?'End':'Start'}} batch</button>
            <span id="toggleConfigBatchError" class="text-danger pt-1 col-auto" placement="right top" ngbTooltip="Could not get batch status"
            *ngIf="batchError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>   
    </div>
    <form [formGroup]="genericOptConfigForm" *ngIf="genericOptConfigFormError === false">
        <div id="card-config-endpoints" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="endpointsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Endpoints</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':endpointsIsCollapsed,'bi-chevron-up':endpointsIsCollapsed===false}"></i>
                </button>
            </div>
            <div #endpointsCollapse="ngbCollapse" [(ngbCollapse)]="endpointsIsCollapsed" class="card-body">
                <app-label-text-button id="serviceAddress" labelColClass="col-12 col-md-2 col-xl-2"
                    textColClass="col-12 col-md-6 col-xl-3" labelText="Service address" controlName="ServiceAddress"
                    [errorInAction]="updateServiceAddressError" (action)="updateServiceAddress($event)"
                    [formGroup]="genericOptConfigForm" maxLength="15"></app-label-text-button>
                <hr />
                <div formArrayName="EsocketEndPoints">
                    <div class="row form-group">
                        <button id="addESocketBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddEsocketForm(addEsocket)">
                            <span class="btn-label"> <i class="bi bi-plus"></i></span>Add eSocket.POS endpoint
                        </button>
                    </div>
                    <div class="row form-group">
                        <div *ngFor="let item of esocketEndPoints.controls; let i = index" class="col-12">
                            <div [formGroupName]="i" class="row form-group">
                                <div class="input-group col-12 col-md-2 col-xl-2">
                                    <label for="eSocketIpAddress{{i}}" class="col-form-label text-truncate">eSocket.POS endpoint</label>
                                </div>
                                <div class="input-group col-12 col-md-3 col-xl-2">
                                    <input id="eSocketIpAddress{{i}}" type="text" formControlName="IpAddress" class="form-control" readonly>
                                </div>
                                <span>:</span>
                                <div class="input-group col-12 col-md-3 col-xl-2">
                                    <input id="eSocketPort{{i}}" type="text" formControlName="Port" class="form-control" readonly>
                                </div>
                                <div class="col-auto">
                                    <button id="eSocketRemoveBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeEsocket(i,$event)"><span
                                            class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                                    <span id="eSocketRemoveError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                                        *ngIf="item.invalid"><i class="bi bi-exclamation-triangle"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <app-card-divert-opt></app-card-divert-opt>
            </div>
        </div>
        <div id="card-config-prod-code-map" class="card form-group">
            <div class="card-header">
                <button id="addProdCodeMapBtn" type="button" (click)="tariffMappingsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Product code mappings</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':tariffMappingsIsCollapsed,'bi-chevron-up':tariffMappingsIsCollapsed===false }"></i>
                </button>
            </div>
            <div #tariffMappingsCollapse="ngbCollapse" [(ngbCollapse)]="tariffMappingsIsCollapsed" class="card-body" formArrayName="TariffMappings">
                <div class="row form-group">
                    <button id="addProdCodeMapBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddTariffMappingForm(addTariffMapping)">
                        <span class="btn-label"><i class="bi bi-plus"></i></span>Add map</button>
                </div>
                <div *ngFor="let item of tariffMappings.controls; let i = index">
                    <div [formGroupName]="i" class="form-group row row-config-prod-code-map">
                        <div class="input-group col-12 col-md-6 col-xl-3">
                            <input id="fullGradeInput{{i}}" type="text" formControlName="FullGrade" class="form-control" readonly>
                        </div>
                        <span><i class="bi bi-arrow-right" style="font-size:1.5rem; line-height:1.5rem;"></i></span>
                        <div class="input-group col-12 col-md-6 col-xl-3">
                            <input id="fullProductCodeInput{{i}}" type="text" formControlName="FullProductCode" class="form-control" readonly>
                        </div>
                        <div class="custom-control custom-switch form-group col-auto">
                        <input class="custom-control-input" type="checkbox" formControlName="FuelCardsOnly" [id]="'fuel-cards-only-' + i" (change)="setTariffMappingFuelCardsOnly(i,$event)">
                        <label class="custom-control-label" [for]="'fuel-cards-only-' + i">Fuel cards only</label>
                        </div>                    
                        <div class="col-auto">
                            <button id="removeProdCodeMapBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeTariffMapping(i,$event)"><span
                                    class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                            <span id="removeProdCodeMapError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                                *ngIf="item.invalid"><i class="bi bi-exclamation-triangle"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="card-config-discount-cards" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="discountCardsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Discount cards</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':discountCardsIsCollapsed,'bi-chevron-up':discountCardsIsCollapsed===false }"></i>
                </button>
            </div>
            <div class="card-body" #discountCardsCollapse="ngbCollapse" [(ngbCollapse)]="discountCardsIsCollapsed" formArrayName="DiscountCards">
                <div class="row form-group">
                    <button id="addDiscountCardBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddDiscountCardForm(addDiscountCard)"><span
                            class="btn-label"><i class="bi bi-plus"></i></span>Add discount card</button>
                </div>
                <ngb-accordion [closeOthers]="true" [activeIds]="activeDiscountCard">
                        <ngb-panel *ngFor="let item of discountCards.controls; let i = index" [formGroupName]="i" [id]="'iin' + item.get('Iin').value" (panelChange)="toggleDiscountCards($event)">
                            <ng-template ngbPanelHeader>
                                <div class="d-flex align-items-center justify-content-between">
                                    <button ngbPanelToggle
                                        class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                                        <span>IIN {{item.get('Iin').value}}</span>
                                    </button>
                                </div>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <app-label-text-button id="discountCardNameInput{{i}}" labelColClass="col-12 col-md-2 col-xl-2"
                                    textColClass="col-12 col-md-6 col-xl-3" labelText="Name" controlName="Name" [errorInAction]="item.get('NameError').value"
                                    (action)="updateDiscountCard(i,'Name',$event)" [formGroup]="item" maxLength="10">
                                </app-label-text-button>
                                <div class="row form-group">
                                    <div class="input-group" class="col-12 col-md-2 col-xl-2">
                                        <label class="col-form-label" for="discountCardTypeInput{{i}}">Type</label>
                                    </div>
                                    <div class="input-group col-12 col-md-6 col-xl-3">
                                        <select id="discountCardTypeInput{{i}}" formControlName="Type" class="custom-select">
                                            <option value="P1">P1</option>
                                            <option value="F1">F1</option>
                                            <option value="F2">F2</option>
                                        </select>
                                    </div>
                                    <div class="col-auto">
                                        <button id="discountCardTypeBtn{{i}}" type="button" class="btn btn-primary" (click)="updateDiscountCard(i,'Type',$event)">Save</button>
                                        <span id="discountCardTypeError{{i}}" class="text-danger ml-2 pt-1" placement="right top" ngbTooltip="Field could not be updated"
                                            *ngIf="item.get('TypeError').value"><i class="bi bi-exclamation-triangle"></i></span>
                                    </div>
                                </div>
                                <app-label-text-button id="discountCardValueInput{{i}}" labelColClass="col-12 col-md-2 col-xl-2"
                                    textColClass="col-12 col-md-6 col-xl-3" labelText="Value" controlName="Value" [errorInAction]="item.get('ValueError').value"
                                    (action)="updateDiscountCard(i,'Value',$event)" [formGroup]="item" maxLength="10">
                                </app-label-text-button>
                                <app-label-text-button id="discountCardGradeInput{{i}}" labelColClass="col-12 col-md-2 col-xl-2"
                                    textColClass="col-12 col-md-6 col-xl-3" labelText="Grade" controlName="Grade" [errorInAction]="item.get('GradeError').value"
                                    (action)="updateDiscountCard(i,'Grade',$event)" [formGroup]="item" maxLength="10"
                                    *ngIf="item.get('OriginalType').value !== 'P1' && item.get('OriginalType').value !== 'F1'">
                                </app-label-text-button>
                                <div class="row form-group d-flex justify-content-center">
                                    <button id="removeDiscountCardBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeDiscountCard(i,$event)"><span
                                            class="btn-label"><i class="bi bi-trash"></i></span>Remove card</button>
                                    <span id="removeDiscountCardError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated" *ngIf="item.get('RemoveError').value"><i
                                            class="bi bi-exclamation-triangle"></i></span>
                                </div>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
            </div>
        </div>
        <div id="card-config-loyalty" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="loyaltyAvailabilityCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Loyalty</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':loyaltyAvailabilityIsCollapsed,'bi-chevron-up':loyaltyAvailabilityIsCollapsed===false }"></i>
                </button>
            </div>
            <div #loyaltyAvailabilityCollapse="ngbCollapse" [(ngbCollapse)]="loyaltyAvailabilityIsCollapsed" class="card-body">
              <app-switch-label id="morrisonsLoyalty" labelText="Loyalty Schemes Enabled" controlName="MorrisonLoyaltyAvailable"
                                [errorInAction]="morrisonsLoyaltyError" (action)="updateLoyalty('Morrisons',$event)"
                                [formGroup]="genericOptConfigForm"></app-switch-label>
              <div id="card-config-loyalty-schemes" class="card form-group">
                <div class="card-header">
                  <button type="button" (click)="loyaltySchemesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Loyalty schemes</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':loyaltySchemesIsCollapsed,'bi-chevron-up':loyaltySchemesIsCollapsed===false }"></i>
                  </button>
                </div>
                <div class="card-body" #loyaltySchemesCollapse="ngbCollapse" [(ngbCollapse)]="loyaltySchemesIsCollapsed" formArrayName="Loyalty">
                  <ngb-accordion [closeOthers]="true" [activeIds]="activeLoyaltyScheme">
                    <ngb-panel *ngFor="let item of loyaltySchemes.controls; let i = index" [formGroupName]="i" [id]="'loyalty-' + item.get('Name').value" (panelChange)="toggleLoyaltyScheme($event)">
                      <ng-template ngbPanelHeader>
                        <div class="d-flex align-items-center justify-content-between">
                          <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                            <span>{{item.get('Name').value}} loyalty</span>
                          </button>
                        </div>
                      </ng-template>
                      <ng-template ngbPanelContent>
                        <app-loyalty-scheme [form]="item" [activeSection]="activeLoyaltySection" (activeSectionChangeEvent)=toggleActiveLoyaltySection($event)></app-loyalty-scheme>
                      </ng-template>
                    </ngb-panel>
                  </ngb-accordion>
                </div>
              </div>
            </div>
        </div>
        <div id="card-config-washes" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="washesCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Washes</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':washesIsCollapsed,'bi-chevron-up':washesIsCollapsed===false }"></i>
                </button>
            </div>
            <div class="card-body" #washesCollapse="ngbCollapse" [(ngbCollapse)]="washesIsCollapsed" formArrayName="Washes">
                <div class="row form-group">
                    <button id="addWashBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddWashingProgram(addWashingProgram)"><span
                            class="btn-label"><i class="bi bi-plus"></i></span>Add washing program</button>
                </div>
                <ngb-accordion [closeOthers]="true" [activeIds]="activeWash" (panelChange)="toggleWash($event)">
                        <ngb-panel *ngFor="let item of washes.controls; let i = index" [formGroupName]="i" [id]="'wash-' + item.get('ProgramId').value">
                            <ng-template ngbPanelHeader>
                                <div class="d-flex align-items-center justify-content-between">
                                    <button ngbPanelToggle
                                        class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                                        <span>{{item.get('Description').value}} (Program Id {{item.get('ProgramId').value}})</span>
                                    </button>
                                </div>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                    id="washProductCodeInput{{i}}" labelText="Product code" controlName="ProductCode">
                                </app-label-text>
                                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                    id="washPriceInput{{i}}" labelText="Price" controlName="FormattedPrice">
                                </app-label-text>
                                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                    id="washVatRateInput{{i}}" labelText="VAT Rate" controlName="VatRate" appendText="%">
                                </app-label-text>
                                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                    id="washCategoryInput{{i}}" labelText="Category" controlName="Category">
                                </app-label-text>
                                <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                    id="washSubcategoryInput{{i}}" labelText="Subcategory" controlName="Subcategory">
                                </app-label-text>
                                <div class="row form-group d-flex justify-content-center">
                                    <button id="removeWashBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeWashingProgram(i,$event)"><span
                                            class="btn-label"><i class="bi bi-trash"></i></span>Remove wash</button>
                                    <span id="removeWashError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated" *ngIf="item.get('RemoveError').value"><i
                                            class="bi bi-exclamation-triangle"></i></span>
                                </div>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
            </div>
        </div>
        <div id="card-config-predefined-amounts" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="predefinedAmountsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Predefined amounts</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':predefinedAmountsIsCollapsed,'bi-chevron-up':predefinedAmountsIsCollapsed===false }"></i>
                </button>
            </div>
            <div class="card-body" #predefinedAmountsCollapse="ngbCollapse" [(ngbCollapse)]="predefinedAmountsIsCollapsed" formArrayName="PredefinedAmounts">
                <div class="row form-group">
                    <button id="addPredefinedAmountBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddPredefinedAmountForm(addPredefinedAmount)">
                        <span class="btn-label"><i class="bi bi-plus"></i></span>Add predefined amount</button>
                </div>
                <div *ngFor="let item of predefinedAmounts.controls; let i = index">
                    <div [formGroupName]="i" class="form-group row">
                        <div class="col-12 col-md-2 col-xl-2 input-group">
                            <label for="formattedAmountInput{{i}}">Amount</label>
                        </div> 
                        <div class="input-group col-12 col-md-6 col-xl-3">
                            <input id="formattedAmountInput{{i}}" type="text" formControlName="FormattedAmount" class="form-control" readonly>
                        </div>
                        <div class="col-auto">
                            <button id="formattedAmountBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removePredefinedAmount(i,$event)"><span
                                    class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                            <span id="formattedAmountError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                                *ngIf="item.get('RemoveError').value"><i class="bi bi-exclamation-triangle"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="card-config-receipt-layout" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="receiptLayoutCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Receipt</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':receiptLayoutIsCollapsed,'bi-chevron-up':receiptLayoutIsCollapsed===false }"></i>
                </button>
            </div>
            <div class="card-body" #receiptLayoutCollapse="ngbCollapse" [(ngbCollapse)]="receiptLayoutIsCollapsed">
                <div class="row form-group" [formGroup]="genericOptConfigForm.controls.ReceiptLayoutMode">
                    <div class="input-group" class="col-12 col-md-2 col-xl-2">
                        <label class="col-form-label" for="layoutMode">Layout mode</label>
                    </div>
                    <div class="input-group" class="col-12 col-md-6 col-xl-3">
                        <select id="layoutMode" formControlName="Mode" class="custom-select">
                            <option value="" selected disabled hidden>Unknown</option>
                            <option value="0">Asda</option>
                            <option value="1">Morrisons</option>
                            <option value="2">MFG</option>
                            <option value="3">Independent</option>
                            <option value="4">Other</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button id="layoutModeBtn" type="button" class="btn btn-primary" (click)="updateLayoutMode($event)">Save</button>
                        <span id="layoutModeError" class="text-danger ml-2 pt-1" placement="right top" ngbTooltip="Field couldn't be updated"
                            *ngIf="layoutModeError"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
                <app-label-text-button id="receiptReprintAvailability" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Receipt reprint availability" appendText="sec"
                controlName="ReceiptReprintAvailability" [errorInAction]="receiptReprintAvailabilityError" (action)="updateReceiptReprintAvailability($event)"
                [formGroup]="genericOptConfigForm.controls.ReceiptLayoutMode" maxLength="10"></app-label-text-button>
                <app-label-text-button id="receiptMaxCount" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Receipt maximum count"
                controlName="ReceiptMaxCount" [errorInAction]="receiptMaxCountError" (action)="updateReceiptMaxCount($event)"
                [formGroup]="genericOptConfigForm.controls.ReceiptLayoutMode" maxLength="10"></app-label-text-button>
                
                <hr />

                <app-opt-receipt [formGroup]="genericOptConfigForm.controls.ReceiptLayoutMode" 
                    headerArrayName="ReceiptHeaders" footerArrayName="ReceiptFooters"></app-opt-receipt>
            </div>
        </div>
        <div id="card-config-contactless" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="contactlessCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>Contactless</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':contactlessIsCollapsed,'bi-chevron-up':!contactlessIsCollapsed }"></i>
                </button>
            </div>
            <div class="card-body" #contactlessCollapse="ngbCollapse" [(ngbCollapse)]="contactlessIsCollapsed">
                <app-switch-label id="isEnabled" labelText="Contactless allowed" controlName="IsEnabled"
                    [errorInAction]="contactlessIsEnabledError" (action)="updateContactlessAllowed($event)" [formGroup]="genericOptConfigForm.controls.ContactlessDetails"></app-switch-label>
                <app-switch-label id="showSingleButton" labelText="Show Single Contactless Button" controlName="ShowSingleButton"
                    [errorInAction]="contactlessSingleButtonError" (action)="updateContactlessSingleButton($event)" [formGroup]="genericOptConfigForm.controls.ContactlessDetails"></app-switch-label>
                <app-label-text-button id="cardPreAuthLimit" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Card preauth limit" controlName="FormattedCardPreAuthLimit"
                [errorInAction]="contactlessCardPreAuthLimitError" (action)="updateContactlessCardPreAuthLimit($event)"
                [formGroup]="genericOptConfigForm.controls.ContactlessDetails" appendText="&pound;"></app-label-text-button>
                <app-label-text-button id="devicePreAuthLimit" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Device preauth limit" controlName="FormattedDevicePreAuthLimit"
                [errorInAction]="contactlessDevicePreAuthLimitError" (action)="updateContactlessDevicePreAuthLimit($event)"
                [formGroup]="genericOptConfigForm.controls.ContactlessDetails" appendText="&pound;"></app-label-text-button>
                <app-label-text-button id="ttq" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="TTQ" controlName="Ttq"
                [errorInAction]="contactlessTtqError" (action)="updateContactlessTtq($event)"
                [formGroup]="genericOptConfigForm.controls.ContactlessDetails"></app-label-text-button>
            </div>
        </div>
        <div id="card-esocket-config" class="card form-group">
            <div class="card-header">
                <button type="button" (click)="eSocketConfigCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                    <span>eSocket config</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':eSocketConfigIsCollapsed,'bi-chevron-up':eSocketConfigIsCollapsed===false}"></i>
                </button>            
            </div>
            <div class="card-body" #eSocketConfigCollapse="ngbCollapse" [(ngbCollapse)]="eSocketConfigIsCollapsed">
                <div id="card-config-term-proc-cat" class="card form-group">
                    <div class="card-header">
                        <button type="button" (click)="termProcCategoryCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <span>Term Proc Category</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':termProcCategoryIsCollapsed,'bi-chevron-up':termProcCategoryIsCollapsed===false }"></i>
                        </button>
                    </div>
                    <div #termProcCategoryCollapse="ngbCollapse" [(ngbCollapse)]="termProcCategoryIsCollapsed" class="card-body">
                        <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                            id="timestamp" labelText="Timestamp" controlName="Timestamp">
                        </app-label-text>
                        <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                            id="termCategory" labelText="Term category" controlName="TermCategory">
                        </app-label-text>
                        <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                            id="procCategory" labelText="Proc category" controlName="ProcCategory">
                        </app-label-text>
                    </div>
                </div>
                <div id="card-config-card-aids" class="card form-group">
                    <div class="card-header">
                        <button type="button" (click)="cardAidsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <span>Card AIDs</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':cardAidsIsCollapsed,'bi-chevron-up':cardAidsIsCollapsed===false }"></i>
                        </button>
                    </div>
                    <div #cardAidsCollapse="ngbCollapse" [(ngbCollapse)]="cardAidsIsCollapsed" class="card-body">
                        <app-card-aids [form]="genericOptConfigForm" formArrayName="CardAids"></app-card-aids>
                    </div>
                </div>
                <div id="card-config-contactless-aids" class="card form-group">
                    <div class="card-header">
                        <button type="button" (click)="cardContactlessAidsCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <span>Card Contactless AIDs</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':cardContactlessAidsIsCollapsed,'bi-chevron-up':cardContactlessAidsIsCollapsed===false }"></i>
                        </button>            
                    </div>
                    <div #cardContactlessAidsCollapse="ngbCollapse" [(ngbCollapse)]="cardContactlessAidsIsCollapsed" class="card-body">
                        <app-card-contactless-aids [form]="genericOptConfigForm" formArrayName="CardClessAids"></app-card-contactless-aids>
                    </div>
                </div>
                <div id="card-config-card-capks" class="card form-group">
                    <div class="card-header">
                        <button type="button" (click)="cardCapksCollapse.toggle()" class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <span>Card CAPKs</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':cardCapksIsCollapsed,'bi-chevron-up':cardCapksIsCollapsed===false }"></i>
                        </button>
                    </div>
                    <div #cardCapksCollapse="ngbCollapse" [(ngbCollapse)]="cardCapksIsCollapsed" class="card-body">
                        <app-card-capks [form]="genericOptConfigForm" formArrayName="CoockedCardCapks"></app-card-capks>
                    </div>
                </div>
                <div id="card-config-fuel-cards" class="card form-group">
                    <div class="card-header">
                        <button type="button" (click)="fuelCardsCollapse.toggle()"
                            class="btn btn-link container-fluid pl-0 d-flex align-items-center justify-content-between">
                            <span>Fuel Cards</span><i class="bi bi-size" [ngClass]="{'bi-chevron-down':fuelCardsIsCollapsed,'bi-chevron-up':fuelCardsIsCollapsed===false }"></i>
                        </button>
                    </div>
                    <div #fuelCardsCollapse="ngbCollapse" [(ngbCollapse)]="fuelCardsIsCollapsed" class="card-body">
                        <app-card-fuel [form]="genericOptConfigForm" formArrayName="FuelCards"></app-card-fuel>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
    <ng-template #addEsocket let-modal>
        <form id="addEsocketForm" [formGroup]="addEsocketForm" (ngSubmit)="onAddEsocketFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add eSocket.POS endpoint</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="idAddress">IP Address</label>
                    <input type="text" class="form-control" id="idAddress" formControlName="IpAddress"
                        [ngClass]="{'is-invalid':addEsocketFormControl.IpAddress.invalid && (addEsocketFormControl.IpAddress.dirty || addEsocketFormControl.IpAddress.touched)}"
                        required aria-describedby="ipAddressFeedback" ngbAutofocus>
                    <div id="ipAddressFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addEsocketFormControl.IpAddress"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="port">Port</label>
                    <input type="text" class="form-control" id="port" formControlName="Port"
                        [ngClass]="{'is-invalid':addEsocketFormControl.Port.invalid && (addEsocketFormControl.Port.dirty || addEsocketFormControl.Port.touched)}"
                        required aria-describedby="portFeedback" ngbAutofocus>
                    <div id="portFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addEsocketFormControl.Port"></app-input-error-messages>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addEsocketForm.invalid">Save</button>
                <span id="addEsocketError" class="text-danger ml-2" placement="right" ngbTooltip="Error adding eSocket.POS endpoint"
                    *ngIf="addEsocketError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>
    
    <ng-template #addTariffMapping let-modal>
        <form id="addTariffMappingForm" [formGroup]="addTariffMappingForm" (ngSubmit)="onAddTariffMappingFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add product code map</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="grade">Grade</label>
                    <input type="text" class="form-control" id="grade" formControlName="Grade"
                        [ngClass]="{'is-invalid':addMapFormControl.Grade.invalid && (addMapFormControl.Grade.dirty || addMapFormControl.Grade.touched)}"
                        required aria-describedby="gradeFeedback" ngbAutofocus>
                    <div id="gradeFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addMapFormControl.Grade"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="productCode">Product code</label>
                    <input type="text" class="form-control" id="productCode" formControlName="ProductCode"
                        [ngClass]="{'is-invalid':addMapFormControl.ProductCode.invalid && (addMapFormControl.ProductCode.dirty || addMapFormControl.ProductCode.touched)}"
                        required aria-describedby="productCodeFeedback" ngbAutofocus>
                    <div id="productCodeFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addMapFormControl.ProductCode"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group custom-control custom-switch">
                    <input class="custom-control-input" type="checkbox" id="fuelCardsOnly" formControlName="FuelCardsOnly">
                    <label class="custom-control-label" for="fuelCardsOnly">Fuel cards only</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addTariffMappingForm.invalid">Save</button>
                <span id="addTariffMappingError" class="text-danger ml-2" placement="right" ngbTooltip="Error adding product code map"
                    *ngIf="addTariffMappingError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>
    
    <ng-template #addDiscountCard let-modal>
        <form id="addDiscountCardForm" [formGroup]="addDiscountCardForm" (ngSubmit)="onAddDiscountCardFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add discount card</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="iin">IIN</label>
                    <input type="text" class="form-control" id="iin" formControlName="Iin"
                        [ngClass]="{'is-invalid':addDiscountCardFormControl.Iin.invalid && (addDiscountCardFormControl.Iin.dirty || addDiscountCardFormControl.Iin.touched)}"
                        required aria-describedby="iinFeedback" ngbAutofocus>
                    <div id="iinFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addDiscountCardFormControl.Iin"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" class="form-control" id="name" formControlName="Name"
                        [ngClass]="{'is-invalid':addDiscountCardFormControl.Name.invalid && (addDiscountCardFormControl.Name.dirty || addDiscountCardFormControl.Name.touched)}"
                        required aria-describedby="nameFeedback" ngbAutofocus>
                    <div id="nameFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addDiscountCardFormControl.Name"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="type">Type</label>
                    <select id="type" formControlName="Type" class="custom-select"
                        [ngClass]="{'is-invalid':addDiscountCardFormControl.Type.invalid && (addDiscountCardFormControl.Type.dirty || addDiscountCardFormControl.Type.touched)}"
                        required aria-describedby="typeFeedback" ngbAutofocus>
                        <option value="P1">P1</option>
                        <option value="F1">F1</option>
                        <option value="F2">F2</option>
                    </select>
                    <div id="typeFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addDiscountCardFormControl.Type"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="value">Value</label>
                    <input type="text" class="form-control" id="value" formControlName="Value"
                        [ngClass]="{'is-invalid':addDiscountCardFormControl.Value.invalid && (addDiscountCardFormControl.Value.dirty || addDiscountCardFormControl.Value.touched)}"
                        required aria-describedby="valueFeedback" ngbAutofocus>
                    <div id="valueFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addDiscountCardFormControl.Value"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="grade">Grade</label>
                    <input type="text" class="form-control" id="grade" formControlName="Grade"
                        [ngClass]="{'is-invalid':addDiscountCardFormControl.Grade.invalid && (addDiscountCardFormControl.Grade.dirty || addDiscountCardFormControl.Grade.touched)}"
                        required aria-describedby="gradeFeedback" ngbAutofocus>
                    <div id="gradeFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addDiscountCardFormControl.Grade"></app-input-error-messages>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addDiscountCardForm.invalid">Save</button>
                <span id="addDiscountCardError" class="text-danger ml-2" placement="right" ngbTooltip="Error adding discount card"
                    *ngIf="addDiscountCardError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>

    <ng-template #addWashingProgram let-modal>
        <form id="addWashingProgramForm" [formGroup]="addWashingProgramForm" (ngSubmit)="onAddWashingProgramFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add washing program</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="programId">Program ID</label>
                    <input type="text" class="form-control" id="programId" formControlName="ProgramId"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.ProgramId.invalid && (addWashingProgramFormControl.ProgramId.dirty || addWashingProgramFormControl.ProgramId.touched)}"
                        required aria-describedby="programIdFeedback" ngbAutofocus>
                    <div id="programIdFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.ProgramId"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="productCode">Product code</label>
                    <input type="text" class="form-control" id="productCode" formControlName="ProductCode"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.ProductCode.invalid && (addWashingProgramFormControl.ProductCode.dirty || addWashingProgramFormControl.ProductCode.touched)}"
                        required aria-describedby="productCodeFeedback" ngbAutofocus>
                    <div id="productCodeFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.ProductCode"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="description">Description</label>
                    <input type="text" class="form-control" id="description" formControlName="Description"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.Description.invalid && (addWashingProgramFormControl.Description.dirty || addWashingProgramFormControl.Description.touched)}"
                        required aria-describedby="descriptionFeedback" ngbAutofocus>
                    <div id="descriptionFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.Description"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="price">Price</label>
                    <input type="text" class="form-control" id="price" formControlName="Price"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.Price.invalid && (addWashingProgramFormControl.Price.dirty || addWashingProgramFormControl.Price.touched)}"
                        required aria-describedby="priceFeedback" ngbAutofocus>
                    <div id="priceFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.Price"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="vatRate">VAT Rate</label>
                    <input type="text" class="form-control" id="vatRate" formControlName="VatRate"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.VatRate.invalid && (addWashingProgramFormControl.VatRate.dirty || addWashingProgramFormControl.VatRate.touched)}"
                        required aria-describedby="vatRateFeedback" ngbAutofocus>
                    <div id="vatRateFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.VatRate"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="category">Category</label>
                    <input type="text" class="form-control" id="category" formControlName="Category"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.Category.invalid && (addWashingProgramFormControl.Category.dirty || addWashingProgramFormControl.Category.touched)}"
                        required aria-describedby="categoryFeedback" ngbAutofocus>
                    <div id="categoryFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.Category"></app-input-error-messages>
                    </div>
                </div>
                <div class="form-group">
                    <label for="subcategory">Subcategory</label>
                    <input type="text" class="form-control" id="subcategory" formControlName="Subcategory"
                        [ngClass]="{'is-invalid':addWashingProgramFormControl.Subcategory.invalid && (addWashingProgramFormControl.Subcategory.dirty || addWashingProgramFormControl.Subcategory.touched)}"
                        required aria-describedby="subcategoryFeedback" ngbAutofocus>
                    <div id="subcategoryFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addWashingProgramFormControl.Subcategory"></app-input-error-messages>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addWashingProgramForm.invalid">Save</button>
                <span id="addWashingProgramError" class="text-danger ml-2" placement="right" ngbTooltip="Error adding washing program"
                    *ngIf="addWashingProgramError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>
    
    <ng-template #addPredefinedAmount let-modal>
        <form id="addPredefinedAmountForm" [formGroup]="addPredefinedAmountForm" (ngSubmit)="onAddPredefinedAmountFormSubmit($event)">
            <div class="modal-header">
                <h4 class="modal-title" id="modal-basic-title">Add predefined amount</h4>
                <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="amount">Amount</label>
                    <input type="text" class="form-control" id="amount" formControlName="Amount"
                        [ngClass]="{'is-invalid':addPredefinedAmountFormControl.Amount.invalid && (addPredefinedAmountFormControl.Amount.dirty || addPredefinedAmountFormControl.Amount.touched)}"
                        required aria-describedby="amountFeedback" ngbAutofocus>
                    <div id="amountFeedback" class="invalid-feedback">
                        <app-input-error-messages [control]="addPredefinedAmountFormControl.Amount"></app-input-error-messages>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" [disabled]="addPredefinedAmountForm.invalid">Save</button>
                <span id="addPredefinedAmountError" class="text-danger ml-2" placement="right" ngbTooltip="Error adding predefined amount"
                    *ngIf="addPredefinedAmountError"><i class="bi bi-exclamation-triangle"></i></span>
            </div>
        </form>
    </ng-template>
</ng-container>
