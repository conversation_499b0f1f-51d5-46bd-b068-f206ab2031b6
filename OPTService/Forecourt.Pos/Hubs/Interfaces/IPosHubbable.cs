using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Pos.Messages;

namespace Forecourt.Pos.Hubs.Interfaces
{
    /// <inheritdoc/>
    public interface IPosHubbable : IHubbable<PosMessage, IMessageTracking>, IPosHubbableIn
    {
    }

    /// <inheritdoc/>
    public interface IPosHubbableIn : IHubbableIn<PosMessage, IMessageTracking>
    {
    }
}
