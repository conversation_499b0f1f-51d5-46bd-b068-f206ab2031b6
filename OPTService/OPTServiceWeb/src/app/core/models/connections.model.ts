export class Connections {
    ToOptPort: number = 0;
    FromOptPort: number = 0;
    HeartbeatPort: number = 0;
    HydraPosPort: number = 0;
    RetalixPosPort: number = 0;
    ThirdPartyPosPort: number = 0;
    MediaChannelPort: number = 0;
    AnprIpAddress: string = '';
    AnprPort: number = 0;
    CarWashIpAddress: string = '';
    CarWashPort: number = 0;
    PumpControllerIpAddress: string = '';
    PumpControllerPort: number = 0;
    PumpControllerLogonInfo: string = '';
    TankGaugeIpAddress: string = '';
    TankGaugePort: number = 0;
    HydraMobileIpAddress: string = '';
    HydraMobilePort: number = 0;
    OptConnectedCount: number = 0;
    HydraPosConnectedCount: number = 0;
    RetalixPosConnectedCount: number = 0;
    ThirdPartyPosConnectedCount: number = 0;
    MediaChannelConnectedCount: number = 0;
    IsSecAuthConnected: boolean = false;
    CarWashConnected: boolean = false;
    TankGaugeConnected: boolean = false;
    HydraMobileConnected: boolean = false;
    PumpControllerConnected: boolean = false;
    PaymentConfigConnected: boolean = false;
    AutoAuth: boolean = false;
    MediaChannel: boolean = false;
    UnmannedPseudoPos: boolean = false;
    RetalixDefined: boolean = false;
    OptIpAddresses: Array<string> = [];
    HydraPosIpAddresses: Array<string> = [];
    RetalixPosIpAddresses: Array<string> = [];
    ThirdPartyPosIpAddresses: Array<string> = [];
    MediaChannelIpAddresses: Array<string> = [];
    RetalixPosPrimaryIpAddress: string = '';
    SignalRPosConnectedCount: number = 0;
    SignalRPosIpAddresses :Array<string> = [];   
    SignalRPosInConnectedCount: number = 0;
    SignalRPosInIpAddresses :Array<string> = [];   
    IsHydraPosConnected : boolean = false;
    IsRetalixPosConnected : boolean = false;
    IsThirdPartyPosConnected : boolean = false;
    IsSignalRPosConnected : boolean = false;
    IsSignalRPosInConnected : boolean = false;
    IsPosConnected : boolean = false;
    SignalRSecAuthConnectedCount: number = 0;
    SignalRSecAuthIpAddresses :Array<string> = [];   
    SignalRBosConnectedCount: number = 0;
    SignalRBosIpAddresses :Array<string> = [];   
    IsSignalRBosConnected : boolean = false;
    IsBosConnected : boolean = false;
}
