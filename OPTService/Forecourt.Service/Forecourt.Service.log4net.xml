<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <root>
    <level value="INFO" />
    <appender-ref ref="File_OPTService" />
    <appender-ref ref="ColoredConsoleAppender" />
  </root>
	<appender name="File_OPTService" type="log4net.Appender.RollingFileAppender">
    <file type="log4net.Util.PatternString">
      <conversionPattern value="C:\\Logs\\%date{yyyy-MM-dd}\\HydraOPTService\\OPTService.log" />
    </file>
    <preserveLogFileNameExtension value="true" />
    <datePattern value="'-'yyyy-MM-dd_HH" />
    <staticLogFileName value="true" />
    <rollingStyle value="Date" />
    <appendToFile value="true" />
    <maxSizeRollBackups value="-1" />
    <countDirection value="0" />
    <filter type="log4net.Filter.LevelRangeFilter">
      <acceptOnMatch value="true" />
      <levelMin value="INFO" />
      <levelMax value="FATAL" />
    </filter>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %property{SiteName} [%22.22thread] %5.5level %-22.22logger - %message%newline" />
    </layout>
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
  </appender>

	<appender name="File_Hsc" type="log4net.Appender.RollingFileAppender">
		<file type="log4net.Util.PatternString">
			<conversionPattern value="C:\\Logs\\%date{yyyy-MM-dd}\\HydraOPTService\\Hsc.log" />
		</file>
		<preserveLogFileNameExtension value="true" />
		<datePattern value="'-'yyyy-MM-dd_HH" />
		<staticLogFileName value="true" />
		<rollingStyle value="Date" />
		<appendToFile value="true" />
		<maxSizeRollBackups value="-1" />
		<countDirection value="0" />
		<filter type="log4net.Filter.LevelRangeFilter">
			<acceptOnMatch value="true" />
			<levelMin value="WARN" />
			<levelMax value="FATAL" />
		</filter>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %property{SiteName} [%22.22thread] %5.5level %-22.22logger - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
	</appender>
  
	<appender name="OPTServiceJournal" type="log4net.Appender.RollingFileAppender">
    <file type="log4net.Util.PatternString" value="C:\HydraOPTService\JournalFiles\OPTJour.txt" />
    <preserveLogFileNameExtension value="true" />
    <datePattern value="'.'yyMMdd" />
    <staticLogFileName value="false" />
    <rollingStyle value="Composite" />
    <appendToFile value="true" />
    <maximumFileSize value="500MB" />
    <maxSizeRollBackups value="2" />
    <filter type="log4net.Filter.LevelRangeFilter">
      <acceptOnMatch value="true" />
      <levelMin value="INFO" />
      <levelMax value="FATAL" />
    </filter>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%message%newline" />
    </layout>
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
  </appender>
  
  <appender name="OPTServiceTelemetry" type="log4net.Appender.RollingFileAppender">
    <file type="log4net.Util.PatternString" value="C:\HydraOPTService\Traces\HydraOPT_Instrumentation.log" />
    <preserveLogFileNameExtension value="true" />
    <datePattern value="'.'yyMMdd" />
    <staticLogFileName value="false" />
    <rollingStyle value="Composite" />
    <appendToFile value="true" />
    <maximumFileSize value="500MB" />
    <maxSizeRollBackups value="2" />
    <filter type="log4net.Filter.LevelRangeFilter">
      <acceptOnMatch value="true" />
      <levelMin value="INFO" />
      <levelMax value="FATAL" />
    </filter>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%message%newline" />
    </layout>
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
  </appender>

  <appender name="ColoredConsoleAppender" type="log4net.Appender.ColoredConsoleAppender" >
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="%date{dd/MM/yyyy HH:mm:ss.fff} [%t] %-5p %c [%property{MethodName}] - %m%n" />
    </layout>
    <mapping>
      <level value="INFO" />
      <forecolor value="Green" />
    </mapping>
    <mapping>
      <level value="WARN" />
      <forecolor value="Yellow" />
    </mapping>
    <mapping>
      <level value="ERROR" />
      <foreColor value="White" />
      <backColor value="Red" />
    </mapping>
    <mapping>
      <level value="FATAL" />
      <foreColor value="White" />
      <backColor value="Red, HighIntensity" />
    </mapping>
    <filter type="log4net.Filter.LevelRangeFilter">
      <acceptOnMatch value="true" />
      <levelMin value="WARN" />
      <levelMax value="FATAL" />
    </filter>
  </appender>

  <logger name="Worker">
    <level value="INFO"/>
  </logger>
  <logger name="Worker.Telemetry">
    <level value="WARN"/>
  </logger>
  <logger name="Worker.Controller">
    <level value="WARN" />
  </logger>
  <logger name="Connection">
    <level value="INFO"/>
  </logger>
  <logger name="Socket">
    <level value="INFO"/>
  </logger>
  <logger name="Socket.Heartbeat">
    <level value="WARN" />
  </logger>
  <logger name="Connection.Heartbeat">
    <level value="WARN"/>
  </logger>
  <logger name="Database">
    <level value="INFO"/>
  </logger>
  <logger name="OptService">
    <level value="INFO"/>
  </logger>
  <logger name="IConfigurationManager">
	  <level value="ERROR" />
  </logger>
  <logger additivity="false" name="Journal">
	  <level value="INFO"/>
	  <appender-ref ref="OPTServiceJournal" />
  </logger>
  <logger additivity="false" name="Telemetry">
	  <level value="INFO"/>
	  <appender-ref ref="OPTServiceTelemetry" />
  </logger>
	<logger additivity="false" name="Hsc">
		<level value="INFO"/>
		<appender-ref ref="File_Hsc" />
	</logger>
</log4net>