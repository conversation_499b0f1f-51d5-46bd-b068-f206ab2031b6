if not exists (select * from sys.databases where name = 'Hydra')
begin
  begin try
    create database Hydra on (filename = 'C:\HydraOPTService\DB\hydra.mdf') for attach
  end try 
  begin catch 
    create database Hydra on primary (name = Hydra, filename = 'C:\HydraOPTService\DB\hydra.mdf')
  end catch
end
go

use Hydra

  if not exists (select 1 from TariffMappings) 
  begin
      insert into TariffMappings (Grade, ProductCode) values (1, '1')
      insert into TariffMappings (Grade, ProductCode) values (2, '2')
      insert into TariffMappings (Grade, ProductCode) values (3, '3')
      insert into TariffMappings (Grade, ProductCode) values (4, '10')
      insert into TariffMappings (Grade, ProductCode, FuelCardsOnly) values (5, '9', 1)
      insert into TariffMappings (Grade, ProductCode) values (6, '4')
      insert into TariffMappings (Grade, ProductCode) values (8, '7')
  end

  if not exists (select 1 from LoyaltyReference) 
  begin
      insert into LoyaltyReference (LoyaltyRef, LoyaltyName, LoyaltyPresent) values (1, 'Morrisons', 1)
  end

  if not exists (select 1 from LoyaltyTerminal) 
  begin
      insert into LoyaltyTerminal (LoyaltyRef, SiteID, TerminalID, Footer1, Footer2, Timeout, ApiKey, HttpHeader)
       values (1, '0000', '75', 'For more details visit', 'www.morrisons.com/more', 30, '86vHcAtHGhVAQhuuiPp8XfTXcJT5sWiN', 'Basic ODZ2SGNBdEhHaFZBUWh1dWlQcDhYZlRYY0pUNXNXaU46MGdRYTNPQW5NVjRSUEdLTg==')
  end

  if not exists (select 1 from LoyaltyHosts) 
  begin
      insert into LoyaltyHosts (LoyaltyRef, IPAddress, Port) values (1, '127.0.0.1', 56001)
  end

  if not exists (select 1 from LoyaltyHostnames) 
  begin
      insert into LoyaltyHostnames (LoyaltyRef, Hostname) values (1, 'https://api.morrisonsplc.co.uk')
  end

  if not exists (select 1 from LoyaltyIINs) 
  begin
      insert into LoyaltyIINs (LoyaltyRef, Low, High) values (1, '98261358', '98261358')
  end

  if not exists (select 1 from LoyaltyMappings) 
  begin
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '1', '29001002')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '2', '29002009')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '3', '29003006')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '4', '29004003')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '5', '29005000')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '6', '29006007')
      insert into LoyaltyMappings (LoyaltyRef, ProductCode, LoyaltyCode) values (1, '8', '29008001')
  end

  if not exists (select 1 from PaymentTimeout) 
  begin
      insert into PaymentTimeout (Mode, Timeout) values (0, 47)
      insert into PaymentTimeout (Mode, Timeout) values (1, 120)
      insert into PaymentTimeout (Mode, Timeout) values (2, 47)
      insert into PaymentTimeout (Mode, Timeout) values (3, 15)
    end

  if not exists (select 1 from SiteInfo) 
  begin
    insert into SiteInfo(Mode, SiteName, VATNumber, NozzleUpForKioskUse, CurrencyCode, TillNumber, FuelCategory) 
    values (1, 'XXXX <SiteName>', '343 4753 55', 0, 826, 75, 7)
  end
  
  if not exists (select 1 from PosClaim) 
  begin
    insert into PosClaim(PosNumber) values (75)
  end

  if not exists (select 1 from NextDayEnd) 
  begin
    insert into NextDayEnd(DayEnd) values (DATEADD(minute, 45, DATEADD(d, 1, DATEDIFF(d, 0, GETDATE()))))
  end

  if not exists (select 1 from ReceiptExpiry) 
  begin
    insert into ReceiptExpiry default values
  end
  
  update FileLocations set ESocketUseConnectionString=0
  
