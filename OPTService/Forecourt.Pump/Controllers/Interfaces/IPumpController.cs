using CSharpFunctionalExtensions;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Pump.Controllers.Interfaces
{
    /// <summary>
    /// Any and all actions of a PumpController, split down in sections to mimic Pump counterparts
    /// </summary>
    public interface IPumpController: IConnectable,
        IPumpControllerConfiguration,
        IPumpControllerSetup, 
        IPumpControllerTransient,
        IPumpControllerJournal
    {
        /// <summary>
        /// Converts the arbitrary decimal amount into a ushort
        /// </summary>
        /// <param name="amount">Decimal amount</param>
        /// <returns>ushort equivalent value</returns>
        uint ToAmount(decimal amount);

        /// <summary>
        /// Converts the arbitrary decimal price into a ushort
        /// </summary>
        /// <param name="price">Decimal price</param>
        /// <returns>ushort equivalent value</returns>
        ushort ToPrice(decimal price);

        /// <summary>
        /// Converts the arbitrary decimal volume into a ushort
        /// </summary>
        /// <param name="volume">Decimal volume</param>
        /// <returns>ushort equivalent value</returns>
        uint ToVolume(decimal volume);
    }

    public interface IPumpControllerJournal
    {
        /// <summary>
        /// Request to fetch Meter information 
        /// </summary>
        /// <param name="now">Date to take into account against pending status and request interval, optional</param>
        /// <param name="loggingReference">Current logging reference</param>
        Result RequestMeters(DateTime? now = null, string loggingReference = null);

        /// <summary>
        /// Request the maximum pump count
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        Result RequestMaxPumps(string loggingReference = null);

        /// <summary>
        /// Request the current Price information
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result RequestPrices(string loggingReference = null);
    }

    public interface IPumpControllerSetup
    {
        /// <summary>
        /// Request to open a pump
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="pump">Pump number</param>        
        void OpenPump(byte pump, string loggingReference);

        /// <summary>
        /// Request to close a pump
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="pump">Pump number</param>
        /// <returns><see cref="Result" /></returns>
        void ClosePump(byte pump, string loggingReference);

        /// <summary>
        /// Reconnect to the Controller
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        void Reconnect(string loggingReference = null);

        /// <summary>
        /// Perform a Reset of the Controller
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        void Reset(string loggingReference = null);

        /// <summary>
        /// Perform a Master Reset of the Controller
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        void MasterReset(string loggingReference = null);
      
        /// <summary>
        /// Check if a disconnection has occurred and take remedial action
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        void CheckForDisconnection(string loggingReference);

        /// <summary>
        /// Check if a login is needed
        /// </summary>
        /// <param name="pos">Pos Claim number</param>
        /// <param name="loggingReference">Current logging reference</param>
        void CheckForLogin(byte pos, string loggingReference);

        /// <summary>
        /// Check if a master reset is needed
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        void CheckForMasterReset(string loggingReference);

        /// <summary>
        /// Check if a state refresh is needed
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        void CheckForRequestPumpData(string loggingReference);

        /// <summary>
        /// Check if a state refresh is needed
        /// </summary>
        /// <param name="fcRequestType">FcRequestType to fetch, default None</param>
        /// <param name="loggingReference">Current logging reference</param>
        void CheckForRequestForecourtData(FetchFcRequestType fcRequestType, string loggingReference);

        /// <summary>
        /// Check if a state refresh is needed
        /// </summary>
        /// <param name="fpId">Fp/Pump Id</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="fpRequestType">FpRequestType to fetch, default None</param>
        void CheckForRequestPumpData(byte fpId, FetchFpRequestType fpRequestType, string loggingReference);
    }

    public interface IPumpControllerConfiguration
    {
        /// <summary>
        /// Log header
        /// </summary>
        string LogHeader { get; }

        /// <summary>
        /// Have the maximum number pumps been received yet
        /// </summary>
        bool GotMaxPumps { get; }

        /// <summary>
        /// Number of pumps, reported by OnMaxPumpsResponse
        /// </summary>
        byte NumberOfPumps { get; }

        /// <summary>
        /// Current endpoint to listen on
        /// </summary>
        GenericEndPoint CurrentEndPoint { get; }

        /// <summary>
        /// Sets the IpAdress 
        /// </summary>
        /// <param name="ipAddress">IpAddress</param>
        /// <param name="port">Port number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns>Result</returns>
        Result SetIpAddress(IPAddress ipAddress, int port, string loggingReference = null);

        /// <summary>
        /// Logon info
        /// </summary>
        string LogonInfo{ get; }

        /// <summary>
        /// Set logon string
        /// </summary>
        /// <param name="logonInfo">Login information</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns>Result</returns>
        Result SetLogonInfo(string logonInfo, string loggingReference = null);

        /// <summary>
        /// Set new grade/fuel prices
        /// </summary>
        /// <param name="gradePrices">List of new fuel/prices</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns>Result</returns>
        Result SetPrices(IEnumerable<Grade> gradePrices, string loggingReference = null);

        /// <summary>
        /// All <see cref="FileVersionInfo"/> supported
        /// </summary>
        /// <returns>Result wrapped list of <see cref="FileVersionInfo"/></returns>
        Result<IEnumerable<FileVersionInfo>> GetVersionInfo();

        /// <summary>
        /// Indicates whether Grade Names are synchonised with HydraDb
        /// </summary>
        bool ShouldSynchroniseGrades { get; }

        /// <summary>
        /// Sets internal Pump grade hose price info for a Pump
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="hose">Hose number</param>
        /// <param name="grade">Grade number</param>
        /// <param name="price">Price</param>
        /// <param name="name">Grade name</param>
        /// <returns>Result</returns>
        Result SetPumpHoseGradePrice(byte pump, byte hose, byte grade, uint price, string name);
    }

    public interface IPumpControllerTransient
    {
        /// <summary>
        /// Request the current Pump State
        /// </summary>
        /// <param name="pump">Pump</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result RequestPumpData(byte pump, string loggingReference);

        /// <summary>
        /// Reserve the pump with a pre-auth limit
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="limit">Pre-auth limit</param>
        /// <param name="pos">POS number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result ReserveWithLimit(byte pump, uint limit, byte pos, string loggingReference);

        /// <summary>
        /// Authorise the pump with a pre-auth limit
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="limit">Pre-auth limit</param>
        /// <param name="pos">POS number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result AuthWithLimit(byte pump, uint limit, byte pos, string loggingReference);

        /// <summary>
        /// Removes any Authorise the pump has
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="pos">POS number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result RemoveAuth(byte pump, byte pos, string loggingReference);

        /// <summary>
        /// Claim the pump transaction by the POS
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="pos">POS number</param>
        /// <param name="tran">transaction number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result Claim(byte pump, byte pos, byte tran, string loggingReference);

        /// <summary>
        /// Release the pump transaction from the POS
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="pos">POS number </param>
        /// <param name="tran">transaction number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result Release(byte pump, byte pos, byte tran, string loggingReference);

        /// <summary>
        /// Un-Reserve the pump from the POS, i.e. remove the Reserve/Preset
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="pos">POS number </param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result RemoveReserve(byte pump, byte pos, string loggingReference);

        /// <summary>
        /// Starts the Cashout process
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="tran">transaction number</param>
        /// <param name="transSeqNum">Transaction Sequence Number</param>
        /// <param name="amount">Trans amount</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="isZeroPaid">ZeroPaid transacton</param>
        /// <returns><see cref="Result" /></returns>
        Result StartCashOut(byte pump, byte tran, int transSeqNum, uint amount, bool isZeroPaid, string loggingReference);

        /// <summary>
        /// Take action when acknowledgeent of payment cleared is received
        /// </summary>
        /// <param name="loggingReference">Current logging reference</param>
        /// <param name="pump">Pump number.</param>
        /// <param name="transSeqNo">Transaction sequence number</param>
        /// <returns><see cref="Result" /></returns>
        Result PaymentClearedOrCancelledAcknowledged(byte pump, int transSeqNo, string loggingReference);

        /// <summary>
        /// Clear the pump transaction, previously claimed by the POS
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="pos">POS number </param>
        /// <param name="tran">transaction number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result Cashout(byte pump, byte pos, byte tran, string loggingReference);

        /// <summary>
        /// Stop the pump (outside of SecAuth/Emergency conditions)
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        void Stop(byte pump, string loggingReference);

        /// <summary>
        /// Update the a Stop status on the pump
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="reg">Any available vehicle registration details</param>
        /// <param name="loggingReference">Current logging reference</param>
        void EmergencyStopUpdate(byte pump, string reg, string loggingReference);

        /// <summary>
        /// Instigate a Stop on the pump
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="reg">Any available vehicle registration details</param>
        /// <param name="loggingReference">Current logging reference</param>
        void EmergencyStop(byte pump, string reg, string loggingReference);

        /// <summary>
        /// Remove any Stop on the pump
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="loggingReference">Current logging reference</param>
        void EmergencyStopCancel(byte pump, string loggingReference);

        /// <summary>
        /// Use to check for any stuck sales, linked to this pump
        /// </summary>
        /// <param name="checkLevels">Levels to check for stuck sales</param>
        /// <param name="pump">Pump number</param>
        /// <param name="isDeliveredPending">Has the Delivered message been ACK'd</param>
        /// <param name="transSeqNum">Transaction sequence number</param>
        /// <param name="amount">Transaction amount</param>
        /// <param name="volume">Transaction volume</param>
        /// <param name="grade">Transaction  grade</param>
        /// <param name="pos">POS number </param>
        /// <param name="loggingReference">Current logging reference</param>
        /// <returns><see cref="Result" /></returns>
        Result CheckForStuckSales((bool, bool) checkLevels, byte pump, bool isDeliveredPending, int transSeqNum, uint amount, uint volume, byte grade, byte pos, string loggingReference);
    }
}
