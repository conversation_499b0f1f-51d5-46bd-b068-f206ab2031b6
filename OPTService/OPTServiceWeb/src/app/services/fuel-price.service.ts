import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class FuelPriceService {

  private baseUrl: string;

/**
   * The FuelPriceService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.fuelPriceController.base;
  }

  /**
   * Gets Fuel Prices
   * @param grade The optional grade parameter.
   */
  public getFuelPrices(grade: string = ''): Observable<any> {
    const url = this.baseUrl + environment.fuelPriceController.getFuelPrices;
    const params = {
      grade: grade
    };
    const options = {
      params: grade ? params : {}
    };

    this.logger.debug(`Getting Fuel Prices via url: ${url} ${grade? ' for grade: ' + grade : ''}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Sets the price for a given grade
   * @param fuelPrice The fuelPrice object to be set 
   */
  public setPrice(fuelPrice: any) {
    const url = this.baseUrl + environment.fuelPriceController.setPrice;
    this.logger.debug(`Setting price ${fuelPrice.PriceToSet} to grade ${fuelPrice.Grade} via ${url}`);
    return this.http.post(url, fuelPrice);
  }

  /**
   * Sets the fuel grade name
   * @param fuelPrice The fuelPrice object to be set 
   */
  public setGradeName(fuelPrice: any) {
    const url = this.baseUrl + environment.fuelPriceController.setGradeName;
    this.logger.debug(`Setting fuel grade name ${fuelPrice.GradeName} to grade ${fuelPrice.Grade} via ${url}`);
    return this.http.post(url, fuelPrice);
  } 

  /**
   * Sets the fuel grade VAT rate for a given grade
   * @param fuelPrice The fuelPrice object to be set 
   */
  public setGradeVatRate(fuelPrice: any) {
    const url = this.baseUrl + environment.fuelPriceController.setGradeVatRate;
    this.logger.debug(`Setting fuel grade VAT rate ${fuelPrice.VatRate} to grade ${fuelPrice.Grade} via ${url}`);
    return this.http.post(url, fuelPrice);
  } 

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
