using FluentMigrator;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Extensions;
using OPT.Common;
using OPT.HydraDb.Common;
using System.Configuration;
using System.Threading;
using cfgConstants = Forecourt.Core.Configuration.Constants;
using intConstants = Forecourt.Core.Configuration.Constants.Integrator;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed by ORBIS integrations (mostly Magical Advanced Config keys)
    /// </summary>
    [Migration(20050024, "ADO#676209 - Set all configuration needed for ORBIS integrations"), Tags(Constants.MigrationTypeData)]
    public class ADO676209_OrbisConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;
        private const string baseUrl = "http://evoserv.evolutiontlm.co.uk:38410";
        private const string baseUrlApi = baseUrl + "/api";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Generice config
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:DomsSetupTcp', @value = '00:00:01', @category = '{cfgConstants.CategoryNameWorkers}'");

            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameORBIS) 
            {
                return;
            }

            // Generice config
            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 0");

            // Integrations 
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'POSTYPE', @value = '{PosType.None}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorPaymentConfig}', @value = '{ConfigConstants.NoneUpper}', @category = '{cfgConstants.CategoryNameSiteInfo}'");

            // Integrations: Bos
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorBos}', @value = '{PosType.GenericSignalRApi}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:SignalRBosOutWorker', @value = '00:01:05', @category = '{cfgConstants.CategoryNameWorkers}'");

            // Integrations: PosMobile
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorPosMobile}', @value = '{intConstants.PosMobileTypeHydraMobile}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'ConnectionThread:Execute:Active:HydraMobileConnectionThread', @value = 'True', @category = 'CONNECTIVITY'");
            Execute.Sql($"EXEC dbo.SetHydraMobileEndPoint @ipAddress = '127.0.0.1', @port = 4000");

            // Integrations: Pump
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorPump}', @value = '{intConstants.PumpTypeDoms}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DOMS:SERVICE-MODE:KIOSKONLY', @value = '13', @category = '{cfgConstants.CategoryNamePump}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DOMS:SERVICE-MODE:KIOSKUSE', @value = '13', @category = '{cfgConstants.CategoryNamePump}'");

            // Integrations: SecAuth
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorSecAuth}', @value = '{intConstants.SecAuthTypeSignalRApiPost}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'AcknowledgeServerMessage:Enabled:', @value = 'True', @category = '{cfgConstants.CategoryNameSignalRHubClient}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'TimedOutResponse:', @value = 'False', @category = '{cfgConstants.CategoryNameSecondaryAuthorisation}'");
            Execute.Sql($"EXEC dbo.SetPaymentTimeout @mode = {(byte)PaymentTimeoutType.SecAuth}, @timeout = 45");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC)
            {
                return;
            }

            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 0");
        }
    }
}
