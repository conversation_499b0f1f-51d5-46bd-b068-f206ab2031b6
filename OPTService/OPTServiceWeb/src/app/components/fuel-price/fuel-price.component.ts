import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { Constants } from 'src/app/helpers/constants';
import { FuelPriceService } from 'src/app/services/fuel-price.service';
import { AdvancedConfig } from 'src/app/core/models/advancedConfig.model';
import { AdvancedConfigService } from 'src/app/services/advanced-config.service';
import { catchError, finalize } from 'rxjs/operators';
import { forkJoin, merge, of, Subscription } from 'rxjs';

@Component({
  selector: 'app-fuel-price',
  templateUrl: './fuel-price.component.html',
  styleUrls: ['./fuel-price.component.css']
})
export class FuelPriceComponent implements OnInit {

  @Input()
  fuelPrice: any;

  // Section collapse vars
  dataContainerIsCollapsed: boolean = true;

  fuelPriceError: boolean = false;
  fuelGradeNameError: boolean = false;
  fuelGradeVATError: boolean = false;
  pricePumpMapping: any = new Array();
  pumpType: string;
  fuelPriceForm = this.fb.group({
    Grade: ['', Validators.required],
    GradeName: ['', Validators.required],
    VatRate: ['', [Validators.required, Validators.pattern(Constants.DecimalTwoDecimalsMaximum), Validators.min(0), Validators.max(100)]],
    PriceToSet: ['', [Validators.required, Validators.pattern(/^\d+(?:[.,]\d{1})?$/)]],
    Prices: this.fb.array([
      this.fb.control('')
    ]),
    PlaylistFileName: ['']
  });

  /**
   * The FuelPrice component constructor.
   * @param fb The angular reactive forms Form Builder.
   * @param fuelPriceService The FuelPrice service.
   * @param logger The logger.
   */
  constructor(
    private fb: FormBuilder,
    private fuelPriceService: FuelPriceService,
    private advancedConfigService: AdvancedConfigService,
    private logger: NGXLogger) {
  }

  /**
   * ngOnInit angular hook
   */
  ngOnInit(): void {

    setTimeout(() => {
      this.refreshData();
    }, 100);
  }

  refreshData(): Subscription {
    const advancedConfigObserver = this.advancedConfigService.getAdvancedConfig().pipe(
      catchError(() => {
        this.logger.error('Problem getting advanced configuration');
        return of(undefined as AdvancedConfig);
      })
    );

    return forkJoin([advancedConfigObserver])
      .pipe(
        finalize(() => {
        })
      )
      .subscribe(([advancedConfig]) => {
        // Advanced Config
        if (advancedConfig !== undefined) {
          this.logger.debug('All advanced configuration data', advancedConfig);
          this.pumpType = advancedConfig.PumpType;
        }

        // Formulary assignments
        this.fuelPriceForm.patchValue(this.fuelPrice);
        Object.keys(this.fuelPriceForm.controls).forEach(key => {
          this.fuelPriceForm.controls[key].valueChanges.subscribe(val => {
            this.fuelPrice[key] = val;
          });
        });

        // Pivoting Pump - price
        // let i = 0;
        this.fuelPrice.Prices.forEach((item) => {
          let key = item.Price;
          // i++;
          // if (i % 2 === 0) {
          //   key = key + 5;
          // }
          const collection = this.pricePumpMapping.find(x => x.Price === key);
          if (!collection) {
            this.pricePumpMapping.push({ Price: key, Pumps: [item.Pump] });
          } else {
            collection.Pumps.push(item.Pump);
          }
        });
     });
  }

  /**
   * Updates the Fuel Price for a given grade
   */
  setFuelPrice() {
    this.logger.debug('Updating fuel price', this.fuelPrice);
    this.fuelPriceError = false;
    this.fuelPriceService.setPrice(this.fuelPrice).subscribe(res => {
      this.logger.debug('Fuel price udpated ok');
    }, error => {
      this.logger.error('Problem updating fuel price', error);
      this.fuelPriceError = true;
    });
  }

  /**
   * Updates the fuel grade name
   */
  setGradeName() {
    this.logger.debug('Updating fuel grade name', this.fuelPrice);
    this.fuelGradeNameError = false;
    this.fuelPriceService.setGradeName(this.fuelPrice).subscribe(res => {
      this.logger.debug('Fuel grade name udpated ok');
    }, error => {
      this.logger.error('Problem updating fuel grade name', error);
      this.fuelGradeNameError = true;
    });
  }

  /**
   * Updates the fuel grade VAT rate
   */
  setGradeVatRate() {
    this.logger.debug('Updating fuel grade VAT rate', this.fuelPrice);
    this.fuelGradeVATError = false;
    this.fuelPriceService.setGradeVatRate(this.fuelPrice).subscribe(res => {
      this.logger.debug('Fuel grade VAT rate udpated ok');
    }, error => {
      this.logger.error('Problem updating fuel grade VAT rate', error);
      this.fuelGradeVATError = true;
    });
  }

}
