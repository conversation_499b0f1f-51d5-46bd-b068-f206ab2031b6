using FluentAssertions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using NSubstitute;
using Xunit;

namespace Forecourt.SecondaryAuth.Tests.Workers
{
    public class AnprSecAuthOutWorkerTests
    {
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;
        private readonly IAnprWorker _anprWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pump;
        private readonly ISecAuthIntegratorOut<IMessageTracking> _worker;

        public AnprSecAuthOutWorkerTests()
        {
            _logManager = Substitute.For<IHtecLogManager>();
            _configurationManager = Substitute.For<IConfigurationManager>();

            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _allPumps = Substitute.For<IPumpCollection>();
            _pump = Substitute.For<IPump>();
            _anprWorker = Substitute.For<IAnprWorker>();

            _worker = new AnprSecAuthOutWorker(_logManager, _configurationManager, _allPumps, _telemetryWorker, _anprWorker);
        }

        [Fact]
        public void check_that_preauth_does_action_secauth()
        {
            // Arrange
            var request = new SecAuthRequest { Pump = 1 };
            var message = new MessageTracking() { IdAsString = "LOG-REF" };

            _allPumps.TryGetPump(Arg.Is<byte>(1), out var p).Returns(true);
            _allPumps.When(x => x.TryGetPump(Arg.Is<byte>(1), out var p1)).Do(c =>
            {
                c[1] = _pump;
            });

            // Assert
            var result = _worker.PreAuthRequest(request, message);

            // Act
            result.IsSuccess.Should().BeTrue();
            _anprWorker.Received(1).SendSecondaryAuthRequest(Arg.Any<IPump>(), Arg.Any<IMessageTracking>());
            _pump.Received(1).SecAuthRequestSent(Arg.Any<string>());
        }

        [Fact]
        public void check_that_postauth_does_not_action_secauth()
        {
            // Arrange
            var request = new SecAuthRequest { Pump = 1 };
            var message = new MessageTracking() { IdAsString = "LOG-REF" };

            // Assert
            var result = _worker.PostAuthRequest(request, message);

            // Act
            result.IsSuccess.Should().BeFalse();
            _anprWorker.Received(0).SendSecondaryAuthRequest(Arg.Any<IPump>(), Arg.Any<IMessageTracking>());
            _pump.Received(0).SecAuthRequestSent(Arg.Any<string>());
        }
    }
}