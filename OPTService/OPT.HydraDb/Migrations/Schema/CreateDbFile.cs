using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    [Migration(1, TransactionBehavior.None, "Create the HydraDb database (file) - VersionInfo in [master]"), Tags(Constants.MigrationTypeBootStrap)]
    public class CreateDbFile: Migration
    {
        public override void Up()
        {
           Execute.EmbeddedScript($"{Constants.EmbeddedResourcePrefix}.{Constants.MigrationTypeSchema}.CreateDbFile.sql");
        }

        public override void Down()
        {
            
        }
    }

    [Migration(2, TransactionBehavior.None, "Create the HydraDb database (file) - VersionInfo in [Hydra]"), Tags(Constants.MigrationTypeSchema)]
    public class CreateDbFileInCorrectDb : Migration
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{Constants.EmbeddedResourcePrefix}.{Constants.MigrationTypeSchema}.CreateDbFile.sql");
        }

        public override void Down()
        {

        }
    }
}
