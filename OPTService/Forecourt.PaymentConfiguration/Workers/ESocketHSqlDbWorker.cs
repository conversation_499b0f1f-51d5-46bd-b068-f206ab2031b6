using CSharpFunctionalExtensions;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Forecourt.PaymentConfiguration.HydraDb.Interfaces;
using Forecourt.PaymentConfiguration.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces;
using System.IO.Abstractions;

namespace Forecourt.PaymentConfiguration.Workers
{
    /// <summary>
    /// eSocket.POS (HSqlDb) implementation of the <see cref="IPaymentConfigIntegrator"/>
    /// </summary>
    public class ESocketHSqlDbWorker : ESocketWorker, IESocketHSqlDbWorker
    {
        /// <inheritdoc/>
        public ESocketHSqlDbWorker(IHtecLogManager logManager, IHydraDb hydraDb, IFileSystem fileSystem, IEsocketDb esocketDb, IContactlessProperties contactlessProperties, IConfigurationManager configurationManager = null) : 
            base(logManager, nameof(ESocketSqlServerWorker), hydraDb, fileSystem, esocketDb, contactlessProperties, configurationManager)
        {
        }

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            Initialise(() =>
            {
                HydraDb.SetEsocketUseConnectionString(false);
                ESocketDb.SetUseConnectionString(false, true);
            });

            return base.DoStart(startParams);
        }
    }
}
