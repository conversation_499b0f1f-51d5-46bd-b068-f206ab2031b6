using Forecourt.SecondaryAuth.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.SecondaryAuth.Factories
{
    /// <summary>
    /// SecAuth integrator out factory
    /// </summary>
    public class SecAuthIntegratorOutFactory : Factory<string, ISecAuthIntegratorOut<IMessageTracking>>, ISecAuthIntegratorOutFactory
    {
        private readonly Func<string, ISecAuthIntegratorOut<IMessageTracking>> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>     
        public SecAuthIntegratorOutFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, ISecAuthIntegratorOut<IMessageTracking>> resolveTypeInstance) :
            base(logManager, $"{nameof(SecAuthIntegratorOutFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.SecAuthTypeHydra, "Htec Hydra ANPR (PreAuth)", (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.SecAuthTypeSignalRApiPre, "Generic SignalR and RestAPI (PreAuth)", (key) => _resolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.SecAuthTypeSignalRApiPost, "Generic SignalR and RestAPI (PostAuth)", (key) => _resolveTypeInstance(key));
        }
    }
}
