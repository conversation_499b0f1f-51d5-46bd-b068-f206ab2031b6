import { DOCUMENT } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { environment } from 'src/environments/environment';

import { PumpService } from './pump.service';

describe('PumpService', () => {
  let service: PumpService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };
    
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        PumpService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    service = TestBed.inject(PumpService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('.getPumps() should handle success response from API', () => {
    //Arrange
    let mockedResponse = {
      Number: 1,
      Tid: '99979904',
      Closed: false,
      ClosePending: false,
      OptStringId: '1',
      InUse: false,
      NozzleUp: false,
      HasPayment: false,
      AnprRequested: false,
      AnprOk: false,
      Delivering: false,
      Delivered: false,
      ThirdPartyPending: false,
      PodMode: true,
      KioskOnly: true,
      IsMobile: false,
      OutsideOnly: true,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false
      };

    //Act
    service.getPumps().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.getPumpInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getPumps() for single pump should handle success response from API', () => {
    //Arrange
    let mockedResponse = {
      Number: 1,
      Tid: '99979904',
      Closed: false,
      ClosePending: false,
      OptStringId: '1',
      InUse: false,
      NozzleUp: false,
      HasPayment: false,
      AnprRequested: false,
      AnprOk: false,
      Delivering: false,
      Delivered: false,
      ThirdPartyPending: false,
      PodMode: true,
      KioskOnly: true,
      OutsideOnly: true,
      IsMobile: false,
      DefaultKioskOnly: false,
      DefaultOutsideOnly: true,
      MaxFillOverrideForFuelCards: false,
      MaxFillOverrideForPaymentCards: false
      };

    //Act
    service.getPumps('1').subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.getPumpInfo + '?pumpNumber=1');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getPumps() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.getPumps().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.getPumpInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getTids() should handle success response from API', () => {
    //Arrange
    let mockedResponse = ['99979901', '99979902','99979903', '99979904', '99979905', '99979906'];

    //Act
    service.getTids().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.tidController.base + environment.tidController.getTidList);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getTids() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.getTids().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.tidController.base + environment.tidController.getTidList);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getMaxFillOverride() should handle success response from API', () => {
    //Arrange
    let mockedResponse = 1;

    //Act
    service.getMaxFillOverride().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toEqual(mockedResponse);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + 'Advanced/GetMaxFillOverride');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(mockedResponse);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getMaxFillOverride() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.getMaxFillOverride().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + 'Advanced/GetMaxFillOverride');

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setTid() should handle success response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      Tid: '99979906'
    };
        
    //Act
    service.setTid(pump).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.MapToTid);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setTid() should handle unsuccess response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      Tid: '99979906'
    };
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setTid(pump).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.MapToTid);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setOpt() should handle success response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      OptStringId: '1'
    };
        
    //Act
    service.setOpt(pump).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.MapToOpt);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setOpt() should handle unsuccess response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      OptStringId: '1'
    };
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setOpt(pump).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.MapToOpt);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setDefaultDayMode() should handle success response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      DefaultDayMode: '1'
    };
        
    //Act
    service.setDefaultDayMode(pump).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.SetDefaultMode);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setDefaultDayMode() should handle unsuccess response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1,
      DefaultDayMode: '1'
    };
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setDefaultDayMode(pump).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.SetDefaultMode);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
  
  it('.changePumpStatus() should handle success response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1
    };
        
    //Act
    service.changePumpStatus(pump).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.ClosePump);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.changePumpStatus() should handle unsuccess response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1
    };
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.changePumpStatus(pump).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.ClosePump);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.forceClosePump() should handle success response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1
    };
        
    //Act
    service.forceClosePump(pump).subscribe((response) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.ForceClosePump);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.forceClosePump() should handle unsuccess response from API', () => {
    //Arrange
    let pump: any = {
      Number: 1
    };
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.forceClosePump(pump).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.pumpController.base + environment.pumpController.ForceClosePump);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

});
