using FluentMigrator;
using Htec.Common.Extensions;
using OPT.Common;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed by MADIC integrations (mostly Magical Advanced Config keys)
    /// </summary>
    [Migration(20060001, "ADO#674010 - Set further configuration needed for MADIC integrations #2"), Tags(Constants.MigrationTypeData)]
    public class ADO674010_MADICConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;
        private const string CategoryNameLoggingDeveloper = "LOGGING:DEVELOPER";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC) 
            {
                return;
            }

            // Generice config
            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 1");
            Execute.Sql($"EXEC dbo.SetTillNumber @number = 90");
            Execute.Sql($"EXEC dbo.SetReceiptLayoutMode @mode = 2");
            Execute.Sql($"EXEC dbo.SetReceiptFooter @opt = 'GLOBAL', @receiptFooter = 'Thank You\r\nDrive Carefully\r\nYour feedback is important\r\nContact us on 01727 898890'");
            Execute.Sql($"EXEC dbo.SetNextDayEnd @dayEnd = null");

            // BOS
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BookTransaction:Ignore:Invalid', @value = 'False', @category = '{cfgConstants.CategoryNameBOS}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BookTransaction:Retry:Interval', @value = '00:00:45', @category = '{cfgConstants.CategoryNameBOS}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BookTransaction:Retry:MaxAttempts', @value = '5', @category = '{cfgConstants.CategoryNameBOS}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'CardRefererPrefix', @value = 'OPT', @category = '{cfgConstants.CategoryNameBOS}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DefaultRefererName', @value = 'OPT Spare 1', @category = '{cfgConstants.CategoryNameBOS}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'OperatorId', @value = '123', @category = '{cfgConstants.CategoryNameBOS}'");

            // Pre-defined Amounts
            Execute.Sql($"EXEC dbo.ClearPredefinedAmounts");
            Execute.Sql($"EXEC dbo.AddPredefinedAmount @amount = 10000");
            Execute.Sql($"EXEC dbo.AddPredefinedAmount @amount = 5000");
            Execute.Sql($"EXEC dbo.AddPredefinedAmount @amount = 2000");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'PredefinedAmount:Enabled:PaymentCard', @value = 'True', @category = '{ConfigConstants.NameOptUpper}'");

            // Contactless
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'ENABLED', @value = 'True', @category = '{cfgConstants.CategoryNameContactless.ToUpper()}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'SingleButton', @value = 'True', @category = '{cfgConstants.CategoryNameContactless.ToUpper()}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DEVICEPREAUTH', @value = '10000', @category = '{cfgConstants.CategoryNameContactless.ToUpper()}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'CARDPREAUTH', @value = '10000', @category = '{cfgConstants.CategoryNameContactless.ToUpper()}'");

            // CardReferences = Tenders
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'AMERICAN EXPRESS', @externalCardName = 'OPT Amex'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'KEYFUELS', @externalCardName = 'OPT Keyfuels Bunkering'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'AMEX', @externalCardName = 'OPT Amex'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'UK Fuels M Card', @externalCardName = 'OPT UK Fuels Retail'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'UK Fuels', @externalCardName = 'OPT UK Fuels Bunkering'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'MasterCard Debit', @externalCardName = 'OPT Debit Mastercard'");
            Execute.Sql($"EXEC dbo.SetExternalName @cardName = 'Visa Debit', @externalCardName = 'OPT Visa'");

            // Prune Data
            Execute.Sql("DELETE dbo.Transactions");
            Execute.Sql("DELETE dbo.Receipts");
            Execute.Sql("DELETE dbo.CardSales");
            Execute.Sql("DELETE dbo.CardVolumeSales");
            Execute.Sql("DELETE dbo.DayCardSales");
            Execute.Sql("DELETE dbo.DayItemSales");

            // Debug Logging
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'DomsSetup:Log:RxTx:DOMS', @value = 'True', @category = 'CONNECTIVITY'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsMessageReader', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsMessageSender', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsController', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:DomsSetupTcp', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'LogLevel:State:BookTransactionOrchestration', @value = 'Info', @category = '{CategoryNameLoggingDeveloper}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC)
            {
                return;
            }

            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 0");
            Execute.Sql($"EXEC dbo.SetReceiptLayoutMode @mode = 3");
            Execute.Sql($"EXEC dbo.SetReceiptFooter @opt = 'GLOBAL', @receiptFooter = ''");
            Execute.Sql($"EXEC dbo.ClearPredefinedAmounts");            
        }
    }
}
