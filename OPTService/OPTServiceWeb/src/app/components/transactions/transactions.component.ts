import { Component, Injectable, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { NgbAccordion, NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { NGXLogger } from 'ngx-logger';
import { forkJoin, Subscription } from 'rxjs';
import { FuelTransactionDetails } from 'src/app/core/models/fuelTransactionDetails.model';
import { OtherEvents } from 'src/app/core/models/otherEvents.model';
import { TransactionService } from 'src/app/services/transaction.service';
import { PumpService } from 'src/app/services/pump.service';
import { SignalRService } from 'src/app/services/signal-r.service';
import { DatePipe } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { LoadingService } from 'src/app/services/loading.service';

const FILTER_NONE = 'None';
const FILTER_PUMP_PREFIX = 'Pump ';
const FILTER_CAR_WASH_ONLY = 'Car Wash Only';

const DATE_ERROR_REQUIRED = "Date is required";
const DATE_ERROR_FORMAT = "Date format is invalid";
const DATE_ERROR_TODAY = "The end date must be on or before today's date";
const DATE_ERROR_END_LATER = "End date must be later than start date";
const DATE_ERROR_END_TIME_LATER = "End time must be later than start time";

@Injectable()
export class CustomDateParserFormatter extends NgbDateParserFormatter {

  readonly DELIMITER = '/';
  readonly REGEXP_DATE_FORMAT = new RegExp('^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]+$');

  parse(value: string): NgbDateStruct | null {
    if (value) {
      if (!this.REGEXP_DATE_FORMAT.test(value)) {
        return null;
      }

      let date = value.split(this.DELIMITER);
      return {
        day : parseInt(date[0], 10),
        month : parseInt(date[1], 10),
        year : parseInt(date[2], 10)
      };
    }
    return null;
  }

  format(date: NgbDateStruct | null): string {
    return date ? date.day + this.DELIMITER + date.month + this.DELIMITER + date.year : '';
  }
}

@Component({
  selector: 'app-transactions',
  templateUrl: './transactions.component.html',
  styleUrls: ['./transactions.component.css'],
  providers: [ { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter } ]
})
export class TransactionsComponent implements OnInit {
  @ViewChild('accordion') accordionComponent: NgbAccordion;
  
  showContent: boolean = false;
  fuelTransactionDetailsData: Array<FuelTransactionDetails> = new Array<FuelTransactionDetails>();
  filteredFuelTransactionDetailsData: Array<FuelTransactionDetails> = new Array<FuelTransactionDetails>();
  otherEventsData: Array<OtherEvents> = new Array<OtherEvents>();
  lastRefresh: any | undefined;
  signalRTransactionData: Subscription | undefined;
  signalRPumpData: Subscription | undefined;

  datepickerForm = this.fb.group({
    startDate: [ '' ],
    startTime: [ '' ],
    endDate: [ '' ],
    endTime: [ '' ]
  },{
    validator: this.validateDates.bind(this)
  });

  private startTime: FormControl;
  private endTime: FormControl;
  
  startDateError: string = '';
  endDateError: string = '';
  endTimeError: string = '';
  refreshError: string = '';
  seconds = true;

  activeFilter: string = FILTER_NONE;
  maxPump: number = 1;
  filters: Array<string> = new Array<string>();
  
  showReceipt: boolean = false;
  receiptURL: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl('');
  receiptText: string = '';

  fuelTransactionDetailsForm = this.fb.group({
    TransactionId: [0],
    TransactionTime: [''],
    GradeCode: [''],
    WashCode: [''],
    GradeName: [''],
    WashName: [''],
    PumpDetailsString: [''],
    CardNumber: [''],
    FuelQuantity: [0],
    WashQuantity: [0],
    Amount: [0],
    FuelCategory: [''],
    WashCategory: [''],
    FuelSubcategory: [''],
    WashSubcategory: [''],
    DiscountName: [''],
    DiscountCode: [''],
    DiscountValue: [0],
    DiscountCardNumber: [''],
    LocalAccountMileage: [0],
    LocalAccountRegistration: [''],
    TxnNumber: [''],
    HasReceipt: [false],
    PrinterEnabled: [false]
  });

  constructor(
    private fb: FormBuilder,
    private signalRService: SignalRService,
    private zone: NgZone,
    private transactionService: TransactionService,
    private pumpService: PumpService,
    private logger: NGXLogger,
    private datePipe: DatePipe,
    private sanitizer: DomSanitizer,
    private loadingService: LoadingService) {
      this.signalRTransactionData = this.signalRService.getTransactionSignalRMessage().subscribe(() => {
        this.zone.run(() => {
          this.refreshData();
        });
      });
      this.signalRPumpData = this.signalRService.getPumpSignalRMessage().subscribe(() => {
        this.zone.run(() => {
          this.refreshFilter();
        });
      });
  }

  /**
   * Returns the datepicker form controls
   */
  get datepickerFormControls() {
    return this.datepickerForm.controls;
  }

  /**
   * Validates the start and end date of the datepickers
   * @param formGroup form group object
   */
  validateDates(formGroup: any): { [key: string]: boolean } | null {
    this.startDateError = null;
    this.endDateError = null;
    this.endTimeError = null;

    let startStruct = formGroup.controls.startDate.value;
    if (!startStruct) {
      this.startDateError = DATE_ERROR_REQUIRED;
    } else if (!startStruct.year || !startStruct.month || !startStruct.day) {
      this.startDateError = DATE_ERROR_FORMAT;
    }

    let endStruct = formGroup.controls.endDate.value;
    if (!endStruct) {
      this.endDateError = DATE_ERROR_REQUIRED;
    } else if (!endStruct.year || !endStruct.month || !endStruct.day) {
      this.endDateError = DATE_ERROR_FORMAT;
    }

    if (this.endDateError || this.startDateError) {
      return { validDate: true };
    }

    let start = this.getDate(startStruct);
    let end = this.getDate(endStruct);
    let today = new Date();

    // check date is not later than today
    if (end > today) {
      this.endDateError = DATE_ERROR_TODAY;
    }

    // check date is not sooner than start date
    if (end < start) {
      this.endDateError = DATE_ERROR_END_LATER;      
    }

    if (this.endDateError || this.startDateError) {
      return { validDate: true };
    }

    
    if (start.getDate() === end.getDate()) {
      let start = formGroup.controls.startTime.value;
      let end = formGroup.controls.endTime.value;
      if (start.hour > end.hour) {
        this.endTimeError = DATE_ERROR_END_TIME_LATER;
      } else if (start.hour === end.hour) {
        if (start.minute > end.minute) {
          this.endTimeError = DATE_ERROR_END_TIME_LATER;
        } else if (start.minute === end.minute) {
          if (start.second > end.second) {
            this.endTimeError = DATE_ERROR_END_TIME_LATER;
          }
        }
      }
    }

    return null;
  }

  /**
   * Sets the active filter and updates the filtered transaction data
   * @param value 
   */
  setFilter(value: string) {
    this.activeFilter = value;
    this.getFilteredTransactionData();
  }

  /**
   * Configure the component information related to filters
   */
  configureFilter() {
    this.filters = new Array<string>();
    this.filters.push(FILTER_NONE);
    for (let i = 1; i <= this.maxPump; i++) {
      this.filters.push(FILTER_PUMP_PREFIX + i);
    }
    this.filters.push(FILTER_CAR_WASH_ONLY);
  }

  /**
   * Refresh the available filters
   */
  refreshFilter() {
    this.logger.debug('Refresh filter starts');

    this.pumpService.getPumps().subscribe((data) => {
      this.logger.debug('Pumps details =', data);

      data.forEach((element: { Number: number; }) => {
        if (element.Number > this.maxPump) {
          this.maxPump = element.Number;
        }
      });

      // if current filter is not valid anymore set FILTER_NONE instead
      if (this.activeFilter.startsWith(FILTER_PUMP_PREFIX) && this.activeFilter.length > 5) {
        let pumpNumber = Number(this.activeFilter.substr(5));
        if (pumpNumber > this.maxPump) {
          this.activeFilter = FILTER_NONE;
        }
      }

      this.configureFilter();
    }, (error) => {
      this.logger.error('Problem getting pumps details');
    })
  }

  /**
   * The refresh data method called to retrieve the transaction data from OPT Service
   */
  refreshData(showLoading: boolean = true): void {
    if(showLoading){
      // Show loading screen
      this.showContent = false;
      this.loadingService.showLoadingScreen(); 
    }

    this.logger.debug('Refresh data starts');
    this.logger.debug('StartDate = ', this.datepickerForm.value.startDate);
    this.logger.debug('StarTime = ', this.datepickerForm.value.startTime);
    this.logger.debug('EndDate = ', this.datepickerForm.value.endDate);
    this.logger.debug('EndTime = ', this.datepickerForm.value.endTime);

    let startDate = this.getDate(this.datepickerForm.value.startDate);
    let endDate = this.getDate(this.datepickerForm.value.endDate);
    let startTime = this.datepickerForm.value.startTime;
    let endTime = this.datepickerForm.value.endTime;

    startDate.setHours(startTime.hour, startTime.minute, startTime.second);
    endDate.setHours(endTime.hour, endTime.minute, endTime.second);

    let getFuelTransactions = this.transactionService.getFuelTransactions(startDate, endDate);
    let getOtherEvents = this.transactionService.getOtherEvents(startDate, endDate);
    
    forkJoin([getFuelTransactions, getOtherEvents]).subscribe(results => {
      this.fuelTransactionDetailsData = results[0];
      this.otherEventsData = results[1];

      this.logger.debug('Fuel transaction details =', this.fuelTransactionDetailsData);
      this.logger.debug('Other events =', this.otherEventsData);

      this.accordionComponent?.collapseAll();
      this.getFilteredTransactionData();

      this.fuelTransactionDetailsForm.patchValue(this.filteredFuelTransactionDetailsData);

      this.lastRefresh = new Date().toLocaleString();
      this.refreshError = "";
    }, (error) => {
      this.logger.error('Problem getting transaction details');
      if (error && error.statusText && error.error.Message) {
        this.refreshError = error.statusText + ": "+ error.error.Message;
      } else {
        this.refreshError = "Could not update the transactions list";
      }
      this.loadingService.errorDuringLoading();
      if (showLoading) {
        this.showContent = true;
      }
    },()=>{
      if(showLoading){
        // Hide loading screen
        this.loadingService.hideLoadingScreen();
        this.showContent = true;
      }
    });
    
  }

  /**
   * Converts a bootstrap date to Date
   * @param ngbDate Bootstrap date
   */
  getDate(ngbDate: NgbDateStruct) {
    return ngbDate ? new Date(ngbDate.year, ngbDate.month - 1, ngbDate.day) : null;
  }

  /**
   * Checks if a field is not considered empty
   * @param value String value of the field
   */
  notEmptyField(value: string) {
    return value !== null && value !== "";
  }

  /**
   * Gets the fuel transactions data to display bases on the selected filter
   */
  getFilteredTransactionData() {
    this.logger.info('getFilteredTransactionData - start - filter =', this.activeFilter);
    if (this.activeFilter === FILTER_NONE) {
      this.logger.info('getFilteredTransactionData - filter none');
      this.filteredFuelTransactionDetailsData = this.fuelTransactionDetailsData;
    } else if (this.activeFilter === FILTER_CAR_WASH_ONLY) {
      this.logger.info('getFilteredTransactionData - filter by Car Wash Only');
      this.filteredFuelTransactionDetailsData = this.fuelTransactionDetailsData.filter((element) => {
        return element.PumpDetailsString === null;
      });    
    } else {
      this.logger.info('getFilteredTransactionData - filter by PUMP');
      this.filteredFuelTransactionDetailsData = this.fuelTransactionDetailsData.filter((element) => {
        if (element.PumpDetailsString &&
            element.PumpDetailsString.startsWith("PUMP ") &&
            element.PumpDetailsString.includes(':') &&
            element.PumpDetailsString.substring(5, element.PumpDetailsString.indexOf(':')) === this.activeFilter.substr(5)) {
          return true;
        }
        return false;
      });
    }
  }

  /**
   * Componenent initialization
   */
  ngOnInit(): void {
    // set the default calendar filter
    let date = new Date();
    let today = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    date.setUTCDate(date.getUTCDate() - 1);
    let yesterday = { day: date.getUTCDate(), month: date.getUTCMonth() + 1, year: date.getUTCFullYear()};
    this.datepickerForm.setValue({
      "startDate": today,
      "startTime": { "hour": date.getUTCHours(), "minute": 0, "second": 0 },
      "endDate": today,
      "endTime": { "hour": date.getUTCHours() + 1, "minute": 59, "second": 59 }
    });

    // set the default pump filters
    this.configureFilter();

    setTimeout(()=>{
      this.refreshFilter();
      this.refreshData();
    })
  }

  /**
   * Component destroy
   */
  ngOnDestroy(): void {
    this.signalRTransactionData?.unsubscribe();
    this.signalRPumpData?.unsubscribe();
  }

  /**
   * Format datetime for display
   * @param datetime datetime to format
   */
  formatDatetime(datetime: Date): string {
    return this.datePipe.transform(datetime, 'dd/MM/YYYY, HH:mm:ss');
  }

  /**
   * Fetch a receipt
   * @param fuelTransactionDetails fuel transction details model
   */
  fetchReceipt(fuelTransactionDetails: FuelTransactionDetails) {
    this.logger.info('Fetch receipt - Transaction ', fuelTransactionDetails.TransactionId);
    this.showReceipt = true;
    this.receiptURL = this.sanitizer.bypassSecurityTrustResourceUrl(this.transactionService.getReceiptURL(fuelTransactionDetails.TransactionId));
  }

  /**
 * Get a receipt
 */
  getReceipt(fuelTransactionDetails: FuelTransactionDetails) {
    this.logger.info('Get receipt - Transaction ', fuelTransactionDetails);

    this.transactionService.getReceipt(fuelTransactionDetails.TransactionId).subscribe((data) => {
      this.logger.debug('Get receipt ok');
      this.showReceipt = true;
      this.receiptText = data.ReceiptContent;
      fuelTransactionDetails.ReceiptContent = this.receiptText;
    }, (error: any) => {
      this.logger.error('Get receipt error: ', error);
      this.showReceipt = false;
    });
  }

  /**
   * Hides the receipt iframe
   */
  hideReceipt() {
    this.showReceipt = false;
  }

  /**
   * Prints a receipt
   * @param fuelTransactionDetails fuel transactions details model
   */
  printReceipt(fuelTransactionDetails: FuelTransactionDetails) {
    this.logger.info('Print receipt - Transaction ', fuelTransactionDetails);

    this.transactionService.printReceipt(fuelTransactionDetails).subscribe(() => {
      this.logger.debug('Print receipt ok');
      fuelTransactionDetails.ReceiptError = false;
    }, () => {
      this.logger.error('Print receipt error');
      fuelTransactionDetails.ReceiptError = true;
      fuelTransactionDetails.ReceiptErrorText = "Failed to print receipt";
    });
  }

  /**
   * Save a receipt
   * @param fuelTransactionDetails fuel transactions details model
   */
  saveReceipt(fuelTransactionDetails: FuelTransactionDetails) {
    this.logger.info('Save receipt - Transaction ', fuelTransactionDetails);

    this.transactionService.saveReceipt(fuelTransactionDetails).subscribe(() => {
      this.logger.debug('Save receipt ok');
      fuelTransactionDetails.ReceiptError = false;
    }, () => {
      this.logger.error('Save receipt error');
      fuelTransactionDetails.ReceiptError = true;
      fuelTransactionDetails.ReceiptErrorText = "Failed to save receipt";
    });
  }
}
