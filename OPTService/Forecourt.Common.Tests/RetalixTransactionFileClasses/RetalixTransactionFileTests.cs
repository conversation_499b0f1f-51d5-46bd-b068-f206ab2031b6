using FluentAssertions;
using Forecourt.Bos.TransactionFiles;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Messages.Retalix;
using Htec.Logger.Interfaces.Tracing;
using Htec.Testing.Helpers;
using NSubstitute;
using System;
using System.Collections.Generic;
using System.IO.Abstractions.TestingHelpers;
using System.Net;
using Xunit;

namespace OPT.Common.Tests.RetalixTransactionFileClasses
{
    public class RetalixTransactionFileTests
    {
        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _logger;
        private readonly MockFileSystem _fileSystem;
        private readonly IRetalixPosWorker _retalixPosWorker;

        public RetalixTransactionFileTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _logger = Substitute.For<IHtecLogger>();
            _fileSystem = new MockFileSystem();
            _retalixPosWorker = Substitute.For<IRetalixPosWorker>();
        }

        #region Constructor
        
        [Fact]
        public void constructor_null_hydradb_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new RetalixTransactionFile(null, _logger, _fileSystem, _retalixPosWorker);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "hydraDb");
        }

        [Fact]
        public void constructor_null_logger_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new RetalixTransactionFile(_hydraDb, null, _fileSystem, _retalixPosWorker);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "logger");
        }

        [Fact]
        public void constructor_null_file_system_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new RetalixTransactionFile(_hydraDb, _logger, null, _retalixPosWorker);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "fileSystem");
        }

        [Fact]
        public void constructor_null_pos_worker_throws_argument_null_exception()
        {
            // Arrange
            object Constructor() => new RetalixTransactionFile(_hydraDb, _logger, _fileSystem, null);

            // Act & Assert
            AssertionHelper.AssertConstructorThrowsException(Constructor, "retalixPosWorker");
        }

        #endregion

        #region Set Directory

        [Theory]
        [InlineData(null),
        InlineData(""),
        InlineData(" ")]
        public void set_directory_empty_directory_sets_available_to_false(string directory)
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            // Act
            retalixTransaction.SetFileDirectory(directory);

            // Assert
            retalixTransaction.Available.Should().BeFalse();
        }

        [Fact]
        public void set_directory_valid_directory_sets_available_to_true()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            // Act
            retalixTransaction.SetFileDirectory("C:\\Temp");

            // Assert
            retalixTransaction.Available.Should().BeTrue();
        }

        #endregion

        #region Write Transaction

        [Fact]
        public void write_transaction_available_false_writes_no_file()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var retalixTransactionFileItem = CreateDefaultTransaction();

            // Act
            retalixTransaction.WriteTransactionFile(new[] { retalixTransactionFileItem }, DateTime.Now);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }

        [Fact]
        public void write_transaction_null_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var retalixTransactionFileItem = CreateDefaultTransaction();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteTransactionFile(new[] { retalixTransactionFileItem }, DateTime.Now);

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XT000001_200101130400000.csv");
        }

        [Fact]
        public void write_transaction_empty_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var retalixTransactionFileItem = CreateDefaultTransaction();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteTransactionFile(new[] { retalixTransactionFileItem }, DateTime.Now, new List<IPAddress>());

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XT000001_200101130400000.csv");
        }

        [Fact]
        public void write_transaction_valid_addresses_writes_file_to_octet_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var retalixTransactionFileItem = CreateDefaultTransaction();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteTransactionFile(new[] { retalixTransactionFileItem }, DateTime.Now, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\50\XT000001_200101130400000.csv");
        }

        [Fact]
        public void write_transaction_valid_file_contents_is_correct()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var retalixTransactionFileItem = CreateDefaultTransaction();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteTransactionFile(new[] { retalixTransactionFileItem }, DateTime.Now, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            const string expected = "TRANSACTION,1,1,1,Unleaded,1,123,1,1,******001,200101130400,,5,0,,0,5,,1,0,0,0,0,,0,0,5,0,,,,0,0,0,0,0,0,0,VISA,END\r\n";
            _fileSystem.GetFile(@"C:\temp\50\XT000001_200101130400000.csv").TextContents.Should().Be(expected);
        }

        private static RetalixTransactionFileItem CreateDefaultTransaction()
        {
            return RetalixTransactionFileItem.ConstructSalesItem(1, 1, 1, "Unleaded", "01", 123, 1, 1, "******001", new DateTime(2020, 1, 1, 13, 04, 00), 5, 5, 0, null, 0, 5, "VISA", null, null, 0, null, false);
        }

        #endregion

        #region Write Shift End

        [Fact]
        public void write_shift_end_available_false_writes_no_file()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var shiftEndItem = CreateDefaultShiftEnd();

            // Act
            retalixTransaction.WriteShiftEnd(shiftEndItem, null);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }

        [Fact]
        public void write_shift_end_null_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var shiftEndItem = CreateDefaultShiftEnd();

            // Act
            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteShiftEnd(shiftEndItem, null);

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XSM200102130400.csv");
        }

        [Fact]
        public void write_shift_end_empty_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var shiftEndItem = CreateDefaultShiftEnd();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteShiftEnd(shiftEndItem, new List<IPAddress>());

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XSM200102130400.csv");
        }

        [Fact]
        public void write_shift_end_valid_addresses_writes_file_to_octet_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var shiftEndItem = CreateDefaultShiftEnd();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteShiftEnd(shiftEndItem, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\50\XSM200102130400.csv");
        }

        [Fact]
        public void write_shift_end_valid_file_contents_is_correct()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var shiftEndItem = CreateDefaultShiftEnd();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteShiftEnd(shiftEndItem, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            const string expected = "200101,130400,200102,130400,1,0,0,0,0.05,0,0.05,0,0,0,0,0.00,0,0.10,0,0,1,10\r\n";
            _fileSystem.GetFile(@"C:\temp\50\XSM200102130400.csv").TextContents.Should().Be(expected);
        }

        private static RetalixShiftEndItem CreateDefaultShiftEnd()
        {
            return RetalixShiftEndItem.ConstructShiftEndItem(new DateTime(2020, 1, 1, 13, 04, 00), new DateTime(2020, 1, 2, 13, 04, 00), 1, 5, 5, 0, 1, 10);
        }

        #endregion

        #region Write Sales Item File

        [Fact]
        public void write_sales_item_available_false_writes_no_file()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var salesItem = CreateDefaultSalesItems();

            // Act
            retalixTransaction.WriteSalesItems(salesItem, null);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }

        [Fact]
        public void write_sales_item_null_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var salesItem = CreateDefaultSalesItems();

            // Act
            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteSalesItems(salesItem, null);

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XSI200101130400.csv");
        }

        [Fact]
        public void write_sales_item_empty_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var salesItem = CreateDefaultSalesItems();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteSalesItems(salesItem, new List<IPAddress>());

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XSI200101130400.csv");
        }

        [Fact]
        public void write_sales_item_valid_addresses_writes_file_to_octet_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var salesItem = CreateDefaultSalesItems();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteSalesItems(salesItem, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\50\XSI200101130400.csv");
        }

        [Fact]
        public void write_sales_item_valid_file_contents_is_correct()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var salesItem = CreateDefaultSalesItems();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteSalesItems(salesItem, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            const string expected = "01,15,2,DIESEL,0.01,5\r\n";
            _fileSystem.GetFile(@"C:\temp\50\XSI200101130400.csv").TextContents.Should().Be(expected);
        }

        private static RetalixItemSalesItem[] CreateDefaultSalesItems()
        {
            return new[] { new RetalixItemSalesItem("01", 15, 02, "DIESEL", 5, 5, new DateTime(2020, 1, 1, 13, 04, 00)) };
        }

        #endregion

        #region Write Card Volume File

        [Fact]
        public void write_card_volume_file_available_false_writes_no_file()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            retalixTransaction.WriteCardVolumeItems("1", cardValueItems, null);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }

        [Fact]
        public void write_card_volume_file_null_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardVolumeItems("1", cardValueItems, null);

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XCV200101130400.csv");
        }

        [Fact]
        public void write_card_volume_file_empty_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardVolumeItems("1", cardValueItems, new List<IPAddress>());

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XCV200101130400.csv");
        }

        [Fact]
        public void write_card_volume_file_valid_addresses_writes_file_to_octet_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardVolumeItems("1", cardValueItems, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\50\XCV200101130400.csv");
        }

        [Fact]
        public void write_card_volume_file_valid_file_contents_is_correct()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardVolumeItems("1", cardValueItems, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            const string expected = "1,VISA,1,0.01\r\n";
            _fileSystem.GetFile(@"C:\temp\50\XCV200101130400.csv").TextContents.Should().Be(expected);
        }

        private static RetalixCardVolumeItem[] CreateDefaultCardVolumes()
        {
            return new[] { new RetalixCardVolumeItem(1, "VISA", 1, 5, new DateTime(2020, 1, 1, 13, 04, 00)) };
        }

        #endregion

        #region Write Card Amount File

        [Fact]
        public void write_card_amount_file_available_false_writes_no_file()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardAmountItems = CreateDefaultCardAmounts();

            // Act
            retalixTransaction.WriteCardAmountItems(cardAmountItems, null);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }

        [Fact]
        public void write_card_amount_file_null_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardAmountItems = CreateDefaultCardAmounts();

            // Act
            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardAmountItems(cardAmountItems, null);

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XCA200101130400.csv");
        }

        [Fact]
        public void write_card_amount_file_empty_addresses_writes_file_to_unconnected_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardAmountItems = CreateDefaultCardAmounts();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardAmountItems(cardAmountItems, new List<IPAddress>());

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\unconnected\XCA200101130400.csv");
        }

        [Fact]
        public void write_card_amount_file_valid_addresses_writes_file_to_octet_folder()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardAmountItems = CreateDefaultCardAmounts();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardAmountItems(cardAmountItems, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            _fileSystem.AllFiles.Should().Contain(@"C:\temp\50\XCA200101130400.csv");
        }

        [Fact]
        public void write_card_amount_file_valid_file_contents_is_correct()
        {
            // Arrange
            IRetalixTransactionFile retalixTransaction = CreateDefaultRetalixTransactionFile();

            var cardAmountItems = CreateDefaultCardAmounts();

            // Act
            ((RetalixTransactionFile)retalixTransaction).GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            retalixTransaction.SetFileDirectory("C:\\temp");
            retalixTransaction.WriteCardAmountItems(cardAmountItems, new List<IPAddress> { new IPAddress(new byte[] { 192, 168, 10, 50 }) });

            // Assert
            const string expected = "1,VISA,0.05\r\n";
            _fileSystem.GetFile(@"C:\temp\50\XCA200101130400.csv").TextContents.Should().Be(expected);
        }

        private static RetalixCardAmountItem[] CreateDefaultCardAmounts()
        {
            return new[] { new RetalixCardAmountItem(1, "VISA", 5, new DateTime(2020, 1, 1, 13, 04, 00)) };
        }

        #endregion

        #region Move Offline Transaction Files

        #endregion

        private RetalixTransactionFile CreateDefaultRetalixTransactionFile()
        {
            return CreateDefaultRetalixTransactionFile();
        }
    }
}
