using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.UpdateFileClasses;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using System;
using System.Collections.Generic;
using System.Net;

namespace OPT.Common.Workers.Interfaces
{
    public interface IControllerWorker : IConnectable, IWorkerEvents, INotificationWorker<EventType>, INotificationWorker<string>
    {
        string UpgradeFileDirectory { get; }
        string RollbackFileDirectory { get; }
        string DatabaseBackupDirectory { get; }
        string LogFileDirectory { get; }
        string TraceFileDirectory { get; }
        string JournalFileDirectory { get; }
        string FuelDataUpdateFile { get; }

        /// <summary>Check whether in a batch of config changes.</summary>
        /// <returns>True if in batch, false otherwise.</returns>
        bool IsConfigBatch { get; }

        IEnumerable<string> UploadedFileNames { get; }
        IDictionary<byte, float> GradePriceToSet { get; }

        DateTime? NextDayEnd { get; }

        /// <summary>Inform worker that connections have changed.</summary>
        void ConnectionsChanged();

        /// <summary>Send information message to all controllers.</summary>
        /// <param name="message">Message to send.</param>
        void SendInformation(string message);

        event ControllerWorker.RefreshAboutDelegate RefreshAboutEvent;

        Connections GetConnections();

        Endpoints GetEndpoints();

        IEnumerable<InfoMessage> GetInfo();
        
        /// <summary>Fetch the Listener end point and reconnect if necessary.</summary>
        void ReloadListener();

        string OpenPump(byte pump);
        string ClosePump(byte pump);
        string ForceClosePump(byte pump);
        string ForcePumpOutside(byte pump);
        string SetMaxFillOverrideForFuelCards(byte pump, bool flag);
        string SetMaxFillOverrideForPaymentCards(byte pump, bool flag);
        string MapToTid(byte pump, string tid);
        Result MapToOpt(byte pump, string opt, string reference = null);
        Result SetDefaultMode(byte pump, bool kioskOnly, bool outsideOnly, string reference = null);
        Result SetGradePrice(byte grade, int price);
        Result SetGradePrices(IList<FuelPriceItem> gradePrices);
        string SetGradeName(byte grade, string name);
        string SetGradeVatRate(byte grade, float vatRate);
        string SetAutoAuth(bool isOn);
        string SetForwardFuelPriceUpdate(bool isOn);
        string SetMediaChannel(bool isOn);
        string SetUnmannedPseudoPos(bool isOn);
        string SetAsdaDayEndReport(bool isAsda);
        string SetContactlessAllowed(bool isAllowed);
        string SetContactlessCardPreAuth(uint limit);
        string SetContactlessDevicePreAuth(uint limit);
        string SetContactlessTtq(string ttq);

        string SetContactlessSingleButton(bool showSingleButton);

        string SetReceiptHeader(string optIdString, string header);
        string SetReceiptFooter(string optIdString, string footer);
        string SetPlaylistFileName(string optIdString, string filename);
        string SetLogInterval(int interval);
        string RestartOpt(string optIdString);
        string EngineerResetOpt(string optIdString);
        string RefreshOpt(string optIdString = null);
        string RequestOptLog(string optIdString = null);
        bool SetPaymentTimeout(string modeString, int timeout);
        string SetReceiptTimeout(int timeout);
        string SetReceiptMaxCount(int count);

        [Obsolete("Left in for Unit tests only!")]
        string PerformShiftEnd();
        [Obsolete("Left in for Unit tests only!")]
        string PerformDayEnd();
        void UpgradeService();
        void RollbackService();
        void RestartService();
        void RestartEsocketService();
        string SaveFile(string fileName);
        string SaveSoftwareFile(string fileName);
        string SaveWhitelistFile(string fileName);
        string SaveLayoutFile(string fileName);
        string SaveMediaFile(string fileName);
        string SavePlaylistFile(string fileName);
        string SaveContactlessPropertiesFile(string fileName);
        string RemoveFile(string fileName);
        string RemoveWhitelistFile(string fileName);
        string RemoveLayoutFile(string fileName);
        string RemoveUpgradeFile(string fileName);
        string RemoveSoftwareFile(string fileName);
        string RemoveMediaFile(string fileName);
        string RemovePlaylistFile(string fileName);
        string RemoveDatabaseBackupFile(string fileName);
        string RemoveOptLogFile(string fileName);
        string DatabaseBackup();
        string DatabaseRestore(string fileName);

        string SetServicePorts
            (int fromOpt, int toOpt, int heartbeat, int hydraPos, int retalixPos, int thirdPartyPos, int mediaChannelPort);

        Result SetPumpControllerAddress(IPAddress address, int port);
        string SetAnpr(IPAddress address, int port);
        string SetCarWash(IPAddress address, int port);
        Result SetTankGaugeAddress(IPAddress address, int port);
        string SetHydraMobile(IPAddress address, int port);
        string SetServiceAddress(IPAddress address);
        string AddEsocket(IPAddress address, int port);
        string RemoveEsocket(IPAddress address, int port);
        string DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort);
        string CancelDivertOptService();

        bool IsOptServiceDiverted
            (out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort, out int mediaChannelPort);

        string SetConfigBatch(bool isBatch);
        string AddGenericLoyalty(string name);
        string DeleteGenericLoyalty(string name);
        string SetGenericLoyaltyPresent(string name, bool present);
        string SetGenericLoyalty(string name, GenericLoyalty loyalty);
        string SetTariffMappings(IList<TariffMapping> mappings);
        string SetTariffMappingFuelCardsOnly(byte grade, bool flag);
        string SetPredefinedAmounts(IList<int> amounts);
        string AddWash(Wash wash);
        string RemoveWashByProgramId(byte programId);
        void ReloadOptConfiguration();
        string SetRetalixPosPrimaryIpAddress(IPAddress address);

        string SetRetalixTransactionFileDirectory(string directory = null);
        string SetTransactionFileDirectory(string directory);
        string SetWhitelistDirectory(string directory);
        string SetLayoutDirectory(string directory);
        string SetSoftwareDirectory(string directory);
        string SetMediaDirectory(string directory);
        string SetPlaylistDirectory(string directory);
        string SetOptLogFileDirectory(string directory);
        string SetLogFileDirectory(string directory);
        string SetTraceFileDirectory(string directory);
        string SetJournalFileDirectory(string directory);
        string SetReceivedUpdateDirectory(string directory);
        string SetContactlessPropertiesFile(string fileName);
        string SetFuelDataUpdateFile(string fileName);
        string SetUpgradeFileDirectory(string directory);
        string SetRollbackFileDirectory(string directory);
        string SetDatabaseBackupDirectory(string directory);
        void SetEsocketChanged();
        string SetEsocketConnectionString(string newConnectionString);
        string SetEsocketUseConnectionString(bool useConnectionString);
        string SetEsocketConfigFile(string fileName);
        string SetEsocketKeystoreFile(string fileName);
        string SetEsocketDbUrl(string url);
        string SetEsocketOverrideProperties(bool flag);
        string SetEsocketOverrideKeystore(bool flag);
        string SetEsocketOverrideUrl(bool flag);
        string SetEsocketOverrideContactless(bool flag);
        string SetReceiptLayoutMode(int mode);
        string SetSiteName(string name);
        string SetVatNumber(string number);
        string SetCurrencyCode(int number);
        string SetNozzleUpForKioskUse(bool flag);
        string SetUseReplaceNozzleScreen(bool flag);
        string SetMaxFillOverride(uint maxFillOverride);
        string SetNextDayEnd(DateTime? dayEnd);
        string AddDiscountCard(string iin, string name, string type, float value, byte grade);
        string RemoveDiscountCard(string iin);
        string AddDiscountWhitelist(string iin, string pan);
        string RemoveDiscountWhitelist(string iin, string pan);

        string RemoveLocalAccountCustomer(string customerReference);

        string AddLocalAccountCustomer
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, bool pin, bool printValue,
            bool allowLoyalty);

        string SetLocalAccountCustomerBalance(string customerReference, uint balance);

        string AddLocalAccountCardWithoutRestrictions(string customerReference, string pan, string description, float discount);

        string AddLocalAccountCardWithRestrictions
        (string customerReference, string pan, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods);

        string SetLocalAccountCardHot(string pan, bool hot);

        string RemoveLocalAccountCard(string pan);
        Result SetPumpControllerLogonInfo(string loginString);
        string CheckDomsState();
        string ReconnectDoms();
        string ResetDoms();
        string MasterResetDoms();
        string ClearDomsTransaction(byte fpId, int seqNo);
        string AuthoriseDoms(byte fpId, uint limit);
        string DomsEmergencyStop(byte fpId);
        string DomsCancelEmergencyStop(byte fpId);
        string SetFuellingIndefiniteWait(bool flag);
        string SetFuellingWaitMinutes(int minutes);
        string SetFuellingBackoffAuth(int backoff);
        string SetFuellingBackoffPreAuth(int backoff);
        string SetFuellingBackoffStopStart(int backoff);
        string SetFuellingBackoffStopOnly(int backoff);
        string SetPosClaimNumber(byte number);
        string CheckPassword(string username, string password);
        Result SetSiteType(string type, string reference = null);
        Result SetPosType(string type, string reference = null);
        Result SetPumpType(string type, string reference = null);
        Result SetMobilePaymentType(string type, string reference = null);
        Result SetMobilePosType(string type, string reference = null);
        Result SetBosType(string type, string reference = null);
        Result SetSecAuthType(string type, string reference = null);
        Result SetPaymentConfigType(string type, string reference = null);

        /// <summary>
        /// Retrieves all categories information
        /// </summary>
        IList<CategoryConfiguration> GetCategoriesConfiguration();
        
        /// <summary>
        /// Sets local accounts enabled flag
        /// </summary>
        /// <param name="enabled">flag value</param>
        /// <returns>result of the operation</returns>
        string SetLocalAccountsEnabled(bool enabled);

        /// <summary>
        /// Force notification of an OPT config change.
        /// </summary>
        /// <returns>Result of the operation.</returns>
        string NotifyOptConfigChange();
    }
}
