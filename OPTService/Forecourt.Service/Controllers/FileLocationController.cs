using Htec.Common.Logger.Extensions;
using Htec.Foundation.Web.Http;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Description;
using Unity;

namespace OPTService.Controllers
{
    [Authorize(Roles = "WebUi,Swagger")]

    public class FileLocationController : LoggableApiController
    {
        #region Private properties

        private const string WebName = "Web";

        /// <summary>
        /// The Web object
        /// </summary>
        private readonly IWeb _web;

        #endregion Private properties

        #region Constructor

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="web">The Web object</param>
        /// <param name="logger">The HTEC logger</param>
        public FileLocationController(IWeb web, [Dependency(UnityConfig.DependencyWebLogger)]IHtecLogger logger) 
            : base(logger)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        #endregion Constructor

        #region Update Data

        [HttpPost]
        public HttpResponseMessage SetRetalixTransactionFileDirectory(FileLocations details)
        {
            string directory = details.RetalixTransactionFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                _web.SendChatMessage(WebName, "Clear Retalix Transaction File Directory");
                string result = _web.ClearRetalixTransactionFileDirectory();
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Retalix Transaction File Directory to {directory}");
                string result = _web.SetRetalixTransactionFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetTransactionFileDirectory(FileLocations details)
        {
            string directory = details.TransactionFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Transaction File Directory to {directory}");
                string result = _web.SetTransactionFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetWhitelistDirectory(FileLocations details)
        {
            string directory = details.WhitelistDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Whitelist Directory to {directory}");
                string result = _web.SetWhitelistDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetLayoutDirectory(FileLocations details)
        {
            string directory = details.LayoutDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Layout Directory to {directory}");
                string result = _web.SetLayoutDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetSoftwareDirectory(FileLocations details)
        {
            string directory = details.SoftwareDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Software Directory to {directory}");
                string result = _web.SetSoftwareDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetMediaDirectory(FileLocations details)
        {
            string directory = details.MediaDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Media Directory to {directory}");
                string result = _web.SetMediaDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetPlaylistDirectory(FileLocations details)
        {
            string directory = details.PlaylistDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Playlist Directory to {directory}");
                string result = _web.SetPlaylistDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetOptLogFileDirectory(FileLocations details)
        {
            string directory = details.OptLogFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set OPT Log File Directory to {directory}");
                string result = _web.SetOptLogFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetLogFileDirectory(FileLocations details)
        {
            string directory = details.LogFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Log File Directory to {directory}");
                string result = _web.SetLogFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetTraceFileDirectory(FileLocations details)
        {
            string directory = details.TraceFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Trace File Directory to {directory}");
                string result = _web.SetTraceFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetJournalFileDirectory(FileLocations details)
        {
            string directory = details.JournalFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Journal File Directory to {directory}");
                string result = _web.SetJournalFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetReceivedUpdateDirectory(FileLocations details)
        {
            string directory = details.ReceivedUpdateDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Received Update Directory to {directory}");
                string result = _web.SetReceivedUpdateDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetDatabaseBackupDirectory(FileLocations details)
        {
            string directory = details.DatabaseBackupDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Database Backup Directory to {directory}");
                string result = _web.SetDatabaseBackupDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetContactlessPropertiesFile(FileLocations details)
        {
            string fileName = details.ContactlessPropertiesFile?.Trim() ?? string.Empty;
            _web.SendChatMessage(WebName, $"Set Contactless Properties File to {fileName}");
            string result = _web.SetContactlessPropertiesFile(fileName);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        [HttpPost]
        public HttpResponseMessage SetFuelDataUpdateFile(FileLocations details)
        {
            string fileName = details.FuelDataUpdateFile?.Trim();
            if (string.IsNullOrEmpty(fileName))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "File name is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Fuel Data Update File to {fileName}");
                string result = _web.SetFuelDataUpdateFile(fileName);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetUpgradeFileDirectory(FileLocations details)
        {
            string directory = details.UpgradeFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Upgrade File Directory to {directory}");
                string result = _web.SetUpgradeFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetRollbackFileDirectory(FileLocations details)
        {
            string directory = details.RollbackFileDirectory?.Trim();
            if (string.IsNullOrEmpty(directory))
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest, "Directory is empty");
            }
            else
            {
                _web.SendChatMessage(WebName, $"Set Rollback File Directory to {directory}");
                string result = _web.SetRollbackFileDirectory(directory);
                return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
            }
        }

        [HttpPost]
        public HttpResponseMessage SetEsocketOverrideContactless(FileLocations details)
        {
            bool flag = details.EsocketOverrideContactless;
            _web.SendChatMessage(WebName, $"Set eSocket.POS {(flag ? "" : "do not ")}override contactless properties file");
            string result = _web.SetEsocketOverrideContactless(flag);
            return Request.CreateResponse(result == null ? HttpStatusCode.OK : HttpStatusCode.BadRequest, result);
        }

        #endregion

        #region Get Data

        /// <summary>
        /// Retrieves all the file location configuration or a single item.
        /// </summary>
        /// <param name="fileLocationName">Optional file location parameter</param>
        /// <param name="reference">The logging reference</param>
        /// <returns>Dictionary with all pair values</returns>
        [HttpGet]
        [ResponseType(typeof(Dictionary<string, string>))]
        public IHttpActionResult GetFileLocations(string fileLocationName = null, string reference = null)
        {
            AssignLoggingReference(ref reference);
            GetLogger().Debug(FormatMessage(HtecLoggingConstants.EntryMessage));
            GetLogger().Debug(FormatMessage($"Input value for fileLocationName is {fileLocationName}"));

            Dictionary<string, string> result;
            var fileLocations = _web.GetFileLocations();

            if (!string.IsNullOrEmpty(fileLocationName))
            {
                try
                {
                    object propertyValue = fileLocations.GetType().GetProperty(fileLocationName).GetValue(fileLocations, null);

                    string value;
                    if (propertyValue is string)
                    {
                        value = (string)propertyValue;
                    }
                    else
                    {
                        value = propertyValue.ToString().ToLower();
                    }

                    result = new Dictionary<string, string>
                    {
                        { fileLocationName, value }
                    };
                }
                catch(Exception exception)
                {
                    GetLogger().Debug(FormatMessage($"Returning NotFound - exception: ", exception.Message));
                    GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                    return NotFound();
                }
            }
            else
            {
                try
                {
                    var json = JsonConvert.SerializeObject(fileLocations);
                    result = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                }
                catch(Exception exception)
                {
                    GetLogger().Debug(FormatMessage($"Returning InternalServerError - exception: ", exception.Message));
                    GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
                    return InternalServerError();
                }
            }

            GetLogger().Debug(FormatMessage(HtecLoggingConstants.ExitMessage));
            return Ok(result);
        }

        #endregion Get Data
    }
}