<div class="row form-group" [formGroup]="form">
    <div class="input-group" [ngClass]="labelColClass">
        <label class="col-form-label" [for]="id">{{labelText}}</label>
    </div>
    <div class="input-group" [ngClass]="textColClass">
        <input type="text" class="form-control" [id]="id" [formControl]="control"
            [ngClass]="{'is-invalid':control.invalid && (control.dirty || control.touched)}"
            [placeholder]="placeHolderText" aria-describedby="fieldFeedback" [maxlength]="maxLength"
            [readonly]="disabled">
        <div class="input-group-append d-inline" *ngIf="appendText">
            <span class="input-group-text">{{appendText}}</span>
        </div>
        <div id="fieldFeedback" class="invalid-feedback">
            {{validationErrorText}}
        </div>
    </div>
</div>