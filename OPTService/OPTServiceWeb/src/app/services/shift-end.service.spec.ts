import { DOCUMENT } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { environment } from 'src/environments/environment';

import { ShiftEndService } from './shift-end.service';

describe('ShiftEndService', () => {
  let service: ShiftEndService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const fakeData = {
    DayEndTime: new Date(),
    NextDayEnd: new Date(),
    AsdaDayEndReport: false,
    ShiftEndTime: new Date(),
    Unmanned: false,
    PrinterDetails: {
      BaudRate: 9600,
      DataBits: 8,
      Enabled: true,
      Handshake: "",
      IsPrinterBusy: false,
      PortName: "COM1",
      StopBits: ""
    },
    LogInterval: {
      Hours: 1,
      Minutes: 2,
      Seconds: 3
    }
  };
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        ShiftEndService,
        { provide: NGXLogger, useValue: ngxLoggertSpy },
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    service = TestBed.inject(ShiftEndService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('.getShiftEndInfo() should handle success response from API', () => {
    //Arrange

    //Act
    service.getShiftEndInfo().subscribe((data) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toEqual(fakeData);
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.getShiftEndInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(fakeData);

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.getShiftEndInfo() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';

    //Act
    service.getShiftEndInfo().subscribe(response => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error: HttpErrorResponse) => {
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.getShiftEndInfo);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, { status: 500, statusText: 'Internal Server Error' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.performShiftEnd() should handle success response from API', () => {
    //Arrange

    //Act
    service.performShiftEnd().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.performShiftEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.performShiftEnd() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.performShiftEnd().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.performShiftEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.performDayEnd() should handle success response from API', () => {
    //Arrange

    //Act
    service.performDayEnd().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.performDayEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.performDayEnd() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.performDayEnd().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.performDayEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.clearAutoDayEnd() should handle success response from API', () => {
    //Arrange

    //Act
    service.clearAutoDayEnd().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.clearAutoDayEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.clearAutoDayEnd() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.clearAutoDayEnd().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.clearAutoDayEnd);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setAsdaDayEndReportOn() should handle success response from API', () => {
    //Arrange

    //Act
    service.setAsdaDayEndReportOn().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setAsdaDayEndReportOn);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setAsdaDayEndReportOn() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setAsdaDayEndReportOn().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setAsdaDayEndReportOn);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setAsdaDayEndReportOff() should handle success response from API', () => {
    //Arrange

    //Act
    service.setAsdaDayEndReportOff().subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setAsdaDayEndReportOff);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setAsdaDayEndReportOff() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setAsdaDayEndReportOff().subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setAsdaDayEndReportOff);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setNextDayEnd() should handle success response from API', () => {
    //Arrange
    const urlParam = "2021-03-02T17:00:58.607";

    //Act
    service.setNextDayEnd(urlParam).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setNextDayEnd + "?dayEnd=" + urlParam);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setNextDayEnd() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    const urlParam = "2021-03-02T17:00:58.607";
    
    //Act
    service.setNextDayEnd(urlParam).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setNextDayEnd + "?dayEnd=" + urlParam);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("GET");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLogInterval() should handle success response from API', () => {
    //Arrange

    //Act
    service.setLogInterval(fakeData.LogInterval).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setLogInterval);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLogInterval() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setLogInterval(fakeData.LogInterval).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setLogInterval);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
  
  it('.setPrinterEnabled() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterEnabled(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterEnabled);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterEnabled() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterEnabled(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterEnabled);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
  
  it('.setPrinterPortName() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterPortName(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterPortName);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterPortName() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterPortName(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterPortName);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
  
  it('.setPrinterBaudRate() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterBaudRate(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterBaudRate);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterBaudRate() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterBaudRate(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterBaudRate);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });  

  it('.setPrinterHandshake() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterHandshake(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterHandshake);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterHandshake() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterHandshake(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterHandshake);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });  

  it('.setPrinterStopBits() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterStopBits(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterStopBits);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterStopBits() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterStopBits(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterStopBits);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });  

  it('.setPrinterDataBits() should handle success response from API', () => {
    //Arrange

    //Act
    service.setPrinterDataBits(fakeData.PrinterDetails).subscribe((response) => {
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(response).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterDataBits);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, { status: 200, statusText: 'OK' });

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setPrinterDataBits() should handle unsuccess response from API', () => {
    //Arrange
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    service.setPrinterDataBits(fakeData.PrinterDetails).subscribe(response =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the POST API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.shiftEndController.base + environment.shiftEndController.setPrinterDataBits);

    //Assert 3: Verify that the request called is POST HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });  

});
