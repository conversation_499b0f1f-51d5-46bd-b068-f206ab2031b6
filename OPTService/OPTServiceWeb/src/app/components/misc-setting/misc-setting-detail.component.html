<div class="row form-group" [formGroup]="form" [id]="formGroupId">
    <div class="input-group" [ngClass]="labelColClass">
        <label class="col-form-label text-truncate" [for]="id" [title]="labelText">{{labelText}}</label>
    </div>
    <div class="input-group" [ngClass]="textColClass">
        <input type="text" class="form-control" [id]="id" [formControl]="control"
            [ngClass]="{'is-invalid':control?.invalid && (control?.dirty || control?.touched)}"
            [placeholder]="placeHolderText" [attr.aria-describedby]="fieldFeedbackId" [maxlength]="maxLength"
            [readonly]="disabled">
        <div class="input-group-append d-inline" *ngIf="appendText">
            <span class="input-group-text">{{appendText}}</span>
        </div>
        <div [id]="fieldFeedbackId" class="invalid-feedback">
            {{validationErrorText}}
        </div>
    </div>
    <div>
        <span class="badge badge-success badge-config-level" *ngIf="setting.value['Level'] == MiscConfigLevel.Default">Default</span>
        <span class="badge badge-warning badge-config-level" *ngIf="setting.value['Level'] == MiscConfigLevel.GlobalOverride">Global</span>
        <span class="badge badge-info badge-config-level" *ngIf="setting.value['Level'] == MiscConfigLevel.SuffixOverride">Suffix</span>
    </div>
    <div [ngClass]="buttonColClass">
        <button type="button" [id]="actionButtonId" class="btn btn-primary" (click)="doAction($event)"
            [disabled]="control?.invalid || disabled">{{buttonText}}</button>
        <span class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="errorInActionText"
            *ngIf="errorInAction"><i class="bi bi-exclamation-triangle"></i></span>
    </div>
    <div [ngClass]="buttonColClass" *ngIf="setting.value['Level'] == MiscConfigLevel.GlobalOverride">
        <button type="button" [id]="revertDefaultButtonId" class="btn btn-primary btn-revert" (click)="doRevertAction($event)"
            [disabled]="control?.invalid || disabled">Revert to Default</button>
        <span class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="errorInActionText"
            *ngIf="errorInAction"><i class="bi bi-exclamation-triangle"></i></span>
    </div>
    <div [ngClass]="buttonColClass" *ngIf="setting.value['Level'] == MiscConfigLevel.SuffixOverride">
        <button type="button" [id]="revertGlobalButtonId" class="btn btn-primary btn-revert" (click)="doRevertAction($event)"
            [disabled]="control?.invalid || disabled">Revert to Global</button>
        <span class="text-danger ml-2 pt-1" placement="right top" [ngbTooltip]="errorInActionText"
            *ngIf="errorInAction"><i class="bi bi-exclamation-triangle"></i></span>
    </div>
</div>