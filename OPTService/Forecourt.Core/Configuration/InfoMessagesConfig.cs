using Forecourt.Core.Configuration.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Core.Configuration
{
    /// <summary>
    /// Contains all configuration around Info Messages
    /// </summary>
    [HasConfiguration]
    public class InfoMessagesConfig : Loggable, IInfoMessagesConfig
    {
        private const string MaxInfoMessages = "MaxInfoMessageEntries";

        public const string ConfigKeyMaxInfoMessageEntries = Constants.CategoryNameGeneral + Constants.CategorySeparator + MaxInfoMessages;
        public const int DefaultValueMaxInfoMessageEntries = 30;
        private const int MaxValueMaxInfoMessageEntries = 500;
        protected ConfigurableInt ConfigMaxInfoMessageEntries { get; private set; }
 
        /// <inheritdoc />
        public InfoMessagesConfig(IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(InfoMessagesConfig), configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            ConfigMaxInfoMessageEntries = new ConfigurableInt(this, ConfigKeyMaxInfoMessageEntries, DefaultValueMaxInfoMessageEntries);
         }

        /// <inheritdoc/>
        public int MaxInfoMessageEntries()
        {
            var result = ConfigMaxInfoMessageEntries.GetValue();
            if (result > MaxValueMaxInfoMessageEntries)
            {
                result = MaxValueMaxInfoMessageEntries;
            }
            if (result < DefaultValueMaxInfoMessageEntries)
            {
                result = DefaultValueMaxInfoMessageEntries;
            }
            return result;
        }
    }
}
