import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { UploadFileDetails } from '../core/models/uploadFileDetails.model';
import Utils from '../core/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class WebService {

  private baseUrl: string;

  /**
   * The WebService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.webController.base;
  }

  /**
   * Gets the version info details
   */
  public getVersionInfo(): Observable<any> {
    const url = this.baseUrl + environment.webController.GetVersionInfo;
    const options = {
      params: {}
    };

    this.logger.debug(`Getting VersionInfo via url: ${url}`);
    return this.http.get(url, options)
      .pipe(map(response => {
        return response;
      }, catchError(error => this.handleErrorObservable(error))));
  }

  /**
   * Upgrades OPT service
   */
  public upgradeService() {
    const url = this.baseUrl + environment.webController.UpgradeService;
    this.logger.debug(`Upgrade service via ${url}`);
    return this.http.get(url);
  }

  /**
   * Rollbacks OPT service
   */
  public rollbackService() {
    const url = this.baseUrl + environment.webController.RollbackService;
    this.logger.debug(`Rollback service via ${url}`);
    return this.http.get(url);
  }

  /**
   * Restarts OPT service
   */
  public restartService() {
    const url = this.baseUrl + environment.webController.RestartService;
    this.logger.debug(`Restart service via ${url}`);
    return this.http.get(url);
  }

  /**
   * Executes a file action invoking a file service
   * @param service The service name to call depending on the type of file
   * @param data file data to save
   */
  public commonFileAction(service: string, data: UploadFileDetails) {
    const url = this.baseUrl + service;
    this.logger.debug(`Executing common file action via ${url}`);
    return this.http.post(url, data);
  }

  /**
   * Back up database
   */
  public backupDatabase() {
    const url = this.baseUrl + environment.webController.BackupDatabase;
    this.logger.debug(`Backup OPT service database service via ${url}`);
    return this.http.get(url);
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
