using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;

namespace Forecourt.SecondaryAuth.Workers.Interfaces
{
    /// <summary>
    /// Provides a named interface for <see cref="ISecAuthIntegratorOut{TMessageTracking}"/>, for Unity purposed
    /// </summary>
    public interface IAnprSecAuthOutWorker: ISecAuthIntegratorOut<IMessageTracking>
    {
    }
}
