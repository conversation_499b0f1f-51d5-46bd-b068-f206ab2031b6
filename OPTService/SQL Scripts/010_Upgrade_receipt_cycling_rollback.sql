USE [Hydra]

PRINT N'HOPT-1554 - OPT Service - Retailix Transaction Number cycling, and Receipts Rollback...Begin';
PRINT N'Updating Procedure [dbo].[GetFuelTransactions]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetFuelTransactions') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetFuelTransactions
END
GO

CREATE procedure [dbo].[GetFuelTransactions]
 @startTime DateTime2,
 @endTime DateTime2
 begin
  select rowid as TransactionId, TransactionTime, FuelCode as GradeCode, WashCode, GradeName,
         WashName, PumpDetails, CardNumber, FuelQuantity, WashQuantity, Amount, FuelCategory,
         WashCategory, FuelSubcategory, WashSubcategory, DiscountName, DiscountCode, DiscountValue,
         DiscountCardNumber, LocalAccountMileage, LocalAccountRegistration, TxnNumber from Transactions
   where (@startTime is null or TransactionTime >= @startTime) and
         (@endTime is null or TransactionTime <= @endTime) and
         (FuelCode is not null or WashCode is not null)
 end
 GO

PRINT N'HOPT-1554 - OPT Service - Retailix Transaction Number cycling, and Receipts Rollback...End';
PRINT N'';

GO