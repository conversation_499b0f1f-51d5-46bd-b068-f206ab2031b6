USE [Hydra]
GO

PRINT N'ADO498985 - Set Contactless Custom Data overrides (in advanced config keys)..Begin';

DECLARE @catIdGeneral INT;
SELECT @catIdGeneral = cc.Id FROM dbo.ConfigurationCategory cc WHERE cc.Category = 'GENERAL';

DECLARE @headerIdSC INT;
SELECT @headerIdSC =  ch.Id FROM dbo.ConfigurationHeader ch WHERE ch.Description = 'Site Controller';

DECLARE @val NVARCHAR(1024) = CONCAT(
	'mchip_cvm_cap_above_limit=MC_MCHIP_CAP_ABOVE_LIMIT,',
	'mchip_cvm_cap_below_limit=MC_MCHIP_CAP_BELOW_LIMIT,',
	'mstripe_cvm_cap_above_limit=MC_MSTRIPE_CAP_ABOVE_LIMIT,',
	'mstripe_cvm_cap_below_limit=MC_MSTRIPE_CAP_BELOW_LIMIT');

EXEC dbo.SetConfigValue @headerId = @headerIdSC, @categoryId = @catIdGeneral, @key = 'CustomDataOverride', @value = @val;

PRINT N'ADO498985 - Set Contactless Custom Data overrides (in advanced config keys)..End';
