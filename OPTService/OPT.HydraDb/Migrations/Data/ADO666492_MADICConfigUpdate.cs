using FluentMigrator;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Extensions;
using OPT.HydraDb.Common;
using System.Configuration;
using cfgConstants = Forecourt.Core.Configuration.Constants;
using intConstants = Forecourt.Core.Configuration.Constants.Integrator;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed by MADIC integrations (mostly Magical Advanced Config keys)
    /// </summary>
    [Migration(20050020, "ADO#666492 - Set all configuration needed for MADIC integrations"), Tags(Constants.MigrationTypeData)]
    public class ADO666492_MADICConfigUpdate : Migrationable
    {
        private const string DefaultClient = ClientSettings.DefaultValueClient;
        private const string baseUrl = "http://evoserv.evolutiontlm.co.uk:38410";
        private const string baseUrlApi = baseUrl + "/api";

        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC) 
            {
                return;
            }

            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 1");

            // Integrations 
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'POSTYPE', @value = '{PosType.MadicApiSignalR}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorBos}', @value = '{PosType.MadicApiSignalR}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorPaymentConfig}', @value = '{intConstants.PaymenConfigeSocketHSqlDb}', @category = '{cfgConstants.CategoryNameSiteInfo}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = '{intConstants.ConfigKeyIntegratorPump}', @value = '{intConstants.PumpTypeDoms}', @category = '{cfgConstants.CategoryNameSiteInfo}'");

            // Endpoints Config - API
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'CONFIG', @value = '{baseUrlApi}', @category = '{cfgConstants.CategoryNameEndPointsApi}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BOS', @value = '{baseUrlApi}', @category = '{cfgConstants.CategoryNameEndPointsApi}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'POS', @value = '{baseUrlApi}', @category = '{cfgConstants.CategoryNameEndPointsApi}'");

            // Endpoints Config - SIGNALR
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'ENDPOINT', @value = '{baseUrl}', @category = '{cfgConstants.CategoryNameEndPointsSignalR}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'HUB-NAME', @value = 'EvoServHub', @category = '{cfgConstants.CategoryNameEndPointsSignalR}'");

            // Misc
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:DomsSetupTcp', @value = '00:00:01', @category = '{cfgConstants.CategoryNameWorkers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'BackgroundTask:Interval:MadicRestApiBosOutWorker', @value = '00:01:00', @category = '{cfgConstants.CategoryNameWorkers}'");
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'AcknowledgeServerMessage:Enabled:', @value = 'False', @category = '{cfgConstants.CategoryNameSignalRHubClient}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
            // Get the client
            var client = ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyHydraDbClientSettings, DefaultClient, null);
            if (client.ToLower() != ClientSettings.ClientNameMADIC)
            {
                return;
            }

            Execute.Sql($"EXEC dbo.SetNozzleUpForKioskUse @flag = 0");
        }
    }
}
