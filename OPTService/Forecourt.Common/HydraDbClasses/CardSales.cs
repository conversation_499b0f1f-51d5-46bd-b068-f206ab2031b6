namespace OPT.Common.HydraDbClasses
{
    public class CardSales
    {
        public short CardRef { get; }
        public uint Amount { get; }
        public string ProductName { get; }

        // ReSharper disable once UnusedMember.Global
        public CardSales(int cardRef, long amount, string productName)
        {
            CardRef = (short) cardRef;
            Amount = (uint) amount;
            ProductName = productName;
        }
    }
}