import { Component, Input } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { NgbPanelChangeEvent } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-card-aids',
  templateUrl: './card-aids.component.html',
  styleUrls: ['./card-aids.component.css']
})
export class CardAidsComponent {

  @Input() form: FormGroup;
  @Input() formArrayName: string;

  // Active sections in sub-controls
  activeCardAid: string = '';

  /**
   * The getter for the card AIDs
   */
  get cardAids(): FormArray {
    return this.form?.get(this.formArrayName) as FormArray;
  }

  toggleActiveCardAid(props: NgbPanelChangeEvent): void {
    this.activeCardAid = props.panelId;
  }
}
