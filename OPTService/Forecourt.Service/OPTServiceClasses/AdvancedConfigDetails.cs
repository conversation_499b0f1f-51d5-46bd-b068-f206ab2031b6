using Htec.Foundation.Configuration;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class AdvancedConfigDetails
    {
        public class Timeouts
        {
            public int PaymentTimeoutOpt { get; set; }
            public int PaymentTimeoutPod { get; set; }
            public int PaymentTimeoutMixed { get; set; }
            public int PaymentTimeoutNozzleDown { get; set; }
            public int TimeoutKiosk { get; set; }
            public int TimeoutSecAuth { get; set; }
        }

        public bool AutoAuth { get; set; }
        public bool MediaChannel { get; set; }
        public bool UnmannedPseudoPos { get; set; }
        public bool AsdaDayEndReport { get; set; }
        public List<string> LoyaltyAvailable { get; set; }
        public int PaymentTimeoutOpt { get; set; }
        public int PaymentTimeoutPod { get; set; }
        public int PaymentTimeoutMixed { get; set; }
        public int PaymentTimeoutNozzleDown { get; set; }
        public int TimeoutKiosk { get; set; }
        public int TimeoutSecAuth { get; set; }
        public int TillNumber { get; set; }
        public int FuelCategory { get; set; }
        public bool ForwardFuelPriceUpdate { get; set; }
        public bool FuellingIndefiniteWait { get; set; }
        public int FuellingWaitMinutes { get; set; }
        public int FuellingBackoffAuth { get; set; }
        public int FuellingBackoffPreAuth { get; set; }
        public int FuellingBackoffStopStart { get; set; }
        public int FuellingBackoffStopOnly { get; set; }
        public int PosClaimNumber { get; set; }
        public int FilePruneDays { get; set; }
        public int TransactionPruneDays { get; set; }
        public int ReceiptPruneDays { get; set; }
        public bool NozzleUpForKioskUse { get; set; }
        public bool UseReplaceNozzleScreen { get; set; }
        public uint MaxFillOverride { get; set; }
        public string SiteType { get; set; }
        public string SiteName { get; set; }
        public string VatNumber { get; set; }
        public int CurrencyCode { get; set; }
        public bool LocalAccountsEnabled { get; set; }
        public List<CardReferenceDetails> CardReferences { get; set; }
        public List<CategoryConfiguration> ConfigurationCategories { get; set; }
        public ESocketPosConfigDetails ESocketPosConfig { get; set; }

        /// <summary>
        /// Current POS integrator
        /// </summary>
        public string PosType { get; set; }

        /// <summary>
        /// List of possible Pos integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> PosTypes { get; set; }

        /// <summary>
        /// Current Pump integrator
        /// </summary>
        public string PumpType { get; set; }

        /// <summary>
        /// List of possible Pump integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> PumpTypes { get; set; }

        /// <summary>
        /// Current Mobile (Payment) integrator
        /// </summary>
        public string MobilePaymentType { get; set; }

        /// <summary>
        /// List of possible Mobile (Payment) integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> MobilePaymentTypes { get; set; }

        /// <summary>
        /// Current Mobile (Pos) integrator
        /// </summary>
        public string MobilePosType { get; set; }

        /// <summary>
        /// List of possible Mobile (Pos) integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> MobilePosTypes { get; set; }

        /// <summary>
        /// Current BosType integrator
        /// </summary>
        public string BosType { get; set; }

        /// <summary>
        /// List of possible BosType integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> BosTypes { get; set; }

        /// <summary>
        /// Current SecAuth integrator
        /// </summary>
        public string SecAuthType { get; set; }

        /// <summary>
        /// List of possible SecAuth integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> SecAuthTypes { get; set; }

        /// <summary>
        /// Current PaymentHost integrator
        /// </summary>
        public string PaymentConfigType { get; set; }

        /// <summary>
        /// List of possible PaymentHost integrators
        /// </summary>
        public IEnumerable<IntegrationTypeSetting> PaymentConfigTypes { get; set; }

        /// <summary>
        /// Class to describe each type of Integration setting, maps directly to UI equivalent
        /// </summary>
        public class IntegrationTypeSetting
        {
            private KeyValuePair<string, string> _kvp;

            /// <summary>
            /// Main constructor
            /// </summary>
            /// <param name="description">Description for the pair</param>
            /// <param name="value">Value for the pair</param>
            public IntegrationTypeSetting(string value, string description)
            {
                _kvp = new KeyValuePair<string, string>(description, value);
            }

            /// <summary>
            /// Key for the pair, but actually looks like the description
            /// </summary>
            [JsonProperty("key")]
            public string Key => _kvp.Key;

            /// <summary>
            /// Value for the pair
            /// </summary>
            [JsonProperty("value")]
            public string Value => _kvp.Value;
        }
    }
}