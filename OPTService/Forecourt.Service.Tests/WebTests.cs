using FluentAssertions;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Pump;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.Workers.Interfaces;
using OPTService;
using OPTService.OPTServiceClasses;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO.Abstractions;
using Xunit;

namespace OPTServiceUnitTests
{
    public class WebTests
    {
        private readonly IHtecLogManager _logManager;
        private readonly ICore _core;
        private readonly IWeb _web;
        private readonly IConfigurationManager _configurationManager;
        private readonly IIntegratorFactories _integratorFactories;
        private readonly IFileVersionInfoHelper _fileVersionInfo;
        private readonly IFileSystem _fileSystem;
        private readonly IServiceFilesHelper _serviceFilesHelper;
        private readonly IHydraDb _hydraDb;

        public WebTests()
        {
            var logger = Substitute.For<IHtecLogger>();
            _logManager = Substitute.For<IHtecLogManager>();
            _core = Substitute.For<ICore>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(new NameValueCollection());
            _integratorFactories = Substitute.For<IIntegratorFactories>();
            _fileVersionInfo = Substitute.For<IFileVersionInfoHelper>();
            _fileSystem = Substitute.For<IFileSystem>();
            _serviceFilesHelper = Substitute.For<IServiceFilesHelper>();
            _hydraDb = Substitute.For<IHydraDb>();

            _web = new Web(_core, logger, _configurationManager, _integratorFactories, _fileVersionInfo, _fileSystem, _serviceFilesHelper, _hydraDb,
                Substitute.For<IControllerWorker>(), Substitute.For<IToOptWorker>());
        }

        [Fact]
        public void test_get_generic_opt_config()
        {
            // Arrange

            // Act
            GenericOptConfigDetails configDetails = _web.GetGenericOptConfig();

            // Assert
            configDetails.Should().NotBeNull();
        }

        #region Opts() Tests
        public IEnumerable<Opt> GetTestData_Opts()
        {
            var opt = new Opt(_logManager, "1", 1, "Hydra 1", _configurationManager);
            opt.AddPump(new Pump(1, Substitute.For<IHydraDb>(), _logManager, "Pump"));
            yield return opt;

            opt = new Opt(_logManager, "4", 599, "Hydra 1", _configurationManager);
            opt.AddPump(new Pump(1, Substitute.For<IHydraDb>(), _logManager, "Pump"));
            yield return opt;
        }

        [Fact]
        public void test_get_opts_none_from_db()
        {
            // Arrange
            _core.AllOpts.AllOpts.Returns(new List<Opt>());

            // Act
            var result = _web.Opts();

            // Assert
            result.Count.Should().Be(0);
        }

        [Theory]
        [InlineData("100")]
        [InlineData("599")]
        public void test_get_opts_selects_none(string param)
        {
            // Arrange
            _core.AllOpts.AllOpts.Returns(GetTestData_Opts());

            // Act
            var result = _web.Opts(param);

            // Assert
            result.Count.Should().Be(0);
        }

        [Fact]
        public void test_get_opts_selects_one()
        {
            // Arrange
            _core.AllOpts.AllOpts.Returns(GetTestData_Opts());

            // Act
            var result = _web.Opts("1");

            // Assert
            result.Count.Should().Be(1);
        }

        [Fact]
        public void test_get_opts_selects_all()
        {
            // Arrange
            _core.AllOpts.AllOpts.Returns(GetTestData_Opts());

            // Act
            var result = _web.Opts();

            // Assert
            result.Count.Should().Be(2);
        }

        #endregion

        #region Receipt Headers and Footers

        public class ReceiptLinesTestData
        {
            public string SingleLine { get; }
            public string[] MultiLine { get; }

            public ReceiptLinesTestData(string singleLine, string[] multiLine)
            {
                SingleLine = singleLine;
                MultiLine = multiLine;
            }
        }

        public static TheoryData<ReceiptLinesTestData> ReceiptHeaders => new TheoryData<ReceiptLinesTestData>
        {
            new ReceiptLinesTestData("", new string[] { "" }),
            new ReceiptLinesTestData("\n", new string[] { "", "" }),
            new ReceiptLinesTestData("Line 1\nLine 2", new string[] { "Line 1", "Line 2" }),
        };

        public static TheoryData<ReceiptLinesTestData> ReceiptFooters => new TheoryData<ReceiptLinesTestData>
        {
            new ReceiptLinesTestData("", new string[] { "" }),
            new ReceiptLinesTestData("\n", new string[] { "", "" }),
            new ReceiptLinesTestData("Line 1\nLine 2", new string[] { "Line 1", "Line 2" }),
        };

        [Theory]
        [MemberData(nameof(ReceiptHeaders))]
        public void test_get_opts_sets_receipt_header(ReceiptLinesTestData testData)
        {
            // Arrange
            var opt = Substitute.For<IOpt>();
            opt.Connected.Returns(true);
            opt.ReceiptHeader.Returns(testData.SingleLine);

            var opts = new List<IOpt> { opt };
            _core.AllOpts.AllOpts.Returns(opts);

            // Act
            var result = _web.Opts();

            result[0].ReceiptHeaders.Should().BeEquivalentTo(testData.MultiLine);
        }

        [Theory]
        [MemberData(nameof(ReceiptHeaders))]
        public void test_set_receipt_header(ReceiptLinesTestData testData)
        {
            // Arrange
            const string optId = "1";

            // Act
            _web.SetReceiptHeaders(optId, testData.MultiLine);

            // Assert
            _core.ControllerWorker.Received().SetReceiptHeader(optId, testData.SingleLine);
        }

        [Fact]
        public void test_set_receipt_header_max_length()
        {
            // Arrange
            const string optId = "1";
            string[] headers = new string[] {"This receipt header is too long and will fail validation"};

            // Act
            var result = _web.SetReceiptHeaders(optId, headers);

            // Assert
            result.Should().Be("Invalid Receipt Header");
        }

        [Theory]
        [MemberData(nameof(ReceiptFooters))]
        public void test_get_opts_sets_receipt_footer(ReceiptLinesTestData testData)
        {
            // Arrange
            var opt = Substitute.For<IOpt>();
            opt.Connected.Returns(true);
            opt.ReceiptFooter.Returns(testData.SingleLine);

            var opts = new List<IOpt> { opt };
            _core.AllOpts.AllOpts.Returns(opts);

            // Act
            var result = _web.Opts();

            result[0].ReceiptFooters.Should().BeEquivalentTo(testData.MultiLine);
        }

        [Theory]
        [MemberData(nameof(ReceiptFooters))]
        public void test_set_receipt_footer(ReceiptLinesTestData testData)
        {
            // Arrange
            const string optId = "1";

            // Act
            _web.SetReceiptFooters(optId, testData.MultiLine);

            // Assert
            _core.ControllerWorker.Received().SetReceiptFooter(optId, testData.SingleLine);
        }

        [Fact]
        public void test_set_receipt_footer_max_length()
        {
            // Arrange
            const string optId = "1";
            string[] headers = new string[] { "This receipt footer is too long and will fail validation" };

            // Act
            var result = _web.SetReceiptFooters(optId, headers);

            // Assert
            result.Should().Be("Invalid Receipt Footer");
        }

        #endregion

        #region Pump tests

        public IEnumerable<Pump> GetTestData_Pumps()
        {
            var hydraDb = Substitute.For<IHydraDb>();

            yield return new Pump(1, hydraDb, _logManager, "Pump");
            yield return new Pump(2, hydraDb, _logManager, "Pump");
            yield return new Pump(3, hydraDb, _logManager, "Pump");
        }

        [Fact]
        public void test_Pumps_returns_empty_result_when_there_is_no_pumps()
        {
            // Arrange
            _core.AllPumps.AllPumps.Returns(new List<Pump>());

            // Act
            var actual = _web.Pumps();

            // Assert
            actual.Should().HaveCount(0);
        }

        [Fact]
        public void test_Pumps_returns_empty_result_when_the_pump_is_not_found()
        {
            // Arrange
            var pumpNumber = 5;
            _core.AllPumps.AllPumps.Returns(GetTestData_Pumps());

            // Act
            var actual = _web.Pumps(pumpNumber);

            // Assert
            actual.Should().HaveCount(0);
        }

        [Fact]
        public void test_Pumps_returns_single_item()
        {
            // Arrange
            var pumpNumber = 1;
            _core.AllPumps.AllPumps.Returns(GetTestData_Pumps());

            // Act
            var actual = _web.Pumps(pumpNumber);

            // Assert
            actual.Should().HaveCount(1).And.Contain(x => x.Number.Equals((byte)pumpNumber));
        }

        [Fact]
        public void test_Pumps_returns_multiple_items_pump_number_not_provided()
        {
            // Arrange
            _core.AllPumps.AllPumps.Returns(GetTestData_Pumps());

            // Act
            var actual = _web.Pumps();

            // Assert
            actual.Should().HaveCount(3).And.Contain(x => x.Number.Equals(1)).And.Contain(x => x.Number.Equals(2)).And.Contain(x => x.Number.Equals(3));
        }

        [Fact]
        public void test_Pumps_returns_multiple_items_pump_number_equals_zero()
        {
            // Arrange
            _core.AllPumps.AllPumps.Returns(GetTestData_Pumps());

            // Act
            var actual = _web.Pumps(0);

            // Assert
            actual.Should().HaveCount(3).And.Contain(x => x.Number.Equals(1)).And.Contain(x => x.Number.Equals(2)).And.Contain(x => x.Number.Equals(3));
        }

        #endregion Pump tests
    }
}
