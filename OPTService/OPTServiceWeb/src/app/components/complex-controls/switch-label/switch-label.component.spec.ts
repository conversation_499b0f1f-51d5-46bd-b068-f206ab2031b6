import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ControlContainer, FormControl, FormGroup, FormGroupDirective } from '@angular/forms';

import { SwitchLabelComponent } from './switch-label.component';

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('SwitchLabelComponent', () => {
  let component: SwitchLabelComponent;
  let fixture: ComponentFixture<SwitchLabelComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SwitchLabelComponent],
      providers: [
        { provide: ControlContainer, useValue: fgd }
      ]
    })
    fixture = TestBed.createComponent(SwitchLabelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('doAction', () => {

    it('should emit action', () => {
      spyOn(component.action, 'emit');

      component.doAction();

      expect(component.action.emit).toHaveBeenCalled();
    });
  });
});
