using Forecourt.Pos.Hubs.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using Microsoft.AspNet.SignalR.Hubs;

namespace Forecourt.Pos.Hubs
{
    /// <summary>
    /// Publishes the requests from <see cref="IPosIntegratorOut{IMessageTracking}"/> to the outside world
    /// </summary>
    [HubName(HubName)]
    public class PosHub : Hub<PosMessage, IMessageTracking>, IPosHub, IPosHubIn
    {
        /// <summary>
        /// Name of the Pos SignalR Hub
        /// </summary>
        public const string HubName = "Htec-Hubs:Forecourt-Service:Pos";

        /// <inheritdoc/>
        public PosHub(IHubbableIn<PosMessage, IMessageTracking> hubbable) : base(hubbable) { }
    }
}
