USE [Hydra]

PRINT N'Updating Procedure [dbo].[GetSiteInfo]...';
PRINT N'';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetSiteInfo') AND TYPE in ('P', 'PC'))
BEGIN
	DROP PROCEDURE GetSiteInfo
END
GO

CREATE PROCEDURE GetSiteInfo
AS
BEGIN
	SELECT 
	Mode AS ReceiptLayoutMode, 
	SiteName, 
	VATNumber,
	NozzleUpForKioskUse, 
	UseReplaceNozzleScreen, 
	CurrencyCode, 
	ForwardFuelPriceUpdate, 
	TillNumber, 
	FuelCategory, 
	MaxFillOverride
	FROM SiteInfo
END

GO
PRINT N'.End';
PRINT N'';

GO
