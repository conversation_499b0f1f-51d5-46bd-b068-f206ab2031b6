using FluentMigrator;
using OPT.HydraDb.Common;
using cfgConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to set config needed for HydraMobileWorker embedded timer
    /// </summary>
    [Migration(26200010, "ADO#741349 - Set further configuration needed for HydraMobile worker"), Tags(Constants.MigrationTypeData)]
    public class ADO741349_HydraMobileConfigUpdate : Migrationable
    {
        /// <inheritdoc cref="Migrationable"/>>
        public override void Up()
        {
            // Timers
            Execute.Sql($"EXEC dbo.UpsertConfigurationDetail @key = 'Timer:Interval:HydraMobileWorker+EmbeddedTimerable', @value = '04:00:00', @category = '{cfgConstants.CategoryNameTimers}'");
        }

        /// <inheritdoc cref="Migrationable"/>>
        public override void Down()
        {
        }
    }
}
