using System;

namespace OPT.Common.HydraDbClasses
{
    public class OtherEvent
    {
        public long TransactionId { get; }
        public DateTime TransactionTime { get; }

        // ReSharper disable once UnusedMember.Global
        public OtherEvent(long transactionId, DateTime transactionTime)
        {
            TransactionId = transactionId;
            TransactionTime = transactionTime;
        }
    }
}
