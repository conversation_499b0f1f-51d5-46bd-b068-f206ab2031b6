using Htec.Logger.log4net.Helpers.Interfaces;
using System.Text;

namespace OPT.Common.Helpers
{
    /// <summary>
    /// Interface for handling common logging methods.
    /// </summary>
    public interface ILoggingHelper : IXmlConfigHelper
    {
        /// <summary>
        /// Write entry to the journal file.
        /// </summary>
        /// <param name="entry">Entry to write.</param>
        void WriteDayEndJournal(StringBuilder entry);

        /// <summary>
        /// Validate the unmanned journal file.
        /// </summary>
        /// <param name="unmannedJournalFile">Target journal file name.</param>
        void CheckUnmannedJournal(string unmannedJournalFile);
    }
}