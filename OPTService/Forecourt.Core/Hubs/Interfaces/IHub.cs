using System;

namespace Htec.Foundation.Web.Hubs.Interfaces
{
    public interface IHub: Microsoft.AspNet.SignalR.Hubs.IHub
    {
    }

    /// <summary>
    /// Root interface for the Ack messages that are received from the HubClients
    /// </summary>
    public interface IHubIn : IHub
    {
        /// <summary>
        /// Acknowledges receipt of published message, on the hub
        /// </summary>
        /// <param name="messageId">Unique message id</param>
        void AcknowledgeHubMessage(string messageId);
    }
}
