import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NGXLogger } from 'ngx-logger';
import { Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AdvancedConfigProperty } from '../core/enums/advancedConfigProperty.enum';
import { CardProperty } from '../core/enums/cardProperty.enum';
import { IntegrationType } from '../core/enums/integrationType.enum';
import { AdvancedConfig } from '../core/models/advancedConfig.model';
import { CardReferenceDetails } from '../core/models/cardReferenceDetails.model';
import { ConfigurationCategory } from '../core/models/configurationCategory.model';
import { ESocketPosConfigDetails } from '../core/models/eSocketPosConfigDetails.model';
import Utils from '../core/utils/utils';
import { PaymentTimeoutType } from '../core/enums/paymentTimeoutType.enum';

@Injectable({
  providedIn: 'root'
})
export class AdvancedConfigService {

  private readonly API_SET_PREFIX = 'set';
  private readonly API_CLEAR_PREFIX = 'clear';
  private baseUrl: string;
  private cachedAdvancedConfig: AdvancedConfig | undefined;
  private configDataChanged = new Subject<any>();

  /**
   * The AdvancedConfigService constructor
   * @param http The http client used for requests
   * @param logger The NGXLogger object
   */
  constructor(private http: HttpClient, private logger: NGXLogger) {
    this.baseUrl = Utils.getBaseURLOptService(document) + environment.advancedController.base;
  }

  /** 
   * Gets the Config data changed event
   */
  public getConfigDataChanged(): Observable<any> {
    return this.configDataChanged.asObservable();
  }

  /**
   * Gets the advanced configuration
   */
  public getAdvancedConfig(forceReload: boolean = false): Observable<AdvancedConfig> {
    let self = this;

    // Only call the API if forceReload is set to true or cached data is undefined
    if (forceReload || self.cachedAdvancedConfig === undefined) {
      const url = this.baseUrl + environment.advancedController.getAdvancedConfig;
      const params = {
      };
      const options = {
        params: params
      };
  
      this.logger.debug(`Getting Advanced Config via url: ${url}`);
      return this.http.get<AdvancedConfig>(url, options)
        .pipe(
          map(response => {
            self.cachedAdvancedConfig = response;
            self.configDataChanged.next();
            return response;
          }, 
          catchError(error => self.handleErrorObservable(error)))
        );
    }
    else {
      return of(self.cachedAdvancedConfig);
    } 
  }

  /**
   * Sets the value for a specific (Payment) Timeout type value
   * @param siteType
   */
  public setPaymentTimeout(timeoutType: PaymentTimeoutType, advancedConfig: AdvancedConfig, propertyName: AdvancedConfigProperty) {
    let key = Object.keys(advancedConfig).indexOf(propertyName);
    let value = Object.values(advancedConfig)[key];
    const url = this.baseUrl + environment.advancedController.setPaymentTimeout + '?type=' + timeoutType + '&value=' + value;
    this.logger.debug(`Setting ${timeoutType} Type: ${value} via ${url}`);
    return this.http.post(url, {});
  }

  /**
   * Sets the value for the specified property
   * @param propertyName The name of the property to be set 
   * @param advancedConfig The new advanced configuration data
   */
  public setProperty(propertyName: AdvancedConfigProperty, advancedConfig: AdvancedConfig) {
    const url = this.baseUrl + environment.advancedController[this.API_SET_PREFIX + propertyName];
    this.logger.debug(`Setting ${propertyName}: ${Object.values(advancedConfig)[Object.keys(advancedConfig).indexOf(propertyName)]} via ${url}`);
    return this.http.post(url, advancedConfig);
  } 

  /**
   * Sets a property
   * @param eSocketConfig The eSocket POS Config data
   * @param serviceName The URL to use for the update
   */
  public setESocketProperty(eSocketConfig: ESocketPosConfigDetails, serviceName: string) {
    const url = this.baseUrl + serviceName;
    const options = {
      params: {}
    };
    this.logger.debug(`Setting eSocket Config property in ${url}`);
    return this.http.post(url, eSocketConfig, options);
  }

  /**
   * Sets the value for the specified property
   * @param propertyName The name of the property to be set 
   * @param advancedConfig The new advanced configuration data
   */
  public setCardProperty(propertyName: CardProperty, cardReferenceDetails: CardReferenceDetails) {
    const url = this.baseUrl + environment.advancedController[this.API_SET_PREFIX + propertyName];
    this.logger.debug(`Setting ${propertyName}: ${Object.values(cardReferenceDetails)[Object.keys(cardReferenceDetails).indexOf(propertyName)]} via ${url}`);
    return this.http.post(url, cardReferenceDetails);
  } 

  /**
   * Clear the value for the specified property
   * @param propertyName The name of the property to be clean 
   * @param advancedConfig The new advanced configuration data
   */
  public clearCardProperty(propertyName: CardProperty, cardReferenceDetails: CardReferenceDetails) {
    const url = this.baseUrl + environment.advancedController[this.API_CLEAR_PREFIX + propertyName];
    this.logger.debug(`Cleaning ${propertyName} for card reference: ${cardReferenceDetails.Reference} via ${url}`);
    return this.http.post(url, cardReferenceDetails);
  }

  /**
   * Gets the IsConfigBatch value from the OPT service
   * @returns 
   */
  public getIsConfigBatch():Observable<any>{
    const url = this.baseUrl + environment.advancedController.getIsConfigBatch;
    this.logger.debug(`Getting IsConfigBatch via ${url}`);
    return this.http.get(url);
  }

  /**
   * Sets the value for the configuration categories
   * @param advancedConfig The new magical advanced configuration data
   */
   public setCategories(configurationCategories: Array<ConfigurationCategory>) {
    const url = this.baseUrl +  environment.advancedController.setCategories;
    this.logger.debug(`Setting configuration categories:  ${JSON.stringify(configurationCategories)} via ${url}`);
    return this.http.post(url, configurationCategories);
  } 
 
  /**
   * Sets the value for a specific Integration type
   * @param siteType
   */
  public setIntegrationType(integrationType: IntegrationType, value: string) {
    const url = this.baseUrl + environment.advancedController.setIntegrationType + '?integrationType=' + integrationType + '&value=' + value;
    this.logger.debug(`Setting {integrationType} Type: ${value} via ${url}`);
    return this.http.post(url, {});
  }

  /**
   * Sets the value for site name
   * @param siteName 
   */
  public setSiteName(siteName: string){
    const url = this.baseUrl + environment.advancedController.setSiteName;
    this.logger.debug(`Setting site name: ${siteName} via ${url}`);
    return this.http.post(url, {SiteName: siteName});
  }

  /**
   * Sets the value for VAT number
   */
  public setVatNumber(vatNumber: string){
    const url = this.baseUrl + environment.advancedController.setVatNumber;
    this.logger.debug(`Setting VAT number: ${vatNumber} via ${url}`);
    return this.http.post(url, {VatNumber: vatNumber});
  }

  /**
   * Sets the currency code
   * @param currencyCode 
   */
  public setCurrencyCode(currencyCode: string){
    const url = this.baseUrl + environment.advancedController.setCurrencyCode;
    this.logger.debug(`Setting currency code: ${currencyCode} via ${url}`);
    return this.http.post(url, {CurrencyCode: currencyCode});
  }

  /**
   * Sets the nozzle up for kiosk use field
   * @param nozzleUpForKioskUse 
   */
    public setNozzleUpForKioskUse(nozzleUpForKioskUse: boolean){
    const url = this.baseUrl + environment.advancedController.setNozzleUpForKioskUse;
    this.logger.debug(`Setting nozzle up for kiosk use: ${nozzleUpForKioskUse} via ${url}`);
    return this.http.post(url, {NozzleUpForKioskUse: nozzleUpForKioskUse});
  }

  /**
   * Sets the use replace nozzle screen field
   * @param useReplaceNozzleScreen 
   */
  public setUseReplaceNozzleScreen(useReplaceNozzleScreen: boolean){
    const url = this.baseUrl + environment.advancedController.setUseReplaceNozzleScreen;
    this.logger.debug(`Setting use replace nozzle screen: ${useReplaceNozzleScreen} via ${url}`);
    return this.http.post(url, {UseReplaceNozzleScreen: useReplaceNozzleScreen});
  }

  /**
   * Sets max fill oerride field
   * @param maxFillOverride The Maximum fill override to be set
   */
  public setMaxFillOverride(maxFillOverride: number){
    const url = this.baseUrl + environment.advancedController.setMaxFillOverride;
    this.logger.debug(`Setting max fill override: ${maxFillOverride} via ${url}`);
    return this.http.post(url, {MaxFillOverride: maxFillOverride});
  }

  /**
   * Clear max fill override field
   */
  public clearMaxFillOverride(){
    const url = this.baseUrl + environment.advancedController.clearMaxFillOverride;
    this.logger.debug(`Clearing max fill override via ${url}`);
    return this.http.post(url, {});
  }

  /**
    * Handles error observable
    * @param error 
    * @returns  
    */
   private handleErrorObservable(error: Response | any) {
    return throwError(error.message || error);
  }
}
