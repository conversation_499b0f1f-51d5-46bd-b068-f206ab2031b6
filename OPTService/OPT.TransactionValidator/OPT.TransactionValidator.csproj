<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net462;net472;net48</TargetFrameworks>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Foundation\**" />
		<EmbeddedResource Remove="Foundation\**" />
		<None Remove="Foundation\**" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Extensions\NameValueCollectionExtensions.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CSharpFunctionalExtensions" Version="[2.40.0,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[5.0.0,)" />
		<PackageReference Include="Htec.Logger.log4Net" Version="[7.0.0,)" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>
</Project>

