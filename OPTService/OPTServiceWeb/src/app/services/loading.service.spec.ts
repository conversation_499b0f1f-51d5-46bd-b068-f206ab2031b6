import { TestBed } from '@angular/core/testing';
import { LoadingService } from './loading.service';

describe('LoadingService', () => {
  let loadingService: LoadingService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        LoadingService
      ]
    });
    loadingService = TestBed.inject(LoadingService);
  });

  it('should be created', () => {
    expect(loadingService).toBeTruthy();
  });

  it('initialization is correct', () => {
    //Arrange
    //Act
    //Assert 3: Verify that the request called is GET HTTP method only
    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService.lastRefresh).toBeUndefined();
    expect(loadingService.showError).toBeFalse();
  });

  it('.showLoadingScreen sets data correctly', () => {
    //Arrange
    //Act
    loadingService.showLoadingScreen();

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(loadingService.showLoading).toBeTrue();
    expect(loadingService.lastRefresh).toBeUndefined();
    expect(loadingService.showError).toBeFalse();
  });

  it('.hideLoadingScreen sets data correctly', () => {
    //Arrange
    //Act
    loadingService.hideLoadingScreen();

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService.lastRefresh).toBeDefined();
    expect(loadingService.showError).toBeFalse();
  });

  it('.errorDuringLoading() sets data correctly', () => {
    loadingService.errorDuringLoading();

    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService.lastRefresh).toBeUndefined();
    expect(loadingService.showError).toBeTrue();
  });

  it('should handle multiple calls to .showLoadingScreen() and .hideLoadingScreen()', () => {
    loadingService.showLoadingScreen();
    loadingService.showLoadingScreen();

    expect(loadingService.showLoading).toBeTrue();
    expect(loadingService['_activeLoadings']).toEqual(2);
    expect(loadingService.showError).toBeFalse();
    expect(loadingService.lastRefresh).toBeUndefined();

    loadingService.hideLoadingScreen();
    loadingService.hideLoadingScreen();

    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService['_activeLoadings']).toEqual(0);
    expect(loadingService.showError).toBeFalse();
    expect(loadingService.lastRefresh).toBeDefined();
  });

  it('should handle multiple calls to .showLoadingScreen() and .errorDuringLoading()', () => {
    loadingService.showLoadingScreen();
    loadingService.showLoadingScreen();

    expect(loadingService.showLoading).toBeTrue();
    expect(loadingService['_activeLoadings']).toEqual(2);
    expect(loadingService.showError).toBeFalse();
    expect(loadingService.lastRefresh).toBeUndefined();

    loadingService.errorDuringLoading();
    loadingService.errorDuringLoading();

    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService['_activeLoadings']).toEqual(0);
    expect(loadingService.showError).toBeTrue();
    expect(loadingService.lastRefresh).toBeUndefined();
  });

  it('should handle multiple calls to .showLoadingScreen() and hide/error methods', () => {
    loadingService.showLoadingScreen();
    loadingService.showLoadingScreen();

    expect(loadingService.showLoading).toBeTrue();
    expect(loadingService['_activeLoadings']).toEqual(2);
    expect(loadingService.showError).toBeFalse();
    expect(loadingService.lastRefresh).toBeUndefined();

    loadingService.errorDuringLoading();
    loadingService.hideLoadingScreen();

    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService['_activeLoadings']).toEqual(0);
    expect(loadingService.showError).toBeTrue();
    expect(loadingService.lastRefresh).toBeDefined();
  });

  it('should handle multiple calls to .showLoadingScreen() and .hideLoadingScreen()', () => {
    loadingService.showLoadingScreen();
    loadingService.showLoadingScreen();

    expect(loadingService.showLoading).toBeTrue();
    expect(loadingService['_activeLoadings']).toEqual(2);
    expect(loadingService.lastRefresh).toBeUndefined();

    loadingService.hideLoadingScreen();
    loadingService.hideLoadingScreen();

    expect(loadingService.showLoading).toBeFalse();
    expect(loadingService['_activeLoadings']).toEqual(0);
    expect(loadingService.lastRefresh).toBeDefined();
  });

  it('.updateLastRefresh() sets date correctly', () => {
    loadingService.updateLastRefresh();

    expect(loadingService.lastRefresh).toBeDefined();
  });
});
