using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    [Migration(20030006, "HOPT-1693 - related functions and stored procedures"), Tags(Constants.MigrationTypeProgrammatic)]
    public class Hopt1693: Migrationable
    {
        public Hopt1693(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { "dbo.GetReceiptHash", "dbo.AddReceipt", "dbo.GetReceipt", "dbo.GetReceiptForOpt", "dbo.GetReceipts" };
        }

        public override void Up()
        {
            var result = BackupDbObjects(nameof(Hopt1693), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                // Hmm...not sure I like this
                Execute.EmbeddedScript($"{(dbObject == "dbo.GetReceiptHash" ? EmbeddedResourcePathFunctions : EmbeddedResourcePathStoredProcedures)}.{dbObject}{Constants.FileExtensionSql}");
            }
        }

        public override void Down()
        {
            RestoreDbObjects(nameof(Hopt1693), DbObjects.Reverse().ToArray());
        }
    }
}
