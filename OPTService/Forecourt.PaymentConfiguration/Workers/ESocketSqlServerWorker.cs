using CSharpFunctionalExtensions;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Forecourt.PaymentConfiguration.HydraDb.Interfaces;
using Forecourt.PaymentConfiguration.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Logger.Interfaces;
using System.IO.Abstractions;

namespace Forecourt.PaymentConfiguration.Workers
{
    /// <summary>
    /// eSocket.POS (SqlServer) implementation of the <see cref="IPaymentConfigIntegrator"/>
    /// </summary>
    public class ESocketSqlServerWorker : ESocketWorker, IESocketSqlServerWorker
    {
        /// <inheritdoc/>
        public ESocketSqlServerWorker(IHtecLogManager logManager, IHydraDb hydraDb, IFileSystem fileSystem, IEsocketDb esocketDb, IContactlessProperties contactlessProperties, 
            IConfigurationManager configurationManager = null) : base(logManager, nameof(ESocketSqlServerWorker), hydraDb, fileSystem, esocketDb, contactlessProperties, configurationManager)
        {
        }

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            Initialise(() =>
            {
                HydraDb.SetEsocketUseConnectionString(true);
                ESocketDb.SetUseConnectionString(true, true);
            });

            return base.DoStart(startParams);
        }
    }
}
