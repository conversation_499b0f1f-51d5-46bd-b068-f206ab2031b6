using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces.Tracing;
using System;

namespace Forecourt.Core.HydraDb.Models
{
    // <summary>
    /// Holds all BOS Configuration options
    /// </summary>
    [HasConfiguration()]
    public class BosConfig : Loggable
    {
        #region BOS Config Configurables

        public const string ConfigCategoryBos = Constants.CategoryNameBOS + Constants.CategorySeparator;

        /// <summary>
        /// Config key part, for BookTransaction
        /// </summary>
        public const string ConfigKeyPartBookTransaction = "BookTransaction";

        /// <summary>
        /// Config key part, for RecordTransaction
        /// </summary>
        public const string ConfigKeyPartRecordTransaction = "RecordTransaction";

        /// <summary>
        /// Config key for BOS Company ID
        /// </summary>
        public const string ConfigKeyBosCompanyId = ConfigCategoryBos + "CompanyId";
        /// <summary>
        /// Default vaue for BOS Company ID
        /// </summary>
        public const int DefaultValueBosCompanyId = 1;

        /// <summary>
        /// Configurable value for, BOS Company ID
        /// </summary>
        protected ConfigurableInt ConfigValueBosCompanyId { get; private set; }

        /// <summary>
        /// Config key for BOS Store ID
        /// </summary>
        public const string ConfigKeyBosStoreId = ConfigCategoryBos + "StoreId";

        /// <summary>
        /// Default vaue for BOS Store ID
        /// </summary>
        public const int DefaultValueBosStoreId = 1;

        /// <summary>
        /// Configurable value for, BOS Store ID
        /// </summary>
        protected ConfigurableInt ConfigValueBosStoreId { get; private set; }

        /// <summary>
        /// Config key for BOS Operator Id
        /// </summary>
        public const string ConfigKeyBosOperatorId = ConfigCategoryBos + "OperatorId";
        /// <summary>
        /// Default vaue for BOS Operator Id
        /// </summary>
        public const int DefaultValueBosOperatorId = 1;

        /// <summary>
        /// Configurable value for, BOS Operator Id
        /// </summary>
        protected ConfigurableInt ConfigValueBosOperatorId { get; private set; }

        /// <summary>
        /// Config key for BOS Card Referer Prefix
        /// </summary>
        public const string ConfigKeyBosCardRefererPrefix = ConfigCategoryBos + "CardRefererPrefix";
        /// <summary>
        /// Default vaue for BOS Card Referer Prefix
        /// </summary>
        public const string DefaultValueBosCardRefererPrefix = "";

        /// <summary>
        /// Configurable value for, BOS Card Referer Prefix
        /// </summary>
        protected ConfigurableString ConfigValueBosCardRefererPrefix { get; private set; }

        /// <summary>
        /// Config key for BOS Default Referer Name
        /// </summary>
        public const string ConfigKeyBosDefaultRefererName = ConfigCategoryBos + "DefaultRefererName";
        /// <summary>
        /// Default vaue for BOS Default Referer Name
        /// </summary>
        public const string DefaultValueBosDefaultRefererName = "";

        /// <summary>
        /// Configurable value for, BOS Default Referer Name
        /// </summary>
        protected ConfigurableString ConfigValueBosDefaultRefererName { get; private set; }

        /// <summary>
        /// Config key for, strict configuration mapping validate/result
        /// </summary>
        public const string ConfigKeyBosStrictConfigMappingValidation = ConfigCategoryBos + "ConfigurationMapping:StrictValidation";
        /// <summary>
        /// Default vaue for, strict configuration mapping validate/result
        /// </summary>
        public const bool DefaultValueBosStrictConfigMappingValidation = false;

        /// <summary>
        /// Configurable value for, strict configuration mapping validate/result
        /// </summary>
        protected ConfigurableBool ConfigValueStrictConfigMappingValidation { get; private set; }

        /// <summary>
        /// Config key for, book transaction retry interval
        /// </summary>
        public const string ConfigKeyBosBookTransactionRetryInterval = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":Retry:Interval";
        /// <summary>
        /// Default vaue for, book transaction retry interval
        /// </summary>
        public const string DefaultValueBosBookTransactionRetryInterval = "00:05:00";

        /// <summary>
        /// Configurable value for, book transaction retry interval
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueBookTransactionRetryInterval { get; private set; }

        /// <summary>
        /// Config key for, book transaction max retry attempts
        /// </summary>
        public const string ConfigKeyBosBookTransactionRetryMaxAttempts = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":Retry:MaxAttempts";
        /// <summary>
        /// Default vaue for, book transaction max retry attempts
        /// </summary>
        public const uint DefaultValueBosBookTransactionRetryMaxAttempts = 5;

        /// <summary>
        /// Configurable value for, book transaction max retry attempts
        /// </summary>
        protected ConfigurableUInt ConfigValueBookTransactionRetryMaxAttempts { get; private set; }

        /// <summary>
        /// Config key for, book transaction, ignore invalid/cancelled
        /// </summary>
        public const string ConfigKeyBosBookTransactionIgnoreInvalid = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":Ignore:Invalid";
        /// <summary>
        /// Default vaue for, book transaction, ignore invalid/cancelled
        /// </summary>
        public const bool DefaultValueBosBookTransactionIgnoreInvalid = true;

        /// <summary>
        /// Configurable value for, book transaction, ignore invalid/cancelled
        /// </summary>
        protected ConfigurableBool ConfigValueBookTransactionIgnoreInvalid { get; private set; }

        /// </summary>
        /// Config key for, book transaction, interval in which booking is being created
        /// </summary>
        public const string ConfigKeyBosBookTransactionCreationInterval = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":Creation:Interval";
        /// <summary>
        /// Default vaue for, book transaction, interval in which booking is being created
        /// </summary>
        public const string DefaultValueBosBookTransactionCreationInterval = "00:05:00";

        /// <summary>
        /// Configurable value for, book transaction, interval in which booking is being created
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueBookTransactionCreationInterval { get; private set; }

        /// </summary>
        /// Config key for, record (internal) transaction, duplicate check interval
        /// </summary>
        public const string ConfigKeyBosRecordTransactionDuplicateCheckInterval = ConfigCategoryBos + ConfigKeyPartRecordTransaction + ":DuplicateCheck:Interval";
        /// <summary>
        /// Default vaue for, record (internal) transaction, duplicate check interval
        /// </summary>
        public const string DefaultValueBosRecordTransactionDuplicateCheckInterval = "04:00:00";

        /// <summary>
        /// Configurable value for, record (internal) transaction, duplicate check intervaling created
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueRecordTransactionDuplicateCheckInterval { get; private set; }
        
        /// </summary>
        /// Config key for, book transaction, sequence number duplicate check interval
        /// </summary>
        public const string ConfigKeyBosBookTransactionSeqNumberDuplicateCheckInterval = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":SequenceNumber:DuplicateCheck:Interval";
        /// <summary>
        /// Default vaue for, book transaction, sequence number duplicate check interval
        /// </summary>
        public const string DefaultValueBosBookTransactionSeqNumberDuplicateCheckInterval = "00:05:00";

        /// <summary>
        /// Configurable value for, book transaction, sequence number duplicate check interval
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueBookTransactionSeqNumberDuplicateCheckInterval { get; private set; }

        /// </summary>
        /// Config key for, book transaction, maximum pending to process
        /// </summary>
        public const string ConfigKeyBosBookTransactionMaxPending = ConfigCategoryBos + ConfigKeyPartBookTransaction + ":ProcessPending:Maximum";
        /// <summary>
        /// Default vaue for, book transaction, maximum pending to process
        /// </summary>
        public const int DefaultValueBosBookTransactionMaxPending = 40;

        /// <summary>
        /// Configurable value for, book transaction, maximum pending to process
        /// </summary>
        protected ConfigurableInt ConfigValueBookTransactionMaxPending { get; private set; }

        #endregion

        #region Simple ConfigurableXxx wrappers

        /// <summary>
        /// Configurable value (as an int) for, Company Id
        /// </summary>
        public int CompanyId => ConfigValueBosCompanyId.GetValue();

        /// <summary>
        /// Configurable value (as an int) for, Store Id
        /// </summary>
        public int StoreId => ConfigValueBosStoreId.GetValue();

        /// <summary>
        /// Configurable value (as an int) for, Operator Id
        /// </summary>
        public int OperatorId => ConfigValueBosOperatorId.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, Card Referer prefix
        /// </summary>
        public string CardRefererPrefix => ConfigValueBosCardRefererPrefix.GetValue();

        /// <summary>
        /// Configurable value (as a string) for, Default Card Referer
        /// </summary>
        public string DefaultCardReferer => ConfigValueBosDefaultRefererName.GetValue();

        /// <summary>
        /// Configurable value (as a bool) for, strict configuration mapping validate/result
        /// </summary>
        public bool StrictConfigMappingValidation => ConfigValueStrictConfigMappingValidation.GetValue();

        /// <summary>
        /// Configurable value (as a Timespan) for, book transaction retry interval
        /// </summary>
        public TimeSpan BookTransactionRetryInterval => ConfigValueBookTransactionRetryInterval.GetValue();

        /// <summary>
        /// Configurable value (as an int) for, book transaction max retry attempts
        /// </summary>
        public uint BookTransactionRetryMaxAttempts => ConfigValueBookTransactionRetryMaxAttempts.GetValue();

        /// <summary>
        /// Configurable value (as a bool) for, book transaction, ignore invalid
        /// </summary>
        public bool BookTransactionIgnoreInvalid => ConfigValueBookTransactionIgnoreInvalid.GetValue();

        /// <summary>
        /// Configurable value (as a TimeSpan) for, book transaction, interval in which booking is being created
        /// </summary>
        public TimeSpan BookTransactionCreationInterval => ConfigValueBookTransactionCreationInterval.GetValue();

        /// <summary>
        /// Configurable value (as a TimeSpan) for, record (internal) transaction, duplicate check intervaling created
        /// </summary>
        public TimeSpan RecordTransactionDuplicateCheckInterval => ConfigValueRecordTransactionDuplicateCheckInterval.GetValue();

        /// Configurable value (as a TimeSpan) for, book transaction, sequence number duplicate check interval
        /// </summary>
        public TimeSpan BookTransactionSeqNumberDuplicateCheckInterval => ConfigValueBookTransactionSeqNumberDuplicateCheckInterval.GetValue();

        /// <summary>
        /// Configurable value (as an int) for, book transaction, maximum pending to process
        /// </summary>
        public int BookTransactionMaxPending => ConfigValueBookTransactionMaxPending.GetValue();

        #endregion

        /// <summary>
        /// Perform basic validation on this model
        /// </summary>
        /// <returns>bool</returns>
        public Result IsValid()
        {
            if (CompanyId == 0)
            {
                return Result.Failure("No Company Id defined");
            }

            if (StoreId == 0)
            {
                return Result.Failure("No Store Id defined");
            }

            if (OperatorId == 0)
            {
                return Result.Failure("No Operator Id defined");
            }

            if (CardRefererPrefix.IsNullOrWhiteSpace())
            {
                return Result.Failure("No CardReferer Prefix defined");
            }

            if (DefaultCardReferer.IsNullOrWhiteSpace())
            {
                return Result.Failure("No Default CardReferer defined");
            }

            if (BookTransactionRetryInterval == TimeSpan.Zero)
            {
                return Result.Failure("No Book Transaction Retry Interval defined");
            }

            if (BookTransactionRetryMaxAttempts == 0)
            {
                return Result.Failure("No Book Transaction Retry Max Attempts defined");
            }

            if (BookTransactionCreationInterval == TimeSpan.Zero)
            {
                return Result.Failure("No Book Transaction Creation Interval defined");
            }

            if (RecordTransactionDuplicateCheckInterval == TimeSpan.Zero)
            {
                return Result.Failure("No Record (internal) Transaction Duplicate Check Interval defined");
            }

            if (BookTransactionSeqNumberDuplicateCheckInterval == TimeSpan.Zero)
            {
                return Result.Failure("No Book Transaction Sequence Number Duplicate Check Interval defined");
            }

            if (BookTransactionMaxPending == 0)
            {
                return Result.Failure("No Book Transaction Maximum Pending to Process defined");
            }

            return Result.Success();
        }

        // <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger"><see cref="IHtecLogger"/> instance</param>
        /// <param name="configurationManager"><see cref="IConfigurationManager"/> instance</param>
        public BosConfig(IHtecLogger logger, IConfigurationManager configurationManager) : base(logger, configurationManager)
        {
            ConfigValueBosCompanyId = new ConfigurableInt(this, ConfigKeyBosCompanyId, DefaultValueBosCompanyId);
            ConfigValueBosStoreId = new ConfigurableInt(this, ConfigKeyBosStoreId, DefaultValueBosStoreId);
            ConfigValueBosOperatorId = new ConfigurableInt(this, ConfigKeyBosOperatorId, DefaultValueBosOperatorId);
            ConfigValueBosCardRefererPrefix = new ConfigurableString(this, ConfigKeyBosCardRefererPrefix, DefaultValueBosCardRefererPrefix);
            ConfigValueBosDefaultRefererName = new ConfigurableString(this, ConfigKeyBosDefaultRefererName, DefaultValueBosDefaultRefererName);
            ConfigValueStrictConfigMappingValidation = new ConfigurableBool(this, ConfigKeyBosStrictConfigMappingValidation, DefaultValueBosStrictConfigMappingValidation);
            ConfigValueBookTransactionRetryInterval = new ConfigurableTimeSpan(this, ConfigKeyBosBookTransactionRetryInterval, DefaultValueBosBookTransactionRetryInterval);
            ConfigValueBookTransactionRetryMaxAttempts = new ConfigurableUInt(this, ConfigKeyBosBookTransactionRetryMaxAttempts, DefaultValueBosBookTransactionRetryMaxAttempts);
            ConfigValueBookTransactionIgnoreInvalid = new ConfigurableBool(this, ConfigKeyBosBookTransactionIgnoreInvalid, DefaultValueBosBookTransactionIgnoreInvalid);
            ConfigValueBookTransactionCreationInterval = new ConfigurableTimeSpan(this, ConfigKeyBosBookTransactionCreationInterval, DefaultValueBosBookTransactionCreationInterval);
            ConfigValueRecordTransactionDuplicateCheckInterval = new ConfigurableTimeSpan(this, ConfigKeyBosRecordTransactionDuplicateCheckInterval, DefaultValueBosRecordTransactionDuplicateCheckInterval);
            ConfigValueBookTransactionSeqNumberDuplicateCheckInterval = new ConfigurableTimeSpan(this, ConfigKeyBosBookTransactionSeqNumberDuplicateCheckInterval, DefaultValueBosBookTransactionSeqNumberDuplicateCheckInterval);
            ConfigValueBookTransactionMaxPending = new ConfigurableInt(this, ConfigKeyBosBookTransactionMaxPending, DefaultValueBosBookTransactionMaxPending);
        }
    }
}
