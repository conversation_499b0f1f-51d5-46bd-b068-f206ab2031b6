using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using OPT.Common;
using System;
using ITelemetryWorker = Forecourt.SecondaryAuth.Workers.Interfaces.ITelemetryWorker;

namespace Forecourt.SecondaryAuth.Workers
{
    /// <summary>
    /// Only implementation of <see cref="ISecAuthIntegratorIn{IMessageTracking}"/>
    /// </summary>
    /// <inheritdoc/>
    public class SecAuthInWorker : SecAuthWorker, ISecAuthIntegratorIn<IMessageTracking>, ISecAuthInWorker
    {
        private const string _failureReasonPrefix = "Invalid response: ";

        /// <inheritdoc/>
        public SecAuthInWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, IPumpCollection allPumps, IPumpIntegratorInTransient<IMessageTracking> pumpOutWorker,
            ITelemetryWorker telemetryWorker, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOutWorker) : base(logManager, nameof(SecAuthInWorker), configurationManager, allPumps, telemetryWorker)
        {
            RegisterWorker(pumpOutWorker ?? throw new ArgumentNullException(nameof(pumpOutWorker)));
            RegisterWorker(secAuthOutWorker ?? throw new ArgumentNullException(nameof(secAuthOutWorker)));
        }

        private Result DoProcessResponse(SecAuthResponse response, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var logRef = message.FullId;

            return DoAction(() => {
                DoDeferredLogging(LogLevel.Info, $"{ConfigConstants.HeaderSignalR}.{ByteExtensions.HeaderRxValue}", () => new[] { $"Response: {JsonConvert.SerializeObject(message)}" });

                if (!AllPumps.TryGetPump(response.Pump, out var thePump))
                {
                    return Result.Failure($"{_failureReasonPrefix} Pump");
                }

                if (IsStrictMessaging && (message.ParentIdAsString != thePump.TransactionSummary?.LoggingReference))
                {
                    return Result.Failure($"{_failureReasonPrefix} Message");
                }

                var timedOut = !thePump.IsSecAuthRequested;
                if (timedOut)
                {
                    RequestTimedOut(response, message);
                }
                else
                {
                    thePump.SetSecAuthResponse(response.IsAuthorised, logRef);
                    ControllerWorker?.SendInformation($"SecondaryAuth response received, for Pump {response.Pump}, {(response.IsAuthorised ? "Approved" : "Rejected")}");
                    GetWorker<ITelemetryWorker>()?.MessageReceivedFromSecAuthHost(thePump.Number, logRef);
                }
                ControllerWorker?.PushChange(EventType.PumpChanged, $"{response.Pump}");

                if (thePump.InUseByOpt && (thePump.IsNozzleUp || thePump.IsDelivering))
                {
                    if (response.IsAuthorised)
                    {
                        GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.EmergencyStopCancel(response.Pump, message);
                    }
                    else
                    {
                        GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.EmergencyStop(response.Pump, response.VehicleRegistration, message);
                    }
                }

                return Result.SuccessIf(!timedOut || !response.IsAuthorised, $"{_failureReasonPrefix} TimeOut");
            }, logRef);
        }

        /// <inheritdoc/>
        public Result PostAuthResponse(SecAuthResponse response, IMessageTracking message)
        {
            return DoProcessResponse(response, message);
        }

        /// <inheritdoc/>
        public Result PreAuthReponse(SecAuthResponse response, IMessageTracking message)
        {
            return DoProcessResponse(response, message);
        }

        /// <inheritdoc/>
        public Result RequestTimedOut(SecAuthResponse response, IMessageTracking message)
        {
            var logRef = message.FullId;

            return DoAction(() => {
                if (!AllPumps.TryGetPump(response.Pump, out var thePump))
                {
                    return Result.Failure($"{_failureReasonPrefix} Pump");
                }

                if (!thePump.IsSecAuthRequested)
                {
                    var isAuthorised = ConfigValueSecAuthTimedOutResponse.GetValue();
                    response.IsAuthorised = isAuthorised;
                    thePump.SetSecAuthResponse(response.IsAuthorised, message.FullId);
                    ControllerWorker?.SendInformation($"SecondaryAuth response received (TimedOut), for Pump {response.Pump}, {(response.IsAuthorised ? "Approved" : "Rejected")}");
                    GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.RequestTimedOutRequest(new SecAuthRequest(response.TransactionId, response.MessageId, thePump?.Opt?.IdString, response.Pump), message);
                    GetWorker<ITelemetryWorker>()?.MessageTimeoutFromSecAuthHost(thePump.Number, logRef);
                }
  
                return Result.Success();
            }, logRef);
        }
    }
}
