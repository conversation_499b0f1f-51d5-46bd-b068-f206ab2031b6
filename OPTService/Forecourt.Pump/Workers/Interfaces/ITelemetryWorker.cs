namespace Forecourt.Pump.Workers.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the ITelemetryWorker, related to Pump/TankGauge
    /// </summary>

    public interface ITelemetryWorker: Htec.Foundation.Connections.Workers.Interfaces.ITelemetryWorker
    {
        /// <summary>
        /// Register message sent to Tank Gauge.
        /// </summary>
        void MessageSentToTankGauge();

        /// <summary>
        /// Register message received from Tank Gauge.
        /// </summary>
        void MessageReceivedFromTankGauge();

        /// <summary>
        /// Register timeout waiting for response from Tank Gauge.
        /// </summary>
        void MessageTimeoutFromTankGauge();
    }
}
