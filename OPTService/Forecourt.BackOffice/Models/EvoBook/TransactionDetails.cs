using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class TransactionDetails
    {
        [JsonProperty("key")]
        public TransactionKey Key { get; set; }
        [JsonProperty("operatorId")]
        public string OperatorId { get; set; }
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        [JsonProperty("periodNumber")]
        public int PeriodNumber { get; set; }
        [JsonProperty("cancelledFlag")]
        public bool CancelledFlag { get; set; }
        [JsonProperty("suspendedFlag")]
        public bool SuspendedFlag { get; set; }
        [JsonProperty("trainingFlag")]
        public bool TrainingFlag { get; set; }
        [JsonProperty("shiftId")]
        public int ShiftId { get; set; }
        [JsonProperty("receiptPrintCount")]
        public int ReceiptPrintCount { get; set; }
        [JsonProperty("scoMode")]
        public bool ScoMode { get; set; }
    }
}
