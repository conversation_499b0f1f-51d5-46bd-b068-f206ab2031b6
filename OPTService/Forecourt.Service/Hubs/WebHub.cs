using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Hubs;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;

namespace OPTService.Hubs
{
    [HubName("webHub")]
    public class WebHub : Hub
    {
        private readonly IWeb _web;

        public IWeb Web => _web;

        public WebHub(IWeb web)
        {
            _web = web ?? throw new ArgumentNullException(nameof(web));
        }

        #region Chat

        /// <summary>
        /// Send a message to chat, used for debug.
        /// </summary>
        /// <param name="name">Name to display with message.</param>
        /// <param name="message">Message to display.</param>
        public void Send(string name, string message)
        {
            Clients.All.addMessage(name, message);
        }

        #endregion

        #region Refresh Calls

        public void RefreshTabs()
        {
            IList<string> tids = Web.Tids();
            IList<OptDetails> opts = Web.Opts();
            IList<PumpDetails> pumps = Web.Pumps();
            ConnectionDetails connections = Web.ConnectionsDetails();
            GenericOptConfigDetails genericOptConfig = Web.GetGenericOptConfig();
            bool isConfigBatch = Web.IsConfigBatch;
            IList<LocalAccountCustomerDetails> localAccountCustomers = Web.GetLocalAccountCustomers();
            AdvancedConfigDetails advancedConfig = Web.GetAdvancedConfig();
            FileLocations fileLocations = Web.GetFileLocations();
            IList<GradePrices> gradePrices = Web.GetPrices();
            DivertDetails divertDetails = Web.GetDivertDetails();
            DateTime shiftEndTime = Web.GetShiftEndTime();
            DateTime dayEndTime = Web.GetDayEndTime();
            DateTime? nextDayEnd = Web.GetNextDayEnd();
            LogIntervalDetails logIntervalDetails = Web.GetLogInterval();
            bool unmanned = Web.GetUnmanned();
            bool asdaDayEndReport = Web.GetAsdaDayEndReport();
            PrinterDetails printerDetails = Web.GetPrinterDetails();
            DomsDetails domsDetails = Web.GetDomsDetails();
            IList<InfoMessageDetails> infoMessages = Web.GetInfoDetails();
            VersionInfo versionInfo = Web.GetVersionInfo();
            Clients.Caller.refreshOpts(opts);
            Clients.Caller.refreshPumps(pumps, opts, tids, advancedConfig.MaxFillOverride, Web.GetPosConnected());
            Clients.Caller.refreshConnectionsDetails(connections);
            Clients.Caller.refreshTids(tids);
            Clients.Caller.refreshGenericOptConfig(genericOptConfig, isConfigBatch);
            Clients.Caller.refreshLocalAccounts(localAccountCustomers, isConfigBatch);
            Clients.Caller.refreshAdvancedConfig(advancedConfig);
            Clients.Caller.refreshFileLocations(fileLocations);
            Clients.Caller.refreshPrices(gradePrices);
            Clients.Caller.refreshDivert(divertDetails);
            Clients.Caller.refreshShiftEnd(shiftEndTime, dayEndTime, nextDayEnd, logIntervalDetails, unmanned, asdaDayEndReport,
                printerDetails);
            Clients.Caller.refreshInfo(infoMessages);
            Clients.Caller.refreshAbout(versionInfo);
            Clients.Caller.refreshDoms(domsDetails);
        }

        public void RefreshVersions()
        {
            VersionInfo versionInfo = Web.GetVersionInfo();
            Clients.Caller.refreshAbout(versionInfo);
        }

        #endregion
    }
}