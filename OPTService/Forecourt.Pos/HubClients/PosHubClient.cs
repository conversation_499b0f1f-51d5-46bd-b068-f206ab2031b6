using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.HubClient.Managers;
using Htec.Logger.Interfaces;
using System.Collections.Generic;

namespace Forecourt.Pos.HubClients
{
    public class PosHubClient : Htec.HubClient.HubClient
    {
        public PosHubClient(IHtecLogManager logManager, IConfigurationManager configurationManager, string hubUrl, string hubName, IRetryManager retryManager, IDictionary<string, string> queryString = null, IDictionary<string, HubProxyOnEvent> hubProxyOnEvents = null) :
            base(logManager, nameof(PosHubClient), configurationManager, hubUrl, hubName, retryManager, queryString, hubProxyOnEvents)
        {
        }
    }
}
