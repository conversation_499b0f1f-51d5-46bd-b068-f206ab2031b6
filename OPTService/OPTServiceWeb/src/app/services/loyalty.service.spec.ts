import { DOCUMENT } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { NGXLogger } from 'ngx-logger';
import { environment } from 'src/environments/environment';

import { LoyaltyService } from './loyalty.service';

describe('LoyaltyService', () => {
  let loyaltyService: LoyaltyService;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let httpMock: HttpTestingController;
  let httpClient: HttpClient;
  const URL = "http://localhost";

  beforeEach(() => {
    const ngxLoggertSpy = jasmine.createSpyObj('NGXLogger', ['debug']);
    const mockDocument = { location: { origin: URL } };

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule
      ],
      providers: [
        LoyaltyService,
        {provide: NGXLogger, useValue: ngxLoggertSpy},
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    });
    loyaltyService = TestBed.inject(LoyaltyService);
    httpMock = TestBed.get(HttpTestingController);
    httpClient = TestBed.inject(HttpClient);
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
  });

  it('should be created', () => {
    expect(loyaltyService).toBeTruthy();
  });

  it('.addLoyalty() should handle success response from API', () => {
    //Arrange
    let loyaltySchemaName = 'Morrisons';
        
    //Act
    loyaltyService.addLoyalty(loyaltySchemaName).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.addLoyalty);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.addLoyalty() should handle unsuccess response from API', () => {
    //Arrange
    let loyaltySchemaName = 'Morrisons';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.addLoyalty(loyaltySchemaName).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.addLoyalty);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.deleteLoyalty() should handle success response from API', () => {
    //Arrange
    let loyaltySchemaName = 'Morrisons';
        
    //Act
    loyaltyService.deleteLoyalty(loyaltySchemaName).subscribe((data) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(data).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.deleteLoyalty);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.deleteLoyalty() should handle unsuccess response from API', () => {
    //Arrange
    let loyaltySchemaName = 'Morrisons';
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.deleteLoyalty(loyaltySchemaName).subscribe(data =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.deleteLoyalty);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyPresent() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let present = true;
        
    //Act
    loyaltyService.setLoyaltyPresent(name, present).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyPresent);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyPresent() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let present = true;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyPresent(name,present).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyPresent);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalSiteId() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let siteId = "1";
        
    //Act
    loyaltyService.setLoyaltyTerminalSiteId(name, siteId).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalSiteId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalSiteId() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let siteId = "1";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalSiteId(name,siteId).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalSiteId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalTerminalId() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let terminalId = "1";
        
    //Act
    loyaltyService.setLoyaltyTerminalTerminalId(name, terminalId).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalTerminalId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalTerminalId() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let terminalId = "1";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalTerminalId(name,terminalId).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalTerminalId);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalFooter1() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let footer1 = "Footer 1";
        
    //Act
    loyaltyService.setLoyaltyTerminalFooter1(name, footer1).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalFooter1);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalFooter1() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let footer1 = "Footer 1";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalFooter1(name,footer1).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalFooter1);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalFooter2() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let footer2 = "Footer 2";
        
    //Act
    loyaltyService.setLoyaltyTerminalFooter2(name, footer2).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalFooter2);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalFooter2() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let footer2 = "Footer 2";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalFooter2(name,footer2).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalFooter2);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalTimeout() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let timeout = 500;
        
    //Act
    loyaltyService.setLoyaltyTerminalTimeout(name, timeout).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalTimeout);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalTimeout() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let timeout = 500;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalTimeout(name,timeout).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalTimeout);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalApiKey() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let apiKey = "123456789";
        
    //Act
    loyaltyService.setLoyaltyTerminalApiKey(name, apiKey).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalApiKey);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalApiKey() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let apiKey = "123456789";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalApiKey(name,apiKey).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalApiKey);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalHttpHeader() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let httpHeader = "123456789";
        
    //Act
    loyaltyService.setLoyaltyTerminalHttpHeader(name, httpHeader).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalHttpHeader);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyTerminalHttpHeader() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let httpHeader = "123456789";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyTerminalHttpHeader(name,httpHeader).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyTerminalHttpHeader);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyHostsAddHost() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let ipAddress = "127.0.0.1";
    let port = 5001;
        
    //Act
    loyaltyService.setLoyaltyHostsAddHost(name, ipAddress, port).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyHostsAddHost);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyHostsAddHost() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let ipAddress = "127.0.0.1";
    let port = 5001;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyHostsAddHost(name,ipAddress,port).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyHostsAddHost);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyHostsRemoveHost() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let ipAddress = "127.0.0.1";
    let port = 5001;
        
    //Act
    loyaltyService.setLoyaltyHostsRemoveHost(name, ipAddress, port).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyHostsRemoveHost);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyHostsRemoveHost() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let ipAddress = "127.0.0.1";
    let port = 5001;
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyHostsRemoveHost(name,ipAddress,port).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyHostsRemoveHost);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyAddHostname() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let hostname = "The hostname";
        
    //Act
    loyaltyService.setLoyaltyAddHostname(name, hostname).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyAddHostname);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyAddHostname() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let hostname = "The hostname";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyAddHostname(name,hostname).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyAddHostname);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyRemoveHostname() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let hostname = "The hostname";
        
    //Act
    loyaltyService.setLoyaltyRemoveHostname(name, hostname).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyRemoveHostname);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyRemoveHostname() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let hostname = "The hostname";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyRemoveHostname(name,hostname).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyRemoveHostname);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyIinsAddIin() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let low = "11111";
    let high = "99999";
        
    //Act
    loyaltyService.setLoyaltyIinsAddIin(name, low, high).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyIinsAddIin);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyIinsAddIin() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let low = "11111";
    let high = "99999";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyIinsAddIin(name,low, high).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyIinsAddIin);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyIinsRemoveIin() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let low = "11111";
    let high = "99999";
        
    //Act
    loyaltyService.setLoyaltyIinsRemoveIin(name, low, high).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyIinsRemoveIin);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyIinsRemoveIin() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let low = "11111";
    let high = "99999";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyIinsRemoveIin(name,low, high).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyIinsRemoveIin);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyMappingsAddMapping() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let productCode = "123";
    let loyaltyCode = "321";
        
    //Act
    loyaltyService.setLoyaltyMappingsAddMapping(name, productCode, loyaltyCode).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyMappingsAddMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyMappingsAddMapping() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let productCode = "123";
    let loyaltyCode = "321";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyMappingsAddMapping(name,productCode, loyaltyCode).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyMappingsAddMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyMappingsRemoveMapping() should handle success response from API', () => {
    //Arrange
    let name = "Name 1";
    let productCode = "123";
    let loyaltyCode = "321";
        
    //Act
    loyaltyService.setLoyaltyMappingsRemoveMapping(name, productCode, loyaltyCode).subscribe((genericOptConfig) =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      expect(genericOptConfig).toBeNull;
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyMappingsRemoveMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(null, {status:200, statusText:'OK'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });

  it('.setLoyaltyMappingsRemoveMapping() should handle unsuccess response from API', () => {
    //Arrange
    let name = "Name 1";
    let productCode = "123";
    let loyaltyCode = "321";
    let errorMsg = 'Deliberate 500 error';
    
    //Act
    loyaltyService.setLoyaltyMappingsRemoveMapping(name,productCode, loyaltyCode).subscribe(genericOptConfig =>{
      //Assert 1: Verify the observable when it resolves, its result matches test data
      fail('Should have failed with the 500 error')
    }, (error:HttpErrorResponse)=>{
      expect(error.status).toEqual(500, 'status');
      expect(error.error).toEqual(errorMsg, 'message');
    });

    //Assert 2: Verify the matched URL get called in the GET API else it throws errors
    const req = httpMock.expectOne(URL + environment.optServiceBase + environment.loyaltyController.base + environment.loyaltyController.setLoyaltyMappingsRemoveMapping);

    //Assert 3: Verify that the request called is GET HTTP method only
    expect(req.request.method).toEqual("POST");

    //Assert 4: Ensures the correct data was returned using Subscribe callback
    req.flush(errorMsg, {status:500, statusText:'Internal Server Error'});

    //Assert 5: Ensures that all request are fulfilled and there are no outstanding requests.
    httpMock.verify();
  });
});
