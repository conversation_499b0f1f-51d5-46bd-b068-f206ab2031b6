using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Data
{
    /// <summary>
    /// Migration to clear down obsolete Magical Advanced Config keys
    /// </summary>
    [Migration(20030003, "HOPT-1770 - clear down obsolete config keys"), Tags(Constants.MigrationTypeData)]
    public class Hopt1770: Migrationable
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathData}.HOPT-1770_remove_old_config.sql");
        }

        public override void Down()
        {
            // No need to rollback as this upgrade was to remove obsolete/superceded data!
        }
    }
}
