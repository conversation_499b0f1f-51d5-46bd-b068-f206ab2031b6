export class FuelTransactionDetails {
    TransactionId: number = 0;
    TransactionTime: string = '';
    GradeCode: string = '';
    WashCode: string = '';
    GradeName: string = '';
    WashName: string = '';
    PumpDetailsString: string = '';
    CardNumber: string = '';
    FuelQuantity: number = 0;
    WashQuantity: number = 0;
    Amount: number = 0;
    FuelCategory: string = '';
    WashCategory: string = '';
    FuelSubcategory: string = '';
    WashSubcategory: string = '';
    DiscountName: string = '';
    DiscountCode: string = '';
    DiscountValue: number = 0;
    DiscountCardNumber: string = '';
    LocalAccountMileage: number = 0;
    LocalAccountRegistration: string = '';
    TxnNumber: string = '';
    HasReceipt: boolean = false;
    PrinterEnabled: boolean = false;
    ReceiptError: boolean = false;
  ReceiptErrorText: string = '';
  ReceiptContent: string = '';
}
