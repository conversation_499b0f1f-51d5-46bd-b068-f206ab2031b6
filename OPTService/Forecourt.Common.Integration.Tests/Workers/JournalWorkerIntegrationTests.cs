using CSharpFunctionalExtensions;
using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Hydra.Core.Bos.Messages.Retalix;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.Helpers;
using OPT.Common.HydraDbClasses;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Threading;
using Xunit;
using HscDispenser = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.Dispenser6;
using HscMeterReadings = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.MeterReadings;
using HscPumpData = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpData6;
using HscPumpTotals = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpTotals;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;

namespace OPT.Common.Integration.Tests.Workers
{
    public class JournalWorkerIntegrationTests
    {
        private const short TillNumber = 99;
        private const short FuelCategory = 97;
        private const string SalesItemRecType = "10";
        private const string DiscountItemRecType = "12";
        private const string MethodOfPaymentRecType = "16";
        private const string EndOfSalesRecType = "19";
        private const string EndOfShiftRecType = "32";
        private const string CurrencyZero = "0.00";
        private const string Zero = "0";
        private const string EndOfShiftStatus = "S";
        private const string EndOfDayStatus = "D";
        private const string OpId = "1";
        private const string OpName = "OPT";
        private const int FirstShiftTransaction = 41;
        private const int SalesTransaction = 50;
        private const int CardRef1 = 22;
        private const int CardRef2 = 33;
        private const string CardProductName1 = "Card One";
        private const string CardProductName2 = "Card Two";
        private const string CardExternalName1 = "External Card One";
        private const string CardExternalName2 = "External Card Two";
        private const string Acquirer1 = "Acquirer One";
        private const string Acquirer2 = "Acquirer Two";
        private readonly DateTime _prevDayEndTime = DateTime.Now.AddHours(-12);
        private readonly DateTime _prevShiftEndTime = DateTime.Now.AddHours(-1);
        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _journal;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IHydraTransactionFile _transactionFile;
        private readonly IJournalWorker _journalWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IFromOptWorker _optWorker;
        private readonly ITankGaugeWorker _tankGaugeWorker;
        private readonly IPumpWorker _hscWorker;
        private readonly IList<DateTime> _addEventTimes = new List<DateTime>();
        private readonly IList<string> _transactionGradeNames = new List<string>();
        private readonly IList<string> _transactionCodes = new List<string>();
        private readonly IList<uint> _transactionQuantities = new List<uint>();
        private readonly IList<uint> _transactionAmounts = new List<uint>();
        private readonly IList<string> _transactionPumpDetails = new List<string>();
        private readonly IList<string> _transactionCardNumbers = new List<string>();
        private readonly IList<DateTime> _transactionTimes = new List<DateTime>();
        private readonly IList<string> _transactionCategories = new List<string>();
        private readonly IList<string> _transactionSubCategories = new List<string>();
        private readonly IList<string> _transactionDiscountNames = new List<string>();
        private readonly IList<string> _transactionDiscountCodes = new List<string>();
        private readonly IList<uint> _transactionDiscountValues = new List<uint>();
        private readonly IList<string> _transactionDiscountCardNumbers = new List<string>();
        private readonly IList<uint> _transactionLocalAccountMileages = new List<uint>();
        private readonly IList<string> _transactionLocalAccountRegistrations = new List<string>();
        private readonly IList<string> _transactionTxnNumbers = new List<string>();
        private readonly IList<int> _transactionsAddedTo = new List<int>();
        private readonly IList<uint> _dayEndFuelAmounts = new List<uint>();
        private readonly IList<uint> _dayEndDryAmounts = new List<uint>();
        private readonly IList<uint> _dayEndQuantities = new List<uint>();
        private readonly IList<int> _dayEndTransactionNumbers = new List<int>();
        private readonly IList<short> _dayEndCategories = new List<short>();
        private readonly IList<short> _dayEndSubCategories = new List<short>();
        private readonly IList<string> _dayEndGradeCodes = new List<string>();
        private readonly IList<string> _dayEndGradeNames = new List<string>();
        private readonly IList<string> _dayEndCardProductNames = new List<string>();
        private readonly IList<uint> _dayEndDiscounts = new List<uint>();
        private readonly IList<DayEndItem> _shiftEndSummaries = new List<DayEndItem>();
        private readonly IList<DayEndItem> _dayEndSummaries = new List<DayEndItem>();
        private readonly IList<TransactionFileItem> _transactionsFileItems = new List<TransactionFileItem>();
        private readonly IList<ItemSalesItem> _shiftEndSalesItems = new List<ItemSalesItem>();
        private readonly IList<ItemSalesItem> _dayEndSalesItems = new List<ItemSalesItem>();
        private readonly IList<CardSalesItem> _shiftEndCardSalesItems = new List<CardSalesItem>();
        private readonly IList<CardSalesItem> _dayEndCardSalesItems = new List<CardSalesItem>();
        private readonly IList<CategorySalesItem> _shiftEndCatSalesItems = new List<CategorySalesItem>();
        private readonly IList<CategorySalesItem> _dayEndCatSalesItems = new List<CategorySalesItem>();
        private readonly IList<CardVolumeSalesItem> _shiftEndCardVolumeSalesItems = new List<CardVolumeSalesItem>();
        private readonly IList<CardVolumeSalesItem> _dayEndCardVolumeSalesItems = new List<CardVolumeSalesItem>();
        private readonly IList<RetalixShiftEndItem> _retalixShiftEndSummaries = new List<RetalixShiftEndItem>();
        private readonly IList<RetalixItemSalesItem> _retalixSalesItems = new List<RetalixItemSalesItem>();
        private readonly IList<RetalixCardVolumeItem> _retalixCardVolumeItems = new List<RetalixCardVolumeItem>();
        private readonly IList<RetalixCardAmountItem> _retalixCardAmountItems = new List<RetalixCardAmountItem>();
        private readonly IList<string> _journalInfos = new List<string>();
        private readonly IList<string> _loggerInfos = new List<string>();
        private int _dipsRequested = 0;
        private int _metersRequested = 0;
        private int _shiftTransaction = FirstShiftTransaction;
        private readonly ILoggingHelper _loggingHelper;
        private const int MaxRetalixTransactionNumber = 1000000;
        private readonly IRetalixPosWorker _retalixPosWorker;

        public JournalWorkerIntegrationTests()
        {
            ConfigurationManager.AppSettings["TransactionFileDirectory"] = "";
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.FetchShiftStart(true).Returns(_prevDayEndTime);
            _hydraDb.FetchShiftStart(false).Returns(_prevShiftEndTime);
            _hydraDb.GetSiteInfo().Returns(new SiteInfo(0, string.Empty, string.Empty, false, false, 0, false, TillNumber, FuelCategory, 0));
            _hydraDb.When(x => x.AddEvent(Arg.Do<DateTime>(y => _addEventTimes.Add(y)), out _)).Do(x => x[1] = _shiftTransaction++);
            _hydraDb.When(x => x.AddTransaction(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<string>(),
                Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(),
                Arg.Any<uint>(), Arg.Any<string>(), Arg.Any<uint>(), Arg.Any<string>(), Arg.Any<string>(), MaxRetalixTransactionNumber, out _)).Do(x =>
                {
                    _transactionGradeNames.Add((string)x[0]);
                    _transactionCodes.Add((string)x[1]);
                    _transactionQuantities.Add((uint)x[2]);
                    _transactionAmounts.Add((uint)x[3]);
                    _transactionPumpDetails.Add((string)x[4]);
                    _transactionCardNumbers.Add((string)x[5]);
                    _transactionTimes.Add((DateTime)x[6]);
                    _transactionCategories.Add((string)x[7]);
                    _transactionSubCategories.Add((string)x[8]);
                    _transactionDiscountNames.Add((string)x[9]);
                    _transactionDiscountCodes.Add((string)x[10]);
                    _transactionDiscountValues.Add((uint)x[11]);
                    _transactionDiscountCardNumbers.Add((string)x[12]);
                    _transactionLocalAccountMileages.Add((uint)x[13]);
                    _transactionLocalAccountRegistrations.Add((string)x[14]);
                    _transactionTxnNumbers.Add((string)x[15]);
                    x[16] = SalesTransaction;
                });
            _hydraDb.When(x => x.AddToTransaction(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<uint>(), Arg.Any<uint>(),
                Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<string>(), Arg.Any<string>())).Do(x =>
                {
                    _transactionsAddedTo.Add((int)x[0]);
                    _transactionGradeNames.Add((string)x[1]);
                    _transactionCodes.Add((string)x[2]);
                    _transactionQuantities.Add((uint)x[3]);
                    _transactionAmounts.Add((uint)x[4]);
                    _transactionPumpDetails.Add((string)x[5]);
                    _transactionCardNumbers.Add((string)x[6]);
                    _transactionTimes.Add((DateTime)x[7]);
                    _transactionCategories.Add((string)x[8]);
                    _transactionSubCategories.Add((string)x[9]);
                });
            _hydraDb.When(x => x.AddDayEnd(Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<uint>(), Arg.Any<int>(), Arg.Any<short>(),
                Arg.Any<short>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<uint>())).Do(x =>
                {
                    _dayEndFuelAmounts.Add((uint)x[0]);
                    _dayEndDryAmounts.Add((uint)x[1]);
                    _dayEndQuantities.Add((uint)x[2]);
                    _dayEndTransactionNumbers.Add((int)x[3]);
                    _dayEndCategories.Add((short)x[4]);
                    _dayEndSubCategories.Add((short)x[5]);
                    _dayEndGradeCodes.Add((string)x[6]);
                    _dayEndGradeNames.Add((string)x[7]);
                    _dayEndCardProductNames.Add((string)x[8]);
                    _dayEndDiscounts.Add((uint)x[9]);
                });
            _hydraDb.FetchCardReferences().Returns(new List<CardReference>
            {
                new CardReference(CardRef1, CardProductName1, false, Acquirer1, false, CardExternalName1),
                new CardReference(CardRef2, CardProductName2, true, Acquirer2, false, CardExternalName2)
            });
            _hydraDb.GetPrinterConfig().Returns(new PrinterConfig(false, "COM1", 9200, "None", "One", 8));
            _transactionFile = Substitute.For<IHydraTransactionFile>();
            _transactionFile.WriteShiftEnd(Arg.Do<DayEndItem>(x => _shiftEndSummaries.Add(x)), false);
            _transactionFile.WriteShiftEnd(Arg.Do<DayEndItem>(x => _dayEndSummaries.Add(x)), true);
            _transactionFile.WriteSalesItems(Arg.Do<ItemSalesItem[]>(x =>
            {
                foreach (ItemSalesItem item in x)
                {
                    _shiftEndSalesItems.Add(item);
                }
            }), false);
            _transactionFile.WriteSalesItems(Arg.Do<ItemSalesItem[]>(x =>
            {
                foreach (ItemSalesItem item in x)
                {
                    _dayEndSalesItems.Add(item);
                }
            }), true);
            _transactionFile.WriteCardSalesItems(Arg.Do<CardSalesItem[]>(x =>
            {
                foreach (CardSalesItem item in x)
                {
                    _shiftEndCardSalesItems.Add(item);
                }
            }), false);
            _transactionFile.WriteCardSalesItems(Arg.Do<CardSalesItem[]>(x =>
            {
                foreach (CardSalesItem item in x)
                {
                    _dayEndCardSalesItems.Add(item);
                }
            }), true);
            _transactionFile.WriteCategorySalesItems(Arg.Do<CategorySalesItem[]>(x =>
            {
                foreach (CategorySalesItem item in x)
                {
                    _shiftEndCatSalesItems.Add(item);
                }
            }), false);
            _transactionFile.WriteCategorySalesItems(Arg.Do<CategorySalesItem[]>(x =>
            {
                foreach (CategorySalesItem item in x)
                {
                    _dayEndCatSalesItems.Add(item);
                }
            }), true);
            _transactionFile.WriteTransactionFile(Arg.Do<TransactionFileItem[]>(x =>
            {
                foreach (TransactionFileItem item in x)
                {
                    _transactionsFileItems.Add(item);
                }
            }), DateTime.Now);
            _transactionFile.WriteCardVolumeItems(Arg.Any<string>(), Arg.Do<CardVolumeSalesItem[]>(x =>
            {
                foreach (var item in x)
                {
                    _shiftEndCardVolumeSalesItems.Add(item);
                }
            }), false);
            _transactionFile.WriteCardVolumeItems(Arg.Any<string>(), Arg.Do<CardVolumeSalesItem[]>(x =>
            {
                foreach (var item in x)
                {
                    _dayEndCardVolumeSalesItems.Add(item);
                }
            }), true);

            _loggingHelper = Substitute.For<ILoggingHelper>();
            _optWorker = Substitute.For<IFromOptWorker>();
            _journal = Substitute.For<IHtecLogger>();
            _journal.Info(Arg.Do<string>(x => _journalInfos.Add(x)));
            _logger = Substitute.For<IHtecLogger>();
            _logger.Info(Arg.Do<string>(x => _loggerInfos.Add(x)));
            _logManager = Substitute.For<IHtecLogManager>();
            _logManager.GetLogger(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<bool>(), Arg.Any<ILogFormatter>()).Returns(_logger);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _tankGaugeWorker = Substitute.For<ITankGaugeWorker>();
            _tankGaugeWorker.When(x => x.RequestDips()).Do(x => _dipsRequested++);
            _hscWorker = Substitute.For<IPumpWorker>();
            ((IPumpControllerJournal)_hscWorker).When(x => x.RequestMeters(DateTime.UtcNow)).Do(x => _metersRequested++);
            _journalWorker = new JournalWorker(Substitute.For<IBosIntegratorOut<IMessageTracking>>(), _hydraDb, _telemetryWorker, _journal, _logManager, _loggingHelper,
                Substitute.For<IConfigurationManager>(), Substitute.For<ITimerFactory>(), _hscWorker, _hscWorker, _tankGaugeWorker,
                Substitute.For<IPrinterHelper<IMessageTracking>>(), Substitute.For<ILocalAccountWorker>(), Substitute.For<IOptCollection>(),
                Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IPosIntegratorOutTransient<IMessageTracking>>(), Substitute.For<IGradeHelper>(), 
                Substitute.For<IReceiptHelper>(), Substitute.For<IPumpCollection>(), Substitute.For<IShiftDayEndConfig>(), Substitute.For<IPumpIntegratorConfiguration>());
            _journalWorker.RegisterWorker(_optWorker);
            _journalWorker.RegisterWorker(_tankGaugeWorker);
            _journalWorker.RegisterWorker(_hscWorker);
            _journalWorker.Start();
        }

        [Fact]
        public void test_shift_end()
        {
            // Arrange
            const uint fuelAmount = 1010;
            const uint dryAmount = 1020;
            const uint discountValue = 100;
            const int firstTransaction = 30;
            const int lastTransaction = 40;
            const int shiftNumber = 50;
            const int salesCategory = 99;
            const int salesSubCategory = 3;
            const string salesGradeCode = "2";
            const string salesGradeName = "Unleaded";
            const long salesAmount = 1001;
            const long salesQuantity = 10020;
            const long cardAmount = 1004;
            const int cardVolumeGrade = 4;
            const long cardVolume = 10050;
            DateTime endTime = DateTime.Now;
            DateTime startTime = endTime.AddDays(-1);
            _hydraDb.When(x => x.TakeDayEnd(false, out _, out _, out _, out _, out _, out _, out _, out _)).Do(x =>
            {
                x[1] = fuelAmount;
                x[2] = dryAmount;
                x[3] = discountValue;
                x[4] = startTime;
                x[5] = endTime;
                x[6] = firstTransaction;
                x[7] = lastTransaction;
                x[8] = shiftNumber;
            });
            _hydraDb.TakeItemSales(false).Returns(new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory, salesGradeCode, salesGradeName, salesAmount, salesQuantity)
            });
            _hydraDb.TakeCardSales(false).Returns(new List<CardSales> { new CardSales(CardRef1, cardAmount, CardProductName1) });
            _hydraDb.TakeCardVolumeSales().Returns(new List<CardVolumeSales>
                {new CardVolumeSales(CardRef1, cardVolumeGrade, cardVolume, CardProductName1)});
            string expectedJournalInfo = $"{Environment.NewLine}OPT Shift Number: {shiftNumber}" +
                                         $"{Environment.NewLine}Started at {startTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                         $"{Environment.NewLine}Finished at {endTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                         $"{Environment.NewLine}--------------------------------------" +
                                         $"{Environment.NewLine}Transactions {firstTransaction} to {FirstShiftTransaction}" +
                                         $"{Environment.NewLine}{Environment.NewLine}{"Total Fuel Sales",26} = �{fuelAmount / 100.0:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}{"Car Wash Sales",26} = �{dryAmount / 100.0:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}{"Total of Payments",26} = �{(fuelAmount + dryAmount) / 100.0:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}Sales of fuel this shift:" +
                                         $"{Environment.NewLine}{"Ltr.",8}{"VALUE",29}" +
                                         $"{Environment.NewLine}{salesQuantity / 1000.0,8:F2}{salesGradeName,21} �{salesAmount / 100.0,8:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}Card Sales this shift:" +
                                         $"{Environment.NewLine}{CardProductName1,20} �{cardAmount / 100.0,12:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}{"TOTAL",13} �{(fuelAmount + dryAmount) / 100.00,12:F2}" +
                                         $"{Environment.NewLine}{Environment.NewLine}--------------------------------------{Environment.NewLine}{Environment.NewLine}";
            _journalWorker.SetFuelCategory(salesCategory);

            // Act
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestShiftEnd();

            Thread.Sleep(2000);

            // Assert
            _addEventTimes.Should().HaveCount(1);
            foreach (DateTime eventTime in _addEventTimes)
            {
                eventTime.Should().Be(endTime);
            }

            _transactionsFileItems.Should().HaveCount(1);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().Be(EndOfShiftRecType);
                item.Receipt.Should().Be($"{FirstShiftTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().Be(OpName);
                item.Code.Should().Be("");
                item.Qty.Should().Be(CurrencyZero);
                item.Value.Should().Be(CurrencyZero);
                item.CardNum.Should().Be("");
                item.Date.Should().Be(endTime.ToString("ddMMyyyy"));
                item.Time.Should().Be(endTime.ToString("HHmmss"));
                item.Cat.Should().Be("");
                item.SubCat.Should().Be("");
            }

            _dayEndSummaries.Should().BeEmpty();
            _shiftEndSummaries.Should().HaveCount(1);
            foreach (DayEndItem shiftEnd in _shiftEndSummaries)
            {
                shiftEnd.Till.Should().Be($"{TillNumber:D2}");
                shiftEnd.StartDate.Should().Be(startTime.ToString("ddMMyyyy"));
                shiftEnd.StartTime.Should().Be(startTime.ToString("HHmm"));
                shiftEnd.EndDate.Should().Be(endTime.ToString("ddMMyyyy"));
                shiftEnd.EndTime.Should().Be(endTime.ToString("HHmm"));
                shiftEnd.ShiftReferenceNumber.Should().Be($"{shiftNumber}");
                shiftEnd.Opid.Should().Be(OpId);
                shiftEnd.OpName.Should().Be(OpName);
                shiftEnd.Cash.Should().Be(CurrencyZero);
                shiftEnd.Coupon.Should().Be(CurrencyZero);
                shiftEnd.GiftToken.Should().Be(CurrencyZero);
                shiftEnd.SalesDry.Should().Be($"{dryAmount / 100.0:F2}");
                shiftEnd.PromoDry.Should().Be(CurrencyZero);
                shiftEnd.SalesFuel.Should().Be($"{fuelAmount / 100.0:F2}");
                shiftEnd.PromoFuel.Should().Be($"{discountValue / 100.0:F2}");
                shiftEnd.CostDry.Should().Be(CurrencyZero);
                shiftEnd.CostFuel.Should().Be(CurrencyZero);
                shiftEnd.DriveOffs.Should().Be(CurrencyZero);
                shiftEnd.RetroPays.Should().Be(CurrencyZero);
                shiftEnd.PumpTests.Should().Be(CurrencyZero);
                shiftEnd.InvDry.Should().Be(CurrencyZero);
                shiftEnd.InvFuel.Should().Be(CurrencyZero);
                shiftEnd.CardEft.Should().Be($"{(fuelAmount + dryAmount) / 100.0:F2}");
                shiftEnd.CardManual.Should().Be(CurrencyZero);
                shiftEnd.Cheque.Should().Be(CurrencyZero);
                shiftEnd.NumCheques.Should().Be(Zero);
                shiftEnd.FloatIn.Should().Be(CurrencyZero);
                shiftEnd.FloatOut.Should().Be(CurrencyZero);
                shiftEnd.SafeDrop.Should().Be(CurrencyZero);
                shiftEnd.Payments.Should().Be(CurrencyZero);
                shiftEnd.LocalAccSales.Should().Be(CurrencyZero);
                shiftEnd.LocalAccReceipts.Should().Be(CurrencyZero);
                shiftEnd.PriceOverrideSales.Should().Be(CurrencyZero);
                shiftEnd.FullReducedSales.Should().Be(CurrencyZero);
                shiftEnd.FirstSaleNum.Should().Be($"{firstTransaction}");
                shiftEnd.LastSaleNum.Should().Be($"{FirstShiftTransaction}");
                shiftEnd.Status.Should().Be(EndOfShiftStatus);
            }

            _shiftEndSalesItems.Should().HaveCount(1);
            foreach (ItemSalesItem item in _shiftEndSalesItems)
            {
                item.Code.Should().Be(salesGradeCode);
                item.Cat.Should().Be($"{salesCategory}");
                item.Subcat.Should().Be($"{salesSubCategory}");
                item.Cat.Should().Be($"{salesCategory}");
                item.Name.Should().Be(salesGradeName);
                item.Qty.Should().Be($"{salesQuantity / 1000.0:F2}");
                item.Value.Should().Be($"{salesAmount / 100.0:F2}");
            }

            _dayEndSalesItems.Should().BeEmpty();
            _shiftEndCardSalesItems.Should().HaveCount(1);
            foreach (CardSalesItem item in _shiftEndCardSalesItems)
            {
                item.Card.Should().Be($"{CardRef1}");
                item.EftSwipe.Should().Be($"{cardAmount / 100.0:F2}");
                item.EftKeyed.Should().Be(CurrencyZero);
                item.Manual.Should().Be(CurrencyZero);
                item.Cashback.Should().Be(CurrencyZero);
            }

            _dayEndCardSalesItems.Should().BeEmpty();
            _shiftEndCatSalesItems.Should().HaveCount(1);
            foreach (CategorySalesItem item in _shiftEndCatSalesItems)
            {
                item.Cat.Should().Be($"{salesCategory}");
                item.Subcat.Should().Be($"{salesSubCategory}");
                item.SalesValue.Should().Be($"{salesAmount / 100.0:F2}");
                item.SalesInv.Should().Be(CurrencyZero);
                item.Discount.Should().Be(CurrencyZero);
            }
            
            _dayEndCatSalesItems.Should().BeEmpty();
            _retalixShiftEndSummaries.Should().HaveCount(1);
            foreach (RetalixShiftEndItem item in _retalixShiftEndSummaries)
            {
                item.StartDate.Should().Be(startTime.ToString("yyMMdd"));
                item.StartTime.Should().Be(startTime.ToString("HHmmss"));
                item.EndDate.Should().Be(endTime.ToString("yyMMdd"));
                item.EndTime.Should().Be(endTime.ToString("HHmmss"));
                item.ShiftNumber.Should().Be($"{shiftNumber}");
                item.Cash.Should().Be(Zero);
                item.Coupon.Should().Be(Zero);
                item.GiftTokens.Should().Be(Zero);
                item.DrySales.Should().Be($"{dryAmount / 100.0:F2}");
                item.DryPromotions.Should().Be(Zero);
                item.FuelSales.Should().Be($"{fuelAmount / 100.0:F2}");
                item.FuelPromotions.Should().Be(Zero);
                item.Pumptests.Should().Be(Zero);
                item.Zero1.Should().Be(Zero);
                item.Zero2.Should().Be(Zero);
                item.Discounts.Should().Be($"{discountValue / 100.0:F2}");
                item.Zero3.Should().Be(Zero);
                item.EftTotal.Should().Be($"{(dryAmount + fuelAmount) / 100.0:F2}");
                item.LocalAccounts.Should().Be(Zero);
                item.Zero4.Should().Be(Zero);
                item.FirstSaleNumber.Should().Be($"{firstTransaction}");
                item.LastSaleNumber.Should().Be($"{FirstShiftTransaction}");
            }

            _retalixSalesItems.Should().HaveCount(1);
            foreach (RetalixItemSalesItem item in _retalixSalesItems)
            {
                item.Plu.Should().Be(salesGradeCode);
                item.Category.Should().Be($"{salesCategory}");
                item.Subcategory.Should().Be($"{salesSubCategory}");
                item.Name.Should().Be(salesGradeName);
                item.Quantity.Should().Be($"{salesQuantity / 1000.0:F2}");
                item.ValueInPence.Should().Be($"{salesAmount}");
            }

            _retalixCardVolumeItems.Should().HaveCount(1);
            foreach (RetalixCardVolumeItem item in _retalixCardVolumeItems)
            {
                item.CardTypeNumber.Should().Be($"{CardRef1}");
                item.CardTypeName.Should().Be(CardProductName1);
                item.FuelGradeNumber.Should().Be($"{cardVolumeGrade}");
                item.Quantity.Should().Be($"{cardVolume / 1000.0:F2}");
            }

            _retalixCardAmountItems.Should().HaveCount(1);
            foreach (RetalixCardAmountItem item in _retalixCardAmountItems)
            {
                item.CardTypeNumber.Should().Be($"{CardRef1}");
                item.CardTypeName.Should().Be(CardProductName1);
                item.Value.Should().Be($"{cardAmount / 100.0:F2}");
            }

            _journalInfos.Should().HaveCount(1);
            _journalInfos.Should().Contain(expectedJournalInfo);
        }

        [Fact]
        public void test_day_end()
        {
            // Arrange
            const uint shiftFuelAmount = 1010;
            const uint shiftDryAmount = 1020;
            const uint shiftDiscount = 110;
            const int shiftFirstTransaction = 30;
            const int lastTransaction = 40;
            const int shiftNumber = 50;
            const int salesCategory = 99;
            const int salesSubCategory = 3;
            const string salesGradeCode = "2";
            const string salesGradeName = "Unleaded";
            const long salesAmount = 1001;
            const long salesQuantity = 10020;
            const int cardRef = 11;
            const long cardAmount = 1004;
            const string cardProductName = "XXX";
            const int cardVolumeGrade = 4;
            const long cardVolume = 10050;
            const uint dayFuelAmount = 2010;
            const uint dayDryAmount = 2020;
            const uint dayDiscount = 120;
            const int dayFirstTransaction = 20;
            DateTime endTime = DateTime.Now;
            DateTime shiftStartTime = endTime.AddHours(-12);
            DateTime dayStartTime = endTime.AddDays(-1);
            const int tank1 = 1;
            const int tank1DipLevel = 100;
            const byte grade1 = 2;
            const byte pump1 = 1;
            const uint volume1 = 10000;
            const uint cash1 = 1000;
            const ushort price1 = 1239;
            const int salesWashCategory = 98;
            const int salesWashSubCategory = 1;
            const string salesWashGradeCode = "22";
            const string salesWashGradeName = "Car Wash";
            const long salesWashAmount = 102;
            const long salesWashQuantity = 1;
            _hydraDb.When(x => x.TakeDayEnd(false, out _, out _, out _, out _, out _, out _, out _, out _)).Do(x =>
            {
                x[1] = shiftFuelAmount;
                x[2] = shiftDryAmount;
                x[3] = shiftDiscount;
                x[4] = shiftStartTime;
                x[5] = endTime;
                x[6] = shiftFirstTransaction;
                x[7] = lastTransaction;
                x[8] = shiftNumber;
            });
            _hydraDb.When(x => x.TakeDayEnd(true, out _, out _, out _, out _, out _, out _, out _, out _)).Do(x =>
            {
                x[1] = dayFuelAmount;
                x[2] = dayDryAmount;
                x[3] = dayDiscount;
                x[4] = dayStartTime;
                x[5] = endTime;
                x[6] = dayFirstTransaction;
                x[7] = FirstShiftTransaction;
                x[8] = 0;
            });
            _hydraDb.TakeItemSales(false).Returns(new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory, salesGradeCode, salesGradeName, salesAmount, salesQuantity)
            });
            _hydraDb.TakeCardSales(false).Returns(new List<CardSales> { new CardSales(cardRef, cardAmount, cardProductName) });
            _hydraDb.TakeCardVolumeSales().Returns(new List<CardVolumeSales>
                {new CardVolumeSales(cardRef, cardVolumeGrade, cardVolume, cardProductName)});
            _hydraDb.TakeShiftList().Returns(new List<Shift> { new Shift(shiftNumber, shiftStartTime, endTime) });
            _hydraDb.TakeItemSales(true).Returns(new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory, salesGradeCode, salesGradeName, salesAmount, salesQuantity),
                new ItemSales(salesWashCategory, salesWashSubCategory, salesWashGradeCode, salesWashGradeName, salesWashAmount,
                    salesWashQuantity)
            });
            _optWorker.UnmannedPseudoPos.Returns(true);
            _optWorker.GetGradeName(grade1).Returns(salesGradeName);
            string expectedShiftJournalInfo = $"{Environment.NewLine}OPT Shift Number: {shiftNumber}" +
                                              $"{Environment.NewLine}Started at {shiftStartTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                              $"{Environment.NewLine}Finished at {endTime.ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                              $"{Environment.NewLine}--------------------------------------" +
                                              $"{Environment.NewLine}Transactions {shiftFirstTransaction} to {FirstShiftTransaction}" +
                                              $"{Environment.NewLine}{Environment.NewLine}{"Total Fuel Sales",26} = �{shiftFuelAmount / 100.0:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}{"Car Wash Sales",26} = �{shiftDryAmount / 100.0:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}{"Total of Payments",26} = �{(shiftFuelAmount + shiftDryAmount) / 100.0:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}Sales of fuel this shift:" +
                                              $"{Environment.NewLine}{"Ltr.",8}{"VALUE",29}" +
                                              $"{Environment.NewLine}{salesQuantity / 1000.0,8:F2}{salesGradeName,21} �{salesAmount / 100.0,8:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}Card Sales this shift:" +
                                              $"{Environment.NewLine}{cardProductName,20} �{cardAmount / 100.0,12:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}{"TOTAL",13} �{(shiftFuelAmount + shiftDryAmount) / 100.00,12:F2}" +
                                              $"{Environment.NewLine}{Environment.NewLine}--------------------------------------{Environment.NewLine}{Environment.NewLine}";
            string expectedShiftLoggerInfo = $"Journal entry is {expectedShiftJournalInfo}";
            string expectedDayEndJournalInfo = $" {endTime:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine} Shifts included in this day end" +
                                               $"{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}{Environment.NewLine} Shift {shiftNumber}" +
                                               $"{Environment.NewLine}From {shiftStartTime.ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                               $"{Environment.NewLine} until {endTime.ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine} {endTime:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine}END OF DAY REPORT" +
                                               $"{Environment.NewLine}{endTime:dd/MM/yyyy HH:mm:ss}" +
                                               $"{Environment.NewLine}{Environment.NewLine}Volume Sales Report" +
                                               $"{Environment.NewLine}Tnk     Grade         Dip   Tests Sales" +
                                               $"{Environment.NewLine}{tank1,2} {salesGradeName,15} {tank1DipLevel,6}     0      0" +
                                               $"{Environment.NewLine}{Environment.NewLine}Stock Value" +
                                               $"{Environment.NewLine}Tnk Grade         Retail    Value" +
                                               $"{Environment.NewLine}{tank1,2} {salesGradeName,15} {price1 / 1000.0,5:N3} {tank1DipLevel * price1 / 1000.0,10:N2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}Total Stock Value {tank1DipLevel * price1 / 1000.0,11:N2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}{Environment.NewLine}Pump meters at end of Day:" +
                                               $"{Environment.NewLine}Pump Hose         Volume         Cash" +
                                               $"{Environment.NewLine}{pump1,3}    1 {volume1,15} {cash1,13}" +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}{endTime:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine}{Environment.NewLine}END OF DAY FOR OUTDOOR PAYMENT PUMPS" +
                                               $"{Environment.NewLine}{endTime:dd/MM/yyyy HH:mm:ss}" +
                                               $"{Environment.NewLine}{Environment.NewLine}FUEL BREAKDOWN" +
                                               $"{Environment.NewLine}SALES OF {salesGradeName,10} �{salesAmount / 100.0:F2}" +
                                               $"{Environment.NewLine}   TOTAL FUEL SALES �{salesAmount / 100.0:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}CREDIT CARD VALETING BREAKDOWN" +
                                               $"{Environment.NewLine}SALES OF {salesWashGradeName,10} �{salesWashAmount / 100.0:F2}" +
                                               $"{Environment.NewLine}   TOTAL VALETING �{salesWashAmount / 100.0:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}   EXPECTED RECEIPTS �{(salesAmount + salesWashAmount) / 100.0:F2}" +
                                               $"{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}--------------------------------------{Environment.NewLine}";
            string expectedDayEndLoggerInfo = $"Unmanned journal entry is {expectedDayEndJournalInfo}";
            _journalWorker.SetFuelCategory(salesCategory);
            _journalWorker.PumpDataReceived(new HscPumpData
            { PumpNumber = pump1, Dispenser = new HscDispenser { Tank1 = tank1, Grade1 = grade1, Price1 = price1 } });
            _tankGaugeWorker.IsConnected().Returns(true);
            _hscWorker.IsConnected().Returns(true);

            // Act
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestDayEnd();
            Thread.Sleep(100);
            _journalWorker.MetersReceived(new List<HscMeterReadings>
                {new HscMeterReadings {Pump = pump1, Meter1 = new HscPumpTotals {Volume = volume1, Cash = cash1}}});
            _journalWorker.DipsReceived(new List<Dip> { new Dip(tank1, tank1DipLevel, 0, 0, 0) });
            Thread.Sleep(2000);

            // Assert
            _dipsRequested.Should().Be(1);
            _metersRequested.Should().Be(1);
            _addEventTimes.Should().HaveCount(1);
            foreach (DateTime eventTime in _addEventTimes)
            {
                eventTime.Should().Be(endTime);
            }

            _transactionsFileItems.Should().HaveCount(1);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().Be(EndOfShiftRecType);
                item.Receipt.Should().Be($"{FirstShiftTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().Be(OpName);
                item.Code.Should().Be("");
                item.Qty.Should().Be(CurrencyZero);
                item.Value.Should().Be(CurrencyZero);
                item.CardNum.Should().Be("");
                item.Date.Should().Be(endTime.ToString("ddMMyyyy"));
                item.Time.Should().Be(endTime.ToString("HHmmss"));
                item.Cat.Should().Be("");
                item.SubCat.Should().Be("");
            }

            _dayEndSummaries.Should().HaveCount(1);
            foreach (DayEndItem dayEnd in _dayEndSummaries)
            {
                dayEnd.Till.Should().Be($"{TillNumber:D2}");
                dayEnd.StartDate.Should().Be(dayStartTime.ToString("ddMMyyyy"));
                dayEnd.StartTime.Should().Be(dayStartTime.ToString("HHmm"));
                dayEnd.EndDate.Should().Be(endTime.ToString("ddMMyyyy"));
                dayEnd.EndTime.Should().Be(endTime.ToString("HHmm"));
                dayEnd.ShiftReferenceNumber.Should().Be(Zero);
                dayEnd.Opid.Should().Be(OpId);
                dayEnd.OpName.Should().Be(OpName);
                dayEnd.Cash.Should().Be(CurrencyZero);
                dayEnd.Coupon.Should().Be(CurrencyZero);
                dayEnd.GiftToken.Should().Be(CurrencyZero);
                dayEnd.SalesDry.Should().Be($"{dayDryAmount / 100.0:F2}");
                dayEnd.PromoDry.Should().Be(CurrencyZero);
                dayEnd.SalesFuel.Should().Be($"{dayFuelAmount / 100.0:F2}");
                dayEnd.PromoFuel.Should().Be($"{dayDiscount / 100.0:F2}");
                dayEnd.CostDry.Should().Be(CurrencyZero);
                dayEnd.CostFuel.Should().Be(CurrencyZero);
                dayEnd.DriveOffs.Should().Be(CurrencyZero);
                dayEnd.RetroPays.Should().Be(CurrencyZero);
                dayEnd.PumpTests.Should().Be(CurrencyZero);
                dayEnd.InvDry.Should().Be(CurrencyZero);
                dayEnd.InvFuel.Should().Be(CurrencyZero);
                dayEnd.CardEft.Should().Be($"{(dayFuelAmount + dayDryAmount) / 100.0:F2}");
                dayEnd.CardManual.Should().Be(CurrencyZero);
                dayEnd.Cheque.Should().Be(CurrencyZero);
                dayEnd.NumCheques.Should().Be(Zero);
                dayEnd.FloatIn.Should().Be(CurrencyZero);
                dayEnd.FloatOut.Should().Be(CurrencyZero);
                dayEnd.SafeDrop.Should().Be(CurrencyZero);
                dayEnd.Payments.Should().Be(CurrencyZero);
                dayEnd.LocalAccSales.Should().Be(CurrencyZero);
                dayEnd.LocalAccReceipts.Should().Be(CurrencyZero);
                dayEnd.PriceOverrideSales.Should().Be(CurrencyZero);
                dayEnd.FullReducedSales.Should().Be(CurrencyZero);
                dayEnd.FirstSaleNum.Should().Be($"{dayFirstTransaction}");
                dayEnd.LastSaleNum.Should().Be($"{FirstShiftTransaction}");
                dayEnd.Status.Should().Be(EndOfDayStatus);
            }

            _shiftEndSummaries.Should().HaveCount(1);
            foreach (DayEndItem shiftEnd in _shiftEndSummaries)
            {
                shiftEnd.Till.Should().Be($"{TillNumber:D2}");
                shiftEnd.StartDate.Should().Be(shiftStartTime.ToString("ddMMyyyy"));
                shiftEnd.StartTime.Should().Be(shiftStartTime.ToString("HHmm"));
                shiftEnd.EndDate.Should().Be(endTime.ToString("ddMMyyyy"));
                shiftEnd.EndTime.Should().Be(endTime.ToString("HHmm"));
                shiftEnd.ShiftReferenceNumber.Should().Be($"{shiftNumber}");
                shiftEnd.Opid.Should().Be(OpId);
                shiftEnd.OpName.Should().Be(OpName);
                shiftEnd.Cash.Should().Be(CurrencyZero);
                shiftEnd.Coupon.Should().Be(CurrencyZero);
                shiftEnd.GiftToken.Should().Be(CurrencyZero);
                shiftEnd.SalesDry.Should().Be($"{shiftDryAmount / 100.0:F2}");
                shiftEnd.PromoDry.Should().Be(CurrencyZero);
                shiftEnd.SalesFuel.Should().Be($"{shiftFuelAmount / 100.0:F2}");
                shiftEnd.PromoFuel.Should().Be($"{shiftDiscount / 100.0:F2}");
                shiftEnd.CostDry.Should().Be(CurrencyZero);
                shiftEnd.CostFuel.Should().Be(CurrencyZero);
                shiftEnd.DriveOffs.Should().Be(CurrencyZero);
                shiftEnd.RetroPays.Should().Be(CurrencyZero);
                shiftEnd.PumpTests.Should().Be(CurrencyZero);
                shiftEnd.InvDry.Should().Be(CurrencyZero);
                shiftEnd.InvFuel.Should().Be(CurrencyZero);
                shiftEnd.CardEft.Should().Be($"{(shiftFuelAmount + shiftDryAmount) / 100.0:F2}");
                shiftEnd.CardManual.Should().Be(CurrencyZero);
                shiftEnd.Cheque.Should().Be(CurrencyZero);
                shiftEnd.NumCheques.Should().Be(Zero);
                shiftEnd.FloatIn.Should().Be(CurrencyZero);
                shiftEnd.FloatOut.Should().Be(CurrencyZero);
                shiftEnd.SafeDrop.Should().Be(CurrencyZero);
                shiftEnd.Payments.Should().Be(CurrencyZero);
                shiftEnd.LocalAccSales.Should().Be(CurrencyZero);
                shiftEnd.LocalAccReceipts.Should().Be(CurrencyZero);
                shiftEnd.PriceOverrideSales.Should().Be(CurrencyZero);
                shiftEnd.FullReducedSales.Should().Be(CurrencyZero);
                shiftEnd.FirstSaleNum.Should().Be($"{shiftFirstTransaction}");
                shiftEnd.LastSaleNum.Should().Be($"{FirstShiftTransaction}");
                shiftEnd.Status.Should().Be(EndOfShiftStatus);
            }

            _shiftEndSalesItems.Should().HaveCount(1);
            foreach (ItemSalesItem item in _shiftEndSalesItems)
            {
                item.Code.Should().Be(salesGradeCode);
                item.Cat.Should().Be($"{salesCategory}");
                item.Subcat.Should().Be($"{salesSubCategory}");
                item.Cat.Should().Be($"{salesCategory}");
                item.Name.Should().Be(salesGradeName);
                item.Qty.Should().Be($"{salesQuantity / 1000.0:F2}");
                item.Value.Should().Be($"{salesAmount / 100.0:F2}");
            }

            _dayEndSalesItems.Should().HaveCount(2);
            foreach (ItemSalesItem item in _dayEndSalesItems)
            {
                item.Code.Should().BeOneOf(salesGradeCode, salesWashGradeCode);
                item.Cat.Should().BeOneOf($"{salesCategory}", $"{salesWashCategory}");
                item.Subcat.Should().BeOneOf($"{salesSubCategory}", $"{salesWashSubCategory}");
                item.Cat.Should().BeOneOf($"{salesCategory}", $"{salesWashCategory}");
                item.Name.Should().BeOneOf(salesGradeName, salesWashGradeName);
                item.Qty.Should().BeOneOf($"{salesQuantity / 1000.0:F2}", $"{salesWashQuantity / 1000.0:F2}");
                item.Value.Should().BeOneOf($"{salesAmount / 100.0:F2}", $"{salesWashAmount / 100.0:F2}");
            }

            _shiftEndCardSalesItems.Should().HaveCount(1);
            foreach (CardSalesItem item in _shiftEndCardSalesItems)
            {
                item.Card.Should().Be($"{cardRef}");
                item.EftSwipe.Should().Be($"{cardAmount / 100.0:F2}");
                item.EftKeyed.Should().Be(CurrencyZero);
                item.Manual.Should().Be(CurrencyZero);
                item.Cashback.Should().Be(CurrencyZero);
            }

            _dayEndCardSalesItems.Should().BeEmpty();
            _shiftEndCatSalesItems.Should().HaveCount(1);
            foreach (CategorySalesItem item in _shiftEndCatSalesItems)
            {
                item.Cat.Should().Be($"{salesCategory}");
                item.Subcat.Should().Be($"{salesSubCategory}");
                item.SalesValue.Should().Be($"{salesAmount / 100.0:F2}");
                item.SalesInv.Should().Be(CurrencyZero);
                item.Discount.Should().Be(CurrencyZero);
            }

            _dayEndCatSalesItems.Should().HaveCount(2);
            foreach (CategorySalesItem item in _dayEndCatSalesItems)
            {
                item.Cat.Should().BeOneOf($"{salesCategory}", $"{salesWashCategory}");
                item.Subcat.Should().BeOneOf($"{salesSubCategory}", $"{salesWashSubCategory}");
                item.SalesValue.Should().BeOneOf($"{salesAmount / 100.0:F2}", $"{salesWashAmount / 100.0:F2}");
                item.SalesInv.Should().Be(CurrencyZero);
                item.Discount.Should().Be(CurrencyZero);
            }

            _retalixShiftEndSummaries.Should().HaveCount(1);
            foreach (RetalixShiftEndItem item in _retalixShiftEndSummaries)
            {
                item.StartDate.Should().Be(shiftStartTime.ToString("yyMMdd"));
                item.StartTime.Should().Be(shiftStartTime.ToString("HHmmss"));
                item.EndDate.Should().Be(endTime.ToString("yyMMdd"));
                item.EndTime.Should().Be(endTime.ToString("HHmmss"));
                item.ShiftNumber.Should().Be($"{shiftNumber}");
                item.Cash.Should().Be(Zero);
                item.Coupon.Should().Be(Zero);
                item.GiftTokens.Should().Be(Zero);
                item.DrySales.Should().Be($"{shiftDryAmount / 100.0:F2}");
                item.DryPromotions.Should().Be(Zero);
                item.FuelSales.Should().Be($"{shiftFuelAmount / 100.0:F2}");
                item.FuelPromotions.Should().Be(Zero);
                item.Pumptests.Should().Be(Zero);
                item.Zero1.Should().Be(Zero);
                item.Zero2.Should().Be(Zero);
                item.Discounts.Should().Be($"{shiftDiscount / 100.0:F2}");
                item.Zero3.Should().Be(Zero);
                item.EftTotal.Should().Be($"{(shiftDryAmount + shiftFuelAmount) / 100.0:F2}");
                item.LocalAccounts.Should().Be(Zero);
                item.Zero4.Should().Be(Zero);
                item.FirstSaleNumber.Should().Be($"{shiftFirstTransaction}");
                item.LastSaleNumber.Should().Be($"{FirstShiftTransaction}");
            }

            _retalixSalesItems.Should().HaveCount(1);
            foreach (RetalixItemSalesItem item in _retalixSalesItems)
            {
                item.Plu.Should().Be(salesGradeCode);
                item.Category.Should().Be($"{salesCategory}");
                item.Subcategory.Should().Be($"{salesSubCategory}");
                item.Name.Should().Be(salesGradeName);
                item.Quantity.Should().Be($"{salesQuantity / 1000.0:F2}");
                item.ValueInPence.Should().Be($"{salesAmount}");
            }

            _retalixCardVolumeItems.Should().HaveCount(1);
            foreach (RetalixCardVolumeItem item in _retalixCardVolumeItems)
            {
                item.CardTypeNumber.Should().Be($"{cardRef}");
                item.CardTypeName.Should().Be(cardProductName);
                item.FuelGradeNumber.Should().Be($"{cardVolumeGrade}");
                item.Quantity.Should().Be($"{cardVolume / 1000.0:F2}");
            }

            _retalixCardAmountItems.Should().HaveCount(1);
            foreach (RetalixCardAmountItem item in _retalixCardAmountItems)
            {
                item.CardTypeNumber.Should().Be($"{cardRef}");
                item.CardTypeName.Should().Be(cardProductName);
                item.Value.Should().Be($"{cardAmount / 100.0:F2}");
            }

            _journalInfos.Should().HaveCount(1);
            _journalInfos.Should().ContainInOrder(expectedShiftJournalInfo);
            _loggerInfos.Should().HaveCount(8);
            _loggerInfos.Should().ContainInOrder(expectedShiftLoggerInfo, expectedDayEndLoggerInfo);
        }

        [Fact]
        public void test_day_end_shift_end_shift_end_and_day_end()
        {
            // Arrange
            const byte pump1 = 1;
            const byte pump2 = 2;
            const byte tank1 = 1;
            const byte tank2 = 2;
            const byte tank3 = 3;
            const byte grade1 = 2;
            const byte grade2 = 3;
            const byte grade3 = 4;
            const ushort price1 = 1219;
            const ushort price2 = 1229;
            const ushort price3 = 1239;
            string[] salesGradeNames = { "Unleaded", "Super Unleaded", "Diesel" };
            const string carWashGradeName = "Car Wash";
            const int salesCategory = 99;
            const int carWashCategory = 98;
            const int salesSubCategory1 = 3;
            const int salesSubCategory2 = 4;
            const int salesSubCategory3 = 5;
            const int carWashSubCategory = 6;
            const string salesGradeCode = "Code";
            int shiftCount = 0;
            int dayCount = 0;
            DateTime[] times = { DateTime.Now.AddHours(-3), DateTime.Now.AddHours(-2), DateTime.Now.AddHours(-1), DateTime.Now };
            int[] shiftNumbers = { 11, 12, 13 };
            uint[,] meters0 = { { 10000, 20000 }, { 30000, 40000 } };
            uint[,] volumes0 = { { 100000, 200000 }, { 300000, 400000 } };
            uint[,,] amounts = { { { 10101, 20202 }, { 30303, 40404 } }, { { 50505, 60606 }, { 70707, 80808 } }, { { 90909, 101010 }, { 111111, 121212 } } };
            uint[,,] volumes =
                {{{110000, 120000}, {130000, 140000}}, {{150000, 160000}, {170000, 180000}}, {{190000, 200000}, {210000, 220000}}};
            uint[,,] metersAmounts = new uint[3, 2, 2];
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 2; j++)
                {
                    for (int k = 0; k < 2; k++)
                    {
                        metersAmounts[i, j, k] = i == 0 ? meters0[j, k] : metersAmounts[i - 1, j, k] + amounts[i, j, k];
                    }
                }
            }

            uint[,,] metersVolumes = new uint[3, 2, 2];
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 2; j++)
                {
                    for (int k = 0; k < 2; k++)
                    {
                        metersVolumes[i, j, k] = i == 0 ? volumes0[j, k] : metersVolumes[i - 1, j, k] + volumes[i, j, k];
                    }
                }
            }

            uint[,] salesAmounts =
            {
                {amounts[0, 0, 0], amounts[0, 0, 1] + amounts[0, 1, 0], amounts[0, 1, 1]},
                {amounts[1, 0, 0], amounts[1, 0, 1] + amounts[1, 1, 0], amounts[1, 1, 1]},
                {amounts[2, 0, 0], amounts[2, 0, 1] + amounts[2, 1, 0], amounts[2, 1, 1]}
            };
            uint[] fuelAmounts =
            {
                salesAmounts[0, 0] + salesAmounts[0, 1] + salesAmounts[0, 2],
                salesAmounts[1, 0] + salesAmounts[1, 1] + salesAmounts[1, 2],
                salesAmounts[2, 0] + salesAmounts[2, 1] + salesAmounts[2, 2]
            };
            uint[] carWashAmounts = { 1010, 1011, 1012 };
            uint[,] salesQuantities =
            {
                {volumes[0, 0, 0], volumes[0, 0, 1] + volumes[0, 1, 0], volumes[0, 1, 1]},
                {volumes[1, 0, 0], volumes[1, 0, 1] + volumes[1, 1, 0], volumes[1, 1, 1]},
                {volumes[2, 0, 0], volumes[2, 0, 1] + volumes[2, 1, 0], volumes[2, 1, 1]}
            };
            uint[] carWashQuantities = { 2, 3, 4 };
            uint[,] cardAmounts =
            {
                {amounts[0, 0, 1] + carWashAmounts[0], amounts[0, 0, 0] + amounts[0, 1, 0] + amounts[0, 1, 1]},
                {amounts[1, 0, 1] + carWashAmounts[1], amounts[1, 0, 0] + amounts[1, 1, 0] + amounts[1, 1, 1]},
                {amounts[2, 0, 1] + carWashAmounts[2], amounts[2, 0, 0] + amounts[2, 1, 0] + amounts[2, 1, 1]}
            };
            uint[,,] cardVolumes =
            {
                {{0, volumes[0, 0, 1], 0}, {volumes[0, 0, 0], volumes[0, 1, 0], volumes[0, 1, 1]}},
                {{0, volumes[0, 0, 1], 0}, {volumes[0, 0, 0], volumes[0, 1, 0], volumes[0, 1, 1]}},
                {{0, volumes[0, 0, 1], 0}, {volumes[0, 0, 0], volumes[0, 1, 0], volumes[0, 1, 1]}}
            };
            int[] dipLevels0 = { 10000, 20000, 30000 };
            int[,] dipLevels = new int[3, 3];
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    dipLevels[i, j] = i == 0 ? dipLevels0[j] : dipLevels[i - 1, j] - (int)(salesQuantities[i, j] / 1000);
                }
            }

            string[] expectedShiftJournals = new string[3];
            for (int i = 0; i < 3; i++)
            {
                expectedShiftJournals[i] = $"{Environment.NewLine}OPT Shift Number: {shiftNumbers[i]}" +
                                           $"{Environment.NewLine}Started at {times[i].ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                           $"{Environment.NewLine}Finished at {times[i + 1].ToString("h:mm tt on dd/MM/yyyy").ToLower()}" +
                                           $"{Environment.NewLine}--------------------------------------" +
                                           $"{Environment.NewLine}Transactions {FirstShiftTransaction + i} to {FirstShiftTransaction + i}" +
                                           $"{Environment.NewLine}{Environment.NewLine}{"Total Fuel Sales",26} = �{fuelAmounts[i] / 100.0:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}{"Car Wash Sales",26} = �{carWashAmounts[i] / 100.0:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}{"Total of Payments",26} = �{(fuelAmounts[i] + carWashAmounts[i]) / 100.0:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}Sales of fuel this shift:" +
                                           $"{Environment.NewLine}{"Ltr.",8}{"VALUE",29}" +
                                           $"{Environment.NewLine}{salesQuantities[i, 0] / 1000.0,8:F2}{salesGradeNames[0],21} �{salesAmounts[i, 0] / 100.0,8:F2}" +
                                           $"{Environment.NewLine}{salesQuantities[i, 1] / 1000.0,8:F2}{salesGradeNames[1],21} �{salesAmounts[i, 1] / 100.0,8:F2}" +
                                           $"{Environment.NewLine}{salesQuantities[i, 2] / 1000.0,8:F2}{salesGradeNames[2],21} �{salesAmounts[i, 2] / 100.0,8:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}Sales of car wash this shift:" +
                                           $"{Environment.NewLine}{"Qty.",8}{"VALUE",29}" +
                                           $"{Environment.NewLine}{carWashQuantities[i],8}{carWashGradeName,21} �{carWashAmounts[i] / 100.0,8:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}Card Sales this shift:" +
                                           $"{Environment.NewLine}{CardProductName1,20} �{cardAmounts[i, 0] / 100.0,12:F2}" +
                                           $"{Environment.NewLine}{CardProductName2,20} �{cardAmounts[i, 1] / 100.0,12:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}{"TOTAL",13} �{(fuelAmounts[i] + carWashAmounts[i]) / 100.00,12:F2}" +
                                           $"{Environment.NewLine}{Environment.NewLine}Fuel Card Volumes:" +
                                           $"{Environment.NewLine}{CardProductName2}:" +
                                           $"{Environment.NewLine}{salesGradeNames[0],23} {cardVolumes[i, 1, 0] / 1000.0,9:F2} Ltr." +
                                           $"{Environment.NewLine}{salesGradeNames[1],23} {cardVolumes[i, 1, 1] / 1000.0,9:F2} Ltr." +
                                           $"{Environment.NewLine}{salesGradeNames[2],23} {cardVolumes[i, 1, 2] / 1000.0,9:F2} Ltr." +
                                           $"{Environment.NewLine}{Environment.NewLine}--------------------------------------{Environment.NewLine}{Environment.NewLine}";
            }

            string[] expectedDayEndLoggers = new string[2];
            for (int i = 0; i < 2; i++)
            {
                string expectedDayEndJournal = $" {times[i == 0 ? 1 : 3]:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine} Shifts included in this day end" +
                                               $"{Environment.NewLine}--------------------------------------" +
                                               (i == 0
                                                   ? $"{Environment.NewLine}{Environment.NewLine} Shift {shiftNumbers[0]}" +
                                                     $"{Environment.NewLine}From {times[0].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                                     $"{Environment.NewLine} until {times[1].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}"
                                                   : $"{Environment.NewLine}{Environment.NewLine} Shift {shiftNumbers[1]}" +
                                                     $"{Environment.NewLine}From {times[1].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                                     $"{Environment.NewLine} until {times[2].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                                     $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                                     $"{Environment.NewLine} Shift {shiftNumbers[2]}" +
                                                     $"{Environment.NewLine}From {times[2].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}" +
                                                     $"{Environment.NewLine} until {times[3].ToString("hh:mm tt on dd/MM/yyyy").ToLower()}"
                                               ) + $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine} {times[i == 0 ? 1 : 3]:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine}END OF DAY REPORT" +
                                               $"{Environment.NewLine}{times[i == 0 ? 1 : 3]:dd/MM/yyyy HH:mm:ss}" +
                                               $"{Environment.NewLine}{Environment.NewLine}Volume Sales Report" +
                                               $"{Environment.NewLine}Tnk     Grade         Dip   Tests Sales" +
                                               $"{Environment.NewLine}{tank1,2} {salesGradeNames[0],15} {dipLevels[i == 0 ? 0 : 2, 0],6}     0 {(i == 0 ? 0 : salesAmounts[1, 0] + salesAmounts[2, 0]),6}" +
                                               $"{Environment.NewLine}{tank2,2} {salesGradeNames[1],15} {dipLevels[i == 0 ? 0 : 2, 1],6}     0 {(i == 0 ? 0 : salesAmounts[1, 1] + salesAmounts[2, 1]),6}" +
                                               $"{Environment.NewLine}{tank3,2} {salesGradeNames[2],15} {dipLevels[i == 0 ? 0 : 2, 2],6}     0 {(i == 0 ? 0 : salesAmounts[1, 2] + salesAmounts[2, 2]),6}" +
                                               $"{Environment.NewLine}{Environment.NewLine}Stock Value" +
                                               $"{Environment.NewLine}Tnk Grade         Retail    Value" +
                                               $"{Environment.NewLine}{tank1,2} {salesGradeNames[0],15} {price1 / 1000.0,5:N3} {dipLevels[i == 0 ? 0 : 2, 0] * price1 / 1000.0,10:N2}" +
                                               $"{Environment.NewLine}{tank2,2} {salesGradeNames[1],15} {price2 / 1000.0,5:N3} {dipLevels[i == 0 ? 0 : 2, 1] * price2 / 1000.0,10:N2}" +
                                               $"{Environment.NewLine}{tank3,2} {salesGradeNames[2],15} {price3 / 1000.0,5:N3} {dipLevels[i == 0 ? 0 : 2, 2] * price3 / 1000.0,10:N2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}Total Stock Value {(dipLevels[i == 0 ? 0 : 2, 0] * price1 + dipLevels[i == 0 ? 0 : 2, 1] * price2 + dipLevels[i == 0 ? 0 : 2, 2] * price3) / 1000.0,11:N2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}{Environment.NewLine}Pump meters at end of Day:" +
                                               $"{Environment.NewLine}Pump Hose         Volume         Cash" +
                                               $"{Environment.NewLine}{pump1,3}    1 {metersVolumes[i == 0 ? 0 : 2, 0, 0],15} {metersAmounts[i == 0 ? 0 : 2, 0, 0],13}" +
                                               (i == 0
                                                   ? ""
                                                   : $"{Environment.NewLine}previous read {metersVolumes[0, 0, 0],10} {metersAmounts[0, 0, 0],13}" +
                                                     $"{Environment.NewLine}Todays sales {(metersVolumes[2, 0, 0] - metersVolumes[0, 0, 0]) / 1000.0,11:N2}" +
                                                     $" {(metersAmounts[2, 0, 0] - metersAmounts[0, 0, 0]) / 100.0,13:N2}") +
                                               $"{Environment.NewLine}{pump1,3}    2 {metersVolumes[i == 0 ? 0 : 2, 0, 1],15} {metersAmounts[i == 0 ? 0 : 2, 0, 1],13}" +
                                               (i == 0
                                                   ? ""
                                                   : $"{Environment.NewLine}previous read {metersVolumes[0, 0, 1],10} {metersAmounts[0, 0, 1],13}" +
                                                     $"{Environment.NewLine}Todays sales {(metersVolumes[2, 0, 1] - metersVolumes[0, 0, 1]) / 1000.0,11:N2}" +
                                                     $" {(metersAmounts[2, 0, 1] - metersAmounts[0, 0, 1]) / 100.0,13:N2}") +
                                               $"{Environment.NewLine}{pump2,3}    1 {metersVolumes[i == 0 ? 0 : 2, 1, 0],15} {metersAmounts[i == 0 ? 0 : 2, 1, 0],13}" +
                                               (i == 0
                                                   ? ""
                                                   : $"{Environment.NewLine}previous read {metersVolumes[0, 1, 0],10} {metersAmounts[0, 1, 0],13}" +
                                                     $"{Environment.NewLine}Todays sales {(metersVolumes[2, 1, 0] - metersVolumes[0, 1, 0]) / 1000.0,11:N2}" +
                                                     $" {(metersAmounts[2, 1, 0] - metersAmounts[0, 1, 0]) / 100.0,13:N2}") +
                                               $"{Environment.NewLine}{pump2,3}    2 {metersVolumes[i == 0 ? 0 : 2, 1, 1],15} {metersAmounts[i == 0 ? 0 : 2, 1, 1],13}" +
                                               (i == 0
                                                   ? ""
                                                   : $"{Environment.NewLine}previous read {metersVolumes[0, 1, 1],10} {metersAmounts[0, 1, 1],13}" +
                                                     $"{Environment.NewLine}Todays sales {(metersVolumes[2, 1, 1] - metersVolumes[0, 1, 1]) / 1000.0,11:N2}" +
                                                     $" {(metersAmounts[2, 1, 1] - metersAmounts[0, 1, 1]) / 100.0,13:N2}") +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}{times[i == 0 ? 1 : 3]:dd/MM/yyyy 'at' HH:mm}" +
                                               $"{Environment.NewLine}{Environment.NewLine}{Environment.NewLine}END OF DAY FOR OUTDOOR PAYMENT PUMPS" +
                                               $"{Environment.NewLine}{times[i == 0 ? 1 : 3]:dd/MM/yyyy HH:mm:ss}" +
                                               $"{Environment.NewLine}{Environment.NewLine}FUEL BREAKDOWN" +
                                               $"{Environment.NewLine}SALES OF {salesGradeNames[0],10} �{(i == 0 ? salesAmounts[0, 0] : salesAmounts[1, 0] + salesAmounts[2, 0]) / 100.0:F2}" +
                                               $"{Environment.NewLine}SALES OF {salesGradeNames[1],10} �{(i == 0 ? salesAmounts[0, 1] : salesAmounts[1, 1] + salesAmounts[2, 1]) / 100.0:F2}" +
                                               $"{Environment.NewLine}SALES OF {salesGradeNames[2],10} �{(i == 0 ? salesAmounts[0, 2] : salesAmounts[1, 2] + salesAmounts[2, 2]) / 100.0:F2}" +
                                               $"{Environment.NewLine}   TOTAL FUEL SALES �{(i == 0 ? fuelAmounts[0] : fuelAmounts[1] + fuelAmounts[2]) / 100.0:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}CREDIT CARD VALETING BREAKDOWN" +
                                               $"{Environment.NewLine}SALES OF {carWashGradeName,10} �{(i == 0 ? carWashAmounts[0] : carWashAmounts[1] + carWashAmounts[2]) / 100.0:F2}" +
                                               $"{Environment.NewLine}   TOTAL VALETING �{(i == 0 ? carWashAmounts[0] : carWashAmounts[1] + carWashAmounts[2]) / 100.0:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}   EXPECTED RECEIPTS �{(i == 0 ? fuelAmounts[0] + carWashAmounts[0] : fuelAmounts[1] + fuelAmounts[2] + carWashAmounts[1] + carWashAmounts[2]) / 100.0:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}OUTDOOR PAYMENT EFTPOS BREAKDOWN" +
                                               $"{Environment.NewLine}{Acquirer1,30} �{(i == 0 ? cardAmounts[0, 0] : cardAmounts[1, 0] + cardAmounts[2, 0]) / 100.0,8:F2}" +
                                               $"{Environment.NewLine}{Acquirer2,30} �{(i == 0 ? cardAmounts[0, 1] : cardAmounts[1, 1] + cardAmounts[2, 1]) / 100.0,8:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}        TOTAL �{(i == 0 ? cardAmounts[0, 0] + cardAmounts[0, 1] : cardAmounts[1, 0] + cardAmounts[1, 1] + cardAmounts[2, 0] + cardAmounts[2, 1]) / 100.0,12:F2}" +
                                               $"{Environment.NewLine}{Environment.NewLine}--------------------------------------" +
                                               $"{Environment.NewLine}--------------------------------------{Environment.NewLine}";
                expectedDayEndLoggers[i] = $"Unmanned journal entry is {expectedDayEndJournal}";
            }

            _hydraDb.When(x => x.TakeDayEnd(false, out _, out _, out _, out _, out _, out _, out _, out _)).Do(x =>
            {
                x[1] = fuelAmounts[shiftCount];
                x[2] = carWashAmounts[shiftCount];
                x[3] = (uint)0;
                x[4] = times[shiftCount];
                x[5] = times[shiftCount + 1];
                x[6] = FirstShiftTransaction + shiftCount;
                x[7] = FirstShiftTransaction + shiftCount;
                x[8] = shiftNumbers[shiftCount];
                shiftCount++;
            });
            // ReSharper disable once ImplicitlyCapturedClosure
            _hydraDb.When(x => x.TakeDayEnd(true, out _, out _, out _, out _, out _, out _, out _, out _)).Do(x =>
            {
                x[1] = dayCount == 0 ? fuelAmounts[0] : fuelAmounts[1] + fuelAmounts[2];
                x[2] = dayCount == 0 ? carWashAmounts[0] : carWashAmounts[1] + carWashAmounts[2];
                x[3] = (uint)0;
                x[4] = dayCount == 0 ? times[0] : times[1];
                x[5] = dayCount == 0 ? times[1] : times[3];
                x[6] = dayCount == 0 ? FirstShiftTransaction : FirstShiftTransaction + 1;
                x[7] = dayCount == 0 ? FirstShiftTransaction : FirstShiftTransaction + 2;
                x[8] = 0;
                dayCount++;
            });
            _hydraDb.TakeShiftList().Returns(new List<Shift> { new Shift(shiftNumbers[0], times[0], times[1]) },
                new List<Shift> { new Shift(shiftNumbers[1], times[1], times[2]), new Shift(shiftNumbers[2], times[2], times[3]) });
            _hydraDb.TakeItemSales(false).Returns(new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory1, salesGradeCode, salesGradeNames[0], salesAmounts[0, 0],
                    salesQuantities[0, 0]),
                new ItemSales(salesCategory, salesSubCategory2, salesGradeCode, salesGradeNames[1], salesAmounts[0, 1],
                    salesQuantities[0, 1]),
                new ItemSales(salesCategory, salesSubCategory3, salesGradeCode, salesGradeNames[2], salesAmounts[0, 2],
                    salesQuantities[0, 2]),
                new ItemSales(carWashCategory, carWashSubCategory, salesGradeCode, carWashGradeName, carWashAmounts[0],
                    carWashQuantities[0])
            }, new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory1, salesGradeCode, salesGradeNames[0], salesAmounts[1, 0],
                    salesQuantities[1, 0]),
                new ItemSales(salesCategory, salesSubCategory2, salesGradeCode, salesGradeNames[1], salesAmounts[1, 1],
                    salesQuantities[1, 1]),
                new ItemSales(salesCategory, salesSubCategory3, salesGradeCode, salesGradeNames[2], salesAmounts[1, 2],
                    salesQuantities[1, 2]),
                new ItemSales(carWashCategory, carWashSubCategory, salesGradeCode, carWashGradeName, carWashAmounts[1],
                    carWashQuantities[1])
            }, new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory1, salesGradeCode, salesGradeNames[0], salesAmounts[2, 0],
                    salesQuantities[2, 0]),
                new ItemSales(salesCategory, salesSubCategory2, salesGradeCode, salesGradeNames[1], salesAmounts[2, 1],
                    salesQuantities[2, 1]),
                new ItemSales(salesCategory, salesSubCategory3, salesGradeCode, salesGradeNames[2], salesAmounts[2, 2],
                    salesQuantities[2, 2]),
                new ItemSales(carWashCategory, carWashSubCategory, salesGradeCode, carWashGradeName, carWashAmounts[2],
                    carWashQuantities[2])
            });
            _hydraDb.TakeItemSales(true).Returns(new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory1, salesGradeCode, salesGradeNames[0], salesAmounts[0, 0],
                    salesQuantities[0, 0]),
                new ItemSales(salesCategory, salesSubCategory2, salesGradeCode, salesGradeNames[1], salesAmounts[0, 1],
                    salesQuantities[0, 1]),
                new ItemSales(salesCategory, salesSubCategory3, salesGradeCode, salesGradeNames[2], salesAmounts[0, 2],
                    salesQuantities[0, 2]),
                new ItemSales(carWashCategory, carWashSubCategory, salesGradeCode, carWashGradeName, carWashAmounts[0],
                    carWashQuantities[0])
            }, new List<ItemSales>
            {
                new ItemSales(salesCategory, salesSubCategory1, salesGradeCode, salesGradeNames[0], salesAmounts[1, 0] + salesAmounts[2, 0],
                    salesQuantities[1, 0] + salesQuantities[2, 0]),
                new ItemSales(salesCategory, salesSubCategory2, salesGradeCode, salesGradeNames[1], salesAmounts[1, 1] + salesAmounts[2, 1],
                    salesQuantities[1, 1] + salesQuantities[2, 1]),
                new ItemSales(salesCategory, salesSubCategory3, salesGradeCode, salesGradeNames[2], salesAmounts[1, 2] + salesAmounts[2, 2],
                    salesQuantities[1, 2] + salesQuantities[2, 2]),
                new ItemSales(carWashCategory, carWashSubCategory, salesGradeCode, carWashGradeName, carWashAmounts[1] + carWashAmounts[2],
                    carWashQuantities[1] + carWashQuantities[2])
            });
            _hydraDb.TakeCardSales(false).Returns(new List<CardSales>
            {
                new CardSales(CardRef1, cardAmounts[0, 0], CardProductName1),
                new CardSales(CardRef2, cardAmounts[0, 1], CardProductName2)
            }, new List<CardSales>
            {
                new CardSales(CardRef1, cardAmounts[1, 0], CardProductName1),
                new CardSales(CardRef2, cardAmounts[1, 1], CardProductName2)
            }, new List<CardSales>
            {
                new CardSales(CardRef1, cardAmounts[2, 0], CardProductName1),
                new CardSales(CardRef2, cardAmounts[2, 1], CardProductName2)
            });
            _hydraDb.TakeCardSales(true).Returns(new List<CardSales>
            {
                new CardSales(CardRef1, cardAmounts[0, 0], CardProductName1),
                new CardSales(CardRef2, cardAmounts[0, 1], CardProductName2)
            }, new List<CardSales>
            {
                new CardSales(CardRef1, cardAmounts[1, 0] + cardAmounts[2, 0], CardProductName1),
                new CardSales(CardRef2, cardAmounts[1, 1] + cardAmounts[2, 1], CardProductName2)
            });
            _hydraDb.TakeCardVolumeSales().Returns(new List<CardVolumeSales>
            {
                new CardVolumeSales(CardRef1, grade2, cardVolumes[0, 0, 1], CardProductName1),
                new CardVolumeSales(CardRef2, grade1, cardVolumes[0, 1, 0], CardProductName2),
                new CardVolumeSales(CardRef2, grade2, cardVolumes[0, 1, 1], CardProductName2),
                new CardVolumeSales(CardRef2, grade3, cardVolumes[0, 1, 2], CardProductName2)
            }, new List<CardVolumeSales>
            {
                new CardVolumeSales(CardRef1, grade2, cardVolumes[1, 0, 1], CardProductName1),
                new CardVolumeSales(CardRef2, grade1, cardVolumes[1, 1, 0], CardProductName2),
                new CardVolumeSales(CardRef2, grade2, cardVolumes[1, 1, 1], CardProductName2),
                new CardVolumeSales(CardRef2, grade3, cardVolumes[1, 1, 2], CardProductName2)
            }, new List<CardVolumeSales>
            {
                new CardVolumeSales(CardRef1, grade2, cardVolumes[2, 0, 1], CardProductName1),
                new CardVolumeSales(CardRef2, grade1, cardVolumes[2, 1, 0], CardProductName2),
                new CardVolumeSales(CardRef2, grade2, cardVolumes[2, 1, 1], CardProductName2),
                new CardVolumeSales(CardRef2, grade3, cardVolumes[2, 1, 2], CardProductName2)
            });
            _optWorker.UnmannedPseudoPos.Returns(true);
            _optWorker.GetGradeName(grade1).Returns(salesGradeNames[0]);
            _optWorker.GetGradeName(grade2).Returns(salesGradeNames[1]);
            _optWorker.GetGradeName(grade3).Returns(salesGradeNames[2]);
            _journalWorker.SetFuelCategory(salesCategory);
            _journalWorker.PumpDataReceived(new HscPumpData
            {
                PumpNumber = pump1,
                Dispenser = new HscDispenser
                { Tank1 = tank1, Grade1 = grade1, Price1 = price1, Tank2 = tank2, Grade2 = grade2, Price2 = price2 }
            });
            _journalWorker.PumpDataReceived(new HscPumpData
            {
                PumpNumber = pump2,
                Dispenser = new HscDispenser
                { Tank1 = tank2, Grade1 = grade2, Price1 = price2, Tank2 = tank3, Grade2 = grade3, Price2 = price3 }
            });
            _tankGaugeWorker.IsConnected().Returns(true);
            _hscWorker.IsConnected().Returns(true);

            // Act
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestDayEnd();
            Thread.Sleep(100);
            _journalWorker.MetersReceived(new List<HscMeterReadings>
            {
                new HscMeterReadings
                {
                    Pump = pump1, Meter1 = new HscPumpTotals {Volume = metersVolumes[0, 0, 0], Cash = metersAmounts[0, 0, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[0, 0, 1], Cash = metersAmounts[0, 0, 1]}
                },
                new HscMeterReadings
                {
                    Pump = pump2, Meter1 = new HscPumpTotals {Volume = metersVolumes[0, 1, 0], Cash = metersAmounts[0, 1, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[0, 1, 1], Cash = metersAmounts[0, 1, 1]}
                }
            });
            _journalWorker.DipsReceived(new List<Dip>
            {
                new Dip(tank1, dipLevels[0, 0], 0, 0, 0), new Dip(tank2, dipLevels[0, 1], 0, 0, 0), new Dip(tank3, dipLevels[0, 2], 0, 0, 0)
            });
            Thread.Sleep(2000);
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestShiftEnd();
            Thread.Sleep(100);
            _journalWorker.MetersReceived(new List<HscMeterReadings>
            {
                new HscMeterReadings
                {
                    Pump = pump1, Meter1 = new HscPumpTotals {Volume = metersVolumes[1, 0, 0], Cash = metersAmounts[1, 0, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[1, 0, 1], Cash = metersAmounts[1, 0, 1]}
                },
                new HscMeterReadings
                {
                    Pump = pump2, Meter1 = new HscPumpTotals {Volume = metersVolumes[1, 1, 0], Cash = metersAmounts[1, 1, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[1, 1, 1], Cash = metersAmounts[1, 1, 1]}
                }
            });
            _journalWorker.DipsReceived(new List<Dip>
            {
                new Dip(tank1, dipLevels[1, 0], 0, 0, 0), new Dip(tank2, dipLevels[1, 1], 0, 0, 0), new Dip(tank3, dipLevels[1, 2], 0, 0, 0)
            });
            Thread.Sleep(2000);
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestShiftEnd();
            Thread.Sleep(100);
            _journalWorker.MetersReceived(new List<HscMeterReadings>
            {
                new HscMeterReadings
                {
                    Pump = pump1, Meter1 = new HscPumpTotals {Volume = metersVolumes[2, 0, 0], Cash = metersAmounts[2, 0, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[2, 0, 1], Cash = metersAmounts[2, 0, 1]}
                },
                new HscMeterReadings
                {
                    Pump = pump2, Meter1 = new HscPumpTotals {Volume = metersVolumes[2, 1, 0], Cash = metersAmounts[2, 1, 0]},
                    Meter2 = new HscPumpTotals {Volume = metersVolumes[2, 1, 1], Cash = metersAmounts[2, 1, 1]}
                }
            });
            _journalWorker.DipsReceived(new List<Dip>
            {
                new Dip(tank1, dipLevels[2, 0], 0, 0, 0), new Dip(tank2, dipLevels[2, 1], 0, 0, 0), new Dip(tank3, dipLevels[2, 2], 0, 0, 0)
            });
            Thread.Sleep(2000);
            ((IBosIntegratorInJournal<IMessageTracking>)_journalWorker).RequestDayEnd();
            Thread.Sleep(2000);

            // Assert
            _dipsRequested.Should().Be(3);
            _metersRequested.Should().Be(3);
            _journalInfos.Should().HaveCount(3);
            _journalInfos.Should().ContainInOrder(expectedShiftJournals[0], expectedShiftJournals[1], expectedShiftJournals[2]);
            _loggerInfos.Should().HaveCount(19);
            _loggerInfos.Should().ContainInOrder(expectedDayEndLoggers[0], expectedDayEndLoggers[1]);
        }





        [Fact]
        public void test_write_sales_item_fuel_only()
        {
            // Arrange
            const string fuelGradeName = "Unleaded";
            const byte fuelGrade = 2;
            const uint fuelQuantity = 30030;
            const uint fuelAmount = 3004;
            const byte pumpNumber = 3;
            const byte hose = 4;
            const string cardNumber = "1111";
            const string cardProductName = "XXX";
            const string discountName = "A Discount";
            const string discountType = "XXX";
            const uint discountValue = 100;
            const string discountCardNumber = "123";
            const uint localAccountMileage = 123;
            const string localAccountRegistration = "ABCD";
            const string txnNumber = "678";
            JournalTotalSalesItem total = new JournalTotalSalesItem
            {
                PumpNumber = pumpNumber,
                Hose = hose,
                CardNumber = cardNumber,
                CardProductName = cardProductName
            };
            IList<JournalFuelSalesItem> fuelSales = new List<JournalFuelSalesItem>
            {
                new JournalFuelSalesItem {Grade = fuelGrade, GradeName = fuelGradeName, Quantity = fuelQuantity, Amount = fuelAmount}
            };
            IList<JournalCarWashSalesItem> carWashSales = new List<JournalCarWashSalesItem>();
            IList<JournalOtherSalesItem> otherSales = new List<JournalOtherSalesItem>();
            JournalDiscountItem discount = new JournalDiscountItem
            {
                Name = discountName,
                Type = discountType,
                Value = discountValue,
                CardNumber = discountCardNumber
            };
            JournalLocalAccountItem localAccount = new JournalLocalAccountItem
            {
                Mileage = localAccountMileage,
                Registration = localAccountRegistration
            };

            // Act
            _journalWorker.WriteSalesItems(total, fuelSales, carWashSales, otherSales, discount, localAccount, txnNumber,
                out int transactionNumber);

            // Assert
            transactionNumber.Should().Be(SalesTransaction);
            _transactionGradeNames.Should().HaveCount(1);
            _transactionGradeNames.Should().Contain(fuelGradeName);
            _transactionCodes.Should().HaveCount(1);
            _transactionCodes.Should().Contain($"FUEL{fuelGrade}");
            _transactionQuantities.Should().HaveCount(1);
            _transactionQuantities.Should().Contain(fuelQuantity);
            _transactionAmounts.Should().HaveCount(1);
            _transactionAmounts.Should().Contain(fuelAmount);
            _transactionPumpDetails.Should().HaveCount(1);
            _transactionPumpDetails.Should().Contain($"PUMP {pumpNumber}:{hose}");
            _transactionCardNumbers.Should().HaveCount(1);
            _transactionCardNumbers.Should().Contain(cardNumber);
            _transactionTimes.Should().HaveCount(1);
            _transactionCategories.Should().HaveCount(1);
            _transactionCategories.Should().Contain($"{FuelCategory}");
            _transactionSubCategories.Should().HaveCount(1);
            _transactionSubCategories.Should().Contain($"{fuelGrade}");
            _transactionDiscountNames.Should().HaveCount(1);
            _transactionDiscountNames.Should().Contain(discountName);
            _transactionDiscountCodes.Should().HaveCount(1);
            _transactionDiscountCodes.Should().Contain(discountType);
            _transactionDiscountValues.Should().HaveCount(1);
            _transactionDiscountValues.Should().Contain(discountValue);
            _transactionDiscountCardNumbers.Should().HaveCount(1);
            _transactionDiscountCardNumbers.Should().Contain(discountCardNumber);
            _transactionLocalAccountMileages.Should().HaveCount(1);
            _transactionLocalAccountMileages.Should().Contain(localAccountMileage);
            _transactionLocalAccountRegistrations.Should().HaveCount(1);
            _transactionLocalAccountRegistrations.Should().Contain(localAccountRegistration);
            _transactionTxnNumbers.Should().HaveCount(1);
            _transactionTxnNumbers.Should().Contain(txnNumber);
            _transactionsAddedTo.Should().BeEmpty();
            _dayEndFuelAmounts.Should().HaveCount(1);
            _dayEndFuelAmounts.Should().Contain(fuelAmount);
            _dayEndDryAmounts.Should().HaveCount(1);
            _dayEndDryAmounts.Should().Contain(0);
            _dayEndQuantities.Should().HaveCount(1);
            _dayEndQuantities.Should().Contain(fuelQuantity);
            _dayEndTransactionNumbers.Should().HaveCount(1);
            _dayEndTransactionNumbers.Should().Contain(SalesTransaction);
            _dayEndCategories.Should().HaveCount(1);
            _dayEndCategories.Should().Contain(FuelCategory);
            _dayEndSubCategories.Should().HaveCount(1);
            _dayEndSubCategories.Should().Contain(fuelGrade);
            _dayEndGradeCodes.Should().HaveCount(1);
            _dayEndGradeCodes.Should().Contain($"FUEL{fuelGrade}");
            _dayEndGradeNames.Should().HaveCount(1);
            _dayEndGradeNames.Should().Contain(fuelGradeName);
            _dayEndCardProductNames.Should().HaveCount(1);
            _dayEndCardProductNames.Should().Contain(cardProductName);
            _dayEndDiscounts.Should().HaveCount(1);
            _dayEndDiscounts.Should().Contain(discountValue);
            _transactionsFileItems.Should().HaveCount(4);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().BeOneOf(SalesItemRecType, MethodOfPaymentRecType, EndOfSalesRecType, DiscountItemRecType);
                item.Receipt.Should().Be($"{SalesTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().BeOneOf(fuelGradeName, discountName, "");
                item.Code.Should().BeOneOf($"FUEL{fuelGrade}", discountType, localAccountRegistration, "");
                item.Qty.Should().BeOneOf($"{fuelQuantity / 1000.0:F2}", $"{localAccountMileage}", CurrencyZero);
                item.Value.Should().BeOneOf($"{fuelAmount / 100.0:F2}", $"{discountValue / 100.0:F2}", CurrencyZero);
                item.CardNum.Should().BeOneOf($"PUMP {pumpNumber}:{hose}", cardNumber, discountCardNumber, "");
                item.Cat.Should().BeOneOf($"{FuelCategory}", "");
                item.SubCat.Should().BeOneOf($"{fuelGrade}", "");
            }
        }

        [Fact]
        public void test_write_sales_item_car_wash_only()
        {
            // Arrange
            const byte programId = 22;
            const string washName = "A Wash";
            const uint washQuantity = 4;
            const uint washAmount = 200;
            const short washCategory = 91;
            const short washSubCategory = 7;
            const byte pumpNumber = 3;
            const byte hose = 4;
            const string cardNumber = "1111";
            const string discountName = "A Discount";
            const string discountType = "XXX";
            const uint discountValue = 100;
            const string discountCardNumber = "123";
            const string txnNumber = "765";
            JournalTotalSalesItem total = new JournalTotalSalesItem
            {
                PumpNumber = pumpNumber,
                Hose = hose,
                CardNumber = cardNumber,
                CardProductName = CardProductName1
            };
            IList<JournalFuelSalesItem> fuelSales = new List<JournalFuelSalesItem>();
            IList<JournalCarWashSalesItem> carWashSales = new List<JournalCarWashSalesItem>
            {
                new JournalCarWashSalesItem
                {
                    ProgramId = programId,
                    WashName = washName,
                    Quantity = washQuantity,
                    Amount = washAmount,
                    Category = washCategory,
                    Subcategory = washSubCategory
                }
            };
            IList<JournalOtherSalesItem> otherSales = new List<JournalOtherSalesItem>();
            JournalDiscountItem discount = new JournalDiscountItem
            {
                Name = discountName,
                Type = discountType,
                Value = discountValue,
                CardNumber = discountCardNumber
            };

            // Act
            _journalWorker.WriteSalesItems(total, fuelSales, carWashSales, otherSales, discount, null, txnNumber, out int transactionNumber);

            // Assert
            transactionNumber.Should().Be(SalesTransaction);
            _transactionGradeNames.Should().HaveCount(1);
            _transactionGradeNames.Should().Contain(washName);
            _transactionCodes.Should().HaveCount(1);
            _transactionCodes.Should().Contain($"CARWASH{programId}");
            _transactionQuantities.Should().HaveCount(1);
            _transactionQuantities.Should().Contain(washQuantity);
            _transactionAmounts.Should().HaveCount(1);
            _transactionAmounts.Should().Contain(washAmount);
            _transactionPumpDetails.Should().HaveCount(1);
            _transactionPumpDetails.Should().Contain("");
            _transactionCardNumbers.Should().HaveCount(1);
            _transactionCardNumbers.Should().Contain(cardNumber);
            _transactionTimes.Should().HaveCount(1);
            _transactionCategories.Should().HaveCount(1);
            _transactionCategories.Should().Contain($"{washCategory}");
            _transactionSubCategories.Should().HaveCount(1);
            _transactionSubCategories.Should().Contain($"{washSubCategory}");
            _transactionsAddedTo.Should().BeEmpty();
            _dayEndFuelAmounts.Should().HaveCount(1);
            _dayEndFuelAmounts.Should().Contain(0);
            _dayEndDryAmounts.Should().HaveCount(1);
            _dayEndDryAmounts.Should().Contain(washAmount);
            _dayEndQuantities.Should().HaveCount(1);
            _dayEndQuantities.Should().Contain(washQuantity);
            _dayEndTransactionNumbers.Should().HaveCount(1);
            _dayEndTransactionNumbers.Should().Contain(SalesTransaction);
            _dayEndCategories.Should().HaveCount(1);
            _dayEndCategories.Should().Contain(washCategory);
            _dayEndSubCategories.Should().HaveCount(1);
            _dayEndSubCategories.Should().Contain(washSubCategory);
            _dayEndGradeCodes.Should().HaveCount(1);
            _dayEndGradeCodes.Should().Contain($"CARWASH{programId}");
            _dayEndGradeNames.Should().HaveCount(1);
            _dayEndGradeNames.Should().Contain(washName);
            _dayEndCardProductNames.Should().HaveCount(1);
            _dayEndCardProductNames.Should().Contain(CardProductName1);
            _dayEndDiscounts.Should().HaveCount(1);
            _dayEndDiscounts.Should().Contain(discountValue);
            _transactionTxnNumbers.Should().HaveCount(1);
            _transactionTxnNumbers.Should().Contain(txnNumber);
            _transactionsFileItems.Should().HaveCount(4);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().BeOneOf(SalesItemRecType, MethodOfPaymentRecType, EndOfSalesRecType, DiscountItemRecType);
                item.Receipt.Should().Be($"{SalesTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().BeOneOf(washName, discountName, "");
                item.Code.Should().BeOneOf($"CARWASH{programId}", discountType, "");
                item.Qty.Should().BeOneOf($"{washQuantity:F2}", CurrencyZero);
                item.Value.Should().BeOneOf($"{washAmount / 100.0:F2}", $"{discountValue / 100.0:F2}", CurrencyZero);
                item.CardNum.Should().BeOneOf(cardNumber, discountCardNumber, "");
                item.Cat.Should().BeOneOf($"{washCategory}", "");
                item.SubCat.Should().BeOneOf($"{washSubCategory}", "");
            }
        }

        [Fact]
        public void test_write_sales_item_fuel_and_car_wash()
        {
            // Arrange
            const string fuelGradeName = "Unleaded";
            const byte fuelGrade = 2;
            const uint fuelQuantity = 30030;
            const uint fuelAmount = 3004;
            const byte programId = 22;
            const string washName = "A Wash";
            const uint washQuantity = 4;
            const uint washAmount = 200;
            const short washCategory = 91;
            const short washSubCategory = 7;
            const byte pumpNumber = 3;
            const byte hose = 4;
            const string cardNumber = "1111";
            const string txnNumber = "765";
            JournalTotalSalesItem total = new JournalTotalSalesItem
            {
                PumpNumber = pumpNumber,
                Hose = hose,
                CardNumber = cardNumber,
                CardProductName = CardProductName1
            };
            IList<JournalFuelSalesItem> fuelSales = new List<JournalFuelSalesItem>
            {
                new JournalFuelSalesItem {Grade = fuelGrade, GradeName = fuelGradeName, Quantity = fuelQuantity, Amount = fuelAmount}
            };
            IList<JournalCarWashSalesItem> carWashSales = new List<JournalCarWashSalesItem>
            {
                new JournalCarWashSalesItem
                {
                    ProgramId = programId,
                    WashName = washName,
                    Quantity = washQuantity,
                    Amount = washAmount,
                    Category = washCategory,
                    Subcategory = washSubCategory
                }
            };
            IList<JournalOtherSalesItem> otherSales = new List<JournalOtherSalesItem>();

            // Act
            _journalWorker.WriteSalesItems(total, fuelSales, carWashSales, otherSales, null, null, txnNumber, out int transactionNumber);

            // Assert
            transactionNumber.Should().Be(SalesTransaction);
            _transactionGradeNames.Should().HaveCount(2);
            _transactionGradeNames.Should().ContainInOrder(fuelGradeName, washName);
            _transactionCodes.Should().HaveCount(2);
            _transactionCodes.Should().ContainInOrder($"FUEL{fuelGrade}", $"CARWASH{programId}");
            _transactionQuantities.Should().HaveCount(2);
            _transactionQuantities.Should().ContainInOrder(fuelQuantity, washQuantity);
            _transactionAmounts.Should().HaveCount(2);
            _transactionAmounts.Should().ContainInOrder(fuelAmount, washAmount);
            _transactionPumpDetails.Should().HaveCount(2);
            _transactionPumpDetails.Should().ContainInOrder($"PUMP {pumpNumber}:{hose}", "");
            _transactionCardNumbers.Should().HaveCount(2);
            _transactionCardNumbers.Should().ContainInOrder(cardNumber, cardNumber);
            _transactionTimes.Should().HaveCount(2);
            _transactionCategories.Should().HaveCount(2);
            _transactionCategories.Should().ContainInOrder($"{FuelCategory}", $"{washCategory}");
            _transactionSubCategories.Should().HaveCount(2);
            _transactionSubCategories.Should().ContainInOrder($"{fuelGrade}", $"{washSubCategory}");
            _transactionsAddedTo.Should().HaveCount(1);
            _transactionsAddedTo.Should().Contain(SalesTransaction);
            _dayEndFuelAmounts.Should().HaveCount(2);
            _dayEndFuelAmounts.Should().ContainInOrder(fuelAmount, (uint)0);
            _dayEndDryAmounts.Should().HaveCount(2);
            _dayEndDryAmounts.Should().ContainInOrder((uint)0, washAmount);
            _dayEndQuantities.Should().HaveCount(2);
            _dayEndQuantities.Should().ContainInOrder(fuelQuantity, washQuantity);
            _dayEndTransactionNumbers.Should().HaveCount(2);
            _dayEndTransactionNumbers.Should().ContainInOrder(SalesTransaction, SalesTransaction);
            _dayEndCategories.Should().HaveCount(2);
            _dayEndCategories.Should().ContainInOrder(FuelCategory, washCategory);
            _dayEndSubCategories.Should().HaveCount(2);
            _dayEndSubCategories.Should().ContainInOrder((short)fuelGrade, washSubCategory);
            _dayEndGradeCodes.Should().HaveCount(2);
            _dayEndGradeCodes.Should().ContainInOrder($"FUEL{fuelGrade}", $"CARWASH{programId}");
            _dayEndGradeNames.Should().HaveCount(2);
            _dayEndGradeNames.Should().ContainInOrder(fuelGradeName, washName);
            _dayEndCardProductNames.Should().HaveCount(2);
            _dayEndCardProductNames.Should().ContainInOrder(CardProductName1, CardProductName1);
            _transactionTxnNumbers.Should().HaveCount(1);
            _transactionTxnNumbers.Should().Contain(txnNumber);
            _transactionsFileItems.Should().HaveCount(4);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().BeOneOf(SalesItemRecType, MethodOfPaymentRecType, EndOfSalesRecType);
                item.Receipt.Should().Be($"{SalesTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().BeOneOf(fuelGradeName, washName, "");
                item.Code.Should().BeOneOf($"FUEL{fuelGrade}", $"CARWASH{programId}", "");
                item.Qty.Should().BeOneOf($"{fuelQuantity / 1000.0:F2}", $"{washQuantity:F2}", CurrencyZero);
                item.Value.Should().BeOneOf($"{fuelAmount / 100.0:F2}", $"{washAmount / 100.0:F2}", CurrencyZero);
                item.CardNum.Should().BeOneOf($"PUMP {pumpNumber}:{hose}", cardNumber, "");
                item.Cat.Should().BeOneOf($"{FuelCategory}", $"{washCategory}", "");
                item.SubCat.Should().BeOneOf($"{fuelGrade}", $"{washSubCategory}", "");
            }
        }

        [Fact]
        public void test_write_sales_item_fuel_car_wash_and_other()
        {
            // Arrange
            const string fuelGradeName = "Unleaded";
            const byte fuelGrade = 2;
            const uint fuelQuantity = 30030;
            const uint fuelAmount = 3004;
            const byte programId = 22;
            const string washName = "A Wash";
            const uint washQuantity = 4;
            const uint washAmount = 200;
            const short washCategory = 91;
            const short washSubCategory = 7;
            const string otherProductCode = "44";
            const uint otherQuantity = 8;
            const uint otherAmount = 900;
            const byte pumpNumber = 3;
            const byte hose = 4;
            const string cardNumber = "1111";
            const string txnNumber = "765";
            JournalTotalSalesItem total = new JournalTotalSalesItem
            {
                PumpNumber = pumpNumber,
                Hose = hose,
                CardNumber = cardNumber,
                CardProductName = CardProductName1
            };
            IList<JournalFuelSalesItem> fuelSales = new List<JournalFuelSalesItem>
            {
                new JournalFuelSalesItem {Grade = fuelGrade, GradeName = fuelGradeName, Quantity = fuelQuantity, Amount = fuelAmount}
            };
            IList<JournalCarWashSalesItem> carWashSales = new List<JournalCarWashSalesItem>
            {
                new JournalCarWashSalesItem
                {
                    ProgramId = programId,
                    WashName = washName,
                    Quantity = washQuantity,
                    Amount = washAmount,
                    Category = washCategory,
                    Subcategory = washSubCategory
                }
            };
            IList<JournalOtherSalesItem> otherSales = new List<JournalOtherSalesItem>
            {
                new JournalOtherSalesItem {ProductCode = otherProductCode, Quantity = otherQuantity, Amount = otherAmount}
            };

            // Act
            _journalWorker.WriteSalesItems(total, fuelSales, carWashSales, otherSales, null, null, txnNumber, out int transactionNumber);

            // Assert
            transactionNumber.Should().Be(SalesTransaction);
            _transactionGradeNames.Should().HaveCount(3);
            _transactionGradeNames.Should().ContainInOrder(fuelGradeName, washName, "");
            _transactionCodes.Should().HaveCount(3);
            _transactionCodes.Should().ContainInOrder($"FUEL{fuelGrade}", $"CARWASH{programId}", $"UNKNOWN{otherProductCode}");
            _transactionQuantities.Should().HaveCount(3);
            _transactionQuantities.Should().ContainInOrder(fuelQuantity, washQuantity, otherQuantity);
            _transactionAmounts.Should().HaveCount(3);
            _transactionAmounts.Should().ContainInOrder(fuelAmount, washAmount, otherAmount);
            _transactionPumpDetails.Should().HaveCount(3);
            _transactionPumpDetails.Should().ContainInOrder($"PUMP {pumpNumber}:{hose}", "", "");
            _transactionCardNumbers.Should().HaveCount(3);
            _transactionCardNumbers.Should().ContainInOrder(cardNumber, cardNumber, cardNumber);
            _transactionTimes.Should().HaveCount(3);
            _transactionCategories.Should().HaveCount(3);
            _transactionCategories.Should().ContainInOrder($"{FuelCategory}", $"{washCategory}", "0");
            _transactionSubCategories.Should().HaveCount(3);
            _transactionSubCategories.Should().ContainInOrder($"{fuelGrade}", $"{washSubCategory}", otherProductCode);
            _transactionsAddedTo.Should().HaveCount(2);
            _transactionsAddedTo.Should().ContainInOrder(SalesTransaction, SalesTransaction);
            _dayEndFuelAmounts.Should().HaveCount(3);
            _dayEndFuelAmounts.Should().ContainInOrder(fuelAmount, (uint)0, (uint)0);
            _dayEndDryAmounts.Should().HaveCount(3);
            _dayEndDryAmounts.Should().ContainInOrder((uint)0, washAmount, otherAmount);
            _dayEndQuantities.Should().HaveCount(3);
            _dayEndQuantities.Should().ContainInOrder(fuelQuantity, washQuantity, otherQuantity);
            _dayEndTransactionNumbers.Should().HaveCount(3);
            _dayEndTransactionNumbers.Should().ContainInOrder(SalesTransaction, SalesTransaction, SalesTransaction);
            _dayEndCategories.Should().HaveCount(3);
            _dayEndCategories.Should().ContainInOrder(FuelCategory, washCategory, (short)0);
            _dayEndSubCategories.Should().HaveCount(3);
            _dayEndSubCategories.Should().ContainInOrder((short)fuelGrade, washSubCategory, (short)0);
            _dayEndGradeCodes.Should().HaveCount(3);
            _dayEndGradeCodes.Should().ContainInOrder($"FUEL{fuelGrade}", $"CARWASH{programId}", $"UNKNOWN{otherProductCode}");
            _dayEndGradeNames.Should().HaveCount(3);
            _dayEndGradeNames.Should().ContainInOrder(fuelGradeName, washName, "");
            _dayEndCardProductNames.Should().HaveCount(3);
            _dayEndCardProductNames.Should().ContainInOrder(CardProductName1, CardProductName1, CardProductName1);
            _transactionTxnNumbers.Should().HaveCount(1);
            _transactionTxnNumbers.Should().Contain(txnNumber);
            _transactionsFileItems.Should().HaveCount(5);
            foreach (TransactionFileItem item in _transactionsFileItems)
            {
                item.Till.Should().Be($"{TillNumber:D2}");
                item.RecType.Should().BeOneOf(SalesItemRecType, MethodOfPaymentRecType, EndOfSalesRecType);
                item.Receipt.Should().Be($"{SalesTransaction}");
                item.Opid.Should().Be(OpId);
                item.Name.Should().BeOneOf(fuelGradeName, washName, "");
                item.Code.Should().BeOneOf($"FUEL{fuelGrade}", $"CARWASH{programId}", $"UNKNOWN{otherProductCode}", "");
                item.Qty.Should().BeOneOf($"{fuelQuantity / 1000.0:F2}", $"{washQuantity:F2}", $"{otherQuantity:F2}", CurrencyZero);
                item.Value.Should().BeOneOf($"{fuelAmount / 100.0:F2}", $"{washAmount / 100.0:F2}", $"{otherAmount / 100.0:F2}",
                    CurrencyZero);
                item.CardNum.Should().BeOneOf($"PUMP {pumpNumber}:{hose}", cardNumber, "");
                item.Cat.Should().BeOneOf($"{FuelCategory}", $"{washCategory}", "0", "");
                item.SubCat.Should().BeOneOf($"{fuelGrade}", $"{washSubCategory}", otherProductCode, "");
            }
        }
    }
}
