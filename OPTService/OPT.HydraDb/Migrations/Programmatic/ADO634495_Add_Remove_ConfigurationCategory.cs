using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc cref="Migrationable"/>
    [Migration(20050007, "ADO#634495 - Add stored procedures for adding/removing ConfigurationCategories"), Tags(Constants.MigrationTypeSchema)]
    public class ADO634495_Add_Remove_ConfigurationCategory : MigrationableProgrammibility
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">Migration logger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        public ADO634495_Add_Remove_ConfigurationCategory(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory, IFileSystem fileSystem) :
            base(null, new List<string> { "dbo.AddConfigurationCategory", "dbo.RemoveConfigurationCategory" }, logger, dbExecutorFactory, fileSystem)
        { }
    }
}
