using System.Collections.Specialized;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.TransactionValidator.Common;

namespace OPT.TransactionValidator.Models
{
    /// <summary>
    /// Configuration model for validation response actions
    /// </summary>
    public class ValidatorResponseActions : Loggable
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        public ValidatorResponseActions(IHtecLogManager logMan, IConfigurationManager configurationManager) : base(logMan, "PumpTransactionValidator", configurationManager)
        {
        }

        /// <summary>
        /// Should the pump be stopped.
        /// </summary>
        public bool IsPumpStopped =>
            ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyTransactionQualityBlockPump, Constants.DefaultValueTransactionQualityBlockPump, LoggerIConfigurationManager);

        /// <summary>
        /// Should the OPT be stopped.
        /// </summary>
        public bool IsOptStopped =>
            ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyTransactionQualityBlockOpt, Constants.DefaultValueTransactionQualityBlockOpt, LoggerIConfigurationManager);

        /// <summary>
        /// Should the transaction be cancelled as zero.
        /// </summary>
        public bool IsTransactionCancelled => ConfigurationManager.AppSettings.GetAppSettingOrDefault(Constants.ConfigKeyTransactionQualityClearTransaction,
            Constants.DefaultValueTransactionQualityClearTransaction, LoggerIConfigurationManager);
    }
}
