using System;
using System.Collections.Generic;
using System.Net;

namespace OPT.Common.Workers.Interfaces
{
    [Obsolete("Use Htec.Foundation.Connections.Workers.Interfaces.IListenerWorker{T} instead!")]
    public interface IListenerWorker
    {
        int ConnectedCount { get; }

        /// <summary>Register OPT Worker with Listener Worker.</summary>
        /// <param name="worker">OPT Worker to register.</param>
        void RegisterWorker(IFromOptWorker worker);

        /// <summary>Register Controller Worker with Listener Worker.</summary>
        /// <param name="worker">Controller Worker to register.</param>
        void RegisterWorker(IControllerWorker worker);

        /// <summary>Start the Listener Worker.</summary>
        void Start(string hydraId);

        /// <summary>Stop the Listener Worker.</summary>
        void Stop();

        /// <summary>Inform the Listener Worker that a client has connected.</summary>
        /// <param name="sourceAddress"></param>
        void OnConnected(IPAddress sourceAddress = null);

        /// <summary>Inform the Listener Worker that a client has disconnected.</summary>
        void OnDisconnected();

        /// <summary>Fetch the Listener end point and reconnect if necessary.</summary>
        void ReloadListener();

        /// <summary>Called when plain text message received from an unknown client.</summary>
        /// <param name="message">Message received.</param>
        /// <param name="id">Id to be populated when known.</param>
        /// <returns>Response to send back to client.</returns>
        string OnTextFromUnknownClient(string message, out int id);

        /// <summary>Called when plain text message received from a known client.</summary>
        /// <param name="message">Message received.</param>
        /// <param name="id">Id received from.</param>
        /// <returns>Response to send back to client.</returns>
        string OnTextFromKnownClient(string message, int id);

        IEnumerable<IPAddress> GetAllIpAddresses();
    }
}