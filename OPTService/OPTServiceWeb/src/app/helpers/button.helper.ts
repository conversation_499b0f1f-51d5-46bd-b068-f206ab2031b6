export class ButtonHelper {

  public static clicked(event: Event): void {
    const elem = ButtonHelper.getButton(event);
    if (elem) {
      elem.setAttribute('disabled', 'true');
      elem.classList.add('button-clicked');
    }
  }

  public static reset(event: Event): void {
    const elem = ButtonHelper.getButton(event);
    if (elem) {
      elem.removeAttribute('disabled');
      elem.classList.remove('button-clicked');
    }
  }

  private static getButton(event: Event): Element | undefined {
    const elem = event?.target as Element;
    if (event?.type === 'submit') {
      const btn = elem?.querySelector('button[type="submit"]');
      return btn || elem;
    }
    return elem;
  }
}