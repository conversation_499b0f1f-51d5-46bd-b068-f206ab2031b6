using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(20050006, "ADO#646655 - Add secondary index to Transaction (for ids), and dbo.GetTransaction stored procedure, i.e. FindById"), Tags(Constants.MigrationTypeProgrammatic)]
    public class ADO646655_GetTransaction : Migrationable
    {
        private const string TableName = "Transactions";
        private const string IndexName = "ix_transactionids";

        /// <inheritdoc/>
        public ADO646655_GetTransaction(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
            DbObjects = new List<string>() { $"dbo.GetTransaction" };
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var result = BackupDbObjects(nameof(ADO646655_GetTransaction), DbObjects.ToArray() );
            if (!result.IsSuccess)
            {
                throw result.Error;
            }

            foreach (var dbObject in DbObjects)
            {
                Execute.EmbeddedScript($"{EmbeddedResourcePathStoredProcedures}.{dbObject}{Constants.FileExtensionSql}");
            }

            if (!Schema.Table(TableName).Index(IndexName).Exists())
            { 
                Create.Index(IndexName)
                    .OnTable(TableName)
                    .OnColumn("rowid").Ascending()
                    .OnColumn("TxnNumber").Ascending()
                    .WithOptions()
                    .NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            if (Schema.Table(TableName).Index(IndexName).Exists())
            {
                Delete.Index(IndexName).OnTable(TableName);
            }

            RestoreDbObjects(nameof(ADO646655_GetTransaction), DbObjects.Reverse().ToArray());
        }
    }
}
