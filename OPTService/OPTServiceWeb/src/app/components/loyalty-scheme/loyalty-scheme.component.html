<form [formGroup]="form">
    <app-switch-label id="present" labelText="Present" controlName="Present" [errorInAction]="presentError"
        (action)="updatePresent($event)" [formGroup]="form"></app-switch-label>
<ngb-accordion [closeOthers]="true" [activeIds]="activeSection" (panelChange)="toogleSection($event)">
    <ngb-panel id="loyalty-terminal">
        <ng-template ngbPanelHeader>
            <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                    <span>Terminal</span>
                </button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <app-label-text-button id="siteId" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Site ID"
                controlName="SiteId" [errorInAction]="siteIdError" (action)="updateSiteId($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="terminalId" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Terminal ID"
                controlName="TerminalId" [errorInAction]="terminalIdError" (action)="updateTerminalId($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="footer1" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Footer 1"
                controlName="Footer1" [errorInAction]="footer1Error" (action)="updateFooter1($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="footer2" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Footer 2"
                controlName="Footer2" [errorInAction]="footer2Error" (action)="updateFooter2($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="timeout" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="Timeout"
                controlName="Timeout" [errorInAction]="timeoutError" (action)="updateTimeout($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="apiKey" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="API key"
                controlName="ApiKey" [errorInAction]="apiKeyError" (action)="updateApiKey($event)"
                [formGroup]="form"></app-label-text-button>
            <app-label-text-button id="httpHeader" labelColClass="col-12 col-md-2 col-xl-2"
                textColClass="col-12 col-md-6 col-xl-3" labelText="HTTP Header"
                controlName="HttpHeader" [errorInAction]="httpHeaderError" (action)="updateHttpHeader($event)"
                [formGroup]="form"></app-label-text-button>
        </ng-template>
    </ngb-panel>
    <ngb-panel formArrayName="Hosts" id="loyalty-hosts" (panelChange)="toogleSection($event)">
        <ng-template ngbPanelHeader>
            <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                    <span>Hosts</span>
                </button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="row form-group">
                <button id="addHostBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddHostForm(addHost)"><span
                        class="btn-label"><i class="bi bi-plus"></i></span>Add host</button>
            </div>
            <div *ngFor="let item of hosts.controls; let i = index">
                <div [formGroupName]="i" class="form-group row">
                    <div class="col-1 input-group">
                        <label for="hostFullAddress{{i}}">Address</label>
                    </div>                    
                    <div class="input-group col-3">
                        <input id="hostFullAddress{{i}}" type="text" formControlName="FullAddress" class="form-control" readonly>
                    </div>
                    <div class="col-auto">
                        <button id="removeHostBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeHost(i,$event)"><span
                                class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                        <span id="removeHostError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                            *ngIf="item.get('RemoveError').value"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
    <ngb-panel formArrayName="Hostnames" id="loyalty-hostnames" (panelChange)="toogleSection($event)">
        <ng-template ngbPanelHeader>
            <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                    <span>Hostnames</span>
                </button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="row form-group">
                <button id="addHostnameBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddHostnameForm(addHostname)"><span
                        class="btn-label"><i class="bi bi-plus"></i></span>Add hostname</button>
            </div>
            <div *ngFor="let item of hostnames.controls; let i = index">
                <div [formGroupName]="i" class="form-group row">
                    <div class="col-1 input-group">
                        <label for="hostName{{i}}">Hostname</label>
                    </div>                    
                    <div class="input-group col-3">
                        <input id="hostName{{i}}" type="text" formControlName="Hostname" class="form-control" readonly>
                    </div>
                    <div class="col-auto">
                        <button id="removeHostnameBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeHostname(i,$event)"><span
                                class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                        <span id="removeHostnameError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                            *ngIf="item.get('RemoveError').value"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
    <ngb-panel formArrayName="Iins" id="loyalty-iins" (panelChange)="toogleSection($event)">
        <ng-template ngbPanelHeader>
            <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                    <span>IINs</span>
                </button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="row form-group">
                <button id="addIinBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddIinForm(addIin)"><span
                        class="btn-label"><i class="bi bi-plus"></i></span>Add IIN</button>
            </div>
            <div *ngFor="let item of iins.controls; let i = index">
                <div [formGroupName]="i" class="form-group row">             
                    <div class="input-group col-3">
                        <input type="text" formControlName="FullIin" class="form-control" readonly>
                    </div>
                    <div class="col-auto">
                        <button id="removeIinBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeIin(i,$event)"><span
                                class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                        <span id="removeIinError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                            *ngIf="item.get('RemoveError').value"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
    <ngb-panel formArrayName="LoyaltyMappings" id="loyalty-mappings" (panelChange)="toogleSection($event)">
        <ng-template ngbPanelHeader>
            <div class="d-flex align-items-center justify-content-between">
                <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                    <span>Loyalty mappings</span>
                </button>
            </div>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="row form-group">
                <button id="addLoyaltyMapBtn" type="button" class="btn btn-success m-auto btn-labeled" (click)="openAddLoyaltyMapForm(addLoyaltyMap)"><span
                        class="btn-label"><i class="bi bi-plus"></i></span>Add Loyalty Map</button>
            </div>
            <div *ngFor="let item of loyaltyMap.controls; let i = index">
                <div [formGroupName]="i" class="form-group row">
                    <div class="input-group col-2">
                        <input type="text" formControlName="FullProductCode" class="form-control" readonly>
                    </div>
                    <span><i class="bi bi-arrow-right" style="font-size:1.5rem; line-height:1.5rem;"></i></span>
                    <div class="input-group col-2">
                        <input type="text" formControlName="FullLoyaltyCode" class="form-control" readonly>
                    </div>                    
                    <div class="col-auto">
                        <button id="removeMapBtn{{i}}" type="button" class="btn btn-danger btn-labeled" (click)="removeMap(i,$event)"><span
                                class="btn-label"><i class="bi bi-trash"></i></span>Remove</button>
                        <span id="removeMapError{{i}}" class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                            *ngIf="item.get('RemoveError').value"><i class="bi bi-exclamation-triangle"></i></span>
                    </div>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
</ngb-accordion>
</form>

<ng-template #addHost let-modal>
    <form id="addHostForm" [formGroup]="addHostForm" (ngSubmit)="onAddHostFormSubmit($event)">
        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Add host</h4>
            <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="ipAddress">IP Address</label>
                <input type="text" class="form-control" id="ipAddress" formControlName="IpAddress"
                    [ngClass]="{'is-invalid':addHostFormControl.IpAddress.invalid && (addHostFormControl.IpAddress.dirty || addHostFormControl.IpAddress.touched)}"
                    required aria-describedby="ipAddressFeedback" ngbAutofocus>
                <div id="ipAddressFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addHostFormControl.IpAddress"></app-input-error-messages>
                </div>
            </div>
            <div class="form-group">
                <label for="port">Port</label>
                <input type="text" class="form-control" id="port" formControlName="Port"
                    [ngClass]="{'is-invalid':addHostFormControl.Port.invalid && (addHostFormControl.Port.dirty || addHostFormControl.Port.touched)}"
                    required aria-describedby="portFeedback" ngbAutofocus>
                <div id="portFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addHostFormControl.Port"></app-input-error-messages>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" class="btn btn-primary" [disabled]="addHostForm.invalid">Save</button>
            <span class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                *ngIf="addHostError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </form>
</ng-template>

<ng-template #addHostname let-modal>
    <form id="addHostnameForm" [formGroup]="addHostnameForm" (ngSubmit)="onAddHostnameFormSubmit($event)">
        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Add hostname</h4>
            <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="hostname">Hostname</label>
                <input type="text" class="form-control" id="hostname" formControlName="Hostname"
                    [ngClass]="{'is-invalid':addHostnameFormControl.Hostname.invalid && (addHostnameFormControl.Hostname.dirty || addHostnameFormControl.Hostname.touched)}"
                    required aria-describedby="hostnameFeedback" ngbAutofocus>
                <div id="hostnameFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addHostnameFormControl.Hostname"></app-input-error-messages>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" class="btn btn-primary" [disabled]="addHostnameForm.invalid">Save</button>
            <span class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                *ngIf="addHostnameError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </form>
</ng-template>

<ng-template #addIin let-modal>
    <form id="addIinForm" [formGroup]="addIinForm" (ngSubmit)="onAddIinFormSubmit($event)">
        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Add IIN</h4>
            <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="low">From</label>
                <input type="text" class="form-control" id="low" formControlName="Low"
                    [ngClass]="{'is-invalid':addIinFormControl.Low.invalid && (addIinFormControl.Low.dirty || addIinFormControl.Low.touched)}"
                    required aria-describedby="lowFeedback" ngbAutofocus>
                <div id="lowFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addIinFormControl.Low"></app-input-error-messages>
                </div>
            </div>
            <div class="form-group">
                <label for="high">To</label>
                <input type="text" class="form-control" id="high" formControlName="High"
                    [ngClass]="{'is-invalid':addIinFormControl.High.invalid && (addIinFormControl.High.dirty || addIinFormControl.High.touched)}"
                    required aria-describedby="highFeedback" ngbAutofocus>
                <div id="highFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addIinFormControl.High"></app-input-error-messages>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" class="btn btn-primary" [disabled]="addIinForm.invalid">Save</button>
            <span class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                *ngIf="addIinError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </form>
</ng-template>

<ng-template #addLoyaltyMap let-modal>
    <form id="addLoyaltyMapForm" [formGroup]="addLoyaltyMapForm" (ngSubmit)="onAddLoyaltyMapFormSubmit($event)">
        <div class="modal-header">
            <h4 class="modal-title" id="modal-basic-title">Add loyalty map</h4>
            <button type="button" class="close" aria-label="close" (click)="modal.dismiss()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="productCode">Product code</label>
                <input type="text" class="form-control" id="productCode" formControlName="ProductCode"
                    [ngClass]="{'is-invalid':addLoyaltyMapFormControl.ProductCode.invalid && (addLoyaltyMapFormControl.ProductCode.dirty || addLoyaltyMapFormControl.ProductCode.touched)}"
                    required aria-describedby="productCodeFeedback" ngbAutofocus>
                <div id="productCodeFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addLoyaltyMapFormControl.ProductCode"></app-input-error-messages>
                </div>
            </div>
            <div class="form-group">
                <label for="loyaltyCode">Loyalty code</label>
                <input type="text" class="form-control" id="loyaltyCode" formControlName="LoyaltyCode"
                    [ngClass]="{'is-invalid':addLoyaltyMapFormControl.LoyaltyCode.invalid && (addLoyaltyMapFormControl.LoyaltyCode.dirty || addLoyaltyMapFormControl.LoyaltyCode.touched)}"
                    required aria-describedby="loyaltyCodeFeedback" ngbAutofocus>
                <div id="loyaltyCodeFeedback" class="invalid-feedback">
                    <app-input-error-messages [control]="addLoyaltyMapFormControl.LoyaltyCode"></app-input-error-messages>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" class="btn btn-primary" [disabled]="addLoyaltyMapForm.invalid">Save</button>
            <span class="text-danger ml-2" placement="right" ngbTooltip="Field couldn't be updated"
                *ngIf="addLoyaltyMapError"><i class="bi bi-exclamation-triangle"></i></span>
        </div>
    </form>
</ng-template>