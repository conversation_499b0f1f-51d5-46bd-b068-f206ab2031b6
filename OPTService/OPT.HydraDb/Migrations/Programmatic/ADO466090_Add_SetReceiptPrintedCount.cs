using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc cref="Migrationable"/>
    [Migration(20040003, "ADO#466090 - Add dbo.SetReceiptPrintedCount stored procedure"), Tags(Constants.MigrationTypeSchema)]
    public class ADO466090_Add_SetReceiptPrintedCount : MigrationableProgrammibility
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">Migration logger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        public ADO466090_Add_SetReceiptPrintedCount(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory, IFileSystem fileSystem) :
            base(null, new List<string> { "dbo.SetReceiptPrintedCount" }, logger, dbExecutorFactory, fileSystem)
        { }
    }
}
