import { CardReferenceDetails } from "./cardReferenceDetails.model";
import { ConfigurationCategory } from "./configurationCategory.model";
import { ESocketPosConfigDetails } from "./eSocketPosConfigDetails.model";
import { SettingDetails } from "./settingDetails.model";

export class AdvancedConfig {
    AutoAuth: boolean = false;
    MediaChannel: boolean = false;
    UnmannedPseudoPos: boolean = false;
    AsdaDayEndReport: boolean = false;
    MorrisonLoyaltyAvailable: boolean = false;
    LoyaltyAvailable: Array<string> = [];
    PaymentTimeoutOpt: number = 0;
    PaymentTimeoutPod: number = 0;
    PaymentTimeoutMixed: number = 0;
    PaymentTimeoutNozzleDown: number = 0;
    TimeoutKiosk: number = 0;
    TimeoutSecAuth: number = 0;
    ReceiptTimeout: number = 0;
    ReceiptMaxCount: number = 0;
    TillNumber: number = 0;
    FuelCategory: number = 0;
    ForwardFuelPriceUpdate: boolean = false;
    FuellingIndefiniteWait: boolean = false;
    FuellingWaitMinutes: number = 0;
    FuellingBackoffAuth: number = 0;
    FuellingBackoffPreAuth: number = 0;
    FuellingBackoffStopStart: number = 0;
    FuellingBackoffStopOnly: number = 0;
    PosClaimNumber: number = 0;
    FilePruneDays: number = 0;
    TransactionPruneDays: number = 0;
    ReceiptPruneDays: number = 0;
    NozzleUpForKioskUse: boolean = false;
    UseReplaceNozzleScreen: boolean = false;
    MaxFillOverride: number = 0;
    SiteType: string = '';
    SiteName: string = '';
    VatNumber: string = '';
    CurrencyCode: number = 0;
    LocalAccountsEnabled: boolean = false;
    CardReferences: Array<CardReferenceDetails> = [];
    ConfigurationCategories: Array<ConfigurationCategory> = [];
    ESocketPosConfig: ESocketPosConfigDetails = {} as ESocketPosConfigDetails;
    PosType: string = '';
    PosTypes: Array<SettingDetails> = [];
    PumpType: string = '';
    PumpTypes: Array<SettingDetails> = [];
    MobilePaymentType: string = '';
    MobilePaymentTypes: Array<SettingDetails> = [];
    MobilePosType: string = '';
    MobilePosTypes: Array<SettingDetails> = [];
    BosType: string = '';
    BosTypes: Array<SettingDetails> = [];
    SecAuthType: string = '';
    SecAuthTypes: Array<SettingDetails> = [];
    PaymentConfigType: string = '';
    PaymentConfigTypes: Array<SettingDetails> = [];
}
