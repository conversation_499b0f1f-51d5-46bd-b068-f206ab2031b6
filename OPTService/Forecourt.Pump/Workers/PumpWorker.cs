using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Models;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Workers;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces.Core;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using OPT.Common;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.Workers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Timers;
using CommState = Htec.Hydra.Core.Pump.Common.CommunicationState;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using CoreMeterReadings = Htec.Hydra.Core.Pump.Messages.MeterReadings;
using CorePumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using ITankGaugeIntegratorInJournal = Htec.Hydra.Core.Pump.Interfaces.ITankGaugeIntegratorInJournal;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;
using PumpMeterInfo = Forecourt.Pump.Workers.Models.PumpMeterInfo;
using PumpState = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpState;

namespace Forecourt.Pump.Workers
{
    /// <summary>
    /// Worker that deals with the Pump Controller
    /// </summary>
    public class PumpWorker : HydraDbable<HydraDb.Interfaces.IHydraDb>, IPumpWorker
    {
        /// <summary>
        /// Constant used to convert currency values from tenth pence to pence
        /// </summary>
        public const byte CurrencyFactor = HscPumpData.CurrencyFactor;

        /// <summary>
        /// Constant used to convert volume values from ml to l
        /// </summary>
        public const int VolumeFactor = HscPumpData.VolumeFactor;

        private const string HeaderFailed = "FAILED!";

        /// <summary>
        /// Default value, for the Transaction Sequence Number
        /// </summary>
        public const int DefaultValueTransSeqNum = -1;

        /// <inheritdoc/>
        public string LogHeader => PumpController?.LogHeader ?? "PumpController";

        public class PumpInfo
        {
            public byte Number { get; set; }
            public bool PendingTxnOnRestart { get; set; }
        }

        public class HoseDelivery : HoseTotals
        {
            public bool IsPaid { get; set; }
            public byte Grade { get; set; }
            public IEnumerable<byte> AllGrades { get; set; }
            public DateTime TimeStamp { get; set; }
        }

        public const string HeaderOnPumpState = ConfigConstants.HeaderOnPumpState;

        private const int SecondsToWaitForPumpData = 2;
        private const int SecondsToWaitForPumpClaim = 2;
        private const int SecondsToWaitForDelivering = 10;
        private readonly string NotImplemented = $"{typeof(PumpWorker)} does not implement some {typeof(IPumpIntegratorIn<IMessageTracking>)} members!";

        private const string _categoryNamePump = Forecourt.Core.Configuration.Constants.CategoryNamePump + Forecourt.Core.Configuration.Constants.CategorySeparator;

        /// <summary>
        /// Wait time for pump in initialise state.
        /// </summary>
        public const string ConfigKeyInitialiseWaitTime = _categoryNamePump + "FDC:Initialise:WaitTime";

        /// <summary>
        /// Default value for wait time for pump in initialise state.
        /// </summary>
        public const string DefaultValueInitialiseWaitTime = "00:00:30";

        private int SecondsToWaitInInitialise => (int)ConfigValueWaitInInitialiseInterval.GetValue().TotalSeconds;

        private ConfigurableTimeSpan ConfigValueWaitInInitialiseInterval { get; }

        /// <summary>
        /// Wait interval for pump in initialise state.
        /// </summary>
        public const string ConfigKeyInitialiseWaitInterval = _categoryNamePump + "FDC:Initialise:WaitInterval";

        /// <summary>
        /// Default value for wait interval for pump in initialise state.
        /// </summary>
        public const string DefaultValueInitialiseWaitInterval = "00:00:05";

        private int SecondsInitialiseInterval => (int)ConfigValueSecondsInitialiseInterval.GetValue().TotalSeconds;

        private ConfigurableTimeSpan ConfigValueSecondsInitialiseInterval { get; }

        /// <summary>
        /// Config Key for, the maximum number of attempts to receive an Unpaid POS Claimed Idle
        /// </summary>
        public const string ConfigKeyIsZeroPaidMaximumCashOutAttempts = _categoryNamePump + "Pump:CashOut:IsZeroPaid:MaxAttempts";

        /// <summary>
        /// Default value for, the maximum number of attempts to receive an Unpaid POS Claimed Idle
        /// </summary>
        public const int DefaultValueIsZeroPaidMaximumCashOutAttempts = 10;

        /// <summary>
        /// Configurable value for, the maximum number of attempts to receive an Unpaid POS Claimed Idle
        /// </summary>
        public ConfigurableInt ConfigValueIsZeroPaidMaximumCashOutAttempts { get; }

        /// <summary>
        /// Config Key for, the interval to check whether Transaction CashedOut States can be cleared 
        /// </summary>
        public const string ConfigKeyTransactionCashedOutStateIntervalCheck = _categoryNamePump + "Pump:CashOut:Transaction:States:Interval:Check";

        /// <summary>
        /// Default value for, the interval to check whether Transaction CashedOut States can be cleared 
        /// </summary>
        public const string DefaultValueTransactionCashedOutStateIntervalCheck = "00:01:00";

        /// <summary>
        /// Configurable value for, the interval to check whether Transaction CashedOut States can be cleared 
        /// </summary>
        public ConfigurableTimeSpan ConfigValueTransactionCashedOutStateIntervalCheck { get; }

        /// <summary>
        /// Config Key for, the current cash threshold to throttle log entries by
        /// </summary>
        public const string ConfigKeyLogDeliveringCashThreshold = ConfigConstants.ConfigCategoryLogging + "Pump:State:Delivering:Threshold:Cash";

        /// <summary>
        /// Default value for, the current cash threshold to throttle log entries by
        /// </summary>
        public const int DefaultValueLogDeliveringCashThreshold = 400;

        /// <summary>
        /// Configurable value for, the current cash threshold to throttle log entries by
        /// </summary>
        public ConfigurableUInt ConfigValueLogDeliveringCashThreshold { get; }

        /// <summary>
        /// Config Key for, the current cash threshold to throttle log entries by
        /// </summary>
        public const string ConfigKeyLogDeliveringVolumeThreshold = ConfigConstants.ConfigCategoryLogging + "Pump:State:Delivering:Threshold:Volume";

        /// <summary>
        /// Default value for, the current cash threshold to throttle log entries by
        /// </summary>
        public const int DefaultValueLogDeliveringVolumeThreshold = 2000;

        /// <summary>
        /// Configurable value for, the current volume threshold to throttle log entries by
        /// </summary>
        public ConfigurableUInt ConfigValueLogDeliveringVolumeThreshold { get; }

        /// <summary>
        /// Configurable value for, interval to wait for PumpMeter information
        /// </summary>
        protected ConfigurableTimeSpan PumpMetersWaitForInterval { get; private set; }

        /// <summary>
        /// Configurable value for, interval to wait for PumpMeter information
        /// </summary>
        protected ConfigurableInt PumpMetersWaitForAttempts { get; private set; }

        /// <summary>
        /// Config key for, Level-1 check for Stuck Sales
        /// </summary>
        public const string ConfigKeyStuckSalesCheckLevel1 = _categoryNamePump + "StuckSalesCheck:Level1:Enabled";

        /// <summary>
        /// Default value for, Level-1 check for Stuck Sales
        /// </summary>
        public const bool DefaultValueStuckSalesCheckLevel1 = false;

        /// <summary>
        /// Configurable value for, Level-1 check for Stuck Sales, i.e. check internal state for discepencies
        /// </summary>
        protected ConfigurableBool ConfigValueStuckSalesCheckLevel1 { get; private set; }

        /// <summary>
        /// Config key for, Level-2 check for Stuck Sales
        /// </summary>
        public const string ConfigKeyStuckSalesCheckLevel2 = _categoryNamePump + "StuckSalesCheck:Level2:Enabled";

        /// <summary>
        /// Default value for, Level-2 check for Stuck Sales
        /// </summary>
        public const bool DefaultValueStuckSalesCheckLevel2 = false;

        /// <summary>
        /// Configurable value for, Level-2 check for Stuck Sales, i.e. check PumpController state for discepencies
        /// </summary>
        protected ConfigurableBool ConfigValueStuckSalesCheckLevel2 { get; private set; }

        /// Default value for, whether Price Changes are automatically applied
        /// </summary>
        public const bool DefaultValueAutoApplyPriceChanges = true;

        /// <summary>
        /// Configurable value for, whether Price Changes are automatically applied
        /// </summary>
        public ConfigurableBool ConfigValueAutoApplyPriceChanges { get; }

        /// <summary>
        /// Config Key for, whether Price Changes are automatically applied
        /// </summary>
        public const string ConfigKeyAutoApplyPriceChanges = _categoryNamePump + "Pump:PriceChanges:AutoApply";

        /// <summary>
        /// Config Key for, pump Heartbeat interval
        /// </summary>
        public const string ConfigKeyPumpHeartbeatInterval = ConfigurationConstants.CategoryNamePump + ConfigurationConstants.CategorySeparator + "Interval:Heartbeat";

        /// <summary>
        /// Default value for, pump Heartbeat interval
        /// </summary>
        public const string DefaultValuePumpHeartbeatInterval = "00:00:30";

        /// <summary>
        /// Configurable value for, pump Heartbeat interval
        /// </summary>
        protected ConfigurableTimeSpan ConfigValuePumpHeartbeatInterval { get; set; }

        /// <inheritdoc/>
        public TimeSpan HeartbeatInterval => ConfigValuePumpHeartbeatInterval.GetValue();

        /// <summary>
        /// Config Key for, pump Heartbeat Timeout
        /// </summary>
        public const string ConfigKeyPumpHeartbeatTimeout = ConfigurationConstants.CategoryNamePump + ConfigurationConstants.CategorySeparator + "Interval:Heartbeat:Timeout";

        /// <summary>
        /// Default value for, pump Heartbeat Timeout
        /// </summary>
        public const string DefaultValuePumpHeartbeatTimeout = "00:10:00";

        /// <summary>
        /// Configurable value for, pump Heartbeat Timeout
        /// </summary>
        protected ConfigurableTimeSpan ConfigValuePumpHeartbeatTimeout { get; set; }

        /// <inheritdoc/>
        public TimeSpan HeartbeatTimeout => ConfigValuePumpHeartbeatTimeout.GetValue();

        /// <inheritdoc/>
        public TimeSpan TransactionCashedOutStateCheckInterval => ConfigValueTransactionCashedOutStateIntervalCheck.GetValue();

        /// <summary>
        /// Config Key for, when to log the Out of Sequence Authorise Check failures
        /// </summary>
        public const string ConfigKeyPumpOutOfSequenceAuthoriseCheckThresholdLogAfter = _categoryNamePump + "Pump:OutOfSequenceCheck:Authorise:Threshold:LogAfter";

        /// <summary>
        /// Default value for, when to log the Out of Sequence Authorise Check failures
        /// </summary>
        public const uint DefaultValuePumpOutOfSequenceAuthoriseCheckThresholdLogAfter = 6;

        /// <summary>
        /// Configurable value for, when to log the Out of Sequence Authorise Check failures
        /// </summary>
        public ConfigurableUInt ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdLogAfter { get; }

        /// <summary>
        /// Config Key for, how many Out of Sequence Authorise Check failures, before remedy
        /// </summary>
        public const string ConfigKeyPumpOutOfSequenceAuthoriseCheckThresholdMaximum = _categoryNamePump + "Pump:OutOfSequenceCheck:Authorise:Threshold:Maximum";

        /// <summary>
        /// Default value for, how many Out of Sequence Authorise Check failures, before remedy
        /// </summary>
        public const uint DefaultValuePumpOutOfSequenceAuthoriseCheckThresholdMaximum = 10;

        /// <summary>
        /// Configurable value for, how many Out of Sequence Authorise Check failures, before remedy
        /// </summary>
        public ConfigurableUInt ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdMaximum { get; }

        public bool IndefiniteWait { get; private set; }
        public int WaitMinutes { get; private set; }
        public int BackoffAuth { get; private set; }
        public int BackoffPreAuth { get; private set; }
        public int BackoffStopStart { get; private set; }
        public int BackoffStopOnly { get; private set; }

        public byte PosClaimNumber { get; private set; }

        private readonly IPumpCollection _allPumps;
        private readonly ICacheHelper _cacheHelper;
        private readonly IGradeHelper _gradeHelper;

        private IPumpIntegratorOutJournal<IMessageTracking> JournalWorker => GetWorker<IPumpIntegratorOutJournal<IMessageTracking>>();
        private IPumpIntegratorOutTransient<IMessageTracking> OptWorker => GetWorker<IPumpIntegratorOutTransient<IMessageTracking>>();
        private INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        private IPumpController PumpController;
        private readonly IPumpControllerFactory _pumpControllerFactory;

        private volatile bool _detect;
        private readonly ConcurrentDictionary<byte, (PumpState, DispenserState)> _pumpStates = new ConcurrentDictionary<byte, (PumpState, DispenserState)>();
        private readonly ConcurrentDictionary<byte, uint> _owingTran1 = new ConcurrentDictionary<byte, uint>();
        private readonly ConcurrentDictionary<byte, uint> _owingTran2 = new ConcurrentDictionary<byte, uint>();
        private readonly ConcurrentDictionary<byte, bool> _paidTran1 = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, bool> _paidTran2 = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, bool> _currentTran1 = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, bool> _currentTran2 = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, bool> _pumpPaid = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, bool> _pumpTwoTrans = new ConcurrentDictionary<byte, bool>();
        private readonly ConcurrentDictionary<byte, HoseTotals> _currentInfo = new ConcurrentDictionary<byte, HoseTotals>();

        private readonly ConcurrentDictionary<byte, HoseDelivery> _deliveringTotals = new ConcurrentDictionary<byte, HoseDelivery>();
        private readonly ConcurrentDictionary<byte, uint> _pumpPaidEarly = new ConcurrentDictionary<byte, uint>();

        private readonly ConcurrentDictionary<byte, uint> _pumpAuth = new ConcurrentDictionary<byte, uint>();
        private readonly ConcurrentDictionary<byte, uint> _pumpRestartAuth = new ConcurrentDictionary<byte, uint>();

        private readonly ConcurrentDictionary<byte, byte> _pumpTransToCashOut = new ConcurrentDictionary<byte, byte>();
        private readonly ConcurrentDictionary<byte, IDictionary<byte, IsZeroPaidInfo>> _pumpIsZeroPaid = new ConcurrentDictionary<byte, IDictionary<byte, IsZeroPaidInfo>>();
        private readonly ConcurrentDictionary<byte, byte> _pumpPosToClaim = new ConcurrentDictionary<byte, byte>();
        private readonly ConcurrentDictionary<byte, byte> _pumpTransToClaim = new ConcurrentDictionary<byte, byte>();

        private readonly ConcurrentDictionary<byte, DateTime> _deliveringTimeout = new ConcurrentDictionary<byte, DateTime>();
        private readonly ConcurrentDictionary<byte, DateTime> _pumpDataTimeout = new ConcurrentDictionary<byte, DateTime>();
        private readonly ConcurrentDictionary<byte, DateTime> _pumpClaimTimeout = new ConcurrentDictionary<byte, DateTime>();
        private readonly ConcurrentDictionary<byte, bool> _pumpIsBlocked = new ConcurrentDictionary<byte, bool>();
        private readonly SemaphoreLockManager<byte, OnPumpDataData> _pumpLocks;
        private readonly ConcurrentDictionary<byte, DateTime> _pumpInitializeTimeout = new ConcurrentDictionary<byte, DateTime>();
        private readonly ConcurrentDictionary<byte, DateTime> _pumpLastInitializeCheck = new ConcurrentDictionary<byte, DateTime>();

        private readonly ConcurrentDictionary<byte, PumpInfo> _pumpPendingTxnOnRestart = new ConcurrentDictionary<byte, PumpInfo>();
        private readonly ConcurrentDictionary<byte, PumpMeterInfo> _pumpMeters = new ConcurrentDictionary<byte, PumpMeterInfo>();

        // IDictionary<pump, IDictionary<grade, (price, hose)>> 
        //public IDictionary<byte, IDictionary<byte, (ushort, byte)>> GradePrices { get; } = new ConcurrentDictionary<byte, IDictionary<byte, (ushort, byte)>>();
        public IDictionary<byte, IDictionary<byte, ushort>> GradePrices { get; } = new ConcurrentDictionary<byte, IDictionary<byte, ushort>>();
        public DateTime LatestPriceChange { get; private set; } = DateTime.MinValue;
        public DateTime PendingPriceChange { get; private set; } = DateTime.MinValue;

        protected readonly Queueable<byte, CorePumpData> _pumpDataQueue;

        public Result EmergencyStopUpdate(byte pump, string reg, IMessageTracking message = default)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Warn, "Pump", () => new[] { $"{pump}" });
                ControllerWorker.SendInformation($"Emergency Stop Update, on Pump {pump}");

                PumpController.EmergencyStopUpdate(pump, reg, LoggingReference);
                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc/>
        public Result EmergencyStopCancel(byte pump, IMessageTracking message = default)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Warn, "Pump", () => new[] { $"{pump}" });
                ControllerWorker.SendInformation($"Emergency Stop Cancelled, on Pump {pump}");

                PumpController.EmergencyStopCancel(pump, LoggingReference);
                return Result.Success();
            }, message.FullId);

        }

        /// <inheritdoc/>
        public Result EmergencyStop(byte pump, string reg, IMessageTracking message = default)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Warn, "Pump", () => new[] { $"{pump}" });
                ControllerWorker.SendInformation($"Emergency Stop, on Pump {pump}");

                PumpController.EmergencyStop(pump, reg, LoggingReference);
                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc/>
        public Result RequestPrices(string loggingReference = null)
        {
            return DoAction(() =>
            {
                PumpController.RequestPrices(LoggingReference);
                return Result.Success();
            }, loggingReference);
        }

        private class IsZeroPaidInfo
        {
            public IsZeroPaidInfo(bool value = false, int count = 0)
            {
                Value = value;
                Count = count;
            }

            public bool Value { get; set; }
            public int Count { get; set; }
        }

        private class OnPumpDataData : LockData
        {
            public DateTime StartedWaiting => LockedAt;

            public DateTime FinishedLastEvent => UnlockedAt;

            public int IgnoredEventCount { get; set; }
        }

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="pumpControllerFactory">Site Controller Factory to use, to obtain <see cref="IPumpController"/> instance.</param>
        /// <param name="pumpOutJournal"><see cref="IPumpIntegratorOutJournal{IMessageTracking}"/> to use.</param>
        /// <param name="pumpOutTransient"><see cref="IPumpIntegratorOutTransient{IMessageTracking}"/> to use.</param>
        /// <param name="hydraDb">Hydra DB instance to use.</param>
        /// <param name="logMan">Htec log manager to use.</param>
        /// <param name="configurationManager">The configuration manager.</param>
        /// <param name="timerFactory">ITimerFactory instance</param>
        /// <param name="allPumps">All known pumps</param>
        /// <param name="gradeHelper">IGradeHelper instance</param>
        /// <param name="fileVersionInfoHelper">IFileVersionInfoHelper instance</param>
        /// <param name="cacheHelper">ICacheHelper to use.</param>
        /// <param name="posInModeChangeWorker">IPosIntegratorInMode<IMessageTracking> instance</param>
        /// <exception cref="ArgumentNullException">For any null mandatory parameters</exception>
        public PumpWorker(IPumpControllerFactory pumpControllerFactory, IPumpIntegratorOutJournal<IMessageTracking> pumpOutJournal, IPumpIntegratorOutTransient<IMessageTracking> pumpOutTransient,
            HydraDb.Interfaces.IHydraDb hydraDb, IHtecLogManager logMan, IConfigurationManager configurationManager, ITimerFactory timerFactory, IPumpCollection allPumps, IGradeHelper gradeHelper,
            IFileVersionInfoHelper fileVersionInfoHelper = null, ICacheHelper cacheHelper = null, IPosIntegratorInMode<IMessageTracking> posInModeChangeWorker = null)
            : base(hydraDb, logMan, nameof(PumpWorker), configurationManager, timerFactory: timerFactory)
        {
            _allPumps = allPumps;
            _gradeHelper = gradeHelper ?? throw new ArgumentNullException(nameof(gradeHelper));
            _cacheHelper = cacheHelper;
            _pumpDataQueue = new PumpDataQueue(logMan, configurationManager);
            _pumpLocks = new SemaphoreLockManager<byte, OnPumpDataData>(Logger, ShutdownToken);

            ConfigValueWaitInInitialiseInterval = new ConfigurableTimeSpan(this, ConfigKeyInitialiseWaitTime, DefaultValueInitialiseWaitTime);
            ConfigValueSecondsInitialiseInterval = new ConfigurableTimeSpan(this, ConfigKeyInitialiseWaitInterval, DefaultValueInitialiseWaitInterval);
            ConfigValueIsZeroPaidMaximumCashOutAttempts = new ConfigurableInt(this, ConfigKeyIsZeroPaidMaximumCashOutAttempts, DefaultValueIsZeroPaidMaximumCashOutAttempts);
            ConfigValueLogDeliveringCashThreshold = new ConfigurableUInt(this, ConfigKeyLogDeliveringCashThreshold, DefaultValueLogDeliveringCashThreshold);
            ConfigValueLogDeliveringVolumeThreshold = new ConfigurableUInt(this, ConfigKeyLogDeliveringVolumeThreshold, DefaultValueLogDeliveringVolumeThreshold);
            ConfigValueStuckSalesCheckLevel1 = new ConfigurableBool(this, ConfigKeyStuckSalesCheckLevel1, DefaultValueStuckSalesCheckLevel1);
            ConfigValueStuckSalesCheckLevel2 = new ConfigurableBool(this, ConfigKeyStuckSalesCheckLevel2, DefaultValueStuckSalesCheckLevel2);
            ConfigValueAutoApplyPriceChanges = new ConfigurableBool(this, ConfigKeyAutoApplyPriceChanges, DefaultValueAutoApplyPriceChanges);
            ConfigValuePumpHeartbeatInterval = new ConfigurableTimeSpan(this, ConfigKeyPumpHeartbeatInterval, DefaultValuePumpHeartbeatInterval);
            ConfigValuePumpHeartbeatTimeout = new ConfigurableTimeSpan(this, ConfigKeyPumpHeartbeatTimeout, DefaultValuePumpHeartbeatTimeout);
            ConfigValueTransactionCashedOutStateIntervalCheck = new ConfigurableTimeSpan(this, ConfigKeyTransactionCashedOutStateIntervalCheck, DefaultValueTransactionCashedOutStateIntervalCheck);
            ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdLogAfter = new ConfigurableUInt(this, ConfigKeyPumpOutOfSequenceAuthoriseCheckThresholdLogAfter, DefaultValuePumpOutOfSequenceAuthoriseCheckThresholdLogAfter);
            ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdMaximum = new ConfigurableUInt(this, ConfigKeyPumpOutOfSequenceAuthoriseCheckThresholdMaximum, DefaultValuePumpOutOfSequenceAuthoriseCheckThresholdMaximum);

            PumpMetersWaitForInterval = new ConfigurableTimeSpan(this, CoreHscWorker<HydraDb.Interfaces.IHydraDb>.ConfigKeyPumpMetersWaitForInterval, CoreHscWorker<HydraDb.Interfaces.IHydraDb>.DefaultValuePumpMetersWaitForInterval);
            PumpMetersWaitForAttempts = new ConfigurableInt(this, CoreHscWorker<HydraDb.Interfaces.IHydraDb>.ConfigKeyPumpMetersWaitForAttempts, CoreHscWorker<HydraDb.Interfaces.IHydraDb>.DefaultValuePumpMetersWaitForAttempts);

            _pumpControllerFactory = pumpControllerFactory ?? throw new ArgumentNullException(nameof(pumpControllerFactory));
            RegisterWorker(pumpOutJournal ?? throw new ArgumentNullException(nameof(pumpOutJournal)));
            RegisterWorker(pumpOutTransient ?? throw new ArgumentNullException(nameof(pumpOutTransient)));
            RegisterWorker(posInModeChangeWorker ?? throw new ArgumentNullException(nameof(posInModeChangeWorker)));

            var posClaim = HydraDb.GetPosClaim();
            PosClaimNumber = posClaim.PosNumber;
            var fuelling = HydraDb.GetFuelling();
            IndefiniteWait = fuelling?.IsIndefiniteWait ?? true;
            WaitMinutes = fuelling?.WaitMinutes ?? 0;
            BackoffAuth = fuelling?.BackoffAuth ?? 0;
            BackoffPreAuth = fuelling?.BackoffPreAuth ?? 0;
            BackoffStopStart = fuelling?.BackoffStopStart ?? 0;
            BackoffStopOnly = fuelling?.BackoffStopOnly ?? 0;
        }

        /// <inheritdoc />
        protected override Result DoStart(params object[] startParams)
        {
            var result = base.DoStart(startParams);
            if (!result.IsSuccess)
            {
                return result;
            }

            var results = HydraDb.GetPumpGradePriceInfos();
            if (!result.IsSuccess)
            {
                return results;
            }

            _gradeHelper.Initialise(LoggingReference);

            _pumpDataQueue.Start(startParams);

            PumpController = _pumpControllerFactory.GetInstance(LoggingReference, HydraDb.AdvancedConfig.PumpType);
            PumpController.RegisterWorker(this);
            PumpController.Start(startParams.Concat(new object[] { PosClaimNumber }).ToArray());

            foreach (var info in results.Value)
            {
                SetGradePrices(info.Pump, new Dictionary<byte, ushort>() { [info.Grade] = info.Price }, LoggingReference, false);
                PumpController.SetPumpHoseGradePrice(info.Pump, info.Hose, info.Grade, info.Price, _gradeHelper.GetGradeName(info.Grade));
            }

            lock (_allPumps)
            {
                foreach (var pump in _allPumps.AllPumps.Where(x => x.Opt?.Mode == OptModeType.OptModeMixed))
                {
                    pump.SetHasKioskPayment();
                }
            }

            return Result.Success();
        }

        /// <inheritdoc />
        protected override Result DoStop()
        {
            _pumpDataQueue.Stop();
            PumpController.Stop();
            DeregisterWorker(PumpController);
            PumpController = null;

            return base.DoStop();
        }

        /// <inheritdoc />
        public void Enable()
        {
            Start(StartParameters);
        }

        /// <inheritdoc />
        public void Disable()
        {
            Stop();
        }

        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            var message = new MessageTracking();
            var worker = GetWorker<IPosIntegratorInMode<IMessageTracking, Result>>();
            worker?.RequestModeChange(0, ModeChangeType.Open, message);

            base.DoOnConnected(ipAddress, port);
        }

        protected override void DoOnDisconnected(int? id = null)
        {
            base.DoOnDisconnected(id);

            // Close all pumps if a Disconnect detected, and previously Connected
            var pumpData = CorePumpData.Empty;
            var ss = LoggingReference.Split('/');
            var message = ss.Length == 2 ? new MessageTracking() { ParentIdAsString = ss[1], IdAsString = ss[0] } : new MessageTracking(Guid.Parse($"{LoggingReference}"));
            foreach (var num in _pumpStates.Keys)
            {
                if (_pumpStates.TryGetValue(num, out var _))
                {
                    pumpData.Number = num;
                    HandleCommError(message, pumpData, num, _pumpPaid.TryGetValue(num, out var paid) && paid);
                }
            }
        }

        /// <inheritdoc />
        void IPumpControllerCallbacks.OnMaxPumps(int numberOfPumps, string loggingReference)
        {
            try
            {
                DoDeferredLogging(LogLevel.Info, "Count", () => new[] { $"{numberOfPumps}" }, reference: loggingReference);

                var pricesChanged = false;
                foreach (var pump in GradePrices.Keys.ToList().Where(x => x > numberOfPumps))
                {
                    TryRemoveFromDictionary(GradePrices as ConcurrentDictionary<byte, IDictionary<byte, ushort>>, pump, nameof(GradePrices));
                    pricesChanged = true;
                }

                if (pricesChanged)
                {
                    ControllerWorker?.PushChange(EventType.FuelPriceChanged);
                }

                for (byte pump = 1; pump <= numberOfPumps; pump++)
                {
                    _pumpLocks.GetLockData(pump);
                    TryUpdateValue(_pumpLastInitializeCheck, pump, DateTime.Now, nameof(_pumpLastInitializeCheck));
                    TryUpdateValue(_pumpInitializeTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitInInitialise), nameof(_pumpInitializeTimeout));
                }

                JournalWorker?.OnMaxPumps(numberOfPumps, loggingReference);

                var infos = GetVersionInfo();
                if (infos.IsSuccess)
                {
                    foreach (var info in infos.Value)
                    {
                        ControllerWorker?.SendInformation($"File Name is {info.Name}; Version is {info.Version}; CRC32 Checksum is {info.Checksum}");
                    }
                    ControllerWorker?.PushChange(EventType.AboutChanged);
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex, loggingReference);
            }
        }

        /// <inheritdoc />
        void IPumpControllerCallbacks.OnMeterReadings(IEnumerable<CoreMeterReadings> readings, string loggingReference)
        {
            foreach (var reading in readings)
            {
                TryUpdateValue(_pumpMeters, reading.Number, "_pumpMeters",
                    (byte p) => { return new PumpMeterInfo(p, areReadingsPending: false); },
                    (byte p, PumpMeterInfo v) =>
                    {
                        v.AreReadingsPending = false;
                        return v;
                    });
            }

            if (_pumpMeters.Count == PumpController.NumberOfPumps && _pumpMeters.All(x => !x.Value.AreReadingsPending))
            {
                JournalWorker.OnMeters(readings, loggingReference);
            }
        }

        /// <inheritdoc />
        void IPumpControllerCallbacks.OnPumpData(CorePumpData pumpData)
        {
            Task.Run(() =>
            {
                _pumpDataQueue.Enqueue(pumpData.Number, pumpData);
            }, ShutdownToken);
        }

        private void DoProcessOnPumpData(CorePumpData pumpData)
        {
            var pump = pumpData.Number;
            var lockObj = _pumpLocks.GetLockData(pump);
            var logLevel = lockObj.IgnoredEventCount >= ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdLogAfter.GetValue() ? LogLevel.Warn : ToLogLevel(DeveloperLoggingLevelState.GetValue());

            //lock (lockObj)
            {
                var message = new MessageTracking();
                _allPumps.UpdateParentId(pump, message);
                var reference = message.FullId;
                AssignLoggingReference(ref reference);

                SetThreadName();

                DoDeferredLogging(logLevel, HeaderOnPumpState, () => new[]
                {
                        $"{HeaderBegin} Lock...Pump: {pumpData.Number}", $"State: {pumpData.Dispenser.State}", $"POS: {pumpData.Dispenser.Pos}",
                        $"StartedWaiting: {lockObj.StartedWaiting:yyyy-MM-dd HH:mm:ss,fff}",
                        $"FinishedLast: {lockObj.FinishedLastEvent:yyyy-MM-dd HH:mm:ss,fff}"
                    }, reference: message.FullId);

                var state = pumpData.Dispenser.State;
                var cond1 = true;
                var cond2 = true;
                var cond3 = true;
                var cond3a = true;
                var cond3b = true;
                var cond3c = true;
                if (_allPumps.TryGetPump(pump, out var thePump) && thePump.TransactionSummary != null)
                {
                    var txnSummary = thePump.TransactionSummary;
                    cond1 = txnSummary.HasIntermediateStates();
                    cond2 = !txnSummary.IsAuthorised(checkTransactionState: false);

                    cond3a = txnSummary.WasAuthorisedPriorTo(lockObj.StartedWaiting, checkTransactionState: false);
                    cond3b = txnSummary.IsAuthorised(checkTransactionState: true);
                    cond3c = txnSummary.WasAuthorisedPriorTo(lockObj.StartedWaiting, checkTransactionState: true);
                    cond3 = !cond2 && cond3a && cond3b && cond3c;

                    DoDeferredLogging(logLevel, $"{HeaderOnPumpState}.Pump", () => new[]
                        {
                                $"{pump}",
                                $"{state} Check for Out of sequence Authorise; Conditions ({lockObj.IgnoredEventCount}): {cond1}/{cond2}/{cond3}({cond3a}/{cond3b}/{cond3c}); json: {JsonConvert.SerializeObject(thePump.TransactionSummary)}"
                            },
                        reference: message.FullId);
                }

                var canProcess = !(state == DispenserState.Idle || state.HasFlag(DispenserState.Requested)) || cond1 || cond2 || cond3;

                if (lockObj.IgnoredEventCount >= ConfigValuePumpOutOfSequenceAuthoriseCheckThresholdMaximum.GetValue())
                {
                    DoDeferredLogging(LogLevel.Warn, $"{HeaderOnPumpState}.Pump",
                        () => new[] { $"{pump}", $"Out of sequence Authorise detected; Converted to Request: {state}; Count: {lockObj.IgnoredEventCount}" }, reference: message.FullId);
                    lockObj.IgnoredEventCount = 0;
                    pumpData.Dispenser.State = DispenserState.Requested;
                    OnPumpDataInternal(message, pumpData);
                }
                else
                if (canProcess)
                {
                    lockObj.IgnoredEventCount = 0;
                    OnPumpDataInternal(message, pumpData);
                }
                else
                {
                    lockObj.IgnoredEventCount++;
                    DoDeferredLogging(LogLevel.Warn, $"{HeaderOnPumpState}.Pump",
                        () => new[] { $"{pump}", $"Out of sequence Authorise detected; Ignoring: {state}; Count: {lockObj.IgnoredEventCount}" }, reference: message.FullId);
                }
            }
        }

        private Result DoProcessOnPumpData()
        {
            var tasks = new Dictionary<byte, Task>();
            foreach (var p in _allPumps.AllPumps)
            {
                tasks[p.Number] = Task.Run(() =>
                {
                    var key = p.Number;
                    _pumpDataQueue.CheckAndProcessQueue(key, (msg) =>
                    {
                        DoProcessOnPumpData(msg);

                        return Result.Success();
                    },
                    (pump) => { return _pumpLocks.CanLock(pump); },
                    (pump) => { _pumpLocks.Unlock(pump); }, false);
                }, ShutdownToken);
            }

            return Result.Success();
        }

        internal void OnPumpDataInternal(IMessageTracking message, CorePumpData pumpData)
        {
            DoAction(() => DoOnPumpDataInternal(message, pumpData), message.FullId);
        }

        internal void DoOnPumpDataInternal(IMessageTracking message, CorePumpData pumpData)
        {
            var msg = pumpData.CreateOnPumpDataParamText(ConfigValueLogDeliveringCashThreshold.GetValue(), ConfigValueLogDeliveringVolumeThreshold.GetValue());
            DoDeferredLogging(string.IsNullOrWhiteSpace(msg) ? LogLevel.None : LogLevel.Info, Pump.HeaderPump, () => new[] { msg }, reference: message.FullId, methodName: nameof(IPumpControllerCallbacks.OnPumpData));

            //DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderOnPumpState}.json", () => new[] { JsonConvert.SerializeObject(pumpData) }, reference: message.FullId);

            try
            {
                byte pump = pumpData.Number;
                var disp = pumpData.Dispenser;
                var trans1 = disp.Transaction1;
                var trans2 = disp.Transaction2;
                var currInfo = disp.CurrentInfo;
                var newState = disp.State;
                var (oldState, oldDispenserState) = GetPreviousStates(pump);

                if (_pumpPaidEarly.ContainsKey(pump)
                    && IsTxnPaidEarly(message, pumpData, newState, pump))
                {
                    return;
                }

                if (IsTransactionUnPaid(_paidTran1, pump, trans1) &&
                    IsTransactionUnPaid(_paidTran2, pump, trans2))
                {
                    if (trans1.Amount == currInfo.Amount &&
                        trans1.Volume == currInfo.Volume)
                    {
                        TryUpdateValue(_currentTran2, pump, true, nameof(_currentTran2));
                        TryUpdateValue(_currentTran1, pump, false, nameof(_currentTran1));
                    }
                    else
                    {
                        TryUpdateValue(_currentTran1, pump, true, nameof(_currentTran1));
                        TryUpdateValue(_currentTran2, pump, false, nameof(_currentTran2));
                    }
                }
                else if (IsTransactionUnPaid(_paidTran1, pump, trans1))
                {
                    TryUpdateValue(_currentTran1, pump, true, nameof(_currentTran1));
                    TryUpdateValue(_currentTran2, pump, false, nameof(_currentTran2));
                }
                else if (IsTransactionUnPaid(_paidTran2, pump, trans2))
                {
                    TryUpdateValue(_currentTran2, pump, true, nameof(_currentTran2));
                    TryUpdateValue(_currentTran1, pump, false, nameof(_currentTran1));
                }

                var useTrans1 = ShouldTransactionBeUsed(_currentTran1, pump, trans1);
                var useTrans2 = ShouldTransactionBeUsed(_currentTran2, pump, trans2);

                TryUpdateValue(_paidTran1, pump, trans1.IsPaid, nameof(_paidTran1));
                TryUpdateValue(_paidTran2, pump, trans2.IsPaid, nameof(_paidTran2));
                TryUpdateValue(_owingTran1, pump, trans1.IsPaid ? 0 : PumpController.ToAmount(trans1.Amount), nameof(_owingTran1));
                TryUpdateValue(_owingTran2, pump, trans2.IsPaid ? 0 : PumpController.ToAmount(trans2.Amount), nameof(_owingTran2));

                var oldTwoTrans = _pumpTwoTrans.TryGetValue(pump, out var pumpTwoTrans) && pumpTwoTrans;
                var twoTrans = !trans1.IsPaid && !trans2.IsPaid;
                var oldPaid = _pumpPaid.TryGetValue(pump, out var pumpPaid) && pumpPaid;
                var paid = trans1.IsPaid && trans2.IsPaid;
                _currentInfo.TryGetValue(pump, out var oldTotals);

                // This will deal with SiteController restarts and allow the correct unblocking processing to happen
                if (!_pumpIsBlocked.TryGetValue(pump, out var _))
                {
                    TryUpdateValue(_pumpIsBlocked, pump, true, nameof(_pumpIsBlocked));
                }
                _pumpIsBlocked.TryGetValue(pump, out var oldWasBlocked);

                TryUpdateValue(_pumpPaid, pump, paid, nameof(_pumpPaid));
                TryUpdateValue(_pumpTwoTrans, pump, twoTrans, nameof(_pumpTwoTrans));

                if (IsTransactionPendingOnRestart(disp, pump, useTrans1, useTrans2))
                {
                    DoDeferredLogging(LogLevel.Warn, $"Erroneous pending transaction on restart detected! Pump: {pump}", reference: message.FullId);
                    TryUpdatePumpInfo(_pumpPendingTxnOnRestart, pump, (x, y) => x.PendingTxnOnRestart = y, true, nameof(_pumpPendingTxnOnRestart));
                }

                if (_allPumps.TryGetPump(pump, out var thePump))
                {
                    thePump.IsInError = (CommState)disp.CommErr != CommState.Ok;
                }

                if ((CommState)disp.CommErr != CommState.Ok)
                {
                    HandleCommError(message, pumpData, pump, paid);
                }
                else
                {
                    if (!(newState.Equals(oldDispenserState) || oldDispenserState.HasFlag(DispenserState.Delivering) && newState.HasFlag(DispenserState.Stopped) ||
                          oldDispenserState.HasFlag(DispenserState.Stopped) && newState.HasFlag(DispenserState.Delivering)) || oldPaid != paid || twoTrans != oldTwoTrans ||
                        (newState.HasFlag(DispenserState.Delivering) && oldTotals.Number != currInfo.Number) || (oldWasBlocked != pumpData.RegistrationData.IsBlocked) ||
                        (newState == DispenserState.Idle && paid && !thePump.IsInError && thePump.PumpIsClosed) ||
                        ((oldDispenserState.HasFlag(DispenserState.Idle) || oldDispenserState.HasFlag(DispenserState.Requested)) && newState.HasFlag(DispenserState.Requested) && disp.Pos > 0))
                    {
                        TryUpdateValue(_pumpStates, pump, ((PumpState)newState, disp.State), nameof(_pumpStates));

                        var totals = currInfo;
                        if (disp.State == DispenserState.Requested || disp.State == DispenserState.RequestStarted)
                        {
                            totals = currInfo;
                        }
                        else if (useTrans1)
                        {
                            totals = trans1;
                        }
                        else if (useTrans2)
                        {
                            totals = trans2;
                        }
                        else if (currInfo.Number == 0 && _currentInfo.TryGetValue(pump, out var prevInfo))
                        {
                            totals = prevInfo;
                        }
                        else
                        {
                            totals = currInfo;
                        }

                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.Status.1.FpId", () => new[] { $"{pump}; DispState: {disp.State}; useTrans1: {useTrans1}; useTrans2: {useTrans2}" }, methodName: "OnPumpData");
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.Status.1.c.FpId", () => new[] { $"{pump}; currentInfo: [{JsonConvert.SerializeObject(currInfo)}]" }, methodName: "OnPumpData");
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.Status.1.t1.FpId", () => new[] { $"{pump}; currentInfo: [{JsonConvert.SerializeObject(trans1)}]" }, methodName: "OnPumpData");
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.Status.1.t2.FpId", () => new[] { $"{pump}; currentInfo: [{JsonConvert.SerializeObject(trans2)}]" }, methodName: "OnPumpData");

                        TryUpdateValue(_currentInfo, pump, totals, nameof(_currentInfo));
                        pumpData.Dispenser.CurrentInfo = totals;
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.Status.1.upd.FpId", () => new[] { $"{pump}; currentInfo: [{JsonConvert.SerializeObject(totals)}]" }, methodName: "OnPumpData");

                        // Now Six-Hose aware ADO #488369!!
                        if (totals.Price == 0 && disp.HoseInfo.ContainsKey(totals.Number))
                        {
                            totals.Price = disp.HoseInfo[totals.Number].Grade.Price;
                        }

                        _pumpIsBlocked.TryGetValue(pump, out var wasBlocked);
                        TryUpdateValue(_pumpIsBlocked, pump, pumpData.RegistrationData.IsBlocked, nameof(_pumpIsBlocked));

                        var data = pumpData.ToPumpStateChange(oldDispenserState, false,
                            _pumpPendingTxnOnRestart.TryGetValue(pump, out var pendingTxn) && pendingTxn.PendingTxnOnRestart);

                        OptWorker?.OnPumpState(data, message);
                    }
                }

                ConcurrentDictionary<byte, ushort> gradePrices;
                if (!GradePrices.TryGetValue(pump, out var outGrade))
                {
                    gradePrices = new ConcurrentDictionary<byte, ushort>();
                    TryUpdateValue(GradePrices as ConcurrentDictionary<byte, IDictionary<byte, ushort>>, pump, gradePrices, nameof(GradePrices));
                }
                else
                {
                    gradePrices = (ConcurrentDictionary<byte, ushort>)outGrade;
                }

                // Now Six-Hose aware ADO #488369!!
                bool pricesChanged = false;
                foreach (var hose in disp.HoseInfo)
                {
                    var number = hose.Value.GradeNumber;
                    if (number > 0 && hose.Value.Grade.Price != 0 && !(gradePrices.TryGetValue(number, out var price) && price == PumpController.ToPrice(hose.Value.Grade.Price)))
                    {
                        TryUpdateValue(gradePrices, number, PumpController.ToPrice(hose.Value.Grade.Price), nameof(gradePrices));
                        pricesChanged = true;
                    }
                }

                // Now Six-Hose aware ADO #488369!!
                if (disp.HoseInfo.Values.Any())
                {
                    foreach (var item in gradePrices.Keys.ToList())
                    {
                        if (!disp.HoseInfo.Values.Any(x => x.GradeNumber == item))
                        {
                            TryRemoveFromDictionary(gradePrices, item, nameof(gradePrices));
                            pricesChanged = true;
                        }
                    }
                }

                if (pricesChanged)
                {
                    var priceChange = (PriceChange)pumpData;
                    JournalWorker?.OnPriceChange(priceChange, (MessageTracking)message);
                }

                if (newState.HasFlag(DispenserState.Idle) && paid)
                {
                    TryUpdatePumpInfo(_pumpPendingTxnOnRestart, pump, (x, y) => x.PendingTxnOnRestart = y, false, nameof(_pumpPendingTxnOnRestart));
                }

                if (newState == DispenserState.Initialise)
                {
                    ProcessInitialiseState(message, pump);
                }

                if (newState.HasFlag(DispenserState.Delivering) && _pumpAuth.ContainsKey(pump) &&
                    (!_deliveringTotals.TryGetValue(pump, out var deliveringTotals) || currInfo.Amount > deliveringTotals.Amount))
                {
                    ProcessDeliveringState(message, pumpData, pump);
                }

                if (disp.State.HasFlag(DispenserState.Stopped) && _pumpRestartAuth.ContainsKey(pump))
                {
                    ProcessStoppedState(message, pump);
                }
                else if (!disp.State.HasFlag(DispenserState.Delivering))
                {
                    TryRemoveFromDictionary(_pumpRestartAuth, pump, nameof(_pumpRestartAuth));
                }

                if (disp.State.HasFlag(DispenserState.Delivering) ||
                    disp.State.HasFlag(DispenserState.Finished) || disp.State.HasFlag(DispenserState.Stopped))
                {
                    if (!_deliveringTotals.TryGetValue(pump, out var oldDelivery))
                    {
                        oldDelivery = new() { AllGrades = disp.HoseInfo.Select(x => x.Value.GradeNumber) };
                    }
                    oldDelivery.Number = currInfo.Number;
                    oldDelivery.Amount = currInfo.Amount;
                    oldDelivery.Volume = currInfo.Volume;
                    oldDelivery.Price = currInfo.Price;
                    oldDelivery.IsPaid = trans1.IsPaid && trans2.IsPaid;
                    oldDelivery.Grade = currInfo.Hose.GradeNumber;
                    oldDelivery.TimeStamp = DateTime.Now;

                    TryUpdateValue(_deliveringTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitForDelivering), nameof(_deliveringTimeout));
                    TryUpdateValue(_deliveringTotals, pump, oldDelivery, nameof(_deliveringTotals));
                }
                else
                {
                    TryRemoveFromDictionary(_deliveringTimeout, pump, nameof(_deliveringTimeout));
                }

                TryUpdateValue(_pumpDataTimeout, pump, DateTime.MaxValue, nameof(_pumpDataTimeout));
                TryUpdateValue(_pumpClaimTimeout, pump, DateTime.MaxValue, nameof(_pumpClaimTimeout));

                if (_pumpTransToCashOut.TryGetValue(pump, out var pumpTransToCashOut)
                    //&& (newState == DispenserState.IdleUnpaidClaimed || newState == DispenserState.Idle || newState.HasFlag(DispenserState.Requested))
                    && (pumpTransToCashOut == 1 && trans1.IsPaid || pumpTransToCashOut == 2 && trans2.IsPaid)
                    && !(_pumpIsZeroPaid.TryGetValue(pump, out var dictIsZeroPaid) && dictIsZeroPaid.TryGetValue(pumpTransToCashOut, out var isZeroPaid) && isZeroPaid.Value &&
                         isZeroPaid.Count < ConfigValueIsZeroPaidMaximumCashOutAttempts.GetValue()))
                {
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{newState}.Status.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}", "Removing _pumpTransToCashOut & _pumpAuth" });
                    TryRemoveFromDictionary(_pumpTransToCashOut, pump, nameof(_pumpTransToCashOut));
                    TryRemoveFromDictionary(_pumpAuth, pump, nameof(_pumpAuth));
                }

                if (_pumpPosToClaim.ContainsKey(pump))
                {
                    if (disp.Pos == 0)
                    {
                        if (_pumpTransToCashOut.ContainsKey(pump))
                        {
                            SendClaim(message, pump);
                        }
                        else
                        {
                            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{newState}.Status.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}", "Removing _pumpPosToClaim & _pumpTransToClaim" });
                            TryRemoveFromDictionary(_pumpPosToClaim, pump, nameof(_pumpPosToClaim));
                            TryRemoveFromDictionary(_pumpTransToClaim, pump, nameof(_pumpTransToClaim));
                        }
                    }

                    else if (_pumpPosToClaim.TryGetValue(pump, out var posToClaim) && disp.Pos == posToClaim)
                    {
                        if (_pumpTransToCashOut.ContainsKey(pump))
                        {
                            SendCashOut(message, pump);
                        }
                        else
                        {
                            SendRelease(message, pump);
                        }
                    }
                    else
                    {
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{newState}.Status.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}", "Updating _pumpClaimTimeout" });
                        TryUpdateValue(_pumpClaimTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitForPumpClaim), nameof(_pumpClaimTimeout));
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex, message.FullId);
            }
        }

        private bool IsTransactionPendingOnRestart(Dispenser dispenser, byte pump, bool useTrans1, bool useTrans2)
        {
            return !_pumpPendingTxnOnRestart.TryGetValue(pump, out _) && (useTrans1 || useTrans2);
        }

        private (PumpState, DispenserState) GetPreviousStates(byte pump) =>
            _pumpStates.TryGetValue(pump, out var oldSavedState) ? (oldSavedState.Item1, oldSavedState.Item2) : (PumpState.Closed, DispenserState.Closed);

        private void HandleCommError(IMessageTracking message, CorePumpData pumpData, byte pump, bool paid)
        {
            DoDeferredLogging(LogLevel.Warn, "CommunicationsError", () => new[]
            {
                $"Pump: {pump}. State is {pumpData.Dispenser.State}",
                $" error is {pumpData.Dispenser.CommErr}"
            }, reference: message.FullId);

            var (oldState, oldDispenserState) = GetPreviousStates(pump);
            var data = pumpData.ToPumpStateChange(oldDispenserState, false,
                _pumpPendingTxnOnRestart.TryGetValue(pump, out var pendingTxn) && pendingTxn.PendingTxnOnRestart);
            data.PumpData.Dispenser.State = DispenserState.Closed;

            OptWorker?.OnPumpState(data, message);
        }

        private void ProcessInitialiseState(IMessageTracking message, byte pump)
        {
            if (_pumpInitializeTimeout.TryGetValue(pump, out var timeoutTime))
            {
                var msg = $"{pump}; Waiting {(int)(timeoutTime - DateTime.Now).TotalSeconds} seconds to get an updated state";
                DoDeferredLogging(LogLevel.Info, Pump.HeaderPump, () => new[] { msg }, reference: message.FullId);
                ControllerWorker?.SendInformation($"Pump {msg}");
            }
        }

        private void SendClaim(IMessageTracking message, byte pump)
        {
            var tranNum = _pumpTransToClaim.TryGetValue(pump, out var pumpTransToClaim) ? pumpTransToClaim : (byte)1;

            if (!_pumpPosToClaim.TryGetValue(pump, out var pos))
            {
                DoDeferredLogging(LogLevel.Warn, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; Failed to get from {nameof(_pumpPosToClaim)}" }, reference: message.FullId);
                return;
            }

            var isZeroPaidTran = _pumpIsZeroPaid.TryGetValue(pump, out var isZeroPaid) && isZeroPaid.ContainsKey(tranNum) ? isZeroPaid[tranNum] : new IsZeroPaidInfo();

            var result = PumpController.Claim(pump, pos, tranNum, LoggingReference);
            if (!(result.IsSuccess || isZeroPaidTran.Value))
            {
                DoDeferredLogging(LogLevel.Warn, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}; {HeaderFailed}" }, reference: message.FullId);
                ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Claim command to pump {pump}, POS {pos}, transaction {tranNum}; {HeaderFailed}");
                return;
            }

            DoDeferredLogging(LogLevel.Info, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}" }, reference: message.FullId);
            ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Claim command to pump {pump}, POS {pos}, transaction {tranNum}");

            TryUpdateValue(_pumpClaimTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitForPumpClaim), nameof(_pumpClaimTimeout));

            isZeroPaidTran.Count++;
            TryUpdateIsZeroPaid(_pumpIsZeroPaid, pump, tranNum, isZeroPaidTran, nameof(_pumpIsZeroPaid));
        }

        private void SendRelease(IMessageTracking message, byte pump)
        {
            var tranNum = _pumpTransToClaim.TryGetValue(pump, out var pumpTransToClaim) ? pumpTransToClaim : (byte)1;

            if (!_pumpPosToClaim.TryGetValue(pump, out var pos))
            {
                DoDeferredLogging(LogLevel.Warn, $"Failed to get pump {pump} from {nameof(_pumpPosToClaim)}", reference: message.FullId);
                return;
            }

            var result = PumpController.Release(pump, pos, tranNum, LoggingReference);
            if (!result.IsSuccess)
            {
                DoDeferredLogging(LogLevel.Warn, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}; {HeaderFailed}" }, reference: message.FullId);
                ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Release command to pump {pump}, POS {pos}, transaction {tranNum}; {HeaderFailed}");
                return;
            }

            DoDeferredLogging(LogLevel.Info, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}" }, reference: message.FullId);
            ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Release command to pump {pump}, POS {pos}, transaction {tranNum}");

            TryUpdateValue(_pumpClaimTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitForPumpClaim), nameof(_pumpClaimTimeout));
            TryUpdateIsZeroPaid(_pumpIsZeroPaid, pump, tranNum, new IsZeroPaidInfo(), nameof(_pumpIsZeroPaid));
        }

        private void SendCashOut(IMessageTracking message, byte pump)
        {
            if (!_pumpPosToClaim.TryGetValue(pump, out var pos))
            {
                DoDeferredLogging(LogLevel.Warn, $"Failed to get pump {pump} from {nameof(_pumpPosToClaim)}", reference: message.FullId);
                return;
            }

            if (!_pumpTransToCashOut.TryGetValue(pump, out var tranNum))
            {
                DoDeferredLogging(LogLevel.Warn, "Failed to get pump {pump} from {nameof(_pumpTransToCashOut)}", reference: message.FullId);
                return;
            }

            var result = PumpController.Cashout(pump, pos, tranNum, LoggingReference);
            if (!result.IsSuccess)
            {
                DoDeferredLogging(LogLevel.Warn, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}; {HeaderFailed}" }, reference: message.FullId);
                ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Cash Out command to pump {pump}, POS {pos}, transaction {tranNum}; {HeaderFailed}");
                return;
            }

            DoDeferredLogging(LogLevel.Info, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {pos}; t: {tranNum}" }, reference: message.FullId);
            ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Cash Out command to pump {pump}, POS {pos}, transaction {tranNum}");

            TryUpdateValue(_pumpClaimTimeout, pump, DateTime.Now.AddSeconds(SecondsToWaitForPumpClaim), nameof(_pumpClaimTimeout));
            TryUpdateIsZeroPaid(_pumpIsZeroPaid, pump, tranNum, new IsZeroPaidInfo(), nameof(_pumpIsZeroPaid));
            TryRemoveFromDictionary(_currentInfo, pump, nameof(_currentInfo));
        }

        private void ProcessStoppedState(IMessageTracking message, byte pump)
        {
            if (!_pumpAuth.TryGetValue(pump, out var pumpAuth))
            {
                DoDeferredLogging(LogLevel.Warn, $"Failed to get pump {pump} from {nameof(_pumpAuth)}", reference: message.FullId);
                return;
            }

            DoDeferredLogging(LogLevel.Info, $"Auth.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; lim: {pumpAuth} " }, reference: message.FullId);
            ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Auth With Limit command to pump {pump}, limit {pumpAuth}");

            if (!_pumpRestartAuth.TryGetValue(pump, out var cash))
            {
                DoDeferredLogging(LogLevel.Warn, $"Failed to get pump {pump} from {nameof(_pumpRestartAuth)}", reference: message.FullId);
            }

            PumpController.AuthWithLimit(pump, cash, PosClaimNumber, LoggingReference);
            TryRemoveFromDictionary(_pumpRestartAuth, pump, nameof(_pumpRestartAuth));
        }

        private void ProcessDeliveringState(IMessageTracking message, CorePumpData pumpData, byte pump)
        {
            var amount = PumpController.ToAmount(pumpData.Dispenser.CurrentInfo.Amount);
            if (BackoffStopOnly > 0
                && _pumpAuth.TryGetValue(pump, out var pumpAuthStop)
                && amount > pumpAuthStop - BackoffStopOnly)
            {
                SendStopCommand(pump, () => TryRemoveFromDictionary(_pumpRestartAuth, pump, nameof(_pumpRestartAuth)));
            }
            else if (BackoffStopStart > 0
                     && _pumpAuth.TryGetValue(pump, out var pumpAuthStart)
                     && amount > pumpAuthStart - BackoffStopStart)
            {
                SendStopCommand(pump, () => TryUpdateValue(_pumpRestartAuth, pump, pumpAuthStart, nameof(_pumpRestartAuth)));
            }
        }

        private bool IsTxnPaidEarly(IMessageTracking message, CorePumpData pumpData, DispenserState newState, byte pump)
        {
            byte transToPay = 0;
            if (!pumpData.Dispenser.Transaction1.IsPaid && !pumpData.Dispenser.Transaction2.IsPaid)
            {
                if (pumpData.Dispenser.Transaction1.Amount == pumpData.Dispenser.CurrentInfo.Amount &&
                    pumpData.Dispenser.Transaction1.Volume == pumpData.Dispenser.CurrentInfo.Volume)
                {
                    transToPay = 2;
                }
                else
                {
                    transToPay = 1;
                }
            }

            else if (!pumpData.Dispenser.Transaction1.IsPaid)
            {
                transToPay = 1;
            }
            else if (!pumpData.Dispenser.Transaction2.IsPaid)
            {
                transToPay = 2;
            }

            if (transToPay <= 0)
            {
                return false;
            }

            ControllerWorker?.SendInformation(
                $"Pump state {newState} received for Pump {pump} Paid Early, transaction {transToPay} to pay");
            var amount1 = transToPay == 1 ? pumpData.Dispenser.Transaction1.Amount : pumpData.Dispenser.Transaction2.Amount;
            var amount = PumpController.ToAmount(amount1);
            StartCashOut(message, pump, transToPay, DefaultValueTransSeqNum, amount, false, true);

            if (!_pumpPaidEarly.TryGetValue(pump, out var paidEarlyAmount))
            {
                DoDeferredLogging(LogLevel.Warn, Pump.HeaderPump, () => new[] { $"Failed to get pump from  {nameof(_pumpPaidEarly)}" });
            }

            ControllerWorker?.SendInformation($"Amount paid early was £{paidEarlyAmount / 100.0:F2}," +
                                               $" final amount was £{amount / 100.0:F2}," +
                                               $" discrepancy was £{(amount - paidEarlyAmount) / 100.0:F2}");
            TryRemoveFromDictionary(_pumpPaidEarly, pump, nameof(_pumpPaidEarly));
            if (IsConnected())
            {
                PumpController.RequestPumpData(pump, LoggingReference);
            }

            return true;

        }

        /// <inheritdoc/>
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            if (PumpController == null)
            {
                return;
            }

            var isConnected = IsConnected() && PumpController.IsConnected();
            if (!isConnected)
            {
                return;
            }

            var message = new MessageTracking();

            SetThreadName();

            if (isConnected && !PumpController.GotMaxPumps && LastConnectedAt.HasValue && LastConnectedAt.Value.AddSeconds(1) < DateTime.UtcNow)
            {
                PumpController.RequestMaxPumps();
            }

            var now = DateTime.Now;

            if (GradePrices.Values.Any(x => x.Values.Any(x => x == 0)))
            {
                PumpController.RequestPrices(LoggingReference);
            }

            DoProcessOnPumpData();

            RequestInitialisePumpData(now);

            RequestMeters(now, LoggingReference);
            PumpController.CheckForRequestPumpData(LoggingReference);
            RequestPumpDataByDelivery(now);
            RemovePaidEarly(message, now);
            RequestPumpDataByClaim(now);

            ManageStuckSales(LoggingReference);
        }

        private void RequestInitialisePumpData(DateTime now)
        {
            try
            {
                if (!PumpController.GotMaxPumps)
                {
                    DoDeferredLogging(LogLevel.Info, "MaxPumps not received yet, pump state not requested yet.");
                    return;
                }

                foreach (var pair in _pumpStates.Where(x => x.Value.Item2 == DispenserState.Initialise))
                {
                    if (_pumpLastInitializeCheck.TryGetValue(pair.Key, out var lastCheck) && _pumpInitializeTimeout.TryGetValue(pair.Key, out var timeoutTime))
                    {
                        if (lastCheck > timeoutTime)
                        {
                            DoDeferredLogging(LogLevel.Warn, Pump.HeaderPump, () => new[] { $"{pair.Key} in initialise state for longer than {SecondsToWaitInInitialise} second timeout" });
                            // Reset timer
                            TryUpdateValue(_pumpInitializeTimeout, pair.Key, DateTime.Now.AddSeconds(SecondsToWaitInInitialise), nameof(_pumpInitializeTimeout));

                        }
                        else if (lastCheck.AddSeconds(SecondsInitialiseInterval) < DateTime.Now)
                        {
                            DoDeferredLogging(LogLevel.Info, $"Requesting pump data for pump {pair.Key}, next request in {SecondsInitialiseInterval} seconds");
                            PumpController.RequestPumpData(pair.Key, LoggingReference);
                            TryUpdateValue(_pumpLastInitializeCheck, pair.Key, now.AddSeconds(SecondsInitialiseInterval), nameof(_pumpLastInitializeCheck));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                throw;
            }
        }

        private void RequestPumpDataByClaim(DateTime now)
        {
            try
            {
                foreach (var pump in _pumpClaimTimeout.Keys.ToList())
                {
                    if (_pumpClaimTimeout.TryGetValue(pump, out var datetime) && datetime < now)
                    {
                        if (IsConnected())
                        {
                            PumpController.RequestPumpData(pump, LoggingReference);
                        }

                        TryUpdateValue(_pumpClaimTimeout, pump, now.AddSeconds(SecondsToWaitForPumpClaim), nameof(_pumpClaimTimeout));
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                throw;
            }
        }

        private void RemovePaidEarly(IMessageTracking message, DateTime now)
        {
            try
            {
                foreach (var pump in _pumpDataTimeout.Keys.ToList())
                {
                    if (_pumpDataTimeout.TryGetValue(pump, out var pumpDataTimeout)
                        && pumpDataTimeout < now)
                    {
                        if (!IndefiniteWait
                            && _deliveringTotals.TryGetValue(pump, out var deliveringTotals)
                            && deliveringTotals.TimeStamp.AddMinutes(WaitMinutes) < now)
                        {
                            var deliveringCash = PumpController.ToAmount(deliveringTotals.Amount);


                            var pumpData = new CorePumpData
                            {
                                Number = pump,
                                Dispenser = new Dispenser {
                                    State = DispenserState.Idle,
                                    CurrentInfo = new HoseTotals {
                                        Number = deliveringTotals.Number,
                                        Hose = new Hose(deliveringTotals.Number, grade: deliveringTotals.Grade, price: deliveringTotals.Price),
                                        Price = deliveringTotals.Price,
                                        Volume = deliveringTotals.Volume,
                                        Amount = deliveringTotals.Amount
                                    },
                                }
                            };

                            var (oldState, oldDispenserState) = GetPreviousStates(pump);
                            var data = pumpData.ToPumpStateChange(oldDispenserState, true,
                                _pumpPendingTxnOnRestart.TryGetValue(pump, out var pendingTxn) && pendingTxn.PendingTxnOnRestart);

                            OptWorker?.OnPumpState(data, message);

                            TryRemoveFromDictionary(_pumpDataTimeout, pump, nameof(_pumpDataTimeout));
                            TryUpdateValue(_pumpPaidEarly, pump, deliveringCash, nameof(_pumpPaidEarly));

                            ControllerWorker?.SendInformation($"Pump {pump} paid due to connection loss for £{deliveringCash / 100.0:F2}");
                        }
                        else
                        {
                            TryUpdateValue(_pumpDataTimeout, pump, now.AddSeconds(SecondsToWaitForPumpData), nameof(_pumpDataTimeout));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                throw;
            }
        }

        private void RequestPumpDataByDelivery(DateTime now)
        {
            try
            {
                foreach (var pump in _deliveringTimeout.Keys.ToList())
                {
                    if (_deliveringTimeout.TryGetValue(pump, out var datetime) && datetime < now)
                    {
                        if (IsConnected())
                        {
                            PumpController.RequestPumpData(pump, LoggingReference);
                        }

                        TryRemoveFromDictionary(_deliveringTimeout, pump, nameof(_deliveringTimeout));
                        TryUpdateValue(_pumpDataTimeout, pump, now.AddSeconds(SecondsToWaitForPumpData), nameof(_pumpDataTimeout));
                    }
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                throw;
            }
        }

        /// <inheritdoc/>
        public Result PaymentApproved(byte pump, uint limit, IMessageTracking message = default)
        {
            message ??= new MessageTracking();
            var result = true;

            DoAction(() =>
            {
                if (_pumpTransToCashOut.TryGetValue(pump, out var pumpTransToCashOut))
                {
                    DoDeferredLogging(LogLevel.Info, $"NotAuth.1.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; lim: {limit}" }, reference: message.FullId);
                    result = false;
                    return;
                }

                var pumpAuth = limit > (uint)BackoffAuth ? limit - (uint)BackoffAuth : limit;

                if (_pumpStates.TryGetValue(pump, out var currentState) && (currentState.Item2.HasFlag(DispenserState.Requested) || currentState.Item2.HasFlag(DispenserState.Stopped)))
                {
                    result = false;

                    if (_pumpTransToCashOut.TryGetValue(pump, out pumpTransToCashOut))
                    {
                        DoDeferredLogging(LogLevel.Info, $"NotAuth.2.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; lim: {limit}" }, reference: message.FullId);
                        return;
                    }

                    if (!_allPumps.TryGetPump(pump, out var thePump))
                    {
                        return;
                    }

                    var resultAuth = PumpController.AuthWithLimit(pump, pumpAuth, PosClaimNumber, LoggingReference);
                    if (!resultAuth.IsSuccess)
                    {
                        DoDeferredLogging(LogLevel.Info, $"NotAuth.3.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; lim: {limit}" }, reference: message.FullId);
                        return;
                    }

                    DoDeferredLogging(LogLevel.Info, $"Auth.{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; lim: {pumpAuth}" }, reference: message.FullId);
                    thePump.LogTransactionState(PaymentResult.ApprovedAndAuthorised, message.IdAsString);

                    ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Auth With Limit command to pump {pump}, limit {pumpAuth}");
                    TryUpdateValue(_pumpAuth, pump, pumpAuth, nameof(_pumpAuth));
                    result = true;
                }
            }, message.FullId);

            if (!result && _allPumps.TryGetPump(pump, out var thePump))
            {
                thePump.PaymentApprovedFailed(message.FullId);
            }

            return Result.SuccessIf(result, $"NotAuth.{PumpController.LogHeader}.{Pump.HeaderPump}: {pump}");
        }

        /// <inheritdoc/>
        public Result PaymentCancelled(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                if (_pumpStates.TryGetValue(pump, out var currentState))
                {
                    if (currentState.Item2.HasFlag(DispenserState.Delivering) || currentState.Item2.HasFlag(DispenserState.Authorised))
                    {
                        SendStopCommand(pump, () => TryRemoveFromDictionary(_pumpAuth, pump, nameof(_pumpAuth)));
                    }

                    var result = PumpController.RemoveAuth(pump, PosClaimNumber, message.FullId);
                    return result.IsSuccess ? Result.Success(true) : result;
                }

                return Result.Failure($"Invalid Pump/State: {pump}/{currentState.Item1}");
            }, message.FullId);
        }

        private void SendStopCommand(byte pump, Action action)
        {
            DoDeferredLogging(LogLevel.Info, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}" });
            ControllerWorker?.SendInformation($"Sending {PumpController.LogHeader} Stop command to pump {pump}");

            PumpController.Stop(pump, LoggingReference);
            action();
        }

        private void StartCashOut(IMessageTracking message, byte pump, byte tranNum, int tranSeqNum, uint limit, bool isZeroPaid, bool isPaidEarly)
        {
            DoAction(() =>
            {
                TryUpdateValue(_pumpPosToClaim, pump, PosClaimNumber, nameof(_pumpPosToClaim));
                TryUpdateValue(_pumpTransToClaim, pump, tranNum, nameof(_pumpTransToClaim));
                TryUpdateValue(_pumpTransToCashOut, pump, tranNum, nameof(_pumpTransToCashOut));
                TryUpdateIsZeroPaid(_pumpIsZeroPaid, pump, tranNum, new IsZeroPaidInfo(isZeroPaid), nameof(_pumpIsZeroPaid));

                DoDeferredLogging(LogLevel.Info, $"{(isPaidEarly ? "IsPaidEarly." : string.Empty)}{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[] { $"{pump}; pos: {PosClaimNumber}; t: {tranNum}; lim: {limit}; isZp: {isZeroPaid}; seqNum: {tranSeqNum}" }, reference: message.FullId);
                ControllerWorker?.SendInformation($"Starting {PumpController.LogHeader} Cash Out command on pump {pump}");
                PumpController.StartCashOut(pump, tranNum, tranSeqNum, limit, isZeroPaid, message.FullId);
            }, message.FullId);
        }

        /// <inheritdoc/>
        public Result PaymentCleared(byte pump, uint limit, bool isZeroPaid = false, IMessageTracking message = null, int transSeqNum = 0)
        {
            message ??= new MessageTracking();
            var result = false;

            DoAction(() =>
            {
                limit = isZeroPaid ? 0 : limit;

                byte tranNum = 0;
                if (HasTransactionToBeCleared(_owingTran1, pump, limit, isZeroPaid))
                {
                    tranNum = 1;
                }
                else if (HasTransactionToBeCleared(_owingTran2, pump, limit, isZeroPaid))
                {
                    tranNum = 2;
                }

                if (tranNum > 0)
                {
                    StartCashOut(message, pump, tranNum, transSeqNum, limit, isZeroPaid, false);
                    result = true;
                }
                else
                {
                    DoDeferredLogging(LogLevel.Warn, $"{PumpController.LogHeader}.{Pump.HeaderPump}", () => new[]
                    {
                        $"{pump}; lim: {limit}; isZp: {isZeroPaid}",
                        $"owingTrans1: {(_owingTran1.TryGetValue(pump, out var tran1) ? $"{tran1}" : "n/a")}; owingTrans2: {(_owingTran2.TryGetValue(pump, out var tran2) ? $"{tran2}" : "n/a")}"
                    }, reference: message.FullId, methodName: $"No.{nameof(StartCashOut)}");
                }
            }, message.FullId);
            return Result.SuccessIf(result, $"{PumpController.LogHeader}.{Pump.HeaderPump}: {pump}; No Transaction to Clear");
        }

        private bool HasTransactionToBeCleared(ConcurrentDictionary<byte, uint> owingTran, byte pump, uint limit, bool isZeroPaid = false)
        {
            return _pumpStates.ContainsKey(pump)
                   && _pumpStates.TryGetValue(pump, out var pumpState)
                   && pumpState.Item2.HasFlag(DispenserState.Idle)
                   && owingTran.TryGetValue(pump, out var tran)
                   && (tran > 0 || isZeroPaid)
                   && tran == limit;
        }

        /// <inheritdoc/>
        public Result PaymentClearedOrCancelledAcknowledged(byte pump, int transSeqNum, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            var result = true;

            DoAction(() =>
            {
                PumpController.PaymentClearedOrCancelledAcknowledged(pump, transSeqNum, message.FullId);
            }, message.FullId);
            return Result.SuccessIf(result, $"{PumpController.LogHeader}.{Pump.HeaderPump}: {pump}; No Transaction to Clear");
        }

        /// <inheritdoc/>
        public void SetIndefiniteWait(bool flag, string loggingReference = null)
        {
            SetXxx(() =>
            {
                IndefiniteWait = flag;
                HydraDb.SetFuellingIndefiniteWait(IndefiniteWait);
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void SetWaitMinutes(int minutes, string loggingReference = null)
        {
            SetXxx(() =>
            {
                IndefiniteWait = false;
                WaitMinutes = minutes;
                HydraDb.SetFuellingWaitMinutes(WaitMinutes);
                HydraDb.SetFuellingIndefiniteWait(IndefiniteWait);
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void SetBackoffValue(BackoffType type, int value, string loggingReference = null)
        {
            SetXxx(() =>
            {
                switch (type)
                {
                    case BackoffType.Auth:
                        BackoffAuth = value;
                        HydraDb.SetFuellingBackoffAuth(BackoffAuth);
                        break;

                    case BackoffType.PreAuth:
                        BackoffPreAuth = value;
                        HydraDb.SetFuellingBackoffPreAuth(BackoffPreAuth);
                        break;

                    case BackoffType.StopStart:
                        BackoffStopStart = value;
                        HydraDb.SetFuellingBackoffStopStart(BackoffStopStart);
                        break;

                    case BackoffType.StopOnly:
                        BackoffStopOnly = value;
                        HydraDb.SetFuellingBackoffStopOnly(BackoffStopOnly);
                        break;

                };
            }, loggingReference);
        }

        /// <inheritdoc/>
        public void SetPosClaimNumber(byte number, string loggingReference = null)
        {
            SetXxx(() =>
            {
                PosClaimNumber = number;
                HydraDb.SetPosClaimNumber(PosClaimNumber);
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result SetGradePrices(IEnumerable<Grade> gradePrices, string loggingReference = null)
        {
            var message = new MessageTracking() { IdAsString = loggingReference };

            return DoAction(() =>
            {
                var result = PumpController.SetPrices(gradePrices, loggingReference);

                var dict = gradePrices.ToDictionary(x => x.Id, x => Convert.ToUInt16(x.Price));
                foreach (var pump in _allPumps.AllPumps)
                {
                    SetGradePrices(pump.Number, dict, message.FullId);
                }

                return result;
            }, message.FullId);
        }

        private void SetGradePrices(byte pump, IDictionary<byte, ushort> prices, string loggingReference = null, bool removeMismatched = true)
        {
            DoAction(() =>
            {
                ConcurrentDictionary<byte, ushort> gradePrices;

                if (!GradePrices.TryGetValue(pump, out var outGrade))
                {
                    gradePrices = new ConcurrentDictionary<byte, ushort>();
                    TryUpdateValue(GradePrices as ConcurrentDictionary<byte, IDictionary<byte, ushort>>, pump, gradePrices, nameof(GradePrices));
                }
                else
                {
                    gradePrices = (ConcurrentDictionary<byte, ushort>)outGrade;
                }

                foreach (var grade in prices.Keys.ToList())
                {
                    var gradePrice = (ushort)0;

                    if (prices.TryGetValue(grade, out gradePrice) && (!gradePrices.TryGetValue(grade, out ushort price) || price != gradePrice))
                    {
                        TryUpdateValue(gradePrices, grade, gradePrice, nameof(gradePrices));
                    }
                }

                if (removeMismatched)
                {
                    foreach (var grade in gradePrices.Keys.ToList())
                    {
                        if (!prices.ContainsKey(grade))
                        {
                            TryRemoveFromDictionary(gradePrices, grade, nameof(gradePrices));
                        }
                    }
                }
            }, loggingReference);
        }

        private static bool ShouldTransactionBeUsed(ConcurrentDictionary<byte, bool> currentTransactions, byte pump, Htec.Hydra.Core.Pump.Messages.Transaction transaction)
        {
            return currentTransactions.TryGetValue(pump, out var isCurrentTransaction)
                   && isCurrentTransaction
                   && !transaction.IsPaid
                   && (transaction.Amount > 0 || transaction.SequencNumber > 0 || (transaction.Amount == 0 && transaction.SequencNumber == 0));
        }

        private static bool IsTransactionUnPaid(ConcurrentDictionary<byte, bool> paidTransactions, byte pump, Htec.Hydra.Core.Pump.Messages.Transaction pumpData)
        {
            return (!paidTransactions.TryGetValue(pump, out var paidTransaction) || paidTransaction) && !pumpData.IsPaid;
        }

        private void TryRemoveFromDictionary<T>(ConcurrentDictionary<byte, T> dictionary, byte pump, string dictionaryName)
        {
            if (dictionary.TryRemove(pump, out _))
            {
                DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Failed to remove pump {pump} from {dictionaryName}." });
            }
        }

        private void TryUpdatePumpInfo<T>(ConcurrentDictionary<byte, PumpInfo> dictionary, byte pump, Action<PumpInfo, T> updatePropertyAction, T newValue, string dictionaryName)
        {
            if (!dictionary.TryGetValue(pump, out var pumpInfo))
            {
                DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Failed to get {pump} from {dictionaryName}" });
                pumpInfo = new PumpInfo();
                dictionary.TryAdd(pump, pumpInfo);
            }

            updatePropertyAction(pumpInfo, newValue);
        }

        private void TryUpdateIsZeroPaid(ConcurrentDictionary<byte, IDictionary<byte, IsZeroPaidInfo>> dictionary, byte pump, byte tran, IsZeroPaidInfo newValue, string dictionaryName)
        {
            if (!dictionary.TryGetValue(pump, out var dictTran))
            {
                DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Failed to get {pump} from {dictionaryName}" });
                dictTran = new Dictionary<byte, IsZeroPaidInfo>();
                dictionary.TryAdd(pump, dictTran);
            }

            dictTran[tran] = newValue;
        }

        /// <summary>
        /// Add caching support
        /// </summary>
        /// <inheritdoc/>
        public Result<IEnumerable<FileVersionInfo>> GetVersionInfo()
        {
            return _cacheHelper?.GetCachedItem(nameof(PumpWorker), nameof(FileVersionInfo), () => PumpController.GetVersionInfo()) ?? PumpController.GetVersionInfo();
        }

        /// <inheritdoc/>
        public Result<FileVersionInfo> GetVersionInfo(string fileName)
        {
            var results = GetVersionInfo();
            if (!results.IsSuccess)
            {
                return Result.Failure<FileVersionInfo>(results.Error);
            }

            var result = results.Value.FirstOrDefault(x => x.Name.Equals(fileName, StringComparison.InvariantCultureIgnoreCase));
            return result == null ? Result.Failure<FileVersionInfo>($"File.NotFound: {fileName}") : Result.Success(result);
        }

        private void SetXxx(Action action, string loggingReference = null, [CallerMemberName] string methodName = "")
        {
            DoAction(() =>
            {
                action();
                ControllerWorker?.PushChange(EventType.AdvancedConfigChanged);
            }, loggingReference, methodName);
        }

        /// <inheritdoc/>
        public Result RequestMeters(string loggingReference = null)
        {
            return RequestMeters(null, loggingReference);
        }

        private IEnumerable<byte> RequestMeterPumps(bool isFetching, int maxAttempts, DateTime? now)
        {
            var pump = (byte)0;
            if (isFetching)
            {
                while (pump < PumpController.NumberOfPumps)
                {
                    yield return ++pump;
                }
            }
            else
            {
                foreach (var p in _pumpMeters.Where(x => x.Value.AreReadingsPending && x.Value.RequestExpiry < now && x.Value.RequestedAttempts < maxAttempts))
                {
                    yield return p.Key;
                }

            }
        }

        private Result RequestMeters(DateTime? now, string loggingReference = null)
        {
            var isFetching = now == null || !_pumpMeters.Any();
            var memberName = isFetching ? "FetchMeters" : "RequestMeters";

            return DoAction(() =>
            {
                var pendingPumps = RequestMeterPumps(isFetching, PumpMetersWaitForAttempts.GetValue(memberName), now).ToList();
                if (pendingPumps.Any())
                {
                    foreach (var pump in pendingPumps)
                    {
                        TryUpdateValue(_pumpMeters, pump, "_pumpMeters",
                            (byte p) => new PumpMeterInfo(p, areReadingsPending: true, 0, DateTime.Now.Add(PumpMetersWaitForInterval.GetValue(memberName))),
                            (byte p, PumpMeterInfo v) =>
                            {
                                if (!isFetching)
                                {
                                    v.RequestedAttempts++;
                                }
                                else
                                {
                                    v.AreReadingsPending = true;
                                    v.RequestedAttempts = 0;
                                }
                                v.RequestExpiry = DateTime.Now.Add(PumpMetersWaitForInterval.GetValue(memberName));
                                return v;
                            });
                    }

                    PumpController.RequestMeters(isFetching ? null : now, LoggingReference);
                }

                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result RequestState(IMessageTracking message = default)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                for (var i = (byte)1; i <= PumpController.NumberOfPumps; i++)
                {
                    PumpController.RequestPumpData(i, LoggingReference);
                }
                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc/>
        public Result ReconnectController(string loggingReference = null)
        {
            return DoAction(() =>
            {
                PumpController.Reconnect(loggingReference);
                return Restart();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result ResetController(string loggingReference = null)
        {
            return DoAction(() =>
            {
                PumpController.Reset(loggingReference);
                return Restart();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result MasterResetController(string loggingReference = null)
        {
            return DoAction(() =>
            {
                PumpController.MasterReset(loggingReference);
                return Restart();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result OpenPump(byte pump, string loggingReference = null)
        {
            return DoAction(() =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure($"Pump not found: {pump}");
                }

                PumpController.OpenPump(pump, loggingReference);
                thePump.OpenPump(false, LoggingReference);
                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        public Result ClosePump(byte pump, string loggingReference = null)
        {
            return DoAction(() =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure($"Pump not found: {pump}");
                }

                PumpController.ClosePump(pump, loggingReference);
                thePump.ClosePump(false, LoggingReference);
                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        IDictionary<byte, IEnumerable<Grade>> IPumpIntegratorConfiguration.GradePrices => GradePrices.ToPumpGradeInfoDictionary();

        /// <inheritdoc/>
        IEnumerable<FileVersionInfo> IPumpIntegratorConfiguration.FileVersions
        {
            get
            {
                var result = GetVersionInfo();
                return result.IsSuccess ? result.Value : Enumerable.Empty<FileVersionInfo>();
            }
        }

        FuellingInfo IPumpIntegratorConfiguration.Fuelling => new(IndefiniteWait, WaitMinutes, BackoffAuth, BackoffPreAuth, BackoffStopStart, BackoffStopOnly);

        IPAddress IPumpIntegratorConfiguration.IpAddress => IPAddress.Parse($"{PumpController.CurrentEndPoint.IpAddress}:{PumpController.CurrentEndPoint.Port}");

        string IPumpIntegratorConfiguration.LogonInfo => PumpController.LogonInfo;

        public IPEndPoint EndPoint => PumpController?.CurrentEndPoint?.EndPoint;

        Result IPumpIntegratorConfiguration.SetIpAddress(IPAddress ipAddress, int port, string loggingReference)
        {
            return DoAction(() => {
                var result = PumpController.SetIpAddress(ipAddress, port, LoggingReference);
                DoDeferredLogging(LogLevel.Info, PumpController.LogHeader, () => new[] { !result.IsSuccess ? result.Error : $"{ipAddress}:{port}" });
                return Restart();
            }, loggingReference);
        }

        Result IPumpIntegratorConfiguration.SetLogonInfo(string loginInfo, string loggingReference)
        {
            return DoAction(() => {
                var result = PumpController.SetLogonInfo(loginInfo, LoggingReference);
                DoDeferredLogging(LogLevel.Info, PumpController.LogHeader, () => new[] { !result.IsSuccess ? result.Error : $"{loginInfo}" });
                return result;
            }, loggingReference);
        }

        /// <summary>
        /// Override to additionally include <see cref="ITankGaugeIntegratorInJournal"/> in the connection check
        /// </summary>
        /// <returns>Logical result</returns>
        protected override bool DoIsConnected()
        {
            return base.DoIsConnected() && (PumpController?.IsConnected() ?? false);
        }

        /// <summary>
        /// Override so that when connected it shows as 1
        /// </summary>
        /// <returns></returns>
        protected override int DoGetConnectedCount()
        {
            return DoIsConnected() ? 1 : base.DoGetConnectedCount();
        }

        /// <summary>
        /// Helper method to try and update a concurrent dictionary
        /// </summary>
        /// <typeparam name="T">Type of the Dictionary value</typeparam>
        /// <param name="dictionary">Dictionary instance</param>
        /// <param name="key">Dictionary key</param>
        /// <param name="value">Dictionary value</param>
        /// <param name="dictionaryName">Dictionary name</param>
        protected void TryUpdateValue<T>(ConcurrentDictionary<byte, T> dictionary, byte key, T value, string dictionaryName)
        {
            if (!dictionary.TryGetValue(key, out _))
            {
                DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Failed to get {key} from {dictionaryName}" });
            }

            dictionary.AddOrUpdate(key, value, (key, oldValue) => value);
        }

        /// <summary>
        /// Helper method to try and update a concurrent dictionary
        /// </summary>
        /// <typeparam name="T">Type of the Dictionary value</typeparam>
        /// <param name="dictionary">Dictionary instance</param>
        /// <param name="key">Dictionary key</param>
        /// <param name="actionNew">Delegate to add the dictionary value</param>
        /// <param name="actionUpdate">Delegate to update dictionary value</param>
        /// <param name="dictionaryName">Dictionary name</param>
        protected void TryUpdateValue<T>(ConcurrentDictionary<byte, T> dictionary, byte key, string dictionaryName, Func<byte, T> actionNew, Func<byte, T, T> actionUpdate)
        {
            if (!dictionary.TryGetValue(key, out _))
            {
                DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Failed to get {key} from {dictionaryName}" });
            }

            dictionary.AddOrUpdate(key, actionNew(key), (key, oldValue) => actionUpdate(key, oldValue));
        }

        /// <inheritdoc/>
        public Result ReservePump(byte pump, uint limit, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() =>
            {
                if (!_allPumps.TryGetPump(pump, out var thePump))
                {
                    return Result.Failure($"Pump not found: {pump}");
                }

                var result = PumpController.ReserveWithLimit(pump, limit, PosClaimNumber, LoggingReference);
                if (result.IsSuccess)
                {
                    thePump.PumpReserved(LoggingReference);
                }

                return result;
            }, message.FullId);
        }

        /// <inheritdoc/>
        public void OnNewPricesAvailable(DateTime timeStamp)
        {
            var message = new MessageTracking();
            DoAction(() =>
            {
                if (PendingPriceChange == timeStamp)
                {
                    LatestPriceChange = timeStamp;
                    PendingPriceChange = DateTime.MinValue;
                }
                else
                if (LatestPriceChange < timeStamp)
                {
                    if (LatestPriceChange == DateTime.MinValue || ConfigValueAutoApplyPriceChanges.GetValue())
                    {
                        PumpController.RequestPrices(LoggingReference);
                    }
                    PendingPriceChange = timeStamp;
                }
            }, message.FullId);
        }

        /// <inheritdoc/>
        public bool ShouldSynchroniseGrades => PumpController?.ShouldSynchroniseGrades ?? false;

        public class PumpDataQueue : Queueable<byte, CorePumpData>
        {
            public PumpDataQueue(IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(PumpWorker), configurationManager)
            {
            }
        }

        private void ManageStuckSales(string reference)
        {
            var message = reference.ToMessageTracking();
            DoAction(() =>
            {
                foreach (var pump in _allPumps.AllPumps.Where(x => (x.LastPumpState == PumpState.Idle || x.LastPumpState == PumpState.Request) && !(x.InUse || x.HasPayment)))
                {
                    var locked = _pumpLocks.CanLock(pump.Number);
                    if (!locked.IsSuccess)
                    {
                        continue;
                    }
                    try
                    {
                        var result = HydraDb.GetDeliveredInfo(pump.Number, reference);
                        if (result.IsSuccess)
                        {
                            var delivered = result.Value;

                            var resultCheck = PumpController.CheckForStuckSales((ConfigValueStuckSalesCheckLevel1.GetValue(), ConfigValueStuckSalesCheckLevel2.GetValue()), pump.Number, delivered.HasDelivered, delivered.TransSeqNum, delivered.Amount, delivered.Volume, delivered.Grade, PosClaimNumber, reference);
                            if (resultCheck.IsSuccess)
                            {
                                pump.ResetPump(reference);
                            }
                        }
                    }
                    finally
                    {
                        _pumpLocks.Unlock(pump.Number);
                    }
                }
            }, reference);
        }

        /// <inheritdoc/>
        public void OnGradePriceChange(byte pump, byte gradeId, string gradeName, uint price, IEnumerable<byte> hoseIds, string loggingReference = null)
        {
            SetGradePrices(pump, new Dictionary<byte, ushort>() { [gradeId] = Convert.ToUInt16(price) }, loggingReference, false);

            var hose = new Hose(0, grade: gradeId, price: Convert.ToDecimal(price) / HscPumpData.CurrencyFactor);
            hose.Grade.Name = gradeName;

            foreach (var hoseId in hoseIds)
            {
                hose.Number = hoseId;
                var priceChange = new PriceChange()
                {
                    Number = pump,
                    Hoses = new List<Hose> { hose }
                };
                JournalWorker?.OnPriceChange(priceChange, loggingReference.ToMessageTracking());
            }
        }

        /// <inheritdoc/>
        public Result FlushOnPumpData(byte pump)
        {
            return _pumpDataQueue.Flush(pump);
        }
    }
}
