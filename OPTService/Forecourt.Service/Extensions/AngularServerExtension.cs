using System;
using Microsoft.Owin;
using Microsoft.Owin.FileSystems;
using Microsoft.Owin.StaticFiles;
using Owin;

namespace OPTService.Extensions
{
    /// <summary>
    /// Angular Owin server extension
    /// </summary>
    public static class AngularServerExtension
    {
        /// <summary>
        /// The extension entry point.
        /// </summary>
        /// <param name="builder">The webapp builder object</param>
        /// <param name="rootPath">The app root path</param>
        /// <param name="entryPath">The app entry path</param>
        /// <returns>The composed builder</returns>
        public static IAppBuilder UseAngularServer(this IAppBuilder builder, string rootPath, string entryPath)
        {
            var options = new AngularServerOptions()
            {
                FileServerOptions = new FileServerOptions()
                {
                    EnableDirectoryBrowsing = false,
                    FileSystem = new PhysicalFileSystem(System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, rootPath))
                },
                EntryPath = new PathString(entryPath)
            };

            builder.UseDefaultFiles(options.FileServerOptions.DefaultFilesOptions);
            return builder.Use(typeof(AngularServerMiddleware), options);
        }
    }
}