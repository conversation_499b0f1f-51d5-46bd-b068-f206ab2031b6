using System;

namespace Forecourt.Core.HydraDb.Models
{
    public class OptMode
    {
        public string OptId { get; }
        public bool Contactless { get; }
        public string ReceiptHeader { get; }
        public string ReceiptFooter { get; }
        public string PlaylistFileName { get; }
        public DateTime? LastLogTime { get; }

        // ReSharper disable once UnusedMember.Global
        public OptMode(string opt, bool contactless = false, string receiptHeader = null, string receiptFooter = null, string playlistFileName = null, DateTime? lastLogTime = null)
        {
            OptId = opt;
            Contactless = contactless;
            ReceiptHeader = receiptHeader;
            ReceiptFooter = receiptFooter;
            PlaylistFileName = playlistFileName;
            LastLogTime = lastLogTime;
        }
    }
}