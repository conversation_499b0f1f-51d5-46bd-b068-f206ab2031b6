import { TestBed } from "@angular/core/testing";
import { ConnectionService } from "./connection.service";

export function CONNECTION_SERVICE_PROVIDER(): { provide: typeof ConnectionService, useValue: any } {
  return {
    provide: ConnectionService,
    useValue: jasmine.createSpyObj('ConnectionService', ['getConnections', 'setProperty', 'setPumpControllerLogonInfo', 'removeRetalixPosPrimaryIpAddress']),
  };
}

export function CONNECTION_SERVICE_SPY(): jasmine.SpyObj<ConnectionService> {
  return TestBed.inject(ConnectionService) as jasmine.SpyObj<ConnectionService>;
}
