using System;

namespace Forecourt.Core.Configuration.Interfaces
{
    public interface IShiftDayEndConfig
    {
        /// <summary>
        /// Mininum time period from last ShiftEnd before Day End can be processed
        /// </summary>
        TimeSpan MinimumIntervalFromShiftToDayEnd { get; }

        /// <summary>
        /// How long to wait until a Dips response is received
        /// </summary>
        TimeSpan ExpiryIntervalDips { get; }

        /// <summary>
        /// How long to wait until a Meters response is received
        /// </summary>
        TimeSpan ExpiryIntervalMeters { get; }

        /// <summary>
        /// Maximum number of attempts to obtain a Dips response
        /// </summary>
        int MaxAttemptsDips { get; }

        /// <summary>
        /// Maximum number of attempts to obtain a Meters response
        /// </summary>
        int MaxAttemptsMeters { get; }

        /// <summary>
        /// How long to wait until performing the Shift/Day End, taking into account the Dips/Meters configuration parameters
        /// </summary>
        TimeSpan ExpiryIntervalOverall { get; }
    }
}
