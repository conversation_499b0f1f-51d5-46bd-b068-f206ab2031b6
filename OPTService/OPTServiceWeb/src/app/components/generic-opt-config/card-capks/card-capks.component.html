<div [formGroup]="form">
    <ngb-accordion [closeOthers]="true" [activeIds]="activeCardCapk" (panelChange)="toggleActiveCardCapk($event)" [formArrayName]="formArrayName">
        <ngb-panel *ngFor="let rid of coockedCardCapks?.controls; let i = index" [formGroupName]="i" [id]="'capk-'+rid.get('Rid').value">
            <ng-template ngbPanelHeader>
                <div class="d-flex align-items-center justify-content-between">
                    <button ngbPanelToggle class="btn btn-link container-fluid pl-0 d-flex align-items-center"> 
                        <span>RID {{rid.get('Rid').value}}</span>
                    </button>
                </div>
            </ng-template>
            <ng-template ngbPanelContent>
                <ngb-accordion [closeOthers]="true" formArrayName="Indexes" [activeIds]="activeCardCapkIndex" (panelChange)="toggleActiveCardCapkIndex($event)">
                    <ngb-panel *ngFor="let ridItem of rid.get('Indexes').controls; let j = index" [formGroupName]="j" [id]="'capk-index-'+ rid.get('Rid').value + '-' + ridItem.get('TheIndex').value">
                        <ng-template ngbPanelHeader>
                            <div class="d-flex align-items-center justify-content-between">
                                <button ngbPanelToggle
                                    class="btn btn-link container-fluid pl-0 d-flex align-items-center">
                                    <span>Index {{ridItem.get('TheIndex').value}}</span>
                                </button>
                            </div>
                        </ng-template>
                        <ng-template ngbPanelContent>
                            <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                id="modulus" labelText="Modulus" controlName="Modulus">
                            </app-label-text>
                            <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                id="exponent" labelText="Exponent" controlName="Exponent">
                            </app-label-text>
                            <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                id="checksum" labelText="Checksum" controlName="Checksum">
                            </app-label-text>
                            <app-label-text labelColClass="col-12 col-md-2 col-xl-2" textColClass="col-12 col-md-6 col-xl-3" disabled="true"
                                id="expiryDate" labelText="Expiry date" controlName="ExpiryDate">
                            </app-label-text>
                        </ng-template>
                    </ngb-panel>
                </ngb-accordion>
            </ng-template>
        </ngb-panel>
    </ngb-accordion>
</div>