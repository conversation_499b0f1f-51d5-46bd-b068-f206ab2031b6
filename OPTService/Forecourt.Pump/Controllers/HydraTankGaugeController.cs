using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Workers;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Timers;
using ITelemetryWorker = Forecourt.Pump.Workers.Interfaces.ITelemetryWorker;
using NewDip = Htec.Hydra.Core.Pump.Messages.Dip;

namespace Forecourt.Pump.Controllers
{
    public class HydraTankGaugeController : ClientWorker<string, IHydraDb, ITelemetryWorker>, IHydraTankGaugeController
    {
        /// <inheritdoc/>
        string ITankGaugeControllerConfiguration.LogHeader => "HSC";

        private bool _dipsPending = false;
        private DateTime _currentTimeout;
        private int _attempts = 0;
        private INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        private readonly IShiftDayEndConfig _shiftDayEndConfig;

        /// <inheritdoc />
        public GenericEndPoint CurrentEndPoint => EndPoint;

        /// <summary>Constructor for ANPR Worker.</summary>
        /// <param name="telemetryWorker">Telemetry Worker for this class.</param>
        /// <param name="hydraDb">Hydra Database.</param>
        /// <param name="connectionThread">ConnectionThread for this class.</param>
        /// <param name="logger">Htec Logger for this class.</param>
        /// <param name="configurationManager"><see cref="IConfigurationManager"/> instance</param>
        /// <param name="timerFactory"><see cref="ITimerFactory"/> instance</param>
        /// <param name="shiftDayEndConfig">Shift/Day End configuration</param>
        public HydraTankGaugeController(ITelemetryWorker telemetryWorker, IHydraDb hydraDb, IClientConnectionThread<string> connectionThread,
            IHtecLogger logger, IConfigurationManager configurationManager, ITimerFactory timerFactory, IShiftDayEndConfig shiftDayEndConfig) :
            base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory)
        {
            _shiftDayEndConfig = shiftDayEndConfig ?? throw new ArgumentNullException(nameof(shiftDayEndConfig));
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            return HydraDb.FetchTankGaugeEndPoint();
        }

        /// <inheritdoc />
        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            var now = DateTime.Now;
            if (_dipsPending && _currentTimeout < now)
            {
                GetWorker<ITelemetryWorker>()?.MessageTimeoutFromTankGauge();
                _dipsPending = false;
                if (_attempts < _shiftDayEndConfig.MaxAttemptsDips)
                {
                    SendDipRequestToTankGauge();
                }
            }
        }

        /// <inheritdoc/>
        public Result RequestDips(string loggingReference = null)
        {
            return DoAction(() =>
            {
                _attempts = 0;
                return SendDipRequestToTankGauge();
            }, loggingReference);
        }

        private Result SendDipRequestToTankGauge()
        {
            if (!IsConnected())
            {
                return Result.Failure(ConfigConstants.NotConnected);
            }

            DoDeferredLogging(LogLevel.Info, HeaderStatus, () => new[] { "Sending Dip Request to Tank Gauge" });
            SendRequest("Dip request", action: () =>
            {
                _dipsPending = true;
                _attempts++;
                _currentTimeout = DateTime.Now.AddSeconds(_shiftDayEndConfig.ExpiryIntervalDips.TotalSeconds);
            });
            GetWorker<ITelemetryWorker>().MessageSentToTankGauge();

            return Result.Success();
        }

        private IList<string> GetDipsFromMessage(string message)
        {
            const string space = " ";
            var result = DoAction(() =>
            {
                var dips = new List<string>();
                while (message.Contains("DIP"))
                {
                    message = message.Trim();
                    var start = message.IndexOf("DIP", StringComparison.InvariantCulture);
                    if (start > 0)
                    {
                        DoDeferredLogging(LogLevel.Warn, HeaderInformation, () => new[] { $"Discarding characters from message {message}" });
                        message = message.Substring(start);
                    }

                    if (message.Contains(space))
                    {
                        var thisMessage = message.Substring(0, message.IndexOf(space, StringComparison.InvariantCulture));
                        dips.Add(thisMessage);
                        message = message.Substring(message.IndexOf(space, StringComparison.InvariantCulture) + 1);
                    }
                    else
                    {
                        dips.Add(message);
                        message = string.Empty;
                    }
                }

                return Result.Success(dips);
            }, LoggingReference);

            return !result.IsSuccess ? new List<string>() : result.Value;
        }

        private bool TryParsePart(string dip, string indexOf1, string indexOf2, ref int part)
        {
            return dip.Substring(dip.IndexOf(indexOf1, StringComparison.InvariantCulture)).Contains(indexOf2) &&
                   int.TryParse(
                       dip.Substring(dip.IndexOf(indexOf1, StringComparison.InvariantCulture) + 1,
                           dip.Substring(dip.IndexOf(indexOf1, StringComparison.InvariantCulture)).IndexOf(indexOf2, StringComparison.InvariantCulture) - 1),
                       out part);
        }

        private NewDip ProcessDip(string dip)
        {
            var result = DoAction(() =>
            {
                var dipLevel = 0;
                var waterLevel = 0;
                var temperature = 0;

                DoDeferredLogging(LogLevel.Info, "Dip", () => new[] { $"{dip}" });
                if (dip.StartsWith("DIP") && dip.Contains(":") &&
                    int.TryParse(dip.Substring("DIP".Length, dip.IndexOf(":", StringComparison.InvariantCulture) - "DIP".Length), out var tank) &&
                    TryParsePart(dip, ":", "W", ref dipLevel) &&
                    TryParsePart(dip, "W", "T", ref waterLevel) &&
                    TryParsePart(dip, "T", "H", ref temperature) &&
                    int.TryParse(dip.Substring(dip.IndexOf("H", StringComparison.InvariantCulture) + 1), out var height))
                {
                    ControllerWorker
                        ?.SendInformation(
                            $"Tank is {tank}, dip level is {dipLevel}, water level is {waterLevel}, temperature is {temperature}, height is {height}");
                    return Result.Success(new NewDip(tank, dipLevel, waterLevel, temperature, height));
                }

                DoDeferredLogging(LogLevel.Warn, $"{HeaderEndFail}/InvalidDip", () => new[] { $"{dip}" });
                return Result.Failure<NewDip>("Null");
            }, LoggingReference);

            return !result.IsSuccess ? null : result.Value;
        }

        /// <inheritdoc />
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message)
        {
            var request = message.Request;
            var dipStrings = GetDipsFromMessage(request);
            if (!dipStrings.Any())
            {
                return null;
            }

            var dips = new List<NewDip>();
            if (_dipsPending)
            {
                GetWorker<ITelemetryWorker>()?.MessageReceivedFromTankGauge();
                _dipsPending = false;
            }

            foreach (var dipString in dipStrings)
            {
                var dip = ProcessDip(dipString);
                if (dip != null)
                {
                    dips.Add(dip);
                }
            }

            if (dips.Any())
            {
                GetWorker<ITankGaugeControllerCallbacks>()?.OnDips(dips, message.FullId);
            }

            return null;
        }

        /// <inheritdoc/>
        protected override void DoOnConnected(IPAddress ipAddress = null, int? port = null)
        {
            base.DoOnConnected(ipAddress, port);

            GetWorker<ITankGaugeControllerCallbacks>()?.OnConnected(ipAddress, port);
        }

        /// <inheritdoc/>
        protected override void DoOnDisconnected(int? id = null)
        {
            base.DoOnDisconnected(id);

            GetWorker<ITankGaugeControllerCallbacks>()?.OnDisconnected(id);
        }

        /// <inheritdoc/>
        public Result SetIpAddress(IPAddress ipAddress, int port, string loggingReference = null)
        {
            HydraDb.SetTankGaugeEndPoint(ipAddress, port == 0 ? CurrentEndPoint.Port : port);
            return Result.Success();
        }
    }
}
