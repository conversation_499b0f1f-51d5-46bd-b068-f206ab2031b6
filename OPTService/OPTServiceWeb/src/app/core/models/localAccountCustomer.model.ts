import { LocalAccountCard } from "./LocalAccountCard.model";

export class LocalAccountCustomer {
    AllowLoyalty: boolean = false;
    Balance: number = 0;
    Cards: Array<LocalAccountCard> = [];
    CustomerReference: string = '';
    FuelOnly: boolean = false;
    LowCreditWarning: boolean = false;
    MaxCreditReached: boolean = false;
    MileageEntry: boolean = false;
    Name: string = '';
    Pin: boolean = false;
    PrePayAccount: boolean = false;
    PrintValue: boolean = false;
    RegistrationEntry: boolean = false;
    TransactionLimit: number = 0;
    TransactionsAllowed: boolean = false;
}