using System;
using System.Collections.Generic;
using System.Linq;

namespace OPT.TransactionValidator.Extensions
{
    /// <summary>
    /// Extension methods for <see cref="IEnumerable{T}"/>.
    /// </summary>
    public static class EnumerableExtensions
    {
        /// <summary>
        /// Gets a single result from the collection, using <paramref name="comparisonFunc"/> to compare each entry.
        /// </summary>
        /// <typeparam name="T">Type the collection contains.</typeparam>
        /// <param name="enumerable">The collection.</param>
        /// <param name="comparisonFunc">Function to compare collection elements.</param>
        /// <returns>The single instance.</returns>
        public static T SingleFromComparison<T>(this IEnumerable<T> enumerable, Func<T, T, bool> comparisonFunc)
        {
            return enumerable.Aggregate((current, x) => current == null || comparisonFunc(current, x) ? current : x);
        }
    }
}
