import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { AdvancedConfig } from 'src/app/core/models/advancedConfig.model';
import { ADVANCED_SERVICE_PROVIDER, ADVANCED_SERVICE_SPY } from 'src/app/services/advanced-config.service.spy';
import { LOADING_SERVICE_PROVIDER } from 'src/app/services/loading.service.spy';
import { SIGNAL_R_SERVICE_PROVIDER } from 'src/app/services/signal-r.service.spy';
import { NGX_LOGGER_PROVIDER, NGX_LOGGER_SPY } from 'src/app/testing/ngxlogger.spy';
import { FileLocationsComponent } from '../file-locations/file-locations.component';

import { EsocketPosConfigComponent } from './esocket-pos-config.component';

describe('EsocketPosConfigComponent', () => {
  let component: EsocketPosConfigComponent;
  let fixture: ComponentFixture<EsocketPosConfigComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ EsocketPosConfigComponent ],
      providers: [
        FileLocationsComponent,
        FormBuilder,
        NGX_LOGGER_PROVIDER(),
        ADVANCED_SERVICE_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of({} as AdvancedConfig));
    ADVANCED_SERVICE_SPY().getConfigDataChanged.and.returnValue(of());

    fixture = TestBed.createComponent(EsocketPosConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  it('.setEsocketUseConnectionString() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketUseConnectionString();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketUseConnectionString() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketUseConnectionString();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketConnectionString() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketConnectionString();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketConnectionString() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketConnectionString();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideProperties() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketOverrideProperties();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideProperties() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketOverrideProperties();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketConfigFile() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketConfigFile();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketConfigFile() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketConfigFile();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideKeystore() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketOverrideKeystore();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideKeystore() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketOverrideKeystore();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketKeystoreFile() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketKeystoreFile();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketKeystoreFile() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketKeystoreFile();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideUrl() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketOverrideUrl();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketOverrideUrl() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketOverrideUrl();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketDbUrl() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(of({}));
    
    //Act
    component.setEsocketDbUrl();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.setEsocketDbUrl() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setESocketProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    component.setEsocketDbUrl();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setESocketProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });
});
