using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Text;

namespace Forecourt.Pump.DomsSetupClasses
{
    public abstract class DomsMessage : Loggable
    {
        protected readonly Encoding _encoding = Encoding.GetEncoding(28591);

        protected DomsMessage(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager) : base(logManager, loggerName, configurationManager) { }

        protected void LogMessageBytes(byte[] message, [CallerMemberName] string methodName = "") =>
            // TODO: Needs to be on the equivalent of LogRxTx
            DoDeferredLogging(LogLevel.Debug, "Length", () => new[] { $"{message.Length}; Content: {ToHexString(message)}" }, methodName: methodName);

        protected string ToHexString(byte[] bytes) => bytes.ToHexString(_encoding);

        protected Result<IEnumerable<(bool, byte, (DomsMessageCode, byte))>> LogInvalidMessageFormat()
        {
            const string msg = "InvalidMessageFormat";
            DoDeferredLogging(LogLevel.Warn, msg);
            return Result.Failure<IEnumerable<(bool, byte, (DomsMessageCode, byte))>>(msg);
        }

        protected string FormatHeader(string header, int count, int item) => $"{header}.{(count >= 100 ? $"{item,3}" : count >= 10 ? $"{item,2}" : $"{item}")}";

        protected string FormatDateTime(DateTime dtm) => $"{dtm:dd/MM/yyyy HH:mm:ss}";

        protected string BooleanToStringOrDefault(bool value, string result, string separator = ";") => value ? $" {result}{separator}" : string.Empty;
    }
}
