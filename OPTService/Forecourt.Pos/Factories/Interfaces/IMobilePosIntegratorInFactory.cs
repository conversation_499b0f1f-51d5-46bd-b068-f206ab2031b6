using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;

namespace Forecourt.Pos.Factories.Interfaces
{
    /// <summary>
    /// Standard IFactory based definition for the (mobile) POS Integrator In
    /// </summary>
    public interface IMobilePosIntegratorInFactory : IFactory<string, IPosIntegratorIn<IMessageTracking, IPump, Result>>
    {
    }
}
