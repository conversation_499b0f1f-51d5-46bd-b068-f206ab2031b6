using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;

namespace Forecourt.SecondaryAuth.Hubs.Interfaces
{
    /// <inheritdoc/>
    public interface ISecAuthHubbable : IHubbable<SecAuthMessage, IMessageTracking>, ISecAuthHubbableIn
    {
    }

    /// <inheritdoc/>
    public interface ISecAuthHubbableIn : IHubbableIn<SecAuthMessage, IMessageTracking>
    {
    }
}
