using CSharpFunctionalExtensions;
using Forecourt.Core.HydraDb.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Foundation.Models;
using OPT.Common.HydraDb.Interfaces;
using System.Collections.Generic;
using System.Net;
using FuelTransaction = Forecourt.Core.HydraDb.Models.FuelTransaction;

namespace Forecourt.Core.HydraDb.Interfaces
{
    /// <summary>
    /// Any and all capabilities of the Core IHydraDb
    /// </summary>
    public interface IHydraDb:
        ICoreHydraDb<FuelTransaction>, 
        IHydraDbCoreConfig,
        IHydraDbGenericOptConfig,
        IHydraDbTransaction
    {
    }

    /// <summary>
    /// Isolation of the configuration aspects of the Core IHydraDb
    /// </summary>
    public interface IHydraDbCoreConfig: IEntity<string>
    {
        /// <summary>
        /// Get all <see cref="SiteInfo"/> information
        /// </summary>
        /// <returns><see cref="SiteInfo"/></returns>
        SiteInfo GetSiteInfo();

        /// <summary>
        /// Get all <see cref="AdvancedConfig"/> advanced configuration informaion
        /// </summary>
        AdvancedConfig AdvancedConfig { get; }

        /// <summary>
        /// Get all <see cref="EndPointsConfig"/> endpoints configuration informaion
        /// </summary>
        EndPointsConfig EndPointsConfig { get; }

        /// <summary>
        /// Get all <see cref="BosConfig"/> endpoints configuration informaion
        /// </summary>
        BosConfig BosConfig { get; }

        /// <summary>
        /// Get migration version information
        /// </summary>
        /// <returns>Result wrapped <see cref="VersionInfo"/> list</returns>
        Result<IEnumerable<VersionInfo>> GetVersionInfo();

        /// <summary>
        /// Fetch the set of end points from the database.
        /// </summary>
        /// <returns>The end points.</returns>
        OptEndPoints FetchEndPoints(string hydraId = null);

        /// <summary>
        /// Get all file and folder locations
        /// </summary>
        /// <returns><see cref="AllFileLocations"/> instance</returns>
        AllFileLocations GetFileLocations();

        /// <summary>
        /// Get the primary IpAddress, for Retalix
        /// </summary>
        /// <returns><see cref="IPAddress"/> instance</returns>
        IPAddress GetRetalixPosPrimaryIpAddress();

        /// <summary>
        /// Provides the global HydraId so it is accessible from anywhere
        /// </summary>
        /// <param name="id">HydraDb Id</param>
        void SetId(string id);

        /// <summary>
        /// Retrieve card references stored in the database.
        /// </summary>
        /// <param name="message">Current message</param>
        /// <returns>List of card references.</returns>
        IList<CardReference> FetchCardReferences(IMessageTracking message = null);

        /// <summary>
        /// Set the reference number for a card type.
        /// </summary>
        /// <param name="name">Name of card type.</param>
        /// <param name="reference">Reference number to set.</param>
        Result<int> SetCardReference(string name, int reference);

        /// <summary>
        /// Clear a card type.
        /// </summary>
        /// <param name="name">Name of card type.</param>
        Result<int> ClearCardReference(string name);
    }

    /// <summary>
    /// Isolation of the core GenericOptConfig aspects of the Core IHydraDb
    /// </summary>
    public interface IHydraDbGenericOptConfig
    {
        /// <summary>
        /// Fetch the set of Tariff Mappings from the database.
        /// </summary>
        /// <returns>The Tariff Mappings.</returns>
        IList<TariffMapping> FetchTariffMappings();

        /// <summary>
        /// Store Tariff Mappings in the database.
        /// </summary>
        /// <param name="mappings">The mappings to store</param>>
        void SetTariffMappings(IList<TariffMapping> mappings);

        /// <summary>
        /// Set Tariff Mapping Fuel Cards Only in the database.
        /// </summary>
        /// <param name="grade">The grade to set.</param>>
        /// <param name="flag">True to set, false to clear.</param>>
        Result<int> SetTariffMappingFuelCardsOnly(byte grade, bool flag);
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Transactions
    /// </summary>
    public interface IHydraDbTransaction
    {
        /// <summary>
        /// Set a pump as having an a delivery notification.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="grade">The grade number.</param>
        /// <param name="volume">The delivery volume.</param>
        /// <param name="amount">The delivery amount.</param>
        /// <param name="name">The grade name.</param>
        /// <param name="price">The delivery price.</param>
        /// <param name="netAmount">The delivery net amount.</param>
        /// <param name="vatAmount">The delivery VAT amount.</param>
        /// <param name="vatRate">The delivery VAT rate.</param>
        /// <param name="message">The current message</param>
        /// <param name="transSeqNum">The Pump transaction sequence number</param>
        /// <param name="hose">The delivered hose.</param>
        Result<int> SetDelivered(IMessageTracking message, byte pump, byte grade, uint volume, uint amount, string name, ushort price, uint netAmount, uint vatAmount, float vatRate, int transSeqNum, byte hose);

        /// <summary>
        /// Clear delivery notification for pump.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="message">The current message</param>
        Result<int> ClearDelivered(byte pump, IMessageTracking message = null);

        /// <summary>
        /// Fetch a single transaction (fuel or other) using either internal or OPT transaction ids
        /// </summary>
        /// <param name="maxTransactionNumber">Maximum transaction number for number cycling.</param>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="txnNumber">OPT transaction number</param>
        /// <param name="message">Current message</param>
        /// <returns>Result wrapped <see cref="FuelTransaction"/></returns>
        Result<FuelTransaction> FetchTransaction(uint maxTransactionNumber, long? transId = null, string txnNumber = null, IMessageTracking message = null);

        /// <inheritdoc cref="FetchTransaction(uint, long?, string, IMessageTracking)" />
        Result<StatusCodeResult> FetchTransactionHttp(uint maxTransactionNumber, long? transId = null, string txnNumber = null, IMessageTracking message = null);
    }
}