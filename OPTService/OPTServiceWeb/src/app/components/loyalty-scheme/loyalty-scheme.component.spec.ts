import { CUSTOM_ELEMENTS_SCHEMA, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { NGXLogger } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { LoyaltyService } from 'src/app/services/loyalty.service';

import { LoyaltySchemeComponent } from './loyalty-scheme.component';

describe('LoyaltySchemeComponent', () => {
  let fixture: ComponentFixture<LoyaltySchemeComponent>;
  let component: LoyaltySchemeComponent;
  let ngxLoggerSpy: jasmine.SpyObj<NGXLogger>;
  let loyaltyServiceSpy: jasmine.SpyObj<LoyaltyService>;
  const formBuilder: FormBuilder = new FormBuilder();

  beforeEach(() => {
    const ngxLoggerObjSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);
    const loyaltyServiceObjSpy = jasmine.createSpyObj('LoyaltyService', ['addLoyalty', 'setLoyaltyPresent', 
      'setLoyaltyTerminalSiteId', 'setLoyaltyTerminalTerminalId', 'setLoyaltyTerminalFooter1', 
      'setLoyaltyTerminalFooter2', 'setLoyaltyTerminalTimeout', 'setLoyaltyTerminalApiKey',
      'setLoyaltyTerminalHttpHeader', 'setLoyaltyHostsAddHost', 'setLoyaltyHostsRemoveHost',
      'setLoyaltyAddHostname', 'setLoyaltyIinsAddIin', 'setLoyaltyMappingsAddMapping']);
   
    TestBed.configureTestingModule({
      declarations: [ LoyaltySchemeComponent ],
      providers: [
        { provide: NGXLogger, useValue: ngxLoggerObjSpy },
        { provide: LoyaltyService, useValue: loyaltyServiceObjSpy },
        { provide: FormBuilder, useValue: formBuilder }
      ],
      schemas:[CUSTOM_ELEMENTS_SCHEMA]
    })
    loyaltyServiceSpy = TestBed.inject(LoyaltyService) as jasmine.SpyObj<LoyaltyService>;
    ngxLoggerSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    fixture = TestBed.createComponent(LoyaltySchemeComponent);
    component = fixture.componentInstance;
    component.form = formBuilder.group({
      Name: undefined,
      SiteId: undefined,
      Present: undefined,
      TerminalId: undefined,
      Footer1: undefined,
      Footer2: undefined,
      Timeout: undefined,
      ApiKey: undefined,
      HttpHeader: undefined
    });
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.updatePresent() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyPresent.and.returnValue(of(true));
    fixture.detectChanges();

    component.updatePresent();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.presentError).toBe(false);
  });

  it('.updatePresent() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyPresent.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updatePresent();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.presentError).toBe(true);
  });

  it('.updateSiteId() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalSiteId.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateSiteId();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.siteIdError).toBe(false);
  });

  it('.updateSiteId() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalSiteId.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateSiteId();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.siteIdError).toBe(true);
  });

  it('.updateTerminalId() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalTerminalId.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateTerminalId();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.terminalIdError).toBe(false);
  });

  it('.updateTerminalId() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalTerminalId.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateTerminalId();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.terminalIdError).toBe(true);
  });

  it('.updateFooter1() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalFooter1.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateFooter1();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.footer1Error).toBe(false);
  });

  it('.updateFooter1() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalFooter1.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateFooter1();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.footer1Error).toBe(true);
  });

  it('.updateFooter2() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalFooter2.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateFooter2();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.footer2Error).toBe(false);
  });

  it('.updateFooter2() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalFooter2.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateFooter2();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.footer2Error).toBe(true);
  });

  it('.updateTimeout() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalTimeout.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateTimeout();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.timeoutError).toBe(false);
  });

  it('.updateTimeout() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalTimeout.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateTimeout();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.timeoutError).toBe(true);
  });

  it('.updateApiKey() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalApiKey.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateApiKey();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.apiKeyError).toBe(false);
  });

  it('.updateApiKey() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalApiKey.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateApiKey();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.apiKeyError).toBe(true);
  });

  it('.updateHttpHeader() should handle success response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalHttpHeader.and.returnValue(of(true));
    fixture.detectChanges();

    component.updateHttpHeader();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.httpHeaderError).toBe(false);
  });

  it('.updateHttpHeader() should handle failure response from service', () => {
    loyaltyServiceSpy.setLoyaltyTerminalHttpHeader.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.updateHttpHeader();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.httpHeaderError).toBe(true);
  });

  it('.onAddHostFormSubmit() should handle success response from service', () => {
    component.createAddHostForm()
    loyaltyServiceSpy.setLoyaltyHostsAddHost.and.returnValue(of(true));
    fixture.detectChanges();

    component.onAddHostFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.addHostError).toBe(false);
  });

  it('.onAddHostFormSubmit() should handle failure response from service', () => {
    component.createAddHostForm()
    loyaltyServiceSpy.setLoyaltyHostsAddHost.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.onAddHostFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.addHostError).toBe(true);
  });

  it('.onAddHostnameFormSubmit() should handle success response from service', () => {
    component.createAddHostnameForm();
    loyaltyServiceSpy.setLoyaltyAddHostname.and.returnValue(of(true));
    fixture.detectChanges();

    component.onAddHostnameFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.addHostnameError).toBe(false);
  });

  it('.onAddHostnameFormSubmit() should handle failure response from service', () => {
    component.createAddHostnameForm()
    loyaltyServiceSpy.setLoyaltyAddHostname.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.onAddHostnameFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.addHostnameError).toBe(true);
  });

  it('.onAddIinFormSubmit() should handle success response from service', () => {
    component.createAddIinForm();
    loyaltyServiceSpy.setLoyaltyIinsAddIin.and.returnValue(of(true));
    fixture.detectChanges();

    component.onAddIinFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.addIinError).toBe(false);
  });

  it('.onAddIinFormSubmit() should handle failure response from service', () => {
    component.createAddIinForm()
    loyaltyServiceSpy.setLoyaltyIinsAddIin.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.onAddIinFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.addIinError).toBe(true);
  });

  it('.onAddLoyaltyMapFormSubmit() should handle success response from service', () => {
    component.createAddLoyaltyMapForm();
    loyaltyServiceSpy.setLoyaltyMappingsAddMapping.and.returnValue(of(true));
    fixture.detectChanges();

    component.onAddLoyaltyMapFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.debug).toHaveBeenCalledTimes(1);
    expect(component.addLoyaltyMapError).toBe(false);
  });

  it('.onAddLoyaltyMapFormSubmit() should handle failure response from service', () => {
    component.createAddLoyaltyMapForm();
    loyaltyServiceSpy.setLoyaltyMappingsAddMapping.and.returnValue(throwError({status:500}));
    fixture.detectChanges();

    component.onAddLoyaltyMapFormSubmit();
    
    expect(ngxLoggerSpy.info).toHaveBeenCalledTimes(1);
    expect(ngxLoggerSpy.error).toHaveBeenCalledTimes(1);
    expect(component.addLoyaltyMapError).toBe(true);
  });
});
