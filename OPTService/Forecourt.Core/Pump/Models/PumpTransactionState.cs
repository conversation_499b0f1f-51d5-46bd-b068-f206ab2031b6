using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using System;

namespace Forecourt.Core.Pump.Models
{
    /// <summary>
    /// Model summarising the overall transaction state at each stage of the transaction
    /// </summary>
    public class PumpTransactionState
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="state">PumpState</param>
        /// <param name="startTime">Time the state is first entered.</param>
        public PumpTransactionState(PumpState state, DateTime startTime)
        {
            PumpState = state;
            FirstEntered = startTime;
        }

        /// <summary>
        /// PumpState equivalent for this state
        /// </summary>
        public PumpState PumpState { get; }

        /// <summary>
        /// Time first entered the state
        /// </summary>
        public DateTime FirstEntered { get; }

        /// <summary>
        /// Time first entered the state
        /// </summary>
        public DateTime LastEntered { get; set; }

        /// <summary>
        /// Count state encountered
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Is the Opt in use
        /// </summary>
        public bool UseOpt { get; set; }

        /// <summary>
        /// Should the Opt be informed of progress
        /// </summary>
        public bool TellOpt { get; set; }

        /// <summary>
        /// Is this a Kiosk only transaction
        /// </summary>
        public bool IsKioskOnly { get; set; }

        /// <summary>
        /// Volume vended
        /// </summary>
        public uint Volume { get; set; }

        /// <summary>
        /// Amount vended
        /// </summary>
        public uint Amount { get; set; }

        /// <summary>
        /// The logging reference, at this stage of the transaction
        /// </summary>
        public string Reference { get; set; }
    }
}
