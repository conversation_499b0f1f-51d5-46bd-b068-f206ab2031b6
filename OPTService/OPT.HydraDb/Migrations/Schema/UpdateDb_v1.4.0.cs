using FluentMigrator;
using OPT.HydraDb.Common;

namespace OPT.HydraDb.Migrations.Schema
{
    [Migration(5, "Upgrade the HydraDb database to, v1.4.0"), Tags(Constants.MigrationTypeSchema)]
    public class UpdateDb_v1_4_0: Migrationable
    {
        public override void Up()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.001_Upgrade_contactless_install.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.002_Upgrade_transaction-cycling_install.sql");
        }

        public override void Down()
        {
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.002_Upgrade_transaction-cycling_rollback.sql");
            Execute.EmbeddedScript($"{EmbeddedResourcePathSchema}.001_Upgrade_contactless_rollback.sql");
        }
    }
}
