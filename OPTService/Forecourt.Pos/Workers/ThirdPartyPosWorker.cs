using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc cref="ListenerWorker{T, THydraDb, TTelemetryWorker}" />
    public class ThirdPartyPosWorker : ListenerWorker<string, IHydraDb, ITelemetryWorker>, IThirdPartyPosWorker
    {
        private const int NoId = -1;
        private const string MessageStart = "Start,";
        private const string MessageEnd = ",End";

        private readonly IPumpCollection _allPumps;
        private readonly IDictionary<byte, uint> _requestingPumps = new ConcurrentDictionary<byte, uint>();

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();
            var endPoint = endPoints.ThirdPartyPosBindEndPoint;
            GetLogger().Debug(FormatMessage("EndPoint", $"Third Party POS: {endPoint.Address}"));

            return new GenericEndPoint(endPoint.Address, endPoint.Port);
        }

        /// <summary>
        /// ThirdPartyPosWorker constructor
        /// </summary>
        /// <param name="hydraDb">The Hydra database</param>
        /// <param name="pumpWorker">The Pump worker</param>
        /// <param name="allPumps">The pumps collection</param>
        /// <param name="logger">The logger</param>
        /// <param name="telemetryWorker">The telemetry worker</param>
        /// <param name="connectionThread">The connection thread</param>
        public ThirdPartyPosWorker(IHydraDb hydraDb, IPumpIntegratorInTransient<IMessageTracking> pumpWorker, IPumpCollection allPumps, IHtecLogger logger,
            ITelemetryWorker telemetryWorker, IListenerConnectionThread<string> connectionThread) 
            : base(logger, telemetryWorker, connectionThread, hydraDb)
        {
            RegisterWorker(pumpWorker ?? throw new ArgumentNullException(nameof(pumpWorker)));

            // TODO: This will go into some base Opt.Common class when all merges are done!
            _allPumps = allPumps;
        }

        /// <inheritdoc cref="IThirdPartyPosWorker"/>
        public void SendAuthRequestToPos(byte pump, uint amount, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (!_requestingPumps.ContainsKey(pump))
                {
                    SendRequest($"Start,Pump{pump:D2},AUTHREQUEST,End", NoId, action: () => { _requestingPumps[pump] = amount; });
                    GetWorker<ITelemetryWorker>()?.MessageSentToThirdPartyPos(pump);
                }
            }, loggingReference);
        }

        /// <inheritdoc cref="IThirdPartyPosWorker"/>
        public void SendClearRequestToPos(byte pump, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (_requestingPumps.ContainsKey(pump))
                {
                    SendRequest($"Start,Pump{pump:D2},IDLE,End", NoId, action: () => _requestingPumps.Remove(pump));
                }
            }, loggingReference);
        }

        /// <inheritdoc cref="IThirdPartyPosWorker"/>
        private void SendAuthorisedToPos(byte pump, string loggingReference = null)
        {
            DoAction(() =>
            {
                SendRequest($"Start,Pump{pump:D2},AUTHORISED,End", NoId);
                if (_requestingPumps.ContainsKey(pump))
                {
                    _requestingPumps.Remove(pump);
                }
            }, loggingReference);
        }

        /// <inheritdoc cref="ListenerWorker{T}"/>
        protected override Result<string> DoOnMessageReceived(IMessageTracking<string> message, int id)
        {
            var request = message.Request;
            DoAction(() =>
            {
                IEnumerable<string> messages = GetThirdPartyMessages(request).ToList();
                var controllerWorker = NotificationWorker;

                if (messages.Any())
                {
                    foreach (var thisMessage in messages)
                    {
                        try
                        {
                            if (thisMessage.StartsWith("Start,Pump") && thisMessage.EndsWith(MessageEnd) &&
                                byte.TryParse(thisMessage.Substring(10, 2), out var pump) && thisMessage[12] == ',' && thisMessage.Length > 16)
                            {
                                var command = thisMessage.Substring(13, thisMessage.Length - 17);
                                GetLogger().Debug($"Command received is {command}, pump is {pump}");
                                if (command.Equals("AUTHORISE"))
                                {
                                    controllerWorker?.SendInformation($"POS Authorise command received, pump is {pump}");
                                    GetWorker<ITelemetryWorker>()?.MessageReceivedFromThirdPartyPos(pump);
                                    if (_allPumps.TryGetPump(pump, out var thePump))
                                    {
                                        thePump.ThirdPartyAuth(message.IdAsString);
                                        SendAuthorisedToPos(pump);
                                        if (thePump.IsAuthorised(out var amount, message.IdAsString))
                                        {
                                            GetWorker<IPumpIntegratorInTransient<IMessageTracking>>()?.PaymentApproved(pump, amount, message);
                                            GetWorker<ISecAuthIntegratorInTransient<IMessageTracking>>()?.RequestTimedOut(new SecAuthResponse(message.ParentIdAsString, message.IdAsString, thePump.Number, isAuthorised: false), message);
                                        }

                                        controllerWorker?.PushChange(EventType.PumpChanged, pump.ToString());
                                    }
                                }
                            }
                            else
                            {
                                GetLogger().Warn($" Unknown Message is {thisMessage}");
                            }
                        }
                        catch (Exception exception)
                        {
                            GetLogger().Error("Failed to process message", exception);
                        }
                    }
                }

            }, message.FullId);

            return null;
        }

        /// <summary>
        /// Gets the list of third party POS messages adding the received argument
        /// </summary>
        /// <param name="message">The message to be included</param>
        /// <returns>The list of third party POS messages including the received one</returns>
        private IEnumerable<string> GetThirdPartyMessages(string message)
        {
            var result = DoAction<IEnumerable<string>>(() =>
            {
                var messages = new List<string>();
                while (message.Length > 0)
                {
                    var start = message.IndexOf(MessageStart, StringComparison.Ordinal);

                    if (start > 0)
                    {
                        GetLogger().Warn($"Discarding characters from message {message}");
                        message = message.Substring(start);
                    }

                    if (message.StartsWith(MessageStart) && message.Contains(MessageEnd))
                    {
                        start = message.IndexOf(MessageEnd, StringComparison.Ordinal) + MessageEnd.Length;
                        messages.Add(message.Substring(0, start));
                    }
                    else
                    {
                        start = message.Length;
                        GetLogger().Warn($"Discarding characters from message {message}");
                    }

                    message = message.Substring(start);
                }

                return Result.Success(messages.AsEnumerable());
            }, LoggingReference);
            return result.Value;
        }
    }
}
