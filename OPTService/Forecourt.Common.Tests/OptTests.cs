using FluentAssertions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.Xsd;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System.Collections.Generic;
using System.Collections.Specialized;
using Xunit;
using PumpStateType = Htec.Hydra.Messages.Opt.Models.PumpStateType;

namespace OPT.Common.Tests
{
    public class OptTests
    {
        private const string HydraId = "Hydra 1";
        private const string IdString = "1234";
        private const int Id = 1;
        private const byte PumpNumber = 2;
        private const byte SecondPumpNumber = 3;
        private const uint DefaultCashLimit = 10000;
        private const int DefaultPaymentTimeout = 30;
        private readonly configType _config = new configType { opt = new opt { mode = (int)OptModeType.OptModeNotSet } };
        private readonly configType _expectedConfig = new configType { opt = new opt { mode = (int)OptModeType.OptModeNotSet } };
        private readonly WhitelistResponse _whitelist = new WhitelistResponse("Success", new List<FileDetails>());
        private readonly IHydraDb _hydraDb;
        private readonly IPump _pump;
        private readonly IPump _secondPump;
        private readonly IOpt _opt;
        private readonly IHtecLogger _logger;
        private readonly IConfigurationManager _configurationManager;
        private readonly IHtecLogManager _logManager;

        public OptTests()
        {
            _logger = Substitute.For<IHtecLogger>();
            _hydraDb = Substitute.For<IHydraDb>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            var appSettings = Substitute.For<NameValueCollection>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(appSettings);

            _logManager = Substitute.For<IHtecLogManager>();

            _opt = CreateDefaultOpt();
            _pump = new Pump(PumpNumber, _hydraDb, _logManager, Pump.LoggerName, configurationManager: _configurationManager);
            _secondPump = new Pump(SecondPumpNumber, _hydraDb, _logManager, Pump.LoggerName, configurationManager: _configurationManager);
        }

        [Fact]
        public void test_initial_state()
        {
            // Arrange

            // Act
            var opt = CreateDefaultOpt();

            // Assert
            opt.IdString.Should().BeEquivalentTo(IdString);
            opt.Id.Should().Be(Id);
            opt.SignedIn.Should().BeFalse();
            opt.ToOptConnected.Should().BeFalse();
            opt.FromOptConnected.Should().BeFalse();
            opt.HeartbeatConnected.Should().BeFalse();
            opt.AllConnected.Should().BeFalse();
            opt.Connected.Should().BeFalse();
            opt.Offline.Should().BeTrue();
            opt.PrinterError.Should().BeFalse();
            opt.PaperLow.Should().BeFalse();
            opt.ConfigNotificationPending.Should().BeFalse();
            opt.WhitelistNotificationPending.Should().BeFalse();
            opt.ConfigCheckPending.Should().BeFalse();
            opt.WhitelistCheckPending.Should().BeFalse();
            opt.ModeChangePending.Should().BeFalse();
            opt.SentConfig.Should().BeNull();
            opt.SentWhitelist.Should().BeNull();
            opt.Mode.Should().Be(OptModeType.OptModeNotSet);
            opt.InUse.Should().BeFalse();
            opt.HasContactless.Should().BeFalse();
            opt.CashLimit.Should().Be(DefaultCashLimit);
            opt.PaymentTimeoutInSeconds.Should().Be(DefaultPaymentTimeout);
            opt.GetCurrentNotification().Should().BeNull();
            opt.RequestSentToOpt.Should().BeFalse();
            opt.HasTimeoutExpired().Should().BeFalse();
            opt.PumpList().Should().BeEmpty();
        }

        [Fact]
        public void test_add_pump()
        {
            // Arrange
            IList<byte> expectedPumpList = new List<byte> { PumpNumber };

            // Act
            _opt.AddPump(_pump);

            // Assert
            _opt.Mode.Should().Be(OptModeType.OptModeOpt);
            _opt.PumpList().Should().BeEquivalentTo(expectedPumpList);
        }

        [Fact]
        public void test_add_pump_add_pump_and_remove_pump()
        {
            // Arrange
            IList<byte> expectedPumpList = new List<byte> { SecondPumpNumber };

            // Act
            _opt.AddPump(_pump);
            _opt.AddPump(_secondPump);
            _opt.RemovePump(_pump);

            // Assert
            _opt.Mode.Should().Be(OptModeType.OptModeOpt);
            _opt.PumpList().Should().BeEquivalentTo(expectedPumpList);
        }

        [Fact]
        public void test_add_pump_add_pump_and_clear_pumps()
        {
            // Arrange

            // Act
            _opt.AddPump(_pump);
            _opt.AddPump(_secondPump);
            _opt.ClearPumps();

            // Assert
            _opt.Mode.Should().Be(OptModeType.OptModeNotSet);
            _opt.PumpList().Should().BeEmpty();
        }

        [Fact]
        public void test_add_pump_and_add_pump()
        {
            // Arrange
            IList<byte> expectedPumpList = new List<byte> { PumpNumber, SecondPumpNumber };
            // Act
            _opt.AddPump(_pump);
            _opt.AddPump(_secondPump);

            // Assert
            _opt.IsInPodMode.Should().BeTrue();
            _opt.PumpList().Should().BeEquivalentTo(expectedPumpList);
        }

        [Fact]
        public void test_received_from_to_opt()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
        }

        [Fact]
        public void test_received_from_from_opt()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.FromOpt);

            // Assert
            _opt.FromOptConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
        }

        [Fact]
        public void test_received_from_heartbeat()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.Heartbeat);

            // Assert
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
        }

        [Fact]
        public void test_received_from_to_opt_from_opt_and_heartbeat()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.FromOptConnected.Should().BeTrue();
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.AllConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.Offline.Should().BeFalse();
            _opt.SoftwareNotificationPending.Should().BeFalse();
            _opt.ConfigNotificationPending.Should().BeFalse();
            _opt.WhitelistNotificationPending.Should().BeFalse();
        }

        [Fact]
        public void test_received_from_to_opt_from_opt_heartbeat_and_config_and_whitelist_needed()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);
            _opt.ConfigNeeded();
            _opt.WhitelistNeeded();
            _opt.SignIn();

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.FromOptConnected.Should().BeTrue();
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.AllConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.Offline.Should().BeFalse();
            _opt.ConfigNotificationPending.Should().BeTrue();
            _opt.WhitelistNotificationPending.Should().BeFalse();
        }

        [Fact]
        public void test_received_from_to_opt_from_opt_heartbeat_config_and_whitelist_needed_and_config_and_whitelist_pending_sent()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);
            _opt.ConfigNeeded();
            _opt.WhitelistNeeded();
            _opt.ConfigPendingSent();
            _opt.WhitelistPendingSent();

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.FromOptConnected.Should().BeTrue();
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.AllConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.Offline.Should().BeFalse();
            _opt.ConfigNotificationPending.Should().BeFalse();
            _opt.WhitelistNotificationPending.Should().BeFalse();
        }

        [Fact]
        public void test_received_from_to_opt_from_opt_heartbeat_config_and_whitelist_sent_and_config_and_whitelist_check_required()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);
            _opt.ConfigSent(_config);
            _opt.WhitelistSent(_whitelist);
            _opt.ConfigCheckRequired();
            _opt.WhitelistCheckRequired();

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.FromOptConnected.Should().BeTrue();
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.AllConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.Offline.Should().BeFalse();
            _opt.ConfigNotificationPending.Should().BeFalse();
            _opt.WhitelistNotificationPending.Should().BeFalse();
            _opt.ConfigCheckPending.Should().BeTrue();
            _opt.WhitelistCheckPending.Should().BeTrue();
            _opt.SentConfig.Should().BeEquivalentTo(_expectedConfig);
            _opt.SentWhitelist.Should().BeEquivalentTo(_whitelist);
        }

        [Fact]
        public void
            test_received_from_to_opt_from_opt_heartbeat_config_and_whitelist_sent_config_and_whitelist_check_required_andconfig_and_whitelist_check_cleared()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);
            _opt.ConfigSent(_config);
            _opt.WhitelistSent(_whitelist);
            _opt.ConfigCheckRequired();
            _opt.WhitelistCheckRequired();
            _opt.ConfigCheckCleared();
            _opt.WhitelistCheckCleared();

            // Assert
            _opt.ToOptConnected.Should().BeTrue();
            _opt.FromOptConnected.Should().BeTrue();
            _opt.HeartbeatConnected.Should().BeTrue();
            _opt.AllConnected.Should().BeTrue();
            _opt.Connected.Should().BeTrue();
            _opt.Offline.Should().BeFalse();
            _opt.ConfigNotificationPending.Should().BeFalse();
            _opt.WhitelistNotificationPending.Should().BeFalse();
            _opt.ConfigCheckPending.Should().BeFalse();
            _opt.WhitelistCheckPending.Should().BeFalse();
            _opt.SentConfig.Should().BeEquivalentTo(_expectedConfig);
            _opt.SentWhitelist.Should().BeEquivalentTo(_whitelist);
        }



        [Fact]
        public void test_received_from_heartbeat_and_disconnected()
        {
            // Arrange

            // Act
            _opt.Received(SocketType.Heartbeat);
            _opt.Disconnected(SocketType.Heartbeat);

            // Assert
            _opt.HeartbeatConnected.Should().BeFalse();
            _opt.Connected.Should().BeFalse();
        }
        
        [Fact]
        public void test_set_payment_timeout()
        {
            // Arrange
            const int paymentTimeout = 10;

            // Act
            _opt.SetPaymentTimeoutInSeconds(paymentTimeout);

            // Assert
            _opt.PaymentTimeoutInSeconds.Should().Be(paymentTimeout);
        }

        [Fact]
        public void test_set_contactless()
        {
            // Arrange

            // Act
            _opt.SetContactless(true);

            // Assert
            _opt.HasContactless.Should().BeTrue();
        }

        [Fact]
        public void test_set_printer_paper_low()
        {
            // Arrange

            // Act
            _opt.SetPrinterStatus(false, true);

            // Assert
            _opt.PaperLow.Should().BeTrue();
        }

        [Fact]
        public void test_set_printer_error()
        {
            // Arrange

            // Act
            _opt.SetPrinterStatus(true);

            // Assert
            _opt.PrinterError.Should().BeTrue();
            _opt.PaperLow.Should().BeFalse();
        }

        [Fact]
        public void test_set_printer_ok()
        {
            // Arrange
            _opt.SetPrinterStatus(true);

            // Act
            _opt.SetPrinterStatus();

            // Assert
            _opt.PrinterError.Should().BeFalse();
            _opt.PaperLow.Should().BeFalse();
        }

        [Fact]
        public void test_sent_config()
        {
            // Arrange

            // Act
            _opt.ConfigSent(_config);

            // Assert
            _opt.SentConfig.Should().BeEquivalentTo(_expectedConfig);
        }

        [Fact]
        public void test_sent_whitelist()
        {
            // Arrange

            // Act
            _opt.WhitelistSent(_whitelist);

            // Assert
            _opt.SentWhitelist.Should().BeEquivalentTo(_whitelist);
        }

        [Fact]
        public void test_sent_config_and_mode_change_sent()
        {
            // Arrange
            _opt.AddPump(_pump);
            _expectedConfig.opt.mode = (int)OptModeType.OptModeOpt;

            // Act
            _opt.ConfigSent(_config);
            _opt.ModeChangeSent();

            // Assert
            _opt.SentConfig.Should().BeEquivalentTo(_expectedConfig);
        }
        [Fact]
        public void test_sent_config_with_changed_mode()
        {
            // Arrange
            _opt.Received(SocketType.ToOpt);
            _opt.Received(SocketType.FromOpt);
            _opt.Received(SocketType.Heartbeat);
            _opt.AddPump(_pump);

            // Act
            _opt.ConfigSent(_config);

            // Assert
            _opt.SentConfig.Should().BeEquivalentTo(_expectedConfig);
            _opt.ModeChangePending.Should().BeTrue();
        }

        [Fact]
        public void test_set_receipt_header()
        {
            // Arrange
            const string receiptHeader = "A Header";

            // Act
            _opt.SetReceiptHeader(receiptHeader);

            // Assert
            _opt.ReceiptHeader.Should().Be(receiptHeader);
        }

        [Fact]
        public void test_set_receipt_footer()
        {
            // Arrange
            const string receiptFooter = "A Footer";

            // Act
            _opt.SetReceiptFooter(receiptFooter);

            // Assert
            _opt.ReceiptFooter.Should().Be(receiptFooter);
        }

        #region Helpers

        private Opt CreateDefaultOpt()
        {
            return new Opt(_logManager, IdString, Id, HydraId, _configurationManager);
        }

        #endregion

        #region // IsNozzleUp

        [Theory, InlineData(1), InlineData(2, true), InlineData(3)]
        public void test_isnozzleup_with_2_pump_pod_with_pump1_lifted_only(byte pump, bool expectedValue = false)
        {
            // Arrange
            _opt.AddPump(_pump);
            _pump.OpenPump(false);
            _opt.AddPump(_secondPump);
            _secondPump.OpenPump(false);

            // Act
            _pump.Requesting(new List<byte> { 0 });

            // Act
            var result = _opt.IsNozzleUp(pump);

            // Assert
            _pump.PumpState.Should().Be(PumpStateType.Open);
            _pump.IsNozzleUp.Should().Be(true);
            result.Should().Be(expectedValue);
        }

        #endregion

        #region // Opt.Mode tests

        private void SetMode(IPump pump, PumpModeType mode)
        {
            switch (mode)
            {
                case PumpModeType.KioskOnly:
                    pump.SetKioskOnly(false, false);
                    break;

                case PumpModeType.OutsideOnly:
                    pump.SetOutsideOnly(false, false);
                    break;

                case PumpModeType.Mixed:
                    pump.SetMixed(false, false);
                    break;

                case PumpModeType.KioskUse:
                    pump.SetKioskUse();
                    break;
            };
        }


        [Theory,
             InlineData(PumpModeType.Mixed, OptModeType.OptModeMixed),
             InlineData(PumpModeType.KioskUse, OptModeType.OptModeKioskOnly),
             InlineData(PumpModeType.KioskOnly, OptModeType.OptModeKioskOnly),
             InlineData(PumpModeType.OutsideOnly, OptModeType.OptModeOpt)]

        public void test_mode_for_1_pump_config(PumpModeType mode, OptModeType expected)
        {
            // Arrange
            _pump.SetMixed(true, false); // default mode
            _pump.SetMixed();
            _opt.AddPump(_pump);
            _pump.OpenPump(false);

            SetMode(_pump, mode);

            // Act
            var result = _opt.Mode;

            // Assert
            result.Should().Be(expected);
        }

        [Theory, 
            InlineData(PumpModeType.Mixed, PumpModeType.Mixed, OptModeType.OptModeMixed),
            InlineData(PumpModeType.Mixed, PumpModeType.KioskOnly, OptModeType.OptModeMixed),
            InlineData(PumpModeType.Mixed, PumpModeType.OutsideOnly, OptModeType.OptModeMixed),
            InlineData(PumpModeType.Mixed, PumpModeType.KioskUse, OptModeType.OptModeMixed),
            InlineData(PumpModeType.KioskUse, PumpModeType.KioskUse, OptModeType.OptModeKioskOnly),
            InlineData(PumpModeType.KioskUse, PumpModeType.OutsideOnly, OptModeType.OptModeMixed),
            InlineData(PumpModeType.KioskUse, PumpModeType.KioskOnly, OptModeType.OptModeKioskOnly),
            InlineData(PumpModeType.OutsideOnly, PumpModeType.OutsideOnly, OptModeType.OptModeOpt),
            InlineData(PumpModeType.OutsideOnly, PumpModeType.KioskOnly, OptModeType.OptModeMixed),
            InlineData(PumpModeType.KioskOnly, PumpModeType.KioskOnly, OptModeType.OptModeKioskOnly),
            ]
        public void test_mode_for_2_pump_pod_config(PumpModeType mode1, PumpModeType mode2, OptModeType expected)
        {
            // Arrange
            _pump.SetMixed(true, false); // default mode
            _pump.SetMixed();
            _secondPump.SetMixed(true, false); // default mode
            _secondPump.SetMixed();

            _opt.AddPump(_pump);
            _pump.OpenPump(false);
            _opt.AddPump(_secondPump);
            _secondPump.OpenPump(false);

            SetMode(_pump, mode1);
            SetMode(_secondPump, mode2);

            // Act
            var result = _opt.Mode;

            // Assert
            result.Should().Be(expected);
        }

        #endregion
    }
}