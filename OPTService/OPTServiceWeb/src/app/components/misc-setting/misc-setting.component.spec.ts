import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Control<PERSON>ontainer, FormBuilder, FormGroup, FormGroupDirective } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { Setting } from 'src/app/core/models/setting.model';

import { MiscSettingComponent } from './misc-setting.component';

const fg: FormGroup = new FormGroup({});
const fgd: FormGroupDirective = new FormGroupDirective([], []);
fgd.form = fg;

describe('MiscSettingComponent', () => {
  let component: MiscSettingComponent;
  let fixture: ComponentFixture<MiscSettingComponent>;

  beforeEach(() => {
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      declarations: [MiscSettingComponent],
      imports: [
        HttpClientTestingModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        FormBuilder,
        { provide: ControlContainer, useValue: fgd },
        { provide: NGXLogger, useValue: ngxLoggerSpy },
      ]
    })
    fixture = TestBed.createComponent(MiscSettingComponent);
    component = fixture.componentInstance;
    component.setting = new Setting();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
